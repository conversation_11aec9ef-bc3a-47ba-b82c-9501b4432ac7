{"version": 3, "file": "static/js/731.9591e2a9.chunk.js", "mappings": "iMAEA,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,8rBAAksB,KAAQ,OAAQ,MAAS,Y,WCKj3BA,EAAe,SAAsBC,EAAOC,GAC9C,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,IAAcA,EAAAA,EAAAA,GAAc,CAAC,EAAGJ,GAAQ,CAAC,EAAG,CAC5FC,IAAKA,EACLI,KAAMC,IAEV,EACAP,EAAaQ,YAAc,eAC3B,MAA4BL,EAAAA,WAAiBH,G,qBCR9B,SAASS,EAAgBR,GACpC,OACI,gBAAKS,UAAU,yBAAwB,UACnC,4BACI,0BACI,gBAAKA,UAAU,YAAYC,IAAKC,EAAQ,OAA2BC,IAAI,QAE3E,gBAAKH,UAAU,OAAM,UAAC,UAAC,IAAM,CAACI,KAAK,UAAUC,QAAS,WAClDC,OAAOC,KAAU,OAALhB,QAAK,IAALA,OAAK,EAALA,EAAOiB,IAAK,QAC5B,EAAE,4CAAc,OAALjB,QAAK,IAALA,OAAK,EAALA,EAAOkB,QAAQ,SAAC,EAAY,aAIvD,C", "sources": ["../node_modules/@ant-design/icons-svg/es/asn/LinkOutlined.js", "../node_modules/@ant-design/icons/es/icons/LinkOutlined.js", "pages/OutLinkTemplate.tsx"], "sourcesContent": ["// This icon file is generated automatically.\nvar LinkOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z\" } }] }, \"name\": \"link\", \"theme\": \"outlined\" };\nexport default LinkOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport LinkOutlinedSvg from \"@ant-design/icons-svg/es/asn/LinkOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar LinkOutlined = function LinkOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: LinkOutlinedSvg\n  }));\n};\nLinkOutlined.displayName = 'LinkOutlined';\nexport default /*#__PURE__*/React.forwardRef(LinkOutlined);", "import { LinkOutlined } from '@ant-design/icons'\nimport { But<PERSON> } from 'antd'\nimport React from 'react'\nimport { IAppMenuItem } from '../api/interface/kubeflowInterface'\n\nexport default function OutLinkTemplate(props?: IAppMenuItem) {\n    return (\n        <div className=\"d-f jc ac h100 fade-in\">\n            <div>\n                <div>\n                    <img className=\"pb32 w384\" src={require('../images/findData.png')} alt=\"\" />\n                </div>\n                <div className=\"ta-c\"><Button type=\"primary\" onClick={() => {\n                    window.open(props?.url, 'blank')\n                }}>{`点击前往${props?.title}`}<LinkOutlined /></Button></div>\n            </div>\n        </div>\n    )\n}\n"], "names": ["LinkOutlined", "props", "ref", "React", "AntdIcon", "_objectSpread", "icon", "LinkOutlinedSvg", "displayName", "OutLinkTemplate", "className", "src", "require", "alt", "type", "onClick", "window", "open", "url", "title"], "sourceRoot": ""}