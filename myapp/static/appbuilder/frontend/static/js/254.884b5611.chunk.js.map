{"version": 3, "file": "static/js/254.884b5611.chunk.js", "mappings": "4MAGe,SAASA,IACpB,OAAwBC,EAAAA,EAAAA,UAAiB,IAAG,eAArCC,EAAI,KAAS,KAIpB,OAHAC,EAAAA,EAAAA,YAAU,WACN,GACD,KAEC,gBAAKC,UAAU,UAAUC,wBAAyB,CAAEC,OAAQJ,IAIpE,C", "sources": ["pages/ShowData.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\nimport { getParam } from '../util'\n\nexport default function ShowData() {\n    const [data, setData] = useState<string>('')\n    useEffect(() => {\n        // some api get data\n    }, [])\n    return (\n        <div className=\"fade-in\" dangerouslySetInnerHTML={{ __html: data }}>\n\n        </div>\n    )\n}\n"], "names": ["ShowData", "useState", "data", "useEffect", "className", "dangerouslySetInnerHTML", "__html"], "sourceRoot": ""}