---
# Source: alluxio/templates/csi/controller.yaml
kind: Deployment
apiVersion: apps/v1
metadata:
  name: alluxio-csi-controller
  labels:
    name: alluxio-csi-controller
    app: alluxio
    chart: alluxio-0.6.53
    release: alluxio
    heritage: Helm
    role: alluxio-csi-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: alluxio
      role: alluxio-csi-controller
      name: alluxio-csi-controller
  template:
    metadata:
      labels:
        name: alluxio-csi-controller
        app: alluxio
        chart: alluxio-0.6.53
        release: alluxio
        heritage: Helm
        role: alluxio-csi-controller
    spec:
      serviceAccount: csi-controller-sa
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      serviceAccountName: csi-controller-sa
      containers:
        - name: csi-provisioner
          image: k8s.gcr.io/sig-storage/csi-provisioner:v2.0.5
          args:
            - "--v=5"
            - "--csi-address=$(ADDRESS)"
            - "--timeout=60s"
            - "--volume-name-prefix=alluxio"
          env:
            - name: ADDRESS
              value: /csi/csi.sock
          volumeMounts:
            - mountPath: /csi
              name: socket-dir
          resources:
            limits:
              cpu: 100m
              memory: 300Mi
            requests:
              cpu: 10m
              memory: 20Mi
        - name: csi-controller
          image: alluxio/alluxio:2.9.0
          imagePullPolicy: IfNotPresent
          command: ["tini", "--", "/entrypoint.sh"]
          args:
            - csiserver
            - --v=5
            - "--nodeid=$(NODE_ID)"
            - "--endpoint=$(CSI_ENDPOINT)"
          env:
            - name: NODE_ID
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: CSI_ENDPOINT
              value: unix://csi/csi.sock
          envFrom:
            - configMapRef:
                name: alluxio-config
          volumeMounts:
            - mountPath: /csi
              name: socket-dir
          resources:
            limits:
              cpu: 200m
              memory: 200Mi
            requests:
              cpu: 10m
              memory: 20Mi
      volumes:
        - name: socket-dir
          emptyDir: {}
---
# Source: alluxio/templates/csi/controller.yaml
#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#
