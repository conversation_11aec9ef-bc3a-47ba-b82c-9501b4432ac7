apiVersion: v1
kind: PersistentVolume
metadata:
  name: kafka1-pv
spec:
  capacity:
    storage: 2000Gi
  # volumeMode field requires BlockVolume Alpha feature gate to be enabled.
  volumeMode: Filesystem
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-kafka
  local:
    path: /data/kafka1
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: pvrole
          operator: In
          values:
          - node1
