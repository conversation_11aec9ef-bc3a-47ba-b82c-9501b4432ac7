# 添加代理80端口的gateway，需要在istio-ingressgateway 的相关配置中打开80端口
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: kubeflow-gateway
  namespace: kubeflow
spec:
  selector:
    istio: ingressgateway         # pod的label
  servers:
  - hosts:
    - '*'        # 此处不能修改为域名，不然泛域名没法用
    port:
      number: 80        # svc服务的外部端口，会对应在ingressgateway的容器内启动svc的目标端口，但无法启动80和443内部端口，所以要先修改svc的目标端口，再创建gateway。（也会监听svc目标端口变化进行更改）
      protocol: HTTP   # HTTP协议在第一个创建时就有端口(80端口可能受限)开启，第二个会vs才启动，
#  # 使用https添加这部分,并且istio-ingressgateway开放443端口，注意不要和rancher端口重叠
#  - hosts:
#    - '*'
#    port:
#      name: https
#      number: 443
#      protocol: HTTPS
#    tls:
#      credentialName: tls-secret
#      mode: SIMPLE
---
# 添加代理8080端口的gateway，需要在istio-ingressgateway 的相关配置中打开8080端口
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: kubeflow-gateway-8080
  namespace: kubeflow
spec:
  selector:
    istio: ingressgateway
  servers:
  - hosts:
    - '*'          # 此处不能修改为域名，不然泛域名没法用
    port:
      number: 8080
      protocol: HTTP       # TCP 协议需要vs tcp match 端口后才会开启







