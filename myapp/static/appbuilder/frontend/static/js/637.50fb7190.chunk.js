"use strict";(self.webpackChunkkubeflow_frontend=self.webpackChunkkubeflow_frontend||[]).push([[637],{39625:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(1413),a=n(4519),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},i=n(29465),c=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};c.displayName="MenuOutlined";var l=a.forwardRef(c)},89923:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(1413),a=n(4519),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z"}}]},name:"redo",theme:"outlined"},i=n(29465),c=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};c.displayName="RedoOutlined";var l=a.forwardRef(c)},18346:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(1413),a=n(4519),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M666.7 505.5l-246-178A8 8 0 00408 334v46.9c0 10.2 4.9 19.9 13.2 25.9L566.6 512 421.2 617.2c-8.3 6-13.2 15.6-13.2 25.9V690c0 6.5 7.4 10.3 12.7 6.5l246-178c4.4-3.2 4.4-9.8 0-13z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"right-circle",theme:"outlined"},i=n(29465),c=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};c.displayName="RightCircleOutlined";var l=a.forwardRef(c)},51052:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(1413),a=n(4519),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},i=n(29465),c=function(e,t){return a.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};c.displayName="StopOutlined";var l=a.forwardRef(c)},81535:function(e,t,n){n.d(t,{r:function(){return O}});var r=n(4942),a=n(15671),o=n(43144),i=n(37762),c=n(93433),l=n(69451),s=n(41660),u=l.r$.define({combine:function(e){return e.length&&Array.isArray(e)?e.flat():[]}}),d=l.r$.define({combine:function(e){return e.length&&Array.isArray(e)?Math.min.apply(Math,(0,c.Z)(e)):2}}),f=s.p.line({attributes:{class:"cm-zebra-stripe"}});function p(e){var t,n=e.state.facet(d),r=e.state.facet(u),a=new l.f_,o=(0,i.Z)(e.visibleRanges);try{for(o.s();!(t=o.n()).done;)for(var c=t.value,s=c.from,p=c.to,m=s;m<=p;){var v=e.state.doc.lineAt(m);v.number%n===0&&0===r.length&&a.add(v.from,v.from,f),r.length>0&&r.flat().includes(v.number)&&a.add(v.from,v.from,f),m=v.to+1}}catch(h){o.e(h)}finally{o.f()}return a.finish()}var m=s.lg.fromClass(function(){function e(t){(0,a.Z)(this,e),this.decorations=void 0,this.decorations=p(t)}return(0,o.Z)(e,[{key:"update",value:function(e){this.decorations=p(e.view)}}]),e}(),{decorations:function(e){return e.decorations}}),v=function(e){var t;return void 0===e&&(e={}),s.tk.baseTheme((t={},(0,r.Z)(t,"&light ."+e.className,{backgroundColor:e.lightColor||"#eef6ff"}),(0,r.Z)(t,"&dark ."+e.className,{backgroundColor:e.darkColor||"#3a404d"}),t))},h=function(e,t,n){return Array.from({length:(t-e)/n+1},(function(t,r){return e+r*n}))};function O(e){void 0===e&&(e={});var t=e.className,n=void 0===t?"cm-zebra-stripe":t;f=s.p.line({attributes:{class:n}}),e.lineNumber&&Array.isArray(e.lineNumber)?(e.step=null,e.lineNumber=e.lineNumber.map((function(e){return Array.isArray(e)&&"number"===typeof e[0]&&"number"===typeof e[1]?h(e[0],e[1],1):e}))):e.lineNumber=null;var r=[null===e.lineNumber?[]:u.of(e.lineNumber||[]),null===e.step?[]:d.of(e.step||2),m];if(n){var a=v({lightColor:e.lightColor,darkColor:e.darkColor,className:n});r.push(a)}return r}},37090:function(e,t,n){n.d(t,{F:function(){return s}});var r=n(93433),a=n(87462),o=n(63193),i=n(41660),c=n(46138),l=function(e){var t=e.theme,n=e.settings,r=void 0===n?{}:n,a=e.styles,o=void 0===a?[]:a,l={".cm-gutters":{}},s={};r.background&&(s.backgroundColor=r.background),r.foreground&&(s.color=r.foreground),(r.background||r.foreground)&&(l["&"]=s),r.fontFamily&&(l["&.cm-editor .cm-scroller"]={fontFamily:r.fontFamily}),r.gutterBackground&&(l[".cm-gutters"].backgroundColor=r.gutterBackground),r.gutterForeground&&(l[".cm-gutters"].color=r.gutterForeground),r.gutterBorder&&(l[".cm-gutters"].borderRightColor=r.gutterBorder),r.caret&&(l[".cm-content"]={caretColor:r.caret},l[".cm-cursor, .cm-dropCursor"]={borderLeftColor:r.caret});var u={};r.gutterActiveForeground&&(u.color=r.gutterActiveForeground),r.lineHighlight&&(l[".cm-activeLine"]={backgroundColor:r.lineHighlight},u.backgroundColor=r.lineHighlight),l[".cm-activeLineGutter"]=u,r.selection&&(l["&.cm-focused .cm-selectionBackground, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection"]={backgroundColor:r.selection}),r.selectionMatch&&(l["& .cm-selectionMatch"]={backgroundColor:r.selectionMatch});var d=i.tk.theme(l,{dark:"dark"===t}),f=c.Qf.define(o);return[d,(0,c.nF)(f)]},s=function(e){var t=e||{},n=t.theme,i=void 0===n?"light":n,c=t.settings,s=void 0===c?{}:c,u=t.styles,d=void 0===u?[]:u;return l({theme:i,settings:(0,a.Z)({background:"#FFFFFF",foreground:"#000000",caret:"#FBAC52",selection:"#FFD420",selectionMatch:"#FFD420",gutterBackground:"#f5f5f5",gutterForeground:"#4D4D4C",gutterBorder:"transparent",lineHighlight:"#00000012"},s),styles:[{tag:[o.pJ.meta,o.pJ.comment],color:"#804000"},{tag:[o.pJ.keyword,o.pJ.strong],color:"#0000FF"},{tag:[o.pJ.number],color:"#FF0080"},{tag:[o.pJ.string],color:"#FF0080"},{tag:[o.pJ.variableName],color:"#006600"},{tag:[o.pJ.escape],color:"#33CC33"},{tag:[o.pJ.tagName],color:"#1C02FF"},{tag:[o.pJ.heading],color:"#0C07FF"},{tag:[o.pJ.quote],color:"#000000"},{tag:[o.pJ.list],color:"#B90690"},{tag:[o.pJ.documentMeta],color:"#888888"},{tag:[o.pJ.function(o.pJ.variableName)],color:"#0000A2"},{tag:[o.pJ.definition(o.pJ.typeName),o.pJ.typeName],color:"#6D79DE"}].concat((0,r.Z)(d))})}()},34745:function(e,t,n){n.d(t,{Z:function(){return A}});var r=n(87462),a=n(4942),o=n(99269),i=n(43270),c=n.n(i),l=n(93433),s=n(15671),u=n(43144),d=n(60136),f=n(29388),p=n(71002),m=n(77935),v=n(4519),h=n(1277),O=n.n(h),g=n(45987),k=n(6605),b=n(29439),y=v.forwardRef((function(e,t){var n,r=e.prefixCls,o=e.forceRender,i=e.className,l=e.style,s=e.children,u=e.isActive,d=e.role,f=v.useState(u||o),p=(0,b.Z)(f,2),m=p[0],h=p[1];return v.useEffect((function(){(o||u)&&h(!0)}),[o,u]),m?v.createElement("div",{ref:t,className:c()("".concat(r,"-content"),(n={},(0,a.Z)(n,"".concat(r,"-content-active"),u),(0,a.Z)(n,"".concat(r,"-content-inactive"),!u),n),i),style:l,role:d},v.createElement("div",{className:"".concat(r,"-content-box")},s)):null}));y.displayName="PanelContent";var C=y,x=["className","id","style","prefixCls","headerClass","children","isActive","destroyInactivePanel","accordion","forceRender","openMotion","extra","collapsible"],Q=function(e){(0,d.Z)(n,e);var t=(0,f.Z)(n);function n(){var e;(0,s.Z)(this,n);for(var r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];return(e=t.call.apply(t,[this].concat(a))).onItemClick=function(){var t=e.props,n=t.onItemClick,r=t.panelKey;"function"===typeof n&&n(r)},e.handleKeyPress=function(t){"Enter"!==t.key&&13!==t.keyCode&&13!==t.which||e.onItemClick()},e.renderIcon=function(){var t=e.props,n=t.showArrow,r=t.expandIcon,a=t.prefixCls,o=t.collapsible;if(!n)return null;var i="function"===typeof r?r(e.props):v.createElement("i",{className:"arrow"});return i&&v.createElement("div",{className:"".concat(a,"-expand-icon"),onClick:"header"===o||"icon"===o?e.onItemClick:null},i)},e.renderTitle=function(){var t=e.props,n=t.header,r=t.prefixCls,a=t.collapsible;return v.createElement("span",{className:"".concat(r,"-header-text"),onClick:"header"===a?e.onItemClick:null},n)},e}return(0,u.Z)(n,[{key:"shouldComponentUpdate",value:function(e){return!O()(this.props,e)}},{key:"render",value:function(){var e,t,n=this.props,o=n.className,i=n.id,l=n.style,s=n.prefixCls,u=n.headerClass,d=n.children,f=n.isActive,p=n.destroyInactivePanel,m=n.accordion,h=n.forceRender,O=n.openMotion,b=n.extra,y=n.collapsible,Q=(0,g.Z)(n,x),Z="disabled"===y,P="header"===y,w="icon"===y,S=c()((e={},(0,a.Z)(e,"".concat(s,"-item"),!0),(0,a.Z)(e,"".concat(s,"-item-active"),f),(0,a.Z)(e,"".concat(s,"-item-disabled"),Z),e),o),N={className:c()("".concat(s,"-header"),(t={},(0,a.Z)(t,u,u),(0,a.Z)(t,"".concat(s,"-header-collapsible-only"),P),(0,a.Z)(t,"".concat(s,"-icon-collapsible-only"),w),t)),"aria-expanded":f,"aria-disabled":Z,onKeyPress:this.handleKeyPress};P||w||(N.onClick=this.onItemClick,N.role=m?"tab":"button",N.tabIndex=Z?-1:0);var T=null!==b&&void 0!==b&&"boolean"!==typeof b;return delete Q.header,delete Q.panelKey,delete Q.onItemClick,delete Q.showArrow,delete Q.expandIcon,v.createElement("div",(0,r.Z)({},Q,{className:S,style:l,id:i}),v.createElement("div",N,this.renderIcon(),this.renderTitle(),T&&v.createElement("div",{className:"".concat(s,"-extra")},b)),v.createElement(k.default,(0,r.Z)({visible:f,leavedClassName:"".concat(s,"-content-hidden")},O,{forceRender:h,removeOnLeave:p}),(function(e,t){var n=e.className,r=e.style;return v.createElement(C,{ref:t,prefixCls:s,className:n,style:r,isActive:f,forceRender:h,role:m?"tabpanel":null},d)})))}}]),n}(v.Component);Q.defaultProps={showArrow:!0,isActive:!1,onItemClick:function(){},headerClass:"",forceRender:!1};var Z=Q;function P(e){var t=e;if(!Array.isArray(t)){var n=(0,p.Z)(t);t="number"===n||"string"===n?[t]:[]}return t.map((function(e){return String(e)}))}var w=function(e){(0,d.Z)(n,e);var t=(0,f.Z)(n);function n(e){var r;(0,s.Z)(this,n),(r=t.call(this,e)).onClickItem=function(e){var t=r.state.activeKey;if(r.props.accordion)t=t[0]===e?[]:[e];else{var n=(t=(0,l.Z)(t)).indexOf(e);n>-1?t.splice(n,1):t.push(e)}r.setActiveKey(t)},r.getNewChild=function(e,t){if(!e)return null;var n=r.state.activeKey,a=r.props,o=a.prefixCls,i=a.openMotion,c=a.accordion,l=a.destroyInactivePanel,s=a.expandIcon,u=a.collapsible,d=e.key||String(t),f=e.props,p=f.header,m=f.headerClass,h=f.destroyInactivePanel,O=f.collapsible,g=null!==O&&void 0!==O?O:u,k={key:d,panelKey:d,header:p,headerClass:m,isActive:c?n[0]===d:n.indexOf(d)>-1,prefixCls:o,destroyInactivePanel:null!==h&&void 0!==h?h:l,openMotion:i,accordion:c,children:e.props.children,onItemClick:"disabled"===g?null:r.onClickItem,expandIcon:s,collapsible:g};return"string"===typeof e.type?e:(Object.keys(k).forEach((function(e){"undefined"===typeof k[e]&&delete k[e]})),v.cloneElement(e,k))},r.getItems=function(){var e=r.props.children;return(0,m.Z)(e).map(r.getNewChild)},r.setActiveKey=function(e){"activeKey"in r.props||r.setState({activeKey:e}),r.props.onChange(r.props.accordion?e[0]:e)};var a=e.activeKey,o=e.defaultActiveKey;return"activeKey"in e&&(o=a),r.state={activeKey:P(o)},r}return(0,u.Z)(n,[{key:"shouldComponentUpdate",value:function(e,t){return!O()(this.props,e)||!O()(this.state,t)}},{key:"render",value:function(){var e,t=this.props,n=t.prefixCls,r=t.className,o=t.style,i=t.accordion,l=c()((e={},(0,a.Z)(e,n,!0),(0,a.Z)(e,r,!!r),e));return v.createElement("div",{className:l,style:o,role:i?"tablist":null},this.getItems())}}],[{key:"getDerivedStateFromProps",value:function(e){var t={};return"activeKey"in e&&(t.activeKey=P(e.activeKey)),t}}]),n}(v.Component);w.defaultProps={prefixCls:"rc-collapse",onChange:function(){},accordion:!1,destroyInactivePanel:!1},w.Panel=Z;var S=w,N=(w.Panel,n(50309)),T=n(48698),U=n(7189),I=n(90690),J=function(e){var t,n=v.useContext(T.E_),i=n.getPrefixCls,l=n.direction,s=e.prefixCls,u=e.className,d=void 0===u?"":u,f=e.bordered,p=void 0===f||f,h=e.ghost,O=e.expandIconPosition,g=void 0===O?"start":O,k=i("collapse",s),b=v.useMemo((function(){return"left"===g?"start":"right"===g?"end":g}),[g]),y=c()("".concat(k,"-icon-position-").concat(b),(t={},(0,a.Z)(t,"".concat(k,"-borderless"),!p),(0,a.Z)(t,"".concat(k,"-rtl"),"rtl"===l),(0,a.Z)(t,"".concat(k,"-ghost"),!!h),t),d),C=(0,r.Z)((0,r.Z)({},U.ZP),{motionAppear:!1,leavedClassName:"".concat(k,"-content-hidden")});return v.createElement(S,(0,r.Z)({openMotion:C},e,{expandIcon:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.expandIcon,r=n?n(t):v.createElement(o.Z,{rotate:t.isActive?90:void 0});return(0,I.Tm)(r,(function(){return{className:c()(r.props.className,"".concat(k,"-arrow"))}}))},prefixCls:k,className:y}),function(){var t=e.children;return(0,m.Z)(t).map((function(e,t){var n;if(null===(n=e.props)||void 0===n?void 0:n.disabled){var a=e.key||String(t),o=e.props,i=o.disabled,c=o.collapsible,l=(0,r.Z)((0,r.Z)({},(0,N.Z)(e.props,["disabled"])),{key:a,collapsible:null!==c&&void 0!==c?c:i?"disabled":void 0});return(0,I.Tm)(e,l)}return e}))}())};J.Panel=function(e){var t=v.useContext(T.E_).getPrefixCls,n=e.prefixCls,o=e.className,i=void 0===o?"":o,l=e.showArrow,s=void 0===l||l,u=t("collapse",n),d=c()((0,a.Z)({},"".concat(u,"-no-arrow"),!s),i);return v.createElement(S.Panel,(0,r.Z)({},e,{prefixCls:u,className:d}))};var A=J},65726:function(e,t,n){n.d(t,{i6:function(){return M}});var r=n(15671),a=n(43144),o=n(37762),i=n(46138),c=n(63193),l=n(21416),s=n(18714),u=5,d=6,f=20,p=21,m=24;function v(e){return e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57}function h(e,t,n){for(var r=!1;;){if(e.next<0)return;if(e.next==t&&!r)return void e.advance();r=n&&!r&&92==e.next,e.advance()}}function O(e,t){for(;95==e.next||v(e.next);)null!=t&&(t+=String.fromCharCode(e.next)),e.advance();return t}function g(e,t){for(;48==e.next||49==e.next;)e.advance();t&&e.next==t&&e.advance()}function k(e,t){for(;;){if(46==e.next){if(t)break;t=!0}else if(e.next<48||e.next>57)break;e.advance()}if(69==e.next||101==e.next)for(e.advance(),43!=e.next&&45!=e.next||e.advance();e.next>=48&&e.next<=57;)e.advance()}function b(e){for(;!(e.next<0||10==e.next);)e.advance()}function y(e,t){for(var n=0;n<t.length;n++)if(t.charCodeAt(n)==e)return!0;return!1}var C=" \t\r\n";function x(e,t,n){var r=Object.create(null);r.true=r.false=u,r.null=r.unknown=d;var a,i=(0,o.Z)(e.split(" "));try{for(i.s();!(a=i.n()).done;){var c=a.value;c&&(r[c]=f)}}catch(k){i.e(k)}finally{i.f()}var l,s=(0,o.Z)(t.split(" "));try{for(s.s();!(l=s.n()).done;){var v=l.value;v&&(r[v]=p)}}catch(k){s.e(k)}finally{s.f()}var h,O=(0,o.Z)((n||"").split(" "));try{for(O.s();!(h=O.n()).done;){var g=h.value;g&&(r[g]=m)}}catch(k){O.e(k)}finally{O.f()}return r}var Q="array binary bit boolean char character clob date decimal double float int integer interval large national nchar nclob numeric object precision real smallint time timestamp varchar varying ",Z="absolute action add after all allocate alter and any are as asc assertion at authorization before begin between both breadth by call cascade cascaded case cast catalog check close collate collation column commit condition connect connection constraint constraints constructor continue corresponding count create cross cube current current_date current_default_transform_group current_transform_group_for_type current_path current_role current_time current_timestamp current_user cursor cycle data day deallocate declare default deferrable deferred delete depth deref desc describe descriptor deterministic diagnostics disconnect distinct do domain drop dynamic each else elseif end end-exec equals escape except exception exec execute exists exit external fetch first for foreign found from free full function general get global go goto grant group grouping handle having hold hour identity if immediate in indicator initially inner inout input insert intersect into is isolation join key language last lateral leading leave left level like limit local localtime localtimestamp locator loop map match method minute modifies module month names natural nesting new next no none not of old on only open option or order ordinality out outer output overlaps pad parameter partial path prepare preserve primary prior privileges procedure public read reads recursive redo ref references referencing relative release repeat resignal restrict result return returns revoke right role rollback rollup routine row rows savepoint schema scroll search second section select session session_user set sets signal similar size some space specific specifictype sql sqlexception sqlstate sqlwarning start state static system_user table temporary then timezone_hour timezone_minute to trailing transaction translation treat trigger under undo union unique unnest until update usage user using value values view when whenever where while with without work write year zone ",P={backslashEscapes:!1,hashComments:!1,spaceAfterDashes:!1,slashComments:!1,doubleQuotedStrings:!1,doubleDollarQuotedStrings:!1,unquotedBitLiterals:!1,treatBitsAsBytes:!1,charSetCasts:!1,operatorChars:"*+-%<>!=&|~^/",specialVar:"?",identifierQuotes:'"',words:x(Z,Q)};function w(e){return new l.Jq((function(t){var n,r,a=t.next;if(t.advance(),y(a,C)){for(;y(t.next,C);)t.advance();t.acceptToken(36)}else if(36==a&&36==t.next&&e.doubleDollarQuotedStrings)!function(e){for(;;){if(e.next<0||e.peek(1)<0)return;if(36==e.next&&36==e.peek(1))return void e.advance(2);e.advance()}}(t),t.acceptToken(3);else if(39==a||34==a&&e.doubleQuotedStrings)h(t,a,e.backslashEscapes),t.acceptToken(3);else if(35==a&&e.hashComments||47==a&&47==t.next&&e.slashComments)b(t),t.acceptToken(1);else if(45!=a||45!=t.next||e.spaceAfterDashes&&32!=t.peek(1))if(47==a&&42==t.next){t.advance();for(var o=-1,i=1;!(t.next<0);)if(t.advance(),42==o&&47==t.next){if(! --i){t.advance();break}o=-1}else 47==o&&42==t.next?(i++,o=-1):o=t.next;t.acceptToken(2)}else if(101!=a&&69!=a||39!=t.next)if(110!=a&&78!=a||39!=t.next||!e.charSetCasts)if(95==a&&e.charSetCasts)for(var c=0;;c++){if(39==t.next&&c>1){t.advance(),h(t,39,e.backslashEscapes),t.acceptToken(3);break}if(!v(t.next))break;t.advance()}else if(40==a)t.acceptToken(7);else if(41==a)t.acceptToken(8);else if(123==a)t.acceptToken(9);else if(125==a)t.acceptToken(10);else if(91==a)t.acceptToken(11);else if(93==a)t.acceptToken(12);else if(59==a)t.acceptToken(13);else if(e.unquotedBitLiterals&&48==a&&98==t.next)t.advance(),g(t),t.acceptToken(22);else if(98!=a&&66!=a||39!=t.next&&34!=t.next){if(48==a&&(120==t.next||88==t.next)||(120==a||88==a)&&39==t.next){var l=39==t.next;for(t.advance();(r=t.next)>=48&&r<=57||r>=97&&r<=102||r>=65&&r<=70;)t.advance();l&&39==t.next&&t.advance(),t.acceptToken(4)}else if(46==a&&t.next>=48&&t.next<=57)k(t,!0),t.acceptToken(4);else if(46==a)t.acceptToken(14);else if(a>=48&&a<=57)k(t,!1),t.acceptToken(4);else if(y(a,e.operatorChars)){for(;y(t.next,e.operatorChars);)t.advance();t.acceptToken(15)}else if(y(a,e.specialVar))t.next==a&&t.advance(),function(e){if(39==e.next||34==e.next||96==e.next){var t=e.next;e.advance(),h(e,t,!1)}else O(e)}(t),t.acceptToken(17);else if(y(a,e.identifierQuotes))h(t,a,!1),t.acceptToken(19);else if(58==a||44==a)t.acceptToken(16);else if(v(a)){var s=O(t,String.fromCharCode(a));t.acceptToken(46==t.next?18:null!==(n=e.words[s.toLowerCase()])&&void 0!==n?n:18)}}else{var u=t.next;t.advance(),e.treatBitsAsBytes?(h(t,u,e.backslashEscapes),t.acceptToken(23)):(g(t,u),t.acceptToken(22))}else t.advance(),h(t,39,e.backslashEscapes),t.acceptToken(3);else t.advance(),h(t,39,!0);else b(t),t.acceptToken(1)}))}var S=w(P),N=l.WQ.deserialize({version:14,states:"%vQ]QQOOO#wQRO'#DSO$OQQO'#CwO%eQQO'#CxO%lQQO'#CyO%sQQO'#CzOOQQ'#DS'#DSOOQQ'#C}'#C}O'UQRO'#C{OOQQ'#Cv'#CvOOQQ'#C|'#C|Q]QQOOQOQQOOO'`QQO'#DOO(xQRO,59cO)PQQO,59cO)UQQO'#DSOOQQ,59d,59dO)cQQO,59dOOQQ,59e,59eO)jQQO,59eOOQQ,59f,59fO)qQQO,59fOOQQ-E6{-E6{OOQQ,59b,59bOOQQ-E6z-E6zOOQQ,59j,59jOOQQ-E6|-E6|O+VQRO1G.}O+^QQO,59cOOQQ1G/O1G/OOOQQ1G/P1G/POOQQ1G/Q1G/QP+kQQO'#C}O+rQQO1G.}O)PQQO,59cO,PQQO'#Cw",stateData:",[~OtOSPOSQOS~ORUOSUOTUOUUOVROXSOZTO]XO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O^]ORvXSvXTvXUvXVvXXvXZvX]vX_vX`vXavXbvXcvXdvXevXfvXgvXhvX~OsvX~P!jOa_Ob_Oc_O~ORUOSUOTUOUUOVROXSOZTO^tO_UO`UOa`Ob`Oc`OdUOeUOfUOgUOhUO~OWaO~P$ZOYcO~P$ZO[eO~P$ZORUOSUOTUOUUOVROXSOZTO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O]hOsoX~P%zOajObjOcjO~O^]ORkaSkaTkaUkaVkaXkaZka]ka_ka`kaakabkackadkaekafkagkahka~Oska~P'kO^]O~OWvXYvX[vX~P!jOWnO~P$ZOYoO~P$ZO[pO~P$ZO^]ORkiSkiTkiUkiVkiXkiZki]ki_ki`kiakibkickidkiekifkigkihki~Oski~P)xOWkaYka[ka~P'kO]hO~P$ZOWkiYki[ki~P)xOasObsOcsO~O",goto:"#hwPPPPPPPPPPPPPPPPPPPPPPPPPPx||||!Y!^!d!xPPP#[TYOZeUORSTWZbdfqT[OZQZORiZSWOZQbRQdSQfTZgWbdfqQ^PWk^lmrQl_Qm`RrseVORSTWZbdfq",nodeNames:"\u26a0 LineComment BlockComment String Number Bool Null ( ) { } [ ] ; . Operator Punctuation SpecialVar Identifier QuotedIdentifier Keyword Type Bits Bytes Builtin Script Statement CompositeIdentifier Parens Braces Brackets Statement",maxTerm:38,skippedNodes:[0,1,2],repeatNodeCount:3,tokenData:"RORO",tokenizers:[0,S],topRules:{Script:[0,25]},tokenPrec:0});function T(e){for(var t=e.cursor().moveTo(e.from,-1);/Comment/.test(t.name);)t.moveTo(t.from,-1);return t.node}function U(e,t){var n=e.sliceString(t.from,t.to),r=/^([`'"])(.*)\1$/.exec(n);return r?r[2]:n}function I(e){return e&&("Identifier"==e.name||"QuotedIdentifier"==e.name)}function J(e,t){if("CompositeIdentifier"==t.name){for(var n=[],r=t.firstChild;r;r=r.nextSibling)I(r)&&n.push(U(e,r));return n}return[U(e,t)]}function A(e,t){for(var n=[];;){if(!t||"."!=t.name)return n;var r=T(t);if(!I(r))return n;n.unshift(U(e,r)),t=T(r)}}function B(e,t){var n=(0,i.qz)(e).resolveInner(t,-1),r=function(e,t){for(var n,r=t;!n;r=r.parent){if(!r)return null;"Statement"==r.name&&(n=r)}for(var a=null,o=n.firstChild,i=!1,c=null;o;o=o.nextSibling){var l="Keyword"==o.name?e.sliceString(o.from,o.to).toLowerCase():null,s=null;if(i)if("as"==l&&c&&I(o.nextSibling))s=U(e,o.nextSibling);else{if(l&&E.has(l))break;c&&I(o)&&(s=U(e,o))}else i="from"==l;s&&(a||(a=Object.create(null)),a[s]=J(e,c)),c=/Identifier$/.test(o.name)?o:null}return a}(e.doc,n);return"Identifier"==n.name||"QuotedIdentifier"==n.name||"Keyword"==n.name?{from:n.from,quoted:"QuotedIdentifier"==n.name?e.doc.sliceString(n.from,n.from+1):null,parents:A(e.doc,T(n)),aliases:r}:"."==n.name?{from:t,quoted:null,parents:A(e.doc,n),aliases:r}:{from:t,quoted:null,parents:[],empty:!0,aliases:r}}var E=new Set("where group having order union intersect except all distinct limit offset fetch for".split(" "));var R=/^\w*$/,F=/^[`'"]?\w*[`'"]?$/,_=function(){function e(){(0,r.Z)(this,e),this.list=[],this.children=void 0}return(0,a.Z)(e,[{key:"child",value:function(t){var n=this.children||(this.children=Object.create(null));return n[t]||(n[t]=new e)}},{key:"childCompletions",value:function(e){return this.children?Object.keys(this.children).filter((function(e){return e})).map((function(t){return{label:t,type:e}})):[]}}]),e}();var X=N.configure({props:[i.uj.add({Statement:(0,i.tC)()}),i.x0.add({Statement:function(e){return{from:e.firstChild.to,to:e.to}},BlockComment:function(e){return{from:e.from+2,to:e.to-2}}}),(0,c.Gv)({Keyword:c.pJ.keyword,Type:c.pJ.typeName,Builtin:c.pJ.standard(c.pJ.name),Bits:c.pJ.number,Bytes:c.pJ.string,Bool:c.pJ.bool,Null:c.pJ.null,Number:c.pJ.number,String:c.pJ.string,Identifier:c.pJ.name,QuotedIdentifier:c.pJ.special(c.pJ.string),SpecialVar:c.pJ.special(c.pJ.name),LineComment:c.pJ.lineComment,BlockComment:c.pJ.blockComment,Operator:c.pJ.operator,"Semi Punctuation":c.pJ.punctuation,"( )":c.pJ.paren,"{ }":c.pJ.brace,"[ ]":c.pJ.squareBracket})]}),z=function(){function e(t,n){(0,r.Z)(this,e),this.dialect=t,this.language=n}return(0,a.Z)(e,[{key:"extension",get:function(){return this.language.extension}}],[{key:"define",value:function(t){var n=function(e,t,n,r){var a={};for(var o in P)a[o]=(e.hasOwnProperty(o)?e:P)[o];return t&&(a.words=x(t,n||"",r)),a}(t,t.keywords,t.types,t.builtin);return new e(n,i.qp.define({name:"sql",parser:X.configure({tokenizers:[{from:S,to:w(n)}]}),languageData:{commentTokens:{line:"--",block:{open:"/*",close:"*/"}},closeBrackets:{brackets:["(","[","{","'",'"',"`"]}}}))}}]),e}();function K(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(e,t){var n=Object.keys(e).map((function(n){return{label:t?n.toUpperCase():n,type:e[n]==p?"type":e[n]==f?"keyword":"variable",boost:-1}}));return(0,s.eC)(["QuotedIdentifier","SpecialVar","String","LineComment","BlockComment","."],(0,s.Mb)(n))}(e.dialect.words,t)}function q(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e.language.data.of({autocomplete:K(e,t)})}function j(e){return e.schema?function(e,t,n,r,a){var i=new _,c=i.child(a||"");for(var l in e){var s=l.indexOf(".");(s>-1?i.child(l.slice(0,s)):c).child(s>-1?l.slice(s+1):l).list=e[l].map((function(e){return"string"==typeof e?{label:e,type:"property"}:e}))}for(var u in c.list=(t||c.childCompletions("type")).concat(r?c.child(r).list:[]),i.children){var d=i.child(u);d.list.length||(d.list=d.childCompletions("type"))}return i.list=c.list.concat(n||i.childCompletions("type")),function(e){var t=B(e.state,e.pos),n=t.parents,a=t.from,l=t.quoted,s=t.empty,u=t.aliases;if(s&&!e.explicit)return null;u&&1==n.length&&(n=u[n[0]]||n);var d,f=i,p=(0,o.Z)(n);try{for(p.s();!(d=p.n()).done;){for(var m=d.value;!f.children||!f.children[m];)if(f==i)f=c;else{if(f!=c||!r)return null;f=f.child(r)}f=f.child(m)}}catch(k){p.e(k)}finally{p.f()}var v,h,O=l&&e.state.sliceDoc(e.pos,e.pos+1)==l,g=f.list;return f==i&&u&&(g=g.concat(Object.keys(u).map((function(e){return{label:e,type:"constant"}})))),{from:a,to:O?e.pos+1:void 0,options:(v=l,h=g,v?h.map((function(e){return Object.assign(Object.assign({},e),{label:v+e.label+v,apply:void 0})})):h),validFor:l?F:R}}}(e.schema,e.tables,e.schemas,e.defaultTable,e.defaultSchema):function(){return null}}function D(e){return e.schema?(e.dialect||L).language.data.of({autocomplete:j(e)}):[]}function M(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dialect||L;return new i.ri(t.language,[D(e),q(t,!!e.upperCaseKeywords)])}var L=z.define({})}}]);
//# sourceMappingURL=637.50fb7190.chunk.js.map