{
  "root": true,
  "env": {
    "browser": true,
    "commonjs": true,
    "node": true,
    "es6": true
  },
  // "meta": {
  //   "hasSuggestions": true
  // },
  "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"],
  "globals": {
    "describe": true,
    "beforeEach": true,
    "it": true,
    "expect": true
  },
  "plugins": ["react","@typescript-eslint"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 2018,
    "sourceType": "module",
    "allowImportExportEverywhere": true
  },
  "rules": {
    "no-alert": "error",
    "no-eval": "error",
    "spaced-comment": ["error", "always"],
    // "linebreak-style": ["error", "windows"],
    "linebreak-style": ["error", "unix"],
    "react/prop-types": 0,
    "no-console": 0,
    "no-useless-escape": 0,
    "@typescript-eslint/no-explicit-any": 0,
    "@typescript-eslint/camelcase": 0,
    "@typescript-eslint/no-non-null-assertion": 0,
    "@typescript-eslint/explicit-function-return-type": 0
  }
}
