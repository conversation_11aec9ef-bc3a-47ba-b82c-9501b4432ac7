{"cells": [{"cell_type": "markdown", "id": "22ad6278-a725-4892-8da6-8bb1083cfad0", "metadata": {"tags": []}, "source": ["### 1.介绍\n", "使用pyhive连接远程hiveserver2\n", "\n", "### 2.安装依赖\n", "```\n", "pip install pyhive thrift sasl thrift_sasl\n", "```\n", "### 3.使用模式\n", "```\n", "NOSASL\n", "```\n", "### 4.配置hive-site.yml\n", "```xml\n", "<property>\n", "    <name>hive.server2.authentication</name>\n", "    <value>NOSASL</value>\n", "    <description>\n", "      Expects one of [nosasl, none, ldap, kerberos, pam, custom].\n", "      Client authentication types.\n", "        NONE: no authentication check\n", "        LDAP: LDAP/AD based authentication\n", "        KERBEROS: Kerberos/GSSAPI authentication\n", "        CUSTOM: Custom authentication provider\n", "                (Use with property hive.server2.custom.authentication.class)\n", "        PAM: Pluggable authentication module\n", "        NOSASL:  Raw transport\n", "    </description>\n", "</property>\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "f1c59347-83ac-4604-88cd-d11f0b306130", "metadata": {"tags": []}, "outputs": [], "source": ["import pyhive\n", "from pyhive import hive\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 11, "id": "c3244a4a-816f-4df9-89d0-ae1ec68e0e31", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tab_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  tab_name\n", "0    test1"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["conn = hive.Connection(host = \"hive-service.default\",port = 10000,database='default',auth='NOSASL')\n", "result = pd.read_sql(\"show tables\",conn)\n", "result"]}, {"cell_type": "code", "execution_count": 15, "id": "ff7d4567-a1ac-4133-a8f9-a83e36c1ed05", "metadata": {"tags": []}, "outputs": [], "source": ["cursor = conn.cursor()\n", "sql = \"CREATE TABLE IF NOT EXISTS test2(key INT,value string) ROW FORMAT DELIMITED FIELDS TERMINATED BY ','\"\n", "cursor.execute(sql)"]}, {"cell_type": "code", "execution_count": 16, "id": "9a9faa94-bc7c-4edd-a96d-7f18e666af94", "metadata": {"tags": []}, "outputs": [], "source": ["sql = 'insert into test2 values(1,\"1\")'\n", "cursor.execute(sql)"]}, {"cell_type": "code", "execution_count": 17, "id": "cbec08cb-3aec-42e0-9aac-0ef06dafb622", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(1, '1')]\n"]}], "source": ["sql = \" select * from test2\"\n", "cursor.execute(sql)\n", "result = cursor.fetchall()\n", "print(result)"]}, {"cell_type": "code", "execution_count": 18, "id": "65028b92-f3b8-4853-8905-1632ea907253", "metadata": {"tags": []}, "outputs": [], "source": ["cursor.close()\n", "conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "140802ad-6af2-4c83-b348-2299dc5de017", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}