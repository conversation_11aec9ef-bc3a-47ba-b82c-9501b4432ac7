{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from random import random\n", "from operator import add\n", "from pyspark.sql import SparkSession\n", "\n", "if __name__ == \"__main__\":\n", "    spark = SparkSession\\\n", "    .builder\\\n", "    .appName(\"PythonPi-Local\")\\\n", "    .master(\"local\")\\\n", "    .getOrCreate()\n", "\n", "    n = 100000 * 2\n", "\n", "    def f(_):\n", "        x = random() * 2 - 1\n", "        y = random() * 2 - 1\n", "        return 1 if x ** 2 + y ** 2 <= 1 else 0\n", "\n", "    count = spark.sparkContext.parallelize(range(1, n + 1), 2).map(f).reduce(add)\n", "    print(\"Pi is roughly %f\" % (4.0 * count / n))\n", "\n", "    spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3.0}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 0}