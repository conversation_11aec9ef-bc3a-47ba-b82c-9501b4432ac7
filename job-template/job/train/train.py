import requests
import argparse
import os
import sys
import json

import logging
from  threading import Event

from clearml import Task
from clearml.automation import (
    DiscreteParameterRange, HyperParameterOptimizer, RandomSearch,
    UniformIntegerParameterRange)


# 超参提前停止控制
hyper_train_stop_event = Event()


# TODO: 超参策略后续也作为参数传递
try:
    from clearml.automation.optuna import OptimizerOptuna  # noqa
    aSearchStrategy = OptimizerOptuna
except ImportError as ex:
    try:
        from clearml.automation.hpbandster import OptimizerBOHB  # noqa
        aSearchStrategy = OptimizerBOHB
    except ImportError as ex:
        logging.getLogger().warning(
            'Apologies, it seems you do not have \'optuna\' or \'hpbandster\' installed, '
            'we will be using RandomSearch strategy instead')
        aSearchStrategy = RandomSearch


def job_complete_callback(
    reach_max=None,
    reach_min=None,
    reach_exact=None,
    optimizer = None,
):
    
    def origin_callback(
        job_id,                 # type: str
        objective_value,        # type: float
        objective_iteration,    # type: int
        job_parameters,         # type: dict
        top_performance_job_id  # type: str
    ):
        print('Job completed!', job_id, objective_value, objective_iteration, job_parameters)
        if job_id == top_performance_job_id:
            print('WOOT WOOT we broke the record! Objective reached {}'.format(objective_value))
        # TODO: 在此处可以看看是否能够达到目标并提前停止

        if isinstance(objective_value,list):
            objective_value = objective_value[0]

        if (reach_exact and objective_value == reach_exact) \
            or (reach_min and objective_value <= reach_min)  \
            or (reach_max and objective_value >= reach_max ):
            
            optimizer.stop()
    return origin_callback


def create_project(name:str, clearml_api_host:str) ->str:

    try:
        project_payload = {"name": name}

        get_all_uri = f"{clearml_api_host}/projects.get_all"
        resp = requests.post(get_all_uri, json = project_payload).json()
        if resp.get("data").get("projects"):
            return resp.get("data").get("projects")[0].get("id")

        create_uri = f"{clearml_api_host}/projects.create"
        resp = requests.post(create_uri, json = project_payload).json()
        return resp.get("data").get("id")
    except Exception as ex:
        print(ex)
        return None


# 新建一个用作模板的Task
def create_template_task(project:str, 
                         cmd: str,
                         params: dict,
                         dataset_id: str,
                         dataset_version: str,
                         algo_id: str,
                         algo_version: str,
                         clearml_api_host:str,
                         image: str,
                         task_name: str
                         ) ->str:

    params_dict = {}
    for param in params:
        params_dict.update({param.get("name"): {"section":"Params", "name":param.get("name"), "value":str(param.get("value")) } })
    
    create_task_payload = {
        "name": task_name if task_name else f'{project} template task',
        "type": "training",
        "project": project,
        "comment": f"template task for {project}", 
        "hyperparams":{
            "Args":{"cmd":{"section": "Args","name": "cmd","value":cmd}},           # cmd需要包含训练和推理的内容
            "Params":params_dict,
            "Mounts":{
                "algo_id":{"section": "Mounts","name":"algo_id", "value":algo_id},
                "algo_version":{"section": "Mounts","name":"algo_version", "value":algo_version},
                "dataset_id":{"section": "Mounts","name":"dataset_id", "value":dataset_id},
                "dataset_version":{"section": "Mounts","name":"dataset_version", "value":dataset_version},
                }
        },
        "container":{
            "image":image,
        }
    }

    print(f"Create template task payload {create_task_payload}")

    try:
        uri = f"{clearml_api_host}/tasks.create"
        resp = requests.post(uri, json = create_task_payload).json()
        print(resp.get('data'))
        return resp.get("data").get("id")
    except Exception as ex:
        print(ex)
        return None

# 启动超参任务 
def start_hyper_tasks(
        project: str,
        template_task: str,
        param_algos: list,
        algo_object_metric_name: str
):

    task = Task.init(project_name=project,
                 task_name=project,
                 task_type=Task.TaskTypes.optimizer,
                 reuse_last_task_id=False)
    
    execution_queue = os.environ.get("CLEARML_QUEUE", "default")

    hyper_parameters = []
    for param in param_algos:
        if param.get("name") and param.get("min") and param.get("max") and param.get("step"):
            print(f'name :{param.get("name")}    min: {param.get("min")}   max: {param.get("max")}    step : {param.get("step")}')
            p = UniformIntegerParameterRange(f'Params/{param.get("name")}', min_value=param.get("min"), max_value=param.get("max"), step_size=param.get("step"))
            hyper_parameters.append(p)
    
    print(f"hyper params : {hyper_parameters}")

    #TODO: 此处很多配置都需要通过参数的方式传递进来
    an_optimizer = HyperParameterOptimizer(
        base_task_id=template_task,
        hyper_parameters=hyper_parameters,
        objective_metric_title=algo_object_metric_name,
        objective_metric_series=algo_object_metric_name,
        objective_metric_sign='max',
        max_number_of_concurrent_tasks=2,
        optimizer_class=aSearchStrategy,
        execution_queue=execution_queue,
        spawn_project=None,
        save_top_k_tasks_only=None,
        time_limit_per_job=10.,
        pool_period_min=0.2,
        total_max_jobs=10,
        min_iteration_per_job=10,
        max_iteration_per_job=30,
    )
    an_optimizer.set_report_period(0.2)
    an_optimizer.start(job_complete_callback=job_complete_callback(
        reach_max= 100,
        reach_min=0,
        reach_exact= 50,
        optimizer=an_optimizer
    ))
    an_optimizer.set_time_limit(in_minutes=120.0)
    an_optimizer.wait()

    # hyper_train_stop_event.wait()
    top_exp = an_optimizer.get_top_experiments(top_k=3)
    print([t.id for t in top_exp])
    an_optimizer.stop()


# 启动普通训练任务
def start_normal_task(project:str, 
                    cmd: str,
                    params: dict,
                    dataset_id: str,
                    dataset_version: str,
                    algo_id: str,
                    algo_version: str,
                    clearml_api_host:str,
                    image: str):
    project_name = os.environ.get("KFJ_TASK_NAME", "")
    template_task = create_template_task(
        project=project,
        cmd=cmd,
        params=params,
        dataset_id=dataset_id,
        dataset_version=dataset_version,
        algo_id=algo_id,
        algo_version=algo_version,
        clearml_api_host=clearml_api_host,
        image=image,
        task_name=f'{project_name} task'
    )
    if template_task:
       # 启动task
       enqueue_task_payload = {
        "task": template_task,
        "queue_name": os.environ.get("CLEARML_QUEUE", "default"),
    }
       try:
            uri = f"{clearml_api_host}/tasks.create"
            resp = requests.post(uri, json = enqueue_task_payload).json()
            print(resp.get('data'))
            return resp.get("data").get("id")
       except Exception as ex:
            print(ex)
            return None

# 判断是否创建超参训练，后续可根据参数控制
def is_hyper_task(params):
    for param in params:
        if param.get('min') and param.get('max'):
            return True
    return False

def training_out_puts(algo_id: str, algo_version: str):
    pass

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="算法训练配置")
    parser.add_argument('--algo_id', type=str, required=True, help="算法ID")
    parser.add_argument('--algo_version', type=str, required=True, help="算法版本")
    parser.add_argument('--dataset_id', type=str, required=True, help="数据集ID")
    parser.add_argument('--dataset_version', type=str, required=True, help="数据集版本")
    parser.add_argument('--cmd', type=str, required=True, help="运行指令")
    parser.add_argument('--algo_params', type=str, required=True, help="算法参数")  
    parser.add_argument('--algo_object_metric_name', type=str, required=True, help="超参目标指标")
    parser.add_argument('--image', type=str, required=True, help="训练使用镜像（来自算法仓）")
    args = parser.parse_args()


    # 1、创建一个project，一个流水线运行任务当成一个project
    # 2、创建训练任务的模板Task
    # 3、基于模板Task创建超参训练任务

    print(f"Algo params {args.algo_params}")
    algo_params = json.loads(args.algo_params)

    project_name = os.environ.get("KFJ_TASK_NAME", "")
    clearml_api_host = os.environ.get("CLEARML_API_HOST", "")

    project = create_project(project_name,clearml_api_host)
    if project:

        if is_hyper_task(algo_params):
            # 超参训练
            # TODO: 创建之前可以先清掉project下已有的task
            template_task = create_template_task(
                project=project,
                cmd=args.cmd,
                params=algo_params,
                algo_id=args.algo_id,
                algo_version=args.algo_version,
                dataset_id=args.dataset_id,
                dataset_version=args.dataset_version,
                clearml_api_host=clearml_api_host,
                image=args.image
            )
            if template_task:
                start_hyper_tasks(project=project_name,
                                template_task=template_task,
                                param_algos=algo_params,
                                algo_object_metric_name=args.algo_object_metric_name
                              )
        else:
            # 普通训练 
            start_normal_task()

