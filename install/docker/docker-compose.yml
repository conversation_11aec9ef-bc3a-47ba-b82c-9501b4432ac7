version: '3'
services:
  redis:
    image: bitnami/redis:6.2.12
    restart: unless-stopped
    environment:
      REDIS_PASSWORD: admin
    ports:
      - "6379:6379"

#  mysql:
#    image: mysql:8.0.32
#    restart: unless-stopped
#    environment:
#      MYSQL_ROOT_PASSWORD: admin
#      MYSQL_DATABASE: kubeflow
#      TZ: Asia/Shanghai
#    ports:
#      - "3306:3306"
#    volumes:
#      - ./data/mysql:/var/lib/mysql
#      - ./docker-add-file/mysqld.cnf:/etc/mysql/mysql.conf.d/mysqld.cnf
#    healthcheck:
#      test: "/usr/bin/mysql --user=root --password=admin --execute \"SHOW DATABASES;\""
#      timeout: 45s
#      interval: 10s
#      retries: 10

  frontend:
    image: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard-frontend:2025.03.01
    restart: unless-stopped
    command: ['sh','-c','nginx -g "daemon off;"']
    ports:
      - '80:80'
    volumes:
      - ../../myapp/static/appbuilder/frontend:/data/web/frontend
      - ../../myapp/static:/data/web/static
      - ../../install/docker/dockerFrontend/nginx.conf:/etc/nginx/nginx.conf
      - ../../install/docker/dockerFrontend/nginx.80.conf:/etc/nginx/conf.d/default.conf

  myapp:
    image: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard:2025.03.01
    restart: unless-stopped
    command: [ 'bash','-c','/entrypoint.sh' ]
    environment:
      STAGE: 'dev'
      REDIS_HOST: 'redis'
      REDIS_PORT: '6379'
      REDIS_PASSWORD: admin
      MYSQL_SERVICE: 'mysql+pymysql://root:<EMAIL>:3306/kubeflow?charset=utf8'
      ENVIRONMENT: DEV
    depends_on:
      - redis
#      - mysql
    volumes:
      - ../../myapp/static/appbuilder/frontend:/data/web/frontend
      - ../../myapp/:/home/<USER>/myapp/
      - ../../aihub/:/cube-studio/aihub/
      - ../../job-template/:/cube-studio/job-template/
      - ./file:/data/k8s/kubeflow
      - ./entrypoint.sh:/entrypoint.sh
      - ./config.py:/home/<USER>/myapp/config.py
      - ./project.py:/home/<USER>/myapp/project.py
      - ./kubeconfig:/home/<USER>/kubeconfig

#
#  beat:
#    image: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard:2025.03.01
#    restart: "no"
#    command: ["bash","-c","celery --app=myapp.tasks.celery_app:celery_app beat --loglevel=info"]
#    shm_size: '100gb'
#    environment:
#      REDIS_HOST: 'redis'
#      REDIS_PORT: '6379'
#      REDIS_PASSWORD: admin
#      MYSQL_SERVICE: 'mysql+pymysql://root:<EMAIL>:3306/kubeflow?charset=utf8'
#      ENVIRONMENT: DEV
#    depends_on:
#      - redis
#    volumes:
#      - ../../myapp/:/home/<USER>/myapp/
#      - ./file:/pvc
#      - ./entrypoint.sh:/entrypoint.sh
#      - ./config.py:/home/<USER>/myapp/config.py
#      - ./kubeconfig:/home/<USER>/kubeconfig
##
#
#  worker:
#    image: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard:2025.03.01
#    restart: unless-stopped
#    # 默认的prefork池针对长时间的任务支持不是很好，如果任务运行时间有数分钟/小时，建议启用 Celery 的 -Ofair 命令参数
#    command: ["bash","-c","celery --app=myapp.tasks.celery_app:celery_app worker --loglevel=info --pool=prefork -Ofair -c 2 -n worker@%h"]
#    environment:
#      REDIS_HOST: 'redis'
#      REDIS_PORT: '6379'
#      REDIS_PASSWORD: admin
#      MYSQL_SERVICE: 'mysql+pymysql://root:<EMAIL>:3306/kubeflow?charset=utf8'
#      ENVIRONMENT: DEV
#    depends_on:
#      - redis
#    volumes:
#      - ../../myapp/:/home/<USER>/myapp/
#      - ./file:/data/k8s/kubeflow
#      - ./entrypoint.sh:/entrypoint.sh
#      - ./config.py:/home/<USER>/myapp/config.py
#      - ./kubeconfig:/home/<USER>/kubeconfig

#  watch:
#    image: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard:2025.03.01
#    restart: unless-stopped
#    # 默认的prefork池针对长时间的任务支持不是很好，如果任务运行时间有数分钟/小时，建议启用 Celery 的 -Ofair 命令参数
#    command: ["supervisord", "-c", "/home/<USER>/myapp/tools/supervisord.conf"]
#    environment:
#      REDIS_HOST: 'redis'
#      REDIS_PORT: '6379'
#      REDIS_PASSWORD: admin
#      MYSQL_SERVICE: 'mysql+pymysql://root:<EMAIL>:3306/kubeflow?charset=utf8'
#      ENVIRONMENT: DEV
#    volumes:
#      - ../../myapp/:/home/<USER>/myapp/
#      - ./file:/data/k8s/kubeflow
#      - ./entrypoint.sh:/entrypoint.sh
#      - ./config.py:/home/<USER>/myapp/config.py
#      - ./kubeconfig:/home/<USER>/kubeconfig

