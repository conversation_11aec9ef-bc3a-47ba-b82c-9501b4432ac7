apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-pushgateway-deployment
  namespace: monitoring
  labels:
    app: cloud-pushgateway-deployment
spec:   # 包括一些container，storage，volume以及其他Kubernetes需要的参数，以及诸如是否在容器失败时重新启动容器的属性。可在特定Kubernetes API找到完整的Kubernetes Pod的属性。
  selector:
    matchLabels:
      app: cloud-pushgateway-pod
  replicas: 1   # 选项定义需要的副本个数，此处可以设置很多属性，例如受此Deployment影响的Pod的选择器等
  template:   # template其实就是对Pod对象的定义
    metadata:
      labels:
        app: cloud-pushgateway-pod
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: monitoring
                    operator: In
                    values:
                      - "true"
      volumes: 
      - name: tz-config
        hostPath:
          path: /usr/share/zoneinfo/Asia/Shanghai
      imagePullSecrets:
        - name: hubsecret
      containers:
      - name: cloud-pushgateway-container     # 容器名称
        image: ccr.ccs.tencentyun.com/cube-studio/prometheus:pushgateway
        command: ['python','server.py']
#        command: ['sleep','300000']
        workingDir: /app/pushgateway    # 
        imagePullPolicy: Always
        env:
          - name: sender
            value: TME_DataInfra         # development
#          - name: receiver
#            value: pengluan
        ports:     # 容器将会监听的指定端口号
        - containerPort: 80
        resources:       
          limits:
            cpu: 1000m
            memory: 2000Mi
          requests:
            cpu: 10m
            memory: 100Mi
        readinessProbe:
          httpGet:
            path: "/"
            port: 80
          initialDelaySeconds: 10
          timeoutSeconds: 5
          periodSeconds: 30
        livenessProbe:
          httpGet:
            path: "/"
            port: 80
          initialDelaySeconds: 100
          timeoutSeconds: 5
          periodSeconds: 30
        volumeMounts:
        - name: tz-config
          mountPath: /etc/localtime

        
# curl -H 'Content-type: application/json' -X POST -d '{"message":"${MY_POD_NAME} juno restart"}' http://9.87.159.253/juno/webhook?username=pengluan&sender_type=wechat
# curl -H 'Content-type: application/json' -X POST -d '{"message":"'"${MY_POD_NAME}"' juno restart"}' http://9.87.159.253/juno/webhook?username=pengluan&sender_type=wechat