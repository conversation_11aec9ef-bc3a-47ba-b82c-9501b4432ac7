
{% import 'appbuilder/general/lib.html' as lib %}

{% set can_add = "can_add" | is_item_visible(modelview_name) %}
{% set can_show = "can_show" | is_item_visible(modelview_name) %}
{% set can_edit = "can_edit" | is_item_visible(modelview_name) %}
{% set can_delete = "can_delete" | is_item_visible(modelview_name) %}
{% set actions = actions | get_actions_on_list(modelview_name) %}

{% if can_add %}
<span class="list-add-action">
    {% set path = url_for(modelview_name + '.add') %}
    {% set path = path | set_link_filters(filters) %}
    &nbsp;{{ lib.lnk_add(path) }}
</span>
{% endif %}

{% if count > 0 %}

{% block begin_content scoped %}
{% endblock %}

{% block begin_loop_header scoped %}
{% endblock %}

{% block begin_loop_values %}
{% endblock %}

{% block end_content scoped %}
{% endblock %}

<div class="form-actions-container">
    {{ lib.render_actions(actions, modelview_name) }}
</div>
{{ lib.action_form(actions,modelview_name) }}

<div class="pagination-container pull-right">
    <strong>{{ _('Record Count') }}:</strong> {{ count }}
    {{ lib.render_pagination(page, page_size, count, modelview_name) }}
    {{ lib.render_set_page_size(page, page_size, count, modelview_name) }}
</div>
<script language="javascript">
    var modelActions = new AdminActions();
</script>

{% else %}
<b>{{_("No records found")}}</b>
{% endif %}