import pandas as pd
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score
import argparse

def load_and_train_knn(
    path: str,
    columns: list = None,
    na_values: list = None,
    test_size: float = 0.2,
    random_state: int = 42,
    n_neighbors: int = 5
):
    """
    加载数据并使用 KNN 分类器进行训练

    参数：
    - path: 数据文件路径（CSV）
    - columns: 数据列名（最后一列为 target）
    - na_values: 缺失值标记
    - test_size: 测试集占比
    - random_state: 随机种子
    - n_neighbors: KNN 的邻居数
    """
    if columns is None:
        columns = ['sepal length (cm)', 'sepal width (cm)',
                   'petal length (cm)', 'petal width (cm)', 'target']
    if na_values is None:
        na_values = ["?", ""]

    # 加载数据
    df = pd.read_csv(path, header=None, names=columns, na_values=na_values)

    X = df[columns[:-1]]
    y = df[columns[-1]]

    # 标签编码
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y_encoded, test_size=test_size, random_state=random_state, stratify=y_encoded
    )

    # 初始化并训练 KNN 模型
    knn = KNeighborsClassifier(n_neighbors=n_neighbors)
    knn.fit(X_train, y_train)

    # 预测并评估
    y_pred = knn.predict(X_test)
    acc = accuracy_score(y_test, y_pred)
    print(f"[INFO] KNN 模型准确率 (k={n_neighbors}): {acc:.4f}")

    return knn, le, acc


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="数据清洗")
    parser.add_argument('--input_path', type=str, required=True, help="输入数据路径")
    parser.add_argument('--columns', type=str, required=True, help="列头")
    parser.add_argument('--n_neighbors', type=int, required=True, help="邻居数量")

    args = parser.parse_args()
    # ✅ 示例调用
    model, label_encoder, accuracy = load_and_train_knn(
        path=args.input_path,
        columns=str.split(args.columns, ","),
        n_neighbors=3
    )