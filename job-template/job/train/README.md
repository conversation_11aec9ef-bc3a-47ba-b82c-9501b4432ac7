## 算法训练组件

### 1、构建镜像：
docker build -f train/Dockerfile -t hub.tiduyun.com:5000/cube-studio/train:v2

### 2、组件配置：
#### 1）启动参数：
{
    "算法训练": {
        "--algo_id": {
           "type": "list",
            "item_type": "str",
            "label": "",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "",
            "placeholder": "",
            "describe": "算法列表获取",
            "editable": 1,
            "syncUrl": "/proxy/algos?page=0&page_size=10",
            "propValue": "id",
            "propLabel": "name"
        },
        "--algo_version": {
            "type": "list",
            "item_type": "str",
            "label": "",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "",
            "placeholder": "",
            "describe": "",
            "editable": 1,
            "propValue": "version",
            "propLabel": "version",
            "syncUrl": "/proxy/algos/${algo_id}/versions"
        },
        "--cmd": {
            "type": "str",
            "item_type": "str",
            "label": "运行命令行",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "python keras_simple.py",
            "placeholder": "",
            "describe": "运行命令行",
            "editable": 1
        },
         "hyperParams": {
            "type": "str",
            "item_type": "str",
            "label": "",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "",
            "placeholder": "",
            "describe": "",
            "editable": 1
        },
        "--image": {
            "type": "str",
            "item_type": "str",
            "label": "算法镜像",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "hub.tiduyun.com:5000/clearml/executor:1.4.1-keras",
            "placeholder": "",
            "describe": "算法镜像",
            "editable": 1
        }
    }
}

#### 2）启动命令：
python train.py

#### 3）环境变量：
CLEARML_API_HOST=http://hub.tiduyun.com:30008
CLEARML_WEB_HOST=http://hub.tiduyun.com:38080
PYTHONUNBUFFERED="1"
CLEARML_QUEUE=default
CLEARML_API_ACCESS_KEY=EGRTCO8JMSIGI6S39GTP43NFWXDQOW
CLEARML_API_SECRET_KEY=x!XTov_G-#vspE*Y(h$Anm&DIc5Ou-F)jsl$PdOyj5wG1&E!Z8
