#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ if .Values.logserver.enabled -}}
{{- $release := .Release }}
{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}-logserver
  labels:
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ $release.Name }}
    heritage: {{ $release.Service }}
    role: alluxio-logserver
spec:
  ports:
    - port: 45600
      name: logging
  selector:
    role: alluxio-logserver
    app: {{ $name }}
    release: {{ $release.Name }}
{{- end }}
