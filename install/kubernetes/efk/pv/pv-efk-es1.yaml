apiVersion: v1
kind: PersistentVolume
metadata:
  name: efk-es1-pv
  namespace: logging
spec:
  capacity:
    storage: 2000Gi
  # volumeMode field requires BlockVolume Alpha feature gate to be enabled.
  volumeMode: Filesystem
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-efk-es
  local:
    path: /data/efk-es1
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: pvrole
          operator: In
          values:
          - node1
