---
# Source: base/templates/reader-serviceaccount.yaml
# This service account aggregates reader permissions for the revisions in a given cluster
# Should be used for remote secret creation.
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-reader-service-account
  namespace: istio-system
  labels:
    app: istio-reader
    release: istio-base
---
# Source: base/templates/serviceaccount.yaml
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
# DO NOT EDIT!
# THIS IS A LEGACY CHART HERE FOR BACKCOMPAT
# UPDATED CHART AT manifests/charts/istio-control/istio-discovery
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istiod-service-account
  namespace: istio-system
  labels:
    app: istiod
    release: istio-base
---
# Source: base/templates/clusterrole.yaml
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
# DO NOT EDIT!
# THIS IS A LEGACY CHART HERE FOR BACKCOMPAT
# UPDATED CHART AT manifests/charts/istio-control/istio-discovery
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istiod-istio-system
  labels:
    app: istiod
    release: istio-base
rules:
  # sidecar injection controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["mutatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update", "patch"]

  # configuration validation webhook controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["validatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update"]

  # istio configuration
  # removing CRD permissions can break older versions of Istio running alongside this control plane (https://github.com/istio/istio/issues/29382)
  # please proceed with caution
  - apiGroups: ["config.istio.io", "security.istio.io", "networking.istio.io", "authentication.istio.io", "rbac.istio.io", "telemetry.istio.io"]
    verbs: ["get", "watch", "list"]
    resources: ["*"]
  - apiGroups: ["networking.istio.io"]
    verbs: [ "get", "watch", "list", "update", "patch", "create", "delete" ]
    resources: [ "workloadentries" ]
  - apiGroups: ["networking.istio.io"]
    verbs: [ "get", "watch", "list", "update", "patch", "create", "delete" ]
    resources: [ "workloadentries/status" ]

  # auto-detect installed CRD definitions
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]

  # discovery and routing
  - apiGroups: [""]
    resources: ["pods", "nodes", "services", "namespaces", "endpoints"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]

  # ingress controller
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses", "ingressclasses"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses/status"]
    verbs: ["*"]

  # required for CA's namespace controller
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["create", "get", "list", "watch", "update"]

  # Istiod and bootstrap.
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "certificatesigningrequests"
      - "certificatesigningrequests/approval"
      - "certificatesigningrequests/status"
    verbs: ["update", "create", "get", "delete", "watch"]
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "signers"
    resourceNames:
    - "kubernetes.io/legacy-unknown"
    verbs: ["approve"]

  # Used by Istiod to verify the JWT tokens
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]

  # Used by Istiod to verify gateway SDS
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]

  # Use for Kubernetes Service APIs
  - apiGroups: ["networking.x-k8s.io", "gateway.networking.k8s.io"]
    resources: ["*"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["networking.x-k8s.io", "gateway.networking.k8s.io"]
    resources: ["*"] # TODO: should be on just */status but wildcard is not supported
    verbs: ["update"]
  - apiGroups: ["gateway.networking.k8s.io"]
    resources: ["gatewayclasses"]
    verbs: ["create", "update", "patch", "delete"]

  # Needed for multicluster secret reading, possibly ingress certs in the future
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "watch", "list"]

  # Used for MCS serviceexport management
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceexports"]
    verbs: ["get", "watch", "list", "create", "delete"]

  # Used for MCS serviceimport management
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceimports"]
    verbs: ["get", "watch", "list"]
---
# Source: base/templates/clusterrole.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istio-reader-istio-system
  labels:
    app: istio-reader
    release: istio-base
rules:
  - apiGroups:
      - "config.istio.io"
      - "security.istio.io"
      - "networking.istio.io"
      - "authentication.istio.io"
      - "rbac.istio.io"
    resources: ["*"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["endpoints", "pods", "services", "nodes", "replicationcontrollers", "namespaces", "secrets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.istio.io"]
    verbs: [ "get", "watch", "list" ]
    resources: [ "workloadentries" ]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceexports"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["multicluster.x-k8s.io"]
    resources: ["serviceimports"]
    verbs: ["get", "watch", "list"]
---
# Source: base/templates/clusterrolebinding.yaml
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
# DO NOT EDIT!
# THIS IS A LEGACY CHART HERE FOR BACKCOMPAT
# UPDATED CHART AT manifests/charts/istio-control/istio-discovery
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istio-reader-istio-system
  labels:
    app: istio-reader
    release: istio-base
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istio-reader-istio-system
subjects:
  - kind: ServiceAccount
    name: istio-reader-service-account
    namespace: istio-system
---
# Source: base/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istiod-istio-system
  labels:
    app: istiod
    release: istio-base
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istiod-istio-system
subjects:
  - kind: ServiceAccount
    name: istiod-service-account
    namespace: istio-system
---
# Source: base/templates/role.yaml
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
# DO NOT EDIT!
# THIS IS A LEGACY CHART HERE FOR BACKCOMPAT
# UPDATED CHART AT manifests/charts/istio-control/istio-discovery
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod-istio-system
  namespace: istio-system
  labels:
    app: istiod
    release: istio-base
rules:
# permissions to verify the webhook is ready and rejecting
# invalid config. We use --server-dry-run so no config is persisted.
- apiGroups: ["networking.istio.io"]
  verbs: ["create"]
  resources: ["gateways"]

# For storing CA secret
- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]
---
# Source: base/templates/rolebinding.yaml
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
# DO NOT EDIT!
# THIS IS A LEGACY CHART HERE FOR BACKCOMPAT
# UPDATED CHART AT manifests/charts/istio-control/istio-discovery
# -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istiod-istio-system
  namespace: istio-system
  labels:
    app: istiod
    release: istio-base
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istiod-istio-system
subjects:
  - kind: ServiceAccount
    name: istiod-service-account
    namespace: istio-system
---
# Source: base/templates/default.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: istiod-default-validator
  labels:
    app: istiod
    release: istio-base
    istio: istiod
    istio.io/rev: default
webhooks:
  - name: validation.istio.io
    clientConfig:
      service:
        name: istiod
        namespace: istio-system
        path: "/validate"
    rules:
      - operations:
          - CREATE
          - UPDATE
        apiGroups:
          - security.istio.io
          - networking.istio.io
        apiVersions:
          - "*"
        resources:
          - "*"
    # Fail open until the validation webhook is ready. The webhook controller
    # will update this to `Fail` and patch in the `caBundle` when the webhook
    # endpoint is ready.
    failurePolicy: Ignore
    sideEffects: None
    admissionReviewVersions: ["v1beta1", "v1"]
