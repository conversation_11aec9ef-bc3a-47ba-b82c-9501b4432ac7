#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ClearML 集成示例

展示如何在不同环境下配置和使用 ClearML 集成功能
"""

import os
import json
from typing import Dict, Any


def setup_clearml_sdk_environment():
    """
    设置 ClearML SDK 环境
    
    这个函数展示了如何配置 ClearML SDK 所需的环境变量
    """
    print("=" * 60)
    print("ClearML SDK 环境配置")
    print("=" * 60)
    
    # ClearML 配置
    clearml_config = {
        "api": {
            "web_server": "http://clearml-web:8080",
            "api_server": "http://clearml-api:8008", 
            "files_server": "http://clearml-files:8081",
            "credentials": {
                "access_key": "your_access_key",
                "secret_key": "your_secret_key"
            }
        }
    }
    
    # 方式1: 通过环境变量配置
    env_vars = {
        "CLEARML_WEB_HOST": "http://clearml-web:8080",
        "CLEARML_API_HOST": "http://clearml-api:8008",
        "CLEARML_FILES_HOST": "http://clearml-files:8081",
        "CLEARML_API_ACCESS_KEY": "your_access_key",
        "CLEARML_API_SECRET_KEY": "your_secret_key"
    }
    
    print("环境变量配置:")
    for key, value in env_vars.items():
        print(f"export {key}={value}")
    
    print("\n" + "=" * 60)
    
    # 方式2: 通过配置文件
    config_file_path = "~/clearml.conf"
    print(f"配置文件路径: {config_file_path}")
    print("配置文件内容:")
    print(json.dumps(clearml_config, indent=2))
    
    print("\n" + "=" * 60)


def example_sdk_vs_api():
    """
    对比 SDK 和 API 两种方式的差异
    """
    print("=" * 60)
    print("SDK vs API 方式对比")
    print("=" * 60)
    
    print("1. ClearML SDK 方式:")
    print("   优势:")
    print("   - 类型安全，IDE 支持自动补全")
    print("   - 自动处理认证和会话管理")
    print("   - 丰富的 API 功能")
    print("   - 更好的错误处理")
    print("   - 支持复杂的查询和过滤")
    print()
    print("   示例代码:")
    print("   ```python")
    print("   from clearml import Task")
    print("   task = Task.get_task(task_id='abc123')")
    print("   metrics = task.get_last_scalar_metrics()")
    print("   artifacts = task.artifacts")
    print("   ```")
    print()
    
    print("2. HTTP API 方式:")
    print("   优势:")
    print("   - 无需额外依赖")
    print("   - 轻量级实现")
    print("   - 跨语言兼容")
    print("   - 直接的 HTTP 调用")
    print()
    print("   示例代码:")
    print("   ```python")
    print("   import requests")
    print("   url = f'{api_host}/tasks.get_by_id'")
    print("   resp = requests.post(url, json={'task': 'abc123'})")
    print("   data = resp.json()")
    print("   ```")
    print()


def example_algorithm_saver_with_clearml():
    """
    展示算法保存器与 ClearML 的集成使用
    """
    print("=" * 60)
    print("算法保存器 ClearML 集成使用示例")
    print("=" * 60)
    
    # 示例1: 使用 SDK 方式（推荐）
    print("示例1: 使用 ClearML SDK（推荐）")
    print("-" * 40)
    
    sdk_command = """
python algorithm_saver.py \\
  --algo_id "resnet50_classifier" \\
  --algo_version "v1.0" \\
  --model_path "/mnt/models/resnet50.pt" \\
  --clearml_project_name "image_classification" \\
  --use_best_task \\
  --clearml_metric_name "accuracy" \\
  --framework "pytorch"
"""
    
    print("命令:")
    print(sdk_command)
    
    print("环境要求:")
    print("- 容器中已安装 clearml 包")
    print("- 配置了 ClearML 凭据")
    print("- 网络可访问 ClearML 服务")
    print()
    
    # 示例2: 使用 API 方式（降级）
    print("示例2: 使用 HTTP API（降级方案）")
    print("-" * 40)
    
    api_command = """
# 设置环境变量
export CLEARML_API_HOST=http://clearml-api:8008

python algorithm_saver.py \\
  --algo_id "bert_sentiment" \\
  --algo_version "v2.0" \\
  --model_path "/mnt/models/bert_model" \\
  --clearml_task_id "specific_task_id_123" \\
  --framework "tensorflow"
"""
    
    print("命令:")
    print(api_command)
    
    print("环境要求:")
    print("- 设置 CLEARML_API_HOST 环境变量")
    print("- 网络可访问 ClearML API 服务")
    print("- 无需安装 clearml 包")
    print()


def example_error_handling():
    """
    展示错误处理和降级机制
    """
    print("=" * 60)
    print("错误处理和降级机制")
    print("=" * 60)
    
    scenarios = [
        {
            "scenario": "ClearML SDK 不可用",
            "description": "容器中未安装 clearml 包",
            "behavior": "自动降级到 HTTP API 方式",
            "log": "WARNING: ClearML SDK 不可用，使用 HTTP API 方式"
        },
        {
            "scenario": "SDK 调用失败",
            "description": "认证失败或网络问题",
            "behavior": "捕获异常，降级到 HTTP API",
            "log": "ERROR: 通过SDK获取任务信息失败，降级到API方式"
        },
        {
            "scenario": "API 调用失败",
            "description": "API 服务不可用",
            "behavior": "返回 None，记录错误",
            "log": "ERROR: 通过API获取任务信息失败"
        },
        {
            "scenario": "任务不存在",
            "description": "指定的任务ID不存在",
            "behavior": "返回 None，记录警告",
            "log": "WARNING: 未找到任务: task_id"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['scenario']}")
        print(f"   描述: {scenario['description']}")
        print(f"   行为: {scenario['behavior']}")
        print(f"   日志: {scenario['log']}")
        print()


def example_best_practices():
    """
    最佳实践建议
    """
    print("=" * 60)
    print("ClearML 集成最佳实践")
    print("=" * 60)
    
    practices = [
        {
            "title": "环境配置",
            "tips": [
                "优先使用 ClearML SDK，提供更好的功能和错误处理",
                "在 Dockerfile 中安装 clearml 包",
                "通过环境变量或配置文件设置 ClearML 凭据",
                "确保网络可访问 ClearML 服务"
            ]
        },
        {
            "title": "任务管理",
            "tips": [
                "使用有意义的项目名称和任务名称",
                "记录关键的评估指标",
                "保存模型文件到 artifacts",
                "使用标签和描述便于后续查找"
            ]
        },
        {
            "title": "错误处理",
            "tips": [
                "监控算子的执行日志",
                "设置合理的超时时间",
                "提供降级方案",
                "记录详细的错误信息"
            ]
        },
        {
            "title": "性能优化",
            "tips": [
                "缓存频繁访问的任务信息",
                "使用批量 API 减少网络调用",
                "合理设置并发数量",
                "定期清理过期的任务和模型"
            ]
        }
    ]
    
    for practice in practices:
        print(f"## {practice['title']}")
        for tip in practice['tips']:
            print(f"- {tip}")
        print()


def main():
    """主函数"""
    print("ClearML 集成完整指南")
    print("=" * 60)
    
    setup_clearml_sdk_environment()
    example_sdk_vs_api()
    example_algorithm_saver_with_clearml()
    example_error_handling()
    example_best_practices()
    
    print("=" * 60)
    print("集成指南完成!")


if __name__ == "__main__":
    main()
