"""empty message

Revision ID: aa2d53864721
Revises: 917384921e87
Create Date: 2022-11-23 07:16:28.167726

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'aa2d53864721'
down_revision = '917384921e87'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('favorite',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('model_name', sa.String(length=100), nullable=False),
    sa.Column('row_id', sa.String(length=500), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('model_name', 'row_id', 'user_id')
    )
    op.drop_table('user_attribute')
    op.add_column('etl_pipeline', sa.Column('workflow', sa.String(length=200), nullable=True))
    op.alter_column('etl_task', 'etl_pipeline_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True)
    op.drop_constraint('etl_task_ibfk_3', 'etl_task', type_='foreignkey')
    op.add_column('metadata_metric', sa.Column('remark', sa.Text(length=65536), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('metadata_metric', 'remark')
    op.create_foreign_key('etl_task_ibfk_3', 'etl_task', 'etl_pipeline', ['etl_pipeline_id'], ['id'])
    op.alter_column('etl_task', 'etl_pipeline_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False)
    op.drop_column('etl_pipeline', 'workflow')
    op.create_table('user_attribute',
    sa.Column('created_on', mysql.DATETIME(), nullable=True),
    sa.Column('changed_on', mysql.DATETIME(), nullable=True),
    sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
    sa.Column('user_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('created_by_fk', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('changed_by_fk', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], name='user_attribute_ibfk_1'),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], name='user_attribute_ibfk_2'),
    sa.ForeignKeyConstraint(['user_id'], ['ab_user.id'], name='user_attribute_ibfk_3'),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8',
    mysql_engine='InnoDB'
    )
    op.drop_table('favorite')
    # ### end Alembic commands ###
