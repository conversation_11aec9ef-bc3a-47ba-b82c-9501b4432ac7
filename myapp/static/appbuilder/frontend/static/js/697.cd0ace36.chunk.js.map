{"version": 3, "file": "static/js/697.cd0ace36.chunk.js", "mappings": "4NAkBaA,EAAwB,SAACC,GAIlC,OAAOC,EAAAA,EAAAA,IAAUD,EACrB,EAEaE,EAAoB,SAACF,GAC9B,OAAOC,EAAAA,EAAAA,IAAUD,EACrB,EAEaG,EAAiB,SAACH,GAC3B,OAAOC,EAAAA,EAAAA,IAAUD,EACrB,C,qIC2DMI,EAAyC,CAC3CC,OAAQ,KAIG,SAASC,EAAWC,GAC/B,OAA0CC,EAAAA,EAAAA,YAA2B,eAA9DC,EAAa,KAAEC,EAAgB,KAChCC,EAAKC,KAAKC,SAASC,SAAS,IAAIC,UAAU,GAChD,GAAoBC,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAEFC,GAFS,EAAJC,KAEI,CAAC,GAchB,OAZAC,EAAAA,EAAAA,YAAU,WACN,IAAMC,EAAWC,SAASC,eAAeZ,GACzC,GAAIU,EAAU,CACV,IAAMG,EAAQC,EAAAA,GAAaJ,GAC3BG,EAAME,WAAU,kBAAKR,GAAWX,EAAMW,SAEjCT,GACDC,EAAiBc,EAEzB,CACJ,GAAG,CAACjB,EAAMW,OAAQX,EAAMoB,QAGpB,SAAC,IAAI,CAACC,SAAUrB,EAAMsB,QAAQ,UAC1B,iBAAKC,UAAU,kBAAiB,WAC5B,gBAAKnB,GAAIA,EAAIoB,OAAK,kBAAO3B,GAAsBG,EAAMwB,SAEjDxB,EAAMyB,UAAW,gBAAKF,UAAU,eAAc,UAC1C,yBAAMb,EAAE,gCACH,SAK7B,C,0UCpHMgB,EAAkBC,EAAQ,OA6B1BC,EAAiB,SAAH,GAAgD,IAA1CC,EAAQ,EAARA,SAAUC,EAAK,EAALA,MAAUC,GAAS,YACtD,OAAKD,GAKJ,SAAC,EAAAE,UAAS,CACTF,MAAOA,EACPhC,OAAQ,EACRmC,QACC,iBACCV,UAAU,yBACVW,QAAS,SAACC,GACTA,EAAEC,iBACH,IAGFP,SAAUA,EACVQ,cAAe,CAAEC,sBAAsB,GAAQ,UAE/C,iCAAQP,GAAS,IAAEP,OAAK,kBAAgB,OAATO,QAAS,IAATA,OAAS,EAATA,EAAWP,OAAK,IAAEe,WAAY,eAlBvD,yBAAQR,GAqBjB,EAiRA,EA/QiB,SAAC/B,GACjB,OAAkDC,EAAAA,EAAAA,WAAS,GAAM,eAA1DuC,EAAiB,KAAEC,EAAoB,KAC9C,GAAoCxC,EAAAA,EAAAA,UAAyC,CAC5EyC,OAAQ,GACRtB,KAAM,KACL,eAHKuB,EAAU,KAAEC,EAAa,KAIhC,GAAsC3C,EAAAA,EAAAA,UAAgB,IAAG,eAAlD4C,EAAW,KAAEC,EAAc,KAGlC,GAAwB7C,EAAAA,EAAAA,UAASD,EAAM+C,SAAQ,eAAxCC,EAAI,KAAEC,EAAO,KACdC,EAAe,SAACC,GACrB,OAAO,SAACC,EAAO,GAAoB,IAAjBC,EAAI,EAAJA,KACjB,KAAIA,EAAKvB,MAAQ,KAAjB,CACA,IAAMwB,GAAI,OAAON,GACjBM,EAAKH,IAAM,kBAAQG,EAAKH,IAAM,IAAErB,MAAOuB,EAAKvB,QAC5C,IAAMyB,EAAaD,EAAKE,QAAO,SAACC,EAAUC,GAAS,OAAKD,EAAMC,EAAK5B,OAAS,GAAG,GAAE,GAAK,IACtF6B,aAAaC,QAAQ5D,EAAM6D,UAAY,GAAIC,KAAKC,UAAUT,IAE1DU,GAAsB,kBAAKC,GAAkB,IAAEC,EAAGX,KAClDN,EAAQK,EAPoB,CAQ7B,CACD,EACMa,EAAgBnB,EAAKoB,KAAI,SAACC,EAAUlB,GACzC,OAAO,kBACHkB,GAAG,IACNvC,MAAOuC,EAAIvC,OAAS,IACpBwC,aAAc,SAACC,GACd,MAAO,CACNzC,MAAOyC,EAAOzC,MACdD,SAAUqB,EAAaC,GAEzB,GAEF,IACA,GAAoDlD,EAAAA,EAAAA,UAASD,EAAMwE,QAAO,eAAnEP,EAAkB,KAAED,EAAqB,KAChD,GAAoBvD,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAAO,EAAJE,MAEXC,EAAAA,EAAAA,YAAU,WACToC,EAAQjD,EAAM+C,QACf,GAAG,CAAC/C,EAAM+C,WAEVlC,EAAAA,EAAAA,YAAU,WACTmD,EAAsBhE,EAAMwE,OAC7B,GAAG,CAACxE,EAAMwE,UAEV3D,EAAAA,EAAAA,YAAU,WACT,GAAIb,EAAMyE,WAAY,CACrB,IAAM1B,EAAU/C,EAAM+C,QAAQ2B,QAAO,SAACC,GAAS,OAAM9B,EAAY+B,QAAQD,EAAKE,UAAU,IACxFC,EAAoB/B,EAAS/C,EAAMyE,WACpC,CACD,GAAG,CAACzE,EAAMyE,WAAYzE,EAAM+C,UAE5B,IAOM+B,EAAsB,WAAoC,IAAnCC,EAAW,uDAAG,GAAI3D,EAAW,uCACnD2B,EAAUgC,EAAYX,KAAI,SAACO,GAAS,OAAKA,EAAKE,SAAS,IAAEH,QAAO,SAACC,GAAY,MAAc,WAATA,CAAiB,IACnGK,EAAcD,EAAYX,KAAI,SAACO,GAAS,OAAKA,EAAKM,KAAK,IAAEP,QAAO,SAACC,GAAY,OAAKA,IAASjE,EAAE,eAAK,IAClGwE,EAAe,GAErB9D,EAAK+D,SAAQ,SAACC,GACb,IAAMC,EAAW,CAAC,EAClBtC,EAAQqB,KAAI,SAACkB,GACZ,IAAMC,EAAMH,EAAQE,GACpBD,EAAIC,GAAWC,GAAO,EACvB,IACAL,EAAQM,KAAKH,EACd,IAEAzC,EAAc,CACbF,OAAQsC,EACR5D,KAAM8D,GAER,EAgBMO,EAAmB,WACxB,IAAM/C,EAASC,EAAWD,OACpBtB,EAAOuB,EAAWvB,KACpBsE,EAAM,GA2BV,OA1BIhD,EAAOiD,QAAUvE,EAAKuE,QACzBD,EACC,IACAhD,EAAOkD,KAAK,KADZ,MAKDxE,EAAK+D,SAAQ,SAACE,GACb,IAAMQ,EAASC,OAAOC,OAAOV,GAAKjB,KAAI,SAACO,GACtC,MAAa,KAATA,EACI,IAEDA,CACR,IACAe,EACCA,EACA,IACAG,EAAOD,KAAK,KAFZF,KAMF,KAEAA,EAAM,GAGAA,CACR,EAEMM,EAAmB,WACxB,IAAMtD,EAASC,EAAWD,OACpBtB,EAAOuB,EAAWvB,KACpBsE,EAAM,GAsBV,OArBIhD,EAAOiD,QAAUvE,EAAKuE,QACzBD,EACChD,EAAOkD,KAAK,MAAI,KAGjBxE,EAAK+D,SAAQ,SAACE,GACb,IAAMQ,EAASC,OAAOC,OAAOV,GAAKjB,KAAI,SAACO,GACtC,MAAa,KAATA,EACI,IAEDA,CACR,IACAe,EACCA,EACAG,EAAOD,KAAK,MAAI,IAGlB,KAEAF,EAAM,GAEAA,CACR,EAEA,OACC,UAAC,IAAK,CAACnE,UAAU,WAAW0E,UAAU,WAAW5C,KAAK,SAAQ,WAC7D,UAAC,IAAK,CACLvB,MAAO,IACPoE,cAAc,EACdC,UAAU,EACVC,UAAW,CAAEC,UAAW,IAAKC,SAAU,QACvCC,QAAS/D,EACTyC,MAAOvE,EAAE,4BACT8F,SAAU,WACT/D,GAAqB,EACtB,EACAgE,OAAQ,KAAK,WAEb,iBAAKjF,MAAO,CAAEkF,SAAU,YAAa,WACpC,iBAAKnF,UAAU,OAAM,WAAC,kBAAMA,UAAU,MAAK,UAAEb,EAAE,oDAAY,aAAQ,SAAC,UAAc,CACjFiG,QAAS3G,EAAM+C,QACbqB,KAAI,SAACO,GAAS,MAAM,CAAEiC,MAAOjC,EAAKM,MAAO4B,MAAOlC,EAAKE,UAAW,IAChEH,QAAO,SAACC,GAAS,MAAoB,WAAfA,EAAKkC,KAAkB,IAC/CC,aAAc,GACdD,MAAOhE,EACPkE,SAAU,SAAChB,GACVjD,EAAeiD,GACf,IAAMhD,EAAU/C,EAAM+C,QAAQ2B,QAAO,SAACC,GAAS,OAAMoB,EAAOnB,QAAQD,EAAKE,UAAU,IACnFC,EAAoB/B,EAAS/C,EAAMyE,WACpC,QAED,iBAAKjD,MAAO,CAAEkF,SAAU,WAAYM,MAAO,EAAGC,OAAQ,GAAI,WACzD,SAAC,IAAM,CACN5D,KAAK,QACL6D,KAAK,OACLhF,QAAS,WACRY,EACC9C,EAAM+C,QACJqB,KAAI,SAACO,GAAS,OAAKA,EAAKE,SAAS,IACjCH,QAAO,SAACC,GAAS,MAAc,WAATA,CAAiB,KAE1CG,EAAoB9E,EAAM+C,QAAS/C,EAAMyE,WAC1C,EAAE,SAED/D,EAAE,mBAEJ,SAAC,IAAM,CACN2C,KAAK,QACL6D,KAAK,OACLhF,QAAS,WACRY,EAAe,IACfgC,EAAoB,GAAI9E,EAAMyE,WAC/B,EAAE,SAED/D,EAAE,yBAKN,UAAC,IAAI,YACJ,SAAC,YAAY,CAACyG,IAAI,mBAAQ,UACzB,SAACzF,EAAe,CAAC0F,KAAM3B,IAAoB4B,OAAQ,kBAAMC,EAAAA,GAAAA,QAAgB5G,EAAE,8CAAW,EAAC,UACtF,gBAAKc,MAAO,CAAE+F,OAAQ,UAAWC,UAAW,KAAM,UACjD,0BAAO/B,WAHqB,SAO/B,SAAC,YAAY,CAAC0B,IAAI,mBAAQ,UACzB,SAACzF,EAAe,CAAC0F,KAAMpB,IAAoBqB,OAAQ,kBAAMC,EAAAA,GAAAA,QAAgB5G,EAAE,8CAAW,EAAC,UACtF,gBAAKc,MAAO,CAAE+F,OAAQ,UAAWC,UAAW,KAAM,UACjD,0BAAOxB,WAHqB,cAmBhChG,EAAMyH,WAAazH,EAAM0H,aAAe1H,EAAM2H,kBAAmB,UAAC,IAAG,CAACC,QAAQ,gBAAgBC,MAAM,SAAQ,WAC3G,SAAC,IAAG,WACH,SAAC,IAAK,CAACA,MAAM,SAAQ,SAAE7H,EAAMyH,eAE9B,SAAC,IAAG,WACH,UAAC,IAAK,CAACI,MAAM,SAAQ,UACnB7H,EAAM0H,WACN1H,EAAM2H,iBAAmB,MACzB,SAAC,IAAM,CAACnG,MAAO,CAAEsG,WAAY,GAAK5F,QAAS,kBAAMO,GAAqB,EAAK,EAAC,SAC1E/B,EAAE,sCAKC,MAEV,SAAC,KAAc,CAACqH,YAxMW,WAAH,OACzB,UAAC,IAAG,CAACH,QAAQ,SAASC,MAAM,SAASrG,MAAO,CAAE1B,OAAQ,IAAKkI,cAAe,UAAW,WACpF,gBAAKC,IAAKC,EAAU1G,MAAO,CAAEM,MAAO,KAAOqG,IAAI,MAC/C,yBAAMzH,EAAE,gCACH,EAoM6C,UACjD,SAAC,IAAK,CACL2C,KAAMrD,EAAMqD,MAAQ,SACpBwC,OAAQ7F,EAAM6F,OAAS7F,EAAM6F,OAAS,KACtCpB,WAAYzE,EAAMyE,WAElB2D,WAAY,CAAE1F,OAAQ,CAAE2F,KAAMzG,IAC9BmB,QAASoB,EACTmE,YAAiC,IAArBtI,EAAMsI,aAAoB,UAAQtI,EAAMsI,YACpD9D,OAAQP,EACR3C,QAAStB,EAAMsB,QACfyF,SAAU/G,EAAM+G,SAChBwB,aAAcvI,EAAMuI,mBAKzB,C,yIC1Ue,SAASC,IACpB,OACI,iBAAKjH,UAAU,UAAUC,MAAO,CAAEiH,MAAO,KAAM,WAC3C,gBAAKlH,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,WAEnB,gBAAKA,UAAU,MAAK,UAChB,gBAAKA,UAAU,YAI/B,C,gHC1LMmH,E,SAAaC,EAyBEC,EAAM,WAiB1B,cAAqE,IAAD,OAAtDC,EAAW,EAAXA,YAAaC,EAAU,EAAVA,WAAW,EAAD,EAAEC,OAAAA,OAAM,MAAG,EAAC,uBAhB1CC,eAAS,OACTC,eAAS,OACTC,cAAQ,OACRC,WAAK,OACLC,WAAK,OACLC,WAAK,OACLC,aAAO,OACPC,gBAAU,OACVC,gBAAU,OACVC,iBAAW,OACXC,SAAG,OACHC,SAAW,GAAG,KACdC,UAAI,OACJC,iBAAW,OACXd,YAAM,OAiINe,UAAY,SAAI1I,GAUtB,IATA,IAQI2I,EARExE,EAAW,GACXyE,EAA0B5I,EAAKgD,KAAI,SAACO,GAIzC,OAHIA,EAAKsF,iBACDtF,EAAKsF,SAENtF,CACR,IACMuF,EAAU,EAAKC,SAASH,EAAyB,SAE9CI,EAAI,EAAGA,EAAIJ,EAAwBrE,OAAQyE,IAAK,CACxD,IAAMzF,EAAOqF,EAAwBI,GACrC,QAAsBC,IAAlB1F,EAAK2F,SAAwB,CAChC,IAAMC,EAASL,EAAQM,IAAI7F,EAAK2F,UAC5BC,EAAON,SACVM,EAAON,SAASzE,KAAKb,GAErB4F,EAAON,SAAW,CAACtF,EAErB,MACCoF,EAAOG,EAAQM,IAAI7F,EAAK8F,MAE1B,CAEA,OADAlF,EAAIC,KAAKuE,GACFxE,CACR,EAAE,KAOKmF,UAAY,SAAItJ,GAGtB,IAHsE,IAAhCuJ,EAAG,uDAAG,WACtCC,GAAK,OAAOxJ,GACZmE,EAAW,GACVqF,EAAMjF,QAAQ,CACpB,IAAMhB,EAAYiG,EAAMC,QACxBtF,EAAIC,KAAKb,GACLA,GAAQA,EAAKgG,IAChBC,EAAMpF,KAAI,MAAVoF,GAAK,OAASjG,EAAKgG,IAErB,CACA,OAAOpF,CACR,EAAE,KAQK4E,SAAW,SAAIW,EAAWH,GAEhC,IAFoF,IAAvCI,EAAS,wDAChD3G,EAAsB,IAAI4G,IACvBZ,EAAI,EAAGA,EAAIU,EAAKnF,OAAQyE,IAAK,CACrC,IAAMzF,EAAYmG,EAAKV,GACnBzF,GAAQA,EAAKgG,KAASI,IAAc3G,EAAIoG,IAAI7F,EAAKgG,MACpDvG,EAAI6G,IAAItG,EAAKgG,GAAMhG,EAErB,CACA,OAAOP,CACR,EAAE,KAQK8G,YAAc,SAAI9J,GAAoE,IAAzDuJ,EAAG,uDAAG,WAAYQ,EAAQ,uDAAG,KAAM/K,EAAW,uCACjF,QAAWiK,IAAPjK,EAAkB,OAAOgB,EAE7B,IAoBMgK,EApBM,SAANC,EAAUjK,GAEf,IADA,IAAMmE,EAAW,GACR6E,EAAI,EAAGA,EAAIhJ,EAAKuE,OAAQyE,IAAK,CACrC,IAAMzF,EAAYvD,EAAKgJ,GACjBkB,GAAO,UAAQ3G,GACjBA,EAAKgG,IAAQhG,EAAKgG,GAAKhF,OACtBhB,EAAKwG,KAAc/K,GACtBkL,EAAQX,GAAO,GACfpF,EAAIC,KAAK8F,KAETA,EAAQX,GAAOU,EAAI1G,EAAKgG,IACxBpF,EAAIC,KAAK8F,IAGV/F,EAAIC,KAAK8F,EAEX,CACA,OAAO/F,CACR,CAEqB8F,CAAIjK,GACzB,OAAOgK,CACR,EAAC,KAEMG,gBAAkB,WAAyC,IAC3DC,GADiC,uDAAG,mBACjBC,MAAM,KAAKrH,KAAI,SAAAsH,GACvC,MAAeA,EAAMD,MAAM,KAAI,eAC/B,MAAO,CAAEvH,GADD,KACQyH,GADL,KAEZ,IACMC,EAAqBJ,EAAUhI,QAAO,SAACC,EAAKC,EAAMmI,GACvD,IAAIC,GAAQ,UAAQrI,GAGpB,OAFAqI,EAAI,IAAD,OAAKD,IAAkBnI,EAAKQ,EAC/B4H,EAAI,IAAD,OAAKD,IAAkBnI,EAAKiI,EACxBG,CACR,GAAG,CAAC,GACEhK,EAAQ8J,EAAcG,GAAKH,EAAcI,GACzClM,EAAS8L,EAAcK,GAAKL,EAAcM,GAQhD,OAPgB,gBACfV,UAAAA,GACGI,GAAa,IAChB9J,MAAOzB,KAAK8L,IAAIrK,GAChBhC,OAAQO,KAAK8L,IAAIrM,GACjBsM,OAAQ,CAACtK,EAAQ,EAAGhC,EAAS,IAG/B,EAlPC,IAAMuM,EAAYC,KAElBA,KAAKvD,OAASA,EACduD,KAAKtD,UAAYjI,SAASC,eAAe6H,GACzCyD,KAAKpD,SAAWqD,EAAAA,GAAU,IAAD,OAAKzD,IAC9BwD,KAAKrD,UAAYqD,KAAKpD,SAASsD,OAAO,KAAKC,KAAK,KAAM,aACtDH,KAAKrD,UAAUwD,KAAK,QAAS,sBAE7B,IAAMC,EAAY,CAAEC,IAAK5D,EAAQ/B,MAAO+B,EAAQ9B,OAAQ8B,EAAQN,KAAMM,GAChES,EAAa8C,KAAKtD,UAAU4D,YAAcF,EAAUC,IAAMD,EAAUzF,OACpEwC,EAAc6C,KAAKtD,UAAU6D,aAAeH,EAAUjE,KAAOiE,EAAU1F,MAE7EsF,KAAKpD,SACHuD,KAAK,QAASjD,GACdiD,KAAK,SAAUhD,GACfgD,KAAK,UAAU,OAAD,OAASjD,EAAU,YAAIC,IACrCgD,KAAK,YAAY,aAAD,OAAeC,EAAUjE,KAAI,aAAKiE,EAAUC,IAAG,MAKjEL,KAAK/C,WAAa+C,KAAKpD,SAASsD,OAAO,KAAKC,KAAK,KAAM,cACvDH,KAAKhD,QAAUgD,KAAKrD,UAAUuD,OAAO,KAAKC,KAAK,KAAM,WACrDH,KAAKjD,MAAQiD,KAAKrD,UAAUuD,OAAO,KAAKC,KAAK,KAAM,SACnDH,KAAKlD,MAAQkD,KAAKrD,UAAUuD,OAAO,KAAKC,KAAK,KAAM,SACnDH,KAAKnD,MAAQmD,KAAKrD,UAChBuD,OAAO,KACPC,KAAK,KAAM,SACXA,KAAK,SAAU,WACfA,KAAK,iBAAkB,OACvBA,KAAK,YAAY,eAAD,OAA+B,GAAdhD,EAAiB,mBAEvC6C,KAAKrD,UAAUuD,OAAO,QAAQC,KAAK,KAAM,SACjDD,OAAO,UACVC,KAAK,KAAM,cACXA,KAAK,UAAW,iBAChBA,KAAK,OAAQ,IACbA,KAAK,OAAQ,GACbA,KAAK,cAAe,GACpBA,KAAK,eAAgB,GACrBA,KAAK,SAAU,QACfD,OAAO,QACPC,KAAK,IAAK,6BACVA,KAAK,OAAQ,WACbA,KAAK,eAAgB,GACrBA,KAAK,SAAU,WAEjB,IAAM/C,EAAMhB,IACZgB,EAAI+C,KAAK,QAAS,UAAUK,MAAK,SAAUC,GAC1C,OAAOA,CACR,IACAT,KAAK5C,IAAMA,EACX4C,KAAKpD,SAAS8D,KAAKtD,GAEnB,IAAME,EAAO2C,EAAAA,MAEXU,YAAY,CAAC,GAAK,IAGlBC,GAAG,QAAQ,SAAC/K,GAIZ,IAAMgL,EAAYhL,EAAEgL,UACpBd,EAAUpD,UAAUwD,KAAK,QAAS,qBAAqBA,KAAK,YAAaU,GACzEd,EAAU3C,IAAI0D,MACf,IAAGF,GAAG,OAAO,WACZb,EAAUpD,UAAUwD,KAAK,QAAS,sBACnC,IAEDH,KAAK1C,KAAOA,EACZ0C,KAAKpD,SACH8D,KAAKpD,GACLoD,KAAKpD,EAAKuD,UAAWZ,EAAAA,IAAAA,UAA0B,EAAG,GAAGc,MAAM,IAC3DH,GAAG,gBAAiB,MAEtBZ,KAAK7C,YAAcA,EACnB6C,KAAK9C,WAAaA,CACnB,CA4CA,OA1CA,8BAKA,WACC8C,KAAKpD,SAASuD,KAAK,QAAS,GAAGA,KAAK,SAAU,GAAGA,KAAK,UAAU,OAAD,OAAS,EAAC,IAAI,IAE7E,IAAMC,EAAmBJ,KAAKvD,OAAxB2D,EAAuCJ,KAAKvD,OAA5C2D,EAA4DJ,KAAKvD,OAAjE2D,EAA+EJ,KAAKvD,OACpFS,EAAa8C,KAAKtD,UAAU4D,YAAcF,EAAgBA,EAC1DjD,EAAc6C,KAAKtD,UAAU6D,aAAeH,EAAiBA,EAEnEY,QAAQC,IAAIjB,KAAKtD,UAAU4D,YAAaN,KAAKtD,UAAU6D,cAEvDP,KAAKpD,SACHuD,KAAK,QAASjD,GACdiD,KAAK,SAAUhD,GACfgD,KAAK,UAAU,OAAD,OAASjD,EAAU,YAAIC,IAEvC6C,KAAK7C,YAAcA,EACnB6C,KAAK9C,WAAaA,CACnB,GAEA,yBAQA,SAAmBgE,GAAoE,IAAD,EAAlD7D,EAAQ,uDAAG,GACxC8D,EAAUnB,KAAK/C,WAAWiD,OAAO,QAAQpF,KAAKoG,GAASf,KAAK,YAAY,GAAD,OAAK9C,EAAQ,OACpF+D,EAAwB,QAAjB,EAAGD,EAAQE,cAAM,aAAd,EAAgBC,UAC1BC,EAAO,CACZ/L,OAAc,OAAP4L,QAAO,IAAPA,OAAO,EAAPA,EAAS5L,QAAS,EACzBhC,QAAe,OAAP4N,QAAO,IAAPA,OAAO,EAAPA,EAAS5N,SAAU,GAG5B,OADA2N,EAAQK,SACDD,CACR,KAEA,EA5I0B,GCqBrBE,G,SAxCeC,EAAAA,KAwCa,CACjC,CAAEC,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAE9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,WAC9H,CAAEL,WAAY,YAAaC,MAAO,UAAWC,OAAQ,UAAWC,YAAa,UAAWC,SAAU,YAAaC,cAAe,aAGzHC,EAjBe,CAAC,OAAQ,WAAY,QAAS,WAiBgB/K,QAAO,SAACC,EAAKC,EAAMP,GAAK,yBAAWM,GAAG,cAAGC,EAAOqK,EAAW5K,EAAQ4K,EAAWpI,SAAO,GAAK,CAAC,GAMzI6I,EAAe,0CAenC,cAA2F,IAAD,EAA5E3F,EAAW,EAAXA,YAAaC,EAAU,EAAVA,WAAW,EAAD,EAAEC,OAAAA,OAAM,MAAG,EAAC,EAAE0F,EAAY,EAAZA,aAEhB,OAF4B,gBAC9D,cAAM,CAAE5F,YAAAA,EAAaC,WAAAA,EAAYC,OAAAA,KAd3BmB,QAAU,IAAIc,IAA8B,EAC5C0D,cAAgB,IAAI1D,IAA8B,EAClD2D,eAAiB,IAAI3D,IAA2B,EAChD4D,gBAAkB,IAAI5D,IAA8B,EACpD6D,qBAAuB,IAAI7D,IAA8B,EACzD8D,UAA8B,GAAG,EACjCC,kBAAY,IACZC,cAAQ,IACRC,qBAAe,IACfC,kBAAY,IACZC,gBAAU,IACTV,kBAAY,IAObW,cAAgB,SAAC1J,GAIvB,MAHY,UAAGA,GAAM2J,QAAQ,4BAA4B,SAAUjF,GAClE,MAAO,KAAOA,EAAEkF,WAAW,GAAK,GACjC,GAED,EARC,EAAKb,eAAiBA,EAAY,CACnC,CAu0BC,OAv0BA,oCASD,SAAoBrO,GACnB,MAAM,QAAN,OAAeA,EAAGqL,MAAM,IAAIrH,KAAI,SAAAO,GAAI,OAAIA,EAAK2K,WAAW,EAAE,IAAE1J,KAAK,IAClE,GAAC,0BAED,SAAoBxF,GACnB,IAAMmP,EAAWnP,EAAGiP,QAAQ,SAAU,IAAIG,WAAW,QAAS,KAAKA,WAAW,QAAS,KACvF,OAAOlD,KAAK8C,cAAcG,EAC3B,GAAC,4BAGD,SAAsBE,GAAwF,IAAD,OAAnEC,EAAkC,uDAAG,WACxEC,EAAgB,IAAI3E,IACpB4E,EAAe,IAAI5E,IAEnB6E,EAAUJ,EAAMrL,KAAI,SAACuJ,EAAMxK,GAAK,qBAAauM,EAAW,YAAIvM,EAAK,IACjE2M,EAAcJ,EACdK,EAA4B,aAAhBL,EAA6B,SAAW,YAG9C,SAANrE,EAAOoE,GAGZ,IAHiH,IAAjFO,EAAK,uDAAG,EAAGC,EAAuB,uCAAEC,EAAgB,uDAAG,GACjF3K,EAAwB,GAErB6E,EAAI,EAAGA,EAAIqF,EAAM9J,OAAQyE,IAAK,CAAC,IAAD,IAChCuD,EAAO8B,EAAMrF,GACnB8F,EAAO1K,KAAK,GAAD,OAAI4E,IACG,EAAKgF,cAAczB,EAAKwC,KAA1C,IACIxF,EAAG,eAAW+E,EAAW,YAAIQ,EAAOtK,KAAK,MAEvCwK,EAAgBR,EAAapF,IAAImD,EAAKwC,KACxCC,IACHzF,EAAMyF,EAAczF,KAErB,IAAM0F,EAAYV,EAAcnF,IAAIG,GAEhC2F,GAAuB,kBACvB3C,GAAI,OACPhD,IAAAA,EACAqF,MAAAA,EACAN,YAAAA,IAAW,SAEVK,EAAYE,EAAS,CAACA,GAAU,KAAE,SAClCH,EAAc,IAAE,IAIlB,GAAIO,EAIFC,EAHYD,EAAUN,GAAW3L,KAAI,SAAAuJ,GAAI,OAAIA,EAAKhD,GAAG,IAAE4F,UAAe,OAANN,QAAM,IAANA,OAAM,EAANA,EAAQtF,MAAO,IAGrE0F,GAEH,kBACHA,GAAS,cAEXN,EAAS,CAAIE,GAAM,eAAKI,EAAUN,OAKlCpC,EAAKmC,IAAiC,QAArB,EAAInC,EAAKmC,UAAY,OAAjB,EAAmBnK,QAC3C0F,EAAIsC,EAAKmC,IAAgB,GAAIE,EAAQ,EAAGM,EAASJ,GAGlDP,EAAc1E,IAAIqF,EAAQ3F,IAAK2F,GAC/BV,EAAa3E,IAAIqF,EAAQH,IAAKG,GAC9B/K,EAAIC,KAAK8K,GAETJ,EAAOM,KACR,CACA,OAAOjL,CACR,CAEA8F,CAAIoE,GAGJE,EAAcxK,SAAQ,SAAAR,GAErB,IADA,IAAM8L,EAAkB9L,EAAKoL,GACpB3F,EAAI,EAAGA,EAAIqG,EAAgB9K,OAAQyE,IAAK,CAChD,IACMsG,EADcD,EAAgBrG,GACTO,IACrBW,EAAUqE,EAAcnF,IAAIkG,GAC9BpF,IACHA,EAAQwE,GAAatK,KAAKb,GAC1BgL,EAAc1E,IAAIyF,EAAQpF,GAE5B,CAEA3G,EAAKoL,GAAapL,EAAKoL,GAAW3L,KAAI,SAAAuJ,GAAI,OAAIgC,EAAcnF,IAAImD,EAAKhD,IAAI,IACzEgF,EAAc1E,IAAItG,EAAKgG,IAAKhG,EAC7B,IAEA,IAAMqK,EAAWa,EAAQzL,KAAI,SAAAuM,GAAM,OAAIhB,EAAcnF,IAAImG,EAAO,IAGhE,OAFY3B,GAAQ,OAAOA,GAAY,EAGxC,GAAC,sBAED,SAAqC5N,GAAY,IAAD,OAE/CkL,KAAKuC,qBAAuB,IAAI7D,IAChCsB,KAAKwC,UAAY,GAEjBxB,QAAQC,IAAI,OAAQnM,GAGpB,IAAMwP,EAAqCtE,KAAKuE,eAAezP,EAAM,UAC/D0P,EAAsCxE,KAAKuE,eAAezP,EAAM,YAEtEkM,QAAQC,IAAI,oBAAqBuD,GAuBjC,IAAK,IAAI1G,EAAI,EAAGA,EAAI0G,EAAkBnL,OAAQyE,IAC7C0G,EAAkB1G,GAAGO,IAAG,eAAWP,GACnC0G,EAAkB1G,GAAGG,OAASqG,EAAiBxG,GAAGG,OAGnD,IAAMyE,EAAW8B,EAAkB,GAC7BC,EAAgBD,EACtBxE,KAAK0C,SAAWA,EAEhB,IAAMgC,EAAgB1E,KAAK2E,mBAAmBF,GAE9CzD,QAAQC,IAAI,gBAAiByD,GAE7B1E,KAAK4E,WAAWF,GAAeG,MAAK,WAInC,EAAKC,WACN,GACD,GAAC,gCAED,SAA0B3B,GAEzB,GAAS,OAALA,QAAK,IAALA,GAAAA,EAAO9J,OAAQ,CAElB,IAAM0L,EAAU/E,KAAK5B,UAAU+E,EAAO,UAChC6B,EAAWhF,KAAK5B,UAAU+E,EAAO,YACjC8B,EAAU,kBAAOF,IAAO,OAAKC,IAG7BE,EAAgBlF,KAAKnC,SAASoH,EAAY,OAC1CE,EAAsBnF,KAAKnC,SAASoH,EAAY,aAChDzC,EAA8B,GACpC0C,EAAcrM,SAAQ,SAACR,GACtBmK,EAAUtJ,KAAKb,EAChB,IACA2H,KAAKpC,QAAUsH,EACflF,KAAKoC,cAAgB+C,EACrBnF,KAAKwC,UAAYA,CAClB,MACCxC,KAAKwC,UAAY,GAElB,OAAOxC,KAAKwC,SACb,GAEA,+BAIA,SAAyBW,EAAyBiC,GAAgC,IAAD,OA2BhF,OAzBYjC,EAAMrL,KAAI,SAACuJ,GAEtB,OADuB,EAAKkB,qBAAqBrE,IAAImD,EAAKhD,KAqBlD,GAnBkB,YAArBgD,EAAKgE,YACF,GAAN,OAAUhE,EAAU,IAAC,yEACJA,EAAKiE,WAAU,uJAI1BjE,EAAKhD,IAAG,kBAIT,GAAN,OAAUgD,EAAU,IAAC,+EACFA,EAAKhD,IAAG,4HAKpBgD,EAAKhD,IAAG,iBAKjB,IACWjG,QAAO,SAAAC,GAAI,QAAMA,CAAI,GACjC,GAEA,uCAIA,SAAiC8K,GAA0B,IAAD,OACnDlK,EAAgB,GAyBtB,OAxBAkK,EAAMtK,SAAQ,SAACwI,GACd,IAAQpD,EAAqBoD,EAArBpD,OAAQN,EAAa0D,EAAb1D,UAEfM,GAAU,IAAIpF,SAAQ,SAACoF,GAEvB,KADuB,EAAKsE,qBAAqBrE,IAAImD,EAAKhD,MAAQ,EAAKkE,qBAAqBrE,IAAID,EAAOI,MAClF,CACpB,IAAMmB,EAAG,sBACNvB,EAAOI,IAAG,aAAOgD,EAAKhD,IAAG,yBAAmBJ,EAAOI,IAAG,iBAAWgD,EAAKhD,IAAG,QAClD,IAAtBpF,EAAIX,QAAQkH,IACfvG,EAAIC,KAAKsG,EAEX,CACD,KACC7B,GAAY,IAAI9E,SAAQ,SAAC0M,GAEzB,KADuB,EAAKhD,qBAAqBrE,IAAImD,EAAKhD,MAAQ,EAAKkE,qBAAqBrE,IAAIqH,EAAMlH,MACjF,CACpB,IAAMmB,EAAG,sBACN6B,EAAKhD,IAAG,aAAOkH,EAAMlH,IAAG,yBAAmBgD,EAAKhD,IAAG,iBAAWkH,EAAMlH,IAAG,QAChD,IAAtBpF,EAAIX,QAAQkH,IACfvG,EAAIC,KAAKsG,EAEX,CACD,GACD,IACOvG,CACR,GAEA,8BAGA,WAA2B,IAAD,OACnB8G,EAAYC,KACZqC,EAAiB,IAAI3D,IAE3BuB,EAAAA,IAAa,SAASuB,SAEtBvB,EAAAA,IAAa,SAASuF,MAAK,SAACnN,GAC3B,IAAK,IAAD,EACGgG,EAAMhG,EAAKgG,IACXoH,EAAuB,QAAf,EAAG,EAAK7H,eAAO,aAAZ,EAAcM,IAAIG,GAC7BqH,EAAoBzD,GAAyB,OAARwD,QAAQ,IAARA,OAAQ,EAARA,EAAUJ,cAAe,KAAO5D,EAAW,GAChFkE,EAAW1F,EAAAA,IAAa,IAAD,OAAK5B,EAAG,aAAYuH,QAC3CpG,EAAM,CACXqG,WAAYxN,EACZyN,SAAUzH,EACVvJ,KAAM2Q,EACNM,MAAOL,EACP5R,GAAIuK,EACJzG,EAAG+N,EAAI7F,OAAOlI,EACdyH,EAAGsG,EAAI7F,OAAOT,GAEfgD,EAAe1D,IAAIN,EAAKmB,GAExBS,EAAAA,IAAa,IAAD,OAAK5B,EAAG,0BAAyB8B,KAAK,OAAO,WAE1D,CAAE,MAAO6F,GACRhF,QAAQC,IAAI+E,EACb,CAUD,IAEAhG,KAAKqC,eAAiBA,EAEtBrC,KAAKiG,gBAkCL,IAAIC,EAAe,KACnBjG,EAAAA,IAAa,yBACXW,GAAG,SAAS,SAAUS,EAAWZ,GACjC0F,aAAaD,GAEbA,EAAUE,YAAW,WACpBrG,EAAU4C,iBAAmB5C,EAAU4C,gBAAgBtB,EACxD,GAAG,IACJ,IAWD,IAAMgF,EAAa5R,SAASC,eAAe,cACvC2R,IACHA,EAAWC,QAAU,SAACzQ,GAErB,IADA,IAAI0Q,GAAS,EACJzI,EAAI,EAAGA,EAAIjI,EAAE2Q,KAAKnN,OAAQyE,IAAK,CACvC,IAAM2I,EAAO5Q,EAAE2Q,KAAK1I,GACpB,GAAI2I,EAAK3S,KAAO2S,EAAK3S,GAAGwE,QAAQ,SAAU,CACzCiO,GAAS,EACT,KACD,CACD,CACKA,GACJxG,EAAU2G,SAEZ,EAEF,GAAC,2BAED,WAAwB,IAAD,OAChB3G,EAAYC,KACZ2G,EAAO1G,EAAAA,IAAa,iBAAiBnL,OAE3CkM,QAAQC,IAAI,OAAQ0F,GAEpBA,EAAK9N,SAAQ,SAACR,GAAe,IAAD,EACrBsN,EAAMtN,EAAKuO,KACXC,EAAMxO,EAAK4F,OAAOI,IAClBoH,EAAuB,QAAf,EAAG,EAAK7H,eAAO,aAAZ,EAAcM,IAAI2I,GAE/BpB,IACHxF,EAAAA,GAAU,IAAD,OAAK4G,IAAOrF,SACrBvB,EAAAA,GAAU,cACRC,OAAO,KACPC,KAAK,KAAM0G,GACX1G,KAAK,QAAS,QACdA,KAAK,OAAQ,UACbS,GAAG,SAAS,SAAUS,EAAWZ,GAAS,IAAD,EACnCqG,EAAmC,QAA3B,EAAG/G,EAAUsC,sBAAc,aAAxB,EAA0BnE,IAAI8B,KAAKlM,IAChDgT,GACH/G,EAAUgH,kBAAkBD,GAE7B/G,EAAU4C,iBAAmB5C,EAAU4C,gBAAwB,OAARmE,QAAQ,IAARA,OAAQ,EAARA,EAAUhS,KAClE,IACCoL,OAAO,QACPC,KAAK,KAAK,QAAD,OAAU0G,IACnB1G,KAAK,IAAKwF,EAAI/N,GACduI,KAAK,IAAKwF,EAAItG,GACdc,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,QAASwF,EAAInQ,OAClB2K,KAAK,SAAUwF,EAAInS,QACnB2M,KAAK,OAAQ,QACbA,KAAK,SAAU,WACfA,KAAK,eAAgB,GACrBA,KAAK,QAAS,wBAEhBF,EAAAA,GAAU,IAAD,OAAK4G,IACZ3G,OAAO,QACPC,KAAK,IAAI,IAAD,OAAMwF,EAAI/N,EAAC,YAAI+N,EAAIqB,GAAE,aAAKrB,EAAI/N,EAAgB,EAAZ+N,EAAInQ,MAAY,EAAC,YAAImQ,EAAIqB,KACnE7G,KAAK,SAAU,WACfA,KAAK,mBAAoB,OAE3BF,EAAAA,GAAU,IAAD,OAAK4G,IACZ3G,OAAO,QACPC,KAAK,QAAS,UACdA,KAAK,IAAKwF,EAAI/N,GACduI,KAAK,IAAKwF,EAAItG,GACdc,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,QAASwF,EAAInS,QAClB2M,KAAK,SAAUwF,EAAInS,QACnB2M,KAAK,OAAQsF,EAAS7D,OAExB3B,EAAAA,GAAU,IAAD,OAAK4G,IACZ3G,OAAO,QACPC,KAAK,QAAS,UACdA,KAAK,KAAK,YAAD,OAAc0G,IACvB1G,KAAK,IAAKwF,EAAI/N,EAAI+N,EAAInS,OAAS,GAC/B2M,KAAK,IAAKwF,EAAItG,GACdc,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,QAASwF,EAAInS,OAAS,GAC3B2M,KAAK,SAAUwF,EAAInS,QACnB2M,KAAK,OAAQsF,EAAS7D,OACtBzB,KAAK,QAAS,wBAEhBF,EAAAA,GAAU,IAAD,OAAK4G,IACZ3G,OAAO,KACPC,KAAK,KAAK,QAAD,OAAU0G,IACnBrG,KAAKiF,EAASwB,MAEhBhH,EAAAA,GAAU,SAAD,OAAU4G,EAAG,SACpB1G,KAAK,OAAQ,QACbA,KAAK,QAASwF,EAAInS,OAAS,GAC3B2M,KAAK,SAAUwF,EAAInS,OAAS,GAC5B2M,KAAK,IAAKwF,EAAI/N,EAAI+N,EAAInS,OAAS,GAC/B2M,KAAK,IAAKwF,EAAItG,EAAiB,IAAbsG,EAAInS,QAExByM,EAAAA,GAAU,IAAD,OAAK4G,IACZ3G,OAAO,QACPpF,KAAK2K,EAAS9M,OACdwH,KAAK,QAAS,YACdA,KAAK,IAAKwF,EAAI/N,EAAiB,IAAb+N,EAAInS,QACtB2M,KAAK,IAAKwF,EAAItG,EAAiB,IAAbsG,EAAInS,OAAgB,GACtC2M,KAAK,QAASwF,EAAInS,QAClB2M,KAAK,SAAUwF,EAAInS,QACnB2M,KAAK,cAAe,QACpBA,KAAK,OAAQsF,EAAS7D,OAIxB3B,EAAAA,GAAU,IAAD,OAAK4G,IACZjG,GAAG,aAAa,SAAUH,GAC1BR,EAAAA,GAAU,SAAD,OAAU4G,IAAO1G,KAAK,SAAUsF,EAAS7D,OAClD3B,EAAAA,GAAU,aAAD,OAAc4G,IACrB1G,KAAK,KAAM,GACXA,KAAK,KAAM,GACXA,KAAK,IAAKwF,EAAI/N,EAAI+N,EAAInS,OAAS,EAClC,IACCoN,GAAG,YAAY,SAAUH,GACzBR,EAAAA,GAAU,SAAD,OAAU4G,IAAO1G,KAAK,SAAU,WACzCF,EAAAA,GAAU,aAAD,OAAc4G,IACrB1G,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,KAAMwF,EAAInS,OAAS,GACxB2M,KAAK,IAAKwF,EAAI/N,EAAI+N,EAAInS,OAAS,EAClC,IAEDyM,EAAAA,GAAU,IAAD,OAAK4G,IACZ3G,OAAO,QACPpF,KAAK2K,EAASyB,MACd/G,KAAK,QAAS,eACdA,KAAK,IAAKwF,EAAI/N,EAAiB,IAAb+N,EAAInS,QACtB2M,KAAK,IAAKwF,EAAItG,EAAiB,GAAbsG,EAAInS,QACtB2M,KAAK,QAASwF,EAAInS,QAClB2M,KAAK,SAAUwF,EAAInS,QAerByM,EAAAA,GAAU,IAAD,OAAK4G,IACZ3G,OAAO,KACPC,KAAK,KAAK,oBAAD,OAAsB0G,IAC/B3G,OAAO,QACPpF,KAAK2K,EAAS0B,OAAO7M,OACrB6F,KAAK,IAAKwF,EAAI/N,EAAI+N,EAAInQ,MAAqB,IAAbmQ,EAAInS,OAAe,GACjD2M,KAAK,IAAKwF,EAAItG,EAAiB,GAAbsG,EAAInS,OAAe,GAGzC,GACD,GAAC,iCAED,SAA2B4T,GAAkB,IAAD,IACrC7J,EAAcyC,KAAKpC,QAAQM,IAAIkJ,GAC/BC,EAAgC,OAAX9J,QAAW,IAAXA,OAAW,EAAXA,EAAa6F,YACxC,GAA+B,OAAX7F,QAAW,IAAXA,OAAW,EAAXA,EAAaU,SAAU,GAApCqJ,GAAsC,YAA7B,GAChB,GAAiC,OAAX/J,QAAW,IAAXA,OAAW,EAAXA,EAAaI,WAAY,GAAxC4J,GAA0C,YAA/B,GACZC,EAAaF,IAAyB,QAAhB,EAAItH,KAAKpC,eAAO,aAAZ,EAAcM,IAAIoJ,EAAUjJ,MACtDoJ,EAAeF,IAA2B,QAAhB,EAAIvH,KAAKpC,eAAO,aAAZ,EAAcM,IAAIqJ,EAAYlJ,MAElE,GAAImJ,GAAcjK,GAAsC,aAAvB8J,EAAmC,CACnE,IAAMK,EAAaF,EAAWG,oBAAsB,GAC9ChK,EAAW6J,EAAW7J,UAAY,GAClCiK,EAAqBJ,EAAW7J,SAASuG,MACzC2D,EAAaH,EAAWnJ,QAE9B,GAAIsJ,EAAY,CAEf7H,KAAKuC,qBAAqBuF,OAAOD,EAAWxJ,KAE5C,IADA,IAAM0J,GAAU,OAAOF,EAAWlK,UAC3BoK,EAAW1O,QAAQ,CACzB,IAAM2O,EAAWD,EAAWxJ,QACxByJ,IACHhI,KAAKuC,qBAAqBuF,OAAOE,EAAS3J,KAC1C0J,EAAW7O,KAAI,MAAf6O,GAAU,OAAUC,EAASrK,UAAY,KAE3C,CAGAJ,EAAY+H,YAAc/H,EAAY+H,YAAc,GAAK,EACzD/H,EAAY0K,gBAAkBP,EAC9BF,EAAWG,mBAAqBD,EAC5BA,EAAWrO,OACdmO,EAAW7J,SAAQ,kBAAOA,GAAQ,CAAEkK,EAAYD,IAEhDJ,EAAW7J,SAAQ,kBAAOA,GAAQ,CAAEkK,GAEtC,CACD,CAEA,GAAIJ,GAAgBlK,GAAsC,WAAvB8J,EAAiC,CACnE,IAAMK,EAAaD,EAAaS,kBAAoB,GAC9CjK,EAASwJ,EAAaxJ,QAAU,GAChC2J,EAAqBH,EAAaxJ,OAAOiG,MACzC2D,EAAaH,EAAWnJ,QAE9B,GAAIsJ,EAAY,CAEf7H,KAAKuC,qBAAqBuF,OAAOD,EAAWxJ,KAE5C,IADA,IAAM0J,GAAU,OAAOF,EAAW5J,QAC3B8J,EAAW1O,QAAQ,CACzB,IAAM2O,EAAWD,EAAWxJ,QACxByJ,IACHhI,KAAKuC,qBAAqBuF,OAAOE,EAAS3J,KAC1C0J,EAAW7O,KAAI,MAAf6O,GAAU,OAAUC,EAAS/J,QAAU,KAEzC,CAGAV,EAAY+H,YAAc/H,EAAY+H,YAAc,GAAK,EACzD/H,EAAY0K,gBAAkBP,EAC9BD,EAAaS,iBAAmBR,EAC5BA,EAAWrO,OACdoO,EAAaxJ,OAAM,kBAAOA,GAAM,CAAE4J,EAAYD,IAE9CH,EAAaxJ,OAAM,kBAAOA,GAAM,CAAE4J,GAEpC,CACD,CACD,GAEA,wBAIA,SAAkB1E,EAAyBiC,GAAsB,IAAD,OAC/DpF,KAAK4C,cAAgB5C,KAAK4C,eAK1B,IAAMuF,EAAcnI,KAAKoI,kBAAkBjF,EAAOiC,GAC5CiD,EAAsBrI,KAAKsI,0BAA0BnF,GACrDoF,EAAM,6IAMTJ,EAAY7O,KAAK,KAAI,YAAI+O,EAAoB/O,KAAK,KAAI,eAG3C2G,EAAAA,GAAU,UACtByB,SAAS,CACTpE,MAAM,EACNkL,oBAAqB,CAAC,EAAG,KAIzBC,IAAIF,GACN,OAAO,IAAIG,SAAQ,SAACC,EAASC,GAC5BxC,YAAW,WACV,IACEnG,EAAAA,GAAU,UACTyB,SAAS,CACTpE,MAAM,EACNkL,oBAAqB,CAAC,EAAG,KAIzBK,UAAUN,EACb,CAAE,MAAOvC,GACRhF,QAAQC,IAAI+E,EACb,CAGA,EAAK8C,mBAEL,EAAKjG,YAAc,EAAKA,aACxB8F,EAAQ,GACT,GAAG,IACJ,GACD,GAAC,+BAED,SAAyBtH,GAAoB,IAAD,IAG3CpB,EAAAA,IAAa,iBAAiBE,KAAK,SAAU,WAAWA,KAAK,OAAQ,WACrEF,EAAAA,IAAa,cAAcE,KAAK,OAAQ,WACxCF,EAAAA,IAAa,iBAAiBE,KAAK,OAAQ,WAC3CF,EAAAA,IAAa,cAAcE,KAAK,SAAU,WAC1CF,EAAAA,IAAa,iBAAiBE,KAAK,SAAU,WAAWA,KAAK,OAAQ,WAErE,IAAM4I,EAAoB/I,KAAKpB,YAAY,CAACyC,EAAKvM,MAAO,SAAU,MAAoB,QAAf,EAAEkL,KAAK0C,gBAAQ,aAAb,EAAerE,KAClF2K,EAAsBhJ,KAAKpB,YAAY,CAACyC,EAAKvM,MAAO,WAAY,MAAoB,QAAf,EAAEkL,KAAK0C,gBAAQ,aAAb,EAAerE,KACtF4K,EAAiBjJ,KAAK5B,UAAU2K,EAAmB,UACnDG,EAAmBlJ,KAAK5B,UAAU4K,EAAqB,YAE7DhI,QAAQC,IAAI,iBAAkBgI,GAE9B,IAAK,IAAInL,EAAI,EAAGA,EAAImL,EAAe5P,OAAQyE,IAAK,CAC/C,IAAMzF,EAAO4Q,EAAenL,GAG5B,GAAIzF,EAAM,CAKT,IAAM8Q,EAAe9Q,EAAKuJ,MAC1B3B,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,aAAY8B,KAAK,OAAQgJ,GAClDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,eAAc8B,KAAK,OAAQgJ,GACpDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,kBAAiB8B,KAAK,OAAQ,OACxD,CAGA,GAAI9H,GAAQA,EAAK4F,QAAU5F,EAAK4F,OAAO5E,OACtC,IAAK,IAAIyE,EAAI,EAAGA,EAAIzF,EAAK4F,OAAO5E,OAAQyE,IAAK,CAC5C,IAAMsL,EAAM/Q,EAAK4F,OAAOH,GAClBuL,EAAM,kBAAcD,EAAI/K,IAAG,iBAAShG,EAAKgG,KAC/C4B,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,UAASlJ,KAAK,SAAU,WAC/CF,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,aAAYlJ,KAAK,SAAU,WAAWA,KAAK,OAAQ,UAC3E,CAEF,CAGA,IAAK,IAAIrC,EAAI,EAAGA,EAAIoL,EAAiB7P,OAAQyE,IAAK,CACjD,IAAMzF,EAAO6Q,EAAiBpL,GAC9B,GAAIzF,EAAM,CAKT,IAAM8Q,EAAe9Q,EAAKuJ,MAC1B3B,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,aAAY8B,KAAK,OAAQgJ,GAClDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,eAAc8B,KAAK,OAAQgJ,GACpDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,kBAAiB8B,KAAK,OAAQ,OACxD,CAEA,GAAI9H,GAAQA,EAAKsF,UAAYtF,EAAKsF,SAAStE,OAC1C,IAAK,IAAIyE,EAAI,EAAGA,EAAIzF,EAAKsF,SAAStE,OAAQyE,IAAK,CAC9C,IAAMyH,EAAQlN,EAAKsF,SAASG,GACtBuL,EAAM,kBAAchR,EAAKgG,IAAG,iBAASkH,EAAMlH,KACjD4B,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,UAASlJ,KAAK,SAAU,WAC/CF,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,aAAYlJ,KAAK,SAAU,WAAWA,KAAK,OAAQ,UAC3E,CAEF,CACD,GAAC,qBAED,WACCa,QAAQC,IAAI,WAOZ,IAFA,IAAMqI,EAAWtJ,KAAKwC,UAEb1E,EAAI,EAAGA,EAAIwL,EAASjQ,OAAQyE,IAAK,CACzC,IAAMzF,EAAOiR,EAASxL,GACtB,GAA0B,aAAlB,OAAJzF,QAAI,IAAJA,OAAI,EAAJA,EAAMgN,aAAV,CAOA,GAAIhN,EAAM,CAET,IAAM8Q,EAAe9Q,EAAKuJ,MAC1B3B,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,aAAY8B,KAAK,OAAQgJ,GAClDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,eAAc8B,KAAK,OAAQgJ,GACpDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,kBAAiB8B,KAAK,OAAQ,OACxD,CAIA,GAAI9H,GAAQA,EAAK4F,QAAU5F,EAAK4F,OAAO5E,OACtC,IAAK,IAAIyE,EAAI,EAAGA,EAAIzF,EAAK4F,OAAO5E,OAAQyE,IAAK,CAC5C,IAAMsL,EAAM/Q,EAAK4F,OAAOH,GAClBuL,EAAM,kBAAcD,EAAI/K,IAAG,iBAAShG,EAAKgG,KAC/C4B,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,UAASlJ,KAAK,SAAU,WAC/CF,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,aAAYlJ,KAAK,SAAU,WAAWA,KAAK,OAAQ,UAC3E,CAnBD,MAHCF,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,aAAY8B,KAAK,SAAU,WACpDF,EAAAA,GAAU,IAAD,OAAK5H,EAAKgG,IAAG,UAAS8B,KAAK,OAAQ,UAuB9C,CAEA,IAAK,IAAIrC,EAAI,EAAGA,EAAIwL,EAASjQ,OAAQyE,IAAK,CACzC,IAAMzF,EAAOiR,EAASxL,GACtB,GAA0B,aAAlB,OAAJzF,QAAI,IAAJA,OAAI,EAAJA,EAAMgN,aAAV,CAMA,GAAIhN,EAAM,CAET,IAAM8Q,EAAe9Q,EAAKuJ,MAC1B3B,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,aAAY8B,KAAK,OAAQgJ,GAClDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,eAAc8B,KAAK,OAAQgJ,GACpDlJ,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,kBAAiB8B,KAAK,OAAQ,OACxD,CAEA,GAAI9H,GAAQA,EAAKsF,UAAYtF,EAAKsF,SAAStE,OAC1C,IAAK,IAAIyE,EAAI,EAAGA,EAAIzF,EAAKsF,SAAStE,OAAQyE,IAAK,CAC9C,IAAMyH,EAAQlN,EAAKsF,SAASG,GACtBuL,EAAM,kBAAchR,EAAKgG,IAAG,iBAASkH,EAAMlH,KACjD4B,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,UAASlJ,KAAK,SAAU,WAC/CF,EAAAA,IAAa,IAAD,OAAKoJ,EAAM,aAAYlJ,KAAK,SAAU,WAAWA,KAAK,OAAQ,UAC3E,CAhBD,MAHCF,EAAAA,IAAa,IAAD,OAAK5H,EAAKgG,IAAG,aAAY8B,KAAK,SAAU,WACpDF,EAAAA,GAAU,IAAD,OAAK5H,EAAKgG,IAAG,UAAS8B,KAAK,OAAQ,UAoB9C,CACD,GAEA,wBAMA,SAAkBrM,GAAa,IAAD,EAC7BkM,KAAKuJ,UAAUvJ,KAAKyC,cAAgB,IACpC,IACM+G,EADmBvJ,EAAAA,GAAU,cAAc2F,QACnB6D,YAAYpK,EAEpCuF,EAAqC,QAAtB,EAAG5E,KAAKqC,sBAAc,aAAnB,EAAqBnE,IAAIpK,GACjD,GAAI8Q,EAAY,CAEf,IAAMhN,GAAKgN,EAAWhN,GAAK,GAAK,IAAMoI,KAAK9C,WAAa,EAAI,IAEtDmC,IAAMmK,IAAa5E,EAAWvF,IAAM,GAAK,IAAMW,KAAK7C,YAAc,EACxE6C,KAAKpD,SAAS8D,KAAKV,KAAK1C,KAAKuD,UAAWZ,EAAAA,IAAAA,UAA0BrI,EAAGyH,GAAG0B,MAAM,GAC/E,CACAf,KAAK0J,WAAW5V,EACjB,GAEA,uBAMA,WACCkM,KAAKuJ,UAAUvJ,KAAKyC,cAAgB,IACpC,IACMkH,EADiB1J,EAAAA,GAAU,cAAcoB,OAChBC,UACzBsI,EAAiBD,EAAanU,MAAQ,EAEtCoC,EADcoI,KAAK9C,WAAa,EACd0M,EAElBC,EAAiBF,EAAanW,OAAS,EAEvC6L,EADcW,KAAK7C,YAAc,EACf0M,EAAiB,IAEzC7J,KAAKpD,SAAS8D,KAAKV,KAAK1C,KAAKuD,UAAWZ,EAAAA,IAAAA,UAA0BrI,EAAGyH,GAAG0B,MAAM,GAC/E,GAAC,wBAED,SAAkBjN,GAAa,IAAD,IACvB8Q,EAAgC,QAAtB,EAAG5E,KAAKqC,sBAAc,aAAnB,EAAqBnE,IAAIpK,GACtCgT,EAAuB,QAAf,EAAG9G,KAAKpC,eAAO,aAAZ,EAAcM,IAAIpK,GAC/BkM,KAAKyC,cACRxC,EAAAA,IAAa,IAAD,OAAKD,KAAKyC,aAAY,kBAAiBtC,KAAK,OAAQ,QAE7DyE,GAAckC,IACjB9G,KAAKyC,aAAe3O,EAatB,GAAC,uBAED,SAAiBA,GAAa,IAAD,IACtB8Q,EAAgC,QAAtB,EAAG5E,KAAKqC,sBAAc,aAAnB,EAAqBnE,IAAIpK,GACtCgT,EAAuB,QAAf,EAAG9G,KAAKpC,eAAO,aAAZ,EAAcM,IAAIpK,GAC/B8Q,GAAckC,GACjB7G,EAAAA,IAAa,iBAAD,OAAkB2E,EAAWkB,SAAQ,MAAK3F,KAAK,OAAQ,UAOrE,KAAC,EAz1BkC,CAAS7D,G,WCrD9B,SAASwN,EAASpW,GAChC,OAA8BC,EAAAA,EAAAA,WAAS,GAAM,eAAtCqB,EAAO,KAAE+U,EAAU,KAC1B,GAA0CpW,EAAAA,EAAAA,WAAS,GAAM,eAAlDqW,EAAa,KAAEC,EAAgB,KACtC,GAAgCtW,EAAAA,EAAAA,WAAS,GAAM,eAAxCwB,EAAQ,KAAE+U,EAAW,KAC5B,GAAoCvW,EAAAA,EAAAA,UAA4B,IAAG,eAA5DwW,EAAU,KAAEC,EAAa,KAChC,GAA0CzW,EAAAA,EAAAA,WAAS,GAAM,eAAlD0W,EAAa,KAAEC,EAAgB,KACtC,GAAwC3W,EAAAA,EAAAA,YAAyB,eAA1D4W,EAAY,KAAEC,EAAe,KACpC,GAAoBrW,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAEFqW,GAFS,EAAJnW,MAESoW,EAAAA,EAAAA,WAEpB,GAA+C/W,EAAAA,EAAAA,YAA2B,eAAnEgX,EAAe,KAAEC,EAAmB,KACrCC,GAAqBH,EAAAA,EAAAA,QAAOC,IAMlCpW,EAAAA,EAAAA,YAAU,WACT,IAN2BO,EAMrBgW,EAAS,IAAI5I,EAAgB,CAClC3F,YAAa,cACbC,WAAY,aACZC,OAAQ,KATkB3H,EAWRgW,EAVnBD,EAAmBE,QAAUjW,EAC7B8V,EAAoB9V,GAWpB,IAAMkW,EAAevW,SAASC,eAAe,gBACzCsW,EAAaC,gBAChBD,EAAaC,cAAcC,SAAW,WACrC9E,YAAW,WAAO,IAAD,EAChBpF,QAAQC,IAAI4J,EAAmBE,SACL,QAA1B,EAAAF,EAAmBE,eAAO,OAA1B,EAA4BI,QAC7B,GAAG,IACJ,EAEF,GAAG,KAEH5W,EAAAA,EAAAA,YAAU,WACT,GAAIoW,EAAiB,CACpB,IAAMS,GAAUC,EAAAA,EAAAA,IAAS,YAAc,GACvCC,EAAuBF,EACxB,CAED,GAAG,CAACT,IAEJ,IAAMY,EAAkB,SAAClK,GACxBL,QAAQC,IAAII,GACZ,IAAM9D,EAAcoN,GAAmBA,EAAgB/M,SAAW+M,EAAgB/M,QAAQM,IAAImD,EAAKhD,KACnG2C,QAAQC,IAAI,cAAe1D,GAE3B6M,EAAc,IACdE,GAAiB,GACjBL,GAAiB,IACjB5W,EAAAA,EAAAA,KAA6B,OAAXkK,QAAW,IAAXA,OAAW,EAAXA,EAAaiO,aAAc,IAAI3G,MAAK,SAAA5L,GACrD+H,QAAQC,IAAIhI,EAAInE,KAAK2W,OAAOC,QAC5B,IAAMA,EAASzS,EAAInE,KAAK2W,OAAOC,OAC/BtB,EAAcsB,GACdpB,GAAiB,EAClB,IAAGqB,OAAM,WACRrB,GAAiB,EAClB,GACD,EAEMgB,EAAyB,SAACnY,GAC3BwX,IACGxX,GACL4W,GAAW,IACX7W,EAAAA,EAAAA,IAAsBC,GACpB0R,MAAK,SAAC5L,GACN,IAAM2S,EAAM3S,EAAInE,KAAK2W,OAAOG,KAAO,GAC7BC,EAAS5S,EAAInE,KAAK2W,OAAOI,QAAU,CAAC,EAC1CpB,EAAYM,QAAUa,EAEjBA,EAAIvS,OAGR6Q,GAAY,GAFZA,GAAY,GAIbS,EAAgBmB,SAASF,GACzBjB,EAAgBhI,gBAAkB4I,EAClCZ,EAAgB/H,aAAe,WAC9BmH,GAAW,EACZ,EACAY,EAAgB9H,WAAa,WAC5BkH,GAAW,EACZ,EACAS,EAAgBqB,EACjB,IACCF,OAAM,SAACI,GACP/K,QAAQC,IAAI8K,GACZ7B,GAAY,EACb,IACC8B,SAAQ,WACRjC,GAAW,EACZ,MAEDY,EAAgBmB,SAAS,IACzB5B,GAAY,IAGf,EAEA,OACC,iBAAKjV,UAAU,WAAWnB,GAAG,gBAAe,WAC3C,mBACCA,GAAG,eACH6H,IAAI,GACJsQ,YAAY,IACZhX,UAAU,UACVC,MAAO,CAAEM,MAAO,OAAQhC,OAAQ,UAGhCwB,GAAU,gBAAKC,UAAU,2CAA0C,UAClE,SAAC,IAAI,CAACF,SAAUC,EAASkX,WAAW,SAAChQ,EAAO,IAAI,UAC/C,uBAEO,MAEV,SAAC,IAAM,CACNvD,MAAM,2BACNnD,MAAO,IACP2W,UAAU,EACVC,QAAS,WAAQnC,GAAiB,EAAO,EACzChQ,QAAS+P,EACT/U,UAAU,oBAAmB,UAE7B,SAAC,IAAI,CAACF,SAAUsV,EAAc,UAC7B,SAACgC,EAAA,EAAU,CAACvX,KAAMqV,QAKnBhV,GAAW,gBAAKF,UAAU,kCAAiC,UAC1D,4BACC,0BAAK,gBAAKA,UAAU,OAAO0G,IAAKtG,EAAQ,OAA8BwG,IAAI,QAC1E,gBAAK5G,UAAU,OAAM,SAAEb,EAAE,mCAElB,MAEV,iBAAKa,UAAU,qBAAoB,WAClC,iBAAKA,UAAU,cAAa,WAC3B,iBAAKA,UAAU,kBAAiB,WAC/B,iBAAKA,UAAU,SAAQ,WACtB,iBAAMA,UAAU,cAAcqX,wBAAyB,CAAEC,QAAoB,OAAZhC,QAAY,IAAZA,OAAY,EAAZA,EAActD,OAAQ,OACvF,iBAAMhS,UAAU,WAAU,SAAc,OAAZsV,QAAY,IAAZA,OAAY,EAAZA,EAAc5R,YAE3C,yBAEc,OAAZ4R,QAAY,IAAZA,OAAY,EAAZA,EAAciC,aAAa1U,KAAI,SAAA2U,GAC9B,OAAO,gBAAK7W,QAAS,WACpB8W,OAAOC,KAAKF,EAAOtZ,IAAK,QACzB,EAAG8B,UAAU,8BAA6B,SAAEwX,EAAOnS,OACpD,UAIH,gBAAKrF,UAAU,wBAAuB,WAEvB,OAAZsV,QAAY,IAAZA,OAAY,EAAZA,EAAcmB,SAAU,IAAI5T,KAAI,SAAA4T,GAChC,OAAO,gBAAKzW,UAAU,gCAA+B,SAEnDyW,EAAO5T,KAAI,SAAA8U,GACV,OAAO,iBAAK3X,UAAU,MAAK,UAAE2X,EAAMtS,MAAM,SAAEsS,EAAMrS,QAClD,KAGH,UAIH,gBAAKzG,GAAG,cAAcmB,UAAU,QAAO,UACtC,gBAAKnB,GAAG,sBAKb,CC7Le,SAAS+Y,IACpB,OACI,+BACI,SAAC/C,EAAQ,KAGrB,CCPe,SAASgD,IACpB,OACI,gBAAK7X,UAAU,wBAAuB,UAClC,SAAC4X,EAAc,KAG3B,C,o5CCSe,SAASR,WAAW3Y,OAC/B,eAAwCC,EAAAA,mCAAAA,WAAS,GAAM,8IAAhDoZ,aAAY,cAAEC,gBAAe,cACpC,YAA8CrZ,EAAAA,mCAAAA,YAA2B,+IAAlEsZ,gBAAe,cAAEC,mBAAkB,cAE1C,YAA6CvZ,EAAAA,mCAAAA,UAA8B,CAAC,GAAE,+IAAvEwZ,eAAc,cAAEC,mBAAkB,cACnCC,mBAAoB3C,EAAAA,mCAAAA,QAAOyC,gBAC3BG,kBAAoB,SAACxY,GACvBuY,kBAAkBtC,QAAUjW,EAC5BsY,mBAAmBtY,EACvB,GAEAP,EAAAA,mCAAAA,YAAU,WACN,IAAMgZ,EAAiB,GACjBC,EAAqB,GAC3B9Z,MAAMoB,KAAK+D,SAAQ,SAAAgC,GACfA,EAAI4S,QAAQ5U,SAAQ,SAAA+T,GAChB,GAAgC,QAA5BA,EAAMc,aAAa9S,KAAgB,CACnC,IAAM+S,GAAMra,EAAAA,iDAAAA,IAAesZ,EAAMc,aAAanT,OAC9CgT,EAAQrU,KAAKyU,GACbH,EAAStU,KAAK,GAAD,OAAI2B,EAAI+S,QAAO,YAAIhB,EAAMiB,WAC1C,CACJ,GACJ,IACA7M,QAAQC,IAAI,UAAWsM,GACvB7E,QAAQoF,IAAIP,GAAS1I,MAAK,SAAA5L,GACtB,IACM8U,EADS9U,EAAInB,KAAI,SAAAO,GAAI,OAAIA,EAAKvD,KAAK2W,MAAM,IACzBvU,QAAO,SAACC,EAAKC,EAAMP,GAAK,qPAAWM,GAAG,6HAAGqW,EAAS3W,IAAM,wHAAQO,IAAI,GAAO,CAAC,GAClGkW,kBAAkBS,EACtB,GACJ,GAAG,CAACra,MAAMoB,OAEV,IAAMkZ,sBAAwB,SAAC3V,GAC3B,IACIb,KAAKyW,MAAM,GAAD,OAAI5V,EAAKkC,OAAS,IAChC,CAAE,MAAOyL,GACLhF,QAAQC,IAAI+E,EAChB,CACA,IAAMkI,EAAW1W,KAAKyW,MAAM,GAAD,OAAI5V,EAAKkC,OAAS,OACzC4T,EAAgB3U,OAAO4U,QAAQF,EAAS,IAAM,CAAC,GAAGhX,QAAO,SAACC,EAAS,GAAD,iIAAGkH,EAAG,KAAO,+IAAUlH,GAAG,CAAE,CAAEwB,MAAO0F,EAAK9F,UAAW8F,EAAKA,IAAAA,IAAK,GAAG,IAExI,OAAO,sDAAC,qCAAG,CAACgQ,KAAM,GAAG,UACjB,sDAAC,6DAAQ,CACL9U,OAAQ,SAAC+U,GACL,OAAO9W,KAAKC,UAAU6W,EAC1B,EACAvX,KAAM,QACNsE,kBAAkB,EAClB5E,QAAS0X,EACTnS,YAAY,EACZ7D,WAAY+V,KAIxB,EAEMK,mBAAqB,SAAC3T,EAAyB6S,EAAcG,EAAiBC,GAChF,OAAQjT,GACJ,IAAK,MACD,OAAO4T,mBAAmBf,GAC9B,IAAK,SACD,OAAOgB,aAAahB,GACxB,IAAK,OACD,OAAOiB,cAAcjB,GACzB,IAAK,SACD,OAAOkB,gBAAgBlB,GAC3B,IAAK,OACD,OAAOmB,WAAWnB,GACtB,IAAK,MACD,OAAOoB,UAAUpB,EAASG,EAASC,GACvC,QACI,OAAO,8DAAM3Y,MAAO,CAAE4Z,UAAW,aAAcC,WAAY,YAAa,SAAEtB,IAEtF,EAEMoB,UAAY,SAAC/Z,EAAc8Y,EAAiBC,GAC9C,IAAMmB,EAAM,UAAMpB,EAAO,YAAIC,GAO7B,OAAO,sEAAMR,kBAAkBtC,QAAQiE,GAAU3B,kBAAkBtC,QAAQiE,GAAQzU,MAAQ,IAC/F,EACMqU,WAAa,SAAC9Z,GAChB,OAAO,6DAAKwX,wBAAyB,CAAEC,OAAQzX,IACnD,EACM0Z,mBAAqB,SAAC1Z,GACxB,IAAMoZ,EAAoD1U,OAAO4U,QAAQtZ,GAAMoC,QAAO,SAACC,EAAS,GAAD,iIAAGkH,EAAG,KAAE4Q,EAAG,+IAAW9X,GAAG,CAAE,CAAEmD,MAAO+D,EAAK9D,MAAO0U,IAAK,GAAI,IACxJ,OAAO,6DAAKha,UAAU,eAAc,SAE5BiZ,EAASpW,KAAI,SAACO,EAAMxB,GAChB,OAAO,uDAAC,qCAAG,CAAC5B,UAAU,WAAU,WAC5B,sDAAC,qCAAG,CAACoZ,KAAM,EAAE,UAAC,6DAAKpZ,UAAU,OAAM,UAAC,2EAASoD,EAAKiC,MAAM,iBACxD,sDAAC,qCAAG,CAAC+T,KAAM,GAAG,UAAC,8DAAMnZ,MAAO,CAAE4Z,UAAW,aAAcC,WAAY,YAAa,SAAE1W,EAAKkC,YAAmB,yBAFtD1D,GAI5D,KAGZ,EACM4X,aAAe,SAAfA,aAAgB3Z,MAClB,IAAIoa,WAAkB,CAAC,EAEvB,OADAC,KAAK,cAAD,OAAera,QACZ,6DAAKG,UAAU,eAAc,UAChC,sDAAC,iEAAU,CAACZ,OAAQ6a,WAAYla,SAAS,KAEjD,EACM0Z,cAAgB,SAAC5Z,GACnB,OAAO,6DAAKG,UAAU,eAAeC,MAAO,CAAE4Z,UAAW,aAAcC,WAAY,YAAa,SAAEja,GACtG,EACM6Z,gBAAkB,SAAC7Z,GACrB,OAAO,gEACH6G,IAAK7G,EAAK3B,IACVic,iBAAe,EACfC,MAAM,0CACNpa,UAAU,eACVC,MAAO,CAAE2M,OAAQ,EAAGrO,OAAQ,MAEpC,EAEA,OACI,yHACI,sDAAC,qCAAI,CAACyB,UAAU,iBAAgB,SAExBvB,MAAMoB,KAAKgD,KAAI,SAAC+C,EAAKyU,GACjB,OAAO,sDAAC,6CAAY,CAACzU,IAAKA,EAAI+S,QAAQ,UAClC,8DAAK3Y,UAAU,qBAAoB,WAC/B,6DAAKA,UAAU,QAAO,SAEd4F,EAAI4S,QAAQ3V,KAAI,SAAC8U,EAAO2C,GACpB,OAAO,8DAAKta,UAAU,OAAM,WACxB,6DAAKA,UAAU,8BAA8BC,MAAO,CAAEsa,gBAAiB,GAAKlD,wBAAyB,CAAEC,OAAQK,EAAMiB,cACrH,sEACKU,mBAAmB3B,EAAMc,aAAa9S,KAAMgS,EAAMc,aAAanT,MAAOM,EAAI+S,QAAShB,EAAMiB,eACxF,mBAJoC0B,GAMlD,OAGR,6DAAKta,UAAU,kBAAiB,SAExB4F,EAAI4U,aAAa3X,KAAI,SAAA2U,GACjB,OAAO,uDAAC,qCAAM,CAACxX,UAAU,yBAAyBW,QAAS,WACvD8W,OAAOC,KAAKF,EAAOtZ,IAAK,QAC5B,EAAE,WACE,8DAAM8B,UAAU,YAAYqX,wBAAyB,CAAEC,OAAQE,EAAOxF,SACtE,8DAAMhS,UAAU,MAAK,SAAEwX,EAAO3R,SAEtC,UAGN,uBA1BkDwU,GA4BhE,OAKpB,C", "sources": ["api/commonPipeline.ts", "components/EchartCore/EchartCore.tsx", "components/TableBox/TableBox.tsx", "components/Loading/Loading.tsx", "pages/CommonPipeline/D3Tool.ts", "pages/CommonPipeline/TreePlusDiagram.tsx", "pages/CommonPipeline/TreePlus.tsx", "pages/CommonPipeline/CommonPipeline.tsx", "pages/CommonPipeline/DWStandard.tsx", "pages/CommonPipeline/NodeDetail.tsx"], "sourcesContent": ["import { AxiosResponse } from 'axios'\nimport axios, { AxiosResFormat } from '.'\nimport { ILayoutConfig, INodeDetailItem, INodeItem } from '../pages/CommonPipeline/TreePlusInterface'\n\nexport const getNodeRelation = (pipelineId: string): AxiosResFormat<{\n    dag: INodeItem[]\n}> => {\n    return axios.get(`/workflow_modelview/api/web/dag/idc/pipeline/${pipelineId}`)\n}\n\nexport const getPipelineConfig = (pipelineId: string): AxiosResFormat<Array<Record<string, string>>> => {\n    return axios.get(`/workflow_modelview/api/web/layout/idc/pipeline/${pipelineId}`)\n}\n\nexport const getNodeInfo = (pipelineId: string, nodeId: string): AxiosResFormat<{ detail: INodeDetailItem[] }> => {\n    return axios.get(`/workflow_modelview/api/web/node_detail/idc/pipeline/${pipelineId}/${nodeId}`)\n}\n\nexport const getNodeRelationCommon = (url: string): AxiosResFormat<{\n    dag: INodeItem[]\n    layout: ILayoutConfig\n}> => {\n    return axios.get(url)\n}\n\nexport const getNodeInfoCommon = (url: string): AxiosResFormat<{ detail: INodeDetailItem[] }> => {\n    return axios.get(url)\n}\n\nexport const getNodeInfoApi = (url: string): AxiosResFormat<any> => {\n    return axios.get(url)\n}\n", "import React, { useEffect, useState } from 'react'\nimport * as echarts from 'echarts';\n// import * as echarts from 'echarts/core';\n// import {\n//     BarChart,\n//     // 系列类型的定义后缀都为 SeriesOption\n//     BarSeriesOption,\n//     PieChart,\n//     PieSeriesOption,\n//     LineChart,\n//     LineSeriesOption,\n//     HeatmapChart,\n//     HeatmapSeriesOption\n// } from 'echarts/charts';\n// import {\n//     TitleComponent,\n//     // 组件类型的定义后缀都为 ComponentOption\n//     TitleComponentOption,\n//     TooltipComponent,\n//     TooltipComponentOption,\n//     GridComponent,\n//     GridComponentOption,\n//     // 数据集组件\n//     DatasetComponent,\n//     DatasetComponentOption,\n//     LegendComponent,\n//     // 内置数据转换器组件 (filter, sort)\n//     TransformComponent,\n//     CalendarComponentOption,\n//     CalendarComponent,\n//     VisualMapComponent,\n//     VisualMapComponentOption,\n//     ToolboxComponent\n// } from 'echarts/components';\nimport { LabelLayout, UniversalTransition } from 'echarts/features';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport './EchartCore.less';\nimport { Spin } from 'antd';\nimport { FieldNumberOutlined } from '@ant-design/icons';\nimport { useTranslation } from 'react-i18next';\n\nexport type ECOption = echarts.EChartsOption\n// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型\n// export type ECOption = echarts.ComposeOption<\n//     | BarSeriesOption\n//     | LineSeriesOption\n//     | TitleComponentOption\n//     | TooltipComponentOption\n//     | GridComponentOption\n//     | DatasetComponentOption\n//     | CalendarComponentOption\n//     | HeatmapSeriesOption\n//     | VisualMapComponentOption\n//     | PieSeriesOption\n// >;\n\n// // 注册必须的组件\n// echarts.use([\n//     LegendComponent,\n//     TitleComponent,\n//     TooltipComponent,\n//     GridComponent,\n//     DatasetComponent,\n//     TransformComponent,\n//     CalendarComponent,\n//     VisualMapComponent,\n//     ToolboxComponent,\n//     BarChart,\n//     LineChart,\n//     PieChart,\n//     LabelLayout,\n//     HeatmapChart,\n//     UniversalTransition,\n//     CanvasRenderer\n// ]);\n\ninterface IProps {\n    // option: ECOption\n    option: echarts.EChartsOption\n    loading?: boolean\n    title?: string\n    style?: React.CSSProperties\n    unit?: string\n    data?: {\n        xData: any[]\n        yData: any[]\n    }\n    isNoData?: boolean\n}\n\nconst defaultChartStyle: React.CSSProperties = {\n    height: 300\n}\n\n// https://echarts.apache.org/handbook/zh/how-to/data/dynamic-data\nexport default function EchartCore(props: IProps) {\n    const [chartInstance, setChartInstance] = useState<echarts.ECharts>()\n    const id = Math.random().toString(36).substring(2);\n    const { t, i18n } = useTranslation();\n\n    const option = {}\n\n    useEffect(() => {\n        const chartDom = document.getElementById(id)\n        if (chartDom) {\n            const chart = echarts.init(chartDom);\n            chart.setOption({ ...option, ...props.option })\n\n            if (!chartInstance) {\n                setChartInstance(chart)\n            }\n        }\n    }, [props.option, props.data])\n\n    return (\n        <Spin spinning={props.loading}>\n            <div className=\"chart-container\">\n                <div id={id} style={{ ...defaultChartStyle, ...props.style }}></div>\n                {\n                    props.isNoData ? <div className=\"chart-nodata\">\n                        <div>{t('暂无数据')}</div>\n                    </div> : null\n                }\n            </div>\n        </Spin>\n    )\n}\n", "import React, { ReactNode, useEffect, useState } from 'react';\nimport { Row, Col, Space, Table, ConfigProvider, Button, Modal, Tabs, message, Checkbox } from 'antd';\nimport './TableBox.less';\nimport { TablePaginationConfig } from 'antd/lib/table/Table';\nimport emptyImg from '../../images/emptyBg.png';\nimport { GetRowKey, SorterResult, TableRowSelection } from 'antd/lib/table/interface';\n// import ExportJsonExcel from 'js-export-excel';\nimport { Resizable } from 'react-resizable';\nimport { useTranslation } from 'react-i18next';\n\nconst CopyToClipboard = require('react-copy-to-clipboard');\n\ninterface IProps {\n\tsize?: 'large' | 'middle' | 'small'\n\ttableKey?: string\n\trowKey?: string | GetRowKey<any>;\n\ttitleNode?: string | ReactNode;\n\tbuttonNode?: ReactNode;\n\tdataSource: any;\n\tcolumns: any;\n\tpagination?: false | TablePaginationConfig;\n\tscroll?:\n\t| ({\n\t\tx?: string | number | true | undefined;\n\t\ty?: string | number | undefined;\n\t} & {\n\t\tscrollToFirstRowOnChange?: boolean | undefined;\n\t})\n\t| undefined;\n\tloading?: boolean;\n\trowSelection?: TableRowSelection<any>;\n\tcancelExportData?: boolean;\n\tonChange?: (\n\t\tpagination: TablePaginationConfig,\n\t\tfilters: Record<string, (string | number | boolean)[] | null>,\n\t\tsorter: SorterResult<any> | SorterResult<any>[],\n\t) => void;\n}\n\nconst ResizableTitle = ({ onResize, width, ...restProps }: any) => {\n\tif (!width) {\n\t\treturn <th {...restProps} />;\n\t}\n\n\treturn (\n\t\t<Resizable\n\t\t\twidth={width}\n\t\t\theight={0}\n\t\t\thandle={\n\t\t\t\t<span\n\t\t\t\t\tclassName=\"react-resizable-handle\"\n\t\t\t\t\tonClick={(e) => {\n\t\t\t\t\t\te.stopPropagation();\n\t\t\t\t\t}}\n\t\t\t\t/>\n\t\t\t}\n\t\t\tonResize={onResize}\n\t\t\tdraggableOpts={{ enableUserSelectHack: false }}\n\t\t>\n\t\t\t<th {...restProps} style={{ ...restProps?.style, userSelect: 'none' }} />\n\t\t</Resizable>\n\t);\n};\n\nconst TableBox = (props: IProps) => {\n\tconst [exportDataVisible, setExportDataVisible] = useState(false);\n\tconst [dataFormat, setDataFormat] = useState<{ header: any[]; data: any[] }>({\n\t\theader: [],\n\t\tdata: [],\n\t});\n\tconst [filterValue, setFilterValue] = useState<any[]>([]);\n\n\t// 可伸缩列设置\n\tconst [cols, setCols] = useState(props.columns);\n\tconst handleResize = (index: any) => {\n\t\treturn (_: any, { size }: any) => {\n\t\t\tif (size.width < 100) return\n\t\t\tconst temp = [...cols];\n\t\t\ttemp[index] = { ...temp[index], width: size.width };\n\t\t\tconst tableWidth = temp.reduce((pre: any, next: any) => pre + next.width || 100, 0) + 200\n\t\t\tlocalStorage.setItem(props.tableKey || '', JSON.stringify(temp))\n\t\t\t// console.log(currentTableScroll, temp);\n\t\t\tsetCurrentTableScroll({ ...currentTableScroll, x: tableWidth })\n\t\t\tsetCols(temp);\n\t\t};\n\t};\n\tconst customColumns = cols.map((col: any, index: any) => {\n\t\treturn {\n\t\t\t...col,\n\t\t\twidth: col.width || 200,\n\t\t\tonHeaderCell: (column: any) => {\n\t\t\t\treturn {\n\t\t\t\t\twidth: column.width,\n\t\t\t\t\tonResize: handleResize(index),\n\t\t\t\t};\n\t\t\t},\n\t\t};\n\t});\n\tconst [currentTableScroll, setCurrentTableScroll] = useState(props.scroll)\n\tconst { t, i18n } = useTranslation();\n\n\tuseEffect(() => {\n\t\tsetCols(props.columns);\n\t}, [props.columns]);\n\n\tuseEffect(() => {\n\t\tsetCurrentTableScroll(props.scroll);\n\t}, [props.scroll]);\n\n\tuseEffect(() => {\n\t\tif (props.dataSource) {\n\t\t\tconst columns = props.columns.filter((item: any) => ~filterValue.indexOf(item.dataIndex));\n\t\t\thanddleFilterHeader(columns, props.dataSource);\n\t\t}\n\t}, [props.dataSource, props.columns]);\n\n\tconst customizeRenderEmpty = () => (\n\t\t<Row justify=\"center\" align=\"middle\" style={{ height: 360, flexDirection: 'column' }}>\n\t\t\t<img src={emptyImg} style={{ width: 266 }} alt=\"\" />\n\t\t\t<div>{t('暂无数据')}</div>\n\t\t</Row>\n\t);\n\n\tconst handdleFilterHeader = (dataColumns = [], data: any[]) => {\n\t\tconst columns = dataColumns.map((item: any) => item.dataIndex).filter((item: string) => item !== 'handle');\n\t\tconst sheetHeader = dataColumns.map((item: any) => item.title).filter((item: string) => item !== t('操作'));\n\t\tconst tarData: any = [];\n\n\t\tdata.forEach((dataRow: any) => {\n\t\t\tconst row: any = {};\n\t\t\tcolumns.map((colName: string) => {\n\t\t\t\tconst res = dataRow[colName];\n\t\t\t\trow[colName] = res || '';\n\t\t\t});\n\t\t\ttarData.push(row);\n\t\t});\n\n\t\tsetDataFormat({\n\t\t\theader: sheetHeader,\n\t\t\tdata: tarData,\n\t\t});\n\t};\n\n\t// const handleClickOutputExcel = () => {\n\t// \tconst option: any = {};\n\t// \toption.fileName = 'result';\n\t// \toption.datas = [\n\t// \t\t{\n\t// \t\t\tsheetData: dataFormat.data,\n\t// \t\t\tsheetName: 'sheet',\n\t// \t\t\tsheetHeader: dataFormat.header,\n\t// \t\t},\n\t// \t];\n\t// \tconst toExcel = new ExportJsonExcel(option);\n\t// \ttoExcel.saveExcel();\n\t// };\n\n\tconst handleExportJira = () => {\n\t\tconst header = dataFormat.header;\n\t\tconst data = dataFormat.data;\n\t\tlet str = '';\n\t\tif (header.length && data.length) {\n\t\t\tstr =\n\t\t\t\t'|' +\n\t\t\t\theader.join('|') +\n\t\t\t\t'|' +\n\t\t\t\t`\n`;\n\t\t\tdata.forEach((row: any) => {\n\t\t\t\tconst rowKey = Object.values(row).map((item) => {\n\t\t\t\t\tif (item === '') {\n\t\t\t\t\t\treturn ' ';\n\t\t\t\t\t}\n\t\t\t\t\treturn item;\n\t\t\t\t});\n\t\t\t\tstr =\n\t\t\t\t\tstr +\n\t\t\t\t\t'|' +\n\t\t\t\t\trowKey.join('|') +\n\t\t\t\t\t'|' +\n\t\t\t\t\t`\n`;\n\t\t\t});\n\t\t} else {\n\t\t\tstr = '';\n\t\t}\n\n\t\treturn str;\n\t};\n\n\tconst handleExportText = () => {\n\t\tconst header = dataFormat.header;\n\t\tconst data = dataFormat.data;\n\t\tlet str = '';\n\t\tif (header.length && data.length) {\n\t\t\tstr =\n\t\t\t\theader.join('\t') +\n\t\t\t\t`\n`;\n\t\t\tdata.forEach((row: any) => {\n\t\t\t\tconst rowKey = Object.values(row).map((item) => {\n\t\t\t\t\tif (item === '') {\n\t\t\t\t\t\treturn ' ';\n\t\t\t\t\t}\n\t\t\t\t\treturn item;\n\t\t\t\t});\n\t\t\t\tstr =\n\t\t\t\t\tstr +\n\t\t\t\t\trowKey.join('\t') +\n\t\t\t\t\t`\n`;\n\t\t\t});\n\t\t} else {\n\t\t\tstr = '';\n\t\t}\n\t\treturn str;\n\t};\n\n\treturn (\n\t\t<Space className=\"tablebox\" direction=\"vertical\" size=\"middle\">\n\t\t\t<Modal\n\t\t\t\twidth={1000}\n\t\t\t\tmaskClosable={false}\n\t\t\t\tcentered={true}\n\t\t\t\tbodyStyle={{ maxHeight: 500, overflow: 'auto' }}\n\t\t\t\tvisible={exportDataVisible}\n\t\t\t\ttitle={t('导出数据')}\n\t\t\t\tonCancel={() => {\n\t\t\t\t\tsetExportDataVisible(false);\n\t\t\t\t}}\n\t\t\t\tfooter={null}\n\t\t\t>\n\t\t\t\t<div style={{ position: 'relative' }}>\n\t\t\t\t\t<div className=\"mb16\"><span className=\"pr8\">{t('选择需要导出的列')}：</span><Checkbox.Group\n\t\t\t\t\t\toptions={props.columns\n\t\t\t\t\t\t\t.map((item: any) => ({ label: item.title, value: item.dataIndex }))\n\t\t\t\t\t\t\t.filter((item: any) => item.value !== 'handle')}\n\t\t\t\t\t\tdefaultValue={[]}\n\t\t\t\t\t\tvalue={filterValue}\n\t\t\t\t\t\tonChange={(values: any) => {\n\t\t\t\t\t\t\tsetFilterValue(values);\n\t\t\t\t\t\t\tconst columns = props.columns.filter((item: any) => ~values.indexOf(item.dataIndex));\n\t\t\t\t\t\t\thanddleFilterHeader(columns, props.dataSource);\n\t\t\t\t\t\t}}\n\t\t\t\t\t/></div>\n\t\t\t\t\t<div style={{ position: 'absolute', right: 0, bottom: 0 }}>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\ttype=\"link\"\n\t\t\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\t\t\tsetFilterValue(\n\t\t\t\t\t\t\t\t\tprops.columns\n\t\t\t\t\t\t\t\t\t\t.map((item: any) => item.dataIndex)\n\t\t\t\t\t\t\t\t\t\t.filter((item: any) => item !== 'handle'),\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\thanddleFilterHeader(props.columns, props.dataSource);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{t('全选')}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\ttype=\"link\"\n\t\t\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\t\t\tsetFilterValue([]);\n\t\t\t\t\t\t\t\thanddleFilterHeader([], props.dataSource);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{t('反选')}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Tabs>\n\t\t\t\t\t<Tabs.TabPane tab=\"Wiki格式\" key=\"jira\">\n\t\t\t\t\t\t<CopyToClipboard text={handleExportJira()} onCopy={() => message.success(t('已复制到粘贴板'))}>\n\t\t\t\t\t\t\t<pre style={{ cursor: 'pointer', minHeight: 100 }}>\n\t\t\t\t\t\t\t\t<code>{handleExportJira()}</code>\n\t\t\t\t\t\t\t</pre>\n\t\t\t\t\t\t</CopyToClipboard>\n\t\t\t\t\t</Tabs.TabPane>\n\t\t\t\t\t<Tabs.TabPane tab=\"Text格式\" key=\"test\">\n\t\t\t\t\t\t<CopyToClipboard text={handleExportText()} onCopy={() => message.success(t('已复制到粘贴板'))}>\n\t\t\t\t\t\t\t<pre style={{ cursor: 'pointer', minHeight: 100 }}>\n\t\t\t\t\t\t\t\t<code>{handleExportText()}</code>\n\t\t\t\t\t\t\t</pre>\n\t\t\t\t\t\t</CopyToClipboard>\n\t\t\t\t\t</Tabs.TabPane>\n\t\t\t\t\t{/* <Tabs.TabPane tab=\"Excel格式\" key=\"excel\">\n\t\t\t\t\t\t<Row justify=\"center\" align=\"middle\" style={{ minHeight: 100 }}>\n\t\t\t\t\t\t\t<Col>\n\t\t\t\t\t\t\t\t<Button type=\"primary\" onClick={handleClickOutputExcel}>\n\t\t\t\t\t\t\t\t\t导出Excel\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t</Row>\n\t\t\t\t\t</Tabs.TabPane> */}\n\t\t\t\t</Tabs>\n\t\t\t</Modal>\n\t\t\t{\n\t\t\t\tprops.titleNode || props.buttonNode || !props.cancelExportData ? <Row justify=\"space-between\" align=\"middle\">\n\t\t\t\t\t<Col>\n\t\t\t\t\t\t<Space align=\"center\">{props.titleNode}</Space>\n\t\t\t\t\t</Col>\n\t\t\t\t\t<Col>\n\t\t\t\t\t\t<Space align=\"center\">\n\t\t\t\t\t\t\t{props.buttonNode}\n\t\t\t\t\t\t\t{props.cancelExportData ? null : (\n\t\t\t\t\t\t\t\t<Button style={{ marginLeft: 6 }} onClick={() => setExportDataVisible(true)}>\n\t\t\t\t\t\t\t\t\t{t('导出数据')}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t</Space>\n\t\t\t\t\t</Col>\n\t\t\t\t</Row> : null\n\t\t\t}\n\t\t\t<ConfigProvider renderEmpty={customizeRenderEmpty}>\n\t\t\t\t<Table\n\t\t\t\t\tsize={props.size || 'middle'}\n\t\t\t\t\trowKey={props.rowKey ? props.rowKey : 'id'}\n\t\t\t\t\tdataSource={props.dataSource}\n\t\t\t\t\t// columns={props.columns}\n\t\t\t\t\tcomponents={{ header: { cell: ResizableTitle } }}\n\t\t\t\t\tcolumns={customColumns}\n\t\t\t\t\tpagination={props.pagination !== false ? { ...props.pagination } : false}\n\t\t\t\t\tscroll={currentTableScroll}\n\t\t\t\t\tloading={props.loading}\n\t\t\t\t\tonChange={props.onChange}\n\t\t\t\t\trowSelection={props.rowSelection}\n\t\t\t\t/>\n\t\t\t</ConfigProvider>\n\t\t</Space>\n\t);\n};\n\nexport default TableBox;\n", "import React from 'react'\nimport './Loading.less';\n\nexport default function Loading() {\n    return (\n        <div className=\"dna p-a\" style={{ left: -180 }}>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n            <div className=\"ele\">\n                <div className=\"dot\"></div>\n            </div>\n        </div>\n    )\n}\n", "import * as d3 from 'd3';\nimport d3TipF from 'd3-tip';\nconst d3Tip: any = d3TipF;\n\nexport interface ID3ToolParams {\n\tcontainerId: string;\n\tmainViewId: string;\n\tmargin?: number;\n}\n\nexport interface IRect {\n\tcenter: number[]\n\tcoordList: Array<{ x: number, y: number }>\n\theight: number\n\twidth: number\n\tx0: number\n\tx1: number\n\tx2: number\n\tx3: number\n\ty0: number\n\ty1: number\n\ty2: number\n\ty3: number\n}\n\ntype TD3NodeDom = d3.Selection<SVGGElement, unknown, HTMLElement, any>;\n\nexport default class D3Tool {\n\tpublic container: HTMLElement;\n\tpublic scaleView: TD3NodeDom;\n\tpublic mainView: TD3NodeDom;\n\tpublic gNode: TD3NodeDom;\n\tpublic gLink: TD3NodeDom;\n\tpublic gText: TD3NodeDom;\n\tpublic gBorder: TD3NodeDom;\n\tpublic gTextCache: TD3NodeDom;\n\tpublic innerWidth: number;\n\tpublic innerHeight: number;\n\tpublic tip: any;\n\tpublic fontSize = 14;\n\tpublic zoom: d3.ZoomBehavior<any, any>;\n\tpublic currentNode: any;\n\tpublic margin: number;\n\n\tconstructor({ containerId, mainViewId, margin = 0 }: ID3ToolParams) {\n\t\tconst _selfThis = this;\n\n\t\tthis.margin = margin;\n\t\tthis.container = document.getElementById(containerId) as HTMLElement;\n\t\tthis.mainView = d3.select(`#${mainViewId}`);\n\t\tthis.scaleView = this.mainView.append('g').attr('id', 'scaleView');\n\t\tthis.scaleView.attr('style', 'transition:all .3s');\n\n\t\tconst marginObj = { top: margin, right: margin, bottom: margin, left: margin };\n\t\tconst innerWidth = this.container.scrollWidth - marginObj.top - marginObj.bottom;\n\t\tconst innerHeight = this.container.scrollHeight - marginObj.left - marginObj.right;\n\t\t// 设置视图区域大小\n\t\tthis.mainView\n\t\t\t.attr('width', innerWidth)\n\t\t\t.attr('height', innerHeight)\n\t\t\t.attr('viewBox', `0 0 ${innerWidth} ${innerHeight}`)\n\t\t\t.attr('transform', `translate(${marginObj.left}, ${marginObj.top})`)\n\t\t// .attr('transition', 'all .3s');\n\t\t// .on('click', () => {\n\t\t// \tthis.tip.hide();\n\t\t// });\n\t\tthis.gTextCache = this.mainView.append('g').attr('id', 'gTextCache');\n\t\tthis.gBorder = this.scaleView.append('g').attr('id', 'gBorder');\n\t\tthis.gText = this.scaleView.append('g').attr('id', 'gText');\n\t\tthis.gLink = this.scaleView.append('g').attr('id', 'gLink');\n\t\tthis.gNode = this.scaleView\n\t\t\t.append('g')\n\t\t\t.attr('id', 'gNode')\n\t\t\t.attr('cursor', 'pointer')\n\t\t\t.attr('pointer-events', 'all')\n\t\t\t.attr(\"transform\", `translate(0 ${innerHeight * 0.2}) scale(.5,.5)`);\n\t\t// 设置箭头工具\n\t\tconst defs = this.scaleView.append('defs').attr('id', 'gDefs');\n\t\tdefs.append('marker')\n\t\t\t.attr('id', 'arrowRight')\n\t\t\t.attr('viewBox', '-10 -10 20 20')\n\t\t\t.attr('refX', 10)\n\t\t\t.attr('refY', 0)\n\t\t\t.attr('markerWidth', 6)\n\t\t\t.attr('markerHeight', 6)\n\t\t\t.attr('orient', 'auto')\n\t\t\t.append('path')\n\t\t\t.attr('d', 'M -10,-10 L 10,0 L -10,10')\n\t\t\t.attr('fill', '#cdcdcd')\n\t\t\t.attr('stroke-width', 2)\n\t\t\t.attr('stroke', '#cdcdcd');\n\t\t// 创建提示插件\n\t\tconst tip = d3Tip();\n\t\ttip.attr('class', 'd3-tip').html(function (d: any) {\n\t\t\treturn d;\n\t\t});\n\t\tthis.tip = tip;\n\t\tthis.mainView.call(tip);\n\t\t// 创建缩放插件\n\t\tconst zoom = d3\n\t\t\t.zoom()\n\t\t\t.scaleExtent([0.1, 2])\n\t\t\t// .scale(2)\n\t\t\t// .on(\"dblclick\", null)\n\t\t\t.on('zoom', (e: any) => {\n\t\t\t\t// console.log('graphviz zoomed with event:', e);\n\t\t\t\t// fix\n\t\t\t\t// const transform = d3.event.transform;\n\t\t\t\tconst transform = e.transform\n\t\t\t\t_selfThis.scaleView.attr('style', 'transition:all 0s').attr('transform', transform);\n\t\t\t\t_selfThis.tip.hide()\n\t\t\t}).on(\"end\", () => {\n\t\t\t\t_selfThis.scaleView.attr('style', 'transition:all 0.3s')\n\t\t\t}) as d3.ZoomBehavior<any, any>;\n\n\t\tthis.zoom = zoom;\n\t\tthis.mainView\n\t\t\t.call(zoom as any)\n\t\t\t.call(zoom.transform, d3.zoomIdentity.translate(0, 0).scale(1))\n\t\t\t.on(\"dblclick.zoom\", null);\n\n\t\tthis.innerHeight = innerHeight;\n\t\tthis.innerWidth = innerWidth;\n\t}\n\n\t/**\n\t *重新调整画布尺寸\n\t *\n\t * @memberof CostMap\n\t */\n\tpublic reSize() {\n\t\tthis.mainView.attr('width', 0).attr('height', 0).attr('viewBox', `0 0 ${0} ${0}`);\n\n\t\tconst marginObj = { top: this.margin, right: this.margin, bottom: this.margin, left: this.margin };\n\t\tconst innerWidth = this.container.scrollWidth - marginObj.top - marginObj.bottom;\n\t\tconst innerHeight = this.container.scrollHeight - marginObj.left - marginObj.right;\n\n\t\tconsole.log(this.container.scrollWidth, this.container.scrollHeight);\n\n\t\tthis.mainView\n\t\t\t.attr('width', innerWidth)\n\t\t\t.attr('height', innerHeight)\n\t\t\t.attr('viewBox', `0 0 ${innerWidth} ${innerHeight}`);\n\n\t\tthis.innerHeight = innerHeight;\n\t\tthis.innerWidth = innerWidth;\n\t}\n\n\t/**\n\t *获取文字rect dom\n\t *\n\t * @param {string} textStr\n\t * @param {number} [fontSize=14]\n\t * @returns {{ width: number; height: number }}\n\t * @memberof CostMap\n\t */\n\tpublic getTextRect(textStr: string, fontSize = 14): { width: number; height: number } {\n\t\tconst textDom = this.gTextCache.append('text').text(textStr).attr('font-size', `${fontSize}px`);\n\t\tconst textBox = textDom.node()?.getBBox();\n\t\tconst rect = {\n\t\t\twidth: textBox?.width || 0,\n\t\t\theight: textBox?.height || 0,\n\t\t};\n\t\ttextDom.remove();\n\t\treturn rect;\n\t}\n\n\t/**\n\t * 列表转树\n\t * @param data \n\t */\n\tpublic list2Tree = <T>(data: Array<T>): Array<T> => {\n\t\tconst res: T[] = [];\n\t\tconst withoutChildrenDataList = data.map((item: any) => {\n\t\t\tif (item.children) {\n\t\t\t\tdelete item.children;\n\t\t\t}\n\t\t\treturn item;\n\t\t});\n\t\tconst dataMap = this.list2Map(withoutChildrenDataList, 'ci_id');\n\t\tlet root: any;\n\t\tfor (let i = 0; i < withoutChildrenDataList.length; i++) {\n\t\t\tconst item = withoutChildrenDataList[i];\n\t\t\tif (item.parentId !== undefined) {\n\t\t\t\tconst parent = dataMap.get(item.parentId);\n\t\t\t\tif (parent.children) {\n\t\t\t\t\tparent.children.push(item);\n\t\t\t\t} else {\n\t\t\t\t\tparent.children = [item];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\troot = dataMap.get(item.ci_id);\n\t\t\t}\n\t\t}\n\t\tres.push(root);\n\t\treturn res;\n\t};\n\n\t/**\n\t * 树形结构扁平化\n\t * @param data \n\t * @param key \n\t */\n\tpublic tree2List = <T>(data: Array<T>, key = 'children'): Array<T> => {\n\t\tconst quene = [...data];\n\t\tconst res: T[] = [];\n\t\twhile (quene.length) {\n\t\t\tconst item: any = quene.shift();\n\t\t\tres.push(item);\n\t\t\tif (item && item[key]) {\n\t\t\t\tquene.push(...item[key]);\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t};\n\n\t/**\n\t * 列表转字典\n\t * @param list \n\t * @param key \n\t * @param isReplace \n\t */\n\tpublic list2Map = <T>(list: T[], key: string, isReplace = false): Map<string, T> => {\n\t\tconst map: Map<string, T> = new Map();\n\t\tfor (let i = 0; i < list.length; i++) {\n\t\t\tconst item: any = list[i];\n\t\t\tif (item && item[key] && (isReplace || !map.get(item[key]))) {\n\t\t\t\tmap.set(item[key], item);\n\t\t\t}\n\t\t}\n\t\treturn map;\n\t};\n\n\t/**\n\t * 树型结构剪枝\n\t * @param data \n\t * @param key \n\t * @param id \n\t */\n\tpublic treeCutNode = <T>(data: T[], key = 'children', matchKey = 'id', id?: string): T[] => {\n\t\tif (id === undefined) return data\n\n\t\tconst dfs = <T>(data: T[]): T[] => {\n\t\t\tconst res: T[] = []\n\t\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\t\tconst item: any = data[i];\n\t\t\t\tconst tarItem = { ...item }\n\t\t\t\tif (item[key] && item[key].length) {\n\t\t\t\t\tif (item[matchKey] === id) {\n\t\t\t\t\t\ttarItem[key] = []\n\t\t\t\t\t\tres.push(tarItem)\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarItem[key] = dfs(item[key])\n\t\t\t\t\t\tres.push(tarItem)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tres.push(tarItem)\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn res\n\t\t}\n\n\t\tconst treeCutAfter = dfs(data)\n\t\treturn treeCutAfter\n\t}\n\n\tpublic parse2Rectangle = (coords: string = '0,0 2,0 2,2 0,2') => {\n\t\tconst coordList = coords.split(' ').map(coord => {\n\t\t\tconst [x, y] = coord.split(',')\n\t\t\treturn { x: +x, y: +y }\n\t\t})\n\t\tconst rectSourceObj: any = coordList.reduce((pre, next, currentIndex) => {\n\t\t\tlet tar: any = { ...pre }\n\t\t\ttar[`x${currentIndex}`] = next.x\n\t\t\ttar[`y${currentIndex}`] = next.y\n\t\t\treturn tar\n\t\t}, {})\n\t\tconst width = rectSourceObj.x1 - rectSourceObj.x0\n\t\tconst height = rectSourceObj.y3 - rectSourceObj.y0\n\t\tconst res: IRect = {\n\t\t\tcoordList,\n\t\t\t...rectSourceObj,\n\t\t\twidth: Math.abs(width),\n\t\t\theight: Math.abs(height),\n\t\t\tcenter: [width / 2, height / 2]\n\t\t}\n\t\treturn res\n\t}\n}\n", "/* eslint-disable @typescript-eslint/no-invalid-this */\n/* eslint-disable @typescript-eslint/no-this-alias */\nimport * as d3 from 'd3';\nimport D3Tool, { ID3ToolParams } from './D3Tool';\nimport { graphviz } from 'd3-graphviz';\nimport React from 'react';\nimport { INodeItem } from './TreePlusInterface';\n// 防止被treeShaking\nconst graphvizName = graphviz.name;\n\ninterface IThemeColor {\n\tbackground: string;\n\tcolor: string;\n\tborder: string;\n\tactiveColor?: string;\n\tactiveBackground?: string;\n\tdisabled?: string\n\tdisabledColor?: string\n}\n\ninterface IPreHandleNode extends INodeItem {\n\trelativeKey: 'parent' | 'children'\n\tkey: string\n\tlevel: number;\n\tchildren: IPreHandleNode[],\n\tparent: IPreHandleNode[]\n\tcollectNum?: number\n\tcollectionNodes?: IPreHandleNode[]\n\texpandsNumParent?: number\n\tcollectionParent?: IPreHandleNode[]\n\texpandsNumChildren?: number\n\tcollectionChildren?: IPreHandleNode[]\n\tisCollectionNode?: boolean\n\tdata_fields: string\n}\n\ninterface IRenderNode {\n\tid: string | number;\n\tx: string | number;\n\ty: string | number;\n\trenderId: string | number;\n\tdata?: IPreHandleNode\n\ttheme: IThemeColor\n\trenderInfo?: any\n}\n\nconst NodeTypeList = ['ROOT', 'BUSINESS', 'THEME', 'COLLECT']\n\nconst ThemeColor: IThemeColor[] = [\n\t{ background: '#00d4001a', color: '#00d400', border: '#00d400', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#00d4c81a', color: '#00d4c8', border: '#00d4c8', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#0000d41a', color: '#0000d4', border: '#0000d4', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#c800d41a', color: '#c800d4', border: '#c800d4', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#d464001a', color: '#d46400', border: '#d46400', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\n\t{ background: '#96d4001a', color: '#96d400', border: '#96d400', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#00d4961a', color: '#00d496', border: '#00d496', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#0096d41a', color: '#0096d4', border: '#0096d4', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#6400d41a', color: '#6400d4', border: '#6400d4', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#d400001a', color: '#d40000', border: '#d40000', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n\t{ background: '#d4c8001a', color: '#d4c800', border: '#d4c800', activeColor: '#0078d4', disabled: '#cdcdcd1a', disabledColor: '#cdcdcd' },\n];\n\nconst nodeTypeThemeMap: Record<string, IThemeColor> = NodeTypeList.reduce((pre, next, index) => ({ ...pre, [next]: ThemeColor[index % ThemeColor.length] }), {})\n\ninterface IRelationDiagramProps extends ID3ToolParams {\n\tisCollection?: boolean\n}\n\nexport default class RelationDiagram extends D3Tool {\n\n\tpublic dataMap = new Map<string, IPreHandleNode>();\n\tpublic dataMapByName = new Map<string, IPreHandleNode>();\n\tpublic renderNodesMap = new Map<string, IRenderNode>();\n\tpublic nodesInGraphMap = new Map<string, IPreHandleNode>();\n\tpublic nodesInCollectionMap = new Map<string, IPreHandleNode>();\n\tpublic dataNodes: IPreHandleNode[] = [];\n\tpublic activeNodeId?: string;\n\tpublic rootNode?: IPreHandleNode\n\tpublic handleNodeClick?: ((node: any) => void);\n\tpublic loadingStart?: (() => void);\n\tpublic loadingEnd?: (() => void);\n\tprivate isCollection?: boolean\n\n\tconstructor({ containerId, mainViewId, margin = 0, isCollection }: IRelationDiagramProps) {\n\t\tsuper({ containerId, mainViewId, margin });\n\t\tthis.isCollection = !!isCollection\n\t}\n\n\tpublic htmlStrEnCode = (str: string) => {\n\t\tconst res = `${str}`.replace(/[\\u00A0-\\u9999<>\\-\\&\\:]/g, function (i) {\n\t\t\treturn '&#' + i.charCodeAt(0) + ';';\n\t\t})\n\t\treturn res\n\t}\n\n\tpublic enCodeNodeId(id: string) {\n\t\treturn `node_${id.split('').map(item => item.charCodeAt(0)).join('')}`\n\t}\n\n\tpublic deCodeNodeId(id: string) {\n\t\tconst sourceId = id.replace(/^node_/, '').replaceAll('_rep_', ':').replaceAll('_mid_', '-')\n\t\treturn this.htmlStrEnCode(sourceId)\n\t}\n\n\n\tpublic preHandleNodes(nodes: INodeItem[], relativeKey: 'children' | 'parent' = 'children'): IPreHandleNode[] {\n\t\tconst nodesMapByKey = new Map<string | number, IPreHandleNode>()\n\t\tconst nodesMapById = new Map<string | number, IPreHandleNode>()\n\t\t// const rootId = this.enCodeNodeId((nodes[0] || {}).nid || '')\n\t\tconst rootIds = nodes.map((node, index) => `node_${relativeKey}_${index}`)\n\t\tconst childrenKey = relativeKey\n\t\tconst parentKey = relativeKey === 'children' ? 'parent' : 'children'\n\n\t\t// 处理树结构上的每一个节点\n\t\tconst dfs = (nodes: INodeItem[], level = 0, upItem?: IPreHandleNode, idPath: string[] = []): IPreHandleNode[] => {\n\t\t\tconst res: IPreHandleNode[] = [];\n\n\t\t\tfor (let i = 0; i < nodes.length; i++) {\n\t\t\t\tconst node = nodes[i];\n\t\t\t\tidPath.push(`${i}`)\n\t\t\t\tconst encodeKey = this.htmlStrEnCode(node.nid)\n\t\t\t\tlet key = `node_${relativeKey}_${idPath.join('_')}`\n\n\t\t\t\tconst nodeCacheById = nodesMapById.get(node.nid)\n\t\t\t\tif (nodeCacheById) {\n\t\t\t\t\tkey = nodeCacheById.key\n\t\t\t\t}\n\t\t\t\tconst nodeCache = nodesMapByKey.get(key)\n\n\t\t\t\tlet tarNode: IPreHandleNode = {\n\t\t\t\t\t...node,\n\t\t\t\t\tkey,\n\t\t\t\t\tlevel,\n\t\t\t\t\trelativeKey,\n\t\t\t\t\t// 构建双向链表结构\n\t\t\t\t\t[parentKey]: upItem ? [upItem] : [],\n\t\t\t\t\t[childrenKey]: []\n\t\t\t\t} as IPreHandleNode\n\n\t\t\t\t// 处理已经遍历过得情况\n\t\t\t\tif (nodeCache) {\n\t\t\t\t\tconst flag = nodeCache[parentKey].map(node => node.key).includes(upItem?.key || '')\n\t\t\t\t\t// const flag = false\n\t\t\t\t\tif (flag) {\n\t\t\t\t\t\ttarNode = nodeCache\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarNode = {\n\t\t\t\t\t\t\t...nodeCache,\n\t\t\t\t\t\t\t// 构建双向链表结构\n\t\t\t\t\t\t\t[parentKey]: [upItem, ...nodeCache[parentKey]],\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (node[childrenKey] && node[childrenKey]?.length) {\n\t\t\t\t\tdfs(node[childrenKey] || [], level + 1, tarNode, idPath)\n\t\t\t\t}\n\n\t\t\t\tnodesMapByKey.set(tarNode.key, tarNode)\n\t\t\t\tnodesMapById.set(tarNode.nid, tarNode)\n\t\t\t\tres.push(tarNode);\n\n\t\t\t\tidPath.pop()\n\t\t\t}\n\t\t\treturn res;\n\t\t};\n\n\t\tdfs(nodes)\n\n\t\t// 节点重构建\n\t\tnodesMapByKey.forEach(item => {\n\t\t\tconst currentItemList = item[parentKey]\n\t\t\tfor (let i = 0; i < currentItemList.length; i++) {\n\t\t\t\tconst currentItem = currentItemList[i];\n\t\t\t\tconst itemId = currentItem.key\n\t\t\t\tconst tarItem = nodesMapByKey.get(itemId)\n\t\t\t\tif (tarItem) {\n\t\t\t\t\ttarItem[childrenKey].push(item)\n\t\t\t\t\tnodesMapByKey.set(itemId, tarItem)\n\t\t\t\t}\n\t\t\t}\n\t\t\t// 更新当前节点关系\n\t\t\titem[parentKey] = item[parentKey].map(node => nodesMapByKey.get(node.key)) as IPreHandleNode[]\n\t\t\tnodesMapByKey.set(item.key, item)\n\t\t})\n\n\t\tconst rootNode = rootIds.map(rootId => nodesMapByKey.get(rootId) as IPreHandleNode)\n\t\tconst res = rootNode ? [...rootNode] : []\n\n\t\treturn res;\n\t}\n\n\tpublic initData<T extends INodeItem>(data: T[]) {\n\t\t// 初始化\n\t\tthis.nodesInCollectionMap = new Map()\n\t\tthis.dataNodes = []\n\n\t\tconsole.log('data', data);\n\n\t\t// 这一步之后已经构建了完整的链路关系\n\t\tconst preHandlePreData: IPreHandleNode[] = this.preHandleNodes(data, 'parent');\n\t\tconst preHandleNextData: IPreHandleNode[] = this.preHandleNodes(data, 'children');\n\n\t\tconsole.log('preHandleNextData', preHandleNextData);\n\n\t\t// if (this.isCollection) {\n\t\t// \tthis.handleCollectionNodes(preHandlePreData, 'parent')\n\t\t// \tthis.handleCollectionNodes(preHandleNextData, 'children')\n\t\t// }\n\n\t\t// 合并根节点\n\t\t// const [preRoot] = preHandlePreData\n\t\t// const [nextRoot] = preHandleNextData\n\t\t// if (preRoot && nextRoot) {\n\t\t// \tpreHandlePreData[0].key = `node_0`\n\t\t// \tpreHandlePreData[0].children = nextRoot.children\n\t\t// \tpreHandlePreData[0].expandsNumChildren = nextRoot.expandsNumChildren\n\t\t// \tpreHandlePreData[0].collectionChildren = nextRoot.collectionChildren\n\n\t\t// \tpreHandleNextData[0].key = `node_0`\n\t\t// \tpreHandleNextData[0].parent = preRoot.parent\n\t\t// \tpreHandleNextData[0].expandsNumParent = preRoot.expandsNumParent\n\t\t// \tpreHandleNextData[0].collectionParent = preRoot.collectionParent\n\t\t// }\n\n\t\t// 合并根节点\n\t\tfor (let i = 0; i < preHandleNextData.length; i++) {\n\t\t\tpreHandleNextData[i].key = `node_${i}`;\n\t\t\tpreHandleNextData[i].parent = preHandlePreData[i].parent\n\t\t}\n\n\t\tconst rootNode = preHandleNextData[0];\n\t\tconst preHandleData = preHandleNextData\n\t\tthis.rootNode = rootNode;\n\n\t\tconst preRenderData = this.preRenderDataReady(preHandleData)\n\n\t\tconsole.log('preRenderData', preRenderData);\n\n\t\tthis.renderNode(preRenderData).then(() => {\n\t\t\t// if (rootNode) {\n\t\t\t// \tthis.anchorNode(rootNode.key);\n\t\t\t// }\n\t\t\tthis.centerApp()\n\t\t})\n\t}\n\n\tpublic preRenderDataReady(nodes?: IPreHandleNode[]) {\n\n\t\tif (nodes?.length) {\n\t\t\t// 扁平化\n\t\t\tconst preData = this.tree2List(nodes, 'parent');\n\t\t\tconst nextData = this.tree2List(nodes, 'children');\n\t\t\tconst targetData = [...preData, ...nextData];\n\n\t\t\t// 构建图的Map\n\t\t\tconst targetDataMap = this.list2Map(targetData, 'key');\n\t\t\tconst targetDataMapByName = this.list2Map(targetData, 'node_name');\n\t\t\tconst dataNodes: IPreHandleNode[] = [];\n\t\t\ttargetDataMap.forEach((item) => {\n\t\t\t\tdataNodes.push(item);\n\t\t\t});\n\t\t\tthis.dataMap = targetDataMap;\n\t\t\tthis.dataMapByName = targetDataMapByName;\n\t\t\tthis.dataNodes = dataNodes;\n\t\t} else {\n\t\t\tthis.dataNodes = []\n\t\t}\n\t\treturn this.dataNodes\n\t}\n\n\t/**\n\t * 构造渲染节点\n\t * @param nodes \n\t */\n\tpublic createRenderNodes(nodes: IPreHandleNode[], isDisable?: boolean): string[] {\n\n\t\tconst res = nodes.map((node) => {\n\t\t\tconst isInCollection = this.nodesInCollectionMap.get(node.key)\n\t\t\tif (!isInCollection) {\n\t\t\t\tif (node.data_fields === 'COLLECT') {\n\t\t\t\t\treturn `${node['key']}\n\t\t\t\t\t[label=\"聚合节点，剩余${node.collectNum}个节点(双击展开) + \",\n\t\t\t\t\t\tshape=box,\n\t\t\t\t\t\tstyle=dashed,\n\t\t\t\t\t\tmargin=0,\n\t\t\t\t\t\tid=${node.key}\n\t\t\t\t\t];`;\n\t\t\t\t}\n\n\t\t\t\treturn `${node['key']}\n\t\t\t\t\t[label=\"占位符占位符占位${node.key}\",\n\t\t\t\t\t\tshape=box,\n\t\t\t\t\t\twidth=8,\n\t\t\t\t\t\theight=0.8,\n\t\t\t\t\t\tmargin=0,\n\t\t\t\t\t\tid=${node.key}\n\t\t\t\t\t];`;\n\t\t\t} else {\n\t\t\t\treturn ''\n\t\t\t}\n\t\t});\n\t\treturn res.filter(item => !!item)\n\t}\n\n\t/**\n\t * 构造渲染关系（边）\n\t * @param nodes \n\t */\n\tpublic cerateRenderNodesRelation(nodes: IPreHandleNode[]) {\n\t\tconst res: string[] = [];\n\t\tnodes.forEach((node) => {\n\t\t\tconst { parent, children } = node;\n\n\t\t\t(parent || []).forEach((parent: any) => {\n\t\t\t\tconst isInCollection = this.nodesInCollectionMap.get(node.key) || this.nodesInCollectionMap.get(parent.key)\n\t\t\t\tif (!isInCollection) {\n\t\t\t\t\tconst tar = `\n\t\t\t\t\t${(parent.key)}->${(node.key)} [id=\"edgePre_${(parent.key)}_edge_${(node.key)}\"];`;\n\t\t\t\t\tif (res.indexOf(tar) === -1) {\n\t\t\t\t\t\tres.push(tar);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\t(children || []).forEach((child: any) => {\n\t\t\t\tconst isInCollection = this.nodesInCollectionMap.get(node.key) || this.nodesInCollectionMap.get(child.key)\n\t\t\t\tif (!isInCollection) {\n\t\t\t\t\tconst tar = `\n\t\t\t\t\t${(node.key)}->${(child.key)} [id=\"edgePre_${(node.key)}_edge_${(child.key)}\"];`;\n\t\t\t\t\tif (res.indexOf(tar) === -1) {\n\t\t\t\t\t\tres.push(tar);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t\treturn res;\n\t}\n\n\t/**\n\t * 渲染后处理，事件绑定等等\n\t */\n\tpublic backRenderHandle() {\n\t\tconst _selfThis = this;\n\t\tconst renderNodesMap = new Map<string, IRenderNode>();\n\t\t// 去掉多余的提示信息\n\t\td3.selectAll('title').remove()\n\n\t\td3.selectAll('.node').each((item: any) => {\n\t\t\ttry {\n\t\t\t\tconst key = item.key;\n\t\t\t\tconst nodeData = this.dataMap?.get(key)\n\t\t\t\tconst currentColorTheme = nodeTypeThemeMap[nodeData?.data_fields || ''] || ThemeColor[0]\n\t\t\t\tconst box: any = d3.selectAll(`#${key} polygon`).datum();\n\t\t\t\tconst tar = {\n\t\t\t\t\trenderInfo: item,\n\t\t\t\t\trenderId: key,\n\t\t\t\t\tdata: nodeData,\n\t\t\t\t\ttheme: currentColorTheme,\n\t\t\t\t\tid: key,\n\t\t\t\t\tx: box.center.x,\n\t\t\t\t\ty: box.center.y,\n\t\t\t\t};\n\t\t\t\trenderNodesMap.set(key, tar);\n\n\t\t\t\td3.selectAll(`#${key} text[fill=\"#000000\"]`).attr('type', `mainText`);\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.log(error);\n\t\t\t}\n\n\t\t\t// 调试位置坐标\n\t\t\t// d3.selectAll(`#${nodeId}`)\n\t\t\t// \t.append('g')\n\t\t\t// \t.append('text')\n\t\t\t// \t.text(`${box.attributes.x},${box.attributes.y}`)\n\t\t\t// \t.attr('fill', '#ff0000')\n\t\t\t// \t.attr('x', box.attributes.x)\n\t\t\t// \t.attr('y', box.attributes.y + 300);\n\t\t});\n\n\t\tthis.renderNodesMap = renderNodesMap;\n\n\t\tthis.beautifulNode()\n\n\t\tlet tipsContent: JSX.Element;\n\n\t\t// d3.selectAll('.node')\n\t\t// \t.on('mouseenter', function (node: any, d: any) {\n\t\t// \t\tconst key = node.key;\n\t\t// \t\tconst curNode = _selfThis.dataMap?.get(Number(key));\n\t\t// \t\ttipsContent = (\n\t\t// \t\t\t<div>\n\t\t// \t\t\t\t<div className=\"pb12 d-f jc-b ac fs16\">\n\t\t// \t\t\t\t\t<strong>详情</strong>\n\t\t// \t\t\t\t</div>\n\t\t// \t\t\t\t<div>{123}</div>\n\t\t// \t\t\t</div>\n\t\t// \t\t);\n\t\t// \t\t_selfThis.tip\n\t\t// \t\t\t.offset([0, 0])\n\t\t// \t\t\t.show(ReactDOMServer.renderToString(tipsContent), this);\n\t\t// \t})\n\t\t// \t.on('mouseleave', function (node: any, d: any) {\n\t\t// \t\t_selfThis.tip.hide();\n\t\t// \t});\n\n\t\t// d3.select('.d3-tip')\n\t\t// \t.on('mouseenter', function (node: any, d: any) {\n\t\t// \t\t_selfThis.tip.show(ReactDOMServer.renderToString(tipsContent));\n\t\t// \t})\n\t\t// \t.on('mouseleave', function (node: any, d: any) {\n\t\t// \t\t_selfThis.tip.hide();\n\t\t// \t});\n\n\n\t\t// 区分单双击事件\n\t\tlet timeout: any = null;\n\t\td3.selectAll('.node[type=\"collect\"]')\n\t\t\t.on('click', function (node: any, d: any) {\n\t\t\t\tclearTimeout(timeout);\n\n\t\t\t\ttimeout = setTimeout(function () {\n\t\t\t\t\t_selfThis.handleNodeClick && _selfThis.handleNodeClick(node)\n\t\t\t\t}, 200)\n\t\t\t})\n\t\t// .on('dblclick', function (node: any, d: any) {\n\t\t// \tclearTimeout(timeout);\n\n\t\t// \t_selfThis.handleCollectExpand(node.key)\n\n\t\t// \tconst rootNode = _selfThis.dataMap?.get(_selfThis.rootNode?.key || '') as IPreHandleNode\n\t\t// \tconst preRenderData = _selfThis.preRenderDataReady(rootNode)\n\t\t// \t_selfThis.renderNode(preRenderData).then(() => { })\n\t\t// })\n\n\t\tconst d3MainView = document.getElementById('d3MainView')\n\t\tif (d3MainView) {\n\t\t\td3MainView.onclick = (e: any) => {\n\t\t\t\tlet isNode = false\n\t\t\t\tfor (let i = 0; i < e.path.length; i++) {\n\t\t\t\t\tconst elem = e.path[i];\n\t\t\t\t\tif (elem.id && ~elem.id.indexOf('node_')) {\n\t\t\t\t\t\tisNode = true\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!isNode) {\n\t\t\t\t\t_selfThis.refresh()\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic beautifulNode() {\n\t\tconst _selfThis = this\n\t\tconst boxs = d3.selectAll('.node polygon').data()\n\n\t\tconsole.log('boxs', boxs);\n\n\t\tboxs.forEach((item: any) => {\n\t\t\tconst box = item.bbox\n\t\t\tconst pid = item.parent.key\n\t\t\tconst nodeData = this.dataMap?.get(pid)\n\n\t\t\tif (nodeData) {\n\t\t\t\td3.select(`#${pid}`).remove()\n\t\t\t\td3.select('#mainGroup')\n\t\t\t\t\t.append('g')\n\t\t\t\t\t.attr('id', pid)\n\t\t\t\t\t.attr('class', 'node')\n\t\t\t\t\t.attr('type', 'normal')\n\t\t\t\t\t.on('click', function (node: any, d: any) {\n\t\t\t\t\t\tconst dataNode = _selfThis.renderNodesMap?.get(this.id)\n\t\t\t\t\t\tif (dataNode) {\n\t\t\t\t\t\t\t_selfThis.highlightRelation(dataNode)\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_selfThis.handleNodeClick && _selfThis.handleNodeClick(dataNode?.data)\n\t\t\t\t\t})\n\t\t\t\t\t.append('rect')\n\t\t\t\t\t.attr('id', `rect_${pid}`)\n\t\t\t\t\t.attr('x', box.x)\n\t\t\t\t\t.attr('y', box.y)\n\t\t\t\t\t.attr('rx', box.height / 2)\n\t\t\t\t\t.attr('ry', box.height / 2)\n\t\t\t\t\t.attr('width', box.width)\n\t\t\t\t\t.attr('height', box.height)\n\t\t\t\t\t.attr('fill', '#fff')\n\t\t\t\t\t.attr('stroke', '#cdcdcd')\n\t\t\t\t\t.attr('stroke-width', 1)\n\t\t\t\t\t.attr('style', 'transition:all 0.3s;')\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.append('path')\n\t\t\t\t\t.attr('d', `M${box.x} ${box.cy} L${box.x + box.width * 2 / 3} ${box.cy}`)\n\t\t\t\t\t.attr('stroke', '#cdcdcd')\n\t\t\t\t\t.attr('stroke-dasharray', '5,5')\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.append('rect')\n\t\t\t\t\t.attr('class', 'rectBg')\n\t\t\t\t\t.attr('x', box.x)\n\t\t\t\t\t.attr('y', box.y)\n\t\t\t\t\t.attr('rx', box.height / 2)\n\t\t\t\t\t.attr('ry', box.height / 2)\n\t\t\t\t\t.attr('width', box.height)\n\t\t\t\t\t.attr('height', box.height)\n\t\t\t\t\t.attr('fill', nodeData.color)\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.append('rect')\n\t\t\t\t\t.attr('class', 'rectBg')\n\t\t\t\t\t.attr('id', `iconRect_${pid}`)\n\t\t\t\t\t.attr('x', box.x + box.height / 4)\n\t\t\t\t\t.attr('y', box.y)\n\t\t\t\t\t.attr('rx', box.height / 2)\n\t\t\t\t\t.attr('ry', box.height / 2)\n\t\t\t\t\t.attr('width', box.height / 2)\n\t\t\t\t\t.attr('height', box.height)\n\t\t\t\t\t.attr('fill', nodeData.color)\n\t\t\t\t\t.attr('style', 'transition:all 0.3s;')\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.append('g')\n\t\t\t\t\t.attr('id', `icon_${pid}`)\n\t\t\t\t\t.html(nodeData.icon)\n\n\t\t\t\td3.select(`#icon_${pid} svg`)\n\t\t\t\t\t.attr('fill', '#fff')\n\t\t\t\t\t.attr('width', box.height / 2)\n\t\t\t\t\t.attr('height', box.height / 2)\n\t\t\t\t\t.attr('x', box.x + box.height / 4)\n\t\t\t\t\t.attr('y', box.y + box.height * 0.23)\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.append('text')\n\t\t\t\t\t.text(nodeData.title)\n\t\t\t\t\t.attr('class', 'nodeType')\n\t\t\t\t\t.attr('x', box.x + box.height * 1.2)\n\t\t\t\t\t.attr('y', box.y + box.height * 0.75 / 2)\n\t\t\t\t\t.attr('width', box.height)\n\t\t\t\t\t.attr('height', box.height)\n\t\t\t\t\t.attr('font-weight', 'bold')\n\t\t\t\t\t.attr('fill', nodeData.color)\n\t\t\t\t// .attr('text-anchor', 'start')\n\t\t\t\t// .attr('dominant-baseline', 'start')\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.on(\"mouseover\", function (d) {\n\t\t\t\t\t\td3.select(`#rect_${pid}`).attr(\"stroke\", nodeData.color);\n\t\t\t\t\t\td3.select(`#iconRect_${pid}`)\n\t\t\t\t\t\t\t.attr(\"rx\", 0)\n\t\t\t\t\t\t\t.attr(\"ry\", 0)\n\t\t\t\t\t\t\t.attr('x', box.x + box.height / 2);\n\t\t\t\t\t})\n\t\t\t\t\t.on(\"mouseout\", function (d) {\n\t\t\t\t\t\td3.select(`#rect_${pid}`).attr(\"stroke\", \"#cdcdcd\");\n\t\t\t\t\t\td3.select(`#iconRect_${pid}`)\n\t\t\t\t\t\t\t.attr('rx', box.height / 2)\n\t\t\t\t\t\t\t.attr('ry', box.height / 2)\n\t\t\t\t\t\t\t.attr('x', box.x + box.height / 4)\n\t\t\t\t\t})\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.append('text')\n\t\t\t\t\t.text(nodeData.name)\n\t\t\t\t\t.attr('class', 'nodeContent')\n\t\t\t\t\t.attr('x', box.x + box.height * 1.2)\n\t\t\t\t\t.attr('y', box.y + box.height * 0.8)\n\t\t\t\t\t.attr('width', box.height)\n\t\t\t\t\t.attr('height', box.height)\n\n\n\t\t\t\t// d3.select(`#${pid}`)\n\t\t\t\t// \t.append('g')\n\t\t\t\t// \t.attr('id', `icon_status_${pid}`)\n\t\t\t\t// \t.html(nodeData.status.icon)\n\n\t\t\t\t// d3.select(`#icon_status_${pid} svg`)\n\t\t\t\t// \t.attr('fill', '#000')\n\t\t\t\t// \t.attr('width', box.height / 3)\n\t\t\t\t// \t.attr('height', box.height / 3)\n\t\t\t\t// \t.attr('x', box.x + box.width - box.height * 2 / 3)\n\t\t\t\t// \t.attr('y', box.y + box.height * 0.2 / 2)\n\n\t\t\t\td3.select(`#${pid}`)\n\t\t\t\t\t.append('g')\n\t\t\t\t\t.attr('id', `icon_status_text_${pid}`)\n\t\t\t\t\t.append('text')\n\t\t\t\t\t.text(nodeData.status.label)\n\t\t\t\t\t.attr('x', box.x + box.width - box.height * 2.8 / 3)\n\t\t\t\t\t.attr('y', box.y + box.height * 0.6 / 2)\n\n\t\t\t}\n\t\t})\n\t}\n\n\tpublic handleCollectExpand(nodeKey: string) {\n\t\tconst currentNode = this.dataMap.get(nodeKey)\n\t\tconst currentNodeInGraph = currentNode?.relativeKey\n\t\tconst [tarParent] = currentNode?.parent || []\n\t\tconst [tarChildren] = currentNode?.children || []\n\t\tconst parentNode = tarParent && this.dataMap?.get(tarParent.key)\n\t\tconst childrenNode = tarChildren && this.dataMap?.get(tarChildren.key)\n\n\t\tif (parentNode && currentNode && currentNodeInGraph === 'children') {\n\t\t\tconst collection = parentNode.collectionChildren || []\n\t\t\tconst children = parentNode.children || []\n\t\t\tconst collectionTypeNode = parentNode.children.pop() as IPreHandleNode\n\t\t\tconst targetNode = collection.shift()\n\n\t\t\tif (targetNode) {\n\t\t\t\t// 处理在节点collect里存在关系的情况\n\t\t\t\tthis.nodesInCollectionMap.delete(targetNode.key)\n\t\t\t\tconst nodesQuene = [...targetNode.children]\n\t\t\t\twhile (nodesQuene.length) {\n\t\t\t\t\tconst nodeItem = nodesQuene.shift()\n\t\t\t\t\tif (nodeItem) {\n\t\t\t\t\t\tthis.nodesInCollectionMap.delete(nodeItem.key)\n\t\t\t\t\t\tnodesQuene.push(...(nodeItem.children || []))\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 处理展开关系\n\t\t\t\tcurrentNode.collectNum = (currentNode.collectNum || 0) - 1\n\t\t\t\tcurrentNode.collectionNodes = collection\n\t\t\t\tparentNode.collectionChildren = collection\n\t\t\t\tif (collection.length) {\n\t\t\t\t\tparentNode.children = [...children, targetNode, collectionTypeNode]\n\t\t\t\t} else {\n\t\t\t\t\tparentNode.children = [...children, targetNode]\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (childrenNode && currentNode && currentNodeInGraph === 'parent') {\n\t\t\tconst collection = childrenNode.collectionParent || []\n\t\t\tconst parent = childrenNode.parent || []\n\t\t\tconst collectionTypeNode = childrenNode.parent.pop() as IPreHandleNode\n\t\t\tconst targetNode = collection.shift()\n\n\t\t\tif (targetNode) {\n\t\t\t\t// 处理在节点collect里存在关系的情况\n\t\t\t\tthis.nodesInCollectionMap.delete(targetNode.key)\n\t\t\t\tconst nodesQuene = [...targetNode.parent]\n\t\t\t\twhile (nodesQuene.length) {\n\t\t\t\t\tconst nodeItem = nodesQuene.shift()\n\t\t\t\t\tif (nodeItem) {\n\t\t\t\t\t\tthis.nodesInCollectionMap.delete(nodeItem.key)\n\t\t\t\t\t\tnodesQuene.push(...(nodeItem.parent || []))\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 处理展开关系\n\t\t\t\tcurrentNode.collectNum = (currentNode.collectNum || 0) - 1\n\t\t\t\tcurrentNode.collectionNodes = collection\n\t\t\t\tchildrenNode.collectionParent = collection\n\t\t\t\tif (collection.length) {\n\t\t\t\t\tchildrenNode.parent = [...parent, targetNode, collectionTypeNode]\n\t\t\t\t} else {\n\t\t\t\t\tchildrenNode.parent = [...parent, targetNode]\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * 渲染节点\n\t * @param nodes\n\t */\n\tpublic renderNode(nodes: IPreHandleNode[], isDisable?: boolean) {\n\t\tthis.loadingStart && this.loadingStart()\n\t\t// console.log('nodes', nodes);\n\t\t// console.log('nodesInCollectionMap', this.nodesInCollectionMap);\n\t\t// console.log('nodesInGraphMap', this.nodesInGraphMap);\n\n\t\tconst nodesRender = this.createRenderNodes(nodes, isDisable)\n\t\tconst nodesRenderRelation = this.cerateRenderNodesRelation(nodes);\n\t\tconst dotSrc = `digraph  {\n\t\t\tid=mainGroup;\n\t\t\trankdir = LR;\n\t\t\tranksep = 1;\n\t\t\tnodesep = 1;\n\t\t\tedge [color=\"#cdcdcd\"];\n\t\t\t${nodesRender.join(' ')} ${nodesRenderRelation.join(' ')}\n        }`;\n\n\t\tconst test = (d3.select('#gNode') as any)\n\t\t\t.graphviz({\n\t\t\t\tzoom: false,\n\t\t\t\tzoomTranslateExtent: [0, 0],\n\t\t\t\t// width: this.innerWidth,\n\t\t\t\t// height: this.innerHeight\n\t\t\t})\n\t\t\t.dot(dotSrc);\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tsetTimeout(() => {\n\t\t\t\ttry {\n\t\t\t\t\t(d3.select('#gNode') as any)\n\t\t\t\t\t\t.graphviz({\n\t\t\t\t\t\t\tzoom: false,\n\t\t\t\t\t\t\tzoomTranslateExtent: [0, 0],\n\t\t\t\t\t\t\t// width: this.innerWidth,\n\t\t\t\t\t\t\t// height: this.innerHeight\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.renderDot(dotSrc);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.log(error);\n\t\t\t\t}\n\n\t\t\t\t// 后处理\n\t\t\t\tthis.backRenderHandle()\n\n\t\t\t\tthis.loadingEnd && this.loadingEnd()\n\t\t\t\tresolve('')\n\t\t\t}, 100)\n\t\t})\n\t}\n\n\tpublic highlightRelation(node: IRenderNode) {\n\n\t\t// 全局置灰\n\t\td3.selectAll(`.node polygon`).attr('stroke', '#cdcdcd').attr('fill', '#ffffff');\n\t\td3.selectAll(`.node text`).attr('fill', '#cdcdcd');\n\t\td3.selectAll(`.node .rectBg`).attr('fill', '#cdcdcd');\n\t\td3.selectAll(`.edge path`).attr('stroke', '#cdcdcd');\n\t\td3.selectAll(`.edge polygon`).attr('stroke', '#cdcdcd').attr('fill', '#cdcdcd');\n\n\t\tconst cutTreeNodeParent = this.treeCutNode([node.data], 'parent', 'key', this.rootNode?.key)\n\t\tconst cutTreeNodeChildren = this.treeCutNode([node.data], 'children', 'key', this.rootNode?.key)\n\t\tconst nodeParentList = this.tree2List(cutTreeNodeParent, 'parent')\n\t\tconst nodeChildrenList = this.tree2List(cutTreeNodeChildren, 'children')\n\n\t\tconsole.log('nodeParentList', nodeParentList);\n\n\t\tfor (let i = 0; i < nodeParentList.length; i++) {\n\t\t\tconst item = nodeParentList[i];\n\n\t\t\t// 高亮节点\n\t\t\tif (item) {\n\t\t\t\t// d3.selectAll(`#${item.key} polygon`).attr('stroke', '#0078d4').attr('fill', '#ffffff');\n\t\t\t\t// d3.selectAll(`#${item.key} text`).attr('fill', '#0078d4');\n\t\t\t\t// d3.selectAll(`#${item.key} #rect_${item.key}`).attr('stroke', '#1e1653');\n\t\t\t\t// const currentColorTheme = nodeTypeThemeMap[item.data_fields] || ThemeColor[0]\n\t\t\t\tconst currentColor = item.color\n\t\t\t\td3.selectAll(`#${item.key} .rectBg`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeType`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeContent`).attr('fill', '#000');\n\t\t\t}\n\n\t\t\t// 高亮边\n\t\t\tif (item && item.parent && item.parent.length) {\n\t\t\t\tfor (let i = 0; i < item.parent.length; i++) {\n\t\t\t\t\tconst par = item.parent[i];\n\t\t\t\t\tconst edgeId = `edgePre_${par.key}_edge_${item.key}`;\n\t\t\t\t\td3.selectAll(`#${edgeId} path`).attr('stroke', '#1e1653');\n\t\t\t\t\td3.selectAll(`#${edgeId} polygon`).attr('stroke', '#1e1653').attr('fill', '#1e1653');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\n\t\tfor (let i = 0; i < nodeChildrenList.length; i++) {\n\t\t\tconst item = nodeChildrenList[i];\n\t\t\tif (item) {\n\t\t\t\t// d3.selectAll(`#${item.key} polygon`).attr('stroke', '#1e1653').attr('fill', '#ffffff');\n\t\t\t\t// d3.selectAll(`#${item.key} text`).attr('fill', '#1e1653');\n\t\t\t\t// d3.selectAll(`#${item.key} #rect_${item.key}`).attr('stroke', '#1e1653');\n\t\t\t\t// const currentColorTheme = nodeTypeThemeMap[item.data_fields] || ThemeColor[0]\n\t\t\t\tconst currentColor = item.color\n\t\t\t\td3.selectAll(`#${item.key} .rectBg`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeType`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeContent`).attr('fill', '#000');\n\t\t\t}\n\n\t\t\tif (item && item.children && item.children.length) {\n\t\t\t\tfor (let i = 0; i < item.children.length; i++) {\n\t\t\t\t\tconst child = item.children[i];\n\t\t\t\t\tconst edgeId = `edgePre_${item.key}_edge_${child.key}`;\n\t\t\t\t\td3.selectAll(`#${edgeId} path`).attr('stroke', '#1e1653')\n\t\t\t\t\td3.selectAll(`#${edgeId} polygon`).attr('stroke', '#1e1653').attr('fill', '#1e1653');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic refresh() {\n\t\tconsole.log('refresh');\n\t\t// todo 图的改造\n\n\t\t// const nodeParentList = this.tree2List([this.rootNode], 'parent')\n\t\t// const nodeChildrenList = this.tree2List([this.rootNode], 'children')\n\t\tconst nodeList = this.dataNodes\n\n\t\tfor (let i = 0; i < nodeList.length; i++) {\n\t\t\tconst item = nodeList[i];\n\t\t\tif (item?.data_fields === 'COLLECT') {\n\t\t\t\td3.selectAll(`#${item.key} polygon`).attr('stroke', '#000000')\n\t\t\t\td3.select(`#${item.key} text`).attr('fill', '#000000');\n\t\t\t\tcontinue\n\t\t\t}\n\n\t\t\t// 高亮节点\n\t\t\tif (item) {\n\t\t\t\t// const currentColorTheme = nodeTypeThemeMap[item.data_fields] || ThemeColor[0]\n\t\t\t\tconst currentColor = item.color\n\t\t\t\td3.selectAll(`#${item.key} .rectBg`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeType`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeContent`).attr('fill', '#000');\n\t\t\t}\n\n\n\t\t\t// 高亮边\n\t\t\tif (item && item.parent && item.parent.length) {\n\t\t\t\tfor (let i = 0; i < item.parent.length; i++) {\n\t\t\t\t\tconst par = item.parent[i];\n\t\t\t\t\tconst edgeId = `edgePre_${par.key}_edge_${item.key}`;\n\t\t\t\t\td3.selectAll(`#${edgeId} path`).attr('stroke', '#cdcdcd');\n\t\t\t\t\td3.selectAll(`#${edgeId} polygon`).attr('stroke', '#cdcdcd').attr('fill', '#cdcdcd');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfor (let i = 0; i < nodeList.length; i++) {\n\t\t\tconst item = nodeList[i];\n\t\t\tif (item?.data_fields === 'COLLECT') {\n\t\t\t\td3.selectAll(`#${item.key} polygon`).attr('stroke', '#000000')\n\t\t\t\td3.select(`#${item.key} text`).attr('fill', '#000000');\n\t\t\t\tcontinue\n\t\t\t}\n\n\t\t\tif (item) {\n\t\t\t\t// const currentColorTheme = nodeTypeThemeMap[item.data_fields] || ThemeColor[0]\n\t\t\t\tconst currentColor = item.color\n\t\t\t\td3.selectAll(`#${item.key} .rectBg`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeType`).attr('fill', currentColor);\n\t\t\t\td3.selectAll(`#${item.key} .nodeContent`).attr('fill', '#000');\n\t\t\t}\n\n\t\t\tif (item && item.children && item.children.length) {\n\t\t\t\tfor (let i = 0; i < item.children.length; i++) {\n\t\t\t\t\tconst child = item.children[i];\n\t\t\t\t\tconst edgeId = `edgePre_${item.key}_edge_${child.key}`;\n\t\t\t\t\td3.selectAll(`#${edgeId} path`).attr('stroke', '#cdcdcd')\n\t\t\t\t\td3.selectAll(`#${edgeId} polygon`).attr('stroke', '#cdcdcd').attr('fill', '#cdcdcd');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t *将某个节点移动到画布中间\n\t *\n\t * @param {(string)} id\n\t * @memberof CostMap\n\t */\n\tpublic anchorNode(id: string) {\n\t\tthis.resetNode(this.activeNodeId || '');\n\t\tconst graphvizDom: any = d3.select('#mainGroup').datum();\n\t\tconst relativeY = graphvizDom.translation.y;\n\n\t\tconst renderNode: any = this.renderNodesMap?.get(id);\n\t\tif (renderNode) {\n\t\t\t// pt转px\n\t\t\tconst x = -renderNode.x * (96 / 72) + this.innerWidth / 2 + 100;\n\t\t\t// const y = renderNode.y * (96 / 72) - relativeY;\n\t\t\tconst y = -(relativeY - -renderNode.y) * (96 / 72) + this.innerHeight / 2;\n\t\t\tthis.mainView.call(this.zoom.transform, d3.zoomIdentity.translate(x, y).scale(1));\n\t\t}\n\t\tthis.activeNode(id);\n\t}\n\n\t/**\n\t * 将整个应用居中展示\n\t *\n\t * @param {(string)} id\n\t * @memberof CostMap\n\t */\n\tpublic centerApp() {\n\t\tthis.resetNode(this.activeNodeId || '');\n\t\tconst scaleView: any = d3.select('#scaleView').node();\n\t\tconst scaleViewBox = scaleView.getBBox();\n\t\tconst currentCenterX = scaleViewBox.width / 2;\n\t\tconst viewCenterX = this.innerWidth / 2;\n\t\tconst x = viewCenterX - currentCenterX\n\n\t\tconst currentCenterY = scaleViewBox.height / 2;\n\t\tconst viewCenterY = this.innerHeight / 2;\n\t\tconst y = viewCenterY - currentCenterY - 250\n\n\t\tthis.mainView.call(this.zoom.transform, d3.zoomIdentity.translate(x, y).scale(1));\n\t}\n\n\tpublic activeNode(id: string) {\n\t\tconst renderNode = this.renderNodesMap?.get(id);\n\t\tconst dataNode = this.dataMap?.get(id);\n\t\tif (this.activeNodeId) {\n\t\t\td3.selectAll(`#${this.activeNodeId} .nodeContent`).attr('fill', '#000');\n\t\t}\n\t\tif (renderNode && dataNode) {\n\t\t\tthis.activeNodeId = id;\n\t\t\t// d3.selectAll(`#${id} .nodeContent`).attr('fill', ThemeColor[dataNode.level % 5].activeColor || '');\n\n\t\t\t// console.log(d3.selectAll(`#${renderNode.renderId} text[fill=\"#000000\"]`));\n\t\t\t// d3.selectAll(`#${renderNode.renderId} text`).attr('fill', ThemeColor[dataNode.level % 5].activeColor || '');\n\t\t\t// d3.selectAll(`#${renderNode.renderId} path`)\n\t\t\t// \t// .attr('fill', ThemeColor[dataNode.level].activeColor || '')\n\t\t\t// \t.attr('stroke', ThemeColor[dataNode.level].activeColor || '');\n\t\t\t// d3.selectAll(`#${renderNode.renderId} polyline`).attr(\n\t\t\t// \t'stroke',\n\t\t\t// \tThemeColor[dataNode.level].activeColor || '',\n\t\t\t// );\n\t\t}\n\t}\n\n\tpublic resetNode(id: string) {\n\t\tconst renderNode = this.renderNodesMap?.get(id);\n\t\tconst dataNode = this.dataMap?.get(id);\n\t\tif (renderNode && dataNode) {\n\t\t\td3.selectAll(`[node_id=value${renderNode.renderId}]`).attr('fill', '#000000');\n\t\t\t// d3.selectAll(`#${renderNode.renderId} text`).attr('fill', ThemeColor[dataNode.level % 5].color);\n\t\t\t// d3.selectAll(`#${renderNode.renderId} path`)\n\t\t\t// \t// .attr('fill', ThemeColor[dataNode.level % 5].background)\n\t\t\t// \t.attr('stroke', ThemeColor[dataNode.level % 5].border);\n\t\t\t// d3.selectAll(`#${renderNode.renderId} polyline`).attr('stroke', ThemeColor[dataNode.level % 5].border);\n\t\t}\n\t}\n}\n", "import { <PERSON>er, Spin, } from 'antd';\nimport React, { useEffect, useRef, useState } from 'react';\nimport { getNodeInfoCommon, getNodeRelationCommon } from '../../api/commonPipeline';\nimport Loading from '../../components/Loading/Loading';\nimport { getParam } from '../../util';\nimport NodeDetail from './NodeDetail';\nimport RelationDiagram from './TreePlusDiagram';\nimport { ILayoutConfig, INodeDetailItem, INodeItem } from './TreePlusInterface';\nimport './TreePlus.less';\nimport { useTranslation } from 'react-i18next';\n\ninterface IProps {\n\tid?: string | number | undefined | null;\n\tisCollapsed?: boolean;\n}\n\nexport default function TreePlus(props: IProps) {\n\tconst [loading, setLoading] = useState(false);\n\tconst [visableDrawer, setVisableDrawer] = useState(false)\n\tconst [isNoData, setIsNoData] = useState(false)\n\tconst [nodeDetail, setNodeDetail] = useState<INodeDetailItem[]>([])\n\tconst [loadingDetail, setLoadingDetail] = useState(false)\n\tconst [layoutConfig, setLayoutConfig] = useState<ILayoutConfig>()\n\tconst { t, i18n } = useTranslation();\n\n\tconst treeDataRef = useRef<INodeItem[]>()\n\n\tconst [relationDiagram, _setRelationDiagram] = useState<RelationDiagram>();\n\tconst relationDiagramRef = useRef(relationDiagram);\n\tconst setRelationDiagram = (data: RelationDiagram): void => {\n\t\trelationDiagramRef.current = data;\n\t\t_setRelationDiagram(data);\n\t};\n\n\tuseEffect(() => {\n\t\tconst target = new RelationDiagram({\n\t\t\tcontainerId: 'd3Container',\n\t\t\tmainViewId: 'd3MainView',\n\t\t\tmargin: 16,\n\t\t});\n\t\tsetRelationDiagram(target);\n\n\t\tconst resizeIframe = document.getElementById('resizeIframe') as HTMLIFrameElement;\n\t\tif (resizeIframe.contentWindow) {\n\t\t\tresizeIframe.contentWindow.onresize = () => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tconsole.log(relationDiagramRef.current);\n\t\t\t\t\trelationDiagramRef.current?.reSize();\n\t\t\t\t}, 1000);\n\t\t\t};\n\t\t}\n\t}, []);\n\n\tuseEffect(() => {\n\t\tif (relationDiagram) {\n\t\t\tconst backurl = getParam('backurl') || ''\n\t\t\tfetchBloodRelationData(backurl)\n\t\t}\n\n\t}, [relationDiagram]);\n\n\tconst handleClickNode = (node: any) => {\n\t\tconsole.log(node)\n\t\tconst currentNode = relationDiagram && relationDiagram.dataMap && relationDiagram.dataMap.get(node.key)\n\t\tconsole.log('currentNode', currentNode);\n\n\t\tsetNodeDetail([])\n\t\tsetLoadingDetail(true)\n\t\tsetVisableDrawer(true)\n\t\tgetNodeInfoCommon(currentNode?.detail_url || '').then(res => {\n\t\t\tconsole.log(res.data.result.detail);\n\t\t\tconst detail = res.data.result.detail\n\t\t\tsetNodeDetail(detail)\n\t\t\tsetLoadingDetail(false)\n\t\t}).catch(() => {\n\t\t\tsetLoadingDetail(false)\n\t\t})\n\t}\n\n\tconst fetchBloodRelationData = (url: string) => {\n\t\tif (relationDiagram) {\n\t\t\tif (!!url) {\n\t\t\t\tsetLoading(true);\n\t\t\t\tgetNodeRelationCommon(url)\n\t\t\t\t\t.then((res) => {\n\t\t\t\t\t\tconst dag = res.data.result.dag || []\n\t\t\t\t\t\tconst layout = res.data.result.layout || {}\n\t\t\t\t\t\ttreeDataRef.current = dag\n\n\t\t\t\t\t\tif (!dag.length) {\n\t\t\t\t\t\t\tsetIsNoData(true)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tsetIsNoData(false)\n\t\t\t\t\t\t}\n\t\t\t\t\t\trelationDiagram.initData(dag);\n\t\t\t\t\t\trelationDiagram.handleNodeClick = handleClickNode;\n\t\t\t\t\t\trelationDiagram.loadingStart = () => {\n\t\t\t\t\t\t\tsetLoading(true)\n\t\t\t\t\t\t}\n\t\t\t\t\t\trelationDiagram.loadingEnd = () => {\n\t\t\t\t\t\t\tsetLoading(false)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tsetLayoutConfig(layout)\n\t\t\t\t\t})\n\t\t\t\t\t.catch((err) => {\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t\tsetIsNoData(true)\n\t\t\t\t\t})\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tsetLoading(false);\n\t\t\t\t\t});\n\t\t\t} else {\n\t\t\t\trelationDiagram.initData([])\n\t\t\t\tsetIsNoData(true)\n\t\t\t}\n\t\t}\n\t};\n\n\treturn (\n\t\t<div className=\"p-r h100\" id=\"fullContainer\">\n\t\t\t<iframe\n\t\t\t\tid=\"resizeIframe\"\n\t\t\t\tsrc=\"\"\n\t\t\t\tframeBorder=\"0\"\n\t\t\t\tclassName=\"p-a z-1\"\n\t\t\t\tstyle={{ width: '100%', height: '100%' }}\n\t\t\t/>\n\t\t\t{\n\t\t\t\tloading ? <div className=\"p-a w100 h100 d-f ac jc mark z999 fadein\">\n\t\t\t\t\t<Spin spinning={loading} indicator={<Loading />}>\n\t\t\t\t\t\t<div />\n\t\t\t\t\t</Spin>\n\t\t\t\t</div> : null\n\t\t\t}\n\t\t\t<Drawer\n\t\t\t\ttitle=\"节点详情\"\n\t\t\t\twidth={800}\n\t\t\t\tclosable={false}\n\t\t\t\tonClose={() => { setVisableDrawer(false) }}\n\t\t\t\tvisible={visableDrawer}\n\t\t\t\tclassName=\"nodedetail-wapper\"\n\t\t\t>\n\t\t\t\t<Spin spinning={loadingDetail}>\n\t\t\t\t\t<NodeDetail data={nodeDetail} />\n\t\t\t\t</Spin>\n\t\t\t</Drawer>\n\n\t\t\t{\n\t\t\t\tisNoData ? <div className=\"p-a w100 h100 d-f ac jc ta-c z1\">\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<div><img className=\"w320\" src={require('../../images/workData.png')} alt=\"\" /></div>\n\t\t\t\t\t\t<div className=\"fs22\">{t('暂无数据')}</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div> : null\n\t\t\t}\n\t\t\t<div className=\"d-f fd-c h100 ov-h\">\n\t\t\t\t<div className=\"tree-header\">\n\t\t\t\t\t<div className=\"p16 d-f jc-b ac\">\n\t\t\t\t\t\t<div className=\"d-f ac\">\n\t\t\t\t\t\t\t<span className=\"icon-custom\" dangerouslySetInnerHTML={{ __html: layoutConfig?.icon || '' }}></span>\n\t\t\t\t\t\t\t<span className=\"ml8 fs18\">{layoutConfig?.title}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tlayoutConfig?.right_button.map(button => {\n\t\t\t\t\t\t\t\t\treturn <div onClick={() => {\n\t\t\t\t\t\t\t\t\t\twindow.open(button.url, \"blank\")\n\t\t\t\t\t\t\t\t\t}} className=\"c-text-w ml8 btn-ghost d-il\">{button.label}</div>\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div className=\"header-detail p16 d-f\">\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t(layoutConfig?.detail || []).map(detail => {\n\t\t\t\t\t\t\t\treturn <div className=\"flex1 mr48 header-detail-item\">\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tdetail.map(group => {\n\t\t\t\t\t\t\t\t\t\t\treturn <div className=\"pb2\">{group.label}：{group.value}</div>\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div id=\"d3Container\" className=\"flex1\">\n\t\t\t\t\t<svg id=\"d3MainView\" />\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n", "import React from 'react'\nimport TreePlus from './TreePlus'\n\nexport default function CommonPipeline() {\n    return (\n        <>\n            <TreePlus />\n        </>\n    )\n}\n", "import CommonPipeline from './CommonPipeline'\n\nexport default function DWStandard() {\n    return (\n        <div className=\"fade-in h100 d-f fd-c\">\n            <CommonPipeline />\n        </div>\n    )\n}\n", "import { But<PERSON>, Col, message, Row, Tabs } from 'antd'\nimport React, { useEffect, useRef, useState } from 'react'\nimport { CopyToClipboard } from 'react-copy-to-clipboard';\nimport { CopyOutlined } from '@ant-design/icons';\nimport TableBox from '../../components/TableBox/TableBox';\nimport { INodeDetailItem, TGroupContentType } from './TreePlusInterface';\nimport EchartCore from '../../components/EchartCore/EchartCore';\nimport { getNodeInfoApi } from '../../api/commonPipeline';\nimport './NodeDetail.less';\nimport { group } from 'd3';\n\ninterface IProps {\n    data: INodeDetailItem[]\n}\n\nexport type ITNodeDetail = 'common' | 'table' | 'sql'\n\nexport default function NodeDetail(props: IProps) {\n    const [isSqlVisable, setIsSqlVisable] = useState(false)\n    const [currentDataItem, setCurrentDataItem] = useState<INodeDetailItem>()\n\n    const [nodeInfoApiMap, _setNodeInfoApiMap] = useState<Record<string, any>>({})\n    const nodeInfoApiMapRef = useRef(nodeInfoApiMap);\n    const setNodeInfoApiMap = (data: Record<string, any>): void => {\n        nodeInfoApiMapRef.current = data;\n        _setNodeInfoApiMap(data);\n    };\n\n    useEffect(() => {\n        const apiList: any[] = []\n        const nameList: string[] = []\n        props.data.forEach(tab => {\n            tab.content.forEach(group => {\n                if (group.groupContent.type === 'api') {\n                    const req = getNodeInfoApi(group.groupContent.value)\n                    apiList.push(req)\n                    nameList.push(`${tab.tabName}_${group.groupName}`)\n                }\n            })\n        })\n        console.log('apiList', apiList);\n        Promise.all(apiList).then(res => {\n            const result = res.map(item => item.data.result)\n            const resMap = result.reduce((pre, next, index) => ({ ...pre, [nameList[index]]: { ...next } }), {})\n            setNodeInfoApiMap(resMap)\n        })\n    }, [props.data])\n\n    const handelNodeDetailTable = (item: any) => {\n        try {\n            JSON.parse(`${item.value || []}`)\n        } catch (error) {\n            console.log(error);\n        }\n        const dataList = JSON.parse(`${item.value || '[]'}`)\n        let columnsConfig = Object.entries(dataList[0] || {}).reduce((pre: any, [key, value]) => [...pre, { title: key, dataIndex: key, key }], [])\n\n        return <Col span={16}>\n            <TableBox\n                rowKey={(record: any) => {\n                    return JSON.stringify(record)\n                }}\n                size={'small'}\n                cancelExportData={true}\n                columns={columnsConfig}\n                pagination={false}\n                dataSource={dataList}\n            // scroll={{ x: 1500, y: scrollY }}\n            />\n        </Col>\n    }\n\n    const handleGroupContent = (type: TGroupContentType, content: any, tabName: string, groupName: string) => {\n        switch (type) {\n            case 'map':\n                return renderMapComponent(content)\n            case 'echart':\n                return renderEchart(content)\n            case 'text':\n                return renderMapText(content)\n            case 'iframe':\n                return renderMapIframe(content)\n            case 'html':\n                return renderHtml(content)\n            case 'api':\n                return renderApi(content, tabName, groupName)\n            default:\n                return <span style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>{content}</span>\n        }\n    }\n\n    const renderApi = (data: string, tabName: string, groupName: string) => {\n        const apiKey = `${tabName}_${groupName}`;\n\n        // getNodeInfoApi(data).then(res => {\n        //     console.log(res.data.result);\n        //     const result = res.data.result\n\n        // }).catch(err => { })\n        return <div>{nodeInfoApiMapRef.current[apiKey] ? nodeInfoApiMapRef.current[apiKey].value : ''}</div>\n    }\n    const renderHtml = (data: string) => {\n        return <div dangerouslySetInnerHTML={{ __html: data }}></div>\n    }\n    const renderMapComponent = (data: Record<string, string>) => {\n        const dataList: Array<{ label: string, value: string }> = Object.entries(data).reduce((pre: any, [key, val]) => ([...pre, { label: key, value: val }]), [])\n        return <div className=\"bg-title p16\">\n            {\n                dataList.map((item, index) => {\n                    return <Row className=\"mb8 w100\" key={`nodeDetailItem_${index}`}>\n                        <Col span={8}><div className=\"ta-l\"><strong>{item.label}：</strong></div></Col>\n                        <Col span={16}><span style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>{item.value}</span></Col>\n                    </Row>\n                })\n            }\n        </div>\n    }\n    const renderEchart = (data: any) => {\n        var currentOps: any = {}\n        eval(`currentOps=${data}`)\n        return <div className=\"bg-title p16\">\n            <EchartCore option={currentOps} loading={false} />\n        </div>\n    }\n    const renderMapText = (data: string) => {\n        return <div className=\"p16 bg-title\" style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>{data}</div>\n    }\n    const renderMapIframe = (data: any) => {\n        return <iframe\n            src={data.url}\n            allowFullScreen\n            allow=\"microphone;camera;midi;encrypted-media;\"\n            className=\"w100 fade-in\"\n            style={{ border: 0, height: 500 }}>\n        </iframe>\n    }\n\n    return (\n        <>\n            <Tabs className=\"nodedetail-tab\">\n                {\n                    props.data.map((tab, tabIndex) => {\n                        return <Tabs.TabPane tab={tab.tabName} key={`nodeDetailTab${tabIndex}`}>\n                            <div className=\"d-f fd-c jc-b h100\">\n                                <div className=\"flex1\">\n                                    {\n                                        tab.content.map((group, groupIndex) => {\n                                            return <div className=\"mb32\" key={`nodeGroup${groupIndex}`}>\n                                                <div className=\"fs16 mb16 bor-l b-theme pl4\" style={{ borderLeftWidth: 2 }} dangerouslySetInnerHTML={{ __html: group.groupName }}></div>\n                                                <div>\n                                                    {handleGroupContent(group.groupContent.type, group.groupContent.value, tab.tabName, group.groupName)}\n                                                </div>\n                                            </div>\n                                        })\n                                    }\n                                </div>\n                                <div className=\"nodedetail-tool\">\n                                    {\n                                        tab.bottomButton.map(button => {\n                                            return <Button className=\"mr12 icon-tool-wrapper\" onClick={() => {\n                                                window.open(button.url, 'blank')\n                                            }}>\n                                                <span className=\"icon-tool\" dangerouslySetInnerHTML={{ __html: button.icon }}></span>\n                                                <span className=\"ml6\">{button.text}</span>\n                                            </Button>\n                                        })\n                                    }\n                                </div>\n                            </div>\n                        </Tabs.TabPane>\n                    })\n                }\n            </Tabs>\n        </>\n    )\n}\n"], "names": ["getNodeRelationCommon", "url", "axios", "getNodeInfoCommon", "getNodeInfoApi", "defaultChartStyle", "height", "EchartCore", "props", "useState", "chartInstance", "setChartInstance", "id", "Math", "random", "toString", "substring", "useTranslation", "t", "option", "i18n", "useEffect", "chartDom", "document", "getElementById", "chart", "echarts", "setOption", "data", "spinning", "loading", "className", "style", "isNoData", "CopyToClipboard", "require", "ResizableTitle", "onResize", "width", "restProps", "Resizable", "handle", "onClick", "e", "stopPropagation", "draggableOpts", "enableUserSelectHack", "userSelect", "exportDataVisible", "setExportDataVisible", "header", "dataFormat", "setDataFormat", "filterValue", "setFilterValue", "columns", "cols", "setCols", "handleResize", "index", "_", "size", "temp", "tableWidth", "reduce", "pre", "next", "localStorage", "setItem", "table<PERSON><PERSON>", "JSON", "stringify", "setCurrentTableScroll", "currentTableScroll", "x", "customColumns", "map", "col", "onHeaderCell", "column", "scroll", "dataSource", "filter", "item", "indexOf", "dataIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataColumns", "sheetHeader", "title", "tarData", "for<PERSON>ach", "dataRow", "row", "colName", "res", "push", "handleExportJira", "str", "length", "join", "<PERSON><PERSON><PERSON>", "Object", "values", "handleExportText", "direction", "maskClosable", "centered", "bodyStyle", "maxHeight", "overflow", "visible", "onCancel", "footer", "position", "options", "label", "value", "defaultValue", "onChange", "right", "bottom", "type", "tab", "text", "onCopy", "message", "cursor", "minHeight", "titleNode", "buttonNode", "cancelExportData", "justify", "align", "marginLeft", "renderEmpty", "flexDirection", "src", "emptyImg", "alt", "components", "cell", "pagination", "rowSelection", "Loading", "left", "d3Tip", "d3TipF", "D3Tool", "containerId", "mainViewId", "margin", "container", "scaleView", "mainView", "gNode", "gLink", "gText", "gBorder", "gTextCache", "innerWidth", "innerHeight", "tip", "fontSize", "zoom", "currentNode", "list2Tree", "root", "withoutChildrenDataList", "children", "dataMap", "list2Map", "i", "undefined", "parentId", "parent", "get", "ci_id", "tree2List", "key", "quene", "shift", "list", "isReplace", "Map", "set", "treeCutNode", "matchKey", "treeCutAfter", "dfs", "tarItem", "parse2Rectangle", "coordList", "split", "coord", "y", "rectSourceObj", "currentIndex", "tar", "x1", "x0", "y3", "y0", "abs", "center", "_selfThis", "this", "d3", "append", "attr", "marginObj", "top", "scrollWidth", "scrollHeight", "html", "d", "call", "scaleExtent", "on", "transform", "hide", "scale", "console", "log", "textStr", "textDom", "textBox", "node", "getBBox", "rect", "remove", "ThemeColor", "graphviz", "background", "color", "border", "activeColor", "disabled", "disabledColor", "nodeTypeThemeMap", "RelationDiagram", "isCollection", "dataMapByName", "renderNodesMap", "nodesInGraphMap", "nodesInCollectionMap", "dataNodes", "activeNodeId", "rootNode", "handleNodeClick", "loadingStart", "loadingEnd", "htmlStrEnCode", "replace", "charCodeAt", "sourceId", "replaceAll", "nodes", "<PERSON><PERSON><PERSON>", "nodesMapByKey", "nodesMapById", "rootIds", "<PERSON><PERSON><PERSON>", "parent<PERSON><PERSON>", "level", "upItem", "idPath", "nid", "nodeCacheById", "nodeCache", "tarNode", "includes", "pop", "currentItemList", "itemId", "rootId", "preHandlePreData", "preHandleNodes", "preHandleNextData", "preHandleData", "preRenderData", "preRenderDataReady", "renderNode", "then", "centerApp", "preData", "nextData", "targetData", "targetDataMap", "targetDataMapByName", "isDisable", "data_fields", "collectNum", "child", "each", "nodeData", "currentColorTheme", "box", "datum", "renderInfo", "renderId", "theme", "error", "beautifulNode", "timeout", "clearTimeout", "setTimeout", "d3MainView", "onclick", "isNode", "path", "elem", "refresh", "boxs", "bbox", "pid", "dataNode", "highlightRelation", "cy", "icon", "name", "status", "nodeKey", "currentNodeInGraph", "tarParent", "tarC<PERSON><PERSON>n", "parentNode", "childrenNode", "collection", "collection<PERSON><PERSON><PERSON>n", "collectionTypeNode", "targetNode", "delete", "nodesQuene", "nodeItem", "collectionNodes", "collectionParent", "<PERSON><PERSON><PERSON>", "createRenderNodes", "nodesRenderRelation", "cerateRenderNodesRelation", "dotSrc", "zoomTranslateExtent", "dot", "Promise", "resolve", "reject", "renderDot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cutTreeNodeParent", "cutTreeNodeChildren", "nodeParentList", "nodeChildrenList", "currentColor", "par", "edgeId", "nodeList", "resetNode", "relativeY", "translation", "activeNode", "scaleViewBox", "currentCenterX", "currentCenterY", "TreePlus", "setLoading", "visableDrawer", "setVisableDrawer", "setIsNoData", "nodeDetail", "setNodeDetail", "loadingDetail", "setLoadingDetail", "layoutConfig", "setLayoutConfig", "treeDataRef", "useRef", "relationDiagram", "_setRelationDiagram", "relationDiagramRef", "target", "current", "resizeIframe", "contentWindow", "onresize", "reSize", "backurl", "getPara<PERSON>", "fetchBloodRelationData", "handleClickNode", "detail_url", "result", "detail", "catch", "dag", "layout", "initData", "err", "finally", "frameBorder", "indicator", "closable", "onClose", "NodeDetail", "dangerouslySetInnerHTML", "__html", "right_button", "button", "window", "open", "group", "CommonPipeline", "DWStandard", "isSqlVisable", "setIsSqlVisable", "currentDataItem", "setCurrentDataItem", "nodeInfoApiMap", "_setNodeInfoApiMap", "nodeInfoApiMapRef", "setNodeInfoApiMap", "apiList", "nameList", "content", "groupContent", "req", "tabName", "groupName", "all", "resMap", "handelNodeDetailTable", "parse", "dataList", "columnsConfig", "entries", "span", "record", "handleGroupContent", "renderMapComponent", "renderEchart", "renderMapText", "renderMapIframe", "renderHtml", "renderApi", "wordBreak", "whiteSpace", "<PERSON><PERSON><PERSON><PERSON>", "val", "currentOps", "eval", "allowFullScreen", "allow", "tabIndex", "groupIndex", "borderLeftWidth", "bottomButton"], "sourceRoot": ""}