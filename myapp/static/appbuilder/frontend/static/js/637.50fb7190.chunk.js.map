{"version": 3, "file": "static/js/637.50fb7190.chunk.js", "mappings": "oLAEA,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,8QAAkR,KAAQ,OAAQ,MAAS,Y,WCKjcA,EAAe,SAAsBC,EAAOC,GAC9C,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,IAAcA,EAAAA,EAAAA,GAAc,CAAC,EAAGJ,GAAQ,CAAC,EAAG,CAC5FC,IAAKA,EACLI,KAAMC,IAEV,EACAP,EAAaQ,YAAc,eAC3B,MAA4BL,EAAAA,WAAiBH,E,gFCX7C,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,yrBAA6rB,KAAQ,OAAQ,MAAS,Y,WCK52BS,EAAe,SAAsBR,EAAOC,GAC9C,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,IAAcA,EAAAA,EAAAA,GAAc,CAAC,EAAGJ,GAAQ,CAAC,EAAG,CAC5FC,IAAKA,EACLI,KAAMI,IAEV,EACAD,EAAaD,YAAc,eAC3B,MAA4BL,EAAAA,WAAiBM,E,gFCX7C,EAD0B,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,oLAAuL,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,oLAAwL,KAAQ,eAAgB,MAAS,Y,WCK9kBE,EAAsB,SAA6BV,EAAOC,GAC5D,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,IAAcA,EAAAA,EAAAA,GAAc,CAAC,EAAGJ,GAAQ,CAAC,EAAG,CAC5FC,IAAKA,EACLI,KAAMM,IAEV,EACAD,EAAoBH,YAAc,sBAClC,MAA4BL,EAAAA,WAAiBQ,E,gFCX7C,EADmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,SAAW,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,2SAA+S,KAAQ,OAAQ,MAAS,Y,WCK9dE,EAAe,SAAsBZ,EAAOC,GAC9C,OAAoBC,EAAAA,cAAoBC,EAAAA,GAAUC,EAAAA,EAAAA,IAAcA,EAAAA,EAAAA,GAAc,CAAC,EAAGJ,GAAQ,CAAC,EAAG,CAC5FC,IAAKA,EACLI,KAAMQ,IAEV,EACAD,EAAaL,YAAc,eAC3B,MAA4BL,EAAAA,WAAiBU,E,wICTvCE,EAAaC,EAAAA,GAAAA,OAAa,CAC9BC,QAAUC,SAAAA,GACR,OAAOA,EAAOC,QAAUC,MAAMC,QAAQH,GAAUA,EAAOI,OAAS,EAClE,IAGIC,EAAWP,EAAAA,GAAAA,OAAa,CAC5BC,QAAUC,SAAAA,GACR,OAAOA,EAAOC,QAAUC,MAAMC,QAAQH,GAAUM,KAAKC,IAAG,MAARD,MAAI,OAAQN,IAAU,CACxE,IAGEQ,EAASC,EAAAA,EAAAA,KAAgB,CAC3BC,WAAY,CAAEC,MAAO,qBAGvB,SAASC,EAAWC,GAClB,IAG2C,EAHrCC,EAAOD,EAAKE,MAAMC,MAAMX,GACxBY,EAAMJ,EAAKE,MAAMC,MAAMnB,GACvBqB,EAAU,IAAIC,EAAAA,GAA6B,UACxBN,EAAKO,eAAa,IAA3C,IAAK,EAAL,qBACE,IAD2C,cAAlCC,EAAI,EAAJA,KAAMC,EAAAA,EAAAA,GACNC,EAAMF,EAAME,GAAOD,GAAM,CAChC,IAAIE,EAAOX,EAAKE,MAAMU,IAAIC,OAAOH,GAE7BC,EAAKG,OAASb,IAAS,GAAoB,IAAfG,EAAIhB,QAClCiB,EAAQU,IAAIJ,EAAKH,KAAMG,EAAKH,KAAMb,GAEhCS,EAAIhB,OAAS,GAAKgB,EAAIb,OAAOyB,SAASL,EAAKG,SAC7CT,EAAQU,IAAIJ,EAAKH,KAAMG,EAAKH,KAAMb,GAEpCe,EAAMC,EAAKF,GAAK,CAClB,CACF,+BACA,OAAOJ,EAAQY,QACjB,CAEA,IAAMC,EAAcC,EAAAA,GAAAA,UAAoB,WAGpCC,SAAAA,EAAYpB,IAAkB,oBAD9BqB,iBAAW,EAETC,KAAKD,YAActB,EAAWC,EAChC,CAOA,OAPA,8BAEAuB,SAAOA,GACLD,KAAKD,YAActB,EAAWwB,EAAOvB,KAIvC,OAZoC,GActC,CACEqB,YAAcG,SAAAA,GAAC,OAAKA,EAAEH,WAAAA,IAIpBI,EAAY,SAACC,GAA2F,MAC5G,YADkG,IAAjFA,IAAAA,EAAoF,CAAC,GAC/FC,EAAAA,GAAAA,WAAoB,eACzB,WAAYD,EAAIE,UAAc,CAAEC,gBAAiBH,EAAII,YAAc,aAAW,SAC9E,UAAWJ,EAAIE,UAAc,CAAEC,gBAAiBH,EAAIK,WAAa,YAAU,GAE/E,EAcMC,EAAQ,SAACC,EAAeC,EAAcjC,GAAY,OACtDZ,MAAMmB,KAAK,CAAEpB,QAAS8C,EAAOD,GAAShC,EAAO,IAAK,SAACkC,EAAGC,GAAC,OAAKH,EAAQG,EAAInC,CAAI,GAAC,EAExE,SAASoC,EAAaC,QAA4B,IAA5BA,IAAAA,EAA+B,CAAC,GAC3D,IAAiD,EAAPA,EAAlCV,UAAAA,OAAS,IAAG,sBACpBjC,EAASC,EAAAA,EAAAA,KAAgB,CACvBC,WAAY,CAAEC,MAAO8B,KAEnBU,EAAQtD,YAAcK,MAAMC,QAAQgD,EAAQtD,aAC9CsD,EAAQrC,KAAO,KACfqC,EAAQtD,WAAasD,EAAQtD,WAAWuD,KAAKC,SAAAA,GAC3C,OAAInD,MAAMC,QAAQkD,IAA4B,kBAAZA,EAAK,IAAsC,kBAAZA,EAAK,GAC7DR,EAAMQ,EAAK,GAAIA,EAAK,GAAI,GAE1BA,CACT,KAEAF,EAAQtD,WAAa,KAEvB,IAAMyD,EAAa,CACM,OAAvBH,EAAQtD,WAAsB,GAAKA,EAAW0D,GAAGJ,EAAQtD,YAAc,IACtD,OAAjBsD,EAAQrC,KAAgB,GAAKT,EAASkD,GAAGJ,EAAQrC,MAAQ,GACzDiB,GAEF,GAAIU,EAAW,CACb,IAAMe,EAAmBlB,EAAU,CAAEK,WAAYQ,EAAQR,WAAYC,UAAWO,EAAQP,UAAWH,UAAAA,IACnGa,EAAWG,KAAKD,EAClB,CACA,OAAOF,CACT,C,mHC9DaI,EAAc,SAAAC,GAA0E,IAAvEC,EAAuDD,EAAvDC,MAAK,EAAkDD,EAAhDE,SAAAA,OAAQ,IAAG,GAAC,EAAC,IAAmCF,EAAjCG,OAAAA,OAAM,IAAG,OACrDC,EAA0C,CAC9C,cAAe,CAAC,GAEZC,EAAuB,CAAC,EAC1BH,EAASI,aACXD,EAAUtB,gBAAkBmB,EAASI,YAEnCJ,EAASK,aACXF,EAAUG,MAAQN,EAASK,aAEzBL,EAASI,YAAcJ,EAASK,cAClCH,EAAa,KAAOC,GAGlBH,EAASO,aACXL,EAAa,4BAA8B,CACzCK,WAAYP,EAASO,aAGrBP,EAASQ,mBACXN,EAAa,eAAerB,gBAAkBmB,EAASQ,kBAErDR,EAASS,mBACXP,EAAa,eAAeI,MAAQN,EAASS,kBAE3CT,EAASU,eACXR,EAAa,eAAeS,iBAAmBX,EAASU,cAGtDV,EAASY,QACXV,EAAa,eAAiB,CAC5BW,WAAYb,EAASY,OAEvBV,EAAa,8BAAgC,CAC3CY,gBAAiBd,EAASY,QAG9B,IAAIG,EAAmC,CAAC,EACpCf,EAASgB,yBACXD,EAAsBT,MAAQN,EAASgB,wBAErChB,EAASiB,gBACXf,EAAa,kBAAoB,CAC/BrB,gBAAiBmB,EAASiB,eAE5BF,EAAsBlC,gBAAkBmB,EAASiB,eAEnDf,EAAa,wBAA0Ba,EAEnCf,EAASkB,YACXhB,EACE,+GACE,CACFrB,gBAAiBmB,EAASkB,YAG1BlB,EAASmB,iBACXjB,EAAa,wBAA0B,CACrCrB,gBAAiBmB,EAASmB,iBAG9B,IAAMC,EAAiBzC,EAAAA,GAAAA,MAAiBuB,EAAc,CACpDmB,KAAgB,SAAVtB,IAGFuB,EAAiBC,EAAAA,GAAAA,OAAsBtB,GAG7C,MAFkB,CAACmB,GAAgBI,EAAAA,EAAAA,IAAmBF,GAGxD,EC7EaG,EAnCcnC,SAAAA,GACzB,MAAwDA,GAAW,CAAC,EAAC,IAA7DS,MAAAA,OAAK,IAAG,UAAO,MAAEC,SAAAA,OAAQ,IAAG,GAAC,EAAC,MAAEC,OAAAA,OAAM,IAAG,OACjD,OAAOJ,EAAY,CACjBE,MAAOA,EACPC,UAAU,QACRI,WAAY,UACZC,WAAY,UACZO,MAAO,UACPM,UAAW,UACXC,eAAgB,UAChBX,iBAAkB,UAClBC,iBAAkB,UAClBC,aAAc,cACdO,cAAe,aACZjB,GAELC,OAAQ,CACN,CAAEyB,IAAK,CAACC,EAAAA,GAAAA,KAAQA,EAAAA,GAAAA,SAAYrB,MAAO,WACnC,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,QAAWA,EAAAA,GAAAA,QAAWrB,MAAO,WACrC,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,QAAWrB,MAAO,WAC1B,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,QAAWrB,MAAO,WAC1B,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,cAAiBrB,MAAO,WAChC,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,QAAWrB,MAAO,WAC1B,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,SAAYrB,MAAO,WAC3B,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,SAAYrB,MAAO,WAC3B,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,OAAUrB,MAAO,WACzB,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,MAASrB,MAAO,WACxB,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,cAAiBrB,MAAO,WAChC,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,SAAWA,EAAAA,GAAAA,eAAkBrB,MAAO,WAC5C,CAAEoB,IAAK,CAACC,EAAAA,GAAAA,WAAaA,EAAAA,GAAAA,UAAaA,EAAAA,GAAAA,UAAarB,MAAO,YAAW,eAC9DL,KAGT,CAEsB2B,E,0PC9BlBC,EAA4BzG,EAAAA,YAAiB,SAAUF,EAAOC,GAChE,IAAI2G,EAEAC,EAAY7G,EAAM6G,UAClBC,EAAc9G,EAAM8G,YACpBpD,EAAY1D,EAAM0D,UAClBqD,EAAQ/G,EAAM+G,MACdC,EAAWhH,EAAMgH,SACjBC,EAAWjH,EAAMiH,SACjBC,EAAOlH,EAAMkH,KAEbC,EAAkBjH,EAAAA,SAAe+G,GAAYH,GAC7CM,GAAmBC,EAAAA,EAAAA,GAAeF,EAAiB,GACnDG,EAAWF,EAAiB,GAC5BG,EAAcH,EAAiB,GAQnC,OANAlH,EAAAA,WAAgB,YACV4G,GAAeG,IACjBM,GAAY,EAEhB,GAAG,CAACT,EAAaG,IAEZK,EAIepH,EAAAA,cAAoB,MAAO,CAC7CD,IAAKA,EACLyD,UAAW8D,IAAW,GAAGC,OAAOZ,EAAW,aAAcD,EAAc,CAAC,GAAGc,EAAAA,EAAAA,GAAgBd,EAAa,GAAGa,OAAOZ,EAAW,mBAAoBI,IAAWS,EAAAA,EAAAA,GAAgBd,EAAa,GAAGa,OAAOZ,EAAW,sBAAuBI,GAAWL,GAAclD,GAC9PqD,MAAOA,EACPG,KAAMA,GACQhH,EAAAA,cAAoB,MAAO,CACzCwD,UAAW,GAAG+D,OAAOZ,EAAW,iBAC/BG,IAVM,IAWX,IACAL,EAAapG,YAAc,eAC3B,QCrCIoH,EAAY,CAAC,YAAa,KAAM,QAAS,YAAa,cAAe,WAAY,WAAY,uBAAwB,YAAa,cAAe,aAAc,QAAS,eASxKC,EAA6B,SAAUC,IACzCC,EAAAA,EAAAA,GAAUF,EAAeC,GAEzB,IAAIE,GAASC,EAAAA,EAAAA,GAAaJ,GAE1B,SAASA,IACP,IAAIK,GAEJC,EAAAA,EAAAA,GAAgB9E,KAAMwE,GAEtB,IAAK,IAAIO,EAAOC,UAAUlH,OAAQmH,EAAO,IAAIlH,MAAMgH,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/ED,EAAKC,GAAQF,UAAUE,GAoDzB,OAjDAL,EAAQF,EAAOQ,KAAKC,MAAMT,EAAQ,CAAC3E,MAAMqE,OAAOY,KAE1CI,YAAc,WAClB,IAAIC,EAAcT,EAAMjI,MACpByI,EAAcC,EAAYD,YAC1BE,EAAWD,EAAYC,SAEA,oBAAhBF,GACTA,EAAYE,EAEhB,EAEAV,EAAMW,eAAiB,SAAUC,GACjB,UAAVA,EAAEC,KAAiC,KAAdD,EAAEE,SAA8B,KAAZF,EAAEG,OAC7Cf,EAAMQ,aAEV,EAEAR,EAAMgB,WAAa,WACjB,IAAIC,EAAejB,EAAMjI,MACrBmJ,EAAYD,EAAaC,UACzBC,EAAaF,EAAaE,WAC1BvC,EAAYqC,EAAarC,UACzBwC,EAAcH,EAAaG,YAE/B,IAAKF,EACH,OAAO,KAGT,IAAIG,EAAiC,oBAAfF,EAA4BA,EAAWnB,EAAMjI,OAAsBE,EAAAA,cAAoB,IAAK,CAChHwD,UAAW,UAEb,OAAO4F,GAAyBpJ,EAAAA,cAAoB,MAAO,CACzDwD,UAAW,GAAG+D,OAAOZ,EAAW,gBAChC0C,QAAyB,WAAhBF,GAA4C,SAAhBA,EAAyBpB,EAAMQ,YAAc,MACjFa,EACL,EAEArB,EAAMuB,YAAc,WAClB,IAAIC,EAAexB,EAAMjI,MACrB0J,EAASD,EAAaC,OACtB7C,EAAY4C,EAAa5C,UACzBwC,EAAcI,EAAaJ,YAC/B,OAAoBnJ,EAAAA,cAAoB,OAAQ,CAC9CwD,UAAW,GAAG+D,OAAOZ,EAAW,gBAChC0C,QAAyB,WAAhBF,EAA2BpB,EAAMQ,YAAc,MACvDiB,EACL,EAEOzB,CACT,CAmFA,OAjFA0B,EAAAA,EAAAA,GAAa/B,EAAe,CAAC,CAC3BkB,IAAK,wBACLc,MAAO,SAA+BC,GACpC,OAAQC,IAAa1G,KAAKpD,MAAO6J,EACnC,GACC,CACDf,IAAK,SACLc,MAAO,WACL,IAAIG,EAAaC,EAEbC,EAAe7G,KAAKpD,MACpB0D,EAAYuG,EAAavG,UACzBwG,EAAKD,EAAaC,GAClBnD,EAAQkD,EAAalD,MACrBF,EAAYoD,EAAapD,UACzBsD,EAAcF,EAAaE,YAC3BnD,EAAWiD,EAAajD,SACxBC,EAAWgD,EAAahD,SACxBmD,EAAuBH,EAAaG,qBACpCC,EAAYJ,EAAaI,UACzBvD,EAAcmD,EAAanD,YAC3BwD,EAAaL,EAAaK,WAC1BC,EAAQN,EAAaM,MACrBlB,EAAcY,EAAaZ,YAC3BmB,GAAOC,EAAAA,EAAAA,GAAyBR,EAActC,GAE9C+C,EAA2B,aAAhBrB,EACXsB,EAAoC,WAAhBtB,EACpBuB,EAAkC,SAAhBvB,EAClBwB,EAAUC,KAAYf,EAAc,CAAC,GAAGrC,EAAAA,EAAAA,GAAgBqC,EAAa,GAAGtC,OAAOZ,EAAW,UAAU,IAAOa,EAAAA,EAAAA,GAAgBqC,EAAa,GAAGtC,OAAOZ,EAAW,gBAAiBI,IAAWS,EAAAA,EAAAA,GAAgBqC,EAAa,GAAGtC,OAAOZ,EAAW,kBAAmB6D,GAAWX,GAAcrG,GAIvRqH,EAAc,CAChBrH,UAJcoH,IAAW,GAAGrD,OAAOZ,EAAW,YAAamD,EAAe,CAAC,GAAGtC,EAAAA,EAAAA,GAAgBsC,EAAcG,EAAaA,IAAczC,EAAAA,EAAAA,GAAgBsC,EAAc,GAAGvC,OAAOZ,EAAW,4BAA6B8D,IAAoBjD,EAAAA,EAAAA,GAAgBsC,EAAc,GAAGvC,OAAOZ,EAAW,0BAA2B+D,GAAkBZ,IAK3U,gBAAiB/C,EACjB,gBAAiByD,EACjBM,WAAY5H,KAAKwF,gBAGd+B,GAAsBC,IACzBG,EAAYxB,QAAUnG,KAAKqF,YAC3BsC,EAAY7D,KAAOmD,EAAY,MAAQ,SACvCU,EAAYE,SAAWP,GAAY,EAAI,GAGzC,IAAIQ,EAAyB,OAAVX,QAA4BY,IAAVZ,GAAwC,mBAAVA,EAOnE,cALOC,EAAKd,cACLc,EAAK7B,gBACL6B,EAAK/B,mBACL+B,EAAKrB,iBACLqB,EAAKpB,WACQlJ,EAAAA,cAAoB,OAAOkL,EAAAA,EAAAA,GAAS,CAAC,EAAGZ,EAAM,CAChE9G,UAAWmH,EACX9D,MAAOA,EACPmD,GAAIA,IACWhK,EAAAA,cAAoB,MAAO6K,EAAa3H,KAAK6F,aAAc7F,KAAKoG,cAAe0B,GAA6BhL,EAAAA,cAAoB,MAAO,CACtJwD,UAAW,GAAG+D,OAAOZ,EAAW,WAC/B0D,IAAsBrK,EAAAA,cAAoBmL,EAAAA,SAAWD,EAAAA,EAAAA,GAAS,CAC/DE,QAASrE,EACTsE,gBAAiB,GAAG9D,OAAOZ,EAAW,oBACrCyD,EAAY,CACbxD,YAAaA,EACb0E,cAAepB,KACb,SAAUxF,EAAM3E,GAClB,IAAIwL,EAAkB7G,EAAKlB,UACvBgI,EAAc9G,EAAKmC,MACvB,OAAoB7G,EAAAA,cAAoByG,EAAc,CACpD1G,IAAKA,EACL4G,UAAWA,EACXnD,UAAW+H,EACX1E,MAAO2E,EACPzE,SAAUA,EACVH,YAAaA,EACbI,KAAMmD,EAAY,WAAa,MAC9BrD,EACL,IACF,KAGKY,CACT,CApJiC,CAoJ/B1H,EAAAA,WAEF0H,EAAc+D,aAAe,CAC3BxC,WAAW,EACXlC,UAAU,EACVwB,YAAa,WAAwB,EACrC0B,YAAa,GACbrD,aAAa,GAEf,QC9JA,SAAS8E,EAAmBC,GAC1B,IAAIC,EAAmBD,EAEvB,IAAK1K,MAAMC,QAAQ0K,GAAmB,CACpC,IAAIC,GAAgBC,EAAAA,EAAAA,GAAQF,GAE5BA,EAAqC,WAAlBC,GAAgD,WAAlBA,EAA6B,CAACD,GAAoB,EACrG,CAEA,OAAOA,EAAiBzH,KAAI,SAAUyE,GACpC,OAAOmD,OAAOnD,EAChB,GACF,CAEA,IAAIoD,EAAwB,SAAUrE,IACpCC,EAAAA,EAAAA,GAAUoE,EAAUrE,GAEpB,IAAIE,GAASC,EAAAA,EAAAA,GAAakE,GAE1B,SAASA,EAASC,GAChB,IAAIlE,GAEJC,EAAAA,EAAAA,GAAgB9E,KAAM8I,IAEtBjE,EAAQF,EAAOQ,KAAKnF,KAAM+I,IAEpBC,YAAc,SAAUtD,GAC5B,IAAI+C,EAAY5D,EAAMjG,MAAM6J,UAE5B,GAAI5D,EAAMjI,MAAMqK,UACdwB,EAAYA,EAAU,KAAO/C,EAAM,GAAK,CAACA,OACpC,CAEL,IAAIuD,GADJR,GAAYS,EAAAA,EAAAA,GAAmBT,IACTU,QAAQzD,GACfuD,GAAS,EAItBR,EAAUW,OAAOH,EAAO,GAExBR,EAAUnH,KAAKoE,EAEnB,CAEAb,EAAMwE,aAAaZ,EACrB,EAEA5D,EAAMyE,YAAc,SAAUC,EAAON,GACnC,IAAKM,EAAO,OAAO,KACnB,IAAId,EAAY5D,EAAMjG,MAAM6J,UACxBnD,EAAcT,EAAMjI,MACpB6G,EAAY6B,EAAY7B,UACxByD,EAAa5B,EAAY4B,WACzBD,EAAY3B,EAAY2B,UACxBuC,EAA2BlE,EAAY0B,qBACvChB,EAAaV,EAAYU,WACzBC,EAAcX,EAAYW,YAE1BP,EAAM6D,EAAM7D,KAAOmD,OAAOI,GAC1BQ,EAAeF,EAAM3M,MACrB0J,EAASmD,EAAanD,OACtBS,EAAc0C,EAAa1C,YAC3BC,EAAuByC,EAAazC,qBACpC0C,EAAmBD,EAAaxD,YAShC0D,EAAwC,OAArBD,QAAkD,IAArBA,EAA8BA,EAAmBzD,EACjGrJ,EAAQ,CACV8I,IAAKA,EACLH,SAAUG,EACVY,OAAQA,EACRS,YAAaA,EACblD,SAZEoD,EACSwB,EAAU,KAAO/C,EAEjB+C,EAAUU,QAAQzD,IAAQ,EAUrCjC,UAAWA,EACXuD,qBAA+C,OAAzBA,QAA0D,IAAzBA,EAAkCA,EAAuBwC,EAChHtC,WAAYA,EACZD,UAAWA,EACXrD,SAAU2F,EAAM3M,MAAMgH,SACtByB,YAAkC,aAArBsE,EAAkC,KAAO9E,EAAMmE,YAC5DhD,WAAYA,EACZC,YAAa0D,GAGf,MAA0B,kBAAfJ,EAAMK,KACRL,GAGTM,OAAOC,KAAKlN,GAAOmN,SAAQ,SAAUC,GACJ,qBAApBpN,EAAMoN,WACRpN,EAAMoN,EAEjB,IACoBlN,EAAAA,aAAmByM,EAAO3M,GAChD,EAEAiI,EAAMoF,SAAW,WACf,IAAIrG,EAAWiB,EAAMjI,MAAMgH,SAC3B,OAAOsG,EAAAA,EAAAA,GAAQtG,GAAU3C,IAAI4D,EAAMyE,YACrC,EAEAzE,EAAMwE,aAAe,SAAUZ,GACvB,cAAe5D,EAAMjI,OACzBiI,EAAMsF,SAAS,CACb1B,UAAWA,IAIf5D,EAAMjI,MAAMwN,SAASvF,EAAMjI,MAAMqK,UAAYwB,EAAU,GAAKA,EAC9D,EAEA,IAAI4B,EAAatB,EAAON,UAEpBC,EADmBK,EAAOuB,iBAU9B,MAPI,cAAevB,IACjBL,EAAmB2B,GAGrBxF,EAAMjG,MAAQ,CACZ6J,UAAWD,EAAmBE,IAEzB7D,CACT,CAqCA,OAnCA0B,EAAAA,EAAAA,GAAauC,EAAU,CAAC,CACtBpD,IAAK,wBACLc,MAAO,SAA+BC,EAAW8D,GAC/C,OAAQ7D,IAAa1G,KAAKpD,MAAO6J,KAAeC,IAAa1G,KAAKpB,MAAO2L,EAC3E,GACC,CACD7E,IAAK,SACLc,MAAO,WACL,IAAIG,EAEAb,EAAe9F,KAAKpD,MACpB6G,EAAYqC,EAAarC,UACzBnD,EAAYwF,EAAaxF,UACzBqD,EAAQmC,EAAanC,MACrBsD,EAAYnB,EAAamB,UACzBuD,EAAoB9C,KAAYf,EAAc,CAAC,GAAGrC,EAAAA,EAAAA,GAAgBqC,EAAalD,GAAW,IAAOa,EAAAA,EAAAA,GAAgBqC,EAAarG,IAAaA,GAAYqG,IAC3J,OAAoB7J,EAAAA,cAAoB,MAAO,CAC7CwD,UAAWkK,EACX7G,MAAOA,EACPG,KAAMmD,EAAY,UAAY,MAC7BjH,KAAKiK,WACV,IACE,CAAC,CACHvE,IAAK,2BACLc,MAAO,SAAkCC,GACvC,IAAIgE,EAAW,CAAC,EAMhB,MAJI,cAAehE,IACjBgE,EAAShC,UAAYD,EAAmB/B,EAAUgC,YAG7CgC,CACT,KAGK3B,CACT,CAxJ4B,CAwJ1BhM,EAAAA,WAEFgM,EAASP,aAAe,CACtB9E,UAAW,cACX2G,SAAU,WAAqB,EAC/BnD,WAAW,EACXD,sBAAsB,GAExB8B,EAAS4B,MAAQlG,EACjB,IC7LA,ED6LA,E,GAAA,EC5LYsE,M,0CCWRA,EAAW,SAAkBlM,GAC/B,IAAI+J,EACAgE,EAAoB7N,EAAAA,WAAiB8N,EAAAA,IACvCC,EAAeF,EAAkBE,aACjCC,EAAYH,EAAkBG,UAC5BC,EAAqBnO,EAAM6G,UAC7BuH,EAAmBpO,EAAM0D,UACzBA,OAAiC,IAArB0K,EAA8B,GAAKA,EAC/CC,EAAkBrO,EAAMsO,SACxBA,OAA+B,IAApBD,GAAoCA,EAC/CE,EAAQvO,EAAMuO,MACdC,EAAwBxO,EAAMyO,mBAC9BA,OAA+C,IAA1BD,EAAmC,QAAUA,EAChE3H,EAAYoH,EAAa,WAAYE,GAIrCO,EAA2BxO,EAAAA,SAAc,WAC3C,MAA2B,SAAvBuO,EACK,QAEqB,UAAvBA,EAAiC,MAAQA,CAClD,GAAG,CAACA,IAaAb,EAAoB9C,IAAW,GAAGrD,OAAOZ,EAAW,mBAAmBY,OAAOiH,IAA4B3E,EAAc,CAAC,GAAGrC,EAAAA,EAAAA,GAAgBqC,EAAa,GAAGtC,OAAOZ,EAAW,gBAAiByH,IAAW5G,EAAAA,EAAAA,GAAgBqC,EAAa,GAAGtC,OAAOZ,EAAW,QAAuB,QAAdqH,IAAsBxG,EAAAA,EAAAA,GAAgBqC,EAAa,GAAGtC,OAAOZ,EAAW,YAAa0H,GAAQxE,GAAcrG,GAChX4G,GAAac,EAAAA,EAAAA,IAASA,EAAAA,EAAAA,GAAS,CAAC,EAAGuD,EAAAA,IAAiB,CACtDC,cAAc,EACdrD,gBAAiB,GAAG9D,OAAOZ,EAAW,qBAoBxC,OAAoB3G,EAAAA,cAAoB2O,GAAYzD,EAAAA,EAAAA,GAAS,CAC3Dd,WAAYA,GACXtK,EAAO,CACRoJ,WAtCqB,WACrB,IAAI0F,EAAa1G,UAAUlH,OAAS,QAAsBiK,IAAjB/C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAClFgB,EAAapJ,EAAMoJ,WACnB/I,EAAO+I,EAAaA,EAAW0F,GAA2B5O,EAAAA,cAAoB6O,EAAAA,EAAe,CAC/FC,OAAQF,EAAW7H,SAAW,QAAKkE,IAErC,OAAO8D,EAAAA,EAAAA,IAAa5O,GAAM,WACxB,MAAO,CACLqD,UAAWoH,IAAWzK,EAAKL,MAAM0D,UAAW,GAAG+D,OAAOZ,EAAW,WAErE,GACF,EA4BEA,UAAWA,EACXnD,UAAWkK,IAvBE,WACb,IAAI5G,EAAWhH,EAAMgH,SACrB,OAAOsG,EAAAA,EAAAA,GAAQtG,GAAU3C,KAAI,SAAUsI,EAAON,GAC5C,IAAI6C,EACJ,GAA2B,QAAtBA,EAAKvC,EAAM3M,aAA0B,IAAPkP,OAAgB,EAASA,EAAGxE,SAAU,CACvE,IAAI5B,EAAM6D,EAAM7D,KAAOmD,OAAOI,GAC1BQ,EAAeF,EAAM3M,MACvB0K,EAAWmC,EAAanC,SACxBrB,EAAcwD,EAAaxD,YACzB8F,GAAa/D,EAAAA,EAAAA,IAASA,EAAAA,EAAAA,GAAS,CAAC,GAAGgE,EAAAA,EAAAA,GAAKzC,EAAM3M,MAAO,CAAC,cAAe,CACvE8I,IAAKA,EACLO,YAA6B,OAAhBA,QAAwC,IAAhBA,EAAyBA,EAAcqB,EAAW,gBAAaS,IAEtG,OAAO8D,EAAAA,EAAAA,IAAatC,EAAOwC,EAC7B,CACA,OAAOxC,CACT,GACF,CAOIU,GACN,EACAnB,EAAS4B,MCxEW,SAAuB9N,GAEzC,IACEiO,EADsB/N,EAAAA,WAAiB8N,EAAAA,IACNC,aAC/BE,EAAqBnO,EAAM6G,UAC7BuH,EAAmBpO,EAAM0D,UACzBA,OAAiC,IAArB0K,EAA8B,GAAKA,EAC/CiB,EAAmBrP,EAAMmJ,UACzBA,OAAiC,IAArBkG,GAAqCA,EAC/CxI,EAAYoH,EAAa,WAAYE,GACrCmB,EAAyBxE,KAAWpD,EAAAA,EAAAA,GAAgB,CAAC,EAAG,GAAGD,OAAOZ,EAAW,cAAesC,GAAYzF,GAC5G,OAAoBxD,EAAAA,cAAoB2O,EAAAA,OAAkBzD,EAAAA,EAAAA,GAAS,CAAC,EAAGpL,EAAO,CAC5E6G,UAAWA,EACXnD,UAAW4L,IAEf,ED0DA,IE/EA,EF+EA,C,0IGrEEC,EAAO,EACPC,EAAO,EAcPC,EAAU,GACVC,EAAO,GAGPC,EAAU,GAEZ,SAASC,EAAQC,GACb,OAAOA,GAAM,IAAiBA,GAAM,IAAiBA,GAAM,IAAiBA,GAAM,KAAkBA,GAAM,IAAkBA,GAAM,EACtI,CAIA,SAASC,EAAYC,EAAOC,EAAUC,GAClC,IAAK,IAAIC,GAAU,IAAS,CACxB,GAAIH,EAAMI,KAAO,EACb,OACJ,GAAIJ,EAAMI,MAAQH,IAAaE,EAE3B,YADAH,EAAMK,UAGVF,EAAUD,IAAqBC,GAAyB,IAAdH,EAAMI,KAChDJ,EAAMK,SACV,CACJ,CAYA,SAASC,EAASN,EAAOO,GACrB,KACsB,IAAdP,EAAMI,MAAmCP,EAAQG,EAAMI,OAE7C,MAAVG,IACAA,GAAUrE,OAAOsE,aAAaR,EAAMI,OACxCJ,EAAMK,UAEV,OAAOE,CACX,CAWA,SAASE,EAAST,EAAOC,GACrB,KAAqB,IAAdD,EAAMI,MAAwC,IAAdJ,EAAMI,MACzCJ,EAAMK,UACNJ,GAAYD,EAAMI,MAAQH,GAC1BD,EAAMK,SACd,CACA,SAASK,EAAWV,EAAOW,GACvB,OAAS,CACL,GAAkB,IAAdX,EAAMI,KAAyB,CAC/B,GAAIO,EACA,MACJA,GAAS,CACb,MACK,GAAIX,EAAMI,KAAO,IAAkBJ,EAAMI,KAAO,GACjD,MAEJJ,EAAMK,SACV,CACA,GAAkB,IAAdL,EAAMI,MAAuC,KAAdJ,EAAMI,KAIrC,IAHAJ,EAAMK,UACY,IAAdL,EAAMI,MAA0C,IAAdJ,EAAMI,MACxCJ,EAAMK,UACHL,EAAMI,MAAQ,IAAkBJ,EAAMI,MAAQ,IACjDJ,EAAMK,SAElB,CACA,SAASO,EAAIZ,GACT,OAASA,EAAMI,KAAO,GAAmB,IAAdJ,EAAMI,OAC7BJ,EAAMK,SACd,CACA,SAASQ,EAASf,EAAIgB,GAClB,IAAK,IAAI3M,EAAI,EAAGA,EAAI2M,EAAI3P,OAAQgD,IAC5B,GAAI2M,EAAIC,WAAW5M,IAAM2L,EACrB,OAAO,EACf,OAAO,CACX,CACA,IAAMkB,EAAQ,UACd,SAASC,EAASA,EAAUC,EAAOC,GAC/B,IAAIZ,EAASrD,OAAOkE,OAAO,MAC3Bb,EAAa,KAAIA,EAAc,MAAIf,EACnCe,EAAa,KAAIA,EAAgB,QAAId,EAAK,IACR,EADQ,UAC3BwB,EAASI,MAAM,MAAI,IAAlC,IAAK,EAAL,qBACI,KADKC,EAAE,QACHA,IACAf,EAAOe,GAAM5B,EAAO,CAAC,mCACE,EADF,UACdwB,EAAMG,MAAM,MAAI,IAA/B,IAAK,EAAL,qBACI,KADKE,EAAE,QACHA,IACAhB,EAAOgB,GAAM5B,EAAI,CAAC,mCACe,EADf,WACVwB,GAAW,IAAIE,MAAM,MAAI,IAAzC,IAAK,EAAL,qBACI,KADKC,EAAE,QACHA,IACAf,EAAOe,GAAM1B,EAAO,CAAC,+BAC7B,OAAOW,CACX,CACA,IAAMiB,EAAW,gMACXC,EAAc,k6DACdC,EAAW,CACbxB,kBAAkB,EAClByB,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,qBAAqB,EACrBC,2BAA2B,EAC3BC,qBAAqB,EACrBC,kBAAkB,EAClBC,cAAc,EACdC,cAAe,gBACfC,WAAY,IACZC,iBAAkB,IAClBC,MAAoBrB,EAASQ,EAAaD,IAU9C,SAASe,EAAUC,GACf,OAAO,IAAIC,EAAAA,IAAkB,SAAAzC,GACzB,IAAIb,EA7HQW,EA8HNM,EAASJ,EAATI,KAEN,GADAJ,EAAMK,UACFQ,EAAST,EAAMY,GAAQ,CACvB,KAAOH,EAASb,EAAMI,KAAMY,IACxBhB,EAAMK,UACVL,EAAM0C,YAhKC,GAiKX,MACK,GAAY,IAARtC,GAA4C,IAAdJ,EAAMI,MAA8BoC,EAAET,2BAtHrF,SAAiC/B,GAC7B,OAAS,CACL,GAAIA,EAAMI,KAAO,GAAKJ,EAAM2C,KAAK,GAAK,EAClC,OACJ,GAAkB,IAAd3C,EAAMI,MAA+C,IAAjBJ,EAAM2C,KAAK,GAE/C,YADA3C,EAAMK,QAAQ,GAGlBL,EAAMK,SACV,CACJ,CA6GYuC,CAAwB5C,GACxBA,EAAM0C,YAjKL,QAmKA,GAAY,IAARtC,GAA2C,IAARA,GAAmCoC,EAAEV,oBAC7E/B,EAAYC,EAAOI,EAAMoC,EAAEtC,kBAC3BF,EAAM0C,YArKL,QAuKA,GAAY,IAARtC,GAA4BoC,EAAEb,cAC3B,IAARvB,GAA2C,IAAdJ,EAAMI,MAA6BoC,EAAEX,cAClEjB,EAAIZ,GACJA,EAAM0C,YA5KF,QA8KH,GAAY,IAARtC,GAA0C,IAAdJ,EAAMI,MACrCoC,EAAEZ,kBAAqC,IAAjB5B,EAAM2C,KAAK,GAIlC,GAAY,IAARvC,GAA2C,IAAdJ,EAAMI,KAA0B,CAClEJ,EAAMK,UACN,IAAK,IAAIwC,GAAQ,EAAGC,EAAQ,IACpB9C,EAAMI,KAAO,IAGjB,GADAJ,EAAMK,UACM,IAARwC,GAA0C,IAAd7C,EAAMI,KAA2B,CAE7D,OADA0C,EACY,CACR9C,EAAMK,UACN,KACJ,CACAwC,GAAQ,CACZ,MACiB,IAARA,GAA2C,IAAd7C,EAAMI,MACxC0C,IACAD,GAAQ,GAGRA,EAAO7C,EAAMI,KAGrBJ,EAAM0C,YAxMD,EAyMT,MACK,GAAa,KAARtC,GAAkC,IAARA,GAAwC,IAAdJ,EAAMI,KAI/D,GAAa,KAARA,GAAkC,IAARA,GAAwC,IAAdJ,EAAMI,OAChEoC,EAAEN,aAKD,GAAY,IAAR9B,GAAkCoC,EAAEN,aACzC,IAAK,IAAI/N,EAAI,GAAIA,IAAK,CAClB,GAAkB,IAAd6L,EAAMI,MAAmCjM,EAAI,EAAG,CAChD6L,EAAMK,UACNN,EAAYC,EAAO,GAAyBwC,EAAEtC,kBAC9CF,EAAM0C,YAxNb,GAyNO,KACJ,CACA,IAAK7C,EAAQG,EAAMI,MACf,MACJJ,EAAMK,SACV,MAEC,GAAY,IAARD,EACLJ,EAAM0C,YA7NP,QA+NE,GAAY,IAARtC,EACLJ,EAAM0C,YA/NP,QAiOE,GAAY,KAARtC,EACLJ,EAAM0C,YAjOP,QAmOE,GAAY,KAARtC,EACLJ,EAAM0C,YAnOP,SAqOE,GAAY,IAARtC,EACLJ,EAAM0C,YArOL,SAuOA,GAAY,IAARtC,EACLJ,EAAM0C,YAvOL,SAyOA,GAAY,IAARtC,EACLJ,EAAM0C,YAzOT,SA2OI,GAAIF,EAAER,qBAA+B,IAAR5B,GAAwC,IAAdJ,EAAMI,KAC9DJ,EAAMK,UACNI,EAAST,GACTA,EAAM0C,YArOT,SAuOI,GAAa,IAARtC,GAAiC,IAARA,GAAyC,IAAdJ,EAAMI,MAAiD,IAAdJ,EAAMI,MAYxG,GAAY,IAARA,IAAyC,KAAdJ,EAAMI,MAAwC,IAAdJ,EAAMI,QAC7D,KAARA,GAAkC,IAARA,IAAwC,IAAdJ,EAAMI,KAAiC,CAC5F,IAAI2C,EAAuB,IAAd/C,EAAMI,KAEnB,IADAJ,EAAMK,WA/OEP,EAgPUE,EAAMI,OA/OnB,IAAkBN,GAAM,IAAkBA,GAAM,IAAiBA,GAAM,KAAkBA,GAAM,IAAiBA,GAAM,IAgPvHE,EAAMK,UACN0C,GAAwB,IAAd/C,EAAMI,MAChBJ,EAAMK,UACVL,EAAM0C,YA7QP,EA8QH,MACK,GAAY,IAARtC,GAA2BJ,EAAMI,MAAQ,IAAkBJ,EAAMI,MAAQ,GAC9EM,EAAWV,GAAO,GAClBA,EAAM0C,YAjRP,QAmRE,GAAY,IAARtC,EACLJ,EAAM0C,YA1QV,SA4QK,GAAItC,GAAQ,IAAkBA,GAAQ,GACvCM,EAAWV,GAAO,GAClBA,EAAM0C,YAxRP,QA0RE,GAAI7B,EAAST,EAAMoC,EAAEL,eAAgB,CACtC,KAAOtB,EAASb,EAAMI,KAAMoC,EAAEL,gBAC1BnC,EAAMK,UACVL,EAAM0C,YAlRL,GAmRL,MACK,GAAI7B,EAAST,EAAMoC,EAAEJ,YAClBpC,EAAMI,MAAQA,GACdJ,EAAMK,UApOtB,SAA0BL,GACtB,GAAkB,IAAdA,EAAMI,MAAiD,IAAdJ,EAAMI,MAAiD,IAAdJ,EAAMI,KAA8B,CACtH,IAAI4C,EAAQhD,EAAMI,KAClBJ,EAAMK,UACNN,EAAYC,EAAOgD,GAAO,EAC9B,MAEI1C,EAASN,EAEjB,CA4NYiD,CAAiBjD,GACjBA,EAAM0C,YAtRH,SAwRF,GAAI7B,EAAST,EAAMoC,EAAEH,kBACtBtC,EAAYC,EAAOI,GAAM,GACzBJ,EAAM0C,YAxRG,SA0RR,GAAY,IAARtC,GAAqC,IAARA,EAClCJ,EAAM0C,YA9RF,SAgSH,GAAI7C,EAAQO,GAAO,CACpB,IAAI8C,EAAO5C,EAASN,EAAO9D,OAAOsE,aAAaJ,IAC/CJ,EAAM0C,YAA0B,IAAd1C,EAAMI,KAhSrB,GAgSmG,QAAtCjB,EAAKqD,EAAEF,MAAMY,EAAKC,sBAAmC,IAAPhE,EAAgBA,EAhS3H,GAiSP,MAtD+I,CAC3I,IAAMiE,EAAapD,EAAMI,KACzBJ,EAAMK,UACFmC,EAAEP,kBACFlC,EAAYC,EAAOoD,EAAYZ,EAAEtC,kBACjCF,EAAM0C,YA3OZ,MA8OMjC,EAAST,EAAOoD,GAChBpD,EAAM0C,YAhPb,IAkPD,MAtDI1C,EAAMK,UACNN,EAAYC,EAAO,GAAyBwC,EAAEtC,kBAC9CF,EAAM0C,YAjNL,QA0MD1C,EAAMK,UACNN,EAAYC,EAAO,IAAyB,QA7B5CY,EAAIZ,GACJA,EAAM0C,YAjLF,EAmTZ,GACJ,CACA,IAAMW,EAAsBd,EAAUb,GAGhC4B,EAAwBC,EAAAA,GAAAA,YAAqB,CACjDC,QAAS,GACTC,OAAQ,yYACRC,UAAW,yhBACXC,KAAM,8HACNC,UAAW,4OACXC,QAAS,GACTC,aAAc,CAAC,EAAE,EAAE,GACnBC,gBAAiB,EACjBC,UAAW,OACXC,WAAY,CAAC,EAAGZ,GAChBa,SAAU,CAAC,OAAS,CAAC,EAAE,KACvBC,UAAW,IAGb,SAASC,EAAYC,GAEjB,IADA,IAAIC,EAASD,EAAKC,SAASC,OAAOF,EAAK9R,MAAO,GACvC,UAAUiS,KAAKF,EAAOG,OACzBH,EAAOC,OAAOD,EAAO/R,MAAO,GAChC,OAAO+R,EAAOI,IAClB,CACA,SAASC,EAAOhS,EAAK+R,GACjB,IAAIE,EAAOjS,EAAIkS,YAAYH,EAAKnS,KAAMmS,EAAKlS,IACvCuQ,EAAS,kBAAkB+B,KAAKF,GACpC,OAAO7B,EAASA,EAAO,GAAK6B,CAChC,CACA,SAASG,EAAQL,GACb,OAAOA,IAAsB,cAAbA,EAAKD,MAAqC,oBAAbC,EAAKD,KACtD,CACA,SAASO,EAAQrS,EAAKwH,GAClB,GAAe,uBAAXA,EAAGsK,KAA+B,CAElC,IADA,IAAIQ,EAAO,GACFnF,EAAK3F,EAAG+K,WAAYpF,EAAIA,EAAKA,EAAGqF,YACjCJ,EAAQjF,IACRmF,EAAKtQ,KAAKgQ,EAAOhS,EAAKmN,IAC9B,OAAOmF,CACX,CACA,MAAO,CAACN,EAAOhS,EAAKwH,GACxB,CACA,SAASiL,EAAWzS,EAAK+R,GACrB,IAAK,IAAIO,EAAO,KAAM,CAClB,IAAKP,GAAqB,KAAbA,EAAKD,KACd,OAAOQ,EACX,IAAIR,EAAOL,EAAYM,GACvB,IAAKK,EAAQN,GACT,OAAOQ,EACXA,EAAKI,QAAQV,EAAOhS,EAAK8R,IACzBC,EAAON,EAAYK,EACvB,CACJ,CACA,SAASa,EAAcrT,EAAOsT,GAC1B,IAAI9S,GAAM+S,EAAAA,EAAAA,IAAWvT,GAAOwT,aAAaF,GAAW,GAChDG,EAeR,SAAoB/S,EAAKgT,GAErB,IADA,IAAIC,EACKC,EAASF,GAAKC,EAAWC,EAASA,EAAOA,OAAQ,CACtD,IAAKA,EACD,OAAO,KACQ,aAAfA,EAAOpB,OACPmB,EAAYC,EACpB,CAEA,IADA,IAAIH,EAAU,KACLI,EAAOF,EAAUV,WAAYa,GAAU,EAAOC,EAAS,KAAMF,EAAMA,EAAOA,EAAKX,YAAa,CACjG,IAAI7D,EAAkB,WAAbwE,EAAKrB,KAAoB9R,EAAIkS,YAAYiB,EAAKvT,KAAMuT,EAAKtT,IAAI2Q,cAAgB,KAClF8C,EAAQ,KACZ,GAAKF,EAGA,GAAU,MAANzE,GAAc0E,GAAUjB,EAAQe,EAAKX,aAC1Cc,EAAQtB,EAAOhS,EAAKmT,EAAKX,iBAExB,IAAI7D,GAAM4E,EAAQC,IAAI7E,GACvB,MAEK0E,GAAUjB,EAAQe,KACvBG,EAAQtB,EAAOhS,EAAKmT,GACxB,MAVIC,EAAgB,QAANzE,EAWV2E,IACKP,IACDA,EAAUxI,OAAOkE,OAAO,OAC5BsE,EAAQO,GAASjB,EAAQrS,EAAKqT,IAElCA,EAAS,cAAcxB,KAAKsB,EAAKrB,MAAQqB,EAAO,IACpD,CACA,OAAOJ,CACX,CA/CkBU,CAAWnU,EAAMU,IAAKF,GACpC,MAAgB,cAAZA,EAAIgS,MAAoC,oBAAZhS,EAAIgS,MAA0C,WAAZhS,EAAIgS,KAC3D,CAAElS,KAAME,EAAIF,KACfwQ,OAAoB,oBAAZtQ,EAAIgS,KAA6BxS,EAAMU,IAAIkS,YAAYpS,EAAIF,KAAME,EAAIF,KAAO,GAAK,KACzF8T,QAASjB,EAAWnT,EAAMU,IAAKyR,EAAY3R,IAC3CiT,QAAAA,GAEQ,KAAZjT,EAAIgS,KACG,CAAElS,KAAMgT,EAAUxC,OAAQ,KAAMsD,QAASjB,EAAWnT,EAAMU,IAAKF,GAAMiT,QAAAA,GAGrE,CAAEnT,KAAMgT,EAAUxC,OAAQ,KAAMsD,QAAS,GAAIC,OAAO,EAAMZ,QAAAA,EAEzE,CACA,IAAMQ,EAAuB,IAAIK,IAAiB,sFAAsFlF,MAAM,MAuC9I,IAAMmF,EAAO,QAASC,EAAa,oBAC7BC,EAAe,WACjB,cAAc,eACVrT,KAAKsT,KAAO,GACZtT,KAAK4D,cAAWmE,CACpB,CAOC,OAPA,6BACD,SAAMqJ,GACF,IAAIxN,EAAW5D,KAAK4D,WAAa5D,KAAK4D,SAAWiG,OAAOkE,OAAO,OAC/D,OAAOnK,EAASwN,KAAUxN,EAASwN,GAAQ,IAAIiC,EACnD,GAAC,8BACD,SAAiBzJ,GACb,OAAO5J,KAAK4D,SAAWiG,OAAOC,KAAK9J,KAAK4D,UAAU2P,QAAO,SAAAC,GAAC,OAAIA,CAAC,IAAEvS,KAAI,SAAAmQ,GAAI,MAAK,CAAEqC,MAAOrC,EAAMxH,KAAAA,EAAM,IAAK,EAC5G,KAAC,EAXgB,GAqErB,IAAI8J,EAAsBzD,EAAS0D,UAAU,CACzC/W,MAAO,CACUgX,EAAAA,GAAAA,IAAmB,CAC5BC,WAAwBC,EAAAA,EAAAA,QAEfC,EAAAA,GAAAA,IAAiB,CAC1BF,UAAS,SAAC7C,GAAQ,MAAO,CAAE9R,KAAM8R,EAAKa,WAAW1S,GAAIA,GAAI6R,EAAK7R,GAAM,EACpE6U,aAAY,SAAChD,GAAQ,MAAO,CAAE9R,KAAM8R,EAAK9R,KAAO,EAAGC,GAAI6R,EAAK7R,GAAK,EAAK,KAE7D8U,EAAAA,EAAAA,IAAU,CACnB5H,QAAS6H,EAAAA,GAAAA,QACT5H,KAAM4H,EAAAA,GAAAA,SACN3H,QAAsB2H,EAAAA,GAAAA,SAAcA,EAAAA,GAAAA,MACpCC,KAAMD,EAAAA,GAAAA,OACNE,MAAOF,EAAAA,GAAAA,OACP/H,KAAM+H,EAAAA,GAAAA,KACN9H,KAAM8H,EAAAA,GAAAA,KACNG,OAAQH,EAAAA,GAAAA,OACRrL,OAAQqL,EAAAA,GAAAA,OACRI,WAAYJ,EAAAA,GAAAA,KACZK,iBAA+BL,EAAAA,GAAAA,QAAaA,EAAAA,GAAAA,QAC5CM,WAAyBN,EAAAA,GAAAA,QAAaA,EAAAA,GAAAA,MACtCO,YAAaP,EAAAA,GAAAA,YACbF,aAAcE,EAAAA,GAAAA,aACdQ,SAAUR,EAAAA,GAAAA,SACV,mBAAoBA,EAAAA,GAAAA,YACpB,MAAOA,EAAAA,GAAAA,MACP,MAAOA,EAAAA,GAAAA,MACP,MAAOA,EAAAA,GAAAA,mBAObS,EAAU,WACZ,WAIAC,EAIAC,IAAU,eACN7U,KAAK4U,QAAUA,EACf5U,KAAK6U,SAAWA,CACpB,CAqBC,OApBD,+BAGA,WAAkB,OAAO7U,KAAK6U,SAASC,SAAW,IAClD,qBAGA,SAAcC,GACV,IAAI5F,EAhZZ,SAAiB4F,EAAMC,EAAKnH,EAAOC,GAC/B,IAAI8G,EAAU,CAAC,EACf,IAAK,IAAIK,KAAQ5G,EACbuG,EAAQK,IAASF,EAAKG,eAAeD,GAAQF,EAAO1G,GAAU4G,GAGlE,OAFID,IACAJ,EAAQ3F,MAAQrB,EAASoH,EAAKnH,GAAS,GAAIC,IACxC8G,CACX,CAyYgBA,CAAQG,EAAMA,EAAKnH,SAAUmH,EAAKlH,MAAOkH,EAAKjH,SAWtD,OAAO,IAAI6G,EAAWxF,EAVPgG,EAAAA,GAAAA,OAAkB,CAC7B/D,KAAM,MACNsC,OAAQA,EAAOC,UAAU,CACrB/C,WAAY,CAAC,CAAE1R,KAAM8Q,EAAQ7Q,GAAI+P,EAAUC,OAE/CiG,aAAc,CACVC,cAAe,CAAEhW,KAAM,KAAMiW,MAAO,CAAEC,KAAM,KAAMC,MAAO,OACzDC,cAAe,CAAEC,SAAU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,SAIjE,KAAC,EAjCW,GAuChB,SAASC,EAAwBf,GAA4B,IAAnBgB,EAAY,UAAH,8CAC/C,OApFJ,SAA0BhI,EAAUgI,GAChC,IAAIC,EAAchM,OAAOC,KAAK8D,GAAU3M,KAAI,SAAA6U,GAAO,MAAK,CACpDrC,MAAOmC,EAAYE,EAAQC,cAAgBD,EAC3ClM,KAAMgE,EAASkI,IAAYxJ,EAAO,OAASsB,EAASkI,IAAYzJ,EAAU,UAAY,WACtF2J,OAAQ,EACX,IACD,OAAOC,EAAAA,EAAAA,IAAQ,CAAC,mBAAoB,aAAc,SAAU,cAAe,eAAgB,MAAMC,EAAAA,EAAAA,IAAiBL,GACtH,CA6EWM,CAAiBvB,EAAQA,QAAQ3F,MAAO2G,EACnD,CAIA,SAASQ,EAAkBxB,GAA4B,IAAnBgB,EAAY,UAAH,8CACzC,OAAOhB,EAAQC,SAASwB,KAAKjV,GAAG,CAC5BkV,aAAcX,EAAwBf,EAASgB,IAEvD,CAKA,SAASW,EAAuBC,GAC5B,OAAOA,EAAOC,OAlJlB,SAA4BA,EAAQC,EAAQC,EAASC,EAAkBC,GACnE,IAAIC,EAAM,IAAIzD,EACV0D,EAAgBD,EAAIvN,MAAMsN,GAAqB,IACnD,IAAK,IAAIG,KAASP,EAAQ,CACtB,IAAIQ,EAAMD,EAAM7N,QAAQ,MACA8N,GAAO,EAAIH,EAAIvN,MAAMyN,EAAME,MAAM,EAAGD,IAAQF,GAC3BxN,MAAM0N,GAAO,EAAID,EAAME,MAAMD,EAAM,GAAKD,GAChE1D,KAAOmD,EAAOO,GAAO/V,KAAI,SAAAkW,GAAG,MAAkB,iBAAPA,EAAkB,CAAE1D,MAAO0D,EAAKvN,KAAM,YAAeuN,CAAG,GACpH,CAGA,IAAK,IAAIC,KAFTL,EAAczD,MAAQoD,GAAUK,EAAcM,iBAAiB,SAC1DhT,OAAOuS,EAAmBG,EAAcxN,MAAMqN,GAAkBtD,KAAO,IAC1DwD,EAAIlT,SAAU,CAC5B,IAAI6S,EAASK,EAAIvN,MAAM6N,GAClBX,EAAOnD,KAAKxV,SACb2Y,EAAOnD,KAAOmD,EAAOY,iBAAiB,QAC9C,CAEA,OADAP,EAAIxD,KAAOyD,EAAczD,KAAKjP,OAAOsS,GAAWG,EAAIO,iBAAiB,SAC9D,SAACC,GACJ,MAAgDrF,EAAcqF,EAAQ1Y,MAAO0Y,EAAQlY,KAA/E4T,EAAO,EAAPA,QAAS9T,EAAI,EAAJA,KAAMwQ,EAAM,EAANA,OAAQuD,EAAK,EAALA,MAAOZ,EAAO,EAAPA,QACpC,GAAIY,IAAUqE,EAAQC,SAClB,OAAO,KACPlF,GAA6B,GAAlBW,EAAQlV,SACnBkV,EAAUX,EAAQW,EAAQ,KAAOA,GACrC,IACwB,EADpBwE,EAAQV,EAAI,UACC9D,GAAO,IAAxB,IAAK,EAAL,qBAA0B,CACtB,IADsB,IAAjB5B,EAAI,SACDoG,EAAM5T,WAAa4T,EAAM5T,SAASwN,IACtC,GAAIoG,GAASV,EACTU,EAAQT,MACP,IAAIS,GAAST,IAAiBH,EAG/B,OAAO,KAFPY,EAAQA,EAAMjO,MAAMqN,EAET,CAEnBY,EAAQA,EAAMjO,MAAM6H,EACxB,CAAC,+BACD,IAtDuBzB,EAAOkG,EAsD1B4B,EAAa/H,GAAU4H,EAAQ1Y,MAAM8Y,SAASJ,EAAQlY,IAAKkY,EAAQlY,IAAM,IAAMsQ,EAC/E1O,EAAUwW,EAAMlE,KAGpB,OAFIkE,GAASV,GAAOzE,IAChBrR,EAAUA,EAAQqD,OAAOwF,OAAOC,KAAKuI,GAASpR,KAAI,SAAAmQ,GAAI,MAAK,CAAEqC,MAAOrC,EAAMxH,KAAM,WAAY,MACzF,CACH1K,KAAAA,EACAC,GAAIsY,EAAaH,EAAQlY,IAAM,OAAI2I,EACnC/G,SA7DmB2O,EA6DYD,EA7DLmG,EA6Da7U,EA5D1C2O,EAEEkG,EAAY5U,KAAI,SAAA0W,GAAC,OAAK9N,OAAO+N,OAAO/N,OAAO+N,OAAO,CAAC,EAAGD,GAAI,CAAElE,MAAO9D,EAAQgI,EAAElE,MAAQ9D,EAAOvK,WAAO2C,GAAY,IAD3G8N,GA4DHgC,SAAUnI,EAAS0D,EAAaD,EAExC,CACJ,CAoG2B2E,CAAmBtB,EAAOC,OAAQD,EAAOE,OAAQF,EAAOG,QAASH,EAAOuB,aAAcvB,EAAOO,eAC9G,kBAAM,IAAI,CACpB,CAIA,SAASiB,EAAiBxB,GACtB,OAAOA,EAAOC,QAAUD,EAAO5B,SAAWqD,GAAapD,SAASwB,KAAKjV,GAAG,CACpEkV,aAAcC,EAAuBC,KACpC,EACT,CAMA,SAAS0B,IAAiB,IAAb1B,EAAS,UAAH,6CAAG,CAAC,EACf2B,EAAO3B,EAAO5B,SAAWqD,EAC7B,OAAO,IAAIG,EAAAA,GAAgBD,EAAKtD,SAAU,CAACmD,EAAiBxB,GAASJ,EAAkB+B,IAAQ3B,EAAO6B,oBAC1G,CAIA,IAAMJ,EAA2BtD,EAAW2D,OAAO,CAAC,E", "sources": ["../node_modules/@ant-design/icons-svg/es/asn/MenuOutlined.js", "../node_modules/@ant-design/icons/es/icons/MenuOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/RedoOutlined.js", "../node_modules/@ant-design/icons/es/icons/RedoOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/RightCircleOutlined.js", "../node_modules/@ant-design/icons/es/icons/RightCircleOutlined.js", "../node_modules/@ant-design/icons-svg/es/asn/StopOutlined.js", "../node_modules/@ant-design/icons/es/icons/StopOutlined.js", "../node_modules/@uiw/codemirror-extensions-zebra-stripes/src/index.ts", "../node_modules/@uiw/codemirror-themes/src/index.tsx", "../node_modules/@uiw/codemirror-theme-bbedit/src/index.ts", "../node_modules/antd/node_modules/rc-collapse/es/PanelContent.js", "../node_modules/antd/node_modules/rc-collapse/es/Panel.js", "../node_modules/antd/node_modules/rc-collapse/es/Collapse.js", "../node_modules/antd/node_modules/rc-collapse/es/index.js", "../node_modules/antd/es/collapse/Collapse.js", "../node_modules/antd/es/collapse/CollapsePanel.js", "../node_modules/antd/es/collapse/index.js", "../node_modules/@codemirror/lang-sql/dist/index.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar MenuOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"menu\", \"theme\": \"outlined\" };\nexport default MenuOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport MenuOutlinedSvg from \"@ant-design/icons-svg/es/asn/MenuOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar MenuOutlined = function MenuOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: MenuOutlinedSvg\n  }));\n};\nMenuOutlined.displayName = 'MenuOutlined';\nexport default /*#__PURE__*/React.forwardRef(MenuOutlined);", "// This icon file is generated automatically.\nvar RedoOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z\" } }] }, \"name\": \"redo\", \"theme\": \"outlined\" };\nexport default RedoOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport RedoOutlinedSvg from \"@ant-design/icons-svg/es/asn/RedoOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar RedoOutlined = function RedoOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: RedoOutlinedSvg\n  }));\n};\nRedoOutlined.displayName = 'RedoOutlined';\nexport default /*#__PURE__*/React.forwardRef(RedoOutlined);", "// This icon file is generated automatically.\nvar RightCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M666.7 505.5l-246-178A8 8 0 00408 334v46.9c0 10.2 4.9 19.9 13.2 25.9L566.6 512 421.2 617.2c-8.3 6-13.2 15.6-13.2 25.9V690c0 6.5 7.4 10.3 12.7 6.5l246-178c4.4-3.2 4.4-9.8 0-13z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }] }, \"name\": \"right-circle\", \"theme\": \"outlined\" };\nexport default RightCircleOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport RightCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/RightCircleOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar RightCircleOutlined = function RightCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: RightCircleOutlinedSvg\n  }));\n};\nRightCircleOutlined.displayName = 'RightCircleOutlined';\nexport default /*#__PURE__*/React.forwardRef(RightCircleOutlined);", "// This icon file is generated automatically.\nvar StopOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z\" } }] }, \"name\": \"stop\", \"theme\": \"outlined\" };\nexport default StopOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport StopOutlinedSvg from \"@ant-design/icons-svg/es/asn/StopOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar StopOutlined = function StopOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: StopOutlinedSvg\n  }));\n};\nStopOutlined.displayName = 'StopOutlined';\nexport default /*#__PURE__*/React.forwardRef(StopOutlined);", "import { Extension } from '@codemirror/state';\nimport { Facet, RangeSetBuilder } from '@codemirror/state';\nimport { EditorView, Decoration, ViewPlugin, DecorationSet, ViewUpdate } from '@codemirror/view';\n\nconst lineNumber = Facet.define({\n  combine: (values) => {\n    return values.length && Array.isArray(values) ? values.flat() : [];\n  },\n});\n\nconst stepSize = Facet.define({\n  combine: (values) => {\n    return values.length && Array.isArray(values) ? Math.min(...values) : 2;\n  },\n});\n\nlet stripe = Decoration.line({\n  attributes: { class: 'cm-zebra-stripe' },\n});\n\nfunction stripeDeco(view: EditorView) {\n  const step = view.state.facet(stepSize);\n  const num = view.state.facet(lineNumber);\n  const builder = new RangeSetBuilder<Decoration>();\n  for (let { from, to } of view.visibleRanges) {\n    for (let pos = from; pos <= to; ) {\n      let line = view.state.doc.lineAt(pos);\n\n      if (line.number % step === 0 && num.length === 0) {\n        builder.add(line.from, line.from, stripe);\n      }\n      if (num.length > 0 && num.flat().includes(line.number)) {\n        builder.add(line.from, line.from, stripe);\n      }\n      pos = line.to + 1;\n    }\n  }\n  return builder.finish();\n}\n\nconst showStripes = ViewPlugin.fromClass(\n  class {\n    decorations: DecorationSet;\n    constructor(view: EditorView) {\n      this.decorations = stripeDeco(view);\n    }\n\n    update(update: ViewUpdate) {\n      this.decorations = stripeDeco(update.view);\n      // if (update.docChanged || update.viewportChanged) {\n      //   this.decorations = stripeDeco(update.view);\n      // }\n    }\n  },\n  {\n    decorations: (v) => v.decorations,\n  },\n);\n\nconst baseTheme = (opt: Pick<Partial<ZebraStripesOptions>, 'lightColor' | 'darkColor' | 'className'> = {}) => {\n  return EditorView.baseTheme({\n    [`&light .${opt.className}`]: { backgroundColor: opt.lightColor || '#eef6ff' },\n    [`&dark .${opt.className}`]: { backgroundColor: opt.darkColor || '#3a404d' },\n  });\n};\n\nexport type ZebraStripesOptions = {\n  step?: number | null;\n  lightColor?: string;\n  darkColor?: string;\n  /**\n   * @example `[1,[2,6], 10]`\n   */\n  lineNumber?: (number | number[])[] | null;\n  /** @default `cm-zebra-stripe` */\n  className?: string;\n};\n\nconst range = (start: number, stop: number, step: number) =>\n  Array.from({ length: (stop - start) / step + 1 }, (_, i) => start + i * step);\n\nexport function zebraStripes(options: ZebraStripesOptions = {}): Extension {\n  const { className = 'cm-zebra-stripe' } = options;\n  stripe = Decoration.line({\n    attributes: { class: className },\n  });\n  if (options.lineNumber && Array.isArray(options.lineNumber)) {\n    options.step = null;\n    options.lineNumber = options.lineNumber.map((item) => {\n      if (Array.isArray(item) && typeof item[0] === 'number' && typeof item[1] === 'number') {\n        return range(item[0], item[1], 1);\n      }\n      return item;\n    });\n  } else {\n    options.lineNumber = null;\n  }\n  const extensions = [\n    options.lineNumber === null ? [] : lineNumber.of(options.lineNumber || []),\n    options.step === null ? [] : stepSize.of(options.step || 2),\n    showStripes,\n  ];\n  if (className) {\n    const zebraStripeTheme = baseTheme({ lightColor: options.lightColor, darkColor: options.darkColor, className });\n    extensions.push(zebraStripeTheme);\n  }\n  return extensions;\n}\n", "import { EditorView } from '@codemirror/view';\nimport { Extension } from '@codemirror/state';\nimport { HighlightStyle, TagStyle, syntaxHighlighting } from '@codemirror/language';\nimport { StyleSpec } from 'style-mod';\n\nexport interface CreateThemeOptions {\n  /**\n   * Theme inheritance. Determines which styles CodeMirror will apply by default.\n   */\n  theme: Theme;\n  /**\n   * Settings to customize the look of the editor, like background, gutter, selection and others.\n   */\n  settings: Settings;\n  /** Syntax highlighting styles. */\n  styles: TagStyle[];\n}\n\ntype Theme = 'light' | 'dark';\n\nexport interface Settings {\n  /** Editor background. */\n  background?: string;\n  /** Default text color. */\n  foreground?: string;\n  /** Caret color. */\n  caret?: string;\n  /** Selection background. */\n  selection?: string;\n  /** Selection match background. */\n  selectionMatch?: string;\n  /** Background of highlighted lines. */\n  lineHighlight?: string;\n  /** Gutter background. */\n  gutterBackground?: string;\n  /** Text color inside gutter. */\n  gutterForeground?: string;\n  /** Text active color inside gutter. */\n  gutterActiveForeground?: string;\n  /** Gutter right border color. */\n  gutterBorder?: string;\n  /** set editor font */\n  fontFamily?: string;\n}\n\nexport const createTheme = ({ theme, settings = {}, styles = [] }: CreateThemeOptions): Extension => {\n  const themeOptions: Record<string, StyleSpec> = {\n    '.cm-gutters': {},\n  };\n  const baseStyle: StyleSpec = {};\n  if (settings.background) {\n    baseStyle.backgroundColor = settings.background;\n  }\n  if (settings.foreground) {\n    baseStyle.color = settings.foreground;\n  }\n  if (settings.background || settings.foreground) {\n    themeOptions['&'] = baseStyle;\n  }\n\n  if (settings.fontFamily) {\n    themeOptions['&.cm-editor .cm-scroller'] = {\n      fontFamily: settings.fontFamily,\n    };\n  }\n  if (settings.gutterBackground) {\n    themeOptions['.cm-gutters'].backgroundColor = settings.gutterBackground;\n  }\n  if (settings.gutterForeground) {\n    themeOptions['.cm-gutters'].color = settings.gutterForeground;\n  }\n  if (settings.gutterBorder) {\n    themeOptions['.cm-gutters'].borderRightColor = settings.gutterBorder;\n  }\n\n  if (settings.caret) {\n    themeOptions['.cm-content'] = {\n      caretColor: settings.caret,\n    };\n    themeOptions['.cm-cursor, .cm-dropCursor'] = {\n      borderLeftColor: settings.caret,\n    };\n  }\n  let activeLineGutterStyle: StyleSpec = {};\n  if (settings.gutterActiveForeground) {\n    activeLineGutterStyle.color = settings.gutterActiveForeground;\n  }\n  if (settings.lineHighlight) {\n    themeOptions['.cm-activeLine'] = {\n      backgroundColor: settings.lineHighlight,\n    };\n    activeLineGutterStyle.backgroundColor = settings.lineHighlight;\n  }\n  themeOptions['.cm-activeLineGutter'] = activeLineGutterStyle;\n\n  if (settings.selection) {\n    themeOptions[\n      '&.cm-focused .cm-selectionBackground, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection'\n    ] = {\n      backgroundColor: settings.selection,\n    };\n  }\n  if (settings.selectionMatch) {\n    themeOptions['& .cm-selectionMatch'] = {\n      backgroundColor: settings.selectionMatch,\n    };\n  }\n  const themeExtension = EditorView.theme(themeOptions, {\n    dark: theme === 'dark',\n  });\n\n  const highlightStyle = HighlightStyle.define(styles);\n  const extension = [themeExtension, syntaxHighlighting(highlightStyle)];\n\n  return extension;\n};\n\nexport default createTheme;\n", "import { tags as t } from '@lezer/highlight';\nimport { createTheme, CreateThemeOptions } from '@uiw/codemirror-themes';\n\nexport const bbeditInit = (options?: Partial<CreateThemeOptions>) => {\n  const { theme = 'light', settings = {}, styles = [] } = options || {};\n  return createTheme({\n    theme: theme,\n    settings: {\n      background: '#FFFFFF',\n      foreground: '#000000',\n      caret: '#FBAC52',\n      selection: '#FFD420',\n      selectionMatch: '#FFD420',\n      gutterBackground: '#f5f5f5',\n      gutterForeground: '#4D4D4C',\n      gutterBorder: 'transparent',\n      lineHighlight: '#00000012',\n      ...settings,\n    },\n    styles: [\n      { tag: [t.meta, t.comment], color: '#804000' },\n      { tag: [t.keyword, t.strong], color: '#0000FF' },\n      { tag: [t.number], color: '#FF0080' },\n      { tag: [t.string], color: '#FF0080' },\n      { tag: [t.variableName], color: '#006600' },\n      { tag: [t.escape], color: '#33CC33' },\n      { tag: [t.tagName], color: '#1C02FF' },\n      { tag: [t.heading], color: '#0C07FF' },\n      { tag: [t.quote], color: '#000000' },\n      { tag: [t.list], color: '#B90690' },\n      { tag: [t.documentMeta], color: '#888888' },\n      { tag: [t.function(t.variableName)], color: '#0000A2' },\n      { tag: [t.definition(t.typeName), t.typeName], color: '#6D79DE' },\n      ...styles,\n    ],\n  });\n};\n\nexport const bbedit = bbeditInit();\n", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\n/* eslint-disable no-underscore-dangle */\n\n/* eslint-disable react/prop-types */\nimport * as React from 'react';\nimport classnames from 'classnames';\nvar PanelContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classnames;\n\n  var prefixCls = props.prefixCls,\n      forceRender = props.forceRender,\n      className = props.className,\n      style = props.style,\n      children = props.children,\n      isActive = props.isActive,\n      role = props.role;\n\n  var _React$useState = React.useState(isActive || forceRender),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      rendered = _React$useState2[0],\n      setRendered = _React$useState2[1];\n\n  React.useEffect(function () {\n    if (forceRender || isActive) {\n      setRendered(true);\n    }\n  }, [forceRender, isActive]);\n\n  if (!rendered) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: classnames(\"\".concat(prefixCls, \"-content\"), (_classnames = {}, _defineProperty(_classnames, \"\".concat(prefixCls, \"-content-active\"), isActive), _defineProperty(_classnames, \"\".concat(prefixCls, \"-content-inactive\"), !isActive), _classnames), className),\n    style: style,\n    role: role\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content-box\")\n  }, children));\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"className\", \"id\", \"style\", \"prefixCls\", \"headerClass\", \"children\", \"isActive\", \"destroyInactivePanel\", \"accordion\", \"forceRender\", \"openMotion\", \"extra\", \"collapsible\"];\n\n/* eslint-disable react/prop-types */\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport shallowEqual from 'shallowequal';\nimport PanelContent from './PanelContent';\n\nvar CollapsePanel = /*#__PURE__*/function (_React$Component) {\n  _inherits(CollapsePanel, _React$Component);\n\n  var _super = _createSuper(CollapsePanel);\n\n  function CollapsePanel() {\n    var _this;\n\n    _classCallCheck(this, CollapsePanel);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _this.onItemClick = function () {\n      var _this$props = _this.props,\n          onItemClick = _this$props.onItemClick,\n          panelKey = _this$props.panelKey;\n\n      if (typeof onItemClick === 'function') {\n        onItemClick(panelKey);\n      }\n    };\n\n    _this.handleKeyPress = function (e) {\n      if (e.key === 'Enter' || e.keyCode === 13 || e.which === 13) {\n        _this.onItemClick();\n      }\n    };\n\n    _this.renderIcon = function () {\n      var _this$props2 = _this.props,\n          showArrow = _this$props2.showArrow,\n          expandIcon = _this$props2.expandIcon,\n          prefixCls = _this$props2.prefixCls,\n          collapsible = _this$props2.collapsible;\n\n      if (!showArrow) {\n        return null;\n      }\n\n      var iconNode = typeof expandIcon === 'function' ? expandIcon(_this.props) : /*#__PURE__*/React.createElement(\"i\", {\n        className: \"arrow\"\n      });\n      return iconNode && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-expand-icon\"),\n        onClick: collapsible === 'header' || collapsible === 'icon' ? _this.onItemClick : null\n      }, iconNode);\n    };\n\n    _this.renderTitle = function () {\n      var _this$props3 = _this.props,\n          header = _this$props3.header,\n          prefixCls = _this$props3.prefixCls,\n          collapsible = _this$props3.collapsible;\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-header-text\"),\n        onClick: collapsible === 'header' ? _this.onItemClick : null\n      }, header);\n    };\n\n    return _this;\n  }\n\n  _createClass(CollapsePanel, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(nextProps) {\n      return !shallowEqual(this.props, nextProps);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames, _classNames2;\n\n      var _this$props4 = this.props,\n          className = _this$props4.className,\n          id = _this$props4.id,\n          style = _this$props4.style,\n          prefixCls = _this$props4.prefixCls,\n          headerClass = _this$props4.headerClass,\n          children = _this$props4.children,\n          isActive = _this$props4.isActive,\n          destroyInactivePanel = _this$props4.destroyInactivePanel,\n          accordion = _this$props4.accordion,\n          forceRender = _this$props4.forceRender,\n          openMotion = _this$props4.openMotion,\n          extra = _this$props4.extra,\n          collapsible = _this$props4.collapsible,\n          rest = _objectWithoutProperties(_this$props4, _excluded);\n\n      var disabled = collapsible === 'disabled';\n      var collapsibleHeader = collapsible === 'header';\n      var collapsibleIcon = collapsible === 'icon';\n      var itemCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-item\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-active\"), isActive), _defineProperty(_classNames, \"\".concat(prefixCls, \"-item-disabled\"), disabled), _classNames), className);\n      var headerCls = classNames(\"\".concat(prefixCls, \"-header\"), (_classNames2 = {}, _defineProperty(_classNames2, headerClass, headerClass), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-header-collapsible-only\"), collapsibleHeader), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-icon-collapsible-only\"), collapsibleIcon), _classNames2));\n      /** header 节点属性 */\n\n      var headerProps = {\n        className: headerCls,\n        'aria-expanded': isActive,\n        'aria-disabled': disabled,\n        onKeyPress: this.handleKeyPress\n      };\n\n      if (!collapsibleHeader && !collapsibleIcon) {\n        headerProps.onClick = this.onItemClick;\n        headerProps.role = accordion ? 'tab' : 'button';\n        headerProps.tabIndex = disabled ? -1 : 0;\n      }\n\n      var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean'; // https://github.com/ant-design/ant-design/pull/37419#issuecomment-1238812797\n\n      delete rest.header;\n      delete rest.panelKey;\n      delete rest.onItemClick;\n      delete rest.showArrow;\n      delete rest.expandIcon;\n      return /*#__PURE__*/React.createElement(\"div\", _extends({}, rest, {\n        className: itemCls,\n        style: style,\n        id: id\n      }), /*#__PURE__*/React.createElement(\"div\", headerProps, this.renderIcon(), this.renderTitle(), ifExtraExist && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-extra\")\n      }, extra)), /*#__PURE__*/React.createElement(CSSMotion, _extends({\n        visible: isActive,\n        leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n      }, openMotion, {\n        forceRender: forceRender,\n        removeOnLeave: destroyInactivePanel\n      }), function (_ref, ref) {\n        var motionClassName = _ref.className,\n            motionStyle = _ref.style;\n        return /*#__PURE__*/React.createElement(PanelContent, {\n          ref: ref,\n          prefixCls: prefixCls,\n          className: motionClassName,\n          style: motionStyle,\n          isActive: isActive,\n          forceRender: forceRender,\n          role: accordion ? 'tabpanel' : null\n        }, children);\n      }));\n    }\n  }]);\n\n  return CollapsePanel;\n}(React.Component);\n\nCollapsePanel.defaultProps = {\n  showArrow: true,\n  isActive: false,\n  onItemClick: function onItemClick() {},\n  headerClass: '',\n  forceRender: false\n};\nexport default CollapsePanel;", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/* eslint-disable react/prop-types */\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport shallowEqual from 'shallowequal';\nimport CollapsePanel from './Panel';\n\nfunction getActiveKeysArray(activeKey) {\n  var currentActiveKey = activeKey;\n\n  if (!Array.isArray(currentActiveKey)) {\n    var activeKeyType = _typeof(currentActiveKey);\n\n    currentActiveKey = activeKeyType === 'number' || activeKeyType === 'string' ? [currentActiveKey] : [];\n  }\n\n  return currentActiveKey.map(function (key) {\n    return String(key);\n  });\n}\n\nvar Collapse = /*#__PURE__*/function (_React$Component) {\n  _inherits(Collapse, _React$Component);\n\n  var _super = _createSuper(Collapse);\n\n  function Collapse(_props) {\n    var _this;\n\n    _classCallCheck(this, Collapse);\n\n    _this = _super.call(this, _props);\n\n    _this.onClickItem = function (key) {\n      var activeKey = _this.state.activeKey;\n\n      if (_this.props.accordion) {\n        activeKey = activeKey[0] === key ? [] : [key];\n      } else {\n        activeKey = _toConsumableArray(activeKey);\n        var index = activeKey.indexOf(key);\n        var isActive = index > -1;\n\n        if (isActive) {\n          // remove active state\n          activeKey.splice(index, 1);\n        } else {\n          activeKey.push(key);\n        }\n      }\n\n      _this.setActiveKey(activeKey);\n    };\n\n    _this.getNewChild = function (child, index) {\n      if (!child) return null;\n      var activeKey = _this.state.activeKey;\n      var _this$props = _this.props,\n          prefixCls = _this$props.prefixCls,\n          openMotion = _this$props.openMotion,\n          accordion = _this$props.accordion,\n          rootDestroyInactivePanel = _this$props.destroyInactivePanel,\n          expandIcon = _this$props.expandIcon,\n          collapsible = _this$props.collapsible; // If there is no key provide, use the panel order as default key\n\n      var key = child.key || String(index);\n      var _child$props = child.props,\n          header = _child$props.header,\n          headerClass = _child$props.headerClass,\n          destroyInactivePanel = _child$props.destroyInactivePanel,\n          childCollapsible = _child$props.collapsible;\n      var isActive = false;\n\n      if (accordion) {\n        isActive = activeKey[0] === key;\n      } else {\n        isActive = activeKey.indexOf(key) > -1;\n      }\n\n      var mergeCollapsible = childCollapsible !== null && childCollapsible !== void 0 ? childCollapsible : collapsible;\n      var props = {\n        key: key,\n        panelKey: key,\n        header: header,\n        headerClass: headerClass,\n        isActive: isActive,\n        prefixCls: prefixCls,\n        destroyInactivePanel: destroyInactivePanel !== null && destroyInactivePanel !== void 0 ? destroyInactivePanel : rootDestroyInactivePanel,\n        openMotion: openMotion,\n        accordion: accordion,\n        children: child.props.children,\n        onItemClick: mergeCollapsible === 'disabled' ? null : _this.onClickItem,\n        expandIcon: expandIcon,\n        collapsible: mergeCollapsible\n      }; // https://github.com/ant-design/ant-design/issues/20479\n\n      if (typeof child.type === 'string') {\n        return child;\n      }\n\n      Object.keys(props).forEach(function (propName) {\n        if (typeof props[propName] === 'undefined') {\n          delete props[propName];\n        }\n      });\n      return /*#__PURE__*/React.cloneElement(child, props);\n    };\n\n    _this.getItems = function () {\n      var children = _this.props.children;\n      return toArray(children).map(_this.getNewChild);\n    };\n\n    _this.setActiveKey = function (activeKey) {\n      if (!('activeKey' in _this.props)) {\n        _this.setState({\n          activeKey: activeKey\n        });\n      }\n\n      _this.props.onChange(_this.props.accordion ? activeKey[0] : activeKey);\n    };\n\n    var _activeKey = _props.activeKey,\n        defaultActiveKey = _props.defaultActiveKey;\n    var currentActiveKey = defaultActiveKey;\n\n    if ('activeKey' in _props) {\n      currentActiveKey = _activeKey;\n    }\n\n    _this.state = {\n      activeKey: getActiveKeysArray(currentActiveKey)\n    };\n    return _this;\n  }\n\n  _createClass(Collapse, [{\n    key: \"shouldComponentUpdate\",\n    value: function shouldComponentUpdate(nextProps, nextState) {\n      return !shallowEqual(this.props, nextProps) || !shallowEqual(this.state, nextState);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n\n      var _this$props2 = this.props,\n          prefixCls = _this$props2.prefixCls,\n          className = _this$props2.className,\n          style = _this$props2.style,\n          accordion = _this$props2.accordion;\n      var collapseClassName = classNames((_classNames = {}, _defineProperty(_classNames, prefixCls, true), _defineProperty(_classNames, className, !!className), _classNames));\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: collapseClassName,\n        style: style,\n        role: accordion ? 'tablist' : null\n      }, this.getItems());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps) {\n      var newState = {};\n\n      if ('activeKey' in nextProps) {\n        newState.activeKey = getActiveKeysArray(nextProps.activeKey);\n      }\n\n      return newState;\n    }\n  }]);\n\n  return Collapse;\n}(React.Component);\n\nCollapse.defaultProps = {\n  prefixCls: 'rc-collapse',\n  onChange: function onChange() {},\n  accordion: false,\n  destroyInactivePanel: false\n};\nCollapse.Panel = CollapsePanel;\nexport default Collapse;", "import Collapse from './Collapse';\nexport default Collapse;\nvar Panel = Collapse.Panel;\nexport { Panel };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport warning from '../_util/warning';\nimport CollapsePanel from './CollapsePanel';\nvar Collapse = function Collapse(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$bordered = props.bordered,\n    bordered = _props$bordered === void 0 ? true : _props$bordered,\n    ghost = props.ghost,\n    _props$expandIconPosi = props.expandIconPosition,\n    expandIconPosition = _props$expandIconPosi === void 0 ? 'start' : _props$expandIconPosi;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  // Warning if use legacy type `expandIconPosition`\n  process.env.NODE_ENV !== \"production\" ? warning(expandIconPosition !== 'left' && expandIconPosition !== 'right', 'Collapse', '`expandIconPosition` with `left` or `right` is deprecated. Please use `start` or `end` instead.') : void 0;\n  // Align with logic position\n  var mergedExpandIconPosition = React.useMemo(function () {\n    if (expandIconPosition === 'left') {\n      return 'start';\n    }\n    return expandIconPosition === 'right' ? 'end' : expandIconPosition;\n  }, [expandIconPosition]);\n  var renderExpandIcon = function renderExpandIcon() {\n    var panelProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var expandIcon = props.expandIcon;\n    var icon = expandIcon ? expandIcon(panelProps) : /*#__PURE__*/React.createElement(RightOutlined, {\n      rotate: panelProps.isActive ? 90 : undefined\n    });\n    return cloneElement(icon, function () {\n      return {\n        className: classNames(icon.props.className, \"\".concat(prefixCls, \"-arrow\"))\n      };\n    });\n  };\n  var collapseClassName = classNames(\"\".concat(prefixCls, \"-icon-position-\").concat(mergedExpandIconPosition), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-borderless\"), !bordered), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), !!ghost), _classNames), className);\n  var openMotion = _extends(_extends({}, collapseMotion), {\n    motionAppear: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n  });\n  var getItems = function getItems() {\n    var children = props.children;\n    return toArray(children).map(function (child, index) {\n      var _a;\n      if ((_a = child.props) === null || _a === void 0 ? void 0 : _a.disabled) {\n        var key = child.key || String(index);\n        var _child$props = child.props,\n          disabled = _child$props.disabled,\n          collapsible = _child$props.collapsible;\n        var childProps = _extends(_extends({}, omit(child.props, ['disabled'])), {\n          key: key,\n          collapsible: collapsible !== null && collapsible !== void 0 ? collapsible : disabled ? 'disabled' : undefined\n        });\n        return cloneElement(child, childProps);\n      }\n      return child;\n    });\n  };\n  return /*#__PURE__*/React.createElement(RcCollapse, _extends({\n    openMotion: openMotion\n  }, props, {\n    expandIcon: renderExpandIcon,\n    prefixCls: prefixCls,\n    className: collapseClassName\n  }), getItems());\n};\nCollapse.Panel = CollapsePanel;\nexport default Collapse;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nvar CollapsePanel = function CollapsePanel(props) {\n  process.env.NODE_ENV !== \"production\" ? warning(!('disabled' in props), 'Collapse.Panel', '`disabled` is deprecated. Please use `collapsible=\"disabled\"` instead.') : void 0;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  var collapsePanelClassName = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-no-arrow\"), !showArrow), className);\n  return /*#__PURE__*/React.createElement(RcCollapse.Panel, _extends({}, props, {\n    prefixCls: prefixCls,\n    className: collapsePanelClassName\n  }));\n};\nexport default CollapsePanel;", "import Collapse from './Collapse';\nexport default Collapse;", "import { syntaxTree, indentNodeProp, continuedIndent, foldNodeProp, LRLanguage, LanguageSupport } from '@codemirror/language';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { ifNotIn, completeFromList } from '@codemirror/autocomplete';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst whitespace = 36,\n  LineComment = 1,\n  BlockComment = 2,\n  String$1 = 3,\n  Number = 4,\n  Bool = 5,\n  Null = 6,\n  ParenL = 7,\n  ParenR = 8,\n  BraceL = 9,\n  BraceR = 10,\n  BracketL = 11,\n  BracketR = 12,\n  Semi = 13,\n  Dot = 14,\n  Operator = 15,\n  Punctuation = 16,\n  SpecialVar = 17,\n  Identifier = 18,\n  QuotedIdentifier = 19,\n  Keyword = 20,\n  Type = 21,\n  Bits = 22,\n  Bytes = 23,\n  Builtin = 24;\n\nfunction isAlpha(ch) {\n    return ch >= 65 /* Ch.A */ && ch <= 90 /* Ch.Z */ || ch >= 97 /* Ch.a */ && ch <= 122 /* Ch.z */ || ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */;\n}\nfunction isHexDigit(ch) {\n    return ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */ || ch >= 97 /* Ch.a */ && ch <= 102 /* Ch.f */ || ch >= 65 /* Ch.A */ && ch <= 70 /* Ch.F */;\n}\nfunction readLiteral(input, endQuote, backslashEscapes) {\n    for (let escaped = false;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == endQuote && !escaped) {\n            input.advance();\n            return;\n        }\n        escaped = backslashEscapes && !escaped && input.next == 92 /* Ch.Backslash */;\n        input.advance();\n    }\n}\nfunction readDoubleDollarLiteral(input) {\n    for (;;) {\n        if (input.next < 0 || input.peek(1) < 0)\n            return;\n        if (input.next == 36 /* Ch.Dollar */ && input.peek(1) == 36 /* Ch.Dollar */) {\n            input.advance(2);\n            return;\n        }\n        input.advance();\n    }\n}\nfunction readWord(input, result) {\n    for (;;) {\n        if (input.next != 95 /* Ch.Underscore */ && !isAlpha(input.next))\n            break;\n        if (result != null)\n            result += String.fromCharCode(input.next);\n        input.advance();\n    }\n    return result;\n}\nfunction readWordOrQuoted(input) {\n    if (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */ || input.next == 96 /* Ch.Backtick */) {\n        let quote = input.next;\n        input.advance();\n        readLiteral(input, quote, false);\n    }\n    else {\n        readWord(input);\n    }\n}\nfunction readBits(input, endQuote) {\n    while (input.next == 48 /* Ch._0 */ || input.next == 49 /* Ch._1 */)\n        input.advance();\n    if (endQuote && input.next == endQuote)\n        input.advance();\n}\nfunction readNumber(input, sawDot) {\n    for (;;) {\n        if (input.next == 46 /* Ch.Dot */) {\n            if (sawDot)\n                break;\n            sawDot = true;\n        }\n        else if (input.next < 48 /* Ch._0 */ || input.next > 57 /* Ch._9 */) {\n            break;\n        }\n        input.advance();\n    }\n    if (input.next == 69 /* Ch.E */ || input.next == 101 /* Ch.e */) {\n        input.advance();\n        if (input.next == 43 /* Ch.Plus */ || input.next == 45 /* Ch.Dash */)\n            input.advance();\n        while (input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */)\n            input.advance();\n    }\n}\nfunction eol(input) {\n    while (!(input.next < 0 || input.next == 10 /* Ch.Newline */))\n        input.advance();\n}\nfunction inString(ch, str) {\n    for (let i = 0; i < str.length; i++)\n        if (str.charCodeAt(i) == ch)\n            return true;\n    return false;\n}\nconst Space = \" \\t\\r\\n\";\nfunction keywords(keywords, types, builtin) {\n    let result = Object.create(null);\n    result[\"true\"] = result[\"false\"] = Bool;\n    result[\"null\"] = result[\"unknown\"] = Null;\n    for (let kw of keywords.split(\" \"))\n        if (kw)\n            result[kw] = Keyword;\n    for (let tp of types.split(\" \"))\n        if (tp)\n            result[tp] = Type;\n    for (let kw of (builtin || \"\").split(\" \"))\n        if (kw)\n            result[kw] = Builtin;\n    return result;\n}\nconst SQLTypes = \"array binary bit boolean char character clob date decimal double float int integer interval large national nchar nclob numeric object precision real smallint time timestamp varchar varying \";\nconst SQLKeywords = \"absolute action add after all allocate alter and any are as asc assertion at authorization before begin between both breadth by call cascade cascaded case cast catalog check close collate collation column commit condition connect connection constraint constraints constructor continue corresponding count create cross cube current current_date current_default_transform_group current_transform_group_for_type current_path current_role current_time current_timestamp current_user cursor cycle data day deallocate declare default deferrable deferred delete depth deref desc describe descriptor deterministic diagnostics disconnect distinct do domain drop dynamic each else elseif end end-exec equals escape except exception exec execute exists exit external fetch first for foreign found from free full function general get global go goto grant group grouping handle having hold hour identity if immediate in indicator initially inner inout input insert intersect into is isolation join key language last lateral leading leave left level like limit local localtime localtimestamp locator loop map match method minute modifies module month names natural nesting new next no none not of old on only open option or order ordinality out outer output overlaps pad parameter partial path prepare preserve primary prior privileges procedure public read reads recursive redo ref references referencing relative release repeat resignal restrict result return returns revoke right role rollback rollup routine row rows savepoint schema scroll search second section select session session_user set sets signal similar size some space specific specifictype sql sqlexception sqlstate sqlwarning start state static system_user table temporary then timezone_hour timezone_minute to trailing transaction translation treat trigger under undo union unique unnest until update usage user using value values view when whenever where while with without work write year zone \";\nconst defaults = {\n    backslashEscapes: false,\n    hashComments: false,\n    spaceAfterDashes: false,\n    slashComments: false,\n    doubleQuotedStrings: false,\n    doubleDollarQuotedStrings: false,\n    unquotedBitLiterals: false,\n    treatBitsAsBytes: false,\n    charSetCasts: false,\n    operatorChars: \"*+\\-%<>!=&|~^/\",\n    specialVar: \"?\",\n    identifierQuotes: '\"',\n    words: /*@__PURE__*/keywords(SQLKeywords, SQLTypes)\n};\nfunction dialect(spec, kws, types, builtin) {\n    let dialect = {};\n    for (let prop in defaults)\n        dialect[prop] = (spec.hasOwnProperty(prop) ? spec : defaults)[prop];\n    if (kws)\n        dialect.words = keywords(kws, types || \"\", builtin);\n    return dialect;\n}\nfunction tokensFor(d) {\n    return new ExternalTokenizer(input => {\n        var _a;\n        let { next } = input;\n        input.advance();\n        if (inString(next, Space)) {\n            while (inString(input.next, Space))\n                input.advance();\n            input.acceptToken(whitespace);\n        }\n        else if (next == 36 /* Ch.Dollar */ && input.next == 36 /* Ch.Dollar */ && d.doubleDollarQuotedStrings) {\n            readDoubleDollarLiteral(input);\n            input.acceptToken(String$1);\n        }\n        else if (next == 39 /* Ch.SingleQuote */ || next == 34 /* Ch.DoubleQuote */ && d.doubleQuotedStrings) {\n            readLiteral(input, next, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 35 /* Ch.Hash */ && d.hashComments ||\n            next == 47 /* Ch.Slash */ && input.next == 47 /* Ch.Slash */ && d.slashComments) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 45 /* Ch.Dash */ && input.next == 45 /* Ch.Dash */ &&\n            (!d.spaceAfterDashes || input.peek(1) == 32 /* Ch.Space */)) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n            input.advance();\n            for (let prev = -1, depth = 1;;) {\n                if (input.next < 0)\n                    break;\n                input.advance();\n                if (prev == 42 /* Ch.Star */ && input.next == 47 /* Ch.Slash */) {\n                    depth--;\n                    if (!depth) {\n                        input.advance();\n                        break;\n                    }\n                    prev = -1;\n                }\n                else if (prev == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n                    depth++;\n                    prev = -1;\n                }\n                else {\n                    prev = input.next;\n                }\n            }\n            input.acceptToken(BlockComment);\n        }\n        else if ((next == 101 /* Ch.e */ || next == 69 /* Ch.E */) && input.next == 39 /* Ch.SingleQuote */) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, true);\n        }\n        else if ((next == 110 /* Ch.n */ || next == 78 /* Ch.N */) && input.next == 39 /* Ch.SingleQuote */ &&\n            d.charSetCasts) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 95 /* Ch.Underscore */ && d.charSetCasts) {\n            for (let i = 0;; i++) {\n                if (input.next == 39 /* Ch.SingleQuote */ && i > 1) {\n                    input.advance();\n                    readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n                    input.acceptToken(String$1);\n                    break;\n                }\n                if (!isAlpha(input.next))\n                    break;\n                input.advance();\n            }\n        }\n        else if (next == 40 /* Ch.ParenL */) {\n            input.acceptToken(ParenL);\n        }\n        else if (next == 41 /* Ch.ParenR */) {\n            input.acceptToken(ParenR);\n        }\n        else if (next == 123 /* Ch.BraceL */) {\n            input.acceptToken(BraceL);\n        }\n        else if (next == 125 /* Ch.BraceR */) {\n            input.acceptToken(BraceR);\n        }\n        else if (next == 91 /* Ch.BracketL */) {\n            input.acceptToken(BracketL);\n        }\n        else if (next == 93 /* Ch.BracketR */) {\n            input.acceptToken(BracketR);\n        }\n        else if (next == 59 /* Ch.Semi */) {\n            input.acceptToken(Semi);\n        }\n        else if (d.unquotedBitLiterals && next == 48 /* Ch._0 */ && input.next == 98 /* Ch.b */) {\n            input.advance();\n            readBits(input);\n            input.acceptToken(Bits);\n        }\n        else if ((next == 98 /* Ch.b */ || next == 66 /* Ch.B */) && (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */)) {\n            const quoteStyle = input.next;\n            input.advance();\n            if (d.treatBitsAsBytes) {\n                readLiteral(input, quoteStyle, d.backslashEscapes);\n                input.acceptToken(Bytes);\n            }\n            else {\n                readBits(input, quoteStyle);\n                input.acceptToken(Bits);\n            }\n        }\n        else if (next == 48 /* Ch._0 */ && (input.next == 120 /* Ch.x */ || input.next == 88 /* Ch.X */) ||\n            (next == 120 /* Ch.x */ || next == 88 /* Ch.X */) && input.next == 39 /* Ch.SingleQuote */) {\n            let quoted = input.next == 39 /* Ch.SingleQuote */;\n            input.advance();\n            while (isHexDigit(input.next))\n                input.advance();\n            if (quoted && input.next == 39 /* Ch.SingleQuote */)\n                input.advance();\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */ && input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */) {\n            readNumber(input, true);\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */) {\n            input.acceptToken(Dot);\n        }\n        else if (next >= 48 /* Ch._0 */ && next <= 57 /* Ch._9 */) {\n            readNumber(input, false);\n            input.acceptToken(Number);\n        }\n        else if (inString(next, d.operatorChars)) {\n            while (inString(input.next, d.operatorChars))\n                input.advance();\n            input.acceptToken(Operator);\n        }\n        else if (inString(next, d.specialVar)) {\n            if (input.next == next)\n                input.advance();\n            readWordOrQuoted(input);\n            input.acceptToken(SpecialVar);\n        }\n        else if (inString(next, d.identifierQuotes)) {\n            readLiteral(input, next, false);\n            input.acceptToken(QuotedIdentifier);\n        }\n        else if (next == 58 /* Ch.Colon */ || next == 44 /* Ch.Comma */) {\n            input.acceptToken(Punctuation);\n        }\n        else if (isAlpha(next)) {\n            let word = readWord(input, String.fromCharCode(next));\n            input.acceptToken(input.next == 46 /* Ch.Dot */ ? Identifier : (_a = d.words[word.toLowerCase()]) !== null && _a !== void 0 ? _a : Identifier);\n        }\n    });\n}\nconst tokens = /*@__PURE__*/tokensFor(defaults);\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser$1 = /*@__PURE__*/LRParser.deserialize({\n  version: 14,\n  states: \"%vQ]QQOOO#wQRO'#DSO$OQQO'#CwO%eQQO'#CxO%lQQO'#CyO%sQQO'#CzOOQQ'#DS'#DSOOQQ'#C}'#C}O'UQRO'#C{OOQQ'#Cv'#CvOOQQ'#C|'#C|Q]QQOOQOQQOOO'`QQO'#DOO(xQRO,59cO)PQQO,59cO)UQQO'#DSOOQQ,59d,59dO)cQQO,59dOOQQ,59e,59eO)jQQO,59eOOQQ,59f,59fO)qQQO,59fOOQQ-E6{-E6{OOQQ,59b,59bOOQQ-E6z-E6zOOQQ,59j,59jOOQQ-E6|-E6|O+VQRO1G.}O+^QQO,59cOOQQ1G/O1G/OOOQQ1G/P1G/POOQQ1G/Q1G/QP+kQQO'#C}O+rQQO1G.}O)PQQO,59cO,PQQO'#Cw\",\n  stateData: \",[~OtOSPOSQOS~ORUOSUOTUOUUOVROXSOZTO]XO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O^]ORvXSvXTvXUvXVvXXvXZvX]vX_vX`vXavXbvXcvXdvXevXfvXgvXhvX~OsvX~P!jOa_Ob_Oc_O~ORUOSUOTUOUUOVROXSOZTO^tO_UO`UOa`Ob`Oc`OdUOeUOfUOgUOhUO~OWaO~P$ZOYcO~P$ZO[eO~P$ZORUOSUOTUOUUOVROXSOZTO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O]hOsoX~P%zOajObjOcjO~O^]ORkaSkaTkaUkaVkaXkaZka]ka_ka`kaakabkackadkaekafkagkahka~Oska~P'kO^]O~OWvXYvX[vX~P!jOWnO~P$ZOYoO~P$ZO[pO~P$ZO^]ORkiSkiTkiUkiVkiXkiZki]ki_ki`kiakibkickidkiekifkigkihki~Oski~P)xOWkaYka[ka~P'kO]hO~P$ZOWkiYki[ki~P)xOasObsOcsO~O\",\n  goto: \"#hwPPPPPPPPPPPPPPPPPPPPPPPPPPx||||!Y!^!d!xPPP#[TYOZeUORSTWZbdfqT[OZQZORiZSWOZQbRQdSQfTZgWbdfqQ^PWk^lmrQl_Qm`RrseVORSTWZbdfq\",\n  nodeNames: \"⚠ LineComment BlockComment String Number Bool Null ( ) { } [ ] ; . Operator Punctuation SpecialVar Identifier QuotedIdentifier Keyword Type Bits Bytes Builtin Script Statement CompositeIdentifier Parens Braces Brackets Statement\",\n  maxTerm: 38,\n  skippedNodes: [0,1,2],\n  repeatNodeCount: 3,\n  tokenData: \"RORO\",\n  tokenizers: [0, tokens],\n  topRules: {\"Script\":[0,25]},\n  tokenPrec: 0\n});\n\nfunction tokenBefore(tree) {\n    let cursor = tree.cursor().moveTo(tree.from, -1);\n    while (/Comment/.test(cursor.name))\n        cursor.moveTo(cursor.from, -1);\n    return cursor.node;\n}\nfunction idName(doc, node) {\n    let text = doc.sliceString(node.from, node.to);\n    let quoted = /^([`'\"])(.*)\\1$/.exec(text);\n    return quoted ? quoted[2] : text;\n}\nfunction plainID(node) {\n    return node && (node.name == \"Identifier\" || node.name == \"QuotedIdentifier\");\n}\nfunction pathFor(doc, id) {\n    if (id.name == \"CompositeIdentifier\") {\n        let path = [];\n        for (let ch = id.firstChild; ch; ch = ch.nextSibling)\n            if (plainID(ch))\n                path.push(idName(doc, ch));\n        return path;\n    }\n    return [idName(doc, id)];\n}\nfunction parentsFor(doc, node) {\n    for (let path = [];;) {\n        if (!node || node.name != \".\")\n            return path;\n        let name = tokenBefore(node);\n        if (!plainID(name))\n            return path;\n        path.unshift(idName(doc, name));\n        node = tokenBefore(name);\n    }\n}\nfunction sourceContext(state, startPos) {\n    let pos = syntaxTree(state).resolveInner(startPos, -1);\n    let aliases = getAliases(state.doc, pos);\n    if (pos.name == \"Identifier\" || pos.name == \"QuotedIdentifier\" || pos.name == \"Keyword\") {\n        return { from: pos.from,\n            quoted: pos.name == \"QuotedIdentifier\" ? state.doc.sliceString(pos.from, pos.from + 1) : null,\n            parents: parentsFor(state.doc, tokenBefore(pos)),\n            aliases };\n    }\n    if (pos.name == \".\") {\n        return { from: startPos, quoted: null, parents: parentsFor(state.doc, pos), aliases };\n    }\n    else {\n        return { from: startPos, quoted: null, parents: [], empty: true, aliases };\n    }\n}\nconst EndFrom = /*@__PURE__*/new Set(/*@__PURE__*/\"where group having order union intersect except all distinct limit offset fetch for\".split(\" \"));\nfunction getAliases(doc, at) {\n    let statement;\n    for (let parent = at; !statement; parent = parent.parent) {\n        if (!parent)\n            return null;\n        if (parent.name == \"Statement\")\n            statement = parent;\n    }\n    let aliases = null;\n    for (let scan = statement.firstChild, sawFrom = false, prevID = null; scan; scan = scan.nextSibling) {\n        let kw = scan.name == \"Keyword\" ? doc.sliceString(scan.from, scan.to).toLowerCase() : null;\n        let alias = null;\n        if (!sawFrom) {\n            sawFrom = kw == \"from\";\n        }\n        else if (kw == \"as\" && prevID && plainID(scan.nextSibling)) {\n            alias = idName(doc, scan.nextSibling);\n        }\n        else if (kw && EndFrom.has(kw)) {\n            break;\n        }\n        else if (prevID && plainID(scan)) {\n            alias = idName(doc, scan);\n        }\n        if (alias) {\n            if (!aliases)\n                aliases = Object.create(null);\n            aliases[alias] = pathFor(doc, prevID);\n        }\n        prevID = /Identifier$/.test(scan.name) ? scan : null;\n    }\n    return aliases;\n}\nfunction maybeQuoteCompletions(quote, completions) {\n    if (!quote)\n        return completions;\n    return completions.map(c => (Object.assign(Object.assign({}, c), { label: quote + c.label + quote, apply: undefined })));\n}\nconst Span = /^\\w*$/, QuotedSpan = /^[`'\"]?\\w*[`'\"]?$/;\nclass CompletionLevel {\n    constructor() {\n        this.list = [];\n        this.children = undefined;\n    }\n    child(name) {\n        let children = this.children || (this.children = Object.create(null));\n        return children[name] || (children[name] = new CompletionLevel);\n    }\n    childCompletions(type) {\n        return this.children ? Object.keys(this.children).filter(x => x).map(name => ({ label: name, type })) : [];\n    }\n}\nfunction completeFromSchema(schema, tables, schemas, defaultTableName, defaultSchemaName) {\n    let top = new CompletionLevel;\n    let defaultSchema = top.child(defaultSchemaName || \"\");\n    for (let table in schema) {\n        let dot = table.indexOf(\".\");\n        let schemaCompletions = dot > -1 ? top.child(table.slice(0, dot)) : defaultSchema;\n        let tableCompletions = schemaCompletions.child(dot > -1 ? table.slice(dot + 1) : table);\n        tableCompletions.list = schema[table].map(val => typeof val == \"string\" ? { label: val, type: \"property\" } : val);\n    }\n    defaultSchema.list = (tables || defaultSchema.childCompletions(\"type\"))\n        .concat(defaultTableName ? defaultSchema.child(defaultTableName).list : []);\n    for (let sName in top.children) {\n        let schema = top.child(sName);\n        if (!schema.list.length)\n            schema.list = schema.childCompletions(\"type\");\n    }\n    top.list = defaultSchema.list.concat(schemas || top.childCompletions(\"type\"));\n    return (context) => {\n        let { parents, from, quoted, empty, aliases } = sourceContext(context.state, context.pos);\n        if (empty && !context.explicit)\n            return null;\n        if (aliases && parents.length == 1)\n            parents = aliases[parents[0]] || parents;\n        let level = top;\n        for (let name of parents) {\n            while (!level.children || !level.children[name]) {\n                if (level == top)\n                    level = defaultSchema;\n                else if (level == defaultSchema && defaultTableName)\n                    level = level.child(defaultTableName);\n                else\n                    return null;\n            }\n            level = level.child(name);\n        }\n        let quoteAfter = quoted && context.state.sliceDoc(context.pos, context.pos + 1) == quoted;\n        let options = level.list;\n        if (level == top && aliases)\n            options = options.concat(Object.keys(aliases).map(name => ({ label: name, type: \"constant\" })));\n        return {\n            from,\n            to: quoteAfter ? context.pos + 1 : undefined,\n            options: maybeQuoteCompletions(quoted, options),\n            validFor: quoted ? QuotedSpan : Span\n        };\n    };\n}\nfunction completeKeywords(keywords, upperCase) {\n    let completions = Object.keys(keywords).map(keyword => ({\n        label: upperCase ? keyword.toUpperCase() : keyword,\n        type: keywords[keyword] == Type ? \"type\" : keywords[keyword] == Keyword ? \"keyword\" : \"variable\",\n        boost: -1\n    }));\n    return ifNotIn([\"QuotedIdentifier\", \"SpecialVar\", \"String\", \"LineComment\", \"BlockComment\", \".\"], completeFromList(completions));\n}\n\nlet parser = /*@__PURE__*/parser$1.configure({\n    props: [\n        /*@__PURE__*/indentNodeProp.add({\n            Statement: /*@__PURE__*/continuedIndent()\n        }),\n        /*@__PURE__*/foldNodeProp.add({\n            Statement(tree) { return { from: tree.firstChild.to, to: tree.to }; },\n            BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n        }),\n        /*@__PURE__*/styleTags({\n            Keyword: tags.keyword,\n            Type: tags.typeName,\n            Builtin: /*@__PURE__*/tags.standard(tags.name),\n            Bits: tags.number,\n            Bytes: tags.string,\n            Bool: tags.bool,\n            Null: tags.null,\n            Number: tags.number,\n            String: tags.string,\n            Identifier: tags.name,\n            QuotedIdentifier: /*@__PURE__*/tags.special(tags.string),\n            SpecialVar: /*@__PURE__*/tags.special(tags.name),\n            LineComment: tags.lineComment,\n            BlockComment: tags.blockComment,\n            Operator: tags.operator,\n            \"Semi Punctuation\": tags.punctuation,\n            \"( )\": tags.paren,\n            \"{ }\": tags.brace,\n            \"[ ]\": tags.squareBracket\n        })\n    ]\n});\n/**\nRepresents an SQL dialect.\n*/\nclass SQLDialect {\n    constructor(\n    /**\n    @internal\n    */\n    dialect, \n    /**\n    The language for this dialect.\n    */\n    language) {\n        this.dialect = dialect;\n        this.language = language;\n    }\n    /**\n    Returns the language for this dialect as an extension.\n    */\n    get extension() { return this.language.extension; }\n    /**\n    Define a new dialect.\n    */\n    static define(spec) {\n        let d = dialect(spec, spec.keywords, spec.types, spec.builtin);\n        let language = LRLanguage.define({\n            name: \"sql\",\n            parser: parser.configure({\n                tokenizers: [{ from: tokens, to: tokensFor(d) }]\n            }),\n            languageData: {\n                commentTokens: { line: \"--\", block: { open: \"/*\", close: \"*/\" } },\n                closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] }\n            }\n        });\n        return new SQLDialect(d, language);\n    }\n}\n/**\nReturns a completion source that provides keyword completion for\nthe given SQL dialect.\n*/\nfunction keywordCompletionSource(dialect, upperCase = false) {\n    return completeKeywords(dialect.dialect.words, upperCase);\n}\n/**\nFIXME remove on 1.0 @internal\n*/\nfunction keywordCompletion(dialect, upperCase = false) {\n    return dialect.language.data.of({\n        autocomplete: keywordCompletionSource(dialect, upperCase)\n    });\n}\n/**\nReturns a completion sources that provides schema-based completion\nfor the given configuration.\n*/\nfunction schemaCompletionSource(config) {\n    return config.schema ? completeFromSchema(config.schema, config.tables, config.schemas, config.defaultTable, config.defaultSchema)\n        : () => null;\n}\n/**\nFIXME remove on 1.0 @internal\n*/\nfunction schemaCompletion(config) {\n    return config.schema ? (config.dialect || StandardSQL).language.data.of({\n        autocomplete: schemaCompletionSource(config)\n    }) : [];\n}\n/**\nSQL language support for the given SQL dialect, with keyword\ncompletion, and, if provided, schema-based completion as extra\nextensions.\n*/\nfunction sql(config = {}) {\n    let lang = config.dialect || StandardSQL;\n    return new LanguageSupport(lang.language, [schemaCompletion(config), keywordCompletion(lang, !!config.upperCaseKeywords)]);\n}\n/**\nThe standard SQL dialect.\n*/\nconst StandardSQL = /*@__PURE__*/SQLDialect.define({});\n/**\nDialect for [PostgreSQL](https://www.postgresql.org).\n*/\nconst PostgreSQL = /*@__PURE__*/SQLDialect.define({\n    charSetCasts: true,\n    doubleDollarQuotedStrings: true,\n    operatorChars: \"+-*/<>=~!@#%^&|`?\",\n    specialVar: \"\",\n    keywords: SQLKeywords + \"a abort abs absent access according ada admin aggregate alias also always analyse analyze array_agg array_max_cardinality asensitive assert assignment asymmetric atomic attach attribute attributes avg backward base64 begin_frame begin_partition bernoulli bit_length blocked bom c cache called cardinality catalog_name ceil ceiling chain char_length character_length character_set_catalog character_set_name character_set_schema characteristics characters checkpoint class class_origin cluster coalesce cobol collation_catalog collation_name collation_schema collect column_name columns command_function command_function_code comment comments committed concurrently condition_number configuration conflict connection_name constant constraint_catalog constraint_name constraint_schema contains content control conversion convert copy corr cost covar_pop covar_samp csv cume_dist current_catalog current_row current_schema cursor_name database datalink datatype datetime_interval_code datetime_interval_precision db debug defaults defined definer degree delimiter delimiters dense_rank depends derived detach detail dictionary disable discard dispatch dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue document dump dynamic_function dynamic_function_code element elsif empty enable encoding encrypted end_frame end_partition endexec enforced enum errcode error event every exclude excluding exclusive exp explain expression extension extract family file filter final first_value flag floor following force foreach fortran forward frame_row freeze fs functions fusion g generated granted greatest groups handler header hex hierarchy hint id ignore ilike immediately immutable implementation implicit import include including increment indent index indexes info inherit inherits inline insensitive instance instantiable instead integrity intersection invoker isnull k key_member key_type label lag last_value lead leakproof least length library like_regex link listen ln load location lock locked log logged lower m mapping matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text min minvalue mod mode more move multiset mumps name namespace nfc nfd nfkc nfkd nil normalize normalized nothing notice notify notnull nowait nth_value ntile nullable nullif nulls number occurrences_regex octet_length octets off offset oids operator options ordering others over overlay overriding owned owner p parallel parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partition pascal passing passthrough password percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding prepared print_strict_params procedural procedures program publication query quote raise range rank reassign recheck recovery refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex rename repeatable replace replica requiring reset respect restart restore result_oid returned_cardinality returned_length returned_octet_length returned_sqlstate returning reverse routine_catalog routine_name routine_schema routines row_count row_number rowtype rule scale schema_name schemas scope scope_catalog scope_name scope_schema security selective self sensitive sequence sequences serializable server server_name setof share show simple skip slice snapshot source specific_name sqlcode sqlerror sqrt stable stacked standalone statement statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time t table_name tables tablesample tablespace temp template ties token top_level_count transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex trigger_catalog trigger_name trigger_schema trim trim_array truncate trusted type types uescape unbounded uncommitted unencrypted unlink unlisten unlogged unnamed untyped upper uri use_column use_variable user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema vacuum valid validate validator value_of var_pop var_samp varbinary variable_conflict variadic verbose version versioning views volatile warning whitespace width_bucket window within wrapper xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate yes\",\n    types: SQLTypes + \"bigint int8 bigserial serial8 varbit bool box bytea cidr circle precision float8 inet int4 json jsonb line lseg macaddr macaddr8 money numeric pg_lsn point polygon float4 int2 smallserial serial2 serial serial4 text timetz timestamptz tsquery tsvector txid_snapshot uuid xml\"\n});\nconst MySQLKeywords = \"accessible algorithm analyze asensitive authors auto_increment autocommit avg avg_row_length binlog btree cache catalog_name chain change changed checkpoint checksum class_origin client_statistics coalesce code collations columns comment committed completion concurrent consistent contains contributors convert database databases day_hour day_microsecond day_minute day_second delay_key_write delayed delimiter des_key_file dev_pop dev_samp deviance directory disable discard distinctrow div dual dumpfile enable enclosed ends engine engines enum errors escaped even event events every explain extended fast field fields flush force found_rows fulltext grants handler hash high_priority hosts hour_microsecond hour_minute hour_second ignore ignore_server_ids import index index_statistics infile innodb insensitive insert_method install invoker iterate keys kill linear lines list load lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modify mutex mysql_errno no_write_to_binlog offline offset one online optimize optionally outfile pack_keys parser partition partitions password phase plugin plugins prev processlist profile profiles purge query quick range read_write rebuild recover regexp relaylog remove rename reorganize repair repeatable replace require resume rlike row_format rtree schedule schema_name schemas second_microsecond security sensitive separator serializable server share show slave slow snapshot soname spatial sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result ssl starting starts std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace terminated triggers truncate uncommitted uninstall unlock upgrade use use_frm user_resources user_statistics utc_date utc_time utc_timestamp variables views warnings xa xor year_month zerofill\";\nconst MySQLTypes = SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int1 int2 int3 int4 int8 float4 float8 varbinary varcharacter precision datetime unsigned signed\";\nconst MySQLBuiltin = \"charset clear edit ego help nopager notee nowarning pager print prompt quit rehash source status system tee\";\n/**\n[MySQL](https://dev.mysql.com/) dialect.\n*/\nconst MySQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"group_concat \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nVariant of [`MySQL`](https://codemirror.net/6/docs/ref/#lang-sql.MySQL) for\n[MariaDB](https://mariadb.org/).\n*/\nconst MariaSQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"always generated groupby_concat hard persistent shutdown soft virtual \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nSQL dialect for Microsoft [SQL\nServer](https://www.microsoft.com/en-us/sql-server).\n*/\nconst MSSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock pivot readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx unpivot updlock with\",\n    types: SQLTypes + \"bigint smallint smallmoney tinyint money real text nvarchar ntext varbinary image hierarchyid uniqueidentifier sql_variant xml\",\n    builtin: \"binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\",\n    operatorChars: \"*+-%<>!=^&|/\",\n    specialVar: \"@\"\n});\n/**\n[SQLite](https://sqlite.org/) dialect.\n*/\nconst SQLite = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort analyze attach autoincrement conflict database detach exclusive fail glob ignore index indexed instead isnull notnull offset plan pragma query raise regexp reindex rename replace temp vacuum virtual\",\n    types: SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int2 int8 unsigned signed real\",\n    builtin: \"auth backup bail changes clone databases dbinfo dump echo eqp explain fullschema headers help import imposter indexes iotrace lint load log mode nullvalue once print prompt quit restore save scanstats separator shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\",\n    operatorChars: \"*+-%<>!=&|/~\",\n    identifierQuotes: \"`\\\"\",\n    specialVar: \"@:?$\"\n});\n/**\nDialect for [Cassandra](https://cassandra.apache.org/)'s SQL-ish query language.\n*/\nconst Cassandra = /*@__PURE__*/SQLDialect.define({\n    keywords: \"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime infinity NaN\",\n    types: SQLTypes + \"ascii bigint blob counter frozen inet list map static text timeuuid tuple uuid varint\",\n    slashComments: true\n});\n/**\n[PL/SQL](https://en.wikipedia.org/wiki/PL/SQL) dialect.\n*/\nconst PLSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort accept access add all alter and any arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body by case cast char_base check close cluster clusters colauth column comment commit compress connected constant constraint crash create current currval cursor data_base database dba deallocate debugoff debugon declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry exception exception_init exchange exclusive exists external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base of off offline on online only option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw rebuild record ref references refresh rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\",\n    builtin: \"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define echo editfile embedded feedback flagger flush heading headsep instance linesize lno loboffset logsource longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar repfooter repheader serveroutput shiftinout show showmode spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout timing trimout trimspool ttitle underline verify version wrap\",\n    types: SQLTypes + \"ascii bfile bfilename bigserial bit blob dec long number nvarchar nvarchar2 serial smallint string text uid varchar2 xml\",\n    operatorChars: \"*/+-%<>!=~\",\n    doubleQuotedStrings: true,\n    charSetCasts: true\n});\n\nexport { Cassandra, MSSQL, MariaSQL, MySQL, PLSQL, PostgreSQL, SQLDialect, SQLite, StandardSQL, keywordCompletion, keywordCompletionSource, schemaCompletion, schemaCompletionSource, sql };\n"], "names": ["MenuOutlined", "props", "ref", "React", "AntdIcon", "_objectSpread", "icon", "MenuOutlinedSvg", "displayName", "RedoOutlined", "RedoOutlinedSvg", "RightCircleOutlined", "RightCircleOutlinedSvg", "StopOutlined", "StopOutlinedSvg", "lineNumber", "Facet", "combine", "values", "length", "Array", "isArray", "flat", "stepSize", "Math", "min", "stripe", "Decoration", "attributes", "class", "stripeDeco", "view", "step", "state", "facet", "num", "builder", "RangeSetBuilder", "visibleRanges", "from", "to", "pos", "line", "doc", "lineAt", "number", "add", "includes", "finish", "showStripes", "ViewPlugin", "constructor", "decorations", "this", "update", "v", "baseTheme", "opt", "Editor<PERSON><PERSON><PERSON>", "className", "backgroundColor", "lightColor", "darkColor", "range", "start", "stop", "_", "i", "zebraStripes", "options", "map", "item", "extensions", "of", "zebraStripeTheme", "push", "createTheme", "_ref", "theme", "settings", "styles", "themeOptions", "baseStyle", "background", "foreground", "color", "fontFamily", "gutterBackground", "gutterForeground", "gutterBorder", "borderRightColor", "caret", "caretColor", "borderLeftColor", "activeLineGutterStyle", "gutterActiveForeground", "lineHighlight", "selection", "selectionMatch", "themeExtension", "dark", "highlightStyle", "HighlightStyle", "syntaxHighlighting", "bbedit", "tag", "t", "bbeditInit", "PanelContent", "_classnames", "prefixCls", "forceRender", "style", "children", "isActive", "role", "_React$useState", "_React$useState2", "_slicedToArray", "rendered", "setRendered", "classnames", "concat", "_defineProperty", "_excluded", "CollapsePanel", "_React$Component", "_inherits", "_super", "_createSuper", "_this", "_classCallCheck", "_len", "arguments", "args", "_key", "call", "apply", "onItemClick", "_this$props", "<PERSON><PERSON><PERSON>", "handleKeyPress", "e", "key", "keyCode", "which", "renderIcon", "_this$props2", "showArrow", "expandIcon", "collapsible", "iconNode", "onClick", "renderTitle", "_this$props3", "header", "_createClass", "value", "nextProps", "shallowEqual", "_classNames", "_classNames2", "_this$props4", "id", "headerClass", "destroyInactivePanel", "accordion", "openMotion", "extra", "rest", "_objectWithoutProperties", "disabled", "collapsibleHeader", "collapsibleIcon", "itemCls", "classNames", "headerProps", "onKeyPress", "tabIndex", "ifExtraExist", "undefined", "_extends", "CSSMotion", "visible", "leavedClassName", "removeOnLeave", "motionClassName", "motionStyle", "defaultProps", "getActiveKeysArray", "active<PERSON><PERSON>", "currentActiveKey", "activeKeyType", "_typeof", "String", "Collapse", "_props", "onClickItem", "index", "_toConsumableArray", "indexOf", "splice", "setActiveKey", "get<PERSON>ew<PERSON><PERSON><PERSON>", "child", "rootDestroyInactivePanel", "_child$props", "childCollapsible", "mergeCollapsible", "type", "Object", "keys", "for<PERSON>ach", "propName", "getItems", "toArray", "setState", "onChange", "_<PERSON><PERSON><PERSON>", "defaultActiveKey", "nextState", "collapseClassName", "newState", "Panel", "_React$useContext", "ConfigContext", "getPrefixCls", "direction", "customizePrefixCls", "_props$className", "_props$bordered", "bordered", "ghost", "_props$expandIconPosi", "expandIconPosition", "mergedExpandIconPosition", "collapseMotion", "motionAppear", "RcCollapse", "panelProps", "RightOutlined", "rotate", "cloneElement", "_a", "childProps", "omit", "_props$showArrow", "collapsePanelClassName", "Bool", "<PERSON><PERSON>", "Keyword", "Type", "Builtin", "isAlpha", "ch", "readLiteral", "input", "endQuote", "backslashEscapes", "escaped", "next", "advance", "readWord", "result", "fromCharCode", "readBits", "readNumber", "sawDot", "eol", "inString", "str", "charCodeAt", "Space", "keywords", "types", "builtin", "create", "split", "kw", "tp", "SQLTypes", "SQLKeywords", "defaults", "hashComments", "spaceAfterDashes", "slashComments", "doubleQuotedStrings", "doubleDollarQuotedStrings", "unquotedBitLiterals", "treatBitsAsBytes", "charSetCasts", "operatorChars", "specialVar", "identifierQuotes", "words", "tokensFor", "d", "ExternalTokenizer", "acceptToken", "peek", "readDoubleDollarLiteral", "prev", "depth", "quoted", "quote", "readWordOrQuoted", "word", "toLowerCase", "quoteStyle", "tokens", "parser$1", "<PERSON><PERSON><PERSON><PERSON>", "version", "states", "stateData", "goto", "nodeNames", "maxTerm", "skippedNodes", "repeatNodeCount", "tokenData", "tokenizers", "topRules", "tokenPrec", "tokenBefore", "tree", "cursor", "moveTo", "test", "name", "node", "idName", "text", "sliceString", "exec", "plainID", "pathFor", "path", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "parents<PERSON>or", "unshift", "sourceContext", "startPos", "syntaxTree", "resolveInner", "aliases", "at", "statement", "parent", "scan", "saw<PERSON>rom", "prevID", "alias", "EndFrom", "has", "getAliases", "parents", "empty", "Set", "Span", "QuotedSpan", "CompletionLevel", "list", "filter", "x", "label", "parser", "configure", "indentNodeProp", "Statement", "continuedIndent", "foldNodeProp", "BlockComment", "styleTags", "tags", "Bits", "Bytes", "Number", "Identifier", "QuotedIdentifier", "SpecialVar", "LineComment", "Operator", "SQLDialect", "dialect", "language", "extension", "spec", "kws", "prop", "hasOwnProperty", "LRLanguage", "languageData", "commentTokens", "block", "open", "close", "closeBrackets", "brackets", "keywordCompletionSource", "upperCase", "completions", "keyword", "toUpperCase", "boost", "ifNotIn", "completeFromList", "completeKeywords", "keywordCompletion", "data", "autocomplete", "schemaCompletionSource", "config", "schema", "tables", "schemas", "defaultTableName", "defaultSchemaName", "top", "defaultSchema", "table", "dot", "slice", "val", "sName", "childCompletions", "context", "explicit", "level", "quoteAfter", "sliceDoc", "c", "assign", "validFor", "completeFromSchema", "defaultTable", "schemaCompletion", "StandardSQL", "sql", "lang", "LanguageSupport", "upperCaseKeywords", "define"], "sourceRoot": ""}