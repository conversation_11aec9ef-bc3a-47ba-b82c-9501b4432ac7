{"tf-mnist": {"project_name": "public", "name": "mnist", "version": "v2022.08.01.1", "describe": "tf mnist 图像分类 tfserving推理", "path": "https://cube-studio.oss-cn-hangzhou.aliyuncs.com/inference/tf-mnist.tar.gz", "framework": "tf", "api_type": "tfserving"}, "pytorch-resnet50": {"project_name": "public", "name": "resnet50", "version": "v2022.08.01.2", "describe": "pytorch resnet50 图像分类 torch-server推理", "path": "https://cube-studio.oss-cn-hangzhou.aliyuncs.com/inference/resnet50.mar", "framework": "pytorch", "api_type": "torch-server"}, "torchscript-resnet50": {"project_name": "public", "name": "resnet50", "version": "v2022.08.01.3", "describe": "torchscript resnet50 图像分类 triton推理", "path": "https://cube-studio.oss-cn-hangzhou.aliyuncs.com/inference/resnet50-torchscript.pt", "framework": "pytorch", "api_type": "triton-server"}, "onnx-resnet50": {"project_name": "public", "name": "resnet50", "version": "v2022.08.01.4", "describe": "onnx resnet50 图像分类 triton推理", "path": "https://cube-studio.oss-cn-hangzhou.aliyuncs.com/inference/resnet50.onnx", "framework": "onnx", "api_type": "triton-server"}}