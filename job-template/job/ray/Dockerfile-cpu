#FROM rayproject/ray:nightly
#ccr.ccs.tencentyun.com/cube-studio/ray:nightly
FROM rayproject/ray:nightly
USER root

ENV TZ Asia/Shanghai
ENV DEBIAN_FRONTEND noninteractive

# 安装调试相关工具
RUN apt update -y && apt install -y --force-yes --no-install-recommends vim apt-transport-https gnupg2 ca-certificates-java rsync jq  wget git dnsutils iputils-ping net-tools curl mysql-client locales zip software-properties-common

# 安装开发相关工具
RUN apt install -y python3.8-dev gcc automake autoconf libtool make ffmpeg build-essential

RUN pip install kubernetes==21.7.0 pysnooper psutil requests numpy  pyinstaller argparse

COPY job/ray/* /app/
COPY job/pkgs /app/job/pkgs

ENV PYTHONPATH=/app:$PYTHONPATH

WORKDIR /app
ENTRYPOINT ["bash", "/app/start.sh"]


