{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import os \n", "from pyflink.table import EnvironmentSettings, TableEnvironment\n", "\n", "env_settings = EnvironmentSettings.in_streaming_mode()\n", "t_env = TableEnvironment.create(env_settings)\n", "\n", "flink_lib_path=\"/opt/third/flink/lib\"\n", "jars = []\n", "for file in os.listdir(flink_lib_path):\n", "    if file.endswith('.jar'):\n", "        jars.append(os.path.basename(file))\n", "str_jars = ';'.join(['file://'+flink_lib_path +'/'+ jar for jar in jars])\n", "t_env.get_config().get_configuration().set_string(\"pipeline.jars\", str_jars)\n", "\n", "from pyflink.table.catalog import HiveCatalog\n", "\n", "# Create a HiveCatalog\n", "catalog_name = \"hive\"\n", "default_database = \"default\"\n", "catalog = HiveCatalog(catalog_name, default_database, \"/opt/third/hive/conf\")\n", "t_env.register_catalog(catalog_name, catalog)\n", "t_env.use_catalog(catalog_name)\n", "\n", "t_env.execute_sql(\"DROP TABLE IF EXISTS random_source_pyflink\")\n", "t_env.execute_sql(\"\"\"\n", "    CREATE TABLE IF NOT EXISTS random_source_pyflink (\n", "        id BIGINT, \n", "        data TINYINT\n", "    ) WITH (\n", "        'connector' = 'datagen',\n", "        'fields.id.kind'='sequence',\n", "        'fields.id.start'='1',\n", "        'fields.id.end'='8',\n", "        'fields.data.kind'='sequence',\n", "        'fields.data.start'='4',\n", "        'fields.data.end'='11'\n", "    )\n", "\"\"\")\n", "\n", "t_env.execute_sql(\"DROP TABLE IF EXISTS print_sink_pyflink\")\n", "t_env.execute_sql(\"\"\"\n", "    CREATE TABLE IF NOT EXISTS print_sink_pyflink  (\n", "        id BIGINT, \n", "        data_sum TINYINT \n", "    ) WITH (\n", "        'connector' = 'print'\n", "    )\n", "\"\"\")\n", "\n", "t_env.execute_sql(\"\"\"\n", "    INSERT INTO print_sink_pyflink \n", "        SELECT id, sum(data) as data_sum FROM \n", "            (SELECT id / 2 as id, data FROM random_source_pyflink )\n", "        WHERE id > 1\n", "        GROUP BY id\n", "\"\"\").wait()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3.0}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}, "pycharm": {"stem_cell": {"cell_type": "raw", "source": [], "metadata": {"collapsed": false}}}}, "nbformat": 4, "nbformat_minor": 0}