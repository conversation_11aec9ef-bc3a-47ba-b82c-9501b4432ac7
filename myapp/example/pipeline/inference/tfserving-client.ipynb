{"cells": [{"cell_type": "code", "execution_count": 19, "id": "9e53d0ab-738c-40b0-a080-fd286a84f6cb", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://mirrors.aliyun.com/pypi/simple\n", "Requirement already satisfied: numpy in /root/miniconda3/envs/python39/lib/python3.9/site-packages (1.23.5)\n", "Requirement already satisfied: requests in /root/miniconda3/envs/python39/lib/python3.9/site-packages (2.29.0)\n", "Requirement already satisfied: matplotlib in /root/miniconda3/envs/python39/lib/python3.9/site-packages (3.7.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (3.1.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (3.4)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (2022.12.7)\n", "Requirement already satisfied: contourpy>=1.0.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (1.0.7)\n", "Requirement already satisfied: cycler>=0.10 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (0.11.0)\n", "Requirement already satisfied: fonttools>=4.22.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (4.39.3)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (1.4.4)\n", "Requirement already satisfied: packaging>=20.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (23.1)\n", "Requirement already satisfied: pillow>=6.2.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (9.5.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (3.0.9)\n", "Requirement already satisfied: python-dateutil>=2.7 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (2.8.2)\n", "Requirement already satisfied: importlib-resources>=3.2.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from matplotlib) (5.12.0)\n", "Requirement already satisfied: zipp>=3.1.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from importlib-resources>=3.2.0->matplotlib) (3.15.0)\n", "Requirement already satisfied: six>=1.5 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from python-dateutil>=2.7->matplotlib) (1.16.0)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["! pip install numpy requests matplotlib"]}, {"cell_type": "code", "execution_count": 20, "id": "744af584-1202-4c44-a1ac-a6b7b3307908", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data: {\"signature_name\": \"serving_default\", \"instances\": ...  [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]]]]}\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json,numpy\n", "import matplotlib.pyplot as plt\n", "class_names = ['T-shirt/top', 'Trouser', 'Pullover', 'Dress', 'Coat', '<PERSON><PERSON>', 'Shirt', '<PERSON><PERSON>ker', 'Bag', 'Ankle boot']\n", "images = [[[[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.011764705882352941], [0.00392156862745098], [0.0], [0.0], [0.027450980392156862], [0.0], [0.1450980392156863], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00392156862745098], [0.00784313725490196], [0.0], [0.10588235294117647], [0.32941176470588235], [0.043137254901960784], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.4666666666666667], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00392156862745098], [0.0], [0.0], [0.34509803921568627], [0.5607843137254902], [0.43137254901960786], [0.0], [0.0], [0.0], [0.0], [0.08627450980392157], [0.36470588235294116], [0.41568627450980394], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.01568627450980392], [0.0], [0.20784313725490197], [0.5058823529411764], [0.47058823529411764], [0.5764705882352941], [0.6862745098039216], [0.615686274509804], [0.6509803921568628], [0.5294117647058824], [0.6039215686274509], [0.6588235294117647], [0.5490196078431373], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00784313725490196], [0.0], [0.043137254901960784], [0.5372549019607843], [0.5098039215686274], [0.5019607843137255], [0.6274509803921569], [0.6901960784313725], [0.6235294117647059], [0.6549019607843137], [0.6980392156862745], [0.5843137254901961], [0.592156862745098], [0.5647058823529412], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00392156862745098], [0.0], [0.00784313725490196], [0.00392156862745098], [0.0], [0.011764705882352941], [0.0], [0.0], [0.45098039215686275], [0.4470588235294118], [0.41568627450980394], [0.5372549019607843], [0.6588235294117647], [0.6], [0.611764705882353], [0.6470588235294118], [0.6549019607843137], [0.5607843137254902], [0.615686274509804], [0.6196078431372549], [0.043137254901960784], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.00392156862745098], [0.0], [0.0], [0.0], [0.0], [0.0], [0.011764705882352941], [0.0], [0.0], [0.34901960784313724], [0.5450980392156862], [0.35294117647058826], [0.3686274509803922], [0.6], [0.5843137254901961], [0.5137254901960784], [0.592156862745098], [0.6627450980392157], [0.6745098039215687], [0.5607843137254902], [0.6235294117647059], [0.6627450980392157], [0.18823529411764706], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00784313725490196], [0.01568627450980392], [0.00392156862745098], [0.0], [0.0], [0.0], [0.3843137254901961], [0.5333333333333333], [0.43137254901960786], [0.42745098039215684], [0.43137254901960786], [0.6352941176470588], [0.5294117647058824], [0.5647058823529412], [0.5843137254901961], [0.6235294117647059], [0.6549019607843137], [0.5647058823529412], [0.6196078431372549], [0.6627450980392157], [0.4666666666666667], [0.0]], [[0.0], [0.0], [0.00784313725490196], [0.00784313725490196], [0.00392156862745098], [0.00784313725490196], [0.0], [0.0], [0.0], [0.0], [0.10196078431372549], [0.4235294117647059], [0.4588235294117647], [0.38823529411764707], [0.43529411764705883], [0.4588235294117647], [0.5333333333333333], [0.611764705882353], [0.5254901960784314], [0.6039215686274509], [0.6039215686274509], [0.611764705882353], [0.6274509803921569], [0.5529411764705883], [0.5764705882352941], [0.611764705882353], [0.6980392156862745], [0.0]], [[0.011764705882352941], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.08235294117647059], [0.20784313725490197], [0.3607843137254902], [0.4588235294117647], [0.43529411764705883], [0.403921568627451], [0.45098039215686275], [0.5058823529411764], [0.5254901960784314], [0.5607843137254902], [0.6039215686274509], [0.6470588235294118], [0.6666666666666666], [0.6039215686274509], [0.592156862745098], [0.6039215686274509], [0.5607843137254902], [0.5411764705882353], [0.5882352941176471], [0.6470588235294118], [0.16862745098039217]], [[0.0], [0.0], [0.09019607843137255], [0.21176470588235294], [0.2549019607843137], [0.2980392156862745], [0.3333333333333333], [0.4627450980392157], [0.5019607843137255], [0.4823529411764706], [0.43529411764705883], [0.44313725490196076], [0.4627450980392157], [0.4980392156862745], [0.49019607843137253], [0.5450980392156862], [0.5215686274509804], [0.5333333333333333], [0.6274509803921569], [0.5490196078431373], [0.6078431372549019], [0.6313725490196078], [0.5647058823529412], [0.6078431372549019], [0.6745098039215687], [0.6313725490196078], [0.7411764705882353], [0.24313725490196078]], [[0.0], [0.26666666666666666], [0.3686274509803922], [0.35294117647058826], [0.43529411764705883], [0.4470588235294118], [0.43529411764705883], [0.4470588235294118], [0.45098039215686275], [0.4980392156862745], [0.5294117647058824], [0.5333333333333333], [0.5607843137254902], [0.49411764705882355], [0.4980392156862745], [0.592156862745098], [0.6039215686274509], [0.5607843137254902], [0.5803921568627451], [0.49019607843137253], [0.6352941176470588], [0.6352941176470588], [0.5647058823529412], [0.5411764705882353], [0.6], [0.6352941176470588], [0.7686274509803922], [0.22745098039215686]], [[0.27450980392156865], [0.6627450980392157], [0.5058823529411764], [0.40784313725490196], [0.3843137254901961], [0.39215686274509803], [0.3686274509803922], [0.3803921568627451], [0.3843137254901961], [0.4], [0.4235294117647059], [0.41568627450980394], [0.4666666666666667], [0.47058823529411764], [0.5058823529411764], [0.5843137254901961], [0.611764705882353], [0.6549019607843137], [0.7450980392156863], [0.7450980392156863], [0.7686274509803922], [0.7764705882352941], [0.7764705882352941], [0.7333333333333333], [0.7725490196078432], [0.7411764705882353], [0.7215686274509804], [0.1411764705882353]], [[0.06274509803921569], [0.49411764705882355], [0.6705882352941176], [0.7372549019607844], [0.7372549019607844], [0.7215686274509804], [0.6705882352941176], [0.6], [0.5294117647058824], [0.47058823529411764], [0.49411764705882355], [0.4980392156862745], [0.5725490196078431], [0.7254901960784313], [0.7647058823529411], [0.8196078431372549], [0.8156862745098039], [1.0], [0.8196078431372549], [0.6941176470588235], [0.9607843137254902], [0.9882352941176471], [0.984313725490196], [0.984313725490196], [0.9686274509803922], [0.8627450980392157], [0.807843137254902], [0.19215686274509805]], [[0.0], [0.0], [0.0], [0.047058823529411764], [0.2627450980392157], [0.41568627450980394], [0.6431372549019608], [0.7254901960784313], [0.7803921568627451], [0.8235294117647058], [0.8274509803921568], [0.8235294117647058], [0.8156862745098039], [0.7450980392156863], [0.5882352941176471], [0.3215686274509804], [0.03137254901960784], [0.0], [0.0], [0.0], [0.6980392156862745], [0.8156862745098039], [0.7372549019607844], [0.6862745098039216], [0.6352941176470588], [0.6196078431372549], [0.592156862745098], [0.043137254901960784]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]]], [[[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.050980392156862744], [0.2627450980392157], [0.0], [0.0], [0.0], [0.0], [0.19607843137254902], [0.14901960784313725], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.03137254901960784], [0.47058823529411764], [0.8196078431372549], [0.8862745098039215], [0.9686274509803922], [0.9294117647058824], [1.0], [1.0], [1.0], [0.9686274509803922], [0.9333333333333333], [0.9215686274509803], [0.6745098039215687], [0.2823529411764706], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.5372549019607843], [0.9372549019607843], [0.9882352941176471], [0.9529411764705882], [0.9176470588235294], [0.8980392156862745], [0.9333333333333333], [0.9568627450980393], [0.9647058823529412], [0.9411764705882353], [0.9019607843137255], [0.9098039215686274], [0.9372549019607843], [0.9725490196078431], [0.984313725490196], [0.7607843137254902], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.4], [1.0], [0.9058823529411765], [0.8941176470588236], [0.8901960784313725], [0.8941176470588236], [0.9137254901960784], [0.9019607843137255], [0.9019607843137255], [0.8980392156862745], [0.8941176470588236], [0.9098039215686274], [0.9098039215686274], [0.9058823529411765], [0.8901960784313725], [0.8784313725490196], [0.9882352941176471], [0.7019607843137254], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.9137254901960784], [0.9450980392156862], [0.8980392156862745], [0.9058823529411765], [1.0], [1.0], [0.9333333333333333], [0.9058823529411765], [0.8901960784313725], [0.9333333333333333], [0.9647058823529412], [0.8941176470588236], [0.9019607843137255], [0.8901960784313725], [0.9176470588235294], [0.9215686274509803], [0.8980392156862745], [0.9450980392156862], [0.0784313725490196], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.9725490196078431], [0.9450980392156862], [0.9058823529411765], [1.0], [0.5843137254901961], [0.1843137254901961], [0.9882352941176471], [0.8941176470588236], [1.0], [0.9490196078431372], [0.8470588235294118], [0.9333333333333333], [0.9098039215686274], [1.0], [0.8941176470588236], [0.8627450980392157], [0.9176470588235294], [0.9803921568627451], [0.21176470588235294], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [1.0], [0.9411764705882353], [0.9098039215686274], [1.0], [0.058823529411764705], [0.0], [1.0], [0.9294117647058824], [0.7490196078431373], [0.0], [0.0], [0.8392156862745098], [1.0], [0.050980392156862744], [0.4823529411764706], [1.0], [0.9176470588235294], [0.9882352941176471], [0.4470588235294118], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.023529411764705882], [1.0], [0.9333333333333333], [0.9372549019607843], [1.0], [0.6941176470588235], [0.0], [1.0], [1.0], [0.0], [0.5098039215686274], [0.4549019607843137], [0.1843137254901961], [0.2549019607843137], [0.16862745098039217], [0.1450980392156863], [1.0], [0.9254901960784314], [0.9764705882352941], [0.6352941176470588], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.12549019607843137], [1.0], [0.9254901960784314], [0.9607843137254902], [1.0], [0.8], [0.0], [1.0], [0.32941176470588235], [0.0], [0.1450980392156863], [0.10980392156862745], [0.12156862745098039], [0.0], [0.09803921568627451], [0.050980392156862744], [1.0], [0.9254901960784314], [0.9764705882352941], [0.7803921568627451], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.20784313725490197], [1.0], [0.9254901960784314], [0.9803921568627451], [0.9803921568627451], [0.9058823529411765], [0.00784313725490196], [1.0], [0.08235294117647059], [0.0], [0.8666666666666667], [1.0], [0.9254901960784314], [0.21176470588235294], [0.9607843137254902], [0.7764705882352941], [0.9529411764705882], [0.9333333333333333], [0.9607843137254902], [0.8745098039215686], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.3137254901960784], [1.0], [0.9294117647058824], [0.9803921568627451], [0.9411764705882353], [1.0], [0.0], [0.0], [0.15294117647058825], [0.615686274509804], [0.0], [0.0], [0.8431372549019608], [0.3686274509803922], [0.0784313725490196], [0.49411764705882355], [1.0], [0.9294117647058824], [0.9372549019607843], [0.9803921568627451], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.396078431372549], [1.0], [0.9215686274509803], [0.9921568627450981], [0.9568627450980393], [0.9529411764705882], [0.5215686274509804], [0.5411764705882353], [0.8156862745098039], [1.0], [0.788235294117647], [0.8392156862745098], [1.0], [0.9019607843137255], [0.027450980392156862], [0.6823529411764706], [1.0], [0.9411764705882353], [0.9333333333333333], [1.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.49411764705882355], [1.0], [0.9137254901960784], [1.0], [0.9725490196078431], [0.9137254901960784], [1.0], [1.0], [0.9411764705882353], [0.9098039215686274], [0.9529411764705882], [0.9529411764705882], [0.9058823529411765], [0.984313725490196], [1.0], [1.0], [0.996078431372549], [0.9529411764705882], [0.9333333333333333], [1.0], [0.011764705882352941], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.5764705882352941], [1.0], [0.9137254901960784], [0.9764705882352941], [0.7098039215686275], [0.9529411764705882], [0.8901960784313725], [0.8784313725490196], [0.9019607843137255], [0.9176470588235294], [0.9019607843137255], [0.9019607843137255], [0.9215686274509803], [0.8941176470588236], [0.9215686274509803], [0.8705882352941177], [0.8117647058823529], [1.0], [0.9254901960784314], [1.0], [0.13725490196078433], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.6392156862745098], [1.0], [0.9607843137254902], [0.8666666666666667], [0.33725490196078434], [1.0], [0.9137254901960784], [0.9137254901960784], [0.9215686274509803], [0.9254901960784314], [0.9176470588235294], [0.9176470588235294], [0.9176470588235294], [0.9098039215686274], [0.9490196078431372], [0.9058823529411765], [0.49019607843137253], [1.0], [0.9254901960784314], [1.0], [0.21568627450980393], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7098039215686275], [0.996078431372549], [1.0], [0.7843137254901961], [0.27058823529411763], [1.0], [0.8941176470588236], [0.9098039215686274], [0.9176470588235294], [0.9215686274509803], [0.9176470588235294], [0.9176470588235294], [0.9137254901960784], [0.9215686274509803], [0.9450980392156862], [0.9294117647058824], [0.27450980392156865], [1.0], [0.9215686274509803], [0.9647058823529412], [0.2235294117647059], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7725490196078432], [0.9686274509803922], [1.0], [0.7372549019607844], [0.43137254901960786], [1.0], [0.8784313725490196], [0.9137254901960784], [0.9176470588235294], [0.9176470588235294], [0.9176470588235294], [0.9176470588235294], [0.9176470588235294], [0.9176470588235294], [0.9411764705882353], [0.9921568627450981], [0.27058823529411763], [1.0], [0.9254901960784314], [0.9725490196078431], [0.30196078431372547], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7843137254901961], [0.9647058823529412], [1.0], [0.5843137254901961], [0.5686274509803921], [1.0], [0.8745098039215686], [0.9215686274509803], [0.9176470588235294], [0.9215686274509803], [0.9215686274509803], [0.9215686274509803], [0.9176470588235294], [0.9294117647058824], [0.9137254901960784], [1.0], [0.1843137254901961], [1.0], [0.9372549019607843], [0.9764705882352941], [0.3843137254901961], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.8], [0.9529411764705882], [1.0], [0.43529411764705883], [0.6784313725490196], [1.0], [0.8901960784313725], [0.9215686274509803], [0.9215686274509803], [0.9254901960784314], [0.9215686274509803], [0.9215686274509803], [0.9215686274509803], [0.9372549019607843], [0.8980392156862745], [1.0], [0.07450980392156863], [0.8901960784313725], [0.9647058823529412], [0.9764705882352941], [0.43137254901960786], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7686274509803922], [0.9411764705882353], [1.0], [0.42745098039215684], [0.8352941176470589], [0.9803921568627451], [0.8980392156862745], [0.9215686274509803], [0.9215686274509803], [0.9254901960784314], [0.9215686274509803], [0.9294117647058824], [0.9254901960784314], [0.9294117647058824], [0.8862745098039215], [1.0], [0.21568627450980393], [0.796078431372549], [0.984313725490196], [0.9607843137254902], [0.47058823529411764], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7529411764705882], [0.9529411764705882], [1.0], [0.4470588235294118], [0.9098039215686274], [0.9411764705882353], [0.9098039215686274], [0.9215686274509803], [0.9215686274509803], [0.9254901960784314], [0.9176470588235294], [0.9294117647058824], [0.9254901960784314], [0.9215686274509803], [0.8980392156862745], [1.0], [0.5254901960784314], [0.6705882352941176], [0.9882352941176471], [0.9568627450980393], [0.5372549019607843], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7411764705882353], [0.984313725490196], [1.0], [0.6039215686274509], [0.9333333333333333], [0.9137254901960784], [0.9254901960784314], [0.9176470588235294], [0.9215686274509803], [0.9254901960784314], [0.9215686274509803], [0.9333333333333333], [0.9254901960784314], [0.9215686274509803], [0.9098039215686274], [1.0], [0.6509803921568628], [0.49019607843137253], [1.0], [0.9529411764705882], [0.5568627450980392], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7176470588235294], [0.9882352941176471], [1.0], [0.6705882352941176], [0.9686274509803922], [0.9098039215686274], [0.9176470588235294], [0.9176470588235294], [0.9137254901960784], [0.9137254901960784], [0.9098039215686274], [0.9176470588235294], [0.9137254901960784], [0.9176470588235294], [0.9137254901960784], [0.9411764705882353], [0.8745098039215686], [0.5019607843137255], [1.0], [0.9490196078431372], [0.592156862745098], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.6980392156862745], [0.9529411764705882], [1.0], [0.2235294117647059], [0.9333333333333333], [0.9450980392156862], [0.9333333333333333], [0.9333333333333333], [0.9333333333333333], [0.9294117647058824], [0.9254901960784314], [0.9294117647058824], [0.9294117647058824], [0.9411764705882353], [0.9294117647058824], [0.996078431372549], [0.6901960784313725], [0.20392156862745098], [1.0], [0.9372549019607843], [0.615686274509804], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.7372549019607844], [0.9411764705882353], [0.9803921568627451], [0.24313725490196078], [0.8549019607843137], [1.0], [0.8627450980392157], [0.8705882352941177], [0.8705882352941177], [0.8705882352941177], [0.8745098039215686], [0.8745098039215686], [0.8784313725490196], [0.8705882352941177], [0.8549019607843137], [1.0], [0.6039215686274509], [0.12549019607843137], [1.0], [0.9254901960784314], [0.7372549019607844], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.5098039215686274], [0.9607843137254902], [0.9490196078431372], [0.09411764705882353], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.13333333333333333], [0.9490196078431372], [0.9568627450980393], [0.5294117647058824], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.2980392156862745], [1.0], [0.9764705882352941], [0.08627450980392157], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.15294117647058825], [0.9764705882352941], [1.0], [0.4823529411764706], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.19215686274509805], [0.803921568627451], [0.7725490196078432], [0.043137254901960784], [0.0], [0.01568627450980392], [0.00392156862745098], [0.00784313725490196], [0.00784313725490196], [0.00784313725490196], [0.00784313725490196], [0.00784313725490196], [0.00784313725490196], [0.00784313725490196], [0.00784313725490196], [0.011764705882352941], [0.0], [0.011764705882352941], [0.6823529411764706], [0.7411764705882353], [0.2627450980392157], [0.0], [0.0], [0.0]]], [[[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00392156862745098], [0.0], [0.2627450980392157], [0.6941176470588235], [0.5058823529411764], [0.6], [0.4588235294117647], [0.5058823529411764], [0.5725490196078431], [0.5529411764705883], [0.6862745098039216], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00784313725490196], [0.0], [0.7686274509803922], [1.0], [1.0], [1.0], [0.9450980392156862], [0.984313725490196], [1.0], [0.9607843137254902], [1.0], [0.2980392156862745], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.9529411764705882], [0.9294117647058824], [0.8509803921568627], [0.8941176470588236], [0.9058823529411765], [0.8705882352941177], [0.8549019607843137], [0.8588235294117647], [1.0], [0.4549019607843137], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [1.0], [0.9215686274509803], [0.9058823529411765], [0.9137254901960784], [0.8862745098039215], [0.8823529411764706], [0.8980392156862745], [0.8705882352941177], [1.0], [0.5686274509803921], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.09019607843137255], [1.0], [0.9019607843137255], [0.8980392156862745], [0.9137254901960784], [0.8980392156862745], [0.8823529411764706], [0.8901960784313725], [0.8666666666666667], [0.9450980392156862], [0.6549019607843137], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.2627450980392157], [1.0], [0.8823529411764706], [0.9176470588235294], [0.9058823529411765], [0.8862745098039215], [0.8901960784313725], [0.8941176470588236], [0.8784313725490196], [0.9176470588235294], [0.7333333333333333], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.4470588235294118], [0.9764705882352941], [0.8509803921568627], [0.9215686274509803], [0.9333333333333333], [0.9607843137254902], [0.8901960784313725], [0.8901960784313725], [0.8823529411764706], [0.9450980392156862], [0.6901960784313725], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.6549019607843137], [0.9686274509803922], [0.8901960784313725], [0.9058823529411765], [0.9803921568627451], [0.7843137254901961], [0.9725490196078431], [0.9058823529411765], [0.8784313725490196], [0.984313725490196], [0.5764705882352941], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.8156862745098039], [0.9490196078431372], [0.8823529411764706], [0.9529411764705882], [0.8823529411764706], [0.0], [1.0], [0.9137254901960784], [0.8862745098039215], [1.0], [0.5058823529411764], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.8745098039215686], [0.9333333333333333], [0.8745098039215686], [1.0], [0.6313725490196078], [0.0], [1.0], [0.9254901960784314], [0.8745098039215686], [1.0], [0.5294117647058824], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.9607843137254902], [0.9215686274509803], [0.8705882352941177], [1.0], [0.2823529411764706], [0.0], [0.9725490196078431], [0.996078431372549], [0.8509803921568627], [1.0], [0.5686274509803921], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [1.0], [0.9137254901960784], [0.8862745098039215], [1.0], [0.027450980392156862], [0.0], [0.7490196078431373], [0.9725490196078431], [0.8627450980392157], [1.0], [0.49411764705882355], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [1.0], [0.9137254901960784], [0.9058823529411765], [0.984313725490196], [0.0], [0.0], [0.6235294117647059], [0.984313725490196], [0.8666666666666667], [1.0], [0.43529411764705883], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [1.0], [0.9098039215686274], [0.9254901960784314], [0.8470588235294118], [0.0], [0.0], [0.5137254901960784], [0.9921568627450981], [0.8627450980392157], [1.0], [0.43529411764705883], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [1.0], [0.8941176470588236], [0.9529411764705882], [0.6745098039215687], [0.0], [0.0], [0.2235294117647059], [0.9764705882352941], [0.8705882352941177], [1.0], [0.43529411764705883], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [1.0], [0.9019607843137255], [0.9568627450980393], [0.5450980392156862], [0.0], [0.0], [0.0392156862745098], [1.0], [0.8901960784313725], [1.0], [0.39215686274509803], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.8901960784313725], [0.9294117647058824], [0.9490196078431372], [0.44313725490196076], [0.0], [0.0], [0.023529411764705882], [1.0], [0.9019607843137255], [1.0], [0.34901960784313724], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.8], [0.9372549019607843], [0.9607843137254902], [0.592156862745098], [0.0], [0.0], [0.0], [1.0], [0.8901960784313725], [1.0], [0.38823529411764707], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.592156862745098], [0.9607843137254902], [0.9333333333333333], [0.7764705882352941], [0.0], [0.0], [0.0], [1.0], [0.9176470588235294], [1.0], [0.3607843137254902], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.34901960784313724], [0.9725490196078431], [0.9137254901960784], [0.9725490196078431], [0.0], [0.0], [0.0], [0.9882352941176471], [0.9294117647058824], [1.0], [0.35294117647058826], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.12156862745098039], [0.9411764705882353], [0.8980392156862745], [0.8862745098039215], [0.0], [0.0], [0.0], [0.9372549019607843], [0.9333333333333333], [1.0], [0.3607843137254902], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.8862745098039215], [0.9137254901960784], [0.9294117647058824], [0.13333333333333333], [0.0], [0.0], [0.9176470588235294], [0.9333333333333333], [1.0], [0.37254901960784315], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.9137254901960784], [0.9254901960784314], [0.9568627450980393], [0.26666666666666666], [0.0], [0.0], [0.8196078431372549], [0.9450980392156862], [0.9294117647058824], [0.3843137254901961], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00392156862745098], [0.0], [0.596078431372549], [0.9490196078431372], [0.9607843137254902], [0.5019607843137255], [0.0], [0.0], [0.7764705882352941], [0.9450980392156862], [0.9333333333333333], [0.3176470588235294], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00784313725490196], [0.0], [0.28627450980392155], [0.9647058823529412], [0.9450980392156862], [0.8274509803921568], [0.0], [0.0], [0.792156862745098], [0.9411764705882353], [0.9294117647058824], [0.2901960784313726], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.01568627450980392], [0.0], [0.0], [0.8980392156862745], [0.9254901960784314], [0.8196078431372549], [0.0], [0.0], [0.6196078431372549], [0.9686274509803922], [0.9333333333333333], [0.38823529411764707], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.00392156862745098], [0.0], [0.0], [0.7803921568627451], [1.0], [0.9686274509803922], [0.22745098039215686], [0.0], [0.6313725490196078], [1.0], [0.9882352941176471], [0.4666666666666667], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]], [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.3843137254901961], [0.6235294117647059], [0.2784313725490196], [0.0], [0.0], [0.26666666666666666], [0.6901960784313725], [0.6431372549019608], [0.22745098039215686], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0.0]]]]\n", "images = numpy.array(images)\n", "\n", "image = images[0]\n", "plt.figure()\n", "plt.imshow(image)\n", "\n", "data = json.dumps({\"signature_name\": \"serving_default\", \"instances\": images[0:3].tolist()})\n", "print('Data: {} ... {}'.format(data[:50], data[len(data)-52:]))"]}, {"cell_type": "code", "execution_count": 21, "id": "211d8dff-90f9-414b-929e-8b16d57beefb", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import requests\n", "import matplotlib.pyplot as plt\n", "headers = {\"content-type\": \"application/json\"}\n", "json_response = requests.post('http://mnist-202208011.service:8501/v1/models/mnist:predict', data=data, headers=headers)\n", "predictions = json.loads(json_response.text)['predictions']\n", "\n", "def show(idx, title):\n", "  plt.figure()\n", "  plt.imshow(images[idx].reshape(28,28))\n", "  plt.axis('off')\n", "  plt.title('\\n\\n{}'.format(title), fontdict={'size': 16})\n", "\n", "show(0, 'The model thought this was a {}'.format(class_names[numpy.argmax(predictions[0])]))"]}, {"cell_type": "code", "execution_count": null, "id": "18f833d0-7bdb-4156-b5ce-617328814791", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}