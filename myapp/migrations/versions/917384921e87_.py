"""empty message

Revision ID: 917384921e87
Revises: ba5f3a6868ac
Create Date: 2022-10-09 12:25:01.490951

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '917384921e87'
down_revision = 'ba5f3a6868ac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('aihub', sa.Column('pipeline', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('aihub', 'pipeline')
    # ### end Alembic commands ###
