apiVersion: v1
data:
  Corefile: |
    .:53 {
        errors
        health {
          lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
          pods insecure
          fallthrough in-addr.arpa ip6.arpa
        }
        # 自定义host
        hosts {
          *************    mirrors.aliyun.com
          *************    ccr.ccs.tencentyun.com
          *************    registry-1.docker.io
          *************    auth.docker.io
          ************     hub.docker.com
          *************    www.modelscope.cn
          *************    modelscope.oss-cn-beijing.aliyuncs.com
          *************    archive.ubuntu.com
          *************    security.ubuntu.com
          *************    cloud.r-project.org
          *************    deb.nodesource.com
          *************    docker-76009.sz.gfp.tencent-cloud.com
          fallthrough
        }
        prometheus :9153
        forward . "/etc/resolv.conf"
        cache 30
        loop
        reload
        loadbalance
    }
    # oa.com结尾的域名使用此类dns代理
    oa.com:53 {
      errors
      cache 30
      hosts {
        *******    example3.oa.com
        *******    example4.oa.com
        fallthrough
      }
      forward . ******* *******
    }
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system

