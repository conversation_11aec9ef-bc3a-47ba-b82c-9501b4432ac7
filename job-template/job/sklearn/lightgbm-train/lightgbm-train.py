import pandas as pd
import lightgbm as lgb
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import argparse

def load_and_train_lightgbm(
    path: str,
    columns: list = None,
    na_values: list = None,
    test_size: float = 0.2,
    random_state: int = 42
):
    """
    加载数据并使用 LightGBM 进行训练

    参数：
    - path: 数据文件路径（CSV）
    - columns: 数据列名列表（最后一列为 target）
    - na_values: 缺失值标记（如 ["?", ""]）
    - test_size: 测试集占比
    - random_state: 随机种子
    """
    if columns is None:
        columns = ['sepal length (cm)', 'sepal width (cm)',
                   'petal length (cm)', 'petal width (cm)', 'target']
    if na_values is None:
        na_values = ["?", ""]

    # 加载数据
    df = pd.read_csv(path, header=None, names=columns, na_values=na_values)

    X = df[columns[:-1]]
    y = df[columns[-1]]

    # 标签编码
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)

    # 划分训练/测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y_encoded, test_size=test_size, random_state=random_state, stratify=y_encoded
    )

    # LightGBM 数据集
    lgb_train = lgb.Dataset(X_train, label=y_train)
    lgb_eval = lgb.Dataset(X_test, label=y_test, reference=lgb_train)

    # 训练参数
    params = {
        'objective': 'multiclass',
        'num_class': len(le.classes_),
        'metric': 'multi_logloss',
        'verbosity': -1,
        'seed': random_state
    }

    # 模型训练
    print("[INFO] 开始 LightGBM 训练...")
    model = lgb.train(
        params,
        lgb_train,
        valid_sets=[lgb_train, lgb_eval],
        valid_names=['train', 'valid'],
        num_boost_round=100,
        # early_stopping_rounds=10,
        # verbose_eval=False
    )

    # 预测 & 评估
    y_pred = model.predict(X_test)
    y_pred_labels = y_pred.argmax(axis=1)
    acc = accuracy_score(y_test, y_pred_labels)

    print(f"[INFO] LightGBM 模型准确率: {acc:.4f}")
    return model, le, acc

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="LightGBM训练")
    parser.add_argument('--input_path', type=str, required=True, help="输入数据路径")
    parser.add_argument('--columns', type=str, required=True, help="列头")
    args = parser.parse_args()

    # ✅ 示例调用
    model, label_encoder, accuracy = load_and_train_lightgbm(
        path=args.input_path,
        columns=str.split(args.columns, ",")
    )