---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: infra-mysql-pv
  labels:
    infra-pvname: infra-mysql-pv
spec:
  capacity:       
    storage: 10Gi
  accessModes:   
    - ReadWriteMany   
  hostPath:
    path: /data/k8s/infra/mysql
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: infra-mysql-pvc
  namespace: infra
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
#  selector:
#    matchLabels:
#      infra-pvname: infra-mysql-pv
  volumeName: infra-mysql-pv
  storageClassName: ""


