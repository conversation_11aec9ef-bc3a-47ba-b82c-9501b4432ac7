from enum import Enum
from itertools import product
from allpairspy import AllPairs
import numpy as np
import random
import os

# 数据生成量限制
HYPER_MAX_GEENERATE_COUNT = int(os.environ.get("DATA_MAX_GEENERATE_COUNT", 10E2))


class EnumMapName(Enum):
    """定义一个枚举映射一个中文名称"""
    def __new__(cls, value, cn_name):
        obj = object.__new__(cls)
        obj._value_ = value
        return obj

    def __init__(self, _, cn_name):
        # 初始化时设置中文名属性
        self.cn_name = cn_name

    @classmethod
    def is_member(cls, val):
        """判断是否是枚举成员"""
        members = [member.value for member in cls.__members__.values()]
        return  val in  members
    
    @classmethod
    def get_map_name(cls, val):
        """获取枚举成员的中文名"""
        for member in cls.__members__.values():
            if member.value == val:
                return member.cn_name
        return None
    
    @classmethod
    def get_value_map_name(cls):
        """获取所有枚举成员对应的中文名"""
        return [{"value": member.value, "name": member.cn_name} for member in cls.__members__.values()]


class HyperParamsKind(EnumMapName):
    """生成超参的类型"""
    RANDOM = ("random", "随机采样")
    GRID = ("grid", "网格组合")
    RTHOGONAL = ("rthogonal", "正交实验")


    @classmethod
    def generate_params(cls, hyperparams:dict):
        """生成超参"""
        kind = hyperparams.get("kind")
        if cls.is_member(kind) == False:
            raise Exception(f"kind: {kind} is not a member of HyperParamsKind")
        
        hyperparams = hyperparams.get("hyper_params")

        values = []
        names = []
        generate_count = 1
        for param in hyperparams:
            step = param.get("step")
            # 整数默认步长为1，浮点数默认步长为0.1
            if step is None:
                step = 1 if param.get("type") == "int" else 0.1
            
            # 生成超惨
            min_value = param.get("min")
            max_value = param.get("max")
            range_values = []
            if (min is not None) and (max is not None):
                range_values = np.arange(min_value, max_value, step).tolist()
                range_values.append(max_value)
            
            # 检查生成的数据是否超出限制
            generate_count *= len(range_values)
            if generate_count > HYPER_MAX_GEENERATE_COUNT:
                raise Exception(f"hyper params generate count: {generate_count}(max count:{HYPER_MAX_GEENERATE_COUNT}) is too large, please reduce the step or max value")
            
            names.append(param.get("name"))
            # 添加值
            values.append(range_values)

        if kind == HyperParamsKind.RANDOM.value:
            return { "names": names, "params": cls.get_random_params(values) }
        elif kind == HyperParamsKind.GRID.value:
            return { "names": names, "params": cls.get_grid_params(values) }
        elif kind == HyperParamsKind.RTHOGONAL.value:
            return { "names": names, "params": cls.get_orthogonal_params(values) }

    @staticmethod
    def get_grid_params(origin_params: []):
        # 参数设计方式-网格组合
        result = list(product(*origin_params))
        ret = []
        for arr in result:
            ret.append([x for x in arr])
        return ret

    @staticmethod
    def get_orthogonal_params(origin_params: []):
        # 参数设计方式-正交实验：
        ret = []
        for i, pairs in enumerate(AllPairs(origin_params)):
            ret.append(pairs)
        return ret

    @classmethod
    def get_random_params(cls, origin_params: [], count: int=3):
         # 参数设计方式-随机采样
        grid_params = cls.get_grid_params(origin_params)
        if count >= len(grid_params):
            return grid_params
        indexes = random.sample([i for i in range(0, len(grid_params))], count)
        return [grid_params[i] for i in indexes]


if __name__ == "__main__":
    params_random = {
        "kind": "random",
        "hyper_params": [
            {"name": "lr", "type": "float", "min": 0.0001, "max": 0.001, "step": 0.01},
            {"name": "batch_size", "type": "int", "min": 16, "max": 64, "step": 1},
        ]
    }
    params_grid = {
        "kind": "grid",
        "hyper_params": [
            {"name": "lr", "type": "float", "min": 0.0001, "max": 0.001, "step": 0.01},
            {"name": "batch_size", "type": "int", "min": 16, "max": 64, "step": 1},
        ]
    }
    params_rthogonal = {
        "kind": "rthogonal",
        "hyper_params": [
            {"name": "lr", "type": "float", "min": 0.0001, "max": 0.001, "step": 0.01},
            {"name": "batch_size", "type": "int", "min": 16, "max": 64, "step": 1},
        ]
    }
    # print(HyperParamsKind.RANDOM.value)
    # print(HyperParamsKind.RANDOM.cn_name)
    # print(HyperParamsKind.get_value_map_name())
    # print(HyperParamsKind.generate_params(params_random))
    # print(HyperParamsKind.generate_params(params_grid))
    print(HyperParamsKind.generate_params(params_rthogonal))