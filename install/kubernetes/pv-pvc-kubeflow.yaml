

# 模型训练
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: kubeflow-kubeflow-user-workspace
  labels:
    kubeflow-pvname: kubeflow-kubeflow-user-workspace
spec:
#  storageClassName: pipeline
  capacity:
    storage: 500Gi
  accessModes:
    - ReadWriteMany
  hostPath:
    path: /data/k8s/kubeflow/pipeline/workspace
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kubeflow-user-workspace
  namespace: kubeflow
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      kubeflow-pvname: kubeflow-kubeflow-user-workspace
  storageClassName: ""
  volumeName: kubeflow-kubeflow-user-workspace