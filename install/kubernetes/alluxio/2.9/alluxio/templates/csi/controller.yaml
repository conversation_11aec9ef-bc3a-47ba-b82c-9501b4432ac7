#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ if .Values.csi.enabled -}}
{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ $fullName }}-csi-controller
  labels:
    name: {{ $fullName }}-csi-controller
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-csi-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ $name }}
      role: alluxio-csi-controller
      name: {{ $fullName }}-csi-controller
  template:
    metadata:
      labels:
        name: {{ $fullName }}-csi-controller
        app: {{ $name }}
        chart: {{ $chart }}
        release: {{ .Release.Name }}
        heritage: {{ .Release.Service }}
        role: alluxio-csi-controller
    spec:
      serviceAccount: csi-controller-sa
      hostNetwork: {{ .Values.csi.controllerPlugin.hostNetwork }}
      dnsPolicy: {{ .Values.csi.controllerPlugin.dnsPolicy }}
      serviceAccountName: csi-controller-sa
      {{- if .Values.imagePullSecrets }}
{{ include "alluxio.imagePullSecrets" . | indent 6 }}
      {{- end}}
      containers:
        - name: csi-provisioner
          image: {{ .Values.csi.controllerPlugin.provisioner.image }}
          args:
            - "--v=5"
            - "--csi-address=$(ADDRESS)"
            - "--timeout=60s"
            - "--volume-name-prefix=alluxio"
          env:
            - name: ADDRESS
              value: /csi/csi.sock
          volumeMounts:
            - mountPath: /csi
              name: socket-dir
          {{- if .Values.csi.controllerPlugin.provisioner.resources  }}
          resources:
            {{- if .Values.csi.controllerPlugin.provisioner.resources.limits }}
            limits:
              cpu: {{ .Values.csi.controllerPlugin.provisioner.resources.limits.cpu }}
              memory: {{ .Values.csi.controllerPlugin.provisioner.resources.limits.memory }}
            {{- end }}
            {{- if .Values.csi.controllerPlugin.provisioner.resources.requests }}
            requests:
              cpu: {{ .Values.csi.controllerPlugin.provisioner.resources.requests.cpu }}
              memory: {{ .Values.csi.controllerPlugin.provisioner.resources.requests.memory }}
            {{- end }}
          {{- end }}
        - name: csi-controller
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.csi.imagePullPolicy }}
          command: ["tini", "--", "/entrypoint.sh"]
          args:
            - csiserver
            - --v=5
            - "--nodeid=$(NODE_ID)"
            - "--endpoint=$(CSI_ENDPOINT)"
          env:
            - name: NODE_ID
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: CSI_ENDPOINT
              value: unix://csi/csi.sock
          envFrom:
            - configMapRef:
                name: {{ template "alluxio.fullname" . }}-config
          volumeMounts:
            - mountPath: /csi
              name: socket-dir
          {{- if .Values.csi.controllerPlugin.controller.resources  }}
          resources:
            {{- if .Values.csi.controllerPlugin.controller.resources.limits }}
            limits:
              cpu: {{ .Values.csi.controllerPlugin.controller.resources.limits.cpu }}
              memory: {{ .Values.csi.controllerPlugin.controller.resources.limits.memory }}
            {{- end }}
            {{- if .Values.csi.controllerPlugin.controller.resources.requests }}
            requests:
              cpu: {{ .Values.csi.controllerPlugin.controller.resources.requests.cpu }}
              memory: {{ .Values.csi.controllerPlugin.controller.resources.requests.memory }}
            {{- end }}
          {{- end }}
      volumes:
        - name: socket-dir
          emptyDir: {}
{{- end }}
