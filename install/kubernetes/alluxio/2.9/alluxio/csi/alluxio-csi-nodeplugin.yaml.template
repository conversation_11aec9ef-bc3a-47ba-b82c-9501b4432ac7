---
# Source: alluxio/templates/csi/nodeplugin.yaml
#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#



kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: alluxio-csi-nodeplugin
  labels:
    name: alluxio-csi-nodeplugin
    app: alluxio
    chart: alluxio-0.6.53
    release: alluxio
    heritage: Helm
    role: alluxio-csi-nodeplugin
spec:
  selector:
    matchLabels:
      app: alluxio
      role: alluxio-csi-nodeplugin
      name: alluxio-csi-nodeplugin
  template:
    metadata:
      labels:
        name: alluxio-csi-nodeplugin
        app: alluxio
        chart: alluxio-0.6.53
        release: alluxio
        heritage: Helm
        role: alluxio-csi-nodeplugin
    spec:
      serviceAccount: csi-controller-sa
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
        - name: node-driver-registrar
          image: k8s.gcr.io/sig-storage/csi-node-driver-registrar:v2.0.0
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "rm -rf /registration/alluxio /registration/alluxio-reg.sock"]
          args:
            - --v=5
            - --csi-address=/plugin/csi.sock
            - --kubelet-registration-path=/var/lib/kubelet/plugins/csi-alluxio-plugin/csi.sock
          resources:
            limits:
              cpu: 100m
              memory: 100Mi
            requests:
              cpu: 10m
              memory: 20Mi
          env:
            - name: KUBE_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          volumeMounts:
            - name: plugin-dir
              mountPath: /plugin
            - name: registration-dir
              mountPath: /registration
        - name: csi-nodeserver
          # run as root user, mount command need root privilege
          securityContext:
            privileged: true
            runAsUser: 0
            runAsGroup: 0
            capabilities:
              add: ["SYS_ADMIN"]
          resources:
            limits:
              cpu: 4
              memory: 8Gi
            requests:
              cpu: 1
              memory: 1Gi
          image: alluxio/alluxio:2.9.0
          imagePullPolicy: IfNotPresent
          command: ["tini", "--", "/entrypoint.sh"]
          args :
            - csiserver
            - --v=5
            - "--nodeid=$(NODE_ID)"
            - "--endpoint=$(CSI_ENDPOINT)"
          env:
            - name: ALLUXIO_CLIENT_HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: NODE_ID
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: CSI_ENDPOINT
              value: unix://plugin/csi.sock
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: alluxio-config
          volumeMounts:
            - name: plugin-dir
              mountPath: /plugin
            - name: pods-mount-dir
              mountPath: /var/lib/kubelet
              mountPropagation: "Bidirectional"
            - name: csi-fuse-config
              mountPath: /opt/alluxio/integration/kubernetes/csi
      volumes:
        - name: plugin-dir
          hostPath:
            path: /var/lib/kubelet/plugins/csi-alluxio-plugin
            type: DirectoryOrCreate
        - name: pods-mount-dir
          hostPath:
            path: /var/lib/kubelet
            type: Directory
        - hostPath:
            path: /var/lib/kubelet/plugins_registry
            type: Directory
          name: registration-dir
        - name: csi-fuse-config
          configMap:
            name: alluxio-csi-fuse-config
            items:
              - key: alluxio-csi-fuse-config
                path: alluxio-csi-fuse.yaml
