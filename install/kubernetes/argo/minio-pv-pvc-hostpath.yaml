---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: kubeflow-minio-pv
  labels:
    kubeflow-pvname: kubeflow-minio-pv
spec:
  capacity:       
    storage: 500Gi
  accessModes:   
    - ReadWriteOnce   
  hostPath:
    path: /data/k8s/kubeflow/minio
  persistentVolumeReclaimPolicy: Retain

---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: minio-pvc
  namespace: kubeflow
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      kubeflow-pvname: kubeflow-minio-pv
  volumeName: kubeflow-minio-pv
  storageClassName: ""
