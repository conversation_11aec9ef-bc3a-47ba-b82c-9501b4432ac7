apiVersion: v1
kind: PersistentVolume
metadata:
  name: service-kubeflow-user-workspace
  labels:
    service-pvname: service-kubeflow-user-workspace
spec:
  capacity:
    storage: 500Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
  csi:
    driver: csi.juicefs.com
    volumeHandle: service-kubeflow-user-workspace
    fsType: juicefs
    nodePublishSecretRef:
      name: juicefs-sc-secret
      namespace: kube-system
    volumeAttributes:
      juicefs/mount-cpu-limit: 5000m
      juicefs/mount-memory-limit: 5Gi
      juicefs/mount-cpu-request: 1m
      juicefs/mount-memory-request: 1Mi
  mountOptions:
    - subdir=kubeflow/pipeline/workspace
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: service-kubeflow-archives
  labels:
    service-pvname: service-kubeflow-archives
spec:
  capacity:
    storage: 500Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
  csi:
    driver: csi.juicefs.com
    volumeHandle: service-kubeflow-archives
    fsType: juicefs
    nodePublishSecretRef:
      name: juicefs-sc-secret
      namespace: kube-system
    volumeAttributes:
      juicefs/mount-cpu-limit: 5000m
      juicefs/mount-memory-limit: 5Gi
      juicefs/mount-cpu-request: 1m
      juicefs/mount-memory-request: 1Mi
  mountOptions:
    - subdir=kubeflow/pipeline/archives
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kubeflow-user-workspace
  namespace: service
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      service-pvname: service-kubeflow-user-workspace
  storageClassName: ""
  volumeName: service-kubeflow-user-workspace
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kubeflow-archives
  namespace: service
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      service-pvname: service-kubeflow-archives
  storageClassName: ""
  volumeName: service-kubeflow-archives