/*! For license information please see 485.5df85c3d.chunk.js.LICENSE.txt */
(self.webpackChunkvite_ml_platform=self.webpackChunkvite_ml_platform||[]).push([[485],{23629:function(e,t,n){"use strict";n.d(t,{W:function(){return It}});var o,r=n(40433),i=n(75971),a=n(72791),s=n(79292),l=n(42792),c=n(87376),d=n(9691),u=n(29723),p=n(96971),f=n(8877),h=n(68959),g=n(40382),m=n(84688),y=n(61460),v=n(92853),x=n(91106),b=n(65260),_=n(78498),k=n(90511),w=n(18511),C=n(25443),S=function(e){var t=e.count,n=e.indentWidth,o=void 0===n?36:n,r=e.role,i=void 0===r?"presentation":r,s=t*o;return t>0?a.createElement("span",{className:"ms-GroupSpacer",style:{display:"inline-block",width:s},role:i}):null};!function(e){e[e.hidden=0]="hidden",e[e.visible=1]="visible"}(o||(o={}));var E=n(45651),T=n(67676),D=(0,s.y)(),R=a.forwardRef((function(e,t){var n=e.checked,o=void 0!==n&&n,r=e.className,i=e.theme,s=e.styles,l=e.useFastIcons,c=void 0===l||l,d=D(s,{theme:i,className:r,checked:o}),u=c?k.xu:w.J;return a.createElement("div",{className:d.root,ref:t},a.createElement(u,{iconName:"CircleRing",className:d.circle}),a.createElement(u,{iconName:"StatusCircleCheckmark",className:d.check}))}));R.displayName="CheckBase";var P=n(40528),I={root:"ms-Check",circle:"ms-Check-circle",check:"ms-Check-check",checkHost:"ms-Check-checkHost"},M=(0,r.z)(R,(function(e){var t,n,o,r,a,s=e.height,l=void 0===s?e.checkBoxHeight||"18px":s,d=e.checked,u=e.className,p=e.theme,f=p.palette,h=p.semanticColors,g=p.fonts,m=(0,c.zg)(p),y=(0,P.Cn)(I,p),v={fontSize:l,position:"absolute",left:0,top:0,width:l,height:l,textAlign:"center",display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle"};return{root:[y.root,g.medium,{lineHeight:"1",width:l,height:l,verticalAlign:"top",position:"relative",userSelect:"none",selectors:(t={":before":{content:'""',position:"absolute",top:"1px",right:"1px",bottom:"1px",left:"1px",borderRadius:"50%",opacity:1,background:h.bodyBackground}},t[".".concat(y.checkHost,":hover &, .").concat(y.checkHost,":focus &, &:hover, &:focus")]={opacity:1},t)},d&&["is-checked",{selectors:{":before":{background:f.themePrimary,opacity:1,selectors:(n={},n[P.qJ]={background:"Window"},n)}}}],u],circle:[y.circle,v,{color:f.neutralSecondary,selectors:(o={},o[P.qJ]={color:"WindowText"},o)},d&&{color:f.white}],check:[y.check,v,{opacity:0,color:f.neutralSecondary,fontSize:P.ld.medium,left:m?"-0.5px":".5px",top:"-1px",selectors:(r={":hover":{opacity:1}},r[P.qJ]=(0,i.pi)({},(0,P.xM)()),r)},d&&{opacity:1,color:f.white,fontWeight:900,selectors:(a={},a[P.qJ]={border:"none",color:"WindowText"},a)}],checkHost:y.checkHost}}),void 0,{scope:"Check"},!0),O=n(29134),L=n(61910),A={tooltipHost:"ms-TooltipHost",root:"ms-DetailsHeader",cell:"ms-DetailsHeader-cell",cellIsCheck:"ms-DetailsHeader-cellIsCheck",collapseButton:"ms-DetailsHeader-collapseButton",isCollapsed:"is-collapsed",isAllSelected:"is-allSelected",isSelectAllHidden:"is-selectAllHidden",isResizingColumn:"is-resizingColumn",cellSizer:"ms-DetailsHeader-cellSizer",isResizing:"is-resizing",dropHintCircleStyle:"ms-DetailsHeader-dropHintCircleStyle",dropHintCaretStyle:"ms-DetailsHeader-dropHintCaretStyle",dropHintLineStyle:"ms-DetailsHeader-dropHintLineStyle",cellTitle:"ms-DetailsHeader-cellTitle",cellName:"ms-DetailsHeader-cellName",filterChevron:"ms-DetailsHeader-filterChevron",gripperBarVertical:"ms-DetailsColumn-gripperBarVertical",checkTooltip:"ms-DetailsHeader-checkTooltip",check:"ms-DetailsHeader-check"},z=function(e){var t=e.theme,n=e.cellStyleProps,o=void 0===n?O.Wt:n,r=t.semanticColors;return[(0,P.Cn)(A,t).cell,(0,P.GL)(t),{color:r.bodyText,position:"relative",display:"inline-block",boxSizing:"border-box",padding:"0 ".concat(o.cellRightPadding,"px 0 ").concat(o.cellLeftPadding,"px"),lineHeight:"inherit",margin:"0",height:42,verticalAlign:"top",whiteSpace:"nowrap",textOverflow:"ellipsis",textAlign:"left"}]},N={root:"ms-DetailsRow-check",isDisabled:"ms-DetailsRow-check--isDisabled",isHeader:"ms-DetailsRow-check--isHeader"},H=n(47535),F=(0,s.y)(),B=a.memo((function(e){return a.createElement(M,{theme:e.theme,checked:e.checked,className:e.className,useFastIcons:!0})}));function j(e){return a.createElement(M,{checked:e.checked})}function W(e){return a.createElement(B,{theme:e.theme,checked:e.checked})}var U,V=(0,r.z)((function(e){var t=e.isVisible,n=void 0!==t&&t,o=e.canSelect,r=void 0!==o&&o,s=e.anySelected,l=void 0!==s&&s,c=e.selected,d=void 0!==c&&c,p=e.selectionMode,f=e.isHeader,h=void 0!==f&&f,g=e.className,m=(e.checkClassName,e.styles),y=e.theme,v=e.compact,x=e.onRenderDetailsCheckbox,b=e.useFastIcons,_=void 0===b||b,k=(0,i._T)(e,["isVisible","canSelect","anySelected","selected","selectionMode","isHeader","className","checkClassName","styles","theme","compact","onRenderDetailsCheckbox","useFastIcons"]),w=_?W:j,C=x?(0,E.k)(x,w):w,S=F(m,{theme:y,canSelect:r,selected:d,anySelected:l,className:g,isHeader:h,isVisible:n,compact:v}),D={checked:d,theme:y},R=(0,T.n)("div",k,["aria-label","aria-labelledby","aria-describedby"]),P=p===H.oW.single?"radio":"checkbox";return r?a.createElement("div",(0,i.pi)({},k,{role:P,className:(0,u.i)(S.root,S.check),"aria-checked":d,"data-selection-toggle":!0,"data-automationid":"DetailsRowCheck",tabIndex:-1}),C(D)):a.createElement("div",(0,i.pi)({},R,{className:(0,u.i)(S.root,S.check)}))}),(function(e){var t=e.theme,n=e.className,o=e.isHeader,r=e.selected,i=e.anySelected,a=e.canSelect,s=e.compact,l=e.isVisible,c=(0,P.Cn)(N,t),d=O.lv.rowHeight,u=O.lv.compactRowHeight,p=o?42:s?u:d,f=l||r||i;return{root:[c.root,n],check:[!a&&c.isDisabled,o&&c.isHeader,(0,P.GL)(t),t.fonts.small,I.checkHost,{display:"flex",alignItems:"center",justifyContent:"center",cursor:"default",boxSizing:"border-box",verticalAlign:"top",background:"none",backgroundColor:"transparent",border:"none",opacity:f?1:0,height:p,width:48,padding:0,margin:0}],isDisabled:[]}}),void 0,{scope:"DetailsRowCheck"},!0),G=n(16455),q=function(){function e(e){this._selection=e.selection,this._dragEnterCounts={},this._activeTargets={},this._lastId=0,this._initialized=!1}return e.prototype.dispose=function(){this._events&&this._events.dispose()},e.prototype.subscribe=function(e,t,n){var o=this;if(!this._initialized){this._events=new v.r(this);var r=(0,G.M)();r&&(this._events.on(r.body,"mouseup",this._onMouseUp.bind(this),!0),this._events.on(r,"mouseup",this._onDocumentMouseUp.bind(this),!0)),this._initialized=!0}var i,a,s,l,c,d,u,p,f,h,g=n.key,m=void 0===g?"".concat(++this._lastId):g,y=[];if(n&&e){var x=n.eventMap,b=n.context,_=n.updateDropState,k={root:e,options:n,key:m};if(p=this._isDraggable(k),f=this._isDroppable(k),(p||f)&&x)for(var w=0,C=x;w<C.length;w++){var S=C[w],E={callback:S.callback.bind(null,b),eventName:S.eventName};y.push(E),this._events.on(e,E.eventName,E.callback)}f&&(a=function(e){e.isHandled||(e.isHandled=!0,o._dragEnterCounts[m]--,0===o._dragEnterCounts[m]&&_(!1,e))},s=function(e){e.preventDefault(),e.isHandled||(e.isHandled=!0,o._dragEnterCounts[m]++,1===o._dragEnterCounts[m]&&_(!0,e))},l=function(e){o._dragEnterCounts[m]=0,_(!1,e)},c=function(e){o._dragEnterCounts[m]=0,_(!1,e),n.onDrop&&n.onDrop(n.context.data,e)},d=function(e){e.preventDefault(),n.onDragOver&&n.onDragOver(n.context.data,e)},this._dragEnterCounts[m]=0,t.on(e,"dragenter",s),t.on(e,"dragleave",a),t.on(e,"dragend",l),t.on(e,"drop",c),t.on(e,"dragover",d)),p&&(u=this._onMouseDown.bind(this,k),l=this._onDragEnd.bind(this,k),i=function(t){var r=n;r&&r.onDragStart&&r.onDragStart(r.context.data,r.context.index,o._selection.getSelection(),t),o._isDragging=!0,t.dataTransfer&&t.dataTransfer.setData("id",e.id)},t.on(e,"dragstart",i),t.on(e,"mousedown",u),t.on(e,"dragend",l)),h={target:k,dispose:function(){if(o._activeTargets[m]===h&&delete o._activeTargets[m],e){for(var n=0,r=y;n<r.length;n++){var g=r[n];o._events.off(e,g.eventName,g.callback)}f&&(t.off(e,"dragenter",s),t.off(e,"dragleave",a),t.off(e,"dragend",l),t.off(e,"dragover",d),t.off(e,"drop",c)),p&&(t.off(e,"dragstart",i),t.off(e,"mousedown",u),t.off(e,"dragend",l))}}},this._activeTargets[m]=h}return{key:m,dispose:function(){h&&h.dispose()}}},e.prototype.unsubscribe=function(e,t){var n=this._activeTargets[t];n&&n.dispose()},e.prototype._onDragEnd=function(e,t){var n=e.options;n.onDragEnd&&n.onDragEnd(n.context.data,t)},e.prototype._onMouseUp=function(e){if(this._isDragging=!1,this._dragData){for(var t=0,n=Object.keys(this._activeTargets);t<n.length;t++){var o=n[t],r=this._activeTargets[o];r.target.root&&(this._events.off(r.target.root,"mousemove"),this._events.off(r.target.root,"mouseleave"))}this._dragData.dropTarget&&(v.r.raise(this._dragData.dropTarget.root,"dragleave"),v.r.raise(this._dragData.dropTarget.root,"drop"))}this._dragData=null},e.prototype._onDocumentMouseUp=function(e){var t=(0,G.M)();t&&e.target===t.documentElement&&this._onMouseUp(e)},e.prototype._onMouseMove=function(e,t){var n=t.buttons,o=void 0===n?1:n;if(this._dragData&&1!==o)this._onMouseUp(t);else{var r=e.root,i=e.key;this._isDragging&&this._isDroppable(e)&&this._dragData&&this._dragData.dropTarget&&this._dragData.dropTarget.key!==i&&!this._isChild(r,this._dragData.dropTarget.root)&&this._dragEnterCounts[this._dragData.dropTarget.key]>0&&(v.r.raise(this._dragData.dropTarget.root,"dragleave"),v.r.raise(r,"dragenter"),this._dragData.dropTarget=e)}},e.prototype._onMouseLeave=function(e,t){this._isDragging&&this._dragData&&this._dragData.dropTarget&&this._dragData.dropTarget.key===e.key&&(v.r.raise(e.root,"dragleave"),this._dragData.dropTarget=void 0)},e.prototype._onMouseDown=function(e,t){if(0===t.button)if(this._isDraggable(e)){this._dragData={clientX:t.clientX,clientY:t.clientY,eventTarget:t.target,dragTarget:e};for(var n=0,o=Object.keys(this._activeTargets);n<o.length;n++){var r=o[n],i=this._activeTargets[r];i.target.root&&(this._events.on(i.target.root,"mousemove",this._onMouseMove.bind(this,i.target)),this._events.on(i.target.root,"mouseleave",this._onMouseLeave.bind(this,i.target)))}}else this._dragData=null},e.prototype._isChild=function(e,t){for(;t&&t.parentElement;){if(t.parentElement===e)return!0;t=t.parentElement}return!1},e.prototype._isDraggable=function(e){var t=e.options;return!(!t.canDrag||!t.canDrag(t.context.data))},e.prototype._isDroppable=function(e){var t=e.options,n=this._dragData&&this._dragData.dragTarget?this._dragData.dragTarget.options.context:void 0;return!(!t.canDrop||!t.canDrop(t.context,n))},e}(),K=(0,s.y)(),Y=function(e){return function(t){return t?t.column.isIconOnly?a.createElement("span",{className:e.accessibleLabel},t.column.name):a.createElement(a.Fragment,null,t.column.name):null}},X=function(e){function t(t){var n=e.call(this,t)||this;return n._root=a.createRef(),n._tooltipRef=a.createRef(),n._onRenderFilterIcon=function(e){return function(e){var t=e.columnProps,n=(0,i._T)(e,["columnProps"]),o=(null===t||void 0===t?void 0:t.useFastIcons)?k.xu:w.J;return a.createElement(o,(0,i.pi)({},n))}},n._onRenderColumnHeaderTooltip=function(e){return a.createElement("span",{className:e.hostClassName},e.children)},n._onColumnClick=function(e){var t=n.props,o=t.onColumnClick,r=t.column;r.columnActionsMode!==y._1.disabled&&(r.onColumnClick&&r.onColumnClick(e,r),o&&o(e,r))},n._onColumnBlur=function(){n._tooltipRef.current&&n._tooltipRef.current.dismiss()},n._onColumnFocus=function(){n._tooltipRef.current&&n._tooltipRef.current.show()},n._onDragStart=function(e,t,o,r){var i=n._classNames;t&&(n._updateHeaderDragInfo(t),n._root.current.classList.add(i.borderWhileDragging),n._async.setTimeout((function(){n._root.current&&n._root.current.classList.add(i.noBorderWhileDragging)}),20))},n._onDragEnd=function(e,t){var o=n._classNames;t&&n._updateHeaderDragInfo(-1,t),n._root.current.classList.remove(o.borderWhileDragging),n._root.current.classList.remove(o.noBorderWhileDragging)},n._updateHeaderDragInfo=function(e,t){n.props.setDraggedItemIndex&&n.props.setDraggedItemIndex(e),n.props.updateDragInfo&&n.props.updateDragInfo({itemIndex:e},t)},n._onColumnContextMenu=function(e){var t=n.props,o=t.onColumnContextMenu,r=t.column;r.onColumnContextMenu&&(r.onColumnContextMenu(r,e),e.preventDefault()),o&&(o(r,e),e.preventDefault())},n._onRootMouseDown=function(e){n.props.isDraggable&&0===e.button&&e.stopPropagation()},(0,f.l)(n),n._async=new h.e(n),n._events=new v.r(n),n}return(0,i.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.column,n=e.parentId,o=e.isDraggable,r=e.styles,s=e.theme,l=e.cellStyleProps,c=void 0===l?O.Wt:l,d=e.useFastIcons,u=void 0===d||d,p=this.props.onRenderColumnHeaderTooltip,f=void 0===p?this._onRenderColumnHeaderTooltip:p;this._classNames=K(r,{theme:s,headerClassName:t.headerClassName,iconClassName:t.iconClassName,isActionable:t.columnActionsMode!==y._1.disabled,isEmpty:!t.name,isIconVisible:t.isSorted||t.isGrouped||t.isFiltered,isPadded:t.isPadded,isIconOnly:t.isIconOnly,cellStyleProps:c,transitionDurationDrag:200,transitionDurationDrop:1500});var h=this._classNames,g=u?k.xu:w.J,m=t.onRenderFilterIcon?(0,E.k)(t.onRenderFilterIcon,this._onRenderFilterIcon(this._classNames)):this._onRenderFilterIcon(this._classNames),v=t.onRenderHeader?(0,E.k)(t.onRenderHeader,Y(this._classNames)):Y(this._classNames),x=t.columnActionsMode!==y._1.disabled&&(void 0!==t.onColumnClick||void 0!==this.props.onColumnClick),b=this.props.onRenderColumnHeaderTooltip?!t.ariaLabel:this._hasAccessibleDescription(),_={"aria-label":t.ariaLabel?t.ariaLabel:t.isIconOnly?t.name:void 0,"aria-labelledby":t.ariaLabel||t.isIconOnly?void 0:"".concat(n,"-").concat(t.key,"-name"),"aria-describedby":b?"".concat(n,"-").concat(t.key,"-tooltip"):void 0};return a.createElement(a.Fragment,null,a.createElement("div",(0,i.pi)({key:t.key,ref:this._root,role:"columnheader"},!x&&_,{"aria-sort":t.isSorted?t.isSortedDescending?"descending":"ascending":"none","data-is-focusable":x||t.columnActionsMode===y._1.disabled?void 0:"true",className:h.root,"data-is-draggable":o,draggable:o,style:{width:(t.calculatedWidth||0)+c.cellLeftPadding+c.cellRightPadding+(t.isPadded?c.cellExtraRightPadding:0)},"data-automationid":"ColumnsHeaderColumn","data-item-key":t.key,onBlur:this._onColumnBlur,onFocus:this._onColumnFocus}),o&&a.createElement(g,{iconName:"GripperBarVertical",className:h.gripperBarVerticalStyle}),f({hostClassName:h.cellTooltip,id:"".concat(n,"-").concat(t.key,"-tooltip"),setAriaDescribedBy:!1,column:t,componentRef:this._tooltipRef,content:t.columnActionsMode!==y._1.disabled?t.ariaLabel:"",children:a.createElement("span",(0,i.pi)({id:"".concat(n,"-").concat(t.key),className:h.cellTitle,"data-is-focusable":x&&t.columnActionsMode!==y._1.disabled?"true":void 0,role:x?"button":void 0},x&&_,{onContextMenu:this._onColumnContextMenu,onClick:this._onColumnClick,"aria-haspopup":t.columnActionsMode===y._1.hasDropdown?"menu":void 0,"aria-expanded":t.columnActionsMode===y._1.hasDropdown?!!t.isMenuOpen:void 0}),a.createElement("span",{id:"".concat(n,"-").concat(t.key,"-name"),className:h.cellName},(t.iconName||t.iconClassName)&&a.createElement(g,{className:h.iconClassName,iconName:t.iconName}),v(this.props)),t.isFiltered&&a.createElement(g,{className:h.nearIcon,iconName:"Filter"}),(t.isSorted||t.showSortIconWhenUnsorted)&&a.createElement(g,{className:h.sortIcon,iconName:t.isSorted?t.isSortedDescending?"SortDown":"SortUp":"Sort"}),t.isGrouped&&a.createElement(g,{className:h.nearIcon,iconName:"GroupedDescending"}),t.columnActionsMode===y._1.hasDropdown&&!t.isIconOnly&&m({"aria-hidden":!0,columnProps:this.props,className:h.filterChevron,iconName:"ChevronDown"}))},this._onRenderColumnHeaderTooltip)),this.props.onRenderColumnHeaderTooltip?null:this._renderAccessibleDescription())},t.prototype.componentDidMount=function(){var e=this;this.props.dragDropHelper&&this.props.isDraggable&&this._addDragDropHandling();var t=this._classNames;this.props.isDropped&&(this._root.current&&(this._root.current.classList.add(t.borderAfterDropping),this._async.setTimeout((function(){e._root.current&&e._root.current.classList.add(t.noBorderAfterDropping)}),20)),this._async.setTimeout((function(){e._root.current&&(e._root.current.classList.remove(t.borderAfterDropping),e._root.current.classList.remove(t.noBorderAfterDropping))}),1520))},t.prototype.componentWillUnmount=function(){this._dragDropSubscription&&(this._dragDropSubscription.dispose(),delete this._dragDropSubscription),this._async.dispose(),this._events.dispose()},t.prototype.componentDidUpdate=function(){!this._dragDropSubscription&&this.props.dragDropHelper&&this.props.isDraggable&&this._addDragDropHandling(),this._dragDropSubscription&&!this.props.isDraggable&&(this._dragDropSubscription.dispose(),this._events.off(this._root.current,"mousedown"),delete this._dragDropSubscription)},t.prototype._getColumnDragDropOptions=function(){var e=this,t=this.props.columnIndex;return{selectionIndex:t,context:{data:t,index:t},canDrag:function(){return e.props.isDraggable},canDrop:function(){return!1},onDragStart:this._onDragStart,updateDropState:function(){},onDrop:function(){},onDragEnd:this._onDragEnd}},t.prototype._hasAccessibleDescription=function(){var e=this.props.column;return!!(e.filterAriaLabel||e.sortAscendingAriaLabel||e.sortDescendingAriaLabel||e.groupAriaLabel||e.sortableAriaLabel)},t.prototype._renderAccessibleDescription=function(){var e=this.props,t=e.column,n=e.parentId,o=this._classNames;return this._hasAccessibleDescription()&&!this.props.onRenderColumnHeaderTooltip?a.createElement("label",{key:"".concat(t.key,"_label"),id:"".concat(n,"-").concat(t.key,"-tooltip"),className:o.accessibleLabel,hidden:!0},t.isFiltered&&t.filterAriaLabel||null,(t.isSorted||t.showSortIconWhenUnsorted)&&(t.isSorted?t.isSortedDescending?t.sortDescendingAriaLabel:t.sortAscendingAriaLabel:t.sortableAriaLabel)||null,t.isGrouped&&t.groupAriaLabel||null):null},t.prototype._addDragDropHandling=function(){this._dragDropSubscription=this.props.dragDropHelper.subscribe(this._root.current,this._events,this._getColumnDragDropOptions()),this._events.on(this._root.current,"mousedown",this._onRootMouseDown)},t}(a.Component),Z={isActionable:"is-actionable",cellIsCheck:"ms-DetailsHeader-cellIsCheck",collapseButton:"ms-DetailsHeader-collapseButton",isCollapsed:"is-collapsed",isAllSelected:"is-allSelected",isSelectAllHidden:"is-selectAllHidden",isResizingColumn:"is-resizingColumn",isEmpty:"is-empty",isIconVisible:"is-icon-visible",cellSizer:"ms-DetailsHeader-cellSizer",isResizing:"is-resizing",dropHintCircleStyle:"ms-DetailsHeader-dropHintCircleStyle",dropHintLineStyle:"ms-DetailsHeader-dropHintLineStyle",cellTitle:"ms-DetailsHeader-cellTitle",cellName:"ms-DetailsHeader-cellName",filterChevron:"ms-DetailsHeader-filterChevron",gripperBarVerticalStyle:"ms-DetailsColumn-gripperBar",nearIcon:"ms-DetailsColumn-nearIcon"},J=(0,r.z)(X,(function(e){var t,n=e.theme,o=e.headerClassName,r=e.iconClassName,a=e.isActionable,s=e.isEmpty,l=e.isIconVisible,c=e.isPadded,d=e.isIconOnly,u=e.cellStyleProps,p=void 0===u?O.Wt:u,f=e.transitionDurationDrag,h=e.transitionDurationDrop,g=n.semanticColors,m=n.palette,y=n.fonts,v=(0,P.Cn)(Z,n),x={iconForegroundColor:g.bodySubtext,headerForegroundColor:g.bodyText,headerBackgroundColor:g.bodyBackground,dropdownChevronForegroundColor:m.neutralSecondary,resizerColor:m.neutralTertiaryAlt},b={color:x.iconForegroundColor,opacity:1,paddingLeft:8},_={outline:"1px solid ".concat(m.themePrimary)},k={outlineColor:"transparent"};return{root:[z(e),y.small,a&&[v.isActionable,{selectors:{":hover":{color:g.bodyText,background:g.listHeaderBackgroundHovered},":active":{background:g.listHeaderBackgroundPressed}}}],s&&[v.isEmpty,{textOverflow:"clip"}],l&&v.isIconVisible,c&&{paddingRight:p.cellExtraRightPadding+p.cellRightPadding},{selectors:{':hover i[data-icon-name="GripperBarVertical"]':{display:"block"}}},o],gripperBarVerticalStyle:{display:"none",position:"absolute",textAlign:"left",color:m.neutralTertiary,left:1},nearIcon:[v.nearIcon,b],sortIcon:[b,{paddingLeft:4,position:"relative",top:1}],iconClassName:[{color:x.iconForegroundColor,opacity:1},r],filterChevron:[v.filterChevron,{color:x.dropdownChevronForegroundColor,paddingLeft:6,verticalAlign:"middle",fontSize:y.small.fontSize}],cellTitle:[v.cellTitle,(0,P.GL)(n),(0,i.pi)({display:"flex",flexDirection:"row",justifyContent:"flex-start",alignItems:"stretch",boxSizing:"border-box",overflow:"hidden",padding:"0 ".concat(p.cellRightPadding,"px 0 ").concat(p.cellLeftPadding,"px")},d?{alignContent:"flex-end",maxHeight:"100%",flexWrap:"wrap-reverse"}:{})],cellName:[v.cellName,{flex:"0 1 auto",overflow:"hidden",textOverflow:"ellipsis",fontWeight:P.lq.semibold,fontSize:y.medium.fontSize},d&&{selectors:(t={},t[".".concat(v.nearIcon)]={paddingLeft:0},t)}],cellTooltip:{display:"block",position:"absolute",top:0,left:0,bottom:0,right:0},accessibleLabel:P.ul,borderWhileDragging:_,noBorderWhileDragging:[k,{transition:"outline ".concat(f,"ms ease")}],borderAfterDropping:_,noBorderAfterDropping:[k,{transition:"outline  ".concat(h,"ms ease")}]}}),void 0,{scope:"DetailsColumn"});!function(e){e[e.none=0]="none",e[e.hidden=1]="hidden",e[e.visible=2]="visible"}(U||(U={}));var $=(0,s.y)(),Q=[],ee=function(e){function t(t){var n=e.call(this,t)||this;return n._rootElement=a.createRef(),n._rootComponent=a.createRef(),n._draggedColumnIndex=-1,n._dropHintDetails={},n._updateDroppingState=function(e,t){n._draggedColumnIndex>=0&&"drop"!==t.type&&!e&&n._resetDropHints()},n._onDragOver=function(e,t){n._draggedColumnIndex>=0&&(t.stopPropagation(),n._computeDropHintToBeShown(t.clientX))},n._onDrop=function(e,t){var o=n._getColumnReorderProps();if(n._draggedColumnIndex>=0&&t){var r=n._draggedColumnIndex>n._currentDropHintIndex?n._currentDropHintIndex:n._currentDropHintIndex-1,i=n._isValidCurrentDropHintIndex();if(t.stopPropagation(),i)if(n._onDropIndexInfo.sourceIndex=n._draggedColumnIndex,n._onDropIndexInfo.targetIndex=r,o.onColumnDrop){var a={draggedIndex:n._draggedColumnIndex,targetIndex:r};o.onColumnDrop(a)}else o.handleColumnReorder&&o.handleColumnReorder(n._draggedColumnIndex,r)}n._resetDropHints(),n._dropHintDetails={},n._draggedColumnIndex=-1},n._computeColumnIndexOffset=function(e){var t=1;return e&&(t+=1),n.props.groupNestingDepth&&n.props.groupNestingDepth>0&&(t+=1),t},n._updateDragInfo=function(e,t){var o=n._getColumnReorderProps(),r=e.itemIndex;if(r>=0)n._draggedColumnIndex=r-n._computeColumnIndexOffset(!n._isCheckboxColumnHidden()),n._getDropHintPositions(),o.onColumnDragStart&&o.onColumnDragStart(!0);else if(t&&n._draggedColumnIndex>=0&&(n._resetDropHints(),n._draggedColumnIndex=-1,n._dropHintDetails={},o.onColumnDragEnd)){var i=n._isEventOnHeader(t);o.onColumnDragEnd({dropLocation:i},t)}},n._getDropHintPositions=function(){for(var e,t=n.props.columns,o=void 0===t?Q:t,r=n._getColumnReorderProps(),i=0,a=0,s=r.frozenColumnCountFromStart||0,l=r.frozenColumnCountFromEnd||0,c=s;c<o.length-l+1;c++)if(n._rootElement.current){var d=n._rootElement.current.querySelectorAll("#columnDropHint_"+c)[0];if(d)if(c===s)i=d.offsetLeft,a=d.offsetLeft,e=d;else{var u=(d.offsetLeft+i)/2;n._dropHintDetails[c-1]={originX:i,startX:a,endX:u,dropHintElementRef:e},a=u,e=d,i=d.offsetLeft,c===o.length-l&&(n._dropHintDetails[c]={originX:i,startX:a,endX:d.offsetLeft,dropHintElementRef:e})}}},n._computeDropHintToBeShown=function(e){var t=(0,c.zg)(n.props.theme);if(n._rootElement.current){var o=e-n._rootElement.current.getBoundingClientRect().left,r=n._currentDropHintIndex;if(n._isValidCurrentDropHintIndex()&&te(t,o,n._dropHintDetails[r].startX,n._dropHintDetails[r].endX))return;var i=n.props.columns,a=void 0===i?Q:i,s=n._getColumnReorderProps(),l=s.frozenColumnCountFromStart||0,d=s.frozenColumnCountFromEnd||0,u=l,p=a.length-d,f=-1;if(ne(t,o,n._dropHintDetails[u].endX)?f=u:oe(t,o,n._dropHintDetails[p].startX)?f=p:n._isValidCurrentDropHintIndex()&&(n._dropHintDetails[r+1]&&te(t,o,n._dropHintDetails[r+1].startX,n._dropHintDetails[r+1].endX)?f=r+1:n._dropHintDetails[r-1]&&te(t,o,n._dropHintDetails[r-1].startX,n._dropHintDetails[r-1].endX)&&(f=r-1)),-1===f)for(var h=l,g=p;h<g;){var m=Math.ceil((g+h)/2);if(te(t,o,n._dropHintDetails[m].startX,n._dropHintDetails[m].endX)){f=m;break}ne(t,o,n._dropHintDetails[m].originX)?g=m:oe(t,o,n._dropHintDetails[m].originX)&&(h=m)}f===n._draggedColumnIndex||f===n._draggedColumnIndex+1?n._isValidCurrentDropHintIndex()&&n._resetDropHints():r!==f&&f>=0&&(n._resetDropHints(),n._updateDropHintElement(n._dropHintDetails[f].dropHintElementRef,"inline-block"),n._currentDropHintIndex=f)}},n._renderColumnSizer=function(e){var t,o=e.columnIndex,r=n.props.columns,i=void 0===r?Q:r,s=i[o],l=n.state.columnResizeDetails,c=n._classNames;return s.isResizable?a.createElement("div",{key:"".concat(s.key,"_sizer"),"aria-hidden":!0,role:"button","data-is-focusable":!1,onClick:re,"data-sizer-index":o,onBlur:n._onSizerBlur,className:(0,u.i)(c.cellSizer,o<i.length-1?c.cellSizerStart:c.cellSizerEnd,(t={},t[c.cellIsResizing]=l&&l.columnIndex===o,t)),onDoubleClick:n._onSizerDoubleClick.bind(n,o)}):null},n._onRenderColumnHeaderTooltip=function(e){return a.createElement("span",{className:e.hostClassName},e.children)},n._onSelectAllClicked=function(){var e=n.props.selection;e&&e.toggleAllSelected()},n._onRootMouseDown=function(e){var t=e.target.getAttribute("data-sizer-index"),o=Number(t),r=n.props.columns,i=void 0===r?Q:r;null!==t&&0===e.button&&(n.setState({columnResizeDetails:{columnIndex:o,columnMinWidth:i[o].calculatedWidth,originX:e.clientX}}),e.preventDefault(),e.stopPropagation())},n._onRootMouseMove=function(e){var t=n.state,o=t.columnResizeDetails,r=t.isSizing;o&&!r&&e.clientX!==o.originX&&n.setState({isSizing:!0})},n._onRootKeyDown=function(e){var t=n.state,o=t.columnResizeDetails,r=t.isSizing,a=n.props,s=a.columns,l=void 0===s?Q:s,u=a.onColumnResized,p=e.target.getAttribute("data-sizer-index");if(p&&!r){var f=Number(p);if(o){var h=void 0;e.which===d.m.enter?(n.setState({columnResizeDetails:void 0}),e.preventDefault(),e.stopPropagation()):e.which===d.m.left?h=(0,c.zg)(n.props.theme)?1:-1:e.which===d.m.right&&(h=(0,c.zg)(n.props.theme)?-1:1),h&&(e.shiftKey||(h*=10),n.setState({columnResizeDetails:(0,i.pi)((0,i.pi)({},o),{columnMinWidth:o.columnMinWidth+h})}),u&&u(l[f],o.columnMinWidth+h,f),e.preventDefault(),e.stopPropagation())}else e.which===d.m.enter&&(n.setState({columnResizeDetails:{columnIndex:f,columnMinWidth:l[f].calculatedWidth}}),e.preventDefault(),e.stopPropagation())}},n._onSizerMouseMove=function(e){var t=e.buttons,o=n.props,r=o.onColumnIsSizingChanged,i=o.onColumnResized,a=o.columns,s=void 0===a?Q:a,l=n.state.columnResizeDetails;if(void 0===t||1===t){if(e.clientX!==l.originX&&r&&r(s[l.columnIndex],!0),i){var d=e.clientX-l.originX;(0,c.zg)(n.props.theme)&&(d=-d),i(s[l.columnIndex],l.columnMinWidth+d,l.columnIndex)}}else n._onSizerMouseUp(e)},n._onSizerBlur=function(e){n.state.columnResizeDetails&&n.setState({columnResizeDetails:void 0,isSizing:!1})},n._onSizerMouseUp=function(e){var t=n.props,o=t.columns,r=void 0===o?Q:o,i=t.onColumnIsSizingChanged,a=n.state.columnResizeDetails;n.setState({columnResizeDetails:void 0,isSizing:!1}),i&&i(r[a.columnIndex],!1)},n._onToggleCollapseAll=function(){var e=n.props.onToggleCollapseAll,t=!n.state.isAllCollapsed;n.setState({isAllCollapsed:t}),e&&e(t)},(0,f.l)(n),n._events=new v.r(n),n.state={columnResizeDetails:void 0,isAllCollapsed:n.props.isAllCollapsed,isAllSelected:!!n.props.selection&&n.props.selection.isAllSelected()},n._onDropIndexInfo={sourceIndex:-1,targetIndex:-1},n._id=(0,x.z)("header"),n._currentDropHintIndex=-1,n._dragDropHelper=new q({selection:{getSelection:function(){}},minimumPixelsForDrag:n.props.minimumPixelsForDrag}),n}return(0,i.ZT)(t,e),t.prototype.componentDidMount=function(){var e=this.props.selection;this._events.on(e,H.F5,this._onSelectionChanged),this._rootElement.current&&(this._events.on(this._rootElement.current,"mousedown",this._onRootMouseDown),this._events.on(this._rootElement.current,"keydown",this._onRootKeyDown),this._getColumnReorderProps()&&(this._subscriptionObject=this._dragDropHelper.subscribe(this._rootElement.current,this._events,this._getHeaderDragDropOptions())))},t.prototype.componentDidUpdate=function(e){if(this._getColumnReorderProps()?!this._subscriptionObject&&this._rootElement.current&&(this._subscriptionObject=this._dragDropHelper.subscribe(this._rootElement.current,this._events,this._getHeaderDragDropOptions())):this._subscriptionObject&&(this._subscriptionObject.dispose(),delete this._subscriptionObject),this.props!==e&&this._onDropIndexInfo.sourceIndex>=0&&this._onDropIndexInfo.targetIndex>=0){var t=e.columns,n=void 0===t?Q:t,o=this.props.columns,r=void 0===o?Q:o;n[this._onDropIndexInfo.sourceIndex].key===r[this._onDropIndexInfo.targetIndex].key&&(this._onDropIndexInfo={sourceIndex:-1,targetIndex:-1})}this.props.isAllCollapsed!==e.isAllCollapsed&&this.setState({isAllCollapsed:this.props.isAllCollapsed})},t.prototype.componentWillUnmount=function(){this._subscriptionObject&&(this._subscriptionObject.dispose(),delete this._subscriptionObject),this._dragDropHelper.dispose(),this._events.dispose()},t.prototype.render=function(){var e=this,t=this.props,n=t.columns,r=void 0===n?Q:n,i=t.ariaLabel,s=t.ariaLabelForToggleAllGroupsButton,l=t.ariaLabelForSelectAllCheckbox,d=t.selectAllVisibility,u=t.ariaLabelForSelectionColumn,p=t.indentWidth,f=t.onColumnClick,h=t.onColumnContextMenu,g=t.onRenderColumnHeaderTooltip,m=void 0===g?this._onRenderColumnHeaderTooltip:g,v=t.styles,x=t.selectionMode,E=t.theme,T=t.onRenderDetailsCheckbox,D=t.groupNestingDepth,R=t.useFastIcons,P=t.checkboxVisibility,I=t.className,M=this.state,O=M.isAllSelected,L=M.columnResizeDetails,A=M.isSizing,z=M.isAllCollapsed,N=d!==U.none,F=d===U.hidden,B=P===y.tY.always,j=this._getColumnReorderProps(),W=j&&j.frozenColumnCountFromStart?j.frozenColumnCountFromStart:0,G=j&&j.frozenColumnCountFromEnd?j.frozenColumnCountFromEnd:0;this._classNames=$(v,{theme:E,isAllSelected:O,isSelectAllHidden:d===U.hidden,isResizingColumn:!!L&&A,isSizing:A,isAllCollapsed:z,isCheckboxHidden:F,className:I});var q=this._classNames,K=R?k.xu:w.J,Y=D>0,X=Y&&this.props.collapseAllVisibility===o.visible,Z=this._computeColumnIndexOffset(N),ee=(0,c.zg)(E);return a.createElement(b.k,{role:"row","aria-label":i,className:q.root,componentRef:this._rootComponent,elementRef:this._rootElement,onMouseMove:this._onRootMouseMove,"data-automationid":"DetailsHeader",direction:_.U.horizontal},N?[a.createElement("div",{key:"__checkbox",className:q.cellIsCheck,"aria-labelledby":"".concat(this._id,"-checkTooltip"),onClick:F?void 0:this._onSelectAllClicked,role:"columnheader"},m({hostClassName:q.checkTooltip,id:"".concat(this._id,"-checkTooltip"),setAriaDescribedBy:!1,content:l,children:a.createElement(V,{id:"".concat(this._id,"-check"),"aria-label":x===H.oW.multiple?l:u,"data-is-focusable":!F||void 0,isHeader:!0,selected:O,anySelected:!1,canSelect:!F,className:q.check,onRenderDetailsCheckbox:T,useFastIcons:R,isVisible:B})},this._onRenderColumnHeaderTooltip)),this.props.onRenderColumnHeaderTooltip?null:l&&!F?a.createElement("label",{key:"__checkboxLabel",id:"".concat(this._id,"-checkTooltip"),className:q.accessibleLabel,"aria-hidden":!0},l):u&&F?a.createElement("label",{key:"__checkboxLabel",id:"".concat(this._id,"-checkTooltip"),className:q.accessibleLabel,"aria-hidden":!0},u):null]:null,X?a.createElement("div",{className:q.cellIsGroupExpander,onClick:this._onToggleCollapseAll,"data-is-focusable":!0,"aria-label":s,"aria-expanded":!z,role:"columnheader"},a.createElement(K,{className:q.collapseButton,iconName:ee?"ChevronLeftMed":"ChevronRightMed"}),a.createElement("span",{className:q.accessibleLabel},s)):Y?a.createElement("div",{className:q.cellIsGroupExpander,"data-is-focusable":!1,role:"columnheader"}):null,a.createElement(S,{indentWidth:p,role:"gridcell",count:D-1}),r.map((function(t,n){var o=!!j&&(n>=W&&n<r.length-G);return[j&&(o||n===r.length-G)&&e._renderDropHint(n),a.createElement(J,{column:t,styles:t.styles,key:t.key,columnIndex:Z+n,parentId:e._id,isDraggable:o,updateDragInfo:e._updateDragInfo,dragDropHelper:e._dragDropHelper,onColumnClick:f,onColumnContextMenu:h,onRenderColumnHeaderTooltip:e.props.onRenderColumnHeaderTooltip,isDropped:e._onDropIndexInfo.targetIndex===n,cellStyleProps:e.props.cellStyleProps,useFastIcons:R}),e._renderColumnDivider(n)]})),j&&0===G&&this._renderDropHint(r.length),A&&a.createElement(C.m,null,a.createElement("div",{className:q.sizingOverlay,onMouseMove:this._onSizerMouseMove,onMouseUp:this._onSizerMouseUp})))},t.prototype.focus=function(){var e;return!!(null===(e=this._rootComponent.current)||void 0===e?void 0:e.focus())},t.prototype._getColumnReorderProps=function(){var e=this.props,t=e.columnReorderOptions;return e.columnReorderProps||t&&(0,i.pi)((0,i.pi)({},t),{onColumnDragEnd:void 0})},t.prototype._getHeaderDragDropOptions=function(){return{selectionIndex:1,context:{data:this,index:0},canDrag:function(){return!1},canDrop:function(){return!0},onDragStart:function(){},updateDropState:this._updateDroppingState,onDrop:this._onDrop,onDragEnd:function(){},onDragOver:this._onDragOver}},t.prototype._isValidCurrentDropHintIndex=function(){return this._currentDropHintIndex>=0},t.prototype._isCheckboxColumnHidden=function(){var e=this.props,t=e.selectionMode,n=e.checkboxVisibility;return t===H.oW.none||n===y.tY.hidden},t.prototype._resetDropHints=function(){this._currentDropHintIndex>=0&&(this._updateDropHintElement(this._dropHintDetails[this._currentDropHintIndex].dropHintElementRef,"none"),this._currentDropHintIndex=-1)},t.prototype._updateDropHintElement=function(e,t){e.childNodes[1].style.display=t,e.childNodes[0].style.display=t},t.prototype._isEventOnHeader=function(e){if(this._rootElement.current){var t=this._rootElement.current.getBoundingClientRect();if(e.clientX>t.left&&e.clientX<t.right&&e.clientY>t.top&&e.clientY<t.bottom)return y.fQ.header}},t.prototype._renderColumnDivider=function(e){var t=this.props.columns,n=(void 0===t?Q:t)[e],o=n.onRenderDivider;return o?o({column:n,columnIndex:e},this._renderColumnSizer):this._renderColumnSizer({column:n,columnIndex:e})},t.prototype._renderDropHint=function(e){var t=this._classNames,n=this.props.useFastIcons?k.xu:w.J;return a.createElement("div",{key:"dropHintKey",className:t.dropHintStyle,id:"columnDropHint_".concat(e)},a.createElement("div",{role:"presentation",key:"dropHintCircleKey",className:t.dropHintCaretStyle,"data-is-focusable":!1,"data-sizer-index":e,"aria-hidden":!0},a.createElement(n,{iconName:"CircleShapeSolid"})),a.createElement("div",{key:"dropHintLineKey","aria-hidden":!0,"data-is-focusable":!1,"data-sizer-index":e,className:t.dropHintLineStyle}))},t.prototype._onSizerDoubleClick=function(e,t){var n=this.props,o=n.onColumnAutoResized,r=n.columns;o&&o((void 0===r?Q:r)[e],e)},t.prototype._onSelectionChanged=function(){var e=!!this.props.selection&&this.props.selection.isAllSelected();this.state.isAllSelected!==e&&this.setState({isAllSelected:e})},t.defaultProps={selectAllVisibility:U.visible,collapseAllVisibility:o.visible,useFastIcons:!0},t}(a.Component);function te(e,t,n,o){return e?t<=n&&t>=o:t>=n&&t<=o}function ne(e,t,n){return e?t>=n:t<=n}function oe(e,t,n){return e?t<=n:t>=n}function re(e){e.stopPropagation()}var ie=(0,r.z)(ee,(function(e){var t,n,o,r,a=e.theme,s=e.className,l=e.isAllSelected,d=e.isResizingColumn,u=e.isSizing,p=e.isAllCollapsed,f=e.cellStyleProps,h=void 0===f?O.Wt:f,g=a.semanticColors,m=a.palette,y=a.fonts,v=(0,P.Cn)(A,a),x={iconForegroundColor:g.bodySubtext,headerForegroundColor:g.bodyText,headerBackgroundColor:g.bodyBackground,resizerColor:m.neutralTertiaryAlt},b={opacity:1,transition:"opacity 0.3s linear"},_=z(e);return{root:[v.root,y.small,{display:"inline-block",background:x.headerBackgroundColor,position:"relative",minWidth:"100%",verticalAlign:"top",height:42,lineHeight:42,whiteSpace:"nowrap",boxSizing:"content-box",paddingBottom:"1px",paddingTop:"16px",borderBottom:"1px solid ".concat(g.bodyDivider),cursor:"default",userSelect:"none",selectors:(t={},t["&:hover .".concat(v.check)]={opacity:1},t["& .".concat(v.tooltipHost," .").concat(v.checkTooltip)]={display:"block"},t)},l&&v.isAllSelected,d&&v.isResizingColumn,s],check:[v.check,{height:42},{selectors:(n={},n[".".concat(L.G$," &:focus")]={opacity:1},n)}],cellWrapperPadded:{paddingRight:h.cellExtraRightPadding+h.cellRightPadding},cellIsCheck:[_,v.cellIsCheck,{position:"relative",padding:0,margin:0,display:"inline-flex",alignItems:"center",border:"none"},l&&{opacity:1}],cellIsGroupExpander:[_,{display:"inline-flex",alignItems:"center",justifyContent:"center",fontSize:y.small.fontSize,padding:0,border:"none",width:36,color:m.neutralSecondary,selectors:{":hover":{backgroundColor:m.neutralLighter},":active":{backgroundColor:m.neutralLight}}}],cellIsActionable:{selectors:{":hover":{color:g.bodyText,background:g.listHeaderBackgroundHovered},":active":{background:g.listHeaderBackgroundPressed}}},cellIsEmpty:{textOverflow:"clip"},cellSizer:[v.cellSizer,(0,P.e2)(),{display:"inline-block",position:"relative",cursor:"ew-resize",bottom:0,top:0,overflow:"hidden",height:"inherit",background:"transparent",zIndex:1,width:16,selectors:(o={":after":{content:'""',position:"absolute",top:0,bottom:0,width:1,background:x.resizerColor,opacity:0,left:"50%"},":focus:after":b,":hover:after":b},o["&.".concat(v.isResizing,":after")]=[b,{boxShadow:"0 0 5px 0 rgba(0, 0, 0, 0.4)"}],o)}],cellIsResizing:v.isResizing,cellSizerStart:{margin:"0 -8px"},cellSizerEnd:{margin:0,marginLeft:-16},collapseButton:[v.collapseButton,{transformOrigin:"50% 50%",transition:"transform .1s linear"},p?[v.isCollapsed,{transform:"rotate(0deg)"}]:{transform:(0,c.zg)(a)?"rotate(-90deg)":"rotate(90deg)"}],checkTooltip:v.checkTooltip,sizingOverlay:u&&{position:"absolute",left:0,top:0,right:0,bottom:0,cursor:"ew-resize",background:"rgba(255, 255, 255, 0)",selectors:(r={},r[P.qJ]=(0,i.pi)({background:"transparent"},(0,P.xM)()),r)},accessibleLabel:P.ul,dropHintCircleStyle:[v.dropHintCircleStyle,{display:"inline-block",visibility:"hidden",position:"absolute",bottom:0,height:9,width:9,borderRadius:"50%",marginLeft:-5,top:34,overflow:"visible",zIndex:10,border:"1px solid ".concat(m.themePrimary),background:m.white}],dropHintCaretStyle:[v.dropHintCaretStyle,{display:"none",position:"absolute",top:-28,left:-6.5,fontSize:y.medium.fontSize,color:m.themePrimary,overflow:"visible",zIndex:10}],dropHintLineStyle:[v.dropHintLineStyle,{display:"none",position:"absolute",bottom:0,top:0,overflow:"hidden",height:42,width:1,background:m.themePrimary,zIndex:10}],dropHintStyle:{display:"inline-block",position:"absolute"}}}),void 0,{scope:"DetailsHeader"}),ae=n(35726),se=n(20777),le=n(15863),ce=function(e){var t=e.columns,n=e.rowClassNames,o=e.cellStyleProps,r=void 0===o?O.Wt:o,i=e.item,s=e.itemIndex,l=e.isSelected,c=e.onRenderItemColumn,d=e.getCellValueKey,p=e.onRenderField,f=e.cellsByColumn,h=e.enableUpdateAnimations,g=e.rowHeaderId,m=a.useRef(),y=m.current||(m.current={}),v=a.useCallback((function(e){var t=e.column,o=e.cellValueKey,i=e.className,s=e.onRender,l=e.item,c=e.itemIndex,d="undefined"===typeof t.calculatedWidth?"auto":t.calculatedWidth+r.cellLeftPadding+r.cellRightPadding+(t.isPadded?r.cellExtraRightPadding:0),p="".concat(t.key).concat(void 0!==o?"-".concat(o):"");return a.createElement("div",{key:p,id:t.isRowHeader?g:void 0,role:t.isRowHeader?"rowheader":"gridcell",className:(0,u.i)(t.className,t.isMultiline&&n.isMultiline,t.isRowHeader&&n.isRowHeader,n.cell,t.isPadded?n.cellPadded:n.cellUnpadded,i),style:{width:d},"data-automationid":"DetailsRowCell","data-automation-key":t.key},s(l,c,t))}),[n,r,g]);return a.createElement("div",{className:n.fields,"data-automationid":"DetailsRowFields",role:"presentation"},t.map((function(e){var t=e.getValueKey,o=void 0===t?d:t,r=f&&e.key in f&&function(){return f[e.key]}||e.onRender||c||de,a=v;e.onRenderField&&(a=(0,E.k)(e.onRenderField,a)),p&&(a=(0,E.k)(p,a));var u=y[e.key],g=h&&o?o(i,s,e):void 0,m=!1;return void 0!==g&&void 0!==u&&g!==u&&(m=!0),y[e.key]=g,a({item:i,itemIndex:s,isSelected:l,column:e,cellValueKey:g,className:m?n.cellAnimation:void 0,onRender:r})})))};function de(e,t,n){return e&&n?function(e,t){var n=e&&t&&t.fieldName?e[t.fieldName]:"";return null!==n&&void 0!==n||(n=""),"boolean"===typeof n?n.toString():n}(e,n):null}var ue=(0,s.y)(),pe=[],fe=function(e){function t(t){var n=e.call(this,t)||this;return n._root=a.createRef(),n._cellMeasurer=a.createRef(),n._focusZone=a.createRef(),n._onSelectionChanged=function(){var e=he(n.props);(0,ae.Vv)(e,n.state.selectionState)||n.setState({selectionState:e})},n._updateDroppingState=function(e,t){var o=n.state.isDropping,r=n.props,i=r.dragDropEvents,a=r.item;e?i.onDragEnter&&(n._droppingClassNames=i.onDragEnter(a,t)):i.onDragLeave&&i.onDragLeave(a,t),o!==e&&n.setState({isDropping:e})},(0,f.l)(n),n._events=new v.r(n),n.state={selectionState:he(t),columnMeasureInfo:void 0,isDropping:!1},n._droppingClassNames="",n}return(0,i.ZT)(t,e),t.getDerivedStateFromProps=function(e,t){return(0,i.pi)((0,i.pi)({},t),{selectionState:he(e)})},t.prototype.componentDidMount=function(){var e=this.props,t=e.dragDropHelper,n=e.selection,o=e.item,r=e.onDidMount;t&&this._root.current&&(this._dragDropSubscription=t.subscribe(this._root.current,this._events,this._getRowDragDropOptions())),n&&this._events.on(n,H.F5,this._onSelectionChanged),r&&o&&(this._onDidMountCalled=!0,r(this))},t.prototype.componentDidUpdate=function(e){var t=this.state,n=this.props,o=n.item,r=n.onDidMount,i=t.columnMeasureInfo;if(this.props.itemIndex===e.itemIndex&&this.props.item===e.item&&this.props.dragDropHelper===e.dragDropHelper||(this._dragDropSubscription&&(this._dragDropSubscription.dispose(),delete this._dragDropSubscription),this.props.dragDropHelper&&this._root.current&&(this._dragDropSubscription=this.props.dragDropHelper.subscribe(this._root.current,this._events,this._getRowDragDropOptions()))),i&&i.index>=0&&this._cellMeasurer.current){var a=this._cellMeasurer.current.getBoundingClientRect().width;i.onMeasureDone(a),this.setState({columnMeasureInfo:void 0})}o&&r&&!this._onDidMountCalled&&(this._onDidMountCalled=!0,r(this))},t.prototype.componentWillUnmount=function(){var e=this.props,t=e.item,n=e.onWillUnmount;n&&t&&n(this),this._dragDropSubscription&&(this._dragDropSubscription.dispose(),delete this._dragDropSubscription),this._events.dispose()},t.prototype.shouldComponentUpdate=function(e,t){if(this.props.useReducedRowRenderer){var n=he(e);return this.state.selectionState.isSelected!==n.isSelected||!(0,ae.Vv)(this.props,e)}return!0},t.prototype.render=function(){var e,t=this.props,n=t.className,o=t.columns,r=void 0===o?pe:o,s=t.dragDropEvents,l=t.item,c=t.itemIndex,d=t.id,p=t.flatIndexOffset,f=void 0===p?2:p,h=t.onRenderCheck,g=void 0===h?this._onRenderCheck:h,m=t.onRenderDetailsCheckbox,v=t.onRenderItemColumn,k=t.onRenderField,w=t.getCellValueKey,C=t.selectionMode,E=t.checkboxVisibility,T=t.getRowAriaLabel,D=t.getRowAriaDescription,R=t.getRowAriaDescribedBy,P=t.isGridRow,I=t.checkButtonAriaLabel,M=t.checkboxCellClassName,O=t.rowFieldsAs,L=t.selection,A=t.indentWidth,z=t.enableUpdateAnimations,N=t.compact,F=t.theme,B=t.styles,j=t.cellsByColumn,W=t.groupNestingDepth,U=t.useFastIcons,V=void 0===U||U,G=t.cellStyleProps,q=t.group,K=t.focusZoneProps,Y=t.disabled,X=void 0!==Y&&Y,Z=this.state,J=Z.columnMeasureInfo,$=Z.isDropping,Q=this.state.selectionState,ee=Q.isSelected,te=void 0!==ee&&ee,ne=Q.isSelectionModal,oe=void 0!==ne&&ne,re=s?!(!s.canDrag||!s.canDrag(l)):void 0,ie=$?this._droppingClassNames||"is-dropping":"",de=T?T(l):void 0,fe=D?D(l):void 0,he=R?R(l):void 0,ge=!!L&&L.canSelectItem(l,c)&&!X,me=C===H.oW.multiple,ye=C!==H.oW.none&&E!==y.tY.hidden,ve=C===H.oW.none?void 0:te,xe=q?c-q.startIndex+1:void 0,be=q?q.count:void 0,_e=K?K.direction:_.U.horizontal;this._classNames=(0,i.pi)((0,i.pi)({},this._classNames),ue(B,{theme:F,isSelected:te,canSelect:!me,anySelected:oe,checkboxCellClassName:M,droppingClassName:ie,className:n,compact:N,enableUpdateAnimations:z,cellStyleProps:G,disabled:X}));var ke={isMultiline:this._classNames.isMultiline,isRowHeader:this._classNames.isRowHeader,cell:this._classNames.cell,cellAnimation:this._classNames.cellAnimation,cellPadded:this._classNames.cellPadded,cellUnpadded:this._classNames.cellUnpadded,fields:this._classNames.fields};(0,ae.Vv)(this._rowClassNames||{},ke)||(this._rowClassNames=ke);var we=O?(0,se.Z)(O,ce):ce,Ce=a.createElement(we,{rowClassNames:this._rowClassNames,rowHeaderId:"".concat(d,"-header"),cellsByColumn:j,columns:r,item:l,itemIndex:c,isSelected:te,columnStartIndex:(ye?1:0)+(W?1:0),onRenderItemColumn:v,onRenderField:k,getCellValueKey:w,enableUpdateAnimations:z,cellStyleProps:G}),Se=this.props.role?this.props.role:"row";this._ariaRowDescriptionId=(0,x.z)("DetailsRow-description");var Ee=r.some((function(e){return!!e.isRowHeader})),Te="".concat(d,"-checkbox")+(Ee?" ".concat(d,"-header"):""),De=P?{}:{"aria-level":W&&W+1||void 0,"aria-posinset":xe,"aria-setsize":be};return a.createElement(b.k,(0,i.pi)({"data-is-focusable":!0},(0,le.pq)(this.props,le.n7),"boolean"===typeof re?{"data-is-draggable":re,draggable:re}:{},K,De,{direction:_e,elementRef:this._root,componentRef:this._focusZone,role:Se,"aria-label":de,"aria-disabled":X||void 0,"aria-describedby":fe?this._ariaRowDescriptionId:he,className:this._classNames.root,"data-selection-index":c,"data-selection-touch-invoke":!0,"data-selection-disabled":null!==(e=this.props["data-selection-disabled"])&&void 0!==e?e:X||void 0,"data-item-index":c,"aria-rowindex":void 0===xe?c+f:void 0,"data-automationid":"DetailsRow","aria-selected":ve,allowFocusRoot:!0}),fe?a.createElement("span",{key:"description",role:"presentation",hidden:!0,id:this._ariaRowDescriptionId},fe):null,ye&&a.createElement("div",{role:"gridcell","data-selection-toggle":!0,className:this._classNames.checkCell},g({id:d?"".concat(d,"-checkbox"):void 0,selected:te,selectionMode:C,anySelected:oe,"aria-label":I,"aria-labelledby":d?Te:void 0,canSelect:ge,compact:N,className:this._classNames.check,theme:F,isVisible:E===y.tY.always,onRenderDetailsCheckbox:m,useFastIcons:V})),a.createElement(S,{indentWidth:A,role:"gridcell",count:0===W?-1:W}),l&&Ce,J&&a.createElement("span",{role:"presentation",className:(0,u.i)(this._classNames.cellMeasurer,this._classNames.cell),ref:this._cellMeasurer},a.createElement(we,{rowClassNames:this._rowClassNames,rowHeaderId:"".concat(d,"-header"),columns:[J.column],item:l,itemIndex:c,columnStartIndex:(ye?1:0)+(W?1:0)+r.length,onRenderItemColumn:v,getCellValueKey:w})))},t.prototype.measureCell=function(e,t){var n=this.props.columns,o=void 0===n?pe:n,r=(0,i.pi)({},o[e]);r.minWidth=0,r.maxWidth=999999,delete r.calculatedWidth,this.setState({columnMeasureInfo:{index:e,column:r,onMeasureDone:t}})},t.prototype.focus=function(e){var t;return void 0===e&&(e=!1),!!(null===(t=this._focusZone.current)||void 0===t?void 0:t.focus(e))},t.prototype._onRenderCheck=function(e){return a.createElement(V,(0,i.pi)({},e))},t.prototype._getRowDragDropOptions=function(){var e=this.props,t=e.item,n=e.itemIndex,o=e.dragDropEvents;return{eventMap:e.eventsToRegister,selectionIndex:n,context:{data:t,index:n},canDrag:o.canDrag,canDrop:o.canDrop,onDragStart:o.onDragStart,updateDropState:this._updateDroppingState,onDrop:o.onDrop,onDragEnd:o.onDragEnd,onDragOver:o.onDragOver}},t}(a.Component);function he(e){var t,n=e.itemIndex,o=e.selection;return{isSelected:!!(null===o||void 0===o?void 0:o.isIndexSelected(n)),isSelectionModal:!!(null===(t=null===o||void 0===o?void 0:o.isModal)||void 0===t?void 0:t.call(o))}}var ge=(0,r.z)(fe,O.GL,void 0,{scope:"DetailsRow"}),me=n(65141),ye=n(12513),ve=n(71675),xe=n(56500),be="data-selection-index",_e="data-selection-toggle",ke="data-selection-invoke",we="data-selection-all-toggle",Ce=function(e){function t(t){var n=e.call(this,t)||this;n._root=a.createRef(),n.ignoreNextFocus=function(){n._handleNextFocus(!1)},n._onSelectionChange=function(){var e=n.props.selection,t=e.isModal&&e.isModal();n.setState({isModal:t})},n._onMouseDownCapture=function(e){var t=e.target;if(document.activeElement===t||(0,m.t)(document.activeElement,t)){if((0,m.t)(t,n._root.current))for(;t!==n._root.current;){if(n._hasAttribute(t,ke)){n.ignoreNextFocus();break}t=(0,me.G)(t)}}else n.ignoreNextFocus()},n._onFocus=function(e){var t=e.target,o=n.props.selection,r=n._isCtrlPressed||n._isMetaPressed,i=n._getSelectionMode();if(n._shouldHandleFocus&&i!==H.oW.none){var a=n._hasAttribute(t,_e),s=n._findItemRoot(t);if(!a&&s){var l=n._getItemIndex(s);void 0===n._getItemSpan(s)&&(r?(o.setIndexSelected(l,o.isIndexSelected(l),!0),n.props.enterModalOnTouch&&n._isTouch&&o.setModal&&(o.setModal(!0),n._setIsTouch(!1))):n.props.isSelectedOnFocus&&n._onItemSurfaceClick("focus",l))}}n._handleNextFocus(!1)},n._onMouseDown=function(e){n._updateModifiers(e);var t=n.props.toggleWithoutModifierPressed,o=e.target,r=n._findItemRoot(o);if(!n._isSelectionDisabled(o))for(;o!==n._root.current&&!n._hasAttribute(o,we);){if(r){if(n._hasAttribute(o,_e))break;if(n._hasAttribute(o,ke))break;if(!(o!==r&&!n._shouldAutoSelect(o)||n._isShiftPressed||n._isCtrlPressed||n._isMetaPressed||t)){n._onInvokeMouseDown(e,n._getItemIndex(r),n._getItemSpan(r));break}if(n.props.disableAutoSelectOnInputElements&&("A"===o.tagName||"BUTTON"===o.tagName||"INPUT"===o.tagName))return}o=(0,me.G)(o)}},n._onTouchStartCapture=function(e){n._setIsTouch(!0)},n._onClick=function(e){var t=n.props.enableTouchInvocationTarget,o=void 0!==t&&t;n._updateModifiers(e);for(var r=e.target,i=n._findItemRoot(r),a=n._isSelectionDisabled(r);r!==n._root.current;){if(n._hasAttribute(r,we)){a||n._onToggleAllClick(e);break}if(i){var s=n._getItemIndex(i),l=n._getItemSpan(i);if(n._hasAttribute(r,_e)){a||(n._isShiftPressed?n._onItemSurfaceClick("click",s,l):n._onToggleClick(e,s,l));break}if(n._isTouch&&o&&n._hasAttribute(r,"data-selection-touch-invoke")||n._hasAttribute(r,ke)){void 0===l&&n._onInvokeClick(e,s);break}if(r===i){a||n._onItemSurfaceClick("click",s,l);break}if("A"===r.tagName||"BUTTON"===r.tagName||"INPUT"===r.tagName)return}r=(0,me.G)(r)}},n._onContextMenu=function(e){var t=e.target,o=n.props,r=o.onItemContextMenu,i=o.selection;if(r){var a=n._findItemRoot(t);if(a){var s=n._getItemIndex(a);n._onInvokeMouseDown(e,s),r(i.getItems()[s],s,e.nativeEvent)||e.preventDefault()}}},n._onDoubleClick=function(e){var t=e.target,o=n.props.onItemInvoked,r=n._findItemRoot(t);if(r&&o&&!n._isInputElement(t)){for(var i=n._getItemIndex(r);t!==n._root.current&&!n._hasAttribute(t,_e)&&!n._hasAttribute(t,ke);){if(t===r){n._onInvokeClick(e,i);break}t=(0,me.G)(t)}t=(0,me.G)(t)}},n._onKeyDownCapture=function(e){n._updateModifiers(e),n._handleNextFocus(!0)},n._onKeyDown=function(e){n._updateModifiers(e);var t=e.target,o=n._isSelectionDisabled(t),r=n.props,i=r.selection,a=r.selectionClearedOnEscapePress,s=e.which===d.m.a&&(n._isCtrlPressed||n._isMetaPressed),l=e.which===d.m.escape;if(!n._isInputElement(t)){var c=n._getSelectionMode();if(s&&c===H.oW.multiple&&!i.isAllSelected())return o||i.setAllSelected(!0),e.stopPropagation(),void e.preventDefault();if(a&&l&&i.getSelectedCount()>0)return o||i.setAllSelected(!1),e.stopPropagation(),void e.preventDefault();var u=n._findItemRoot(t);if(u)for(var p=n._getItemIndex(u),f=n._getItemSpan(u);t!==n._root.current&&!n._hasAttribute(t,_e);){if(n._shouldAutoSelect(t)){o||void 0!==f||n._onInvokeMouseDown(e,p,f);break}if(!(e.which!==d.m.enter&&e.which!==d.m.space||"BUTTON"!==t.tagName&&"A"!==t.tagName&&"INPUT"!==t.tagName&&"SUMMARY"!==t.tagName))return!1;if(t===u){if(e.which===d.m.enter)return void(void 0===f&&(n._onInvokeClick(e,p),e.preventDefault()));if(e.which===d.m.space)return o||n._onToggleClick(e,p,f),void e.preventDefault();break}t=(0,me.G)(t)}}},n._events=new v.r(n),n._async=new h.e(n),(0,f.l)(n);var o=n.props.selection,r=o.isModal&&o.isModal();return n.state={isModal:r},n}return(0,i.ZT)(t,e),t.getDerivedStateFromProps=function(e,t){var n=e.selection.isModal&&e.selection.isModal();return(0,i.pi)((0,i.pi)({},t),{isModal:n})},t.prototype.componentDidMount=function(){var e=(0,ye.J)(this._root.current);this._events.on(e,"keydown, keyup",this._updateModifiers,!0),this._events.on(document,"click",this._findScrollParentAndTryClearOnEmptyClick),this._events.on(document.body,"touchstart",this._onTouchStartCapture,!0),this._events.on(document.body,"touchend",this._onTouchStartCapture,!0),this._events.on(this.props.selection,"change",this._onSelectionChange)},t.prototype.render=function(){var e=this.state.isModal;return a.createElement("div",{className:(0,u.i)("ms-SelectionZone",this.props.className,{"ms-SelectionZone--modal":!!e}),ref:this._root,onKeyDown:this._onKeyDown,onMouseDown:this._onMouseDown,onKeyDownCapture:this._onKeyDownCapture,onClick:this._onClick,role:"presentation",onDoubleClick:this._onDoubleClick,onContextMenu:this._onContextMenu,onMouseDownCapture:this._onMouseDownCapture,onFocusCapture:this._onFocus,"data-selection-is-modal":!!e||void 0},this.props.children,a.createElement(p.u,null))},t.prototype.componentDidUpdate=function(e){var t=this.props.selection;t!==e.selection&&(this._events.off(e.selection),this._events.on(t,"change",this._onSelectionChange))},t.prototype.componentWillUnmount=function(){this._events.dispose(),this._async.dispose()},t.prototype._isSelectionDisabled=function(e){if(this._getSelectionMode()===H.oW.none)return!0;for(;e!==this._root.current;){if(this._hasAttribute(e,"data-selection-disabled"))return!0;e=(0,me.G)(e)}return!1},t.prototype._onToggleAllClick=function(e){var t=this.props.selection;this._getSelectionMode()===H.oW.multiple&&(t.toggleAllSelected(),e.stopPropagation(),e.preventDefault())},t.prototype._onToggleClick=function(e,t,n){var o=this.props.selection,r=this._getSelectionMode();if(o.setChangeEvents(!1),this.props.enterModalOnTouch&&this._isTouch&&(void 0!==n?!o.isRangeSelected(t,n):!o.isIndexSelected(t))&&o.setModal&&(o.setModal(!0),this._setIsTouch(!1)),r===H.oW.multiple)void 0!==n?o.toggleRangeSelected(t,n):o.toggleIndexSelected(t);else{if(r!==H.oW.single)return void o.setChangeEvents(!0);if(void 0===n||1===n){var i=o.isIndexSelected(t),a=o.isModal&&o.isModal();o.setAllSelected(!1),o.setIndexSelected(t,!i,!0),a&&o.setModal&&o.setModal(!0)}}o.setChangeEvents(!0),e.stopPropagation()},t.prototype._onInvokeClick=function(e,t){var n=this.props,o=n.selection,r=n.onItemInvoked;r&&(r(o.getItems()[t],t,e.nativeEvent),e.preventDefault(),e.stopPropagation())},t.prototype._onItemSurfaceClick=function(e,t,n){var o,r=this.props,i=r.selection,a=r.toggleWithoutModifierPressed,s=this._isCtrlPressed||this._isMetaPressed,l=this._getSelectionMode();l===H.oW.multiple?this._isShiftPressed&&!this._isTabPressed?void 0!==n?null===(o=i.selectToRange)||void 0===o||o.call(i,t,n,!s):i.selectToIndex(t,!s):"click"===e&&(s||a)?void 0!==n?i.toggleRangeSelected(t,n):i.toggleIndexSelected(t):this._clearAndSelectIndex(t,n):l===H.oW.single&&this._clearAndSelectIndex(t,n)},t.prototype._onInvokeMouseDown=function(e,t,n){var o=this.props.selection;if(void 0!==n){if(o.isRangeSelected(t,n))return}else if(o.isIndexSelected(t))return;this._clearAndSelectIndex(t,n)},t.prototype._findScrollParentAndTryClearOnEmptyClick=function(e){var t=(0,ve.zj)(this._root.current);this._events.off(document,"click",this._findScrollParentAndTryClearOnEmptyClick),this._events.on(t,"click",this._tryClearOnEmptyClick),(t&&e.target instanceof Node&&t.contains(e.target)||t===e.target)&&this._tryClearOnEmptyClick(e)},t.prototype._tryClearOnEmptyClick=function(e){!this.props.selectionPreservedOnEmptyClick&&this._isNonHandledClick(e.target)&&this.props.selection.setAllSelected(!1)},t.prototype._clearAndSelectIndex=function(e,t){var n,o=this.props,r=o.selection,i=o.selectionClearedOnSurfaceClick,a=void 0===i||i;if(!((void 0===t||1===t)&&1===r.getSelectedCount()&&r.isIndexSelected(e))&&a){var s=r.isModal&&r.isModal();r.setChangeEvents(!1),r.setAllSelected(!1),void 0!==t?null===(n=r.setRangeSelected)||void 0===n||n.call(r,e,t,!0,!0):r.setIndexSelected(e,!0,!0),(s||this.props.enterModalOnTouch&&this._isTouch)&&(r.setModal&&r.setModal(!0),this._isTouch&&this._setIsTouch(!1)),r.setChangeEvents(!0)}},t.prototype._updateModifiers=function(e){this._isShiftPressed=e.shiftKey,this._isCtrlPressed=e.ctrlKey,this._isMetaPressed=e.metaKey;var t=e.keyCode;this._isTabPressed=!!t&&t===d.m.tab},t.prototype._findItemRoot=function(e){for(var t=this.props.selection;e!==this._root.current;){var n=e.getAttribute(be),o=Number(n);if(null!==n&&o>=0&&o<t.getItems().length)break;e=(0,me.G)(e)}if(e!==this._root.current)return e},t.prototype._getItemIndex=function(e){var t,n=parseInt(null!==(t=e.getAttribute(be))&&void 0!==t?t:"",10);return isNaN(n)?-1:n},t.prototype._getItemSpan=function(e){var t,n=parseInt(null!==(t=e.getAttribute("data-selection-span"))&&void 0!==t?t:"",10);return isNaN(n)?void 0:n},t.prototype._shouldAutoSelect=function(e){return this._hasAttribute(e,"data-selection-select")},t.prototype._hasAttribute=function(e,t){for(var n=!1;!n&&e!==this._root.current;){var o=e.getAttribute(t);if("false"===o){n=!1;break}n="true"===o,e=(0,me.G)(e)}return n},t.prototype._isInputElement=function(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||"true"===e.getAttribute("contenteditable")||""===e.getAttribute("contenteditable")},t.prototype._isNonHandledClick=function(e){var t=(0,G.M)();if(t&&e)for(;e&&e!==t.documentElement;){if((0,xe.MW)(e)||e.hasAttribute("data-selection-index"))return!1;e=(0,me.G)(e)}return!0},t.prototype._handleNextFocus=function(e){var t=this;this._shouldHandleFocusTimeoutId&&(this._async.clearTimeout(this._shouldHandleFocusTimeoutId),this._shouldHandleFocusTimeoutId=void 0),this._shouldHandleFocus=e,e&&this._async.setTimeout((function(){t._shouldHandleFocus=!1}),100)},t.prototype._setIsTouch=function(e){var t=this;this._isTouchTimeoutId&&(this._async.clearTimeout(this._isTouchTimeoutId),this._isTouchTimeoutId=void 0),this._isTouch=!0,e&&this._async.setTimeout((function(){t._isTouch=!1}),300)},t.prototype._getSelectionMode=function(){var e=this.props.selection,t=this.props.selectionMode;return void 0===t?e?e.mode:H.oW.none:t},t.defaultProps={isSelectedOnFocus:!0,toggleWithoutModifierPressed:!1,selectionMode:H.oW.multiple,selectionClearedOnEscapePress:!0},t}(a.Component),Se=function(){function e(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e[0]||{},o=n.onSelectionChanged,r=n.onItemsChanged,i=n.getKey,a=n.canSelectItem,s=void 0===a?function(){return!0}:a,l=n.items,c=n.selectionMode,d=void 0===c?H.oW.multiple:c;this.mode=d,this._getKey=i||Ee,this._changeEventSuppressionCount=0,this._exemptedCount=0,this._anchoredIndex=0,this._unselectableCount=0,this._onSelectionChanged=o,this._onItemsChanged=r,this._canSelectItem=s,this._keyToIndexMap={},this._isModal=!1,this.setItems(l||[],!0),this.count=this.getSelectedCount()}return e.prototype.canSelectItem=function(e,t){return!("number"===typeof t&&t<0)&&this._canSelectItem(e,t)},e.prototype.getKey=function(e,t){var n=this._getKey(e,t);return"number"===typeof n||n?"".concat(n):""},e.prototype.setChangeEvents=function(e,t){this._changeEventSuppressionCount+=e?-1:1,0===this._changeEventSuppressionCount&&this._hasChanged&&(this._hasChanged=!1,t||this._change())},e.prototype.isModal=function(){return this._isModal},e.prototype.setModal=function(e){this._isModal!==e&&(this.setChangeEvents(!1),this._isModal=e,e||this.setAllSelected(!1),this._change(),this.setChangeEvents(!0))},e.prototype.setItems=function(e,t){void 0===t&&(t=!0);var n={},o={},r=!1;this.setChangeEvents(!1),this._unselectableCount=0;for(var i=!1,a=0;a<e.length;a++){if(d=e[a])(m=this.getKey(d,a))&&(i||m in this._keyToIndexMap&&this._keyToIndexMap[m]===a||(i=!0),n[m]=a);o[a]=d&&!this.canSelectItem(d),o[a]&&this._unselectableCount++}(t||0===e.length)&&this._setAllSelected(!1,!0);var s={},l=0;for(var c in this._exemptedIndices)if(this._exemptedIndices.hasOwnProperty(c)){var d,u=Number(c),p=(d=this._items[u])?this.getKey(d,Number(u)):void 0,f=p?n[p]:u;void 0===f?r=!0:(s[f]=!0,l++,r=r||f!==u)}if(this._items&&0===this._exemptedCount&&e.length!==this._items.length&&this._isAllSelected&&(r=!0),!i)for(var h=0,g=Object.keys(this._keyToIndexMap);h<g.length;h++){var m;if(!((m=g[h])in n)){i=!0;break}}this._exemptedIndices=s,this._exemptedCount=l,this._keyToIndexMap=n,this._unselectableIndices=o,this._items=e,this._selectedItems=null,r&&this._updateCount(),i&&(v.r.raise(this,H.xC),this._onItemsChanged&&this._onItemsChanged()),r&&this._change(),this.setChangeEvents(!0)},e.prototype.getItems=function(){return this._items},e.prototype.getSelection=function(){if(!this._selectedItems){this._selectedItems=[];var e=this._items;if(e)for(var t=0;t<e.length;t++)this.isIndexSelected(t)&&this._selectedItems.push(e[t])}return this._selectedItems},e.prototype.getSelectedCount=function(){return this._isAllSelected?this._items.length-this._exemptedCount-this._unselectableCount:this._exemptedCount},e.prototype.getSelectedIndices=function(){if(!this._selectedIndices){this._selectedIndices=[];var e=this._items;if(e)for(var t=0;t<e.length;t++)this.isIndexSelected(t)&&this._selectedIndices.push(t)}return this._selectedIndices},e.prototype.getItemIndex=function(e){var t=this._keyToIndexMap[e];return null!==t&&void 0!==t?t:-1},e.prototype.isRangeSelected=function(e,t){if(0===t)return!1;for(var n=e+t,o=e;o<n;o++)if(!this.isIndexSelected(o))return!1;return!0},e.prototype.isAllSelected=function(){var e=this._items.length-this._unselectableCount;return this.mode===H.oW.single&&(e=Math.min(e,1)),this.count>0&&this._isAllSelected&&0===this._exemptedCount||!this._isAllSelected&&this._exemptedCount===e&&e>0},e.prototype.isKeySelected=function(e){var t=this._keyToIndexMap[e];return this.isIndexSelected(t)},e.prototype.isIndexSelected=function(e){return!!(this.count>0&&this._isAllSelected&&!this._exemptedIndices[e]&&!this._unselectableIndices[e]||!this._isAllSelected&&this._exemptedIndices[e])},e.prototype.setAllSelected=function(e){if(!e||this.mode===H.oW.multiple){var t=this._items?this._items.length-this._unselectableCount:0;this.setChangeEvents(!1),t>0&&(this._exemptedCount>0||e!==this._isAllSelected)&&(this._exemptedIndices={},(e!==this._isAllSelected||this._exemptedCount>0)&&(this._exemptedCount=0,this._isAllSelected=e,this._change()),this._updateCount()),this.setChangeEvents(!0)}},e.prototype.setKeySelected=function(e,t,n){var o=this._keyToIndexMap[e];o>=0&&this.setIndexSelected(o,t,n)},e.prototype.setIndexSelected=function(e,t,n){if(this.mode!==H.oW.none&&!((e=Math.min(Math.max(0,e),this._items.length-1))<0||e>=this._items.length)){this.setChangeEvents(!1);var o=this._exemptedIndices[e];!this._unselectableIndices[e]&&(t&&this.mode===H.oW.single&&this._setAllSelected(!1,!0),o&&(t&&this._isAllSelected||!t&&!this._isAllSelected)&&(delete this._exemptedIndices[e],this._exemptedCount--),!o&&(t&&!this._isAllSelected||!t&&this._isAllSelected)&&(this._exemptedIndices[e]=!0,this._exemptedCount++),n&&(this._anchoredIndex=e)),this._updateCount(),this.setChangeEvents(!0)}},e.prototype.setRangeSelected=function(e,t,n,o){if(this.mode!==H.oW.none&&(e=Math.min(Math.max(0,e),this._items.length-1),t=Math.min(Math.max(0,t),this._items.length-e),!(e<0||e>=this._items.length||0===t))){this.setChangeEvents(!1);for(var r=e,i=e+t-1,a=(this._anchoredIndex||0)>=i?r:i;r<=i;r++)this.setIndexSelected(r,n,!!o&&r===a);this.setChangeEvents(!0)}},e.prototype.selectToKey=function(e,t){this.selectToIndex(this._keyToIndexMap[e],t)},e.prototype.selectToRange=function(e,t,n){if(this.mode!==H.oW.none)if(this.mode!==H.oW.single){var o=this._anchoredIndex||0,r=Math.min(e,o),i=Math.max(e+t-1,o);for(this.setChangeEvents(!1),n&&this._setAllSelected(!1,!0);r<=i;r++)this.setIndexSelected(r,!0,!1);this.setChangeEvents(!0)}else 1===t&&this.setIndexSelected(e,!0,!0)},e.prototype.selectToIndex=function(e,t){if(this.mode!==H.oW.none)if(this.mode!==H.oW.single){var n=this._anchoredIndex||0,o=Math.min(e,n),r=Math.max(e,n);for(this.setChangeEvents(!1),t&&this._setAllSelected(!1,!0);o<=r;o++)this.setIndexSelected(o,!0,!1);this.setChangeEvents(!0)}else this.setIndexSelected(e,!0,!0)},e.prototype.toggleAllSelected=function(){this.setAllSelected(!this.isAllSelected())},e.prototype.toggleKeySelected=function(e){this.setKeySelected(e,!this.isKeySelected(e),!0)},e.prototype.toggleIndexSelected=function(e){this.setIndexSelected(e,!this.isIndexSelected(e),!0)},e.prototype.toggleRangeSelected=function(e,t){if(this.mode!==H.oW.none){var n=this.isRangeSelected(e,t),o=e+t;if(!(this.mode===H.oW.single&&t>1)){this.setChangeEvents(!1);for(var r=e;r<o;r++)this.setIndexSelected(r,!n,!1);this.setChangeEvents(!0)}}},e.prototype._updateCount=function(e){void 0===e&&(e=!1);var t=this.getSelectedCount();t!==this.count&&(this.count=t,this._change()),this.count||e||this.setModal(!1)},e.prototype._setAllSelected=function(e,t){if(void 0===t&&(t=!1),!e||this.mode===H.oW.multiple){var n=this._items?this._items.length-this._unselectableCount:0;this.setChangeEvents(!1),n>0&&(this._exemptedCount>0||e!==this._isAllSelected)&&(this._exemptedIndices={},(e!==this._isAllSelected||this._exemptedCount>0)&&(this._exemptedCount=0,this._isAllSelected=e,this._change()),this._updateCount(t)),this.setChangeEvents(!0)}},e.prototype._change=function(){0===this._changeEventSuppressionCount?(this._selectedItems=null,this._selectedIndices=void 0,v.r.raise(this,H.F5),this._onSelectionChanged&&this._onSelectionChanged()):this._hasChanged=!0},e}();function Ee(e,t){var n=(e||{}).key;return void 0===n?"".concat(t):n}var Te={root:"ms-GroupedList",compact:"ms-GroupedList--Compact",group:"ms-GroupedList-group",link:"ms-Link",listCell:"ms-List-cell"},De="cubic-bezier(0.445, 0.050, 0.550, 0.950)",Re={root:"ms-GroupHeader",compact:"ms-GroupHeader--compact",check:"ms-GroupHeader-check",dropIcon:"ms-GroupHeader-dropIcon",expand:"ms-GroupHeader-expand",isCollapsed:"is-collapsed",title:"ms-GroupHeader-title",isSelected:"is-selected",iconTag:"ms-Icon--Tag",group:"ms-GroupedList-group",isDropping:"is-dropping"},Pe="cubic-bezier(0.075, 0.820, 0.165, 1.000)",Ie="cubic-bezier(0.390, 0.575, 0.565, 1.000)",Me="cubic-bezier(0.600, -0.280, 0.735, 0.045)",Oe=n(97708),Le=(0,s.y)(),Ae=function(e){function t(t){var n=e.call(this,t)||this;return n._toggleCollapse=function(){var e=n.props,t=e.group,o=e.onToggleCollapse,r=e.isGroupLoading,i=!n.state.isCollapsed,a=!i&&r&&r(t);n.setState({isCollapsed:i,isLoadingVisible:a}),o&&o(t)},n._onKeyUp=function(e){var t=n.props,o=t.group,r=t.onGroupHeaderKeyUp;if(r&&r(e,o),!e.defaultPrevented){var i=n.state.isCollapsed&&e.which===(0,c.dP)(d.m.right,n.props.theme);(!n.state.isCollapsed&&e.which===(0,c.dP)(d.m.left,n.props.theme)||i)&&(n._toggleCollapse(),e.stopPropagation(),e.preventDefault())}},n._onToggleClick=function(e){n._toggleCollapse(),e.stopPropagation(),e.preventDefault()},n._onHeaderClick=function(){var e=n.props,t=e.group,o=e.onGroupHeaderClick;o&&o(t)},n._onRenderTitle=function(e){if(!e.group)return null;var t=e.onRenderName?(0,E.k)(e.onRenderName,n._onRenderName):n._onRenderName;return a.createElement("div",{className:n._classNames.title,id:n._id,onClick:n._onHeaderClick,role:"gridcell","aria-colspan":n.props.ariaColSpan,"data-selection-invoke":!0},t(e))},n._onRenderName=function(e){var t=e.group;return t?a.createElement(a.Fragment,null,a.createElement("span",null,t.name),a.createElement("span",{className:n._classNames.headerCount},"(",t.count,t.hasMoreData&&"+",")")):null},n._id=(0,x.z)("GroupHeader"),n.state={isCollapsed:n.props.group&&n.props.group.isCollapsed,isLoadingVisible:!1},n}return(0,i.ZT)(t,e),t.getDerivedStateFromProps=function(e,t){if(e.group){var n=e.group.isCollapsed,o=e.isGroupLoading,r=!n&&o&&o(e.group);return(0,i.pi)((0,i.pi)({},t),{isCollapsed:n||!1,isLoadingVisible:r||!1})}return t},t.prototype.render=function(){var e=this.props,t=e.group,n=e.groupLevel,o=void 0===n?0:n,r=e.viewport,s=e.selectionMode,l=e.loadingText,d=e.isSelected,u=void 0!==d&&d,p=e.selected,f=void 0!==p&&p,h=e.indentWidth,g=e.onRenderGroupHeaderCheckbox,m=e.isCollapsedGroupSelectVisible,y=void 0===m||m,v=e.expandButtonProps,x=e.expandButtonIcon,b=e.selectAllButtonProps,_=e.theme,k=e.styles,C=e.className,T=e.compact,D=e.ariaLevel,R=e.ariaPosInSet,P=e.ariaSetSize,I=e.ariaRowIndex,M=e.useFastIcons,O=this.props.onRenderTitle?(0,E.k)(this.props.onRenderTitle,this._onRenderTitle):this._onRenderTitle,L=M?this._fastDefaultCheckboxRender:this._defaultCheckboxRender,A=g?(0,E.k)(g,L):L,z=this.state,N=z.isCollapsed,F=z.isLoadingVisible,B=s===H.oW.multiple,j=B&&(y||!(t&&t.isCollapsed)),W=f||u,U=(0,c.zg)(_);return this._classNames=Le(k,{theme:_,className:C,selected:W,isCollapsed:N,compact:T}),t?a.createElement("div",{className:this._classNames.root,style:r?{minWidth:r.width}:{},role:"row","aria-level":D,"aria-setsize":P,"aria-posinset":R,"aria-rowindex":I,"data-is-focusable":!0,onKeyUp:this._onKeyUp,"aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabel?void 0:this._id,"aria-expanded":!this.state.isCollapsed,"aria-selected":B?W:void 0,"data-selection-index":t.startIndex,"data-selection-span":t.count},a.createElement("div",{className:this._classNames.groupHeaderContainer,role:"presentation"},j?a.createElement("div",{role:"gridcell"},a.createElement("button",(0,i.pi)({"data-is-focusable":!1,type:"button",className:this._classNames.check,role:"checkbox",id:"".concat(this._id,"-check"),"aria-checked":W,"aria-labelledby":"".concat(this._id,"-check ").concat(this._id),"data-selection-toggle":!0},b),A({checked:W,theme:_},A))):s!==H.oW.none&&a.createElement(S,{indentWidth:48,count:1}),a.createElement(S,{indentWidth:h,count:o}),a.createElement("div",{className:this._classNames.dropIcon,role:"presentation"},a.createElement(w.J,{iconName:"Tag"})),a.createElement("div",{role:"gridcell"},a.createElement("button",(0,i.pi)({"data-is-focusable":!1,"data-selection-disabled":!0,type:"button",className:this._classNames.expand,onClick:this._onToggleClick,"aria-expanded":!this.state.isCollapsed},v),a.createElement(w.J,{className:this._classNames.expandIsCollapsed,iconName:x||(U?"ChevronLeftMed":"ChevronRightMed")}))),O(this.props),F&&a.createElement(Oe.$,{label:l}))):null},t.prototype._defaultCheckboxRender=function(e){return a.createElement(M,{checked:e.checked})},t.prototype._fastDefaultCheckboxRender=function(e){return a.createElement(ze,{theme:e.theme,checked:e.checked})},t.defaultProps={expandButtonProps:{"aria-label":"expand collapse group"}},t}(a.Component),ze=a.memo((function(e){return a.createElement(M,{theme:e.theme,checked:e.checked,className:e.className,useFastIcons:!0})})),Ne=(0,r.z)(Ae,(function(e){var t,n,o,r,i,a=e.theme,s=e.className,l=e.selected,d=e.isCollapsed,u=e.compact,p=O.Wt.cellLeftPadding,f=u?40:48,h=a.semanticColors,g=a.palette,m=a.fonts,y=(0,P.Cn)(Re,a),v=[(0,P.GL)(a),{cursor:"default",background:"none",backgroundColor:"transparent",border:"none",padding:0}];return{root:[y.root,(0,P.GL)(a),a.fonts.medium,{borderBottom:"1px solid ".concat(h.listBackground),cursor:"default",userSelect:"none",selectors:(t={":hover":{background:h.listItemBackgroundHovered,color:h.actionLinkHovered}},t["&:hover .".concat(y.check)]={opacity:1},t[".".concat(L.G$," &:focus .").concat(y.check)]={opacity:1},t[":global(.".concat(y.group,".").concat(y.isDropping,")")]={selectors:(n={},n["& > .".concat(y.root," .").concat(y.dropIcon)]={transition:"transform ".concat(P.D1.durationValue4," ").concat(Pe," ")+"opacity ".concat(P.D1.durationValue1," ").concat(Ie),transitionDelay:P.D1.durationValue3,opacity:1,transform:"rotate(0.2deg) scale(1);"},n[".".concat(y.check)]={opacity:0},n)},t)},l&&[y.isSelected,{background:h.listItemBackgroundChecked,selectors:(o={":hover":{background:h.listItemBackgroundCheckedHovered}},o["".concat(y.check)]={opacity:1},o)}],u&&[y.compact,{border:"none"}],s],groupHeaderContainer:[{display:"flex",alignItems:"center",height:f}],headerCount:[{padding:"0px 4px"}],check:[y.check,v,{display:"flex",alignItems:"center",justifyContent:"center",paddingTop:1,marginTop:-1,opacity:0,width:48,height:f,selectors:(r={},r[".".concat(L.G$," &:focus")]={opacity:1},r)}],expand:[y.expand,v,{display:"flex",flexShrink:0,alignItems:"center",justifyContent:"center",fontSize:m.small.fontSize,width:36,height:f,color:l?g.neutralPrimary:g.neutralSecondary,selectors:{":hover":{backgroundColor:l?g.neutralQuaternary:g.neutralLight},":active":{backgroundColor:l?g.neutralTertiaryAlt:g.neutralQuaternaryAlt}}}],expandIsCollapsed:[d?[y.isCollapsed,{transform:"rotate(0deg)",transformOrigin:"50% 50%",transition:"transform .1s linear"}]:{transform:(0,c.zg)(a)?"rotate(-90deg)":"rotate(90deg)",transformOrigin:"50% 50%",transition:"transform .1s linear"}],title:[y.title,{paddingLeft:p,fontSize:u?m.medium.fontSize:m.mediumPlus.fontSize,fontWeight:d?P.lq.regular:P.lq.semibold,cursor:"pointer",outline:0,whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden"}],dropIcon:[y.dropIcon,{position:"absolute",left:-26,fontSize:P.ld.large,color:g.neutralSecondary,transition:"transform ".concat(P.D1.durationValue2," ").concat(Me,", ")+"opacity ".concat(P.D1.durationValue4," ").concat(Ie),opacity:0,transform:"rotate(0.2deg) scale(0.65)",transformOrigin:"10px 10px",selectors:(i={},i[":global(.".concat(y.iconTag,")")]={position:"absolute"},i)}]}}),void 0,{scope:"GroupHeader"}),He={root:"ms-GroupShowAll",link:"ms-Link"},Fe=n(66587),Be=(0,s.y)(),je=function(e,t){a.useImperativeHandle(e.componentRef,(function(){return{focus:function(){t.current&&t.current.focus()}}}),[t])},We=function(e,t){t.as;var n=t.disabled,o=t.target,r=t.href,a=(t.theme,t.getStyles,t.styles,t.componentRef,t.underline,(0,i._T)(t,["as","disabled","target","href","theme","getStyles","styles","componentRef","underline"]));return"string"===typeof e?"a"===e?(0,i.pi)({target:o,href:n?void 0:r},a):"button"===e?(0,i.pi)({type:"button",disabled:n},a):(0,i.pi)((0,i.pi)({},a),{disabled:n}):(0,i.pi)({target:o,href:r,disabled:n},a)},Ue=a.forwardRef((function(e,t){var n=function(e,t){var n=e.as,o=e.className,r=e.disabled,s=e.href,l=e.onClick,c=e.styles,d=e.theme,u=e.underline,f=a.useRef(null),h=(0,Fe.r)(f,t);je(e,f),(0,p.Pr)(f);var g=Be(c,{className:o,isButton:!s,isDisabled:r,isUnderlined:u,theme:d}),m=n||(s?"a":"button");return{state:{},slots:{root:m},slotProps:{root:(0,i.pi)((0,i.pi)({},We(m,e)),{"aria-disabled":r,className:g.root,onClick:function(e){r?e.preventDefault():l&&l(e)},ref:h})}}}(e,t),o=n.slots,r=n.slotProps;return a.createElement(o.root,(0,i.pi)({},r.root))}));Ue.displayName="LinkBase";var Ve=n(13687),Ge=(0,r.z)(Ue,Ve.w,void 0,{scope:"Link"}),qe=(0,s.y)(),Ke=(0,r.z)((function(e){var t=e.group,n=e.groupLevel,o=e.showAllLinkText,r=void 0===o?"Show All":o,i=e.styles,s=e.theme,l=e.onToggleSummarize,c=qe(i,{theme:s}),d=(0,a.useCallback)((function(e){l(t),e.stopPropagation(),e.preventDefault()}),[l,t]);return t?a.createElement("div",{className:c.root},a.createElement(S,{count:n}),a.createElement(Ge,{onClick:d},r)):null}),(function(e){var t,n=e.theme,o=n.fonts,r=(0,P.Cn)(He,n);return{root:[r.root,{position:"relative",padding:"10px 84px",cursor:"pointer",selectors:(t={},t[".".concat(r.link)]={fontSize:o.small.fontSize},t)}]}}),void 0,{scope:"GroupShowAll"}),Ye={root:"ms-groupFooter"},Xe=(0,s.y)(),Ze=(0,r.z)((function(e){var t=e.group,n=e.groupLevel,o=e.footerText,r=e.indentWidth,i=e.styles,s=e.theme,l=Xe(i,{theme:s});return t&&o?a.createElement("div",{className:l.root},a.createElement(S,{indentWidth:r,count:n}),o):null}),(function(e){var t=e.theme,n=e.className,o=(0,P.Cn)(Ye,t);return{root:[t.fonts.medium,o.root,{position:"relative",padding:"5px 38px"},n]}}),void 0,{scope:"GroupFooter"}),Je=n(57780),$e=n(63039),Qe=0,et=1,tt=2,nt=3,ot=function(e){if(void 0===e)return 0;var t=0;return"scrollTop"in e?t=e.scrollTop:"scrollY"in e&&(t=e.scrollY),Math.ceil(t)},rt=function(e,t){"scrollTop"in e?e.scrollTop=t:"scrollY"in e&&e.scrollTo(e.scrollX,t)},it="spacer-",at={top:-1,bottom:-1,left:-1,right:-1,width:0,height:0},st=function(e){return e.getBoundingClientRect()},lt=st,ct=st,dt=function(e){function t(t){var n=e.call(this,t)||this;return n._root=a.createRef(),n._surface=a.createRef(),n._pageRefs={},n._getDerivedStateFromProps=function(e,t){return e.items!==n.props.items||e.renderCount!==n.props.renderCount||e.startIndex!==n.props.startIndex||e.version!==n.props.version||!t.hasMounted&&n.props.renderEarly&&(0,Je.N)()?(n._resetRequiredWindows(),n._requiredRect=null,n._measureVersion++,n._invalidatePageCache(),n._updatePages(e,t)):t},n._onRenderRoot=function(e){var t=e.rootRef,n=e.surfaceElement,o=e.divProps;return a.createElement("div",(0,i.pi)({ref:t},o),n)},n._onRenderSurface=function(e){var t=e.surfaceRef,n=e.pageElements,o=e.divProps;return a.createElement("div",(0,i.pi)({ref:t},o),n)},n._onRenderPage=function(e,t){for(var o,r=n.props,s=r.onRenderCell,l=r.onRenderCellConditional,c=r.role,d=e.page,u=d.items,p=void 0===u?[]:u,f=d.startIndex,h=(0,i._T)(e,["page"]),g=void 0===c?"listitem":"presentation",m=[],y=0;y<p.length;y++){var v=f+y,x=p[y],b=n.props.getKey?n.props.getKey(x,v):x&&x.key;null!==b&&void 0!==b||(b=v);var _=null!==l&&void 0!==l?l:s,k=null!==(o=null===_||void 0===_?void 0:_(x,v,n.props.ignoreScrollingState?void 0:n.state.isScrolling))&&void 0!==o?o:null;l&&!k||m.push(a.createElement("div",{role:g,className:"ms-List-cell",key:b,"data-list-index":v,"data-automationid":"ListCell"},k))}return a.createElement("div",(0,i.pi)({},h),m)},(0,f.l)(n),n.state={pages:[],isScrolling:!1,getDerivedStateFromProps:n._getDerivedStateFromProps,hasMounted:!1},n._async=new h.e(n),n._events=new v.r(n),n._estimatedPageHeight=0,n._totalEstimates=0,n._requiredWindowsAhead=0,n._requiredWindowsBehind=0,n._measureVersion=0,n._onAsyncScroll=n._async.debounce(n._onAsyncScroll,100,{leading:!1,maxWait:500}),n._onAsyncIdle=n._async.debounce(n._onAsyncIdle,200,{leading:!1}),n._onAsyncResize=n._async.debounce(n._onAsyncResize,16,{leading:!1}),n._onScrollingDone=n._async.debounce(n._onScrollingDone,500,{leading:!1}),n._cachedPageHeights={},n._estimatedPageHeight=0,n._focusedIndex=-1,n._pageCache={},n}return(0,i.ZT)(t,e),t.getDerivedStateFromProps=function(e,t){return t.getDerivedStateFromProps(e,t)},Object.defineProperty(t.prototype,"pageRefs",{get:function(){return this._pageRefs},enumerable:!1,configurable:!0}),t.prototype.scrollToIndex=function(e,t,n){void 0===n&&(n=Qe);for(var o=this.props.startIndex,r=o+this._getRenderCount(),i=this._allowedRect,a=0,s=1,l=o;l<r;l+=s){var c=this._getPageSpecification(this.props,l,i),d=c.height;if(s=c.itemCount,l<=e&&l+s>e){if(t&&this._scrollElement){for(var u=ct(this._scrollElement),p=ot(this._scrollElement),f={top:p,bottom:p+u.height},h=e-l,g=0;g<h;++g)a+=t(l+g);var m=a+t(e);switch(n){case et:return void rt(this._scrollElement,a);case tt:return void rt(this._scrollElement,m-u.height);case nt:return void rt(this._scrollElement,(a+m-u.height)/2)}if(a>=f.top&&m<=f.bottom)return;a<f.top||m>f.bottom&&(a=m-u.height)}return void(this._scrollElement&&rt(this._scrollElement,a))}a+=d}},t.prototype.getStartItemIndexInView=function(e){for(var t=0,n=this.state.pages||[];t<n.length;t++){var o=n[t];if(!o.isSpacer&&(this._scrollTop||0)>=o.top&&(this._scrollTop||0)<=o.top+o.height){if(!e){var r=Math.floor(o.height/o.itemCount);return o.startIndex+Math.floor((this._scrollTop-o.top)/r)}for(var i=0,a=o.startIndex;a<o.startIndex+o.itemCount;a++){r=e(a);if(o.top+i<=this._scrollTop&&this._scrollTop<o.top+i+r)return a;i+=r}}}return 0},t.prototype.componentDidMount=function(){this._scrollElement=(0,ve.zj)(this._root.current),this._scrollTop=0,this.setState((0,i.pi)((0,i.pi)({},this._updatePages(this.props,this.state)),{hasMounted:!0})),this._measureVersion++,this._events.on(window,"resize",this._onAsyncResize),this._root.current&&this._events.on(this._root.current,"focus",this._onFocus,!0),this._scrollElement&&(this._events.on(this._scrollElement,"scroll",this._onScroll),this._events.on(this._scrollElement,"scroll",this._onAsyncScroll))},t.prototype.componentDidUpdate=function(e,t){var n=this.props,o=this.state;if(this.state.pagesVersion!==t.pagesVersion){if(n.getPageHeight)this._onAsyncIdle();else this._updatePageMeasurements(o.pages)?(this._materializedRect=null,this._hasCompletedFirstRender?this._onAsyncScroll():(this._hasCompletedFirstRender=!0,this.setState(this._updatePages(n,o)))):this._onAsyncIdle();n.onPagesUpdated&&n.onPagesUpdated(o.pages)}},t.prototype.componentWillUnmount=function(){this._async.dispose(),this._events.dispose(),delete this._scrollElement},t.prototype.shouldComponentUpdate=function(e,t){var n=this.state.pages,o=t.pages,r=!1;if(!t.isScrolling&&this.state.isScrolling)return!0;if(e.version!==this.props.version)return!0;if(e.className!==this.props.className)return!0;if(e.items===this.props.items&&n.length===o.length)for(var i=0;i<n.length;i++){var a=n[i],s=o[i];if(a.key!==s.key||a.itemCount!==s.itemCount){r=!0;break}}else r=!0;return r},t.prototype.forceUpdate=function(){this._invalidatePageCache(),this._updateRenderRects(this.props,this.state,!0),this.setState(this._updatePages(this.props,this.state)),this._measureVersion++,e.prototype.forceUpdate.call(this)},t.prototype.getTotalListHeight=function(){return this._surfaceRect.height},t.prototype.render=function(){for(var e=this.props,t=e.className,n=e.role,o=void 0===n?"list":n,r=e.onRenderSurface,a=e.onRenderRoot,s=this.state.pages,l=void 0===s?[]:s,c=[],d=(0,le.pq)(this.props,le.n7),p=0,f=l;p<f.length;p++){var h=f[p];c.push(this._renderPage(h))}var g=r?(0,E.k)(r,this._onRenderSurface):this._onRenderSurface;return(a?(0,E.k)(a,this._onRenderRoot):this._onRenderRoot)({rootRef:this._root,pages:l,surfaceElement:g({surfaceRef:this._surface,pages:l,pageElements:c,divProps:{role:"presentation",className:"ms-List-surface"}}),divProps:(0,i.pi)((0,i.pi)({},d),{className:(0,u.i)("ms-List",t),role:c.length>0?o:void 0,"aria-label":c.length>0?d["aria-label"]:void 0})})},t.prototype._shouldVirtualize=function(e){void 0===e&&(e=this.props);var t=e.onShouldVirtualize;return!t||t(e)},t.prototype._invalidatePageCache=function(){this._pageCache={}},t.prototype._renderPage=function(e){var t,n=this,o=this.props.usePageCache;if(o&&(t=this._pageCache[e.key])&&t.pageElement)return t.pageElement;var r=this._getPageStyle(e),i=this.props.onRenderPage,a=(void 0===i?this._onRenderPage:i)({page:e,className:"ms-List-page",key:e.key,ref:function(t){n._pageRefs[e.key]=t},style:r,role:"presentation"},this._onRenderPage);return o&&0===e.startIndex&&(this._pageCache[e.key]={page:e,pageElement:a}),a},t.prototype._getPageStyle=function(e){var t=this.props.getPageStyle;return(0,i.pi)((0,i.pi)({},t?t(e):{}),e.items?{}:{height:e.height})},t.prototype._onFocus=function(e){for(var t=e.target;t!==this._surface.current;){var n=t.getAttribute("data-list-index");if(n){this._focusedIndex=Number(n);break}t=(0,me.G)(t)}},t.prototype._onScroll=function(){this.state.isScrolling||this.props.ignoreScrollingState||this.setState({isScrolling:!0}),this._resetRequiredWindows(),this._onScrollingDone()},t.prototype._resetRequiredWindows=function(){this._requiredWindowsAhead=0,this._requiredWindowsBehind=0},t.prototype._onAsyncScroll=function(){var e,t;this._updateRenderRects(this.props,this.state),this._materializedRect&&(e=this._requiredRect,t=this._materializedRect,e.top>=t.top&&e.left>=t.left&&e.bottom<=t.bottom&&e.right<=t.right)||this.setState(this._updatePages(this.props,this.state))},t.prototype._onAsyncIdle=function(){var e=this.props,t=e.renderedWindowsAhead,n=e.renderedWindowsBehind,o=this._requiredWindowsAhead,r=this._requiredWindowsBehind,i=Math.min(t,o+1),a=Math.min(n,r+1);i===o&&a===r||(this._requiredWindowsAhead=i,this._requiredWindowsBehind=a,this._updateRenderRects(this.props,this.state),this.setState(this._updatePages(this.props,this.state))),(t>i||n>a)&&this._onAsyncIdle()},t.prototype._onScrollingDone=function(){this.props.ignoreScrollingState||this.setState({isScrolling:!1})},t.prototype._onAsyncResize=function(){this.forceUpdate()},t.prototype._updatePages=function(e,t){this._requiredRect||this._updateRenderRects(e,t);var n=this._buildPages(e,t),o=t.pages;return this._notifyPageChanges(o,n.pages,this.props),(0,i.pi)((0,i.pi)((0,i.pi)({},t),n),{pagesVersion:{}})},t.prototype._notifyPageChanges=function(e,t,n){var o=n.onPageAdded,r=n.onPageRemoved;if(o||r){for(var i={},a=0,s=e;a<s.length;a++){(d=s[a]).items&&(i[d.startIndex]=d)}for(var l=0,c=t;l<c.length;l++){var d;(d=c[l]).items&&(i[d.startIndex]?delete i[d.startIndex]:this._onPageAdded(d))}for(var u in i)i.hasOwnProperty(u)&&this._onPageRemoved(i[u])}},t.prototype._updatePageMeasurements=function(e){var t=!1;if(!this._shouldVirtualize())return t;for(var n=0;n<e.length;n++){var o=e[n];o.items&&(t=this._measurePage(o)||t)}return t},t.prototype._measurePage=function(e){var t=!1,n=this._pageRefs[e.key],o=this._cachedPageHeights[e.startIndex];if(n&&this._shouldVirtualize()&&(!o||o.measureVersion!==this._measureVersion)){var r={width:n.clientWidth,height:n.clientHeight};(r.height||r.width)&&(t=e.height!==r.height,e.height=r.height,this._cachedPageHeights[e.startIndex]={height:r.height,measureVersion:this._measureVersion},this._estimatedPageHeight=Math.round((this._estimatedPageHeight*this._totalEstimates+r.height)/(this._totalEstimates+1)),this._totalEstimates++)}return t},t.prototype._onPageAdded=function(e){var t=this.props.onPageAdded;t&&t(e)},t.prototype._onPageRemoved=function(e){var t=this.props.onPageRemoved;t&&t(e)},t.prototype._buildPages=function(e,t){var n=e.renderCount,o=e.items,r=e.startIndex,a=e.getPageHeight;n=this._getRenderCount(e);for(var s=(0,i.pi)({},at),l=[],c=1,d=0,u=null,p=this._focusedIndex,f=r+n,h=this._shouldVirtualize(e),g=0===this._estimatedPageHeight&&!a,m=this._allowedRect,y=function(n){var i=v._getPageSpecification(e,n,m),a=i.height,y=i.data,x=i.key;c=i.itemCount;var b,_,k=d+a-1,w=(0,$e.cx)(t.pages,(function(e){return!!e.items&&e.startIndex===n}))>-1,C=!m||k>=m.top&&d<=m.bottom,S=!v._requiredRect||k>=v._requiredRect.top&&d<=v._requiredRect.bottom;if(!g&&(S||C&&w)||!h||p>=n&&p<n+c||n===r){u&&(l.push(u),u=null);var E=Math.min(c,f-n),T=v._createPage(x,o.slice(n,n+E),n,void 0,void 0,y);T.top=d,T.height=a,v._visibleRect&&v._visibleRect.bottom&&(T.isVisible=k>=v._visibleRect.top&&d<=v._visibleRect.bottom),l.push(T),S&&v._allowedRect&&(b=s,_={top:d,bottom:k,height:a,left:m.left,right:m.right,width:m.width},b.top=_.top<b.top||-1===b.top?_.top:b.top,b.left=_.left<b.left||-1===b.left?_.left:b.left,b.bottom=_.bottom>b.bottom||-1===b.bottom?_.bottom:b.bottom,b.right=_.right>b.right||-1===b.right?_.right:b.right,b.width=b.right-b.left+1,b.height=b.bottom-b.top+1)}else u||(u=v._createPage(it+n,void 0,n,0,void 0,y,!0)),u.height=(u.height||0)+(k-d)+1,u.itemCount+=c;if(d+=k-d+1,g&&h)return"break"},v=this,x=r;x<f;x+=c){if("break"===y(x))break}return u&&(u.key=it+"end",l.push(u)),this._materializedRect=s,(0,i.pi)((0,i.pi)({},t),{pages:l,measureVersion:this._measureVersion})},t.prototype._getPageSpecification=function(e,t,n){var o=e.getPageSpecification;if(o){var r=o(t,n,e.items),i=r.itemCount,a=void 0===i?this._getItemCountForPage(t,n):i,s=r.height;return{itemCount:a,height:void 0===s?this._getPageHeight(t,n,a):s,data:r.data,key:r.key}}return{itemCount:a=this._getItemCountForPage(t,n),height:this._getPageHeight(t,n,a)}},t.prototype._getPageHeight=function(e,t,n){if(this.props.getPageHeight)return this.props.getPageHeight(e,t,n,this.props.items);var o=this._cachedPageHeights[e];return o?o.height:this._estimatedPageHeight||30},t.prototype._getItemCountForPage=function(e,t){var n=this.props.getItemCountForPage?this.props.getItemCountForPage(e,t):10;return n||10},t.prototype._createPage=function(e,t,n,o,r,i,a){void 0===n&&(n=-1),void 0===o&&(o=t?t.length:0),void 0===r&&(r={}),e=e||"page-"+n;var s=this._pageCache[e];return s&&s.page?s.page:{key:e,startIndex:n,itemCount:o,items:t,style:r,top:0,height:0,data:i,isSpacer:a||!1}},t.prototype._getRenderCount=function(e){var t=e||this.props,n=t.items,o=t.startIndex,r=t.renderCount;return void 0===r?n?n.length-o:0:r},t.prototype._updateRenderRects=function(e,t,n){var o=e.renderedWindowsAhead,r=e.renderedWindowsBehind,a=t.pages;if(this._shouldVirtualize(e)){var s=this._surfaceRect||(0,i.pi)({},at),l=function(e){if(void 0===e)return 0;var t=0;return"scrollHeight"in e?t=e.scrollHeight:"document"in e&&(t=e.document.documentElement.scrollHeight),t}(this._scrollElement),c=ot(this._scrollElement);this._surface.current&&(n||!a||!this._surfaceRect||!l||l!==this._scrollHeight||Math.abs(this._scrollTop-c)>this._estimatedPageHeight/3)&&(s=this._surfaceRect=lt(this._surface.current),this._scrollTop=c),!n&&l&&l===this._scrollHeight||this._measureVersion++,this._scrollHeight=l||0;var d=Math.max(0,-s.top),u=(0,ye.J)(this._root.current),p={top:d,left:s.left,bottom:d+u.innerHeight,right:s.right,width:s.width,height:u.innerHeight};this._requiredRect=ut(p,this._requiredWindowsBehind,this._requiredWindowsAhead),this._allowedRect=ut(p,r,o),this._visibleRect=p}},t.defaultProps={startIndex:0,onRenderCell:function(e,t,n){return a.createElement(a.Fragment,null,e&&e.name||"")},onRenderCellConditional:void 0,renderedWindowsAhead:2,renderedWindowsBehind:2},t}(a.Component);function ut(e,t,n){var o=e.top-t*e.height,r=e.height+(t+n)*e.height;return{top:o,bottom:o+r,height:r,left:e.left,right:e.right,width:e.width}}var pt=function(e){function t(n){var o=e.call(this,n)||this;o._root=a.createRef(),o._list=a.createRef(),o._subGroupRefs={},o._droppingClassName="",o._onRenderGroupHeader=function(e){return a.createElement(Ne,(0,i.pi)({},e))},o._onRenderGroupShowAll=function(e){return a.createElement(Ke,(0,i.pi)({},e))},o._onRenderGroupFooter=function(e){return a.createElement(Ze,(0,i.pi)({},e))},o._renderSubGroup=function(e,n){var r=o.props,i=r.dragDropEvents,s=r.dragDropHelper,l=r.eventsToRegister,c=r.getGroupItemLimit,d=r.groupNestingDepth,u=r.groupProps,p=r.items,f=r.headerProps,h=r.showAllProps,g=r.footerProps,m=r.listProps,y=r.onRenderCell,v=r.selection,x=r.selectionMode,b=r.viewport,_=r.onRenderGroupHeader,k=r.onRenderGroupShowAll,w=r.onRenderGroupFooter,C=r.onShouldVirtualize,S=r.group,E=r.compact,T=e.level?e.level+1:d;return!e||e.count>0||u&&u.showEmptyGroups?a.createElement(t,{ref:function(e){return o._subGroupRefs["subGroup_"+n]=e},key:o._getGroupKey(e,n),dragDropEvents:i,dragDropHelper:s,eventsToRegister:l,footerProps:g,getGroupItemLimit:c,group:e,groupIndex:n,groupNestingDepth:T,groupProps:u,headerProps:f,items:p,listProps:m,onRenderCell:y,selection:v,selectionMode:x,showAllProps:h,viewport:b,onRenderGroupHeader:_,onRenderGroupShowAll:k,onRenderGroupFooter:w,onShouldVirtualize:C,groups:S?S.children:[],compact:E}):null},o._getGroupDragDropOptions=function(){var e=o.props,t=e.group,n=e.groupIndex,r=e.dragDropEvents;return{eventMap:e.eventsToRegister,selectionIndex:-1,context:{data:t,index:n,isGroup:!0},updateDropState:o._updateDroppingState,canDrag:r.canDrag,canDrop:r.canDrop,onDrop:r.onDrop,onDragStart:r.onDragStart,onDragEnter:r.onDragEnter,onDragLeave:r.onDragLeave,onDragEnd:r.onDragEnd,onDragOver:r.onDragOver}},o._updateDroppingState=function(e,t){var n=o.state.isDropping,r=o.props,i=r.dragDropEvents,a=r.group;n!==e&&(n?i&&i.onDragLeave&&i.onDragLeave(a,t):i&&i.onDragEnter&&(o._droppingClassName=i.onDragEnter(a,t)),o.setState({isDropping:e}))};var r=n.selection,s=n.group;return(0,f.l)(o),o._id=(0,x.z)("GroupedListSection"),o.state={isDropping:!1,isSelected:!(!r||!s)&&r.isRangeSelected(s.startIndex,s.count)},o._events=new v.r(o),o}return(0,i.ZT)(t,e),t.prototype.componentDidMount=function(){var e=this.props,t=e.dragDropHelper,n=e.selection;t&&this._root.current&&(this._dragDropSubscription=t.subscribe(this._root.current,this._events,this._getGroupDragDropOptions())),n&&this._events.on(n,H.F5,this._onSelectionChange)},t.prototype.componentWillUnmount=function(){this._events.dispose(),this._dragDropSubscription&&this._dragDropSubscription.dispose()},t.prototype.componentDidUpdate=function(e){this.props.group===e.group&&this.props.groupIndex===e.groupIndex&&this.props.dragDropHelper===e.dragDropHelper||(this._dragDropSubscription&&(this._dragDropSubscription.dispose(),delete this._dragDropSubscription),this.props.dragDropHelper&&this._root.current&&(this._dragDropSubscription=this.props.dragDropHelper.subscribe(this._root.current,this._events,this._getGroupDragDropOptions())))},t.prototype.render=function(){var e=this.props,t=e.getGroupItemLimit,n=e.group,o=e.groupIndex,r=e.headerProps,s=e.showAllProps,l=e.footerProps,c=e.viewport,d=e.selectionMode,p=e.onRenderGroupHeader,f=void 0===p?this._onRenderGroupHeader:p,h=e.onRenderGroupShowAll,g=void 0===h?this._onRenderGroupShowAll:h,m=e.onRenderGroupFooter,y=void 0===m?this._onRenderGroupFooter:m,v=e.onShouldVirtualize,x=e.groupedListClassNames,b=e.groups,_=e.compact,k=e.listProps,w=void 0===k?{}:k,C=this.state.isSelected,S=n&&t?t(n):1/0,E=n&&!n.children&&!n.isCollapsed&&!n.isShowingAll&&(n.count>S||n.hasMoreData),T=n&&n.children&&n.children.length>0,D=w.version,R={group:n,groupIndex:o,groupLevel:n?n.level:0,isSelected:C,selected:C,viewport:c,selectionMode:d,groups:b,compact:_},P={groupedListId:this._id,ariaLevel:(null===n||void 0===n?void 0:n.level)?n.level+1:1,ariaSetSize:b?b.length:void 0,ariaPosInSet:void 0!==o?o+1:void 0},I=(0,i.pi)((0,i.pi)((0,i.pi)({},r),R),P),M=(0,i.pi)((0,i.pi)({},s),R),O=(0,i.pi)((0,i.pi)({},l),R),L=!!this.props.dragDropHelper&&this._getGroupDragDropOptions().canDrag(n)&&!!this.props.dragDropEvents.canDragGroups;return a.createElement("div",(0,i.pi)({ref:this._root},L&&{draggable:!0},{className:(0,u.i)(x&&x.group,this._getDroppingClassName()),role:"presentation"}),f(I,this._onRenderGroupHeader),n&&n.isCollapsed?null:T?a.createElement(dt,{role:"presentation",ref:this._list,items:n?n.children:[],onRenderCell:this._renderSubGroup,getItemCountForPage:this._returnOne,onShouldVirtualize:v,version:D,id:this._id}):this._onRenderGroup(S),n&&n.isCollapsed?null:E&&g(M,this._onRenderGroupShowAll),y(O,this._onRenderGroupFooter))},t.prototype.forceUpdate=function(){e.prototype.forceUpdate.call(this),this.forceListUpdate()},t.prototype.forceListUpdate=function(){var e=this.props.group;if(this._list.current){if(this._list.current.forceUpdate(),e&&e.children&&e.children.length>0)for(var t=e.children.length,n=0;n<t;n++){var o;(o=this._list.current.pageRefs["subGroup_"+String(n)])&&o.forceListUpdate()}}else(o=this._subGroupRefs["subGroup_"+String(0)])&&o.forceListUpdate()},t.prototype._onSelectionChange=function(){var e=this.props,t=e.group,n=e.selection;if(n&&t){var o=n.isRangeSelected(t.startIndex,t.count);o!==this.state.isSelected&&this.setState({isSelected:o})}},t.prototype._onRenderGroupCell=function(e,t,n){return function(o,r){return e(t,o,r,n)}},t.prototype._onRenderGroup=function(e){var t=this.props,n=t.group,o=t.items,r=t.onRenderCell,s=t.listProps,l=t.groupNestingDepth,c=t.onShouldVirtualize,d=t.groupProps,u=n&&!n.isShowingAll?n.count:o.length,p=n?n.startIndex:0;return a.createElement(dt,(0,i.pi)({role:d&&d.role?d.role:"rowgroup","aria-label":null===n||void 0===n?void 0:n.name,items:o,onRenderCell:this._onRenderGroupCell(r,l,n),ref:this._list,renderCount:Math.min(u,e),startIndex:p,onShouldVirtualize:c,id:this._id},s))},t.prototype._returnOne=function(){return 1},t.prototype._getGroupKey=function(e,t){return"group-"+(e&&e.key?e.key:String(e.level)+String(t))},t.prototype._getDroppingClassName=function(){var e=this.state.isDropping,t=this.props,n=t.group,o=t.groupedListClassNames;return e=!(!n||!e),(0,u.i)(e&&this._droppingClassName,e&&"is-dropping",e&&o&&o.groupIsDropping)},t}(a.Component),ft=(0,s.y)(),ht=O.lv.rowHeight,gt=O.lv.compactRowHeight,mt=function(e){function t(t){var n=e.call(this,t)||this;n._list=a.createRef(),n._renderGroup=function(e,t){var o=n.props,r=o.dragDropEvents,s=o.dragDropHelper,l=o.eventsToRegister,c=o.groupProps,d=o.items,u=o.listProps,p=o.onRenderCell,f=o.selectionMode,h=o.selection,g=o.viewport,m=o.onShouldVirtualize,y=o.groups,v=o.compact,x={onToggleSelectGroup:n._onToggleSelectGroup,onToggleCollapse:n._onToggleCollapse,onToggleSummarize:n._onToggleSummarize},b=(0,i.pi)((0,i.pi)({},c.headerProps),x),_=(0,i.pi)((0,i.pi)({},c.showAllProps),x),k=(0,i.pi)((0,i.pi)({},c.footerProps),x),w=n._getGroupNestingDepth();if(!c.showEmptyGroups&&e&&0===e.count)return null;var C=(0,i.pi)((0,i.pi)({},u||{}),{version:n.state.version});return a.createElement(pt,{key:n._getGroupKey(e,t),dragDropEvents:r,dragDropHelper:s,eventsToRegister:l,footerProps:k,getGroupItemLimit:c&&c.getGroupItemLimit,group:e,groupIndex:t,groupNestingDepth:w,groupProps:c,headerProps:b,listProps:C,items:d,onRenderCell:p,onRenderGroupHeader:c.onRenderHeader,onRenderGroupShowAll:c.onRenderShowAll,onRenderGroupFooter:c.onRenderFooter,selectionMode:f,selection:h,showAllProps:_,viewport:g,onShouldVirtualize:m,groupedListClassNames:n._classNames,groups:y,compact:v})},n._getDefaultGroupItemLimit=function(e){return e.children&&e.children.length>0?e.children.length:e.count},n._getGroupItemLimit=function(e){var t=n.props.groupProps;return(t&&t.getGroupItemLimit?t.getGroupItemLimit:n._getDefaultGroupItemLimit)(e)},n._getGroupHeight=function(e){var t=n.props.compact?gt:ht;return t+(e.isCollapsed?0:t*n._getGroupItemLimit(e))},n._getPageHeight=function(e){var t=n.state.groups,o=n.props.getGroupHeight,r=void 0===o?n._getGroupHeight:o,i=t&&t[e];return i?r(i,e):0},n._onToggleCollapse=function(e){var t=n.props.groupProps,o=t&&t.headerProps&&t.headerProps.onToggleCollapse;e&&(o&&o(e),e.isCollapsed=!e.isCollapsed,n._updateIsSomeGroupExpanded(),n.forceUpdate())},n._onToggleSelectGroup=function(e){var t=n.props,o=t.selection,r=t.selectionMode;e&&o&&r===H.oW.multiple&&o.toggleRangeSelected(e.startIndex,e.count)},n._isInnerZoneKeystroke=function(e){return e.which===(0,c.dP)(d.m.right)},n._onToggleSummarize=function(e){var t=n.props.groupProps,o=t&&t.showAllProps&&t.showAllProps.onToggleSummarize;o?o(e):(e&&(e.isShowingAll=!e.isShowingAll),n.forceUpdate())},n._getPageSpecification=function(e){var t=n.state.groups,o=t&&t[e];return{key:o&&o.key}},(0,f.l)(n),n._isSomeGroupExpanded=n._computeIsSomeGroupExpanded(t.groups);var o=t.listProps,r=(void 0===o?{}:o).version,s=void 0===r?{}:r;return n.state={groups:t.groups,items:t.items,listProps:t.listProps,version:s},n}return(0,i.ZT)(t,e),t.getDerivedStateFromProps=function(e,t){var n=e.groups,o=e.selectionMode,r=e.compact,a=e.items,s=e.listProps,l=s&&s.version,c=(0,i.pi)((0,i.pi)({},t),{selectionMode:o,compact:r,groups:n,listProps:s,items:a}),d=!1;return l===(t.listProps&&t.listProps.version)&&a===t.items&&n===t.groups&&o===t.selectionMode&&r===t.compact||(d=!0),d&&(c=(0,i.pi)((0,i.pi)({},c),{version:{}})),c},t.prototype.scrollToIndex=function(e,t,n){this._list.current&&this._list.current.scrollToIndex(e,t,n)},t.prototype.getStartItemIndexInView=function(){return this._list.current.getStartItemIndexInView()||0},t.prototype.componentDidMount=function(){var e=this.props,t=e.groupProps,n=e.groups,o=void 0===n?[]:n;t&&t.isAllGroupsCollapsed&&this._setGroupsCollapsedState(o,t.isAllGroupsCollapsed)},t.prototype.render=function(){var e=this.props,t=e.className,n=e.usePageCache,o=e.onShouldVirtualize,r=e.theme,s=e.role,l=void 0===s?"treegrid":s,c=e.styles,d=e.compact,p=e.focusZoneProps,f=void 0===p?{}:p,h=e.rootListProps,g=void 0===h?{}:h,m=this.state,y=m.groups,v=m.version;this._classNames=ft(c,{theme:r,className:t,compact:d});var x=f.shouldEnterInnerZone,k=void 0===x?this._isInnerZoneKeystroke:x;return a.createElement(b.k,(0,i.pi)({direction:_.U.vertical,"data-automationid":"GroupedList","data-is-scrollable":"false",role:"presentation"},f,{shouldEnterInnerZone:k,className:(0,u.i)(this._classNames.root,f.className)}),y?a.createElement(dt,(0,i.pi)({ref:this._list,role:l,items:y,onRenderCell:this._renderGroup,getItemCountForPage:this._returnOne,getPageHeight:this._getPageHeight,getPageSpecification:this._getPageSpecification,usePageCache:n,onShouldVirtualize:o,version:v},g)):this._renderGroup(void 0,0))},t.prototype.forceUpdate=function(){e.prototype.forceUpdate.call(this),this._forceListUpdates()},t.prototype.toggleCollapseAll=function(e){var t=this.state.groups,n=void 0===t?[]:t,o=this.props.groupProps,r=o&&o.onToggleCollapseAll;n.length>0&&(r&&r(e),this._setGroupsCollapsedState(n,e),this._updateIsSomeGroupExpanded(),this.forceUpdate())},t.prototype._setGroupsCollapsedState=function(e,t){for(var n=0;n<e.length;n++)e[n].isCollapsed=t},t.prototype._returnOne=function(){return 1},t.prototype._getGroupKey=function(e,t){return"group-"+(e&&e.key?e.key:String(t))},t.prototype._getGroupNestingDepth=function(){for(var e=0,t=this.state.groups;t&&t.length>0;)e++,t=t[0].children;return e},t.prototype._forceListUpdates=function(e){this.setState({version:{}})},t.prototype._computeIsSomeGroupExpanded=function(e){var t=this;return!(!e||!e.some((function(e){return e.children?t._computeIsSomeGroupExpanded(e.children):!e.isCollapsed})))},t.prototype._updateIsSomeGroupExpanded=function(){var e=this.state.groups,t=this.props.onGroupExpandStateChanged,n=this._computeIsSomeGroupExpanded(e);this._isSomeGroupExpanded!==n&&(t&&t(n),this._isSomeGroupExpanded=n)},t.defaultProps={selectionMode:H.oW.multiple,isHeaderVisible:!0,groupProps:{},compact:!1},t}(a.Component),yt=(0,r.z)(mt,(function(e){var t,n,o=e.theme,r=e.className,i=e.compact,a=o.palette,s=(0,P.Cn)(Te,o);return{root:[s.root,o.fonts.small,{position:"relative",selectors:(t={},t[".".concat(s.listCell)]={minHeight:38},t)},i&&[s.compact,{selectors:(n={},n[".".concat(s.listCell)]={minHeight:32},n)}],r],group:[s.group,{transition:"background-color ".concat(P.D1.durationValue2," ").concat(De)}],groupIsDropping:{backgroundColor:a.neutralLight}}}),void 0,{scope:"GroupedList"}),vt=["setState","render","componentWillMount","UNSAFE_componentWillMount","componentDidMount","componentWillReceiveProps","UNSAFE_componentWillReceiveProps","shouldComponentUpdate","componentWillUpdate","getSnapshotBeforeUpdate","UNSAFE_componentWillUpdate","componentDidUpdate","componentWillUnmount"];var xt=function(e){function t(t){var n=e.call(this,t)||this;return n._updateComposedComponentRef=n._updateComposedComponentRef.bind(n),n}return(0,i.ZT)(t,e),t.prototype._updateComposedComponentRef=function(e){var t;this._composedComponentInstance=e,e?this._hoisted=function(e,t,n){void 0===n&&(n=vt);var o=[],r=function(r){"function"!==typeof t[r]||void 0!==e[r]||n&&-1!==n.indexOf(r)||(o.push(r),e[r]=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];t[r].apply(t,e)})};for(var i in t)r(i);return o}(this,e):this._hoisted&&(t=this,this._hoisted.forEach((function(e){return delete t[e]})))},t}(a.Component);function bt(e){var t;return e&&(e===window?t={left:0,top:0,width:window.innerWidth,height:window.innerHeight,right:window.innerWidth,bottom:window.innerHeight}:e.getBoundingClientRect&&(t=e.getBoundingClientRect())),t}function _t(e){return function(t){function n(e){var n=t.call(this,e)||this;return n._root=a.createRef(),n._registerResizeObserver=function(){var e=(0,ye.J)(n._root.current);n._viewportResizeObserver=new e.ResizeObserver(n._onAsyncResize),n._viewportResizeObserver.observe(n._root.current)},n._unregisterResizeObserver=function(){n._viewportResizeObserver&&(n._viewportResizeObserver.disconnect(),delete n._viewportResizeObserver)},n._updateViewport=function(e){var t=n.state.viewport,o=n._root.current,r=bt((0,ve.zj)(o)),i=bt(o);((i&&i.width)!==t.width||(r&&r.height)!==t.height)&&n._resizeAttempts<3&&i&&r?(n._resizeAttempts++,n.setState({viewport:{width:i.width,height:r.height}},(function(){n._updateViewport(e)}))):(n._resizeAttempts=0,e&&n._composedComponentInstance&&n._composedComponentInstance.forceUpdate())},n._async=new h.e(n),n._events=new v.r(n),n._resizeAttempts=0,n.state={viewport:{width:0,height:0}},n}return(0,i.ZT)(n,t),n.prototype.componentDidMount=function(){var e=this,t=this.props,n=t.delayFirstMeasure,o=t.disableResizeObserver,r=t.skipViewportMeasures,i=(0,ye.J)(this._root.current);this._onAsyncResize=this._async.debounce(this._onAsyncResize,500,{leading:!1}),r||(!o&&this._isResizeObserverAvailable()?this._registerResizeObserver():this._events.on(i,"resize",this._onAsyncResize),n?this._async.setTimeout((function(){e._updateViewport()}),500):this._updateViewport())},n.prototype.componentDidUpdate=function(e){var t=e.skipViewportMeasures,n=this.props,o=n.disableResizeObserver,r=n.skipViewportMeasures,i=(0,ye.J)(this._root.current);r!==t&&(r?(this._unregisterResizeObserver(),this._events.off(i,"resize",this._onAsyncResize)):(!o&&this._isResizeObserverAvailable()?this._viewportResizeObserver||this._registerResizeObserver():this._events.on(i,"resize",this._onAsyncResize),this._updateViewport()))},n.prototype.componentWillUnmount=function(){this._events.dispose(),this._async.dispose(),this._unregisterResizeObserver()},n.prototype.render=function(){var t=this.state.viewport,n=t.width>0&&t.height>0?t:void 0;return a.createElement("div",{className:"ms-Viewport",ref:this._root,style:{minWidth:1,minHeight:1}},a.createElement(e,(0,i.pi)({ref:this._updateComposedComponentRef,viewport:n},this.props)))},n.prototype.forceUpdate=function(){this._updateViewport(!0)},n.prototype._onAsyncResize=function(){this._updateViewport()},n.prototype._isResizeObserverAvailable=function(){var e=(0,ye.J)(this._root.current);return e&&e.ResizeObserver},n}(xt)}var kt=n(32561),wt=(0,s.y)(),Ct=100,St={tabIndex:0},Et={},Tt=function(e){var t=e.selection,n=e.ariaLabelForListHeader,o=e.ariaLabelForSelectAllCheckbox,r=e.ariaLabelForSelectionColumn,s=e.className,f=e.checkboxVisibility,h=e.compact,g=e.constrainMode,m=e.dragDropEvents,v=e.groups,k=e.groupProps,w=e.indentWidth,C=e.items,S=e.isPlaceholderData,T=e.isHeaderVisible,D=e.layoutMode,R=e.onItemInvoked,P=e.onItemContextMenu,I=e.onColumnHeaderClick,M=e.onColumnHeaderContextMenu,L=e.selectionMode,A=void 0===L?t.mode:L,z=e.selectionPreservedOnEmptyClick,N=e.selectionZoneProps,F=e.ariaLabel,B=e.ariaLabelForGrid,j=e.rowElementEventMap,W=e.shouldApplyApplicationRole,V=void 0!==W&&W,G=e.getKey,q=e.listProps,K=e.usePageCache,Y=e.onShouldVirtualize,X=e.viewport,Z=e.minimumPixelsForDrag,J=e.getGroupHeight,$=e.styles,Q=e.theme,ee=e.cellStyleProps,te=void 0===ee?O.Wt:ee,ne=e.onRenderCheckbox,oe=e.useFastIcons,re=e.dragDropHelper,ae=e.adjustedColumns,le=e.isCollapsed,ce=e.isSizing,de=e.isSomeGroupExpanded,ue=e.version,pe=e.rootRef,fe=e.listRef,he=e.focusZoneRef,ge=e.columnReorderOptions,me=e.groupedListRef,ye=e.headerRef,ve=e.onGroupExpandStateChanged,xe=e.onColumnIsSizingChanged,be=e.onRowDidMount,_e=e.onRowWillUnmount,ke=e.disableSelectionZone,we=e.isSelectedOnFocus,Se=void 0===we||we,Ee=e.onColumnResized,Te=e.onColumnAutoResized,De=e.onToggleCollapse,Re=e.onActiveRowChanged,Pe=e.onBlur,Ie=e.rowElementEventMap,Me=e.onRenderMissingItem,Oe=e.onRenderItemColumn,Le=e.onRenderField,Ae=e.getCellValueKey,ze=e.getRowAriaLabel,Ne=e.getRowAriaDescribedBy,He=e.checkButtonAriaLabel,Fe=e.checkButtonGroupAriaLabel,Be=e.checkboxCellClassName,je=e.useReducedRowRenderer,We=e.enableUpdateAnimations,Ue=e.enterModalSelectionOnTouch,Ve=e.onRenderDefaultRow,Ge=e.selectionZoneRef,qe=e.focusZoneProps,Ke="grid",Ye=e.role?e.role:Ke,Xe=(0,x.z)("row"),Ze=function(e){var t=0,n=e;for(;n&&n.length>0;)t++,n=n[0].children;return t}(v),Je=function(e){return a.useMemo((function(){var t={};if(e)for(var n=1,o=1,r=0,i=e;r<i.length;r++){var a=i[r];t[a.key]={numOfGroupHeadersBeforeItem:o,totalRowCount:n},o++,n+=a.count+1}return t}),[e])}(v),$e=a.useMemo((function(){return(0,i.pi)({renderedWindowsAhead:ce?0:2,renderedWindowsBehind:ce?0:2,getKey:G,version:ue},q)}),[ce,G,ue,q]),Qe=U.none;if(A===H.oW.single&&(Qe=U.hidden),A===H.oW.multiple){var et=k&&k.headerProps&&k.headerProps.isCollapsedGroupSelectVisible;void 0===et&&(et=!0),Qe=et||!v||de?U.visible:U.hidden}f===y.tY.hidden&&(Qe=U.none);var tt=a.useCallback((function(e){return a.createElement(ie,(0,i.pi)({},e))}),[]),nt=a.useCallback((function(){return null}),[]),ot=e.onRenderDetailsHeader,rt=a.useMemo((function(){return ot?(0,E.k)(ot,tt):tt}),[ot,tt]),it=e.onRenderDetailsFooter,at=a.useMemo((function(){return it?(0,E.k)(it,nt):nt}),[it,nt]),st=a.useMemo((function(){return{columns:ae,groupNestingDepth:Ze,selection:t,selectionMode:A,viewport:X,checkboxVisibility:f,indentWidth:w,cellStyleProps:te}}),[ae,Ze,t,A,X,f,w,te]),lt=ge&&ge.onDragEnd,ct=a.useCallback((function(e,t){var n=e.dropLocation,o=y.fQ.outside;if(lt){if(n&&n!==y.fQ.header)o=n;else if(pe.current){var r=pe.current.getBoundingClientRect();t.clientX>r.left&&t.clientX<r.right&&t.clientY>r.top&&t.clientY<r.bottom&&(o=y.fQ.surface)}lt(o)}}),[lt,pe]),ut=a.useMemo((function(){if(ge)return(0,i.pi)((0,i.pi)({},ge),{onColumnDragEnd:ct})}),[ge,ct]),pt=(T?1:0)+(e.onRenderDetailsFooter?1:0)+function(e){var t=0;if(e)for(var n=(0,i.ev)([],e,!0),o=void 0;n&&n.length>0;)++t,(o=n.pop())&&o.children&&n.push.apply(n,o.children);return t}(v)+(C?C.length:0),ft=(Qe!==U.none?1:0)+(ae?ae.length:0)+(v?1:0),ht=a.useMemo((function(){return wt($,{theme:Q,compact:h,isFixed:D===y.Oh.fixedColumns,isHorizontalConstrained:g===y.ov.horizontalConstrained,className:s})}),[$,Q,h,D,g,s]),gt=k&&k.onRenderFooter,mt=a.useMemo((function(){return gt?function(e,n){return gt((0,i.pi)((0,i.pi)({},e),{columns:ae,groupNestingDepth:Ze,indentWidth:w,selection:t,selectionMode:A,viewport:X,checkboxVisibility:f,cellStyleProps:te}),n)}:void 0}),[gt,ae,Ze,w,t,A,X,f,te]),vt=k&&k.onRenderHeader,xt=a.useMemo((function(){return vt?function(e,n){var o,r,a=e.groupIndex,s=void 0!==a?null===(r=null===(o=e.groups)||void 0===o?void 0:o[a])||void 0===r?void 0:r.key:void 0,l=void 0!==s&&Je[s]?Je[s].totalRowCount:0;return vt((0,i.pi)((0,i.pi)({},e),{columns:ae,groupNestingDepth:Ze,indentWidth:w,selection:t,selectionMode:f!==y.tY.hidden?A:H.oW.none,viewport:X,checkboxVisibility:f,cellStyleProps:te,ariaColSpan:ae.length,ariaLevel:void 0,ariaPosInSet:void 0,ariaSetSize:void 0,ariaRowCount:void 0,ariaRowIndex:void 0!==a?l+(T?1:0):void 0}),n)}:function(e,t){var n,o,r=e.groupIndex,a=void 0!==r?null===(o=null===(n=e.groups)||void 0===n?void 0:n[r])||void 0===o?void 0:o.key:void 0,s=void 0!==a&&Je[a]?Je[a].totalRowCount:0;return t((0,i.pi)((0,i.pi)({},e),{ariaColSpan:ae.length,ariaLevel:void 0,ariaPosInSet:void 0,ariaSetSize:void 0,ariaRowCount:void 0,ariaRowIndex:void 0!==r?s+(T?1:0):void 0}))}}),[vt,ae,Ze,w,T,t,A,X,f,te,Je]),bt=a.useMemo((function(){var e;return(0,i.pi)((0,i.pi)({},k),{role:Ye===Ke?"rowgroup":"presentation",onRenderFooter:mt,onRenderHeader:xt,headerProps:(0,i.pi)((0,i.pi)({},null===k||void 0===k?void 0:k.headerProps),{selectAllButtonProps:(0,i.pi)({"aria-label":Fe},null===(e=null===k||void 0===k?void 0:k.headerProps)||void 0===e?void 0:e.selectAllButtonProps)})})}),[k,mt,xt,Fe,Ye]),_t=(0,kt.B)((function(){return(0,l.NF)((function(e){var t=0;return e.forEach((function(e){return t+=e.calculatedWidth||e.minWidth})),t}))})),Ct=k&&k.collapseAllVisibility,Tt=a.useMemo((function(){return _t(ae)}),[ae,_t]),Dt=a.useCallback((function(n,o,r,i){var a=e.onRenderRow?(0,E.k)(e.onRenderRow,Ve):Ve,s=i?i.key:void 0,l=s&&Je[s]?Je[s].numOfGroupHeadersBeforeItem:0,c=Ye===Ke?void 0:"presentation",d=T||r>0?Et:St,u={item:o,itemIndex:r,flatIndexOffset:(T?2:1)+l,compact:h,columns:ae,groupNestingDepth:n,id:"".concat(Xe,"-").concat(r),selectionMode:A,selection:t,onDidMount:be,onWillUnmount:_e,onRenderItemColumn:Oe,onRenderField:Le,getCellValueKey:Ae,eventsToRegister:Ie,dragDropEvents:m,dragDropHelper:re,viewport:X,checkboxVisibility:f,collapseAllVisibility:Ct,getRowAriaLabel:ze,getRowAriaDescribedBy:Ne,checkButtonAriaLabel:He,checkboxCellClassName:Be,useReducedRowRenderer:je,indentWidth:w,cellStyleProps:te,onRenderDetailsCheckbox:ne,enableUpdateAnimations:We,rowWidth:Tt,useFastIcons:oe,role:c,isGridRow:!0,focusZoneProps:d};return o?a(u):Me?Me(r,u):null}),[h,ae,A,t,Xe,be,_e,Oe,Le,Ae,Ie,m,re,X,f,Ct,ze,Ne,T,He,Be,je,w,te,ne,We,oe,Ve,Me,e.onRenderRow,Tt,Ye,Je]),Rt=a.useCallback((function(e){return function(t,n){return Dt(e,t,n)}}),[Dt]),Pt=a.useCallback((function(e){return e.which===(0,c.dP)(d.m.right,Q)}),[Q]),It=(0,i.pi)((0,i.pi)({},qe),{componentRef:qe&&qe.componentRef?qe.componentRef:he,className:qe&&qe.className?(0,u.i)(ht.focusZone,qe.className):ht.focusZone,direction:qe?qe.direction:_.U.vertical,shouldEnterInnerZone:qe&&qe.shouldEnterInnerZone?qe.shouldEnterInnerZone:Pt,onActiveElementChanged:qe&&qe.onActiveElementChanged?qe.onActiveElementChanged:Re,shouldRaiseClicksOnEnter:!1,onBlur:qe&&qe.onBlur?qe.onBlur:Pe}),Mt=v&&(null===k||void 0===k?void 0:k.groupedListAs)?(0,se.Z)(k.groupedListAs,yt):yt,Ot=v?a.createElement(Mt,{focusZoneProps:It,componentRef:me,groups:v,groupProps:bt,items:C,onRenderCell:Dt,role:"presentation",selection:t,selectionMode:f!==y.tY.hidden?A:H.oW.none,dragDropEvents:m,dragDropHelper:re,eventsToRegister:j,listProps:$e,onGroupExpandStateChanged:ve,usePageCache:K,onShouldVirtualize:Y,getGroupHeight:J,compact:h}):a.createElement(b.k,(0,i.pi)({},It),a.createElement(dt,(0,i.pi)({ref:fe,role:"presentation",items:C,onRenderCell:Rt(0),usePageCache:K,onShouldVirtualize:Y},$e))),Lt=a.useCallback((function(e){e.which===d.m.down&&he.current&&he.current.focus()&&(Se&&0===t.getSelectedIndices().length&&t.setIndexSelected(0,!0,!1),e.preventDefault(),e.stopPropagation())}),[t,he,Se]),At=a.useCallback((function(e){e.which!==d.m.up||e.altKey||ye.current&&ye.current.focus()&&(e.preventDefault(),e.stopPropagation())}),[ye]);return a.createElement("div",(0,i.pi)({ref:pe,className:ht.root,"data-automationid":"DetailsList","data-is-scrollable":"false"},V?{role:"application"}:{}),a.createElement(p.u,null),a.createElement("div",{role:Ye,"aria-label":B||F,"aria-rowcount":S?0:pt,"aria-colcount":ft,"aria-busy":S},a.createElement("div",{onKeyDown:Lt,role:"presentation",className:ht.headerWrapper},T&&rt({componentRef:ye,selectionMode:A,layoutMode:D,selection:t,columns:ae,onColumnClick:I,onColumnContextMenu:M,onColumnResized:Ee,onColumnIsSizingChanged:xe,onColumnAutoResized:Te,groupNestingDepth:Ze,isAllCollapsed:le,onToggleCollapseAll:De,ariaLabel:n,ariaLabelForSelectAllCheckbox:o,ariaLabelForSelectionColumn:r,selectAllVisibility:Qe,collapseAllVisibility:k&&k.collapseAllVisibility,viewport:X,columnReorderProps:ut,minimumPixelsForDrag:Z,cellStyleProps:te,checkboxVisibility:f,indentWidth:w,onRenderDetailsCheckbox:ne,rowWidth:_t(ae),useFastIcons:oe},rt)),a.createElement("div",{onKeyDown:At,role:"presentation",className:ht.contentWrapper},ke?Ot:a.createElement(Ce,(0,i.pi)({ref:Ge,selection:t,selectionPreservedOnEmptyClick:z,selectionMode:A,isSelectedOnFocus:Se,selectionClearedOnEscapePress:Se,toggleWithoutModifierPressed:!Se,onItemInvoked:R,onItemContextMenu:P,enterModalOnTouch:Ue},N||{}),Ot)),at((0,i.pi)({},st))))},Dt=function(e){function t(t){var n=e.call(this,t)||this;return n._root=a.createRef(),n._header=a.createRef(),n._groupedList=a.createRef(),n._list=a.createRef(),n._focusZone=a.createRef(),n._selectionZone=a.createRef(),n._onRenderRow=function(e,t){return a.createElement(ge,(0,i.pi)({},e))},n._getDerivedStateFromProps=function(e,t){var o=n.props,r=o.checkboxVisibility,a=o.items,s=o.setKey,l=o.selectionMode,c=void 0===l?n._selection.mode:l,d=o.columns,u=o.viewport,p=o.compact,f=o.dragDropEvents,h=(n.props.groupProps||{}).isAllGroupsCollapsed,g=void 0===h?void 0:h,m=e.viewport&&e.viewport.width||0,y=u&&u.width||0,v=e.setKey!==s||void 0===e.setKey,x=!1;e.layoutMode!==n.props.layoutMode&&(x=!0);var b=t;return v&&(n._initialFocusedIndex=e.initialFocusedIndex,b=(0,i.pi)((0,i.pi)({},b),{focusedItemIndex:void 0!==n._initialFocusedIndex?n._initialFocusedIndex:-1})),n.props.disableSelectionZone||e.items===a||n._selection.setItems(e.items,v),e.checkboxVisibility===r&&e.columns===d&&m===y&&e.compact===p||(x=!0),b=(0,i.pi)((0,i.pi)({},b),n._adjustColumns(e,b,!0)),e.selectionMode!==c&&(x=!0),void 0===g&&e.groupProps&&void 0!==e.groupProps.isAllGroupsCollapsed&&(b=(0,i.pi)((0,i.pi)({},b),{isCollapsed:e.groupProps.isAllGroupsCollapsed,isSomeGroupExpanded:!e.groupProps.isAllGroupsCollapsed})),e.dragDropEvents!==f&&(n._dragDropHelper&&n._dragDropHelper.dispose(),n._dragDropHelper=e.dragDropEvents?new q({selection:n._selection,minimumPixelsForDrag:e.minimumPixelsForDrag}):void 0,x=!0),x&&(b=(0,i.pi)((0,i.pi)({},b),{version:{}})),b},n._onGroupExpandStateChanged=function(e){n.setState({isSomeGroupExpanded:e})},n._onColumnIsSizingChanged=function(e,t){n.setState({isSizing:t})},n._onRowDidMount=function(e){var t=e.props,o=t.item,r=t.itemIndex,i=n._getItemKey(o,r);n._activeRows[i]=e,n._setFocusToRowIfPending(e);var a=n.props.onRowDidMount;a&&a(o,r)},n._onRowWillUnmount=function(e){var t=n.props.onRowWillUnmount,o=e.props,r=o.item,i=o.itemIndex,a=n._getItemKey(r,i);delete n._activeRows[a],t&&t(r,i)},n._onToggleCollapse=function(e){n.setState({isCollapsed:e}),n._groupedList.current&&n._groupedList.current.toggleCollapseAll(e)},n._onColumnResized=function(e,t,o){var r=Math.max(e.minWidth||Ct,t);n.props.onColumnResize&&n.props.onColumnResize(e,r,o),n._rememberCalculatedWidth(e,r),n.setState((0,i.pi)((0,i.pi)({},n._adjustColumns(n.props,n.state,!0,o)),{version:{}}))},n._onColumnAutoResized=function(e,t){var o=0,r=0,i=Object.keys(n._activeRows).length;for(var a in n._activeRows){if(n._activeRows.hasOwnProperty(a))n._activeRows[a].measureCell(t,(function(a){o=Math.max(o,a),++r===i&&n._onColumnResized(e,o,t)}))}},n._onActiveRowChanged=function(e,t){var o=n.props,r=o.items,i=o.onActiveItemChanged;if(e&&e.getAttribute("data-item-index")){var a=Number(e.getAttribute("data-item-index"));a>=0&&(i&&i(r[a],a,t),n.setState({focusedItemIndex:a}))}},n._onBlur=function(e){n.setState({focusedItemIndex:-1})},(0,f.l)(n),n._async=new h.e(n),n._activeRows={},n._columnOverrides={},n.state={focusedItemIndex:-1,lastWidth:0,adjustedColumns:n._getAdjustedColumns(t,void 0),isSizing:!1,isCollapsed:t.groupProps&&t.groupProps.isAllGroupsCollapsed,isSomeGroupExpanded:t.groupProps&&!t.groupProps.isAllGroupsCollapsed,version:{},getDerivedStateFromProps:n._getDerivedStateFromProps},(0,g.L)("DetailsList",t,{selection:"getKey"}),n._selection=t.selection||new Se({onSelectionChanged:void 0,getKey:t.getKey,selectionMode:t.selectionMode}),n.props.disableSelectionZone||n._selection.setItems(t.items,!1),n._dragDropHelper=t.dragDropEvents?new q({selection:n._selection,minimumPixelsForDrag:t.minimumPixelsForDrag}):void 0,n._initialFocusedIndex=t.initialFocusedIndex,n}return(0,i.ZT)(t,e),t.getDerivedStateFromProps=function(e,t){return t.getDerivedStateFromProps(e,t)},t.prototype.scrollToIndex=function(e,t,n){this._list.current&&this._list.current.scrollToIndex(e,t,n),this._groupedList.current&&this._groupedList.current.scrollToIndex(e,t,n)},t.prototype.focusIndex=function(e,t,n,o){void 0===t&&(t=!1);var r=this.props.items[e];if(r){this.scrollToIndex(e,n,o);var i=this._getItemKey(r,e),a=this._activeRows[i];a&&this._setFocusToRow(a,t)}},t.prototype.getStartItemIndexInView=function(){return this._list&&this._list.current?this._list.current.getStartItemIndexInView():this._groupedList&&this._groupedList.current?this._groupedList.current.getStartItemIndexInView():0},t.prototype.updateColumn=function(e,t){var n,o,r=this.props,i=r.columns,a=void 0===i?[]:i,s=r.selectionMode,l=r.checkboxVisibility,c=r.columnReorderOptions,d=t.width,u=t.newColumnIndex,p=a.findIndex((function(t){return t.key===e.key}));if(d&&this._onColumnResized(e,d,p),void 0!==u&&c){var f=s===H.oW.none||l===y.tY.hidden,h=(l!==y.tY.hidden?2:1)+p,g=f?h-1:h-2,m=f?u-1:u-2,v=null!==(n=c.frozenColumnCountFromStart)&&void 0!==n?n:0,x=null!==(o=c.frozenColumnCountFromEnd)&&void 0!==o?o:0;if(m>=v&&m<a.length-x)if(c.onColumnDrop){var b={draggedIndex:g,targetIndex:m};c.onColumnDrop(b)}else c.handleColumnReorder&&c.handleColumnReorder(g,m)}},t.prototype.componentWillUnmount=function(){this._dragDropHelper&&this._dragDropHelper.dispose(),this._async.dispose()},t.prototype.componentDidUpdate=function(e,t){if((this._notifyColumnsResized(),void 0!==this._initialFocusedIndex)&&(i=this.props.items[this._initialFocusedIndex])){var n=this._getItemKey(i,this._initialFocusedIndex);(o=this._activeRows[n])&&this._setFocusToRowIfPending(o)}if(this.props.items!==e.items&&this.props.items.length>0&&-1!==this.state.focusedItemIndex&&!(0,m.t)(this._root.current,document.activeElement,!1)){var o,r=this.state.focusedItemIndex<this.props.items.length?this.state.focusedItemIndex:this.props.items.length-1,i=this.props.items[r];n=this._getItemKey(i,this.state.focusedItemIndex);(o=this._activeRows[n])?this._setFocusToRow(o):this._initialFocusedIndex=r}this.props.onDidUpdate&&this.props.onDidUpdate(this)},t.prototype.render=function(){return a.createElement(Tt,(0,i.pi)({},this.props,this.state,{selection:this._selection,dragDropHelper:this._dragDropHelper,rootRef:this._root,listRef:this._list,groupedListRef:this._groupedList,focusZoneRef:this._focusZone,headerRef:this._header,selectionZoneRef:this._selectionZone,onGroupExpandStateChanged:this._onGroupExpandStateChanged,onColumnIsSizingChanged:this._onColumnIsSizingChanged,onRowDidMount:this._onRowDidMount,onRowWillUnmount:this._onRowWillUnmount,onColumnResized:this._onColumnResized,onColumnAutoResized:this._onColumnAutoResized,onToggleCollapse:this._onToggleCollapse,onActiveRowChanged:this._onActiveRowChanged,onBlur:this._onBlur,onRenderDefaultRow:this._onRenderRow}))},t.prototype.forceUpdate=function(){e.prototype.forceUpdate.call(this),this._forceListUpdates()},t.prototype._getGroupNestingDepth=function(){for(var e=0,t=this.props.groups;t&&t.length>0;)e++,t=t[0].children;return e},t.prototype._setFocusToRowIfPending=function(e){var t=e.props.itemIndex;void 0!==this._initialFocusedIndex&&t===this._initialFocusedIndex&&(this._setFocusToRow(e),delete this._initialFocusedIndex)},t.prototype._setFocusToRow=function(e,t){void 0===t&&(t=!1),this._selectionZone.current&&this._selectionZone.current.ignoreNextFocus(),this._async.setTimeout((function(){e.focus(t)}),0)},t.prototype._forceListUpdates=function(){this._groupedList.current&&this._groupedList.current.forceUpdate(),this._list.current&&this._list.current.forceUpdate()},t.prototype._notifyColumnsResized=function(){this.state.adjustedColumns.forEach((function(e){e.onColumnResize&&e.onColumnResize(e.currentWidth)}))},t.prototype._adjustColumns=function(e,t,n,o){var r=this._getAdjustedColumns(e,t,n,o),a=this.props.viewport,s=a&&a.width?a.width:0;return(0,i.pi)((0,i.pi)({},t),{adjustedColumns:r,lastWidth:s})},t.prototype._getAdjustedColumns=function(e,t,n,o){var r,i=this,a=e.items,s=e.layoutMode,l=e.selectionMode,c=e.viewport,d=c&&c.width?c.width:0,u=e.columns,p=this.props?this.props.columns:[],f=t?t.lastWidth:-1,h=t?t.lastSelectionMode:void 0;return n||f!==d||h!==l||p&&u!==p?(u=u||function(e,t,n,o,r,i,a,s){var l=[];if(e&&e.length){var c=e[0];for(var d in c)c.hasOwnProperty(d)&&l.push({key:d,name:d,fieldName:d,minWidth:Ct,maxWidth:300,isCollapsible:!!l.length,isMultiline:void 0!==a&&a,isSorted:o===d,isSortedDescending:!!r,isRowHeader:!1,columnActionsMode:null!==s&&void 0!==s?s:y._1.clickable,isResizable:t,onColumnClick:n,isGrouped:i===d})}return l}(a,!0),s===y.Oh.fixedColumns?(r=this._getFixedColumns(u,d,e)).forEach((function(e){i._rememberCalculatedWidth(e,e.calculatedWidth)})):(r=this._getJustifiedColumns(u,d,e)).forEach((function(e){i._getColumnOverride(e.key).currentWidth=e.calculatedWidth})),r):u||[]},t.prototype._getFixedColumns=function(e,t,n){var o=this,r=this.props,a=r.selectionMode,s=void 0===a?this._selection.mode:a,l=r.checkboxVisibility,c=r.flexMargin,d=r.skipViewportMeasures,u=t-(c||0),p=0;e.forEach((function(e){d||!e.flexGrow?u-=e.maxWidth||e.minWidth||Ct:(u-=e.minWidth||Ct,p+=e.flexGrow),u-=Rt(e,n,!0)}));var f=s!==H.oW.none&&l!==y.tY.hidden?48:0,h=36*this._getGroupNestingDepth(),g=(u-=f+h)/p;return d||e.forEach((function(e){var t=(0,i.pi)((0,i.pi)({},e),o._columnOverrides[e.key]);if(t.flexGrow&&t.maxWidth){var n=t.flexGrow*g+t.minWidth,r=n-t.maxWidth;r>0&&(u+=r,p-=r/(n-t.minWidth)*t.flexGrow)}})),g=u>0?u/p:0,e.map((function(e){var n=(0,i.pi)((0,i.pi)({},e),o._columnOverrides[e.key]);return!d&&n.flexGrow&&u<=0&&0===t||n.calculatedWidth||(!d&&n.flexGrow?(n.calculatedWidth=n.minWidth+n.flexGrow*g,n.calculatedWidth=Math.min(n.calculatedWidth,n.maxWidth||Number.MAX_VALUE)):n.calculatedWidth=n.maxWidth||n.minWidth||Ct),n}))},t.prototype._getJustifiedColumns=function(e,t,n){var o=this,r=n.selectionMode,a=void 0===r?this._selection.mode:r,s=n.checkboxVisibility,l=n.skipViewportMeasures,c=a!==H.oW.none&&s!==y.tY.hidden?48:0,d=36*this._getGroupNestingDepth(),u=0,p=0,f=t-(c+d),h=e.map((function(e,t){var r=(0,i.pi)((0,i.pi)({},e),{calculatedWidth:e.minWidth||Ct}),a=(0,i.pi)((0,i.pi)({},r),o._columnOverrides[e.key]);return r.isCollapsible||r.isCollapsable||(p+=Rt(r,n)),u+=Rt(a,n),a}));if(l)return h;for(var g=h.length-1;g>=0&&u>f;){var m=(_=h[g]).minWidth||Ct,v=u-f;if(_.calculatedWidth-m>=v||!_.isCollapsible&&!_.isCollapsable){var x=_.calculatedWidth;p<f&&(_.calculatedWidth=Math.max(_.calculatedWidth-v,m)),u-=x-_.calculatedWidth}else u-=Rt(_,n),h.splice(g,1);g--}for(var b=0;b<h.length&&u<f;b++){var _=h[b],k=b===h.length-1,w=this._columnOverrides[_.key];if(!w||!w.calculatedWidth||k){var C=f-u,S=void 0;if(k)S=C;else{var E=_.maxWidth;m=_.minWidth||E||Ct;S=E?Math.min(C,E-m):C}_.calculatedWidth=_.calculatedWidth+S,u+=S}}return h},t.prototype._rememberCalculatedWidth=function(e,t){var n=this._getColumnOverride(e.key);n.calculatedWidth=t,n.currentWidth=t},t.prototype._getColumnOverride=function(e){return this._columnOverrides[e]=this._columnOverrides[e]||{}},t.prototype._getItemKey=function(e,t){var n=this.props.getKey,o=void 0;return e&&(o=e.key),n&&(o=n(e,t)),o||(o=t),o},t.defaultProps={layoutMode:y.Oh.justified,selectionMode:H.oW.multiple,constrainMode:y.ov.horizontalConstrained,checkboxVisibility:y.tY.onHover,isHeaderVisible:!0,compact:!1,useFastIcons:!0},t=(0,i.gn)([_t],t)}(a.Component);function Rt(e,t,n){var o=t.cellStyleProps,r=void 0===o?O.Wt:o;return(n?0:e.calculatedWidth)+r.cellLeftPadding+r.cellRightPadding+(e.isPadded?r.cellExtraRightPadding:0)}var Pt={root:"ms-DetailsList",compact:"ms-DetailsList--Compact",contentWrapper:"ms-DetailsList-contentWrapper",headerWrapper:"ms-DetailsList-headerWrapper",isFixed:"is-fixed",isHorizontalConstrained:"is-horizontalConstrained",listCell:"ms-List-cell"},It=(0,r.z)(Dt,(function(e){var t,n,o=e.theme,r=e.className,i=e.isHorizontalConstrained,a=e.compact,s=e.isFixed,l=o.semanticColors,c=(0,P.Cn)(Pt,o);return{root:[c.root,o.fonts.small,{position:"relative",color:l.listText,selectors:(t={},t["& .".concat(c.listCell)]={minHeight:38,wordBreak:"break-word"},t)},s&&c.isFixed,a&&[c.compact,{selectors:(n={},n[".".concat(c.listCell)]={minHeight:32},n)}],i&&[c.isHorizontalConstrained,{overflowX:"auto",overflowY:"visible",WebkitOverflowScrolling:"touch"}],r],focusZone:[{display:"inline-block",minWidth:"100%",minHeight:1}],headerWrapper:c.headerWrapper,contentWrapper:c.contentWrapper}}),void 0,{scope:"DetailsList"})},61460:function(e,t,n){"use strict";var o,r,i,a,s;n.d(t,{Oh:function(){return a},_1:function(){return o},fQ:function(){return i},ov:function(){return r},tY:function(){return s}}),function(e){e[e.disabled=0]="disabled",e[e.clickable=1]="clickable",e[e.hasDropdown=2]="hasDropdown"}(o||(o={})),function(e){e[e.unconstrained=0]="unconstrained",e[e.horizontalConstrained=1]="horizontalConstrained"}(r||(r={})),function(e){e[e.outside=0]="outside",e[e.surface=1]="surface",e[e.header=2]="header"}(i||(i={})),function(e){e[e.fixedColumns=0]="fixedColumns",e[e.justified=1]="justified"}(a||(a={})),function(e){e[e.onHover=0]="onHover",e[e.always=1]="always",e[e.hidden=2]="hidden"}(s||(s={}))},29134:function(e,t,n){"use strict";n.d(t,{GL:function(){return u},Wt:function(){return l},lv:function(){return c}});var o=n(75971),r=n(40528),i=n(61910),a=n(13687),s={root:"ms-DetailsRow",compact:"ms-DetailsList--Compact",cell:"ms-DetailsRow-cell",cellAnimation:"ms-DetailsRow-cellAnimation",cellCheck:"ms-DetailsRow-cellCheck",check:"ms-DetailsRow-check",cellMeasurer:"ms-DetailsRow-cellMeasurer",listCellFirstChild:"ms-List-cell:first-child",isContentUnselectable:"is-contentUnselectable",isSelected:"is-selected",isCheckVisible:"is-check-visible",isRowHeader:"is-row-header",fields:"ms-DetailsRow-fields"},l={cellLeftPadding:12,cellRightPadding:8,cellExtraRightPadding:24},c={rowHeight:42,compactRowHeight:32},d=(0,o.pi)((0,o.pi)({},c),{rowVerticalPadding:11,compactRowVerticalPadding:6}),u=function(e){var t,n,c,u,p,f,h,g,m,y,v,x,b,_,k=e.theme,w=e.isSelected,C=e.canSelect,S=e.droppingClassName,E=e.isCheckVisible,T=e.checkboxCellClassName,D=e.compact,R=e.className,P=e.cellStyleProps,I=void 0===P?l:P,M=e.enableUpdateAnimations,O=e.disabled,L=k.palette,A=k.fonts,z=L.neutralPrimary,N=L.white,H=L.neutralSecondary,F=L.neutralLighter,B=L.neutralLight,j=L.neutralDark,W=L.neutralQuaternaryAlt,U=k.semanticColors,V=U.focusBorder,G=U.linkHovered,q=(0,r.Cn)(s,k),K={defaultHeaderText:z,defaultMetaText:H,defaultBackground:N,defaultHoverHeaderText:j,defaultHoverMetaText:z,defaultHoverBackground:F,selectedHeaderText:j,selectedMetaText:z,selectedBackground:B,selectedHoverHeaderText:j,selectedHoverMetaText:z,selectedHoverBackground:W,focusHeaderText:j,focusMetaText:z,focusBackground:B,focusHoverBackground:W},Y=[(0,r.GL)(k,{inset:-1,borderColor:V,outlineColor:N,highContrastStyle:{top:2,right:2,bottom:2,left:2},pointerEvents:"none"}),q.isSelected,{color:K.selectedMetaText,background:K.selectedBackground,borderBottom:"1px solid ".concat(N),selectors:(t={"&:before":{position:"absolute",display:"block",top:-1,height:1,bottom:0,left:0,right:0,content:"",borderTop:"1px solid ".concat(N)}},t[".".concat(q.cell," > .").concat(a.W.root)]={color:G,selectors:(n={},n[r.qJ]={color:"HighlightText"},n)},t["&:hover"]={background:K.selectedHoverBackground,color:K.selectedHoverMetaText,selectors:(c={},c[r.qJ]={background:"Highlight",selectors:(u={},u[".".concat(q.cell)]={color:"HighlightText"},u[".".concat(q.cell," > .").concat(a.W.root)]={forcedColorAdjust:"none",color:"HighlightText"},u)},c[".".concat(q.isRowHeader)]={color:K.selectedHoverHeaderText,selectors:(p={},p[r.qJ]={color:"HighlightText"},p)},c)},t["&:focus"]={background:K.focusBackground,selectors:(f={},f[".".concat(q.cell)]={color:K.focusMetaText,selectors:(h={},h[r.qJ]={color:"HighlightText",selectors:{"> a":{color:"HighlightText"}}},h)},f[".".concat(q.isRowHeader)]={color:K.focusHeaderText,selectors:(g={},g[r.qJ]={color:"HighlightText"},g)},f[r.qJ]={background:"Highlight"},f)},t[r.qJ]=(0,o.pi)((0,o.pi)({background:"Highlight",color:"HighlightText"},(0,r.xM)()),{selectors:{a:{color:"HighlightText"}}}),t["&:focus:hover"]={background:K.focusHoverBackground},t)}],X=[q.isContentUnselectable,{userSelect:"none",cursor:"default"}],Z={minHeight:d.compactRowHeight,border:0},J={minHeight:d.compactRowHeight,paddingTop:d.compactRowVerticalPadding,paddingBottom:d.compactRowVerticalPadding,paddingLeft:"".concat(I.cellLeftPadding,"px")},$=[(0,r.GL)(k,{inset:-1}),q.cell,{display:"inline-block",position:"relative",boxSizing:"border-box",minHeight:d.rowHeight,verticalAlign:"top",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",paddingTop:d.rowVerticalPadding,paddingBottom:d.rowVerticalPadding,paddingLeft:"".concat(I.cellLeftPadding,"px"),selectors:(m={"& > button":{maxWidth:"100%"}},m["[data-is-focusable='true']"]=(0,r.GL)(k,{inset:-1,borderColor:H,outlineColor:N}),m)},w&&{selectors:(y={},y[r.qJ]=(0,o.pi)({background:"Highlight",color:"HighlightText"},(0,r.xM)()),y)},D&&J,O&&{opacity:.5}];return{root:[q.root,r.k4.fadeIn400,S,k.fonts.small,E&&q.isCheckVisible,(0,r.GL)(k,{borderColor:V,outlineColor:N}),{borderBottom:"1px solid ".concat(F),background:K.defaultBackground,color:K.defaultMetaText,display:"inline-flex",minWidth:"100%",minHeight:d.rowHeight,whiteSpace:"nowrap",padding:0,boxSizing:"border-box",verticalAlign:"top",textAlign:"left",selectors:(v={},v[".".concat(q.listCellFirstChild," &:before")]={display:"none"},v["&:hover"]={background:K.defaultHoverBackground,color:K.defaultHoverMetaText,selectors:(x={},x[".".concat(q.isRowHeader)]={color:K.defaultHoverHeaderText},x[".".concat(q.cell," > .").concat(a.W.root)]={color:G},x)},v["&:hover .".concat(q.check)]={opacity:1},v[".".concat(i.G$," &:focus .").concat(q.check)]={opacity:1},v[".ms-GroupSpacer"]={flexShrink:0,flexGrow:0},v)},w&&Y,!C&&X,D&&Z,R],cellUnpadded:{paddingRight:"".concat(I.cellRightPadding,"px")},cellPadded:{paddingRight:"".concat(I.cellExtraRightPadding+I.cellRightPadding,"px"),selectors:(b={},b["&.".concat(q.cellCheck)]={paddingRight:0},b)},cell:$,cellAnimation:M&&r.Ic.slideLeftIn40,cellMeasurer:[q.cellMeasurer,{overflow:"visible",whiteSpace:"nowrap"}],checkCell:[$,q.cellCheck,T,{padding:0,paddingTop:1,marginTop:-1,flexShrink:0}],fields:[q.fields,{display:"flex",alignItems:"stretch"}],isRowHeader:[q.isRowHeader,{color:K.defaultHeaderText,fontSize:A.medium.fontSize},w&&{color:K.selectedHeaderText,fontWeight:r.lq.semibold,selectors:(_={},_[r.qJ]={color:"HighlightText"},_)}],isMultiline:[$,{whiteSpace:"normal",wordBreak:"break-word",textOverflow:"clip"}],check:[q.check]}}},29618:function(e,t,n){"use strict";n.d(t,{J:function(){return U}});var o=n(40433),r=n(75971),i=n(72791),a=n(79292),s=n(29723),l=n(47535),c=n(23629),d=n(40528),u=n(42792),p=n(87376),f={root:"ms-Shimmer-container",shimmerWrapper:"ms-Shimmer-shimmerWrapper",shimmerGradient:"ms-Shimmer-shimmerGradient",dataWrapper:"ms-Shimmer-dataWrapper"},h="100%",g=(0,u.NF)((function(){return(0,d.F4)({"0%":{transform:"translateX(-".concat(h,")")},"100%":{transform:"translateX(".concat(h,")")}})})),m=(0,u.NF)((function(){return(0,d.F4)({"100%":{transform:"translateX(-".concat(h,")")},"0%":{transform:"translateX(".concat(h,")")}})}));var y,v,x=n(15863),b=n(12103);!function(e){e[e.line=1]="line",e[e.circle=2]="circle",e[e.gap=3]="gap"}(y||(y={})),function(e){e[e.line=16]="line",e[e.gap=16]="gap",e[e.circle=24]="circle"}(v||(v={}));var _=(0,a.y)(),k={root:"ms-ShimmerLine-root",topLeftCorner:"ms-ShimmerLine-topLeftCorner",topRightCorner:"ms-ShimmerLine-topRightCorner",bottomLeftCorner:"ms-ShimmerLine-bottomLeftCorner",bottomRightCorner:"ms-ShimmerLine-bottomRightCorner"};var w=(0,o.z)((function(e){var t=e.height,n=e.styles,o=e.width,r=void 0===o?"100%":o,a=e.borderStyle,s=e.theme,l=_(n,{theme:s,height:t,borderStyle:a});return i.createElement("div",{style:{width:r,minWidth:"number"===typeof r?"".concat(r,"px"):"auto"},className:l.root},i.createElement("svg",{width:"2",height:"2",className:l.topLeftCorner},i.createElement("path",{d:"M0 2 A 2 2, 0, 0, 1, 2 0 L 0 0 Z"})),i.createElement("svg",{width:"2",height:"2",className:l.topRightCorner},i.createElement("path",{d:"M0 0 A 2 2, 0, 0, 1, 2 2 L 2 0 Z"})),i.createElement("svg",{width:"2",height:"2",className:l.bottomRightCorner},i.createElement("path",{d:"M2 0 A 2 2, 0, 0, 1, 0 2 L 2 2 Z"})),i.createElement("svg",{width:"2",height:"2",className:l.bottomLeftCorner},i.createElement("path",{d:"M2 2 A 2 2, 0, 0, 1, 0 0 L 0 2 Z"})))}),(function(e){var t,n=e.height,o=e.borderStyle,r=e.theme,i=r.semanticColors,a=(0,d.Cn)(k,r),s=o||{},l={position:"absolute",fill:i.bodyBackground};return{root:[a.root,r.fonts.medium,{height:"".concat(n,"px"),boxSizing:"content-box",position:"relative",borderTopStyle:"solid",borderBottomStyle:"solid",borderColor:i.bodyBackground,borderWidth:0,selectors:(t={},t[d.qJ]={borderColor:"Window",selectors:{"> *":{fill:"Window"}}},t)},s],topLeftCorner:[a.topLeftCorner,{top:"0",left:"0"},l],topRightCorner:[a.topRightCorner,{top:"0",right:"0"},l],bottomRightCorner:[a.bottomRightCorner,{bottom:"0",right:"0"},l],bottomLeftCorner:[a.bottomLeftCorner,{bottom:"0",left:"0"},l]}}),void 0,{scope:"ShimmerLine"}),C=(0,a.y)(),S={root:"ms-ShimmerGap-root"};var E=(0,o.z)((function(e){var t=e.height,n=e.styles,o=e.width,r=void 0===o?"10px":o,a=e.borderStyle,s=e.theme,l=C(n,{theme:s,height:t,borderStyle:a});return i.createElement("div",{style:{width:r,minWidth:"number"===typeof r?"".concat(r,"px"):"auto"},className:l.root})}),(function(e){var t,n=e.height,o=e.borderStyle,r=e.theme,i=r.semanticColors,a=o||{};return{root:[(0,d.Cn)(S,r).root,r.fonts.medium,{backgroundColor:i.bodyBackground,height:"".concat(n,"px"),boxSizing:"content-box",borderTopStyle:"solid",borderBottomStyle:"solid",borderColor:i.bodyBackground,selectors:(t={},t[d.qJ]={backgroundColor:"Window",borderColor:"Window"},t)},a]}}),void 0,{scope:"ShimmerGap"}),T={root:"ms-ShimmerCircle-root",svg:"ms-ShimmerCircle-svg"};var D=(0,a.y)(),R=(0,o.z)((function(e){var t=e.height,n=e.styles,o=e.borderStyle,r=e.theme,a=D(n,{theme:r,height:t,borderStyle:o});return i.createElement("div",{className:a.root},i.createElement("svg",{viewBox:"0 0 10 10",width:t,height:t,className:a.svg},i.createElement("path",{d:"M0,0 L10,0 L10,10 L0,10 L0,0 Z M0,5 C0,7.76142375 2.23857625,10 5,10 C7.76142375,10 10,7.76142375 10,5 C10,2.23857625 7.76142375,2.22044605e-16 5,0 C2.23857625,-2.22044605e-16 0,2.23857625 0,5 L0,5 Z"})))}),(function(e){var t,n,o=e.height,r=e.borderStyle,i=e.theme,a=i.semanticColors,s=(0,d.Cn)(T,i),l=r||{};return{root:[s.root,i.fonts.medium,{width:"".concat(o,"px"),height:"".concat(o,"px"),minWidth:"".concat(o,"px"),boxSizing:"content-box",borderTopStyle:"solid",borderBottomStyle:"solid",borderColor:a.bodyBackground,selectors:(t={},t[d.qJ]={borderColor:"Window"},t)},l],svg:[s.svg,{display:"block",fill:a.bodyBackground,selectors:(n={},n[d.qJ]={fill:"Window"},n)}]}}),void 0,{scope:"ShimmerCircle"}),P=(0,a.y)();var I=(0,u.NF)((function(e,t,n,o,i){var a,s=i&&n?i-n:0;if(e&&"center"!==e?e&&"top"===e?a={borderBottomWidth:"".concat(s,"px"),borderTopWidth:"0px"}:e&&"bottom"===e&&(a={borderBottomWidth:"0px",borderTopWidth:"".concat(s,"px")}):a={borderBottomWidth:"".concat(s?Math.floor(s/2):0,"px"),borderTopWidth:"".concat(s?Math.ceil(s/2):0,"px")},o)switch(t){case y.circle:return{root:(0,r.pi)((0,r.pi)({},a),{borderColor:o}),svg:{fill:o}};case y.gap:return{root:(0,r.pi)((0,r.pi)({},a),{borderColor:o,backgroundColor:o})};case y.line:return{root:(0,r.pi)((0,r.pi)({},a),{borderColor:o}),topLeftCorner:{fill:o},topRightCorner:{fill:o},bottomLeftCorner:{fill:o},bottomRightCorner:{fill:o}}}return{root:a}}));var M={root:"ms-ShimmerElementsGroup-root"};var O=(0,o.z)((function(e){var t=e.styles,n=e.width,o=void 0===n?"auto":n,a=e.shimmerElements,s=e.rowHeight,l=void 0===s?function(e){var t=e.map((function(e){switch(e.type){case y.circle:e.height||(e.height=v.circle);break;case y.line:e.height||(e.height=v.line);break;case y.gap:e.height||(e.height=v.gap)}return e})),n=t.reduce((function(e,t){return t.height&&t.height>e?t.height:e}),0);return n}(a||[]):s,c=e.flexWrap,d=void 0!==c&&c,u=e.theme,p=e.backgroundColor,f=P(t,{theme:u,flexWrap:d});return i.createElement("div",{style:{width:o},className:f.root},function(e,t,n){var o=e?e.map((function(e,o){var a=e.type,s=(0,r._T)(e,["type"]),l=s.verticalAlign,c=s.height,d=I(l,a,c,t,n);switch(e.type){case y.circle:return i.createElement(R,(0,r.pi)({key:o},s,{styles:d}));case y.gap:return i.createElement(E,(0,r.pi)({key:o},s,{styles:d}));case y.line:return i.createElement(w,(0,r.pi)({key:o},s,{styles:d}))}})):i.createElement(w,{height:v.line});return o}(a,p,l))}),(function(e){var t=e.flexWrap,n=e.theme;return{root:[(0,d.Cn)(M,n).root,n.fonts.medium,{display:"flex",alignItems:"center",flexWrap:t?"wrap":"nowrap",position:"relative"}]}}),void 0,{scope:"ShimmerElementsGroup"}),L=n(32561),A=n(60684),z=(0,a.y)(),N=i.forwardRef((function(e,t){var n=e.styles,o=e.shimmerElements,a=e.children,s=e.width,l=e.className,c=e.customElementsGroup,d=e.theme,u=e.ariaLabel,p=e.shimmerColors,f=e.isDataLoaded,h=void 0!==f&&f,g=e.improveCSSPerformance,m=(0,x.pq)(e,x.n7),y=z(n,{theme:d,isDataLoaded:h,className:l,transitionAnimationInterval:200,shimmerColor:p&&p.shimmer,shimmerWaveColor:p&&p.shimmerWave,improveCSSPerformance:g||!c}),v=(0,L.B)({lastTimeoutId:0}),_=(0,A.L)(),k=_.setTimeout,w=_.clearTimeout,C=i.useState(h),S=C[0],E=C[1],T={width:s||"100%"};return i.useEffect((function(){if(h!==S){if(h)return v.lastTimeoutId=k((function(){E(!0)}),200),function(){return w(v.lastTimeoutId)};E(!1)}}),[h]),i.createElement("div",(0,r.pi)({},m,{className:y.root,ref:t}),!S&&i.createElement("div",{style:T,className:y.shimmerWrapper},i.createElement("div",{className:y.shimmerGradient}),c||i.createElement(O,{shimmerElements:o,backgroundColor:p&&p.background})),a&&i.createElement("div",{className:y.dataWrapper},a),u&&!h&&i.createElement("div",{role:"status","aria-live":"polite"},i.createElement(b.U,null,i.createElement("div",{className:y.screenReaderText},u))))}));N.displayName="Shimmer";var H=(0,o.z)(N,(function(e){var t,n=e.isDataLoaded,o=e.className,i=e.theme,a=e.transitionAnimationInterval,s=e.shimmerColor,l=e.shimmerWaveColor,c=e.improveCSSPerformance,u=i.semanticColors,y=(0,d.Cn)(f,i),v=(0,p.zg)(i);return{root:[y.root,i.fonts.medium,{position:"relative",height:"auto"},o],shimmerWrapper:[y.shimmerWrapper,{position:"relative",overflow:"hidden",transform:"translateZ(0)",backgroundColor:s||u.disabledBackground,transition:"opacity ".concat(a,"ms"),selectors:(t={},t[d.qJ]=(0,r.pi)({background:"WindowText\n                        linear-gradient(\n                          to right,\n                          transparent 0%,\n                          Window 50%,\n                          transparent 100%)\n                        0 0 / 90% 100%\n                        no-repeat"},(0,d.xM)()),t)},n&&{opacity:"0",position:"absolute",top:"0",bottom:"0",left:"0",right:"0"},c?{selectors:{"> div:last-child":{transform:"translateZ(0)"}}}:{selectors:{"> *":{transform:"translateZ(0)"}}}],shimmerGradient:[y.shimmerGradient,{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:"".concat(s||u.disabledBackground,"\n                      linear-gradient(\n                        to right,\n                        ").concat(s||u.disabledBackground," 0%,\n                        ").concat(l||u.bodyDivider," 50%,\n                        ").concat(s||u.disabledBackground," 100%)\n                      0 0 / 90% 100%\n                      no-repeat"),transform:"translateX(-".concat(h,")"),animationDuration:"2s",animationTimingFunction:"ease-in-out",animationDirection:"normal",animationIterationCount:"infinite",animationName:v?m():g()}],dataWrapper:[y.dataWrapper,{position:"absolute",top:"0",bottom:"0",left:"0",right:"0",opacity:"0",background:"none",backgroundColor:"transparent",border:"none",transition:"opacity ".concat(a,"ms")},n&&{opacity:"1",position:"static"}],screenReaderText:d.ul}}),void 0,{scope:"Shimmer"}),F=n(61460),B=n(29134),j=(0,a.y)(),W=function(e){function t(t){var n=e.call(this,t)||this;return n._onRenderShimmerPlaceholder=function(e,t){var o=n.props.onRenderCustomPlaceholder,r=o?o(t,e,n._renderDefaultShimmerPlaceholder):n._renderDefaultShimmerPlaceholder(t);return i.createElement(H,{customElementsGroup:r})},n._renderDefaultShimmerPlaceholder=function(e){var t=e.columns,n=e.compact,o=e.selectionMode,r=e.checkboxVisibility,a=e.cellStyleProps,s=void 0===a?B.Wt:a,c=B.lv.rowHeight,d=B.lv.compactRowHeight,u=n?d:c+1,p=[];return o!==l.oW.none&&r!==F.tY.hidden&&p.push(i.createElement(O,{key:"checkboxGap",shimmerElements:[{type:y.gap,width:"40px",height:u}]})),t.forEach((function(e,t){var n=[],o=s.cellLeftPadding+s.cellRightPadding+e.calculatedWidth+(e.isPadded?s.cellExtraRightPadding:0);n.push({type:y.gap,width:s.cellLeftPadding,height:u}),e.isIconOnly?(n.push({type:y.line,width:e.calculatedWidth,height:e.calculatedWidth}),n.push({type:y.gap,width:s.cellRightPadding,height:u})):(n.push({type:y.line,width:.95*e.calculatedWidth,height:7}),n.push({type:y.gap,width:s.cellRightPadding+(e.calculatedWidth-.95*e.calculatedWidth)+(e.isPadded?s.cellExtraRightPadding:0),height:u})),p.push(i.createElement(O,{key:t,width:"".concat(o,"px"),shimmerElements:n}))})),p.push(i.createElement(O,{key:"endGap",width:"100%",shimmerElements:[{type:y.gap,width:"100%",height:u}]})),i.createElement("div",{style:{display:"flex"}},p)},n._shimmerItems=t.shimmerLines?new Array(t.shimmerLines):new Array(10),n}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.detailsListStyles,n=e.enableShimmer,o=e.items,a=e.listProps,l=(e.onRenderCustomPlaceholder,e.removeFadingOverlay),d=(e.shimmerLines,e.styles),u=e.theme,p=e.ariaLabelForGrid,f=e.ariaLabelForShimmer,h=(0,r._T)(e,["detailsListStyles","enableShimmer","items","listProps","onRenderCustomPlaceholder","removeFadingOverlay","shimmerLines","styles","theme","ariaLabelForGrid","ariaLabelForShimmer"]),g=a&&a.className;this._classNames=j(d,{theme:u});var m=(0,r.pi)((0,r.pi)({},a),{className:n&&!l?(0,s.i)(this._classNames.root,g):g});return i.createElement(c.W,(0,r.pi)({},h,{styles:t,items:n?this._shimmerItems:o,isPlaceholderData:n,ariaLabelForGrid:n&&f||p,onRenderMissingItem:this._onRenderShimmerPlaceholder,listProps:m}))},t}(i.Component),U=(0,o.z)(W,(function(e){var t=e.theme.palette;return{root:{position:"relative",selectors:{":after":{content:'""',position:"absolute",top:0,right:0,bottom:0,left:0,backgroundImage:"linear-gradient(to bottom, transparent 30%, ".concat(t.whiteTranslucent40," 65%,").concat(t.white," 100%)")}}}}}),void 0,{scope:"ShimmeredDetailsList"})},13687:function(e,t,n){"use strict";n.d(t,{W:function(){return r},w:function(){return i}});var o=n(40528),r={root:"ms-Link"},i=function(e){var t,n,i,a,s,l,c,d=e.className,u=e.isButton,p=e.isDisabled,f=e.isUnderlined,h=e.theme,g=h.semanticColors,m=g.link,y=g.linkHovered,v=g.disabledText,x=g.focusBorder,b=(0,o.Cn)(r,h);return{root:[b.root,h.fonts.medium,{color:m,outline:"none",fontSize:"inherit",fontWeight:"inherit",textDecoration:f?"underline":"none",selectors:(t={".ms-Fabric--isFocusVisible &:focus":{boxShadow:"0 0 0 1px ".concat(x," inset"),outline:"1px auto ".concat(x),selectors:(n={},n[o.qJ]={outline:"1px solid WindowText"},n)}},t[o.qJ]={borderBottom:"none"},t)},u&&{background:"none",backgroundColor:"transparent",border:"none",cursor:"pointer",display:"inline",margin:0,overflow:"inherit",padding:0,textAlign:"left",textOverflow:"inherit",userSelect:"text",borderBottom:"1px solid transparent",selectors:(i={},i[o.qJ]={color:"LinkText",forcedColorAdjust:"none"},i)},!u&&{selectors:(a={},a[o.qJ]={MsHighContrastAdjust:"auto",forcedColorAdjust:"auto"},a)},p&&["is-disabled",{color:v,cursor:"default"},{selectors:(s={"&:link, &:visited":{pointerEvents:"none"}},s[o.qJ]={color:"GrayText"},s)}],!p&&{selectors:{"&:active, &:hover, &:active:hover":{color:y,textDecoration:"underline",selectors:(l={},l[o.qJ]={color:"LinkText"},l)},"&:focus":{color:m,selectors:(c={},c[o.qJ]={color:"LinkText"},c)}}},b.root,d]}}},61020:function(e,t,n){"use strict";n.d(t,{u:function(){return B}});var o=n(40433),r=n(75971),i=n(72791),a=n(79292),s=n(97471),l=n(92853),c=n(71675),d=n(9691),u=n(84688),p=n(29723),f=n(10892),h=n(40528),g=h.D1.durationValue2,m={root:"ms-Modal",main:"ms-Dialog-main",scrollableContent:"ms-Modal-scrollableContent",isOpen:"is-open",layer:"ms-Modal-Layer"},y=n(69894),v=n(25443),x=n(68416),b=n(98687),_=n(91381),k=n(81080),w=n(18511),C=(0,n(42792).NF)((function(e,t){return{root:(0,h.y0)(e,t&&{touchAction:"none",selectors:{"& *":{userSelect:"none"}}})}})),S=n(64862),E={start:"touchstart",move:"touchmove",stop:"touchend"},T={start:"mousedown",move:"mousemove",stop:"mouseup"},D=function(e){function t(t){var n=e.call(this,t)||this;return n._currentEventType=T,n._events=[],n._onMouseDown=function(e){var t=i.Children.only(n.props.children).props.onMouseDown;return t&&t(e),n._currentEventType=T,n._onDragStart(e)},n._onMouseUp=function(e){var t=i.Children.only(n.props.children).props.onMouseUp;return t&&t(e),n._currentEventType=T,n._onDragStop(e)},n._onTouchStart=function(e){var t=i.Children.only(n.props.children).props.onTouchStart;return t&&t(e),n._currentEventType=E,n._onDragStart(e)},n._onTouchEnd=function(e){var t=i.Children.only(n.props.children).props.onTouchEnd;t&&t(e),n._currentEventType=E,n._onDragStop(e)},n._onDragStart=function(e){if("number"===typeof e.button&&0!==e.button)return!1;if(!(n.props.handleSelector&&!n._matchesSelector(e.target,n.props.handleSelector)||n.props.preventDragSelector&&n._matchesSelector(e.target,n.props.preventDragSelector))){n._touchId=n._getTouchId(e);var t=n._getControlPosition(e);if(void 0!==t){var o=n._createDragDataFromPosition(t);n.props.onStart&&n.props.onStart(e,o),n.setState({isDragging:!0,lastPosition:t}),n._events=[(0,S.on)(document.body,n._currentEventType.move,n._onDrag,!0),(0,S.on)(document.body,n._currentEventType.stop,n._onDragStop,!0)]}}},n._onDrag=function(e){"touchmove"===e.type&&e.preventDefault();var t=n._getControlPosition(e);if(t){var o=n._createUpdatedDragData(n._createDragDataFromPosition(t)),r=o.position;n.props.onDragChange&&n.props.onDragChange(e,o),n.setState({position:r,lastPosition:t})}},n._onDragStop=function(e){if(n.state.isDragging){var t=n._getControlPosition(e);if(t){var o=n._createDragDataFromPosition(t);n.setState({isDragging:!1,lastPosition:void 0}),n.props.onStop&&n.props.onStop(e,o),n.props.position&&n.setState({position:n.props.position}),n._events.forEach((function(e){return e()}))}}},n.state={isDragging:!1,position:n.props.position||{x:0,y:0},lastPosition:void 0},n}return(0,r.ZT)(t,e),t.prototype.componentDidUpdate=function(e){!this.props.position||e.position&&this.props.position===e.position||this.setState({position:this.props.position})},t.prototype.componentWillUnmount=function(){this._events.forEach((function(e){return e()}))},t.prototype.render=function(){var e=i.Children.only(this.props.children),t=e.props,n=this.props.position,o=this.state,a=o.position,s=o.isDragging,l=a.x,c=a.y;return n&&!s&&(l=n.x,c=n.y),i.cloneElement(e,{style:(0,r.pi)((0,r.pi)({},t.style),{transform:"translate(".concat(l,"px, ").concat(c,"px)")}),className:C(t.className,this.state.isDragging).root,onMouseDown:this._onMouseDown,onMouseUp:this._onMouseUp,onTouchStart:this._onTouchStart,onTouchEnd:this._onTouchEnd})},t.prototype._getControlPosition=function(e){var t=this._getActiveTouch(e);if(void 0===this._touchId||t){var n=t||e;return{x:n.clientX,y:n.clientY}}},t.prototype._getActiveTouch=function(e){return e.targetTouches&&this._findTouchInTouchList(e.targetTouches)||e.changedTouches&&this._findTouchInTouchList(e.changedTouches)},t.prototype._getTouchId=function(e){var t=e.targetTouches&&e.targetTouches[0]||e.changedTouches&&e.changedTouches[0];if(t)return t.identifier},t.prototype._matchesSelector=function(e,t){if(!e||e===document.body)return!1;var n=e.matches||e.webkitMatchesSelector||e.msMatchesSelector;return!!n&&(n.call(e,t)||this._matchesSelector(e.parentElement,t))},t.prototype._findTouchInTouchList=function(e){if(void 0!==this._touchId)for(var t=0;t<e.length;t++)if(e[t].identifier===this._touchId)return e[t]},t.prototype._createDragDataFromPosition=function(e){var t=this.state.lastPosition;return void 0===t?{delta:{x:0,y:0},lastPosition:e,position:e}:{delta:{x:e.x-t.x,y:e.y-t.y},lastPosition:t,position:e}},t.prototype._createUpdatedDragData=function(e){var t=this.state.position;return{position:{x:t.x+e.delta.x,y:t.y+e.delta.y},delta:e.delta,lastPosition:t}},t}(i.Component),R=n(3141),P=n(66587),I=n(24994),M=n(60684),O=n(32594),L=n(32561),A=n(38289),z={x:0,y:0},N={isOpen:!1,isDarkOverlay:!0,className:"",containerClassName:"",enableAriaHiddenSiblings:!0},H=(0,a.y)(),F=i.forwardRef((function(e,t){var n,o,a,h,m,C=(0,s.j)(N,e),S=C.allowTouchBodyScroll,E=C.className,T=C.children,F=C.containerClassName,B=C.scrollableContentClassName,j=C.elementToFocusOnDismiss,W=C.firstFocusableSelector,U=C.focusTrapZoneProps,V=C.forceFocusInsideTrap,G=C.disableRestoreFocus,q=void 0===G?C.ignoreExternalFocusing:G,K=C.isBlocking,Y=C.isAlert,X=C.isClickableOutsideFocusTrap,Z=C.isDarkOverlay,J=C.onDismiss,$=C.layerProps,Q=C.overlay,ee=C.isOpen,te=C.titleAriaId,ne=C.styles,oe=C.subtitleAriaId,re=C.theme,ie=C.topOffsetFixed,ae=C.responsiveMode,se=C.onLayerDidMount,le=C.isModeless,ce=C.dragOptions,de=C.onDismissed,ue=C.enableAriaHiddenSiblings,pe=C.popupProps,fe=i.useRef(null),he=i.useRef(null),ge=(0,P.r)(he,null===U||void 0===U?void 0:U.componentRef),me=i.useRef(null),ye=(0,P.r)(fe,t),ve=(0,b.q)(ye),xe=(0,I.M)("ModalFocusTrapZone",null===U||void 0===U?void 0:U.id),be=(0,R.zY)(),_e=(0,M.L)(),ke=_e.setTimeout,we=_e.clearTimeout,Ce=i.useState(ee),Se=Ce[0],Ee=Ce[1],Te=i.useState(ee),De=Te[0],Re=Te[1],Pe=i.useState(z),Ie=Pe[0],Me=Pe[1],Oe=i.useState(),Le=Oe[0],Ae=Oe[1],ze=(0,O.k)(!1),Ne=ze[0],He=ze[1],Fe=He.toggle,Be=He.setFalse,je=(0,L.B)((function(){return{onModalCloseTimer:0,allowTouchBodyScroll:S,scrollableContent:null,lastSetCoordinates:z,events:new l.r({})}})),We=(ce||{}).keepInBounds,Ue=null!==Y&&void 0!==Y?Y:K&&!le,Ve=void 0===$?"":$.className,Ge=H(ne,{theme:re,className:E,containerClassName:F,scrollableContentClassName:B,isOpen:ee,isVisible:De,hasBeenOpened:je.hasBeenOpened,modalRectangleTop:Le,topOffsetFixed:ie,isModeless:le,layerClassName:Ve,windowInnerHeight:null===be||void 0===be?void 0:be.innerHeight,isDefaultDragHandle:ce&&!ce.dragHandleSelector}),qe=(0,r.pi)((0,r.pi)({eventBubblingEnabled:!1},$),{onLayerDidMount:$&&$.onLayerDidMount?$.onLayerDidMount:se,insertFirst:(null===$||void 0===$?void 0:$.insertFirst)||le,className:Ge.layer}),Ke=i.useCallback((function(e){e?je.allowTouchBodyScroll?(0,c.eC)(e,je.events):(0,c.C7)(e,je.events):je.events.off(je.scrollableContent),je.scrollableContent=e}),[je]),Ye=function(){var e=me.current,t=null===e||void 0===e?void 0:e.getBoundingClientRect();t&&(ie&&Ae(t.top),We&&(je.minPosition={x:-t.left,y:-t.top},je.maxPosition={x:t.left,y:t.top}))},Xe=i.useCallback((function(e,t){var n=je.minPosition,o=je.maxPosition;return We&&n&&o&&(t=Math.max(n[e],t),t=Math.min(o[e],t)),t}),[We,je]),Ze=function(){var e;je.lastSetCoordinates=z,Be(),je.isInKeyboardMoveMode=!1,Ee(!1),Me(z),null===(e=je.disposeOnKeyUp)||void 0===e||e.call(je),null===de||void 0===de||de()},Je=i.useCallback((function(){Be(),je.isInKeyboardMoveMode=!1}),[je,Be]),$e=i.useCallback((function(e,t){Me((function(e){return{x:Xe("x",e.x+t.delta.x),y:Xe("y",e.y+t.delta.y)}}))}),[Xe]),Qe=i.useCallback((function(){he.current&&he.current.focus()}),[]);i.useEffect((function(){we(je.onModalCloseTimer),ee&&(requestAnimationFrame((function(){return ke(Ye,0)})),Ee(!0),ce&&function(){var e=function(e){e.altKey&&e.ctrlKey&&e.keyCode===d.m.space&&(0,u.t)(je.scrollableContent,e.target)&&(Fe(),e.preventDefault(),e.stopPropagation())};je.disposeOnKeyUp||(je.events.on(be,"keyup",e,!0),je.disposeOnKeyUp=function(){je.events.off(be,"keyup",e,!0),je.disposeOnKeyUp=void 0})}(),je.hasBeenOpened=!0,Re(!0)),!ee&&Se&&(je.onModalCloseTimer=ke(Ze,1e3*parseFloat(g)),Re(!1))}),[Se,ee]),(0,A.k)((function(){je.events.dispose()})),function(e,t){i.useImperativeHandle(e.componentRef,(function(){return{focus:function(){t.current&&t.current.focus()}}}),[t])}(C,he);var et=i.createElement(f.P,(0,r.pi)({},U,{id:xe,ref:me,componentRef:ge,className:(0,p.i)(Ge.main,null===U||void 0===U?void 0:U.className),elementToFocusOnDismiss:null!==(n=null===U||void 0===U?void 0:U.elementToFocusOnDismiss)&&void 0!==n?n:j,isClickableOutsideFocusTrap:null!==(o=null===U||void 0===U?void 0:U.isClickableOutsideFocusTrap)&&void 0!==o?o:le||X||!K,disableRestoreFocus:null!==(a=null===U||void 0===U?void 0:U.disableRestoreFocus)&&void 0!==a?a:q,forceFocusInsideTrap:(null!==(h=null===U||void 0===U?void 0:U.forceFocusInsideTrap)&&void 0!==h?h:V)&&!le,firstFocusableSelector:(null===U||void 0===U?void 0:U.firstFocusableSelector)||W,focusPreviouslyFocusedInnerElement:null===(m=null===U||void 0===U?void 0:U.focusPreviouslyFocusedInnerElement)||void 0===m||m,onBlur:je.isInKeyboardMoveMode?function(e){var t,n;null===(t=null===U||void 0===U?void 0:U.onBlur)||void 0===t||t.call(U,e),je.lastSetCoordinates=z,je.isInKeyboardMoveMode=!1,null===(n=je.disposeOnKeyDown)||void 0===n||n.call(je)}:void 0}),ce&&je.isInKeyboardMoveMode&&i.createElement("div",{className:Ge.keyboardMoveIconContainer},ce.keyboardMoveIconProps?i.createElement(w.J,(0,r.pi)({},ce.keyboardMoveIconProps)):i.createElement(w.J,{iconName:"move",className:Ge.keyboardMoveIcon})),i.createElement("div",{ref:Ke,className:Ge.scrollableContent,"data-is-scrollable":!0},ce&&Ne&&i.createElement(ce.menu,{items:[{key:"move",text:ce.moveMenuItemText,onClick:function(){var e=function(e){if(e.altKey&&e.ctrlKey&&e.keyCode===d.m.space)return e.preventDefault(),void e.stopPropagation();var t=e.altKey||e.keyCode===d.m.escape;if(Ne&&t&&Be(),!je.isInKeyboardMoveMode||e.keyCode!==d.m.escape&&e.keyCode!==d.m.enter||(je.isInKeyboardMoveMode=!1,e.preventDefault(),e.stopPropagation()),je.isInKeyboardMoveMode){var n=!0,o=function(e){var t=10;return e.shiftKey?e.ctrlKey||(t=50):e.ctrlKey&&(t=1),t}(e);switch(e.keyCode){case d.m.escape:Me(je.lastSetCoordinates);case d.m.enter:je.lastSetCoordinates=z;break;case d.m.up:Me((function(e){return{x:e.x,y:Xe("y",e.y-o)}}));break;case d.m.down:Me((function(e){return{x:e.x,y:Xe("y",e.y+o)}}));break;case d.m.left:Me((function(e){return{x:Xe("x",e.x-o),y:e.y}}));break;case d.m.right:Me((function(e){return{x:Xe("x",e.x+o),y:e.y}}));break;default:n=!1}n&&(e.preventDefault(),e.stopPropagation())}};je.lastSetCoordinates=Ie,Be(),je.isInKeyboardMoveMode=!0,je.events.on(be,"keydown",e,!0),je.disposeOnKeyDown=function(){je.events.off(be,"keydown",e,!0),je.disposeOnKeyDown=void 0}}},{key:"close",text:ce.closeMenuItemText,onClick:Ze}],onDismiss:Be,alignTargetEdge:!0,coverTarget:!0,directionalHint:k.b.topLeftEdge,directionalHintFixed:!0,shouldFocusOnMount:!0,target:je.scrollableContent}),T));return Se&&ve>=(ae||_.eD.small)&&i.createElement(v.m,(0,r.pi)({ref:ye},qe),i.createElement(x.G,(0,r.pi)({role:Ue?"alertdialog":"dialog",ariaLabelledBy:te,ariaDescribedBy:oe,onDismiss:J,shouldRestoreFocus:!q,enableAriaHiddenSiblings:ue,"aria-modal":!le},pe),i.createElement("div",{className:Ge.root,role:le?void 0:"document"},!le&&i.createElement(y.a,(0,r.pi)({"aria-hidden":!0,isDarkThemed:Z,onClick:K?void 0:J,allowTouchBodyScroll:S},Q)),ce?i.createElement(D,{handleSelector:ce.dragHandleSelector||"#".concat(xe),preventDragSelector:"button",onStart:Je,onDragChange:$e,onStop:Qe,position:Ie},et):et)))||null}));F.displayName="Modal";var B=(0,o.z)(F,(function(e){var t,n=e.className,o=e.containerClassName,r=e.scrollableContentClassName,i=e.isOpen,a=e.isVisible,s=e.hasBeenOpened,l=e.modalRectangleTop,c=e.theme,d=e.topOffsetFixed,u=e.isModeless,p=e.layerClassName,f=e.isDefaultDragHandle,y=e.windowInnerHeight,v=c.palette,x=c.effects,b=c.fonts,_=(0,h.Cn)(m,c);return{root:[_.root,b.medium,{backgroundColor:"transparent",position:"fixed",height:"100%",width:"100%",display:"flex",alignItems:"center",justifyContent:"center",opacity:0,pointerEvents:"none",transition:"opacity ".concat(g)},d&&"number"===typeof l&&s&&{alignItems:"flex-start"},i&&_.isOpen,a&&{opacity:1},a&&!u&&{pointerEvents:"auto"},n],main:[_.main,{boxShadow:x.elevation64,borderRadius:x.roundedCorner2,backgroundColor:v.white,boxSizing:"border-box",position:"relative",textAlign:"left",outline:"3px solid transparent",maxHeight:"calc(100% - 32px)",maxWidth:"calc(100% - 32px)",minHeight:"176px",minWidth:"288px",overflowY:"auto",zIndex:u?h.bR.Layer:void 0},u&&{pointerEvents:"auto"},d&&"number"===typeof l&&s&&{top:l},f&&{cursor:"move"},o],scrollableContent:[_.scrollableContent,{overflowY:"auto",flexGrow:1,maxHeight:"100vh",selectors:(t={},t["@supports (-webkit-overflow-scrolling: touch)"]={maxHeight:y},t)},r],layer:u&&[p,_.layer,{pointerEvents:"none"}],keyboardMoveIconContainer:{position:"absolute",display:"flex",justifyContent:"center",width:"100%",padding:"3px 0px"},keyboardMoveIcon:{fontSize:b.xLargePlus.fontSize,width:"24px"}}}),void 0,{scope:"Modal",fields:["theme","styles","enableAriaHiddenSiblings"]});B.displayName="Modal"},82728:function(e,t,n){"use strict";n.d(t,{o:function(){return M}});var o=n(40433),r=n(75971),i=n(72791),a=n(24994),s=n(53193),l=n(79292),c=n(13202),d=n(15863),u=n(29723),p=n(9691),f=n(87376),h=n(36190);function g(e,t){void 0===t&&(t=null);var n=i.useRef({ref:function(){var e=function(e){n.ref.current!==e&&(n.cleanup&&(n.cleanup(),n.cleanup=void 0),n.ref.current=e,null!==e&&(n.cleanup=n.callback(e)))};return e.current=t,e}(),callback:e}).current;return n.callback=e,n.ref}var m=n(7211),y=n(12513),v=function(e){var t=e.onOverflowItemsChanged,n=e.rtl,o=e.pinnedIndex,r=i.useRef(),a=i.useRef(),s=g((function(e){var t=function(e,t){if("undefined"!==typeof ResizeObserver){var n=new ResizeObserver(t);return Array.isArray(e)?e.forEach((function(e){return n.observe(e)})):n.observe(e),function(){return n.disconnect()}}var o=function(){return t(void 0)},r=(0,y.J)(Array.isArray(e)?e[0]:e);if(!r)return function(){};var i=r.requestAnimationFrame(o);return r.addEventListener("resize",o,!1),function(){r.cancelAnimationFrame(i),r.removeEventListener("resize",o,!1)}}(e,(function(t){a.current=t?t[0].contentRect.width:e.clientWidth,r.current&&r.current()}));return function(){t(),a.current=void 0}})),l=g((function(e){return s(e.parentElement),function(){return s(null)}}));return(0,m.L)((function(){var e=s.current,i=l.current;if(e&&i){for(var c=[],d=0;d<e.children.length;d++){var u=e.children[d];u instanceof HTMLElement&&u!==i&&c.push(u)}var p=[],f=0;r.current=function(){var e=a.current;if(void 0!==e){for(var t=c.length-1;t>=0;t--){if(void 0===p[t]){var r=n?e-c[t].offsetLeft:c[t].offsetLeft+c[t].offsetWidth;t+1<c.length&&t+1===o&&(f=p[t+1]-r),t===c.length-2&&(f+=i.offsetWidth),p[t]=r+f}if(e>p[t])return void g(t+1)}g(0)}};var h=c.length,g=function(e){h!==e&&(h=e,t(e,c.map((function(t,n){return{ele:t,isOverflowing:n>=e&&n!==o}}))))},m=void 0;if(void 0!==a.current){var v=(0,y.J)(e);if(v){var x=v.requestAnimationFrame(r.current);m=function(){return v.cancelAnimationFrame(x)}}}return function(){m&&m(),g(c.length),r.current=void 0}}})),{menuButtonRef:l}},x=n(65260),b=n(78498),_=n(81080),k=n(18511),w=n(46933),C=(0,l.y)(),S=function(e,t){var n={links:[],keyToIndexMapping:{},keyToTabIdMapping:{}};return i.Children.forEach(i.Children.toArray(e.children),(function(o,i){if(E(o)){var a=o.props,s=a.linkText,l=(0,r._T)(a,["linkText"]),d=o.props.itemKey||i.toString();n.links.push((0,r.pi)((0,r.pi)({headerText:s},l),{itemKey:d})),n.keyToIndexMapping[d]=i,n.keyToTabIdMapping[d]=function(e,t,n,o){return e.getTabId?e.getTabId(n,o):t+"-Tab".concat(o)}(e,t,d,i)}else o&&(0,c.Z)("The children of a Pivot component must be of type PivotItem to be rendered.")})),n},E=function(e){var t;return i.isValidElement(e)&&(null===(t=e.type)||void 0===t?void 0:t.name)===w.M.name},T=i.forwardRef((function(e,t){var n,o=i.useRef(null),l=i.useRef(null),c=(0,a.M)("Pivot"),g=(0,s.G)(e.selectedKey,e.defaultSelectedKey),m=g[0],y=g[1],w=e.componentRef,T=e.theme,D=e.linkSize,R=e.linkFormat,P=e.overflowBehavior,I=e.overflowAriaLabel,M=e.focusZoneProps,O=e.overflowButtonAs,L={"aria-label":e["aria-label"],"aria-labelledby":e["aria-labelledby"]},A=(0,d.pq)(e,d.n7,["aria-label","aria-labelledby"]),z=S(e,c);i.useImperativeHandle(w,(function(){return{focus:function(){var e;null===(e=o.current)||void 0===e||e.focus()}}}));var N=function(e){if(!e)return null;var t=e.itemCount,o=e.itemIcon,r=e.headerText;return i.createElement("span",{className:n.linkContent},void 0!==o&&i.createElement("span",{className:n.icon},i.createElement(k.J,{iconName:o})),void 0!==r&&i.createElement("span",{className:n.text}," ",e.headerText),void 0!==t&&i.createElement("span",{className:n.count}," (",t,")"))},H=function(e,t,o,a){var s,l=t.itemKey,c=t.headerButtonProps,d=t.onRenderItemLink,p=e.keyToTabIdMapping[l],f=o===l;s=d?d(t,N):N(t);var g=t.headerText||"";g+=t.itemCount?" ("+t.itemCount+")":"",g+=t.itemIcon?" xx":"";var m=t.role&&"tab"!==t.role?{role:t.role}:{role:"tab","aria-selected":f};return i.createElement(h.M,(0,r.pi)({},c,m,{id:p,key:l,className:(0,u.i)(a,f&&n.linkIsSelected),onClick:function(e){return F(l,e)},onKeyDown:function(e){return B(l,e)},"aria-label":t.ariaLabel,name:t.headerText,keytipProps:t.keytipProps,"data-content":g}),s)},F=function(e,t){t.preventDefault(),j(e,t)},B=function(e,t){t.which===p.m.enter&&(t.preventDefault(),j(e))},j=function(t,n){var o;if(y(t),z=S(e,c),e.onLinkClick&&z.keyToIndexMapping[t]>=0){var r=z.keyToIndexMapping[t],a=i.Children.toArray(e.children)[r];E(a)&&e.onLinkClick(a,n)}null===(o=l.current)||void 0===o||o.dismissMenu()};n=C(e.styles,{theme:T,linkSize:D,linkFormat:R});var W=function(){return null===(e=m)||void 0!==e&&void 0!==z.keyToIndexMapping[e]?m:z.links.length?z.links[0].itemKey:void 0;var e}(),U=W?z.keyToIndexMapping[W]:0,V=z.links.map((function(e){return H(z,e,W,n.link)})),G=i.useMemo((function(){return{items:[],alignTargetEdge:!0,directionalHint:_.b.bottomRightEdge}}),[]),q=v({onOverflowItemsChanged:function(e,t){t.forEach((function(e){var t=e.ele,n=e.isOverflowing;return t.dataset.isOverflowing="".concat(n)})),G.items=z.links.slice(e).filter((function(e){return e.itemKey!==W})).map((function(t,o){return t.role="menuitem",{key:t.itemKey||"".concat(e+o),onRender:function(){return H(z,t,W,n.linkInMenu)}}}))},rtl:(0,f.zg)(T),pinnedIndex:U}).menuButtonRef,K=O||h.M;return i.createElement("div",(0,r.pi)({ref:t},A),i.createElement(x.k,(0,r.pi)({componentRef:o,role:"tablist"},L,{direction:b.U.horizontal},M,{className:(0,u.i)(n.root,null===M||void 0===M?void 0:M.className)}),V,"menu"===P&&i.createElement(K,{className:(0,u.i)(n.link,n.overflowMenuButton),elementRef:q,componentRef:l,menuProps:G,menuIconProps:{iconName:"More",style:{color:"inherit"}},ariaLabel:I,role:"tab"})),W&&z.links.map((function(t){return(!0===t.alwaysRender||W===t.itemKey)&&function(t,o){if(e.headersOnly||!t)return null;var r=z.keyToIndexMapping[t],a=z.keyToTabIdMapping[t];return i.createElement("div",{role:"tabpanel",hidden:!o,key:t,"aria-hidden":!o,"aria-labelledby":a,className:n.itemContainer},i.Children.toArray(e.children)[r])}(t.itemKey,W===t.itemKey)})))}));T.displayName="Pivot";var D=n(40528),R=n(61910),P={count:"ms-Pivot-count",icon:"ms-Pivot-icon",linkIsSelected:"is-selected",link:"ms-Pivot-link",linkContent:"ms-Pivot-linkContent",root:"ms-Pivot",rootIsLarge:"ms-Pivot--large",rootIsTabs:"ms-Pivot--tabs",text:"ms-Pivot-text",linkInMenu:"ms-Pivot-linkInMenu",overflowMenuButton:"ms-Pivot-overflowMenuButton"},I=function(e,t,n){var o,i,a;void 0===n&&(n=!1);var s=e.linkSize,l=e.linkFormat,c=e.theme,d=c.semanticColors,u=c.fonts,p="large"===s,f="tabs"===l;return[u.medium,{color:d.actionLink,padding:"0 8px",position:"relative",backgroundColor:"transparent",border:0,borderRadius:0,selectors:{":hover":{backgroundColor:d.buttonBackgroundHovered,color:d.buttonTextHovered,cursor:"pointer"},":active":{backgroundColor:d.buttonBackgroundPressed,color:d.buttonTextHovered},":focus":{outline:"none"}}},!n&&[{display:"inline-block",lineHeight:44,height:44,marginRight:8,textAlign:"center",selectors:(o={},o[".".concat(R.G$," &:focus")]={outline:"1px solid ".concat(d.focusBorder)},o[".".concat(R.G$," &:focus:after")]={content:"attr(data-content)",position:"relative",border:0},o[":before"]={backgroundColor:"transparent",bottom:0,content:'""',height:2,left:8,position:"absolute",right:8,transition:"left ".concat(D.D1.durationValue2," ").concat(D.D1.easeFunction2,",\n                        right ").concat(D.D1.durationValue2," ").concat(D.D1.easeFunction2)},o[":after"]={color:"transparent",content:"attr(data-content)",display:"block",fontWeight:D.lq.bold,height:1,overflow:"hidden",visibility:"hidden"},o)},p&&{fontSize:u.large.fontSize},f&&[{marginRight:0,height:44,lineHeight:44,backgroundColor:d.buttonBackground,padding:"0 10px",verticalAlign:"top",selectors:(i={":focus":{outlineOffset:"-2px"}},i[".".concat(R.G$," &:focus::before")]={height:"auto",background:"transparent",transition:"none"},i["&:hover, &:focus"]={color:d.buttonTextCheckedHovered},i["&:active, &:hover"]={color:d.primaryButtonText,backgroundColor:d.primaryButtonBackground},i["&.".concat(t.linkIsSelected)]={backgroundColor:d.primaryButtonBackground,color:d.primaryButtonText,fontWeight:D.lq.regular,selectors:(a={":before":{backgroundColor:"transparent",transition:"none",position:"absolute",top:0,left:0,right:0,bottom:0,content:'""',height:0},":hover":{backgroundColor:d.primaryButtonBackgroundHovered,color:d.primaryButtonText},":active":{backgroundColor:d.primaryButtonBackgroundPressed,color:d.primaryButtonText}},a[D.qJ]=(0,r.pi)({fontWeight:D.lq.semibold,color:"HighlightText",background:"Highlight"},(0,D.xM)()),a)},i[".".concat(R.G$," &.").concat(t.linkIsSelected,":focus")]={outlineColor:d.primaryButtonText},i)}]]]},M=(0,o.z)(T,(function(e){var t,n,o,i,a=e.className,s=e.linkSize,l=e.linkFormat,c=e.theme,d=c.semanticColors,u=c.fonts,p=(0,D.Cn)(P,c),f="large"===s,h="tabs"===l;return{root:[p.root,u.medium,D.Fv,{position:"relative",color:d.link,whiteSpace:"nowrap"},f&&p.rootIsLarge,h&&p.rootIsTabs,a],itemContainer:{selectors:{"&[hidden]":{display:"none"}}},link:(0,r.ev)((0,r.ev)([p.link],I(e,p),!0),[(t={},t["&[data-is-overflowing='true']"]={display:"none"},t)],!1),overflowMenuButton:[p.overflowMenuButton,(n={visibility:"hidden",position:"absolute",right:0},n[".".concat(p.link,"[data-is-overflowing='true'] ~ &")]={visibility:"visible",position:"relative"},n)],linkInMenu:(0,r.ev)((0,r.ev)([p.linkInMenu],I(e,p,!0),!0),[{textAlign:"left",width:"100%",height:36,lineHeight:36}],!1),linkIsSelected:[p.link,p.linkIsSelected,{fontWeight:D.lq.semibold,selectors:(o={":before":{backgroundColor:d.inputBackgroundChecked,selectors:(i={},i[D.qJ]={backgroundColor:"Highlight"},i)},":hover::before":{left:0,right:0}},o[D.qJ]={color:"Highlight"},o)}],linkContent:[p.linkContent,{flex:"0 1 100%",selectors:{"& > * ":{marginLeft:4},"& > *:first-child":{marginLeft:0}}}],text:[p.text,{display:"inline-block",verticalAlign:"top"}],count:[p.count,{display:"inline-block",verticalAlign:"top"}],icon:p.icon}}),void 0,{scope:"Pivot"})},46933:function(e,t,n){"use strict";n.d(t,{M:function(){return l}});var o=n(75971),r=n(72791),i=n(8877),a=n(23983),s=n(15863),l=function(e){function t(t){var n=e.call(this,t)||this;return(0,i.l)(n),(0,a.b)("PivotItem",t,{linkText:"headerText"}),n}return(0,o.ZT)(t,e),t.prototype.render=function(){return r.createElement("div",(0,o.pi)({},(0,s.pq)(this.props,s.n7)),this.props.children)},t}(r.Component)},47535:function(e,t,n){"use strict";n.d(t,{F5:function(){return i},oW:function(){return o},xC:function(){return a}});var o,r,i="change",a="items-change";!function(e){e[e.none=0]="none",e[e.single=1]="single",e[e.multiple=2]="multiple"}(o||(o={})),function(e){e[e.horizontal=0]="horizontal",e[e.vertical=1]="vertical"}(r||(r={}))},84806:function(e){var t;t=function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=82)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.util=t.PresentationMode=void 0,t.createDom=a,t.hasClass=s,t.addClass=l,t.removeClass=c,t.toggleClass=d,t.findDom=u,t.padStart=p,t.format=f,t.event=h,t.typeOf=g,t.deepCopy=m,t.getBgImage=y,t.copyDom=v,t._setInterval=x,t._clearInterval=b,t.createImgBtn=_,t.isWeiXin=k,t.isUc=w,t.computeWatchDur=C,t.offInDestroy=S,t.on=E,t.once=T,t.getBuffered2=D,t.checkIsBrowser=R,t.setStyle=P,t.checkWebkitSetPresentationMode=function(e){return"function"===typeof e.webkitSetPresentationMode};var o,r=n(7),i=(o=r)&&o.__esModule?o:{default:o};function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=document.createElement(e);return r.className=o,r.innerHTML=t,Object.keys(n).forEach((function(t){var o=t,i=n[t];"video"===e||"audio"===e?i&&r.setAttribute(o,i):r.setAttribute(o,i)})),r}function s(e,t){return!!e&&(e.classList?Array.prototype.some.call(e.classList,(function(e){return e===t})):!!e.className&&!!e.className.match(new RegExp("(\\s|^)"+t+"(\\s|$)")))}function l(e,t){e&&(e.classList?t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)})):s(e,t)||(e.className+=" "+t))}function c(e,t){e&&(e.classList?t.split(/\s+/g).forEach((function(t){e.classList.remove(t)})):s(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ")})))}function d(e,t){e&&t.split(/\s+/g).forEach((function(t){s(e,t)?c(e,t):l(e,t)}))}function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=arguments[1],n=void 0;try{n=e.querySelector(t)}catch(o){0===t.indexOf("#")&&(n=e.getElementById(t.slice(1)))}return n}function p(e,t,n){for(var o=String(n),r=t>>0,i=Math.ceil(r/o.length),a=[],s=String(e);i--;)a.push(o);return a.join("").substring(0,r-s.length)+s}function f(e){if(window.isNaN(e))return"";var t=p(Math.floor(e/3600),2,0),n=p(Math.floor((e-3600*t)/60),2,0),o=p(Math.floor(e-3600*t-60*n),2,0);return("00"===t?[n,o]:[t,n,o]).join(":")}function h(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement}function g(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]}function m(e,t){if("Object"===g(t)&&"Object"===g(e))return Object.keys(t).forEach((function(n){"Object"!==g(t[n])||t[n]instanceof Node?"Array"===g(t[n])?e[n]="Array"===g(e[n])?e[n].concat(t[n]):t[n]:e[n]=t[n]:e[n]?m(e[n],t[n]):e[n]=t[n]})),e}function y(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var n=document.createElement("a");return n.href=t.replace(/url\("|"\)/g,""),n.href}function v(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""}function x(e,t,n,o){e._interval[t]||(e._interval[t]=setInterval(n.bind(e),o))}function b(e,t){clearInterval(e._interval[t]),e._interval[t]=null}function _(e,t,n,o){var r=a("xg-"+e,"",{},"xgplayer-"+e+"-img");if(r.style.backgroundImage='url("'+t+'")',n&&o){var i=void 0,s=void 0,l=void 0;["px","rem","em","pt","dp","vw","vh","vm","%"].every((function(e){return!(n.indexOf(e)>-1&&o.indexOf(e)>-1)||(i=Number(n.slice(0,n.indexOf(e)).trim()),s=Number(o.slice(0,o.indexOf(e)).trim()),l=e,!1)})),r.style.width=""+i+l,r.style.height=""+s+l,r.style.backgroundSize=""+i+l+" "+s+l,r.style.margin="start"===e?"-"+s/2+l+" auto auto -"+i/2+l:"auto 5px auto 5px"}return r}function k(){return window.navigator.userAgent.toLowerCase().indexOf("micromessenger")>-1}function w(){return window.navigator.userAgent.toLowerCase().indexOf("ucbrowser")>-1}function C(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=0;n<e.length;n++)if(!(!e[n].end||e[n].begin<0||e[n].end<0||e[n].end<e[n].begin))if(t.length<1)t.push({begin:e[n].begin,end:e[n].end});else for(var o=0;o<t.length;o++){var r=e[n].begin,i=e[n].end;if(i<t[o].begin){t.splice(o,0,{begin:r,end:i});break}if(!(r>t[o].end)){var a=t[o].begin,s=t[o].end;t[o].begin=Math.min(r,a),t[o].end=Math.max(i,s);break}if(o>t.length-2){t.push({begin:r,end:i});break}}for(var l=0,c=0;c<t.length;c++)l+=t[c].end-t[c].begin;return l}function S(e,t,n,o){e.once(o,(function r(){e.off(t,n),e.off(o,r)}))}function E(e,t,n,o){o?(e.on(t,n),S(e,t,n,o)):e.on(t,(function o(r){n(r),e.off(t,o)}))}function T(e,t,n,o){o?(e.once(t,n),S(e,t,n,o)):e.once(t,(function o(r){n(r),e.off(t,o)}))}function D(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,n=[],o=0;o<e.length;o++)n.push({start:e.start(o)<.5?0:e.start(o),end:e.end(o)});n.sort((function(e,t){var n=e.start-t.start;return n||t.end-e.end}));var r=[];if(t)for(var a=0;a<n.length;a++){var s=r.length;if(s){var l=r[s-1].end;n[a].start-l<t?n[a].end>l&&(r[s-1].end=n[a].end):r.push(n[a])}else r.push(n[a])}else r=n;return new i.default(r)}function R(){return!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement)}function P(e,t,n){var o=e.style;try{o[t]=n}catch(r){o.setProperty(t,n)}}t.PresentationMode={PIP:"picture-in-picture",INLINE:"inline",FULLSCREEN:"fullscreen"},t.util={createDom:a,hasClass:s,addClass:l,removeClass:c,toggleClass:d,findDom:u,padStart:p,format:f,event:h,typeOf:g,deepCopy:m,getBgImage:y,copyDom:v,setInterval:x,clearInterval:b,createImgBtn:_,isWeiXin:k,isUc:w,computeWatchDur:C,offInDestroy:S,on:E,once:T,getBuffered2:D,checkIsBrowser:R,setStyle:P}},function(e,t,n){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n,o=e[1]||"",r=e[3];if(!r)return o;if(t&&"function"===typeof btoa){var i=(n=r,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */"),a=r.sources.map((function(e){return"/*# sourceURL="+r.sourceRoot+e+" */"}));return[o].concat(a).concat([i]).join("\n")}return[o].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n})).join("")},t.i=function(e,n){"string"===typeof e&&(e=[[null,e,""]]);for(var o={},r=0;r<this.length;r++){var i=this[r][0];"number"===typeof i&&(o[i]=!0)}for(r=0;r<e.length;r++){var a=e[r];"number"===typeof a[0]&&o[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),t.push(a))}},t}},function(e,t,n){var o={},r=function(e){var t;return function(){return"undefined"===typeof t&&(t=e.apply(this,arguments)),t}}((function(){return window&&document&&document.all&&!window.atob})),i=function(e){return document.querySelector(e)},a=function(e){var t={};return function(e){if("function"===typeof e)return e();if("undefined"===typeof t[e]){var n=i.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(o){n=null}t[e]=n}return t[e]}}(),s=null,l=0,c=[],d=n(37);function u(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=o[r.id];if(i){i.refs++;for(var a=0;a<i.parts.length;a++)i.parts[a](r.parts[a]);for(;a<r.parts.length;a++)i.parts.push(y(r.parts[a],t))}else{var s=[];for(a=0;a<r.parts.length;a++)s.push(y(r.parts[a],t));o[r.id]={id:r.id,refs:1,parts:s}}}}function p(e,t){for(var n=[],o={},r=0;r<e.length;r++){var i=e[r],a=t.base?i[0]+t.base:i[0],s={css:i[1],media:i[2],sourceMap:i[3]};o[a]?o[a].parts.push(s):n.push(o[a]={id:a,parts:[s]})}return n}function f(e,t){var n=a(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var o=c[c.length-1];if("top"===e.insertAt)o?o.nextSibling?n.insertBefore(t,o.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),c.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!==typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var r=a(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,r)}}function h(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=c.indexOf(e);t>=0&&c.splice(t,1)}function g(e){var t=document.createElement("style");return e.attrs.type="text/css",m(t,e.attrs),f(e,t),t}function m(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}function y(e,t){var n,o,r,i;if(t.transform&&e.css){if(!(i=t.transform(e.css)))return function(){};e.css=i}if(t.singleton){var a=l++;n=s||(s=g(t)),o=x.bind(null,n,a,!1),r=x.bind(null,n,a,!0)}else e.sourceMap&&"function"===typeof URL&&"function"===typeof URL.createObjectURL&&"function"===typeof URL.revokeObjectURL&&"function"===typeof Blob&&"function"===typeof btoa?(n=function(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",m(t,e.attrs),f(e,t),t}(t),o=_.bind(null,n,t),r=function(){h(n),n.href&&URL.revokeObjectURL(n.href)}):(n=g(t),o=b.bind(null,n),r=function(){h(n)});return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else r()}}e.exports=function(e,t){if("undefined"!==typeof DEBUG&&DEBUG&&"object"!==typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"===typeof t.attrs?t.attrs:{},t.singleton||"boolean"===typeof t.singleton||(t.singleton=r()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=p(e,t);return u(n,t),function(e){for(var r=[],i=0;i<n.length;i++){var a=n[i];(s=o[a.id]).refs--,r.push(s)}for(e&&u(p(e,t),t),i=0;i<r.length;i++){var s;if(0===(s=r[i]).refs){for(var l=0;l<s.parts.length;l++)s.parts[l]();delete o[s.id]}}}};var v=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}();function x(e,t,n,o){var r=n?"":o.css;if(e.styleSheet)e.styleSheet.cssText=v(t,r);else{var i=document.createTextNode(r),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function b(e,t){var n=t.css,o=t.media;if(o&&e.setAttribute("media",o),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function _(e,t,n){var o=n.css,r=n.sourceMap,i=void 0===t.convertToAbsoluteUrls&&r;(t.convertToAbsoluteUrls||i)&&(o=d(o)),r&&(o+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */");var a=new Blob([o],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(a),s&&URL.revokeObjectURL(s)}},function(e,t,n){"use strict";var o=n(25)();e.exports=function(e){return e!==o&&null!==e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(8),r={network:{code:1,msg:"\u89c6\u9891\u4e0b\u8f7d\u9519\u8bef",remark:"\u53ea\u8981\u89c6\u9891\u4e0b\u8f7d\u9519\u8bef\u5c31\u4f7f\u7528\u6b64\u7c7b\u578b\uff0c\u65e0\u8bba\u662fvideo\u672c\u8eab\u7684\u8d85\u65f6\u8fd8\u662fxhr\u7684\u5206\u6bb5\u8bf7\u6c42\u8d85\u65f6\u6216\u8005\u8d44\u6e90\u4e0d\u5b58\u5728"},mse:{code:2,msg:"\u6d41\u8ffd\u52a0\u9519\u8bef",remark:"\u8ffd\u52a0\u6d41\u7684\u65f6\u5019\u5982\u679c\u7c7b\u578b\u4e0d\u5bf9\u3001\u65e0\u6cd5\u88ab\u6b63\u786e\u89e3\u7801\u5219\u4f1a\u89e6\u53d1\u6b64\u7c7b\u9519\u8bef"},parse:{code:3,msg:"\u89e3\u6790\u9519\u8bef",remark:"mp4\u3001hls\u3001flv\u6211\u4eec\u90fd\u662f\u4f7f\u7528js\u8fdb\u884c\u683c\u5f0f\u89e3\u6790\uff0c\u5982\u679c\u89e3\u6790\u5931\u8d25\u5219\u4f1a\u89e6\u53d1\u6b64\u7c7b\u9519\u8bef"},format:{code:4,msg:"\u683c\u5f0f\u9519\u8bef",remark:"\u5982\u679c\u6d4f\u89c8\u5668\u4e0d\u652f\u6301\u7684\u683c\u5f0f\u5bfc\u81f4\u64ad\u653e\u9519\u8bef"},decoder:{code:5,msg:"\u89e3\u7801\u9519\u8bef",remark:"\u6d4f\u89c8\u5668\u89e3\u7801\u5f02\u5e38\u4f1a\u629b\u51fa\u6b64\u7c7b\u578b\u9519\u8bef"},runtime:{code:6,msg:"\u8bed\u6cd5\u9519\u8bef",remark:"\u64ad\u653e\u5668\u8bed\u6cd5\u9519\u8bef"},timeout:{code:7,msg:"\u64ad\u653e\u8d85\u65f6",remark:"\u64ad\u653e\u8fc7\u7a0b\u4e2d\u65e0\u6cd5\u6b63\u5e38\u8bf7\u6c42\u4e0b\u4e00\u4e2a\u5206\u6bb5\u5bfc\u81f4\u64ad\u653e\u4e2d\u65ad"},other:{code:8,msg:"\u5176\u4ed6\u9519\u8bef",remark:"\u4e0d\u53ef\u77e5\u7684\u9519\u8bef\u6216\u88ab\u5ffd\u7565\u7684\u9519\u8bef\u7c7b\u578b"}};t.default=function e(t,n,i,a,s,l,c,d){var u=arguments.length>8&&void 0!==arguments[8]?arguments[8]:{line:"",handle:"",msg:"",version:""},p=arguments[9],f=arguments[10];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var h={};if(arguments.length>1)h.playerVersion=o.version,h.errorType=t,h.domain=document.domain,h.duration=i,h.currentTime=n,h.networkState=a,h.readyState=s,h.currentSrc=c,h.src=l,h.ended=d,h.errd=u,h.ex=(r[t]||{}).msg,h.errorCode=p,h.mediaError=f;else{var g=arguments[0];Object.keys(g).map((function(e){h[e]=g[e]})),h.ex=(g.type&&r[g.type]||{}).msg}return h},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o={};Object.defineProperty(o,"device",{get:function(){return o.os.isPc?"pc":"mobile"}}),Object.defineProperty(o,"browser",{get:function(){var e=navigator.userAgent.toLowerCase(),t={ie:/rv:([\d.]+)\) like gecko/,firfox:/firefox\/([\d.]+)/,chrome:/chrome\/([\d.]+)/,opera:/opera.([\d.]+)/,safari:/version\/([\d.]+).*safari/};return[].concat(Object.keys(t).filter((function(n){return t[n].test(e)})))[0]||""}}),Object.defineProperty(o,"os",{get:function(){var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),n=/(?:SymbianOS)/.test(e)||t,o=/(?:Android)/.test(e),r=/(?:Firefox)/.test(e),i=/(?:iPad|PlayBook)/.test(e)||o&&!/(?:Mobile)/.test(e)||r&&/(?:Tablet)/.test(e),a=/(?:iPhone)/.test(e)&&!i;return{isTablet:i,isPhone:a,isAndroid:o,isPc:!a&&!o&&!n&&!i,isSymbian:n,isWindowsPhone:t,isFireFox:r}}}),t.default=o,e.exports=t.default},function(e,t,n){"use strict";e.exports=function(e){return void 0!==e&&null!==e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.bufferedList=t}return o(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}();t.default=r,e.exports=t.default},function(e){e.exports=JSON.parse('{"version":"2.32.5"}')},function(e,t,n){"use strict";var o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=function e(t,n,o){null===t&&(t=Function.prototype);var r=Object.getOwnPropertyDescriptor(t,n);if(void 0===r){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,n,o)}if("value"in r)return r.value;var a=r.get;return void 0!==a?a.call(o):void 0},a=h(n(12)),s=n(0),l=h(n(5)),c=h(n(7)),d=h(n(4)),u=h(n(32)),p=h(n(10));n(35);var f=n(8);function h(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==("undefined"===typeof t?"undefined":o(t))&&"function"!==typeof t?e:t}var m=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=g(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));if(n.config=(0,s.deepCopy)({width:600,height:337.5,ignores:[],whitelist:[],lang:(document.documentElement.getAttribute("lang")||navigator.language||"zh-cn").toLocaleLowerCase(),inactive:3e3,volume:.6,controls:!0,controlsList:["nodownload"]},e),n.version=f.version,n.userTimer=null,n.waitTimer=null,n.history=[],n.isProgressMoving=!1,n.root=(0,s.findDom)(document,"#"+n.config.id),n.controls=(0,s.createDom)("xg-controls","",{unselectable:"on",onselectstart:"return false"},"xgplayer-controls"),n.config.isShowControl&&(n.controls.style.display="none"),!n.root){var o=n.config.el;if(!o||1!==o.nodeType)return n.emit("error",new d.default({type:"use",errd:{line:45,handle:"Constructor",msg:"container id can't be empty"},vid:n.config.vid})),console.error("container id can't be empty"),g(n,!1);n.root=o}if((0,s.addClass)(n.root,"xgplayer xgplayer-"+l.default.device+" xgplayer-nostart xgplayer-pause "+(n.config.controls?"":"xgplayer-no-controls")),n.root.appendChild(n.controls),n.config.fluid?(n.root.style["max-width"]="100%",n.root.style.width="100%",n.root.style.height="0",n.root.style["padding-top"]=100*n.config.height/n.config.width+"%",n.video.style.position="absolute",n.video.style.top="0",n.video.style.left="0"):(n.config.width&&("number"!==typeof n.config.width?n.root.style.width=n.config.width:n.root.style.width=n.config.width+"px"),n.config.height&&("number"!==typeof n.config.height?n.root.style.height=n.config.height:n.root.style.height=n.config.height+"px")),n.config.execBeforePluginsCall&&n.config.execBeforePluginsCall.forEach((function(e){e.call(n,n)})),n.config.closeI18n||t.install(p.default.name,p.default.method),n.config.controlStyle&&"String"===(0,s.typeOf)(n.config.controlStyle)){var r=n;fetch(r.config.controlStyle,{method:"GET",headers:{Accept:"application/json"}}).then((function(e){e.ok&&e.json().then((function(e){for(var t in e)e.hasOwnProperty(t)&&(r.config[t]=e[t]);r.pluginsCall()}))})).catch((function(e){console.log("Fetch\u9519\u8bef:"+e)}))}else n.pluginsCall();n.config.controlPlugins&&t.controlsRun(n.config.controlPlugins,n),n.ev.forEach((function(e){var t=Object.keys(e)[0],o=n[e[t]];o&&n.on(t,o)})),["focus","blur"].forEach((function(e){n.on(e,n["on"+e.charAt(0).toUpperCase()+e.slice(1)])}));var i=n;return n.mousemoveFunc=function(){i.emit("focus"),i.config.closeFocusVideoFocus||i.video.focus()},n.root.addEventListener("mousemove",n.mousemoveFunc),n.playFunc=function(){i.emit("focus"),i.config.closePlayVideoFocus||i.video.focus()},i.once("play",n.playFunc),n.getVideoSize=function(){if(this.video.videoWidth&&this.video.videoHeight){var e=i.root.getBoundingClientRect();"auto"===i.config.fitVideoSize?e.width/e.height>this.video.videoWidth/this.video.videoHeight?i.root.style.height=this.video.videoHeight/this.video.videoWidth*e.width+"px":i.root.style.width=this.video.videoWidth/this.video.videoHeight*e.height+"px":"fixWidth"===i.config.fitVideoSize?i.root.style.height=this.video.videoHeight/this.video.videoWidth*e.width+"px":"fixHeight"===i.config.fitVideoSize&&(i.root.style.width=this.video.videoWidth/this.video.videoHeight*e.height+"px")}},i.once("loadeddata",n.getVideoSize),setTimeout((function(){n.emit("ready"),n.isReady=!0}),0),n.config.videoInit&&(0,s.hasClass)(n.root,"xgplayer-nostart")&&n.start(),i.config.rotate&&(i.on("requestFullscreen",n.updateRotateDeg),i.on("exitFullscreen",n.updateRotateDeg)),i.once("destroy",(function e(){i.root.removeEventListener("mousemove",i.mousemoveFunc),i.off("destroy",e)})),n}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":o(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"attachVideo",value:function(){var e=this;this.video&&1===this.video.nodeType&&this.root.insertBefore(this.video,this.root.firstChild),setTimeout((function(){e.emit("complete"),e.danmu&&"function"===typeof e.danmu.resize&&e.danmu.resize()}),1)}},{key:"start",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.url;if(this.video){var n=this;t&&""!==t?(this.canPlayFunc=function(){n.off("canplay",n.canPlayFunc);var e=n.video.play();void 0!==e&&e&&e.then((function(){n.emit("autoplay started")})).catch((function(){n.emit("autoplay was prevented"),(0,s.addClass)(n.root,"xgplayer-is-autoplay")}))},"Array"!==(0,s.typeOf)(t)?"String"===(0,s.typeOf)(t)&&t.indexOf("blob:")>-1&&t===this.video.src||(this.video.src=t):t.forEach((function(t){e.video.appendChild((0,s.createDom)("source","",{src:""+t.src,type:""+(t.type||"")}))})),this.config.autoplay&&(l.default.os.isPhone?this.canPlayFunc():this.on("canplay",this.canPlayFunc)),this.config.disableStartLoad||this.video.load(),this.attachVideo()):this.emit("urlNull")}}},{key:"reload",value:function(){this.video.load(),this.reloadFunc=function(){var e=this.play();void 0!==e&&e&&e.catch((function(e){}))},this.once("loadeddata",this.reloadFunc)}},{key:"destroy",value:function(){var e=this,n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],o=this;for(var r in clearInterval(this.bulletResizeTimer),this._interval)clearInterval(this._interval[r]),this._interval[r]=null;this.checkTimer&&clearInterval(this.checkTimer),this.waitTimer&&clearTimeout(this.waitTimer),this.ev.forEach((function(t){var n=Object.keys(t)[0],o=e[t[n]];o&&e.off(n,o)})),this.loadeddataFunc&&this.off("loadeddata",this.loadeddataFunc),this.reloadFunc&&this.off("loadeddata",this.reloadFunc),this.replayFunc&&this.off("play",this.replayFunc),this.playFunc&&this.off("play",this.playFunc),this.getVideoSize&&this.off("loadeddata",this.getVideoSize),["focus","blur"].forEach((function(t){e.off(t,e["on"+t.charAt(0).toUpperCase()+t.slice(1)])})),this.config.keyShortcut&&"on"!==this.config.keyShortcut||["video","controls"].forEach((function(t){e[t]&&e[t].removeEventListener("keydown",(function(e){o.onKeydown(e,o)}))})),function(){if(this.emit("destroy"),this.video.removeAttribute("src"),this.video.load(),n){this.root.innerHTML="";var e=this.root.className.split(" ");e.length>0?this.root.className=e.filter((function(e){return e.indexOf("xgplayer")<0})).join(" "):this.root.className=""}for(var t in this)delete this[t];(0,u.default)(this)}.call(this),i(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"destroy",this).call(this)}},{key:"replay",value:function(){var e=this._replay;if((0,s.removeClass)(this.root,"xgplayer-ended"),l.default.browser.indexOf("ie")>-1&&(this.emit("play"),this.emit("playing")),e&&e instanceof Function)e();else{this.currentTime=0;var t=this.play();void 0!==t&&t&&t.catch((function(e){}))}}},{key:"userGestureTrigEvent",value:function(e,t){var n=this,o=function(e,t){n.emit(e,t)};this.config.userGestureEventMiddleware&&"function"===typeof this.config.userGestureEventMiddleware[e]?this.config.userGestureEventMiddleware[e].call(this,this,e,t,o):o.call(this,e,t)}},{key:"pluginsCall",value:function(){var e=this;t.plugins.s_i18n&&t.plugins.s_i18n.call(this,this);var n=this;if(t.plugins){var o=this.config.ignores;Object.keys(t.plugins).forEach((function(r){var i=t.plugins[r];i&&"function"===typeof i?o.some((function(e){return r===e||r==="s_"+e}))||"s_i18n"===r||(["pc","tablet","mobile"].some((function(e){return e===r}))?r===l.default.device&&setTimeout((function(){n.video&&i.call(n,n)}),0):i.call(e,e)):console.warn("plugin name",r,"is invalid")}))}}},{key:"onFocus",value:function(){var e=this;(0,s.hasClass)(this.root,"xgplayer-inactive")&&e.emit("controlShow"),(0,s.removeClass)(this.root,"xgplayer-inactive"),e.userTimer&&clearTimeout(e.userTimer),e.userTimer=setTimeout((function(){e.emit("blur")}),e.config.inactive)}},{key:"onBlur",value:function(){!this.config.enablePausedInactive&&this.paused||this.ended||this.config.closeInactive||((0,s.hasClass)(this.root,"xgplayer-inactive")||this.emit("controlHide"),(0,s.addClass)(this.root,"xgplayer-inactive"))}},{key:"onPlay",value:function(){(0,s.addClass)(this.root,"xgplayer-isloading"),(0,s.addClass)(this.root,"xgplayer-playing"),(0,s.removeClass)(this.root,"xgplayer-pause")}},{key:"onPause",value:function(){(0,s.addClass)(this.root,"xgplayer-pause"),this.userTimer&&clearTimeout(this.userTimer),this.emit("focus")}},{key:"onEnded",value:function(){(0,s.addClass)(this.root,"xgplayer-ended"),(0,s.removeClass)(this.root,"xgplayer-playing")}},{key:"onSeeking",value:function(){this.isSeeking=!0,this.onWaiting()}},{key:"onSeeked",value:function(){var e=this;this.once("timeupdate",(function(){e.isSeeking=!1})),this.waitTimer&&clearTimeout(this.waitTimer),(0,s.removeClass)(this.root,"xgplayer-isloading")}},{key:"onWaiting",value:function(){var e=this;e.waitTimer&&clearTimeout(e.waitTimer),e.checkTimer&&(clearInterval(e.checkTimer),e.checkTimer=null);var t=e.currentTime;e.waitTimer=setTimeout((function(){(0,s.addClass)(e.root,"xgplayer-isloading"),e.checkTimer=setInterval((function(){e.currentTime!==t&&((0,s.removeClass)(e.root,"xgplayer-isloading"),clearInterval(e.checkTimer),e.checkTimer=null)}),1e3)}),500)}},{key:"onPlaying",value:function(){this.paused||(this.isSeeking=!1,this.waitTimer&&clearTimeout(this.waitTimer),(0,s.removeClass)(this.root,"xgplayer-isloading xgplayer-nostart xgplayer-pause xgplayer-ended xgplayer-is-error xgplayer-replay"),(0,s.addClass)(this.root,"xgplayer-playing"))}}],[{key:"install",value:function(e,n){(0,s.checkIsBrowser)()&&(t.plugins||(t.plugins={}),t.plugins[e]||(t.plugins[e]=n))}},{key:"installAll",value:function(e){for(var n=0;n<e.length;n++)t.install(e[n].name,e[n].method)}},{key:"use",value:function(e,n){t.plugins||(t.plugins={}),t.plugins[e]=n}},{key:"useAll",value:function(e){for(var n in e)t.use(e[n].name,e[n].method)}},{key:"controlsRun",value:function(e,t){e.forEach((function(e){e.method.call(t)}))}}]),t}(a.default);m.util=s.util,m.sniffer=l.default,m.Errors=d.default,m.XgplayerTimeRange=c.default,t.default=m,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"s_i18n",method:function(){var e=this,t={en:{HAVE_NOTHING:"There is no information on whether audio/video is ready",HAVE_METADATA:"Audio/video metadata is ready ",HAVE_CURRENT_DATA:"Data about the current play location is available, but there is not enough data to play the next frame/millisecond",HAVE_FUTURE_DATA:"Current and at least one frame of data is available",HAVE_ENOUGH_DATA:"The available data is sufficient to start playing",NETWORK_EMPTY:"Audio/video has not been initialized",NETWORK_IDLE:"Audio/video is active and has been selected for resources, but no network is used",NETWORK_LOADING:"The browser is downloading the data",NETWORK_NO_SOURCE:"No audio/video source was found",MEDIA_ERR_ABORTED:"The fetch process is aborted by the user",MEDIA_ERR_NETWORK:"An error occurred while downloading",MEDIA_ERR_DECODE:"An error occurred while decoding",MEDIA_ERR_SRC_NOT_SUPPORTED:"Audio/video is not supported",REPLAY:"Replay",ERROR:"Network is offline",PLAY_TIPS:"Play",PAUSE_TIPS:"Pause",PLAYNEXT_TIPS:"Play next",DOWNLOAD_TIPS:"Download",ROTATE_TIPS:"Rotate",RELOAD_TIPS:"Reload",FULLSCREEN_TIPS:"Fullscreen",EXITFULLSCREEN_TIPS:"Exit fullscreen",CSSFULLSCREEN_TIPS:"Cssfullscreen",EXITCSSFULLSCREEN_TIPS:"Exit cssfullscreen",TEXTTRACK:"Caption",PIP:"Pip",MINIPLAYER:"Miniplayer",SCREENSHOT:"Screenshot",LIVE:"LIVE",OFF:"Off",MINIPLAYER_DRAG:"Click and hold to drag",AIRPLAY_TIPS:"Airplay"},"zh-cn":{HAVE_NOTHING:"\u6ca1\u6709\u5173\u4e8e\u97f3\u9891/\u89c6\u9891\u662f\u5426\u5c31\u7eea\u7684\u4fe1\u606f",HAVE_METADATA:"\u97f3\u9891/\u89c6\u9891\u7684\u5143\u6570\u636e\u5df2\u5c31\u7eea",HAVE_CURRENT_DATA:"\u5173\u4e8e\u5f53\u524d\u64ad\u653e\u4f4d\u7f6e\u7684\u6570\u636e\u662f\u53ef\u7528\u7684\uff0c\u4f46\u6ca1\u6709\u8db3\u591f\u7684\u6570\u636e\u6765\u64ad\u653e\u4e0b\u4e00\u5e27/\u6beb\u79d2",HAVE_FUTURE_DATA:"\u5f53\u524d\u53ca\u81f3\u5c11\u4e0b\u4e00\u5e27\u7684\u6570\u636e\u662f\u53ef\u7528\u7684",HAVE_ENOUGH_DATA:"\u53ef\u7528\u6570\u636e\u8db3\u4ee5\u5f00\u59cb\u64ad\u653e",NETWORK_EMPTY:"\u97f3\u9891/\u89c6\u9891\u5c1a\u672a\u521d\u59cb\u5316",NETWORK_IDLE:"\u97f3\u9891/\u89c6\u9891\u662f\u6d3b\u52a8\u7684\u4e14\u5df2\u9009\u53d6\u8d44\u6e90\uff0c\u4f46\u5e76\u672a\u4f7f\u7528\u7f51\u7edc",NETWORK_LOADING:"\u6d4f\u89c8\u5668\u6b63\u5728\u4e0b\u8f7d\u6570\u636e",NETWORK_NO_SOURCE:"\u672a\u627e\u5230\u97f3\u9891/\u89c6\u9891\u6765\u6e90",MEDIA_ERR_ABORTED:"\u53d6\u56de\u8fc7\u7a0b\u88ab\u7528\u6237\u4e2d\u6b62",MEDIA_ERR_NETWORK:"\u5f53\u4e0b\u8f7d\u65f6\u53d1\u751f\u9519\u8bef",MEDIA_ERR_DECODE:"\u5f53\u89e3\u7801\u65f6\u53d1\u751f\u9519\u8bef",MEDIA_ERR_SRC_NOT_SUPPORTED:"\u4e0d\u652f\u6301\u7684\u97f3\u9891/\u89c6\u9891\u683c\u5f0f",REPLAY:"\u91cd\u64ad",ERROR:"\u7f51\u7edc\u8fde\u63a5\u4f3c\u4e4e\u51fa\u73b0\u4e86\u95ee\u9898",PLAY_TIPS:"\u64ad\u653e",PAUSE_TIPS:"\u6682\u505c",PLAYNEXT_TIPS:"\u4e0b\u4e00\u96c6",DOWNLOAD_TIPS:"\u4e0b\u8f7d",ROTATE_TIPS:"\u65cb\u8f6c",RELOAD_TIPS:"\u91cd\u65b0\u8f7d\u5165",FULLSCREEN_TIPS:"\u8fdb\u5165\u5168\u5c4f",EXITFULLSCREEN_TIPS:"\u9000\u51fa\u5168\u5c4f",CSSFULLSCREEN_TIPS:"\u8fdb\u5165\u6837\u5f0f\u5168\u5c4f",EXITCSSFULLSCREEN_TIPS:"\u9000\u51fa\u6837\u5f0f\u5168\u5c4f",TEXTTRACK:"\u5b57\u5e55",PIP:"\u753b\u4e2d\u753b",MINIPLAYER:"\u8ff7\u4f60\u64ad\u653e\u5668",SCREENSHOT:"\u622a\u56fe",LIVE:"\u6b63\u5728\u76f4\u64ad",OFF:"\u5173\u95ed",MINIPLAYER_DRAG:"\u70b9\u51fb\u6309\u4f4f\u53ef\u62d6\u52a8\u89c6\u9891",AIRPLAY_TIPS:"\u9694\u7a7a\u64ad\u653e"},"zh-hk":{HAVE_NOTHING:"\u6c92\u6709\u95dc\u65bc\u97f3\u983b/\u8996\u983b\u662f\u5426\u5c31\u7dd2\u7684\u4fe1\u606f",HAVE_METADATA:"\u97f3\u983b/\u8996\u983b\u7684\u5143\u6578\u64da\u5df2\u5c31\u7dd2",HAVE_CURRENT_DATA:"\u95dc\u65bc\u7576\u524d\u64ad\u653e\u4f4d\u7f6e\u7684\u6578\u64da\u662f\u53ef\u7528\u7684\uff0c\u4f46\u6c92\u6709\u8db3\u5920\u7684\u6578\u64da\u4f86\u64ad\u653e\u4e0b\u58f9\u5e40/\u6beb\u79d2",HAVE_FUTURE_DATA:"\u7576\u524d\u53ca\u81f3\u5c11\u4e0b\u58f9\u5e40\u7684\u6578\u64da\u662f\u53ef\u7528\u7684",HAVE_ENOUGH_DATA:"\u53ef\u7528\u6578\u64da\u8db3\u4ee5\u958b\u59cb\u64ad\u653e",NETWORK_EMPTY:"\u97f3\u983b/\u8996\u983b\u5c1a\u672a\u521d\u59cb\u5316",NETWORK_IDLE:"\u97f3\u983b/\u8996\u983b\u662f\u6d3b\u52d5\u7684\u4e14\u5df2\u9078\u53d6\u8cc7\u6e90\uff0c\u4f46\u4e26\u672a\u4f7f\u7528\u7db2\u7d61",NETWORK_LOADING:"\u700f\u89bd\u5668\u6b63\u5728\u4e0b\u8f09\u6578\u64da",NETWORK_NO_SOURCE:"\u672a\u627e\u5230\u97f3\u983b/\u8996\u983b\u4f86\u6e90",MEDIA_ERR_ABORTED:"\u53d6\u56de\u904e\u7a0b\u88ab\u7528\u6236\u4e2d\u6b62",MEDIA_ERR_NETWORK:"\u7576\u4e0b\u8f09\u6642\u767c\u751f\u932f\u8aa4",MEDIA_ERR_DECODE:"\u7576\u89e3\u78bc\u6642\u767c\u751f\u932f\u8aa4",MEDIA_ERR_SRC_NOT_SUPPORTED:"\u4e0d\u652f\u6301\u7684\u97f3\u983b/\u8996\u983b\u683c\u5f0f",REPLAY:"\u91cd\u64ad",ERROR:"\u7db2\u7d61\u9023\u63a5\u4f3c\u4e4e\u51fa\u73fe\u4e86\u554f\u984c",PLAY_TIPS:"\u64ad\u653e",PAUSE_TIPS:"\u66ab\u505c",PLAYNEXT_TIPS:"\u4e0b\u58f9\u96c6",DOWNLOAD_TIPS:"\u4e0b\u8f09",ROTATE_TIPS:"\u65cb\u8f49",RELOAD_TIPS:"\u91cd\u65b0\u8f09\u5165",FULLSCREEN_TIPS:"\u9032\u5165\u5168\u5c4f",EXITFULLSCREEN_TIPS:"\u9000\u51fa\u5168\u5c4f",CSSFULLSCREEN_TIPS:"\u9032\u5165\u6a23\u5f0f\u5168\u5c4f",EXITCSSFULLSCREEN_TIPS:"\u9000\u51fa\u6a23\u5f0f\u5168\u5c4f",TEXTTRACK:"\u5b57\u5e55",PIP:"\u756b\u4e2d\u756b",MINIPLAYER:"\u8ff7\u59b3\u64ad\u653e\u5668",SCREENSHOT:"\u622a\u5716",LIVE:"\u6b63\u5728\u76f4\u64ad",OFF:"\u95dc\u9589",MINIPLAYER_DRAG:"\u9ede\u64ca\u6309\u4f4f\u53ef\u62d6\u52d5\u8996\u983b",AIRPLAY_TIPS:"\u9694\u7a7a\u64ad\u653e"},jp:{HAVE_NOTHING:"\u30aa\u30fc\u30c7\u30a3\u30aa/\u30d3\u30c7\u30aa\u304c\u6e96\u5099\u3067\u304d\u3066\u3044\u308b\u304b\u60c5\u5831\u304c\u3042\u308a\u307e\u305b\u3093",HAVE_METADATA:"\u30aa\u30fc\u30c7\u30a3\u30aa/\u30d3\u30c7\u30aa\u306e\u30e1\u30bf\u30c7\u30fc\u30bf\u306f\u6e96\u5099\u3067\u304d\u3066\u3044\u307e\u3059",HAVE_CURRENT_DATA:"\u73fe\u5728\u306e\u518d\u751f\u4f4d\u7f6e\u306b\u95a2\u3059\u308b\u30c7\u30fc\u30bf\u306f\u5229\u7528\u53ef\u80fd\u3067\u3059\u304c\u3001\u6b21\u306e\u30d5\u30ec\u30fc\u30e0/\u30df\u30ea\u79d2\u3092\u518d\u751f\u3059\u308b\u306e\u306b\u5341\u5206\u306a\u30c7\u30fc\u30bf\u304c\u3042\u308a\u307e\u305b\u3093",HAVE_FUTURE_DATA:"\u73fe\u5728\u3001\u5c11\u306a\u304f\u3068\u3082\u6b21\u306e\u30d5\u30ec\u30fc\u30e0\u306e\u30c7\u30fc\u30bf\u304c\u5229\u7528\u53ef\u80fd\u3067\u3059",HAVE_ENOUGH_DATA:"\u5229\u7528\u53ef\u80fd\u306a\u30c7\u30fc\u30bf\u306f\u518d\u751f\u3092\u958b\u59cb\u3059\u308b\u306e\u306b\u5341\u5206\u3067\u3059",NETWORK_EMPTY:"\u30aa\u30fc\u30c7\u30a3\u30aa/\u30d3\u30c7\u30aa\u304c\u521d\u671f\u5316\u3055\u308c\u3066\u3044\u307e\u305b\u3093",NETWORK_IDLE:"\u30aa\u30fc\u30c7\u30a3\u30aa/\u30d3\u30c7\u30aa\u306f\u30a2\u30af\u30c6\u30a3\u30d6\u3067\u30ea\u30bd\u30fc\u30b9\u304c\u9078\u629e\u3055\u308c\u3066\u3044\u307e\u3059\u304c\u3001\u30cd\u30c3\u30c8\u30ef\u30fc\u30af\u304c\u4f7f\u7528\u3055\u308c\u3066\u3044\u307e\u305b\u3093",NETWORK_LOADING:"\u30d6\u30e9\u30a6\u30b6\u30fc\u306f\u30c7\u30fc\u30bf\u3092\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9\u3057\u3066\u3044\u307e\u3059",NETWORK_NO_SOURCE:"\u30aa\u30fc\u30c7\u30a3\u30aa/\u30d3\u30c7\u30aa \u306e\u30bd\u30fc\u30b9\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093",MEDIA_ERR_ABORTED:"\u30e6\u30fc\u30b6\u30fc\u306b\u3088\u3063\u3066\u30d5\u30a7\u30c3\u30c1\u30d7\u30ed\u30bb\u30b9\u304c\u4e2d\u6b62\u3055\u308c\u307e\u3057\u305f",MEDIA_ERR_NETWORK:"\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9\u4e2d\u306b\u30a8\u30e9\u30fc\u304c\u767a\u751f\u3057\u307e\u3057\u305f",MEDIA_ERR_DECODE:"\u30c7\u30b3\u30fc\u30c9\u4e2d\u306b\u30a8\u30e9\u30fc\u304c\u767a\u751f\u3057\u307e\u3057\u305f",MEDIA_ERR_SRC_NOT_SUPPORTED:"\u30aa\u30fc\u30c7\u30a3\u30aa/\u30d3\u30c7\u30aa \u306e\u5f62\u5f0f\u304c\u30b5\u30dd\u30fc\u30c8\u3055\u308c\u3066\u3044\u307e\u305b\u3093",REPLAY:"\u30ea\u30d7\u30ec\u30a4",ERROR:"\u30cd\u30c3\u30c8\u30ef\u30fc\u30af\u306e\u63a5\u7d9a\u306b\u554f\u984c\u304c\u767a\u751f\u3057\u307e\u3057\u305f",PLAY_TIPS:"\u30d7\u30ec\u30a4",PAUSE_TIPS:"\u4e00\u6642\u505c\u6b62",PLAYNEXT_TIPS:"\u6b21\u3092\u30d7\u30ec\u30a4",DOWNLOAD_TIPS:"\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9",ROTATE_TIPS:"\u56de\u8ee2",RELOAD_TIPS:"\u518d\u8aad\u307f\u8fbc\u307f",FULLSCREEN_TIPS:"\u30d5\u30eb\u30b9\u30af\u30ea\u30fc\u30f3",EXITFULLSCREEN_TIPS:"\u30d5\u30eb\u30b9\u30af\u30ea\u30fc\u30f3\u3092\u7d42\u4e86",CSSFULLSCREEN_TIPS:"\u30b7\u30a2\u30bf\u30fc\u30e2\u30fc\u30c9",EXITCSSFULLSCREEN_TIPS:"\u30b7\u30a2\u30bf\u30fc\u30e2\u30fc\u30c9\u3092\u7d42\u4e86",TEXTTRACK:"\u5b57\u5e55",PIP:"\u30df\u30cb\u30d7\u30ec\u30fc\u30e4\u30fc",MINIPLAYER:"\u30df\u30cb\u30d7\u30ec\u30fc\u30e4\u30fc",SCREENSHOT:"\u30b9\u30af\u30ea\u30fc\u30f3\u30b7\u30e7\u30c3\u30c8",LIVE:"\u751f\u653e\u9001",OFF:"\u30aa\u30d5",MINIPLAYER_DRAG:"\u30dc\u30bf\u30f3\u3092\u62bc\u3057\u3066\u50cd\u753b\u3092\u30c9\u30e9\u30c3\u30b0\u3059\u308b",AIRPLAY_TIPS:"\u9694\u7a7a\u653e\u9001"}};Object.defineProperty(e,"lang",{get:function(){return e.config&&t[e.config.lang]||t.en},set:function(e){"Object"===(0,o.typeOf)(e)&&Object.keys(e).forEach((function(n){t[n]=e[n]}))}})}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function i(e){return t.typeof=i="function"==typeof Symbol&&"symbol"==o(Symbol.iterator)?function(e){return"undefined"===typeof e?"undefined":o(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":"undefined"===typeof e?"undefined":o(e)},i(e)}function a(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,m(o.key),o)}}function s(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){return t.getPrototypeOf=l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}function c(e,n){return t.setPrototypeOf=c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,n)}function d(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e,t){if(t&&("object"===("undefined"===typeof t?"undefined":o(t))||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return u(e)}function f(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=l(e)););return e}function h(){return"undefined"!==typeof Reflect&&Reflect.get?t.get=h=Reflect.get.bind():t.get=h=function(e,t,n){var o=f(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},h.apply(this,arguments)}function g(e,t){if("object"!==("undefined"===typeof e?"undefined":o(e))||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==("undefined"===typeof r?"undefined":o(r)))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function m(e){var t=g(e,"string");return"symbol"===("undefined"===typeof t?"undefined":o(t))?t:String(t)}t.assertThisInitialized=u,t.classCallCheck=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},t.createClass=function(e,t,n){return t&&a(e.prototype,t),n&&a(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},t.createSuper=function(e){var t=d();return function(){var n,o=l(e);if(t){var r=l(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return p(this,n)}},t.defineProperty=s,t.get=h,t.getPrototypeOf=l,t.inherits=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)},t.isNativeReflectConstruct=d,t.objectSpread2=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e},t.possibleConstructorReturn=p,t.setPrototypeOf=c,t.superPropBase=f,t.toPrimitive=g,t.toPropertyKey=m,t.typeof=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=s(n(13)),i=n(0),a=s(n(4));function s(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._hasStart=!1,this.videoConfig={controls:!!t.isShowControl,autoplay:t.autoplay,playsinline:t.playsinline,"webkit-playsinline":t.playsinline,"x5-playsinline":t.playsinline,"x5-video-player-type":t["x5-video-player-type"]||t.x5VideoPlayerType,"x5-video-player-fullscreen":t["x5-video-player-fullscreen"]||t.x5VideoPlayerFullscreen,"x5-video-orientation":t["x5-video-orientation"]||t.x5VideoOrientation,airplay:t.airplay,"webkit-airplay":t.airplay,tabindex:2,mediaType:t.mediaType||"video"},t.muted&&(this.videoConfig.muted="muted"),t.loop&&(this.videoConfig.loop="loop");var o="";if(this.textTrackShowDefault=!0,t.nativeTextTrack&&Array.isArray(t.nativeTextTrack)&&(t.nativeTextTrack.length>0&&!t.nativeTextTrack.some((function(e){return e.default}))&&(t.nativeTextTrack[0].default=!0,this.textTrackShowDefault=!1),t.nativeTextTrack.some((function(e){if(e.src&&e.label&&e.default)return o+='<track src="'+e.src+'" ',e.kind&&(o+='kind="'+e.kind+'" '),o+='label="'+e.label+'" ',e.srclang&&(o+='srclang="'+e.srclang+'" '),o+=(e.default?"default":"")+">",!0})),this.videoConfig.crossorigin="anonymous"),t.textTrackStyle){var a=document.createElement("style");this.textTrackStyle=a,document.head.appendChild(a);var s="";for(var l in t.textTrackStyle)s+=l+": "+t.textTrackStyle[l]+";";var c=t.id?"#"+t.id:t.el.id?"#"+t.el.id:"."+t.el.className;a.sheet.insertRule?a.sheet.insertRule(c+" video::cue { "+s+" }",0):a.sheet.addRule&&a.sheet.addRule(c+" video::cue",s)}var d=t.el?t.el:(0,i.findDom)(document,"#"+t.id),u=this.constructor.XgVideoProxy;u&&this.videoConfig.mediaType===u.mediaType?this.video=new u(d,t):this.video=(0,i.createDom)(this.videoConfig.mediaType,o,this.videoConfig,""),t.videoStyle&&Object.keys(t.videoStyle).forEach((function(e){(0,i.setStyle)(n.video,e,t.videoStyle[e])})),!this.textTrackShowDefault&&o&&(this.video.getElementsByTagName("Track")[0].track.mode="hidden"),t.autoplay&&(this.video.autoplay=!0,t.autoplayMuted&&(this.video.muted=!0)),this.ev=["play","playing","pause","ended","error","seeking","seeked","progress","timeupdate","waiting","canplay","canplaythrough","durationchange","volumechange","ratechange","loadedmetadata","loadeddata","loadstart"].map((function(e){return t={},n=e,o="on"+e.charAt(0).toUpperCase()+e.slice(1),n in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o,t;var t,n,o})),(0,r.default)(this),this._interval={};var p="0,0",f=this,h=function(e){n&&("play"===e?n.hasStart=!0:"canplay"===e?(0,i.removeClass)(n.root,"xgplayer-is-enter"):"waiting"===e?n.inWaitingStart=(new Date).getTime():"playing"===e&&((0,i.removeClass)(n.root,"xgplayer-is-enter"),n.inWaitingStart&&(n.inWaitingStart=void 0)),"error"===e?n._onError(e):n.emit(e,n),n.hasOwnProperty("_interval")&&(["ended","error","timeupdate"].indexOf(e)<0?((0,i._clearInterval)(n,"bufferedChange"),(0,i._setInterval)(n,"bufferedChange",(function(){if(this.video&&this.video.buffered){for(var e=[],t=0,n=this.video.buffered.length;t<n;t++)e.push([this.video.buffered.start(t),this.video.buffered.end(t)]);e.toString()!==p&&(p=e.toString(),this.emit("bufferedChange",e))}}),200)):"timeupdate"!==e&&(0,i._clearInterval)(n,"bufferedChange")))},g=function(e){t.videoEventMiddleware&&"function"===typeof t.videoEventMiddleware[e]?t.videoEventMiddleware[e].call(n,n,e,h):h.call(n,e)};this.ev.forEach((function(e){f.evItem=Object.keys(e)[0];var t=Object.keys(e)[0];f.video.addEventListener(Object.keys(e)[0],g.bind(f,t))}))}return o(e,[{key:"_onError",value:function(e){this.video&&this.video.error&&this.emit(e,new a.default("other",this.currentTime,this.duration,this.networkState,this.readyState,this.currentSrc,this.src,this.ended,{line:162,msg:this.error,handle:"Constructor"},this.video.error.code,this.video.error))}},{key:"destroy",value:function(){this.textTrackStyle&&this.textTrackStyle.parentNode.removeChild(this.textTrackStyle)}},{key:"play",value:function(){return this.video.play()}},{key:"pause",value:function(){this.video.pause()}},{key:"canPlayType",value:function(e){return this.video.canPlayType(e)}},{key:"getBufferedRange",value:function(e){var t=[0,0],n=this.video;e||(e=n.buffered);var o=n.currentTime;if(e)for(var r=0,i=e.length;r<i&&(t[0]=e.start(r),t[1]=e.end(r),!(t[0]<=o&&o<=t[1]));r++);return t[0]-o<=0&&o-t[1]<=0?t:[0,0]}},{key:"proxyOn",value:function(e,t){(0,i.on)(this,e,t,"destroy")}},{key:"proxyOnce",value:function(e,t){(0,i.once)(this,e,t,"destroy")}},{key:"hasStart",get:function(){return this._hasStart},set:function(e){"boolean"!==typeof e||!0!==e||this._hasStart||(this._hasStart=!0,this.emit("hasstart"))}},{key:"autoplay",set:function(e){this.video&&(this.video.autoplay=e)},get:function(){return!!this.video&&this.video.autoplay}},{key:"buffered",get:function(){return this.video?this.video.buffered:void 0}},{key:"buffered2",get:function(){return(0,i.getBuffered2)(this.video.buffered)}},{key:"crossOrigin",get:function(){return!!this.video&&this.video.crossOrigin},set:function(e){this.video&&(this.video.crossOrigin=e)}},{key:"currentSrc",get:function(){return this.video?this.video.currentSrc:void 0}},{key:"currentTime",get:function(){return this.video&&this.video.currentTime||0},set:function(e){var t=this;("function"!==typeof isFinite||isFinite(e))&&((0,i.hasClass)(this.root,"xgplayer-ended")?(this.once("playing",(function(){t.video.currentTime=e})),this.replay()):this.video.currentTime=e,this.emit("currentTimeChange",e))}},{key:"defaultMuted",get:function(){return!!this.video&&this.video.defaultMuted},set:function(e){this.video&&(this.video.defaultMuted=e)}},{key:"duration",get:function(){return this.config.duration?this.video?Math.min(this.config.duration,this.video.duration):this.config.duration:this.video?this.video.duration:null}},{key:"ended",get:function(){return!this.video||this.video.ended||!1}},{key:"error",get:function(){var e=this.video.error;if(!e)return null;var t=[{en:"MEDIA_ERR_ABORTED",cn:"\u53d6\u56de\u8fc7\u7a0b\u88ab\u7528\u6237\u4e2d\u6b62"},{en:"MEDIA_ERR_NETWORK",cn:"\u5f53\u4e0b\u8f7d\u65f6\u53d1\u751f\u9519\u8bef"},{en:"MEDIA_ERR_DECODE",cn:"\u5f53\u89e3\u7801\u65f6\u53d1\u751f\u9519\u8bef"},{en:"MEDIA_ERR_SRC_NOT_SUPPORTED",cn:"\u4e0d\u652f\u6301\u97f3\u9891/\u89c6\u9891"}];return this.lang?this.lang[t[e.code-1].en]:t[e.code-1].en}},{key:"loop",get:function(){return!!this.video&&this.video.loop},set:function(e){this.video&&(this.video.loop=e)}},{key:"muted",get:function(){return!!this.video&&this.video.muted},set:function(e){this.video&&(this.video.muted=e)}},{key:"networkState",get:function(){var e=[{en:"NETWORK_EMPTY",cn:"\u97f3\u9891/\u89c6\u9891\u5c1a\u672a\u521d\u59cb\u5316"},{en:"NETWORK_IDLE",cn:"\u97f3\u9891/\u89c6\u9891\u662f\u6d3b\u52a8\u7684\u4e14\u5df2\u9009\u53d6\u8d44\u6e90\uff0c\u4f46\u5e76\u672a\u4f7f\u7528\u7f51\u7edc"},{en:"NETWORK_LOADING",cn:"\u6d4f\u89c8\u5668\u6b63\u5728\u4e0b\u8f7d\u6570\u636e"},{en:"NETWORK_NO_SOURCE",cn:"\u672a\u627e\u5230\u97f3\u9891/\u89c6\u9891\u6765\u6e90"}];return this.lang?this.lang[e[this.video.networkState].en]:e[this.video.networkState].en}},{key:"paused",get:function(){return(0,i.hasClass)(this.root,"xgplayer-pause")}},{key:"playbackRate",get:function(){return this.video?this.video.playbackRate:1},set:function(e){this.video&&(this.video.playbackRate=e)}},{key:"played",get:function(){return this.video?this.video.played:void 0}},{key:"preload",get:function(){return!!this.video&&this.video.preload},set:function(e){this.video&&(this.video.preload=e)}},{key:"readyState",get:function(){var e=[{en:"HAVE_NOTHING",cn:"\u6ca1\u6709\u5173\u4e8e\u97f3\u9891/\u89c6\u9891\u662f\u5426\u5c31\u7eea\u7684\u4fe1\u606f"},{en:"HAVE_METADATA",cn:"\u5173\u4e8e\u97f3\u9891/\u89c6\u9891\u5c31\u7eea\u7684\u5143\u6570\u636e"},{en:"HAVE_CURRENT_DATA",cn:"\u5173\u4e8e\u5f53\u524d\u64ad\u653e\u4f4d\u7f6e\u7684\u6570\u636e\u662f\u53ef\u7528\u7684\uff0c\u4f46\u6ca1\u6709\u8db3\u591f\u7684\u6570\u636e\u6765\u64ad\u653e\u4e0b\u4e00\u5e27/\u6beb\u79d2"},{en:"HAVE_FUTURE_DATA",cn:"\u5f53\u524d\u53ca\u81f3\u5c11\u4e0b\u4e00\u5e27\u7684\u6570\u636e\u662f\u53ef\u7528\u7684"},{en:"HAVE_ENOUGH_DATA",cn:"\u53ef\u7528\u6570\u636e\u8db3\u4ee5\u5f00\u59cb\u64ad\u653e"}];return this.lang?this.lang[e[this.video.readyState].en]:e[this.video.readyState]}},{key:"seekable",get:function(){return!!this.video&&this.video.seekable}},{key:"seeking",get:function(){return!!this.video&&this.video.seeking}},{key:"src",get:function(){return this.video?this.video.src:void 0},set:function(e){(0,i.hasClass)(this.root,"xgplayer-ended")||this.emit("urlchange",this.video.src),(0,i.removeClass)(this.root,"xgplayer-ended xgplayer-is-replay xgplayer-is-error"),this.video.pause(),this.emit("pause"),this.video.src=e,this.emit("srcChange")}},{key:"poster",set:function(e){var t=(0,i.findDom)(this.root,".xgplayer-poster");t&&(t.style.backgroundImage="url("+e+")")}},{key:"volume",get:function(){return this.video?this.video.volume:1},set:function(e){this.video&&(this.video.volume=e)}},{key:"fullscreen",get:function(){return(0,i.hasClass)(this.root,"xgplayer-is-fullscreen")||(0,i.hasClass)(this.root,"xgplayer-fullscreen-active")}},{key:"bullet",get:function(){return!!(0,i.findDom)(this.root,"xg-danmu")&&(0,i.hasClass)((0,i.findDom)(this.root,"xg-danmu"),"xgplayer-has-danmu")}},{key:"textTrack",get:function(){return(0,i.hasClass)(this.root,"xgplayer-is-textTrack")}},{key:"pip",get:function(){return(0,i.hasClass)(this.root,"xgplayer-pip-active")}},{key:"isMiniPlayer",get:function(){return(0,i.hasClass)(this.root,"xgplayer-miniplayer-active")}}]),e}();t.default=l,e.exports=t.default},function(e,t,n){"use strict";var o,r,i,a,s,l,c,d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u=n(14),p=n(31),f=Function.prototype.apply,h=Function.prototype.call,g=Object.create,m=Object.defineProperty,y=Object.defineProperties,v=Object.prototype.hasOwnProperty,x={configurable:!0,enumerable:!1,writable:!0};r=function(e,t){var n,r;return p(t),r=this,o.call(this,e,n=function(){i.call(r,e,n),f.call(t,this,arguments)}),n.__eeOnceListener__=t,this},a=function(e){var t,n,o,r,i;if(v.call(this,"__ee__")&&(r=this.__ee__[e]))if("object"===("undefined"===typeof r?"undefined":d(r))){for(n=arguments.length,i=new Array(n-1),t=1;t<n;++t)i[t-1]=arguments[t];for(r=r.slice(),t=0;o=r[t];++t)f.call(o,this,i)}else switch(arguments.length){case 1:h.call(r,this);break;case 2:h.call(r,this,arguments[1]);break;case 3:h.call(r,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,i=new Array(n-1),t=1;t<n;++t)i[t-1]=arguments[t];f.call(r,this,i)}},s={on:o=function(e,t){var n;return p(t),v.call(this,"__ee__")?n=this.__ee__:(n=x.value=g(null),m(this,"__ee__",x),x.value=null),n[e]?"object"===d(n[e])?n[e].push(t):n[e]=[n[e],t]:n[e]=t,this},once:r,off:i=function(e,t){var n,o,r,i;if(p(t),!v.call(this,"__ee__"))return this;if(!(n=this.__ee__)[e])return this;if("object"===("undefined"===typeof(o=n[e])?"undefined":d(o)))for(i=0;r=o[i];++i)r!==t&&r.__eeOnceListener__!==t||(2===o.length?n[e]=o[i?0:1]:o.splice(i,1));else o!==t&&o.__eeOnceListener__!==t||delete n[e];return this},emit:a},l={on:u(o),once:u(r),off:u(i),emit:u(a)},c=y({},l),e.exports=t=function(e){return null==e?g(c):y(Object(e),l)},t.methods=s},function(e,t,n){"use strict";var o=n(6),r=n(15),i=n(19),a=n(27),s=n(28),l=e.exports=function(e,t){var n,r,l,c,d;return arguments.length<2||"string"!==typeof e?(c=t,t=e,e=null):c=arguments[2],o(e)?(n=s.call(e,"c"),r=s.call(e,"e"),l=s.call(e,"w")):(n=l=!0,r=!1),d={value:t,configurable:n,enumerable:r,writable:l},c?i(a(c),d):d};l.gs=function(e,t,n){var l,c,d,u;return"string"!==typeof e?(d=n,n=t,t=e,e=null):d=arguments[3],o(t)?r(t)?o(n)?r(n)||(d=n,n=void 0):n=void 0:(d=t,t=n=void 0):t=void 0,o(e)?(l=s.call(e,"c"),c=s.call(e,"e")):(l=!0,c=!1),u={get:t,set:n,configurable:l,enumerable:c},d?i(a(d),u):u}},function(e,t,n){"use strict";var o=n(16),r=/^\s*class[\s{/}]/,i=Function.prototype.toString;e.exports=function(e){return!!o(e)&&!r.test(i.call(e))}},function(e,t,n){"use strict";var o=n(17);e.exports=function(e){if("function"!==typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!==typeof e.length)return!1;if("function"!==typeof e.call)return!1;if("function"!==typeof e.apply)return!1}catch(t){return!1}return!o(e)}},function(e,t,n){"use strict";var o=n(18);e.exports=function(e){if(!o(e))return!1;try{return!!e.constructor&&e.constructor.prototype===e}catch(t){return!1}}},function(e,t,n){"use strict";var o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=n(6),i={object:!0,function:!0,undefined:!0};e.exports=function(e){return!!r(e)&&hasOwnProperty.call(i,"undefined"===typeof e?"undefined":o(e))}},function(e,t,n){"use strict";e.exports=n(20)()?Object.assign:n(21)},function(e,t,n){"use strict";e.exports=function(){var e,t=Object.assign;return"function"===typeof t&&(t(e={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}},function(e,t,n){"use strict";var o=n(22),r=n(26),i=Math.max;e.exports=function(e,t){var n,a,s,l=i(arguments.length,2);for(e=Object(r(e)),s=function(o){try{e[o]=t[o]}catch(r){n||(n=r)}},a=1;a<l;++a)o(t=arguments[a]).forEach(s);if(void 0!==n)throw n;return e}},function(e,t,n){"use strict";e.exports=n(23)()?Object.keys:n(24)},function(e,t,n){"use strict";e.exports=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}},function(e,t,n){"use strict";var o=n(3),r=Object.keys;e.exports=function(e){return r(o(e)?Object(e):e)}},function(e,t,n){"use strict";e.exports=function(){}},function(e,t,n){"use strict";var o=n(3);e.exports=function(e){if(!o(e))throw new TypeError("Cannot use null or undefined");return e}},function(e,t,n){"use strict";var o=n(3),r=Array.prototype.forEach,i=Object.create;e.exports=function(e){var t=i(null);return r.call(arguments,(function(e){o(e)&&function(e,t){var n;for(n in e)t[n]=e[n]}(Object(e),t)})),t}},function(e,t,n){"use strict";e.exports=n(29)()?String.prototype.contains:n(30)},function(e,t,n){"use strict";var o="razdwatrzy";e.exports=function(){return"function"===typeof o.contains&&!0===o.contains("dwa")&&!1===o.contains("foo")}},function(e,t,n){"use strict";var o=String.prototype.indexOf;e.exports=function(e){return o.call(this,e,arguments[1])>-1}},function(e,t,n){"use strict";e.exports=function(e){if("function"!==typeof e)throw new TypeError(e+" is not a function");return e}},function(e,t,n){"use strict";var o=n(33),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t,n=arguments[1];if(o(e),void 0===n)r.call(e,"__ee__")&&delete e.__ee__;else{if(!(t=r.call(e,"__ee__")&&e.__ee__))return;t[n]&&delete t[n]}}},function(e,t,n){"use strict";var o=n(34);e.exports=function(e){if(!o(e))throw new TypeError(e+" is not an Object");return e}},function(e,t,n){"use strict";var o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=n(3),i={function:!0,object:!0};e.exports=function(e){return r(e)&&i["undefined"===typeof e?"undefined":o(e)]||!1}},function(e,t,n){var o=n(36);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default{background:#000;width:100%;height:100%;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;-ms-user-select:none}.xgplayer-skin-default *{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;white-space:normal;word-wrap:normal}.xgplayer-skin-default.xgplayer-rotate-fullscreen{position:absolute;top:0;left:100%;bottom:0;right:0;height:100vw!important;width:100vh!important;-webkit-transform-origin:top left;-ms-transform-origin:top left;transform-origin:top left;-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.xgplayer-skin-default.xgplayer-is-fullscreen{width:100%!important;height:100%!important;padding-top:0!important;z-index:9999}.xgplayer-skin-default.xgplayer-is-fullscreen.xgplayer-inactive{cursor:none}.xgplayer-skin-default video{width:100%;height:100%;outline:none}.xgplayer-skin-default .xgplayer-none{display:none}@-webkit-keyframes loadingRotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loadingRotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes loadingDashOffset{0%{stroke-dashoffset:236}to{stroke-dashoffset:0}}@keyframes loadingDashOffset{0%{stroke-dashoffset:236}to{stroke-dashoffset:0}}.xgplayer-skin-default .xgplayer-controls{display:-webkit-flex;display:-moz-box;display:flex;position:absolute;bottom:0;left:0;right:0;height:40px;background-image:linear-gradient(180deg,transparent,rgba(0,0,0,.37),rgba(0,0,0,.75),rgba(0,0,0,.75));z-index:10}.xgplayer-skin-default.xgplayer-inactive .xgplayer-controls,.xgplayer-skin-default.xgplayer-is-live .xgplayer-controls .xgplayer-progress,.xgplayer-skin-default.xgplayer-is-live .xgplayer-controls .xgplayer-time,.xgplayer-skin-default.xgplayer-no-controls .xgplayer-controls,.xgplayer-skin-default.xgplayer-nostart .xgplayer-controls{display:none}.xgplayer-skin-default.xgplayer-is-live .xgplayer-controls .xgplayer-live{display:block}.xgplayer-skin-default .xgplayer-live{display:block;font-size:12px;color:#fff;line-height:40px;-webkit-order:1;-moz-box-ordinal-group:2;order:1}.xgplayer-skin-default .xgplayer-icon{display:block;width:40px;height:40px;overflow:hidden;fill:#fff}.xgplayer-skin-default .xgplayer-icon svg{position:absolute}.xgplayer-skin-default .xgplayer-tips{background:rgba(0,0,0,.54);border-radius:1px;display:none;position:absolute;font-family:PingFangSC-Regular;font-size:11px;color:#fff;padding:2px 4px;text-align:center;top:-30px;left:50%;margin-left:-16px;width:auto;white-space:nowrap}.xgplayer-skin-default.xgplayer-mobile .xgplayer-tips{display:none!important}.xgplayer-skin-default .xgplayer-screen-container{display:block;width:100%}",""])},function(e,t,n){"use strict";e.exports=function(e){var t="undefined"!==typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!==typeof e)return e;var n=t.protocol+"//"+t.host,o=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(e,t){var r,i=t.trim().replace(/^"(.*)"$/,(function(e,t){return t})).replace(/^'(.*)'$/,(function(e,t){return t}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(i)?e:(r=0===i.indexOf("//")?i:0===i.indexOf("/")?n+i:o+i.replace(/^\.\//,""),"url("+JSON.stringify(r)+")")}))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"mobile",method:function(){var e=this,t=e.root,n=0,r=void 0,i={first:"",second:""};function a(t){e&&e.video&&(e.video.addEventListener("touchend",(function(t){e.onElementTouchend(t,e.video)})),e.video.addEventListener("touchstart",(function(){e.isTouchMove=!1})),e.video.addEventListener("touchmove",(function(){e.isTouchMove=!0})),e.config.autoplay&&e.start())}e.onElementTouchend=function(e,a){this.config.closeVideoPreventDefault||e.preventDefault(),this.config.closeVideoStopPropagation||e.stopPropagation();var s=this;if((0,o.hasClass)(t,"xgplayer-inactive")?s.emit("focus"):s.emit("blur"),!s.config.closeVideoTouch&&!s.isTouchMove){var l=function(){r=setTimeout((function(){if((0,o.hasClass)(s.root,"xgplayer-nostart"))return!1;if(!s.ended)if(s.paused){var e=s.play();void 0!==e&&e&&e.catch((function(e){}))}else s.pause();n=0}),200)};s.config.closeVideoClick||(n++,r&&clearTimeout(r),1===n?s.config.enableVideoDbltouch?i.first=new Date:l():2===n&&s.config.enableVideoDbltouch?(i.second=new Date,Math.abs(i.first-i.second)<400?l():(i.first=new Date,n=1)):n=0)}},e.once("ready",a),e.once("destroy",(function t(){e.off("ready",a),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"pc",method:function(){var e=this;if(e.controls&&e.video){var t=e.controls,n=e.root,r=0,i=void 0;e.onElementClick=function(e,t){this.config.closeVideoPreventDefault||e.preventDefault(),this.config.closeVideoStopPropagation||e.stopPropagation();var n=this;n.config.closeVideoClick||(r++,i&&clearTimeout(i),1===r?i=setTimeout((function(){if((0,o.hasClass)(n.root,"xgplayer-nostart"))return!1;if(!n.ended)if(n.paused){var e=n.play();void 0!==e&&e&&e.catch((function(e){}))}else n.pause();r=0}),200):r=0)},e.video.addEventListener("click",(function(t){e.onElementClick(t,e.video)}),!1),e.onElementDblclick=function(e,n){if(this.config.closeVideoPreventDefault||e.preventDefault(),this.config.closeVideoStopPropagation||e.stopPropagation(),!this.config.closeVideoDblclick){var o=t.querySelector(".xgplayer-fullscreen");if(o){var r=void 0;document.createEvent?(r=document.createEvent("Event")).initEvent("click",!0,!0):r=new Event("click"),o.dispatchEvent(r)}}},e.video.addEventListener("dblclick",(function(t){e.onElementDblclick(t,e.video)}),!1),n.addEventListener("mouseenter",a),n.addEventListener("mouseleave",s),t.addEventListener("mouseenter",(function(t){e.userTimer&&clearTimeout(e.userTimer)})),t.addEventListener("mouseleave",(function(t){e.config.closeControlsBlur||e.emit("focus",e)})),t.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation()})),e.once("ready",l),e.once("destroy",(function t(){n.removeEventListener("mouseenter",a),n.removeEventListener("mouseleave",s),e.off("ready",l),e.off("destroy",t)}))}function a(){clearTimeout(e.leavePlayerTimer),e.emit("focus",e)}function s(){e.config.closePlayerBlur||(e.leavePlayerTimer=setTimeout((function(){e.emit("blur",e)}),e.config.leavePlayerTime||0))}function l(t){e.config.autoplay&&e.start()}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"start",method:function(){var e=this,t=e.root;function n(){e.off("canplay",n);var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}function r(){(0,o.hasClass)(t,"xgplayer-nostart")?((0,o.removeClass)(t,"xgplayer-nostart"),(0,o.addClass)(t,"xgplayer-is-enter"),"function"===typeof t.contains?e.video&&1===e.video.nodeType&&!t.contains(e.video)||e.video&&1!==e.video.nodeType&&void 0===e.video.mediaSource?(e.once("canplay",n),e.start()):n():e.video&&1===e.video.nodeType&&!t.querySelector(this.videoConfig.mediaType)||e.video&&1!==e.video.nodeType&&void 0===e.video.mediaSource?(e.once("canplay",n),e.start()):n()):e.paused&&((0,o.removeClass)(t,"xgplayer-nostart xgplayer-isloading"),setTimeout((function(){var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}),10))}e.on("startBtnClick",r),e.once("destroy",(function t(){e.off("startBtnClick",r),e.off("canplay",n),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=a(n(42)),i=a(n(43));function a(e){return e&&e.__esModule?e:{default:e}}n(44),t.default={name:"s_start",method:function(){var e=this,t=e.root,n=(0,o.createDom)("xg-start",'<div class="xgplayer-icon-play">'+r.default+'</div>\n                                      <div class="xgplayer-icon-pause">'+i.default+"</div>",{},"xgplayer-start");function a(e){(0,o.addClass)(e.root,"xgplayer-skin-default"),e.config&&(e.config.autoplay&&!(0,o.isWeiXin)()&&!(0,o.isUc)()&&(0,o.addClass)(e.root,"xgplayer-is-enter"),e.config.lang&&"en"===e.config.lang?(0,o.addClass)(e.root,"xgplayer-lang-is-en"):"jp"===e.config.lang&&(0,o.addClass)(e.root,"xgplayer-lang-is-jp"),e.config.enableContextmenu||e.video.addEventListener("contextmenu",(function(e){e.preventDefault(),e.stopPropagation()})))}e.config&&e.config.hideStartBtn&&(0,o.addClass)(t,"xgplayer-start-hide"),e.isReady?(t.appendChild(n),a(e)):e.once("ready",(function(){t.appendChild(n),a(e)})),e.once("autoplay was prevented",(function(){(0,o.removeClass)(e.root,"xgplayer-is-enter"),(0,o.addClass)(e.root,"xgplayer-nostart")})),e.once("canplay",(function(){(0,o.removeClass)(e.root,"xgplayer-is-enter")})),n.onclick=function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("startBtnClick")}}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="70" height="70" viewBox="0 0 70 70">\n  <path transform="translate(15,15) scale(0.04,0.04)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="70" height="70" viewBox="0 0 70 70">\n  <path transform="translate(15,15) scale(0.04 0.04)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n'},function(e,t,n){var o=n(45);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-start{border-radius:50%;display:inline-block;width:70px;height:70px;background:rgba(0,0,0,.38);overflow:hidden;text-align:center;line-height:70px;vertical-align:middle;position:absolute;left:50%;top:50%;z-index:115;margin:-35px auto auto -35px;cursor:pointer}.xgplayer-skin-default .xgplayer-start div{position:absolute}.xgplayer-skin-default .xgplayer-start div svg{fill:hsla(0,0%,100%,.7)}.xgplayer-skin-default .xgplayer-start .xgplayer-icon-play{display:block}.xgplayer-skin-default .xgplayer-start .xgplayer-icon-pause{display:none}.xgplayer-skin-default .xgplayer-start:hover{opacity:.85}.xgplayer-skin-default.xgplayer-pause.xgplayer-start-hide .xgplayer-start,.xgplayer-skin-default.xgplayer-playing .xgplayer-start,.xgplayer-skin-default.xgplayer-playing .xgplayer-start .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-start-hide .xgplayer-start{display:none}.xgplayer-skin-default.xgplayer-playing .xgplayer-start .xgplayer-icon-pause{display:block}.xgplayer-skin-default.xgplayer-pause .xgplayer-start{display:inline-block}.xgplayer-skin-default.xgplayer-pause .xgplayer-start .xgplayer-icon-play{display:block}.xgplayer-skin-default.xgplayer-is-replay .xgplayer-start,.xgplayer-skin-default.xgplayer-pause .xgplayer-start .xgplayer-icon-pause{display:none}.xgplayer-skin-default.xgplayer-is-replay .xgplayer-start .xgplayer-icon-play{display:block}.xgplayer-skin-default.xgplayer-is-replay .xgplayer-start .xgplayer-icon-pause{display:none}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"fullscreen",method:function(){var e=this,t=e.root;function n(){e.config.rotateFullscreen?(0,o.hasClass)(t,"xgplayer-rotate-fullscreen")?e.exitRotateFullscreen():e.getRotateFullscreen():(0,o.hasClass)(t,"xgplayer-is-fullscreen")?e.exitFullscreen(t):e.getFullscreen(t)}function r(){var n=document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement;n&&n===t?((0,o.addClass)(t,"xgplayer-is-fullscreen"),e.emit("requestFullscreen")):(0,o.hasClass)(t,"xgplayer-is-fullscreen")&&((0,o.removeClass)(t,"xgplayer-is-fullscreen"),e.emit("exitFullscreen")),e.danmu&&"function"===typeof e.danmu.resize&&e.danmu.resize()}function i(n){e.video.webkitPresentationMode!==o.PresentationMode.FULLSCREEN&&((0,o.removeClass)(t,"xgplayer-is-fullscreen"),e.emit("exitFullscreen"))}e.on("fullscreenBtnClick",n),["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"].forEach((function(e){document.addEventListener(e,r)})),e.video.addEventListener("webkitbeginfullscreen",(function(){(0,o.addClass)(t,"xgplayer-is-fullscreen"),e.emit("requestFullscreen")})),e.video.addEventListener("webkitendfullscreen",(function(){(0,o.removeClass)(t,"xgplayer-is-fullscreen"),e.emit("exitFullscreen")})),(0,o.checkWebkitSetPresentationMode)(e.video)&&e.video.addEventListener("webkitpresentationmodechanged",i),e.once("destroy",(function t(){e.off("fullscreenBtnClick",n),["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"].forEach((function(e){document.removeEventListener(e,r)})),(0,o.checkWebkitSetPresentationMode)(e.video)&&e.video.removeEventListener("webkitpresentationmodechanged",i),e.off("destroy",t)})),e.getFullscreen=function(e){var t=this;if(e.requestFullscreen){var n=e.requestFullscreen();n&&n.catch((function(){t.emit("fullscreen error")}))}else e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen(window.Element.ALLOW_KEYBOARD_INPUT):t.video.webkitSupportsFullscreen?t.video.webkitEnterFullscreen():e.msRequestFullscreen?e.msRequestFullscreen():(0,o.addClass)(e,"xgplayer-is-cssfullscreen")},e.exitFullscreen=function(e){document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen(),(0,o.removeClass)(e,"xgplayer-is-cssfullscreen")},e.getRotateFullscreen=function(){var e=this;document.documentElement.style.width="100%",document.documentElement.style.height="100%",e.config.fluid&&(e.root.style["padding-top"]="",e.root.style["max-width"]="unset"),e.root&&!(0,o.hasClass)(e.root,"xgplayer-rotate-fullscreen")&&(0,o.addClass)(e.root,"xgplayer-rotate-fullscreen"),e.emit("getRotateFullscreen")},e.exitRotateFullscreen=function(){var e=this;document.documentElement.style.width="unset",document.documentElement.style.height="unset",e.config.fluid&&(e.root.style.width="100%",e.root.style.height="0",e.root.style["padding-top"]=100*e.config.height/e.config.width+"%",e.root.style["max-width"]="100%"),e.root&&(0,o.hasClass)(e.root,"xgplayer-rotate-fullscreen")&&(0,o.removeClass)(e.root,"xgplayer-rotate-fullscreen"),e.emit("exitRotateFullscreen")}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"play",method:function(){var e=this;function t(){if(e.config.allowPlayAfterEnded||!e.ended)if((0,o.hasClass)(e.root,"xgplayer-nostart")&&e.start(),e.paused){var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}else e.pause()}e.on("playBtnClick",t),e.once("destroy",(function n(){e.off("playBtnClick",t),e.off("destroy",n)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"replay",method:function(){var e=this,t=e.root;function n(){(0,o.removeClass)(t,"xgplayer-is-replay"),e.replay()}e.on("replayBtnClick",n),e.on("ended",(function(){e.config.loop||(0,o.addClass)(t,"xgplayer-is-replay")})),e.once("destroy",(function t(){e.off("replayBtnClick",n),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=a(n(50)),i=a(n(51));function a(e){return e&&e.__esModule?e:{default:e}}n(52),t.default={name:"s_play",method:function(){var e=this,t=e.config.playBtn?e.config.playBtn:{},n=void 0;n="img"===t.type?(0,o.createImgBtn)("play",t.url.play,t.width,t.height):(0,o.createDom)("xg-play",'<xg-icon class="xgplayer-icon">\n                                      <div class="xgplayer-icon-play">'+r.default+'</div>\n                                      <div class="xgplayer-icon-pause">'+i.default+"</div>\n                                     </xg-icon>",{},"xgplayer-play");var a={};a.play=e.lang.PLAY_TIPS,a.pause=e.lang.PAUSE_TIPS;var s=(0,o.createDom)("xg-tips",'<span class="xgplayer-tip-play">'+a.play+'</span>\n                                        <span class="xgplayer-tip-pause">'+a.pause+"</span>",{},"xgplayer-tips");n.appendChild(s),e.once("ready",(function(){e.controls&&e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("playBtnClick")}))}))}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="translate(2,2) scale(0.0320625 0.0320625)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="translate(2,2) scale(0.0320625 0.0320625)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n'},function(e,t,n){var o=n(53);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-play,.xgplayer-skin-default .xgplayer-play-img{width:40px;position:relative;-webkit-order:0;-moz-box-ordinal-group:1;order:0;display:block;cursor:pointer;margin-left:3px}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon,.xgplayer-skin-default .xgplayer-play .xgplayer-icon{margin-top:3px;width:32px}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon div,.xgplayer-skin-default .xgplayer-play .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default .xgplayer-play .xgplayer-icon .xgplayer-icon-play{display:block}.xgplayer-skin-default .xgplayer-play-img .xgplayer-icon .xgplayer-icon-pause,.xgplayer-skin-default .xgplayer-play .xgplayer-icon .xgplayer-icon-pause{display:none}.xgplayer-skin-default .xgplayer-play-img .xgplayer-tips .xgplayer-tip-play,.xgplayer-skin-default .xgplayer-play .xgplayer-tips .xgplayer-tip-play{display:block}.xgplayer-skin-default .xgplayer-play-img .xgplayer-tips .xgplayer-tip-pause,.xgplayer-skin-default .xgplayer-play .xgplayer-tips .xgplayer-tip-pause{display:none}.xgplayer-skin-default .xgplayer-play-img:hover,.xgplayer-skin-default .xgplayer-play:hover{opacity:.85}.xgplayer-skin-default .xgplayer-play-img:hover .xgplayer-tips,.xgplayer-skin-default .xgplayer-play:hover .xgplayer-tips{display:block}.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-icon .xgplayer-icon-play{display:none}.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-icon .xgplayer-icon-pause,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-icon .xgplayer-icon-pause{display:block}.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-tips .xgplayer-tip-play,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-tips .xgplayer-tip-play{display:none}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-icon .xgplayer-icon-play,.xgplayer-skin-default.xgplayer-playing .xgplayer-play-img .xgplayer-tips .xgplayer-tip-pause,.xgplayer-skin-default.xgplayer-playing .xgplayer-play .xgplayer-tips .xgplayer-tip-pause{display:block}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-icon .xgplayer-icon-pause,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-icon .xgplayer-icon-pause{display:none}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-tips .xgplayer-tip-play,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-tips .xgplayer-tip-play{display:block}.xgplayer-skin-default.xgplayer-pause .xgplayer-play-img .xgplayer-tips .xgplayer-tip-pause,.xgplayer-skin-default.xgplayer-pause .xgplayer-play .xgplayer-tips .xgplayer-tip-pause{display:none}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(55),t.default={name:"s_poster",method:function(){var e=this,t=e.root;if(e.config.poster){var n=(0,o.createDom)("xg-poster","",{},"xgplayer-poster");n.style.backgroundImage="url("+e.config.poster+")",t.appendChild(n)}}},e.exports=t.default},function(e,t,n){var o=n(56);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-poster{display:none;position:absolute;left:0;top:0;width:100%;height:100%;z-index:100;background-size:cover;background-position:50%}.xgplayer-skin-default.xgplayer-nostart .xgplayer-poster{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(58),t.default={name:"s_flex",method:function(){var e=(0,o.createDom)("xg-placeholder","",{},"xgplayer-placeholder");this.controls.appendChild(e)}},e.exports=t.default},function(e,t,n){var o=n(59);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-placeholder{-webkit-flex:1;-moz-box-flex:1;flex:1;-webkit-order:3;-moz-box-ordinal-group:4;order:3;display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=a(n(61)),i=a(n(62));function a(e){return e&&e.__esModule?e:{default:e}}n(63),t.default={name:"s_fullscreen",method:function(){var e=this,t=e.config.fullscreenBtn?e.config.fullscreenBtn:{},n=void 0;n="img"===t.type?(0,o.createImgBtn)("fullscreen",t.url.request,t.width,t.height):(0,o.createDom)("xg-fullscreen",'<xg-icon class="xgplayer-icon">\n                                             <div class="xgplayer-icon-requestfull">'+r.default+'</div>\n                                             <div class="xgplayer-icon-exitfull">'+i.default+"</div>\n                                           </xg-icon>",{},"xgplayer-fullscreen");var a={};a.requestfull=e.lang.FULLSCREEN_TIPS,a.exitfull=e.lang.EXITFULLSCREEN_TIPS;var s=(0,o.createDom)("xg-tips",'<span class="xgplayer-tip-requestfull">'+a.requestfull+'</span>\n                                        <span class="xgplayer-tip-exitfull">'+a.exitfull+"</span>",{},"xgplayer-tips");n.appendChild(s),e.once("ready",(function(){e.controls&&e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("fullscreenBtnClick")}))}))}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.0320625 0.0320625)" d="M598 214h212v212h-84v-128h-128v-84zM726 726v-128h84v212h-212v-84h128zM214 426v-212h212v84h-128v128h-84zM298 598v128h128v84h-212v-212h84z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.0320625 0.0320625)" d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"></path>\n</svg>\n'},function(e,t,n){var o=n(64);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-fullscreen,.xgplayer-skin-default .xgplayer-fullscreen-img{position:relative;-webkit-order:13;-moz-box-ordinal-group:14;order:13;display:block;cursor:pointer;margin-left:5px;margin-right:3px}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon{margin-top:3px}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon div,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:block}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:none}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-tips,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-tips{position:absolute;right:0;left:auto}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:block}.xgplayer-skin-default .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:none}.xgplayer-skin-default .xgplayer-fullscreen-img:hover,.xgplayer-skin-default .xgplayer-fullscreen:hover{opacity:.85}.xgplayer-skin-default .xgplayer-fullscreen-img:hover .xgplayer-tips,.xgplayer-skin-default .xgplayer-fullscreen:hover .xgplayer-tips{display:block}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:block}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-rotate-fullscreen .xgplayer-fullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(66),a=(o=i)&&o.__esModule?o:{default:o};n(67),t.default={name:"s_loading",method:function(){var e=this.root,t=(0,r.createDom)("xg-loading",""+a.default,{},"xgplayer-loading");this.once("ready",(function(){e.appendChild(t)}))}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewbox="0 0 100 100">\n  <path d="M100,50A50,50,0,1,1,50,0"></path>\n</svg>\n'},function(e,t,n){var o=n(68);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-loading{display:none;width:100px;height:100px;overflow:hidden;-webkit-transform:scale(.7);-ms-transform:scale(.7);transform:scale(.7);position:absolute;left:50%;top:50%;margin:-50px auto auto -50px}.xgplayer-skin-default .xgplayer-loading svg{border-radius:50%;-webkit-transform-origin:center;-ms-transform-origin:center;transform-origin:center;-webkit-animation:loadingRotate 1s linear infinite;animation:loadingRotate 1s linear infinite}.xgplayer-skin-default .xgplayer-loading svg path{stroke:#ddd;stroke-dasharray:236;-webkit-animation:loadingDashOffset 2s linear infinite;animation:loadingDashOffset 2s linear infinite;animation-direction:alternate-reverse;fill:none;stroke-width:12px}.xgplayer-skin-default.xgplayer-nostart .xgplayer-loading{display:none}.xgplayer-skin-default.xgplayer-pause .xgplayer-loading{display:none!important}.xgplayer-skin-default.xgplayer-isloading .xgplayer-loading{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(5),a=(o=i)&&o.__esModule?o:{default:o};n(70);var s=function(e){return(0,r.hasClass)(e.root,"xgplayer-rotate-fullscreen")};t.default={name:"s_progress",method:function(){var e=this,t=(0,r.createDom)("xg-progress",'<xg-outer class="xgplayer-progress-outer">\n                                                   <xg-cache class="xgplayer-progress-cache"></xg-cache>\n                                                   <xg-played class="xgplayer-progress-played">\n                                                     <xg-progress-btn class="xgplayer-progress-btn"></xg-progress-btn>\n                                                     <xg-point class="xgplayer-progress-point xgplayer-tips"></xg-point>\n                                                     <xg-thumbnail class="xgplayer-progress-thumbnail xgplayer-tips"></xg-thumbnail>\n                                                   </xg-played>\n                                                 </xg-outer>',{tabindex:1},"xgplayer-progress"),n=void 0;e.controls.appendChild(t);var o=t.querySelector(".xgplayer-progress-played"),i=t.querySelector(".xgplayer-progress-outer"),l=t.querySelector(".xgplayer-progress-cache"),c=t.querySelector(".xgplayer-progress-point"),d=t.querySelector(".xgplayer-progress-thumbnail");function u(n,o){n.addEventListener("mouseenter",(function(e){o&&((0,r.addClass)(n,"xgplayer-progress-dot-show"),(0,r.addClass)(t,"xgplayer-progress-dot-active"))})),n.addEventListener("mouseleave",(function(e){o&&((0,r.removeClass)(n,"xgplayer-progress-dot-show"),(0,r.removeClass)(t,"xgplayer-progress-dot-active"))})),n.addEventListener("touchend",(function(i){i.stopPropagation(),o&&((0,r.hasClass)(n,"xgplayer-progress-dot-show")||Object.keys(e.dotArr).forEach((function(t){e.dotArr[t]&&(0,r.removeClass)(e.dotArr[t],"xgplayer-progress-dot-show")})),(0,r.toggleClass)(n,"xgplayer-progress-dot-show"),(0,r.toggleClass)(t,"xgplayer-progress-dot-active"))}))}function p(){e.config.progressDot&&"Array"===(0,r.typeOf)(e.config.progressDot)&&e.config.progressDot.forEach((function(t){if(t.time>=0&&t.time<=e.duration){var n=(0,r.createDom)("xg-progress-dot",t.text?'<span class="xgplayer-progress-tip">'+t.text+"</span>":"",{},"xgplayer-progress-dot");if(n.style.left=t.time/e.duration*100+"%",t.duration>=0&&(n.style.width=Math.min(t.duration,e.duration-t.time)/e.duration*100+"%"),t.style)for(var o in t.style)n.style[o]=t.style[o];i.appendChild(n),e.dotArr[t.time]=n,u(n,t.text)}}))}e.dotArr={},e.once("canplay",p),e.addProgressDot=function(t,n,o,a){if(!e.dotArr[t]&&t>=0&&t<=e.duration){var s=(0,r.createDom)("xg-progress-dot",n?'<span class="xgplayer-progress-tip">'+n+"</span>":"",{},"xgplayer-progress-dot");if(s.style.left=t/e.duration*100+"%",o>=0&&(s.style.width=Math.min(o,e.duration-t)/e.duration*100+"%"),a)for(var l in a)s.style[l]=a[l];i.appendChild(s),e.dotArr[t]=s,u(s,n)}},e.removeProgressDot=function(t){if(t>=0&&t<=e.duration&&e.dotArr[t]){var n=e.dotArr[t];n.parentNode.removeChild(n),n=null,e.dotArr[t]=null}},e.removeAllProgressDot=function(){Object.keys(e.dotArr).forEach((function(t){if(e.dotArr[t]){var n=e.dotArr[t];n.parentNode.removeChild(n),n=null,e.dotArr[t]=null}}))};var f=0,h=0,g=0,m=0,y=0,v=0,x=[],b=void 0,_=void 0,k=function(){e.config.thumbnail&&(e.config.thumbnail.isShowCoverPreview&&!b&&(o.removeChild(d),(b=(0,r.createDom)("xg-coverpreview",'<xg-outer class="xgplayer-coverpreview-outer">\n            <xg-thumbnail class="xgplayer-coverpreview-thumbnail"></xg-thumbnail>\n            <xg-point class="xgplayer-coverpreview-point"></xg-point>\n          </xg-outer>',{tabindex:1},"xgplayer-coverpreview")).querySelector(".xgplayer-coverpreview-outer"),_=b.querySelector(".xgplayer-coverpreview-point"),d=b.querySelector(".xgplayer-coverpreview-thumbnail"),e.root.appendChild(b)),f=e.config.thumbnail.pic_num,h=e.config.thumbnail.width,g=e.config.thumbnail.height,m=e.config.thumbnail.col,y=e.config.thumbnail.row,x=e.config.thumbnail.urls,d.style.width=h+"px",d.style.height=g+"px")};e.on("loadedmetadata",k),"function"===typeof e.config.disableSwipeHandler&&"function"===typeof e.config.enableSwipeHandler&&(e.root.addEventListener("touchmove",(function(t){t.preventDefault(),e.disableSwipe||(e.disableSwipe=!0,e.config.disableSwipeHandler.call(e))})),e.root.addEventListener("touchstart",(function(t){e.disableSwipe=!0,e.config.disableSwipeHandler.call(e)})),e.root.addEventListener("touchend",(function(t){e.disableSwipe=!1,e.config.enableSwipeHandler.call(e)})));var w=["touchstart","mousedown"];"mobile"===a.default.device&&w.pop(),w.forEach((function(i){t.addEventListener(i,(function(i){if(!e.config.disableProgress){if(i.stopPropagation(),(0,r.event)(i),i._target===c||!e.config.allowSeekAfterEnded&&e.ended)return!0;t.focus();var l=o.getBoundingClientRect().left,u=s(e);u?(l=o.getBoundingClientRect().top,n=t.getBoundingClientRect().height):(n=t.getBoundingClientRect().width,l=o.getBoundingClientRect().left);var p=function(t){t.stopPropagation(),(0,r.event)(t),e.isProgressMoving=!0;var i=(u?t.clientY:t.clientX)-l;i>n&&(i=n);var a=i/n*e.duration;if(a<0&&(a=0),e.config.allowSeekPlayed&&Number(a).toFixed(1)>e.maxPlayedTime);else if(o.style.width=100*i/n+"%","video"!==e.videoConfig.mediaType||e.dash||e.config.closeMoveSeek){var s=(0,r.findDom)(e.controls,".xgplayer-time");s&&(s.innerHTML='<span class="xgplayer-time-current">'+(0,r.format)(a||0)+"</span><span>"+(0,r.format)(e.duration)+"</span>")}else console.log("trigger touchmove"),e.currentTime=Number(a).toFixed(1);if(e.config.thumbnail&&e.config.thumbnail.isShowCoverPreview){_.innerHTML="<span>"+(0,r.format)(a)+"</span> / "+(0,r.format)(e.duration||0),v=e.duration/f;var c=Math.floor(a/v);d.style.backgroundImage="url("+x[Math.ceil((c+1)/(m*y))-1]+")";var p=c+1-m*y*(Math.ceil((c+1)/(m*y))-1),k=Math.ceil(p/y)-1,w=p-k*y-1;d.style["background-position"]="-"+w*h+"px -"+k*g+"px",b.style.display="block"}e.emit("focus")},k=function i(s){if(console.log("up event",s),s.stopPropagation(),(0,r.event)(s),window.removeEventListener("mousemove",p),window.removeEventListener("touchmove",p,{passive:!1}),window.removeEventListener("mouseup",i),window.removeEventListener("touchend",i),a.default.browser.indexOf("ie")<0&&t.blur(),!e.isProgressMoving||e.videoConfig&&"audio"===e.videoConfig.mediaType||e.dash||e.config.closeMoveSeek){var c=(u?s.clientY:s.clientX)-l;c>n&&(c=n);var d=c/n*e.duration;d<0&&(d=0),e.config.allowSeekPlayed&&Number(d).toFixed(1)>e.maxPlayedTime||(o.style.width=100*c/n+"%",console.warn("trigger touchup"),e.currentTime=Number(d).toFixed(1))}e.config.thumbnail&&e.config.thumbnail.isShowCoverPreview&&(b.style.display="none"),e.emit("focus"),e.isProgressMoving=!1};return window.addEventListener("touchmove",p,{passive:!1}),window.addEventListener("touchend",k),window.addEventListener("mousemove",p),window.addEventListener("mouseup",k),!0}}))})),t.addEventListener("mouseenter",(function(n){if(!e.config.allowSeekAfterEnded&&e.ended)return!0;var o=s(e),i=o?t.getBoundingClientRect().top:t.getBoundingClientRect().left,a=o?t.getBoundingClientRect().height:t.getBoundingClientRect().width,l=function(n){var s=((o?n.clientY:n.clientX)-i)/a*e.duration;s=s<0?0:s,c.textContent=(0,r.format)(s);var l=c.getBoundingClientRect().width;if(e.config.thumbnail&&!e.config.thumbnail.isShowCoverPreview){v=e.duration/f;var u=Math.floor(s/v);d.style.backgroundImage="url("+x[Math.ceil((u+1)/(m*y))-1]+")";var p=u+1-m*y*(Math.ceil((u+1)/(m*y))-1),b=Math.ceil(p/y)-1,_=p-b*y-1;d.style["background-position"]="-"+_*h+"px -"+b*g+"px";var k=(o?n.clientY:n.clientX)-i-h/2;k=(k=k>0?k:0)<a-h?k:a-h,d.style.left=k+"px",d.style.top=-10-g+"px",d.style.display="block",c.style.left=k+h/2-l/2+"px"}else{var w=n.clientX-i-l/2;w=(w=w>0?w:0)>a-l?a-l:w,c.style.left=w+"px"}(0,r.hasClass)(t,"xgplayer-progress-dot-active")?c.style.display="none":c.style.display="block"},u=function(e){l(e)};t.addEventListener("mousemove",u,!1),t.addEventListener("mouseleave",(function n(o){t.removeEventListener("mousemove",u,!1),t.removeEventListener("mouseleave",n,!1),l(o),c.style.display="none",e.config.thumbnail&&!e.config.thumbnail.isShowCoverPreview&&(d.style.display="none")}),!1),l(n)}),!1);var C=function(){if(void 0===e.maxPlayedTime&&(e.maxPlayedTime=0),e.maxPlayedTime<e.currentTime&&(e.maxPlayedTime=e.currentTime),!n&&t&&(n=t.getBoundingClientRect().width),!e.isProgressMoving&&!e.isSeeking&&!e.seeking){var r=e.currentTime/e.duration,i=Number(o.style.width.replace("%","")||"0")/Number(t.style.width||"100");Math.abs(r-i)<=1&&(o.style.width=100*e.currentTime/e.duration+"%")}};e.on("timeupdate",C);var S=function(t){o.style.width=100*t/e.duration+"%"};e.on("currentTimeChange",S);var E=function(){o.style.width="0%"};e.on("srcChange",E);var T=function(){var t=e.buffered;if(t&&t.length>0){for(var n=t.end(t.length-1),o=0,r=t.length;o<r;o++)if(e.currentTime>=t.start(o)&&e.currentTime<=t.end(o)){n=t.end(o);for(var i=o+1;i<t.length;i++)if(t.start(i)-t.end(i-1)>=2){n=t.end(i-1);break}break}l.style.width=n/e.duration*100+"%"}},D=["bufferedChange","cacheupdate","ended","timeupdate"];D.forEach((function(t){e.on(t,T)})),e.once("destroy",(function t(){e.removeAllProgressDot(),e.off("canplay",p),e.off("timeupdate",C),e.off("currentTimeChange",S),e.off("srcChange",E),e.off("loadedmetadata",k),D.forEach((function(t){e.off(t,T)})),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){var o=n(71);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-progress{display:block;position:absolute;height:20px;line-height:20px;left:12px;right:12px;outline:none;top:-15px;z-index:35}.xgplayer-skin-default .xgplayer-progress-outer{background:hsla(0,0%,100%,.3);display:block;height:3px;line-height:3px;margin-top:8.5px;width:100%;position:relative;cursor:pointer}.xgplayer-skin-default .xgplayer-progress-cache,.xgplayer-skin-default .xgplayer-progress-played{display:block;height:100%;line-height:1;position:absolute;left:0;top:0}.xgplayer-skin-default .xgplayer-progress-cache{width:0;background:hsla(0,0%,100%,.5)}.xgplayer-skin-default .xgplayer-progress-played{display:block;width:0;background-image:linear-gradient(-90deg,#fa1f41,#e31106);border-radius:0 1.5px 1.5px 0}.xgplayer-skin-default .xgplayer-progress-btn{display:none;position:absolute;left:0;top:-5px;width:13px;height:13px;border-radius:30px;background:#fff;box-shadow:0 0 2px 0 rgba(0,0,0,.26);left:100%;-webkit-transform:translate(-50%);-ms-transform:translate(-50%);transform:translate(-50%);z-index:36}.xgplayer-skin-default .xgplayer-progress-point{position:absolute}.xgplayer-skin-default .xgplayer-progress-point.xgplayer-tips{margin-left:0;top:-25px;display:none;z-index:100}.xgplayer-skin-default .xgplayer-progress-dot{display:inline-block;position:absolute;height:3px;width:5px;top:0;background:#fff;border-radius:6px;z-index:16}.xgplayer-skin-default .xgplayer-progress-dot .xgplayer-progress-tip{position:absolute;bottom:200%;right:50%;-webkit-transform:translateX(50%);-ms-transform:translateX(50%);transform:translateX(50%);height:auto;line-height:30px;width:auto;background:rgba(0,0,0,.3);border-radius:6px;border:1px solid rgba(0,0,0,.8);cursor:default;white-space:nowrap;display:none}.xgplayer-skin-default .xgplayer-progress-dot-show .xgplayer-progress-tip{display:block}.xgplayer-skin-default .xgplayer-progress-thumbnail{position:absolute;-moz-box-sizing:border-box;box-sizing:border-box}.xgplayer-skin-default .xgplayer-progress-thumbnail.xgplayer-tips{margin-left:0;display:none;z-index:99}.xgplayer-skin-default .xgplayer-coverpreview{position:absolute;width:100%;height:100%;top:0;left:0;display:none}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer{position:absolute;display:block;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer .xgplayer-coverpreview-thumbnail{display:block}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer .xgplayer-coverpreview-point{display:block;text-align:center;font-family:PingFangSC-Regular;font-size:11px;color:#ccc;padding:2px 4px}.xgplayer-skin-default .xgplayer-coverpreview .xgplayer-coverpreview-outer .xgplayer-coverpreview-point span{color:#fff}.xgplayer-skin-default .xgplayer-progress:focus .xgplayer-progress-outer,.xgplayer-skin-default .xgplayer-progress:hover .xgplayer-progress-outer{height:6px;margin-top:7px}.xgplayer-skin-default .xgplayer-progress:focus .xgplayer-progress-dot,.xgplayer-skin-default .xgplayer-progress:hover .xgplayer-progress-dot{height:6px}.xgplayer-skin-default .xgplayer-progress:focus .xgplayer-progress-btn,.xgplayer-skin-default .xgplayer-progress:hover .xgplayer-progress-btn{display:block;top:-3px}.xgplayer-skin-default.xgplayer-definition-active .xgplayer-progress,.xgplayer-skin-default.xgplayer-playbackrate-active .xgplayer-progress,.xgplayer-skin-default.xgplayer-texttrack-active .xgplayer-progress,.xgplayer-skin-default.xgplayer-volume-active .xgplayer-progress{z-index:15}.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress-btn{display:block!important}.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:focus .xgplayer-progress-outer,.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:hover .xgplayer-progress-outer{height:3px!important;margin-top:8.5px!important}.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:focus .xgplayer-progress-btn,.xgplayer-skin-default.xgplayer-mobile .xgplayer-progress:hover .xgplayer-progress-btn{display:block!important;top:-5px!important}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(73),t.default={name:"s_time",method:function(){var e=this,t=(0,o.createDom)("xg-time",'<span class="xgplayer-time-current">'+(e.currentTime||(0,o.format)(0))+"</span>\n                                           <span>"+(e.duration||(0,o.format)(0))+"</span>",{},"xgplayer-time");e.once("ready",(function(){e.controls&&e.controls.appendChild(t)}));var n=function(){"audio"===e.videoConfig.mediaType&&e.isProgressMoving&&e.dash||(t.innerHTML='<span class="xgplayer-time-current">'+(0,o.format)(e.currentTime||0)+"</span><span>"+(0,o.format)(e.duration)+"</span>")};e.on("durationchange",n),e.on("timeupdate",n),e.once("destroy",(function t(){e.off("durationchange",n),e.off("timeupdate",n),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){var o=n(74);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,'.xgplayer-skin-default .xgplayer-time{-webkit-order:2;-moz-box-ordinal-group:3;order:2;font-family:ArialMT;font-size:13px;color:#fff;line-height:40px;height:40px;text-align:center;display:inline-block;margin:auto 8px}.xgplayer-skin-default .xgplayer-time span{color:hsla(0,0%,100%,.5)}.xgplayer-skin-default .xgplayer-time .xgplayer-time-current{color:#fff}.xgplayer-skin-default .xgplayer-time .xgplayer-time-current:after{content:"/";display:inline-block;padding:0 3px}',""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(76),a=(o=i)&&o.__esModule?o:{default:o};n(77),t.default={name:"s_replay",method:function(){var e=this,t=e.root,n=e.lang.REPLAY,o=(0,r.createDom)("xg-replay",a.default+'\n                                         <xg-replay-txt class="xgplayer-replay-txt">'+n+"</xg-replay-txt>\n                                        ",{},"xgplayer-replay");function i(){var e=o.querySelector("path");if(e){var t=window.getComputedStyle(e).getPropertyValue("transform");if("string"===typeof t&&t.indexOf("none")>-1)return;e.setAttribute("transform",t)}}e.once("ready",(function(){t.appendChild(o)})),e.on("ended",i),o.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation()}));var s=o.querySelector("svg");["click","touchend"].forEach((function(t){s.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("replayBtnClick")}))})),e.once("destroy",(function t(){e.off("ended",i),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg class="xgplayer-replay-svg" xmlns="http://www.w3.org/2000/svg" width="78" height="78" viewbox="0 0 78 78">\n  <path d="M8.22708362,13.8757234 L11.2677371,12.6472196 C11.7798067,12.4403301 12.3626381,12.6877273 12.5695276,13.1997969 L12.9441342,14.1269807 C13.1510237,14.6390502 12.9036264,15.2218816 12.3915569,15.4287712 L6.8284538,17.6764107 L5.90126995,18.0510173 C5.38920044,18.2579068 4.80636901,18.0105096 4.5994795,17.49844 L1.97723335,11.0081531 C1.77034384,10.4960836 2.0177411,9.91325213 2.52981061,9.70636262 L3.45699446,9.33175602 C3.96906396,9.12486652 4.5518954,9.37226378 4.75878491,9.88433329 L5.67885163,12.1615783 C7.99551726,6.6766934 13.3983951,3 19.5,3 C27.7842712,3 34.5,9.71572875 34.5,18 C34.5,26.2842712 27.7842712,33 19.5,33 C15.4573596,33 11.6658607,31.3912946 8.87004692,28.5831991 C8.28554571,27.9961303 8.28762719,27.0463851 8.87469603,26.4618839 C9.46176488,25.8773827 10.4115101,25.8794641 10.9960113,26.466533 C13.2344327,28.7147875 16.263503,30 19.5,30 C26.127417,30 31.5,24.627417 31.5,18 C31.5,11.372583 26.127417,6 19.5,6 C14.4183772,6 9.94214483,9.18783811 8.22708362,13.8757234 Z"></path>\n</svg>\n'},function(e,t,n){var o=n(78);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-replay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:105;display:none;-webkit-justify-content:center;-moz-box-pack:center;justify-content:center;-webkit-align-items:center;-moz-box-align:center;align-items:center;background:rgba(0,0,0,.54);-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;flex-direction:column}.xgplayer-skin-default .xgplayer-replay svg{background:rgba(0,0,0,.58);border-radius:100%;cursor:pointer}.xgplayer-skin-default .xgplayer-replay svg path{-webkit-transform:translate(20px,21px);-ms-transform:translate(20px,21px);transform:translate(20px,21px);fill:#ddd}.xgplayer-skin-default .xgplayer-replay svg:hover{background:rgba(0,0,0,.38)}.xgplayer-skin-default .xgplayer-replay svg:hover path{fill:#fff}.xgplayer-skin-default .xgplayer-replay .xgplayer-replay-txt{display:inline-block;font-family:PingFangSC-Regular;font-size:14px;color:#fff;line-height:34px}.xgplayer-skin-default.xgplayer.xgplayer-ended .xgplayer-controls{display:none}.xgplayer-skin-default.xgplayer.xgplayer-ended .xgplayer-replay{display:-webkit-flex;display:-moz-box;display:flex}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=[{code:0,msg:"SUCCESS"},{code:1,msg:"LOAD_ERROR"},{code:2,msg:"PARSER_ERROR"},{code:3,msg:"FORMAT_NOT_SUPPORTED"},{code:4,msg:"ID_OR_LANGUAGE_NOT_EXIST"},{code:5,msg:"PARAMETERS_ERROR"},{code:6,msg:"ABORT"},{code:7,msg:"UNKNOWN"},{code:8,msg:"DATA_ERROR:subtitle.url is null"},{code:9,msg:"DATA_ERROR:subtitle.url length is 0"}];t._ERROR=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n={code:o[e].code,msg:o[e].msg};return Object.keys(t).map((function(e){n[e]=t[e]})),n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(11),r=function(){function e(){var t,n;(0,o.classCallCheck)(this,e);var r=new Promise((function(e,o){t=e,n=o}));return r.resolve=function(e){t(e),r.state="fulfilled"},r.reject=function(e){n(e),r.state="rejected",r.isBreak="DESTROYED"===e},r.state="pending",r}return(0,o.createClass)(e,[{key:"resolve",value:function(e){}},{key:"reject",value:function(e){}}]),e}();t.default=r},function(e,t,n){var o=n(170);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){e.exports=n(83)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=D(n(9)),r=D(n(84)),i=D(n(85)),a=D(n(86)),s=D(n(87)),l=D(n(88)),c=D(n(91)),d=D(n(46)),u=D(n(92)),p=D(n(93)),f=D(n(94)),h=D(n(95)),g=D(n(38)),m=D(n(39)),y=D(n(101)),v=D(n(47)),x=D(n(102)),b=D(n(103)),_=D(n(48)),k=D(n(104)),w=D(n(105)),C=D(n(106)),S=D(n(40)),E=D(n(107)),T=D(n(108));function D(e){return e&&e.__esModule?e:{default:e}}n(116),o.default.installAll([r.default,i.default,a.default,s.default,l.default,c.default,d.default,u.default,p.default,f.default,h.default,g.default,m.default,y.default,v.default,x.default,b.default,_.default,k.default,w.default,C.default,S.default,E.default,T.default]),t.default=o.default,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"airplay",method:function(){var e=this;function t(){e.video.webkitShowPlaybackTargetPicker()}e.config.airplay&&window.WebKitPlaybackTargetAvailabilityEvent&&(e.on("airplayBtnClick",t),e.once("destroy",(function n(){e.off("airplayBtnClick",t),e.off("destroy",n)})))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"cssFullscreen",method:function(){var e=this,t=e.root;function n(){(0,o.hasClass)(t,"xgplayer-is-cssfullscreen")?e.exitCssFullscreen():e.getCssFullscreen()}e.on("cssFullscreenBtnClick",n),e.on("exitFullscreen",(function(){(0,o.removeClass)(t,"xgplayer-is-cssfullscreen")})),e.once("destroy",(function t(){e.off("cssFullscreenBtnClick",n),e.off("destroy",t)})),e.getCssFullscreen=function(){var e=this;e.config.fluid&&(e.root.style["padding-top"]=""),(0,o.addClass)(e.root,"xgplayer-is-cssfullscreen"),e.emit("requestCssFullscreen")},e.exitCssFullscreen=function(){var e=this;e.config.fluid&&(e.root.style.width="100%",e.root.style.height="0",e.root.style["padding-top"]=100*e.config.height/e.config.width+"%"),(0,o.removeClass)(e.root,"xgplayer-is-cssfullscreen"),e.emit("exitCssFullscreen")}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"danmu",method:function(){var e=this;e.on("initDefaultDanmu",(function(t){var n=e.root.querySelector("xg-danmu");if((0,o.addClass)(n,"xgplayer-has-danmu"),!e.config.danmu.closeDefaultBtn){var r=function(){t.start()},i=function(){(0,o.hasClass)(e.danmuBtn,"danmu-switch-active")&&t.pause()},a=function(){(0,o.hasClass)(e.danmuBtn,"danmu-switch-active")&&t.play()},s=function(){(0,o.hasClass)(e.danmuBtn,"danmu-switch-active")&&(t.stop(),t.start())};e.danmuBtn=(0,o.copyDom)(t.bulletBtn.createSwitch(!0)),e.controls.appendChild(e.danmuBtn),["click","touchend"].forEach((function(i){e.danmuBtn.addEventListener(i,(function(i){i.preventDefault(),i.stopPropagation(),(0,o.toggleClass)(e.danmuBtn,"danmu-switch-active"),(0,o.hasClass)(e.danmuBtn,"danmu-switch-active")?(e.emit("danmuBtnOn"),(0,o.addClass)(n,"xgplayer-has-danmu"),e.once("timeupdate",r)):(e.emit("danmuBtnOff"),(0,o.removeClass)(n,"xgplayer-has-danmu"),t.stop())}))})),e.onElementClick&&n.addEventListener("click",(function(t){e.onElementClick(t,n)}),!1),e.onElementDblclick&&n.addEventListener("dblclick",(function(t){e.onElementDblclick(t,n)}),!1),e.on("pause",i),e.on("play",a),e.on("seeked",s),e.once("destroy",(function t(){e.off("timeupdate",r),e.off("pause",i),e.off("play",a),e.off("seeked",s),e.off("destroy",t)}))}}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"definition",method:function(){var e=this;e.once("destroy",(function t(){e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(89),i=(o=r)&&o.__esModule?o:{default:o},a=n(90);t.default={name:"download",method:function(){var e=this;function t(){e.download()}e.on("downloadBtnClick",t),e.once("destroy",(function n(){e.off("downloadBtnClick",t),e.off("destroy",n)})),e.download=function(){var e=(0,a.getAbsoluteURL)(this.config.url);(0,i.default)(e)}}},e.exports=t.default},function(e,t,n){"use strict";var o,r,i;"function"===typeof Symbol&&Symbol.iterator,r=[],void 0===(i="function"===typeof(o=function(){return function e(t,n,o){var r,i,a=window,s="application/octet-stream",l=o||s,c=t,d=!n&&!o&&c,u=document.createElement("a"),p=function(e){return String(e)},f=a.Blob||a.MozBlob||a.WebKitBlob||p,h=n||"download";if(f=f.call?f.bind(a):Blob,"true"===String(this)&&(l=(c=[c,l])[0],c=c[1]),d&&d.length<2048&&(h=d.split("/").pop().split("?")[0],u.href=d,-1!==u.href.indexOf(d))){var g=new XMLHttpRequest;return g.open("GET",d,!0),g.responseType="blob",g.onload=function(t){e(t.target.response,h,s)},setTimeout((function(){g.send()}),0),g}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(c)){if(!(c.length>2096103.424&&f!==p))return navigator.msSaveBlob?navigator.msSaveBlob(x(c),h):b(c);l=(c=x(c)).type||s}else if(/([\x80-\xff])/.test(c)){for(var m=0,y=new Uint8Array(c.length),v=y.length;m<v;++m)y[m]=c.charCodeAt(m);c=new f([y],{type:l})}function x(e){for(var t=e.split(/[:;,]/),n=t[1],o=("base64"==t[2]?atob:decodeURIComponent)(t.pop()),r=o.length,i=0,a=new Uint8Array(r);i<r;++i)a[i]=o.charCodeAt(i);return new f([a],{type:n})}function b(e,t){if("download"in u)return u.href=e,u.setAttribute("download",h),u.className="download-js-link",u.innerHTML="downloading...",u.style.display="none",document.body.appendChild(u),setTimeout((function(){u.click(),document.body.removeChild(u),!0===t&&setTimeout((function(){a.URL.revokeObjectURL(u.href)}),250)}),66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,s)),window.open(e)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=e),!0;var n=document.createElement("iframe");document.body.appendChild(n),!t&&/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,s)),n.src=e,setTimeout((function(){document.body.removeChild(n)}),333)}if(r=c instanceof f?c:new f([c],{type:l}),navigator.msSaveBlob)return navigator.msSaveBlob(r,h);if(a.URL)b(a.URL.createObjectURL(r),!0);else{if("string"===typeof r||r.constructor===p)try{return b("data:"+l+";base64,"+a.btoa(r))}catch(_){return b("data:"+l+","+encodeURIComponent(r))}(i=new FileReader).onload=function(e){b(this.result)},i.readAsDataURL(r)}return!0}})?o.apply(t,r):o)||(e.exports=i)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getAbsoluteURL=function(e){if(!e.match(/^https?:\/\//)){var t=document.createElement("div");t.innerHTML='<a href="'+e+'">x</a>',e=t.firstChild.href}return e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(4),i=(o=r)&&o.__esModule?o:{default:o},a={maxCount:3,backupUrl:"",isFetch:!0,fetchTimeout:100};t.default={name:"errorretry",method:function(){var e=this,t=this;if(t.config.errorConfig&&!(t.src.indexOf("blob:")>-1)){var n={},o=t.config.errorConfig;for(var r in a)void 0===o[r]?n[r]=a[r]:n[r]=o[r];t.retryData={count:0,errfTimer:null,isFetchReturn:!1,currentTime:0};var s=t._onError;t._onError=function(o){var r=e.retryData.count;if(r>n.maxCount)n.isFetch?function(e,t,n){var o=function(t,n){e.retryData.isFetchReturn||(e.retryData.isFetchReturn=!0,t(n))};return new Promise((function(r,i){try{var a=new window.XMLHttpRequest;a.open("get",t),a.onload=function(){o(r,{status:a.status,statusText:a.statusText,xhr:a})},a.onerror=function(){o(r,{status:a.status,statusText:a.statusText||"The network environment is disconnected or the address is invalid",xhr:a})},a.onabort=function(){},e.retryData.errfTimer=window.setTimeout((function(){var t=e.retryData.errfTimer;window.clearTimeout(t),e.retryData.errfTimer=null,o(r,{status:-1,statusText:"request timeout"})}),n),a.send()}catch(s){e.retryData.isFetchReturn=!0,o(r,{status:-2,statusText:"request error"})}}))}(e,e.currentSrc,n.fetchTimeout).then((function(t){e.emit("error",new i.default({type:"network",currentTime:e.currentTime,duration:e.duration||0,networkState:e.networkState,readyState:e.readyState,currentSrc:e.currentSrc,src:e.src,ended:e.ended,httpCode:t.status,httpMsg:t.statusText,errd:{line:101,msg:e.error,handle:"plugin errorRetry"},errorCode:e.video&&e.video.error.code,mediaError:e.video&&e.video.error})),s.call(e,t)})):s.call(e,o);else{0===r&&(e.retryData.currentTime=e.currentTime,e.once("canplay",l.bind(e)));var a="";a=n.count<2?n.backupUrl?n.backupUrl:t.currentSrc:n.backupUrl&&r>1?n.backupUrl:t.currentSrc,e.retryData.count++,e.src=a}}}function l(){this.currentTime=this.retryData.currentTime,this.play(),this.retryData.retryCode=0,this.retryData.isFetchReturn=!1,this.retryData.currentTime=0}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=n(0),i=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.player=t,this.state={playbackRate:0,isRepeat:!1,keyCode:0,repeat:0,isBody:!1},this.timer=null,this.initEvents()}return o(e,[{key:"initEvents",value:function(){var e=this,t=this.player,n=t.root,o=t.config;this.player.onBodyKeydown=this.onBodyKeydown.bind(this),this.player.onKeydown=this.onKeydown.bind(this),this.player.onKeyup=this.onKeyup.bind(this),o.keyShortcut&&"on"!==o.keyShortcut||(document.addEventListener("keydown",this.player.onBodyKeydown),n.addEventListener("keydown",this.player.onKeydown),(0,r.on)(this.player,"destroy",(function(){document.removeEventListener("keydown",e.player.onBodyKeydown),n.removeEventListener("keydown",e.player.onKeydown),clearTimeout(e.timer),e.timer=null})))}},{key:"checkTarget",value:function(e){var t=this.player;return e.target===t.root||e.target===t.video||e.target===t.controls}},{key:"onBodyKeydown",value:function(e){var t=e||window.event,n=t.keyCode;if(t.target===document.body&&(37===n||39===n||32===n))return t.preventDefault(),t.cancelBubble=!0,t.returnValue=!1,t.repeat||document.addEventListener("keyup",this.player.onKeyup),this.handler(t),!1}},{key:"onKeydown",value:function(e){var t=e||window.event,n=t.keyCode;if(this.checkTarget(t)&&(37===n||38===n||39===n||40===n||32===n||27===n))return t.preventDefault(),t.cancelBubble=!0,t.returnValue=!1,this.player.emit("focus"),t.repeat||this.player.root.addEventListener("keyup",this.player.onKeyup),this.handler(t),!1}},{key:"onKeyup",value:function(){var e=this.state,t=this.player;document.removeEventListener("keyup",this.player.onKeyup),t.root.removeEventListener("keyup",this.player.onKeyup),e.keyCode&&(0!==e.playbackRate&&(t.playbackRate=e.playbackRate),e.isRepeat||this.handlerKeyCode(e.keyCode,!1),e.playbackRate=0,e.isRepeat=!1,e.keyCode=0,e.repeat=0,this.changeVolumeSlide())}},{key:"handler",value:function(e){var t=this.state,n=this.player;t.keyCode=e.keyCode,t.isRepeat=e.repeat,e.repeat&&(n.config.disableLongPress?this.handlerKeyCode(t.keyCode,!1):t.repeat%2===0&&this.handlerKeyCode(t.keyCode,!0),t.repeat++)}},{key:"handlerKeyCode",value:function(e,t){var n=this.player,o=this.state;switch(e){case 39:t?0===o.repeat&&this.changeRate():this.seek(!1,t);break;case 37:this.seek(!0,t);break;case 38:this.changeVolume(!0);break;case 40:this.changeVolume(!1);break;case 32:t||(n.paused?n.play():n.pause());break;case 27:(0,r.hasClass)(n.root,"xgplayer-is-cssfullscreen")&&n.exitCssFullscreen()}}},{key:"seek",value:function(e,t){var n=this.player,o=(n.config.keyShortcutStep||{}).currentTime||10;n.isLoading||n.isSeeking||t&&this.state.repeat%8>0||(e?n.currentTime-o>=0?n.currentTime-=o:n.currentTime=0:n.maxPlayedTime&&n.config.allowSeekPlayed&&n.currentTime+o>n.maxPlayedTime?n.currentTime=n.maxPlayedTime:n.currentTime+o<=n.duration?n.currentTime+=o:n.currentTime=n.duration+1)}},{key:"changeRate",value:function(){this.state.playbackRate=this.player.playbackRate,this.player.playbackRate=this.player.config.keyboardRate||5}},{key:"changeVolumeSlide",value:function(e){var t=this.player;t.controls&&(e?(t.emit("focus"),(0,r.hasClass)(t.root,"xgplayer-volume-active")||(0,r.addClass)(t.root,"xgplayer-volume-active")):(clearTimeout(this.timer),this.timer=setTimeout((function(){(0,r.removeClass)(t.root,"xgplayer-volume-active")}),1e3)))}},{key:"changeVolume",value:function(e){var t=this.player,n=(t.config.keyShortcutStep||{}).volume||.1;this.changeVolumeSlide(!0);var o=t.volume;e&&o+n<=1?t.volume=o+n:!e&&o-n>=0&&(t.volume=o-n)}}]),e}();t.default={name:"keyboard",method:function(){this.keyboard=new i(this)}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"localPreview",method:function(){var e=this,t=e.root;function n(n){e.uploadFile=n.files[0];var r=URL.createObjectURL(e.uploadFile);if((0,o.hasClass)(t,"xgplayer-nostart"))e.config.url=r,e.start();else{e.src=r;var i=e.play();void 0!==i&&i&&i.catch((function(e){}))}}e.on("upload",n),e.once("destroy",(function t(){e.off("upload",n),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"memoryPlay",method:function(){var e=this;e.on("memoryPlayStart",(function(t){setTimeout((function(){console.log("memoryPlayStart",t,e.readyState,11),e.currentTime=t}))}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(96),a=(o=i)&&o.__esModule?o:{default:o};t.default={name:"miniplayer",method:function(){var e=this,t=e.root;function n(){(0,r.hasClass)(t,"xgplayer-miniplayer-active")?e.exitMiniplayer():e.getMiniplayer()}e.on("miniplayerBtnClick",n),e.once("destroy",(function t(){e.off("miniplayerBtnClick",n),e.off("destroy",t)})),e.getMiniplayer=function(){(0,r.hasClass)(t,"xgplayer-is-fullscreen")&&this.exitFullscreen(t),(0,r.hasClass)(t,"xgplayer-is-cssfullscreen")&&this.exitCssFullscreen(),(0,r.hasClass)(t,"xgplayer-rotate-fullscreen")&&this.exitRotateFullscreen();var e=(0,r.createDom)("xg-miniplayer-lay","<div></div>",{},"xgplayer-miniplayer-lay");this.root.appendChild(e);var n=(0,r.createDom)("xg-miniplayer-drag",'<div class="drag-handle"><span>'+this.lang.MINIPLAYER_DRAG+"</span></div>",{tabindex:9},"xgplayer-miniplayer-drag");this.root.appendChild(n),new a.default(".xgplayer",{handle:".drag-handle"}),(0,r.addClass)(this.root,"xgplayer-miniplayer-active"),this.root.style.right=0,this.root.style.bottom="200px",this.root.style.top="",this.root.style.left="",this.root.style.width="320px",this.root.style.height="180px",this.config.miniplayerConfig&&(void 0!==this.config.miniplayerConfig.top&&(this.root.style.top=this.config.miniplayerConfig.top+"px",this.root.style.bottom=""),void 0!==this.config.miniplayerConfig.bottom&&(this.root.style.bottom=this.config.miniplayerConfig.bottom+"px"),void 0!==this.config.miniplayerConfig.left&&(this.root.style.left=this.config.miniplayerConfig.left+"px",this.root.style.right=""),void 0!==this.config.miniplayerConfig.right&&(this.root.style.right=this.config.miniplayerConfig.right+"px"),void 0!==this.config.miniplayerConfig.width&&(this.root.style.width=this.config.miniplayerConfig.width+"px"),void 0!==this.config.miniplayerConfig.height&&(this.root.style.height=this.config.miniplayerConfig.height+"px")),this.config.fluid&&(this.root.style["padding-top"]="");var o=this;["click","touchend"].forEach((function(t){e.addEventListener(t,(function(e){e.preventDefault(),e.stopPropagation(),o.exitMiniplayer()}))}))},e.exitMiniplayer=function(){(0,r.removeClass)(this.root,"xgplayer-miniplayer-active"),this.root.style.right="",this.root.style.bottom="",this.root.style.top="",this.root.style.left="",this.config.fluid?(this.root.style.width="100%",this.root.style.height="0",this.root.style["padding-top"]=100*this.config.height/this.config.width+"%"):(this.config.width&&("number"!==typeof this.config.width?this.root.style.width=this.config.width:this.root.style.width=this.config.width+"px"),this.config.height&&("number"!==typeof this.config.height?this.root.style.height=this.config.height:this.root.style.height=this.config.height+"px"));var e=(0,r.findDom)(this.root,".xgplayer-miniplayer-lay");e&&e.parentNode&&e.parentNode.removeChild(e);var t=(0,r.findDom)(this.root,".xgplayer-miniplayer-drag");t&&t.parentNode&&t.parentNode.removeChild(t)}}},e.exports=t.default},function(e,t,n){"use strict";var o,r;"function"===typeof Symbol&&Symbol.iterator,function(i,a){o=[n(97),n(98)],r=function(e,t){return function(e,t,n){function o(e,t){for(var n in t)e[n]=t[n];return e}function r(){}var i=e.jQuery;function a(e,t){this.element="string"==typeof e?document.querySelector(e):e,i&&(this.$element=i(this.element)),this.options=o({},this.constructor.defaults),this.option(t),this._create()}var s=a.prototype=Object.create(n.prototype);a.defaults={},s.option=function(e){o(this.options,e)};var l={relative:!0,absolute:!0,fixed:!0};function c(e,t,n){return n=n||"round",t?Math[n](e/t)*t:e}return s._create=function(){this.position={},this._getPosition(),this.startPoint={x:0,y:0},this.dragPoint={x:0,y:0},this.startPosition=o({},this.position);var e=getComputedStyle(this.element);l[e.position]||(this.element.style.position="relative"),this.on("pointerMove",this.onPointerMove),this.on("pointerUp",this.onPointerUp),this.enable(),this.setHandles()},s.setHandles=function(){this.handles=this.options.handle?this.element.querySelectorAll(this.options.handle):[this.element],this.bindHandles()},s.dispatchEvent=function(e,t,n){var o=[t].concat(n);this.emitEvent(e,o),this.dispatchJQueryEvent(e,t,n)},s.dispatchJQueryEvent=function(t,n,o){var r=e.jQuery;if(r&&this.$element){var i=r.Event(n);i.type=t,this.$element.trigger(i,o)}},s._getPosition=function(){var e=getComputedStyle(this.element),t=this._getPositionCoord(e.left,"width"),n=this._getPositionCoord(e.top,"height");this.position.x=isNaN(t)?0:t,this.position.y=isNaN(n)?0:n,this._addTransformPosition(e)},s._getPositionCoord=function(e,n){if(-1!=e.indexOf("%")){var o=t(this.element.parentNode);return o?parseFloat(e)/100*o[n]:0}return parseInt(e,10)},s._addTransformPosition=function(e){var t=e.transform;if(0===t.indexOf("matrix")){var n=t.split(","),o=0===t.indexOf("matrix3d")?12:4,r=parseInt(n[o],10),i=parseInt(n[o+1],10);this.position.x+=r,this.position.y+=i}},s.onPointerDown=function(e,t){this.element.classList.add("is-pointer-down"),this.dispatchJQueryEvent("pointerDown",e,[t])},s.pointerDown=function(e,t){this.okayPointerDown(e)&&this.isEnabled?(this.pointerDownPointer={pageX:t.pageX,pageY:t.pageY},e.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(e),this.element.classList.add("is-pointer-down"),this.dispatchEvent("pointerDown",e,[t])):this._pointerReset()},s.dragStart=function(e,t){this.isEnabled&&(this._getPosition(),this.measureContainment(),this.startPosition.x=this.position.x,this.startPosition.y=this.position.y,this.setLeftTop(),this.dragPoint.x=0,this.dragPoint.y=0,this.element.classList.add("is-dragging"),this.dispatchEvent("dragStart",e,[t]),this.animate())},s.measureContainment=function(){var e=this.getContainer();if(e){var n=t(this.element),o=t(e),r=this.element.getBoundingClientRect(),i=e.getBoundingClientRect(),a=o.borderLeftWidth+o.borderRightWidth,s=o.borderTopWidth+o.borderBottomWidth,l=this.relativeStartPosition={x:r.left-(i.left+o.borderLeftWidth),y:r.top-(i.top+o.borderTopWidth)};this.containSize={width:o.width-a-l.x-n.width,height:o.height-s-l.y-n.height}}},s.getContainer=function(){var e=this.options.containment;if(e)return e instanceof HTMLElement?e:"string"==typeof e?document.querySelector(e):this.element.parentNode},s.onPointerMove=function(e,t,n){this.dispatchJQueryEvent("pointerMove",e,[t,n])},s.dragMove=function(e,t,n){if(this.isEnabled){var o=n.x,r=n.y,i=this.options.grid,a=i&&i[0],s=i&&i[1];o=c(o,a),r=c(r,s),o=this.containDrag("x",o,a),r=this.containDrag("y",r,s),o="y"==this.options.axis?0:o,r="x"==this.options.axis?0:r,this.position.x=this.startPosition.x+o,this.position.y=this.startPosition.y+r,this.dragPoint.x=o,this.dragPoint.y=r,this.dispatchEvent("dragMove",e,[t,n])}},s.containDrag=function(e,t,n){if(!this.options.containment)return t;var o="x"==e?"width":"height",r=c(-this.relativeStartPosition[e],n,"ceil"),i=this.containSize[o];return i=c(i,n,"floor"),Math.max(r,Math.min(i,t))},s.onPointerUp=function(e,t){this.element.classList.remove("is-pointer-down"),this.dispatchJQueryEvent("pointerUp",e,[t])},s.dragEnd=function(e,t){this.isEnabled&&(this.element.style.transform="",this.setLeftTop(),this.element.classList.remove("is-dragging"),this.dispatchEvent("dragEnd",e,[t]))},s.animate=function(){if(this.isDragging){this.positionDrag();var e=this;requestAnimationFrame((function(){e.animate()}))}},s.setLeftTop=function(){this.element.style.left=this.position.x+"px",this.element.style.top=this.position.y+"px"},s.positionDrag=function(){this.element.style.transform="translate3d( "+this.dragPoint.x+"px, "+this.dragPoint.y+"px, 0)"},s.staticClick=function(e,t){this.dispatchEvent("staticClick",e,[t])},s.setPosition=function(e,t){this.position.x=e,this.position.y=t,this.setLeftTop()},s.enable=function(){this.isEnabled=!0},s.disable=function(){this.isEnabled=!1,this.isDragging&&this.dragEnd()},s.destroy=function(){this.disable(),this.element.style.transform="",this.element.style.left="",this.element.style.top="",this.element.style.position="",this.unbindHandles(),this.$element&&this.$element.removeData("draggabilly")},s._init=r,i&&i.bridget&&i.bridget("draggabilly",a),a}(i,e,t)}.apply(t,o),void 0===r||(e.exports=r)}(window)},function(e,t,n){"use strict";var o,r,i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};window,void 0===(r="function"===typeof(o=function(){function e(e){var t=parseFloat(e);return-1==e.indexOf("%")&&!isNaN(t)&&t}function t(){}var n="undefined"==typeof console?t:function(e){console.error(e)},o=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],r=o.length;function a(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<r;t++)e[o[t]]=0;return e}function s(e){var t=getComputedStyle(e);return t||n("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),t}var l,c=!1;function d(){if(!c){c=!0;var t=document.createElement("div");t.style.width="200px",t.style.padding="1px 2px 3px 4px",t.style.borderStyle="solid",t.style.borderWidth="1px 2px 3px 4px",t.style.boxSizing="border-box";var n=document.body||document.documentElement;n.appendChild(t);var o=s(t);l=200==Math.round(e(o.width)),u.isBoxSizeOuter=l,n.removeChild(t)}}function u(t){if(d(),"string"==typeof t&&(t=document.querySelector(t)),t&&"object"==("undefined"===typeof t?"undefined":i(t))&&t.nodeType){var n=s(t);if("none"==n.display)return a();var c={};c.width=t.offsetWidth,c.height=t.offsetHeight;for(var u=c.isBorderBox="border-box"==n.boxSizing,p=0;p<r;p++){var f=o[p],h=n[f],g=parseFloat(h);c[f]=isNaN(g)?0:g}var m=c.paddingLeft+c.paddingRight,y=c.paddingTop+c.paddingBottom,v=c.marginLeft+c.marginRight,x=c.marginTop+c.marginBottom,b=c.borderLeftWidth+c.borderRightWidth,_=c.borderTopWidth+c.borderBottomWidth,k=u&&l,w=e(n.width);!1!==w&&(c.width=w+(k?0:m+b));var C=e(n.height);return!1!==C&&(c.height=C+(k?0:y+_)),c.innerWidth=c.width-(m+b),c.innerHeight=c.height-(y+_),c.outerWidth=c.width+v,c.outerHeight=c.height+x,c}}return u})?o.call(t,n,t,e):o)||(e.exports=r)},function(e,t,n){"use strict";var o,r;"function"===typeof Symbol&&Symbol.iterator,function(i,a){o=[n(99)],r=function(e){return function(e,t){function n(){}var o=n.prototype=Object.create(t.prototype);o.bindHandles=function(){this._bindHandles(!0)},o.unbindHandles=function(){this._bindHandles(!1)},o._bindHandles=function(t){for(var n=(t=void 0===t||t)?"addEventListener":"removeEventListener",o=t?this._touchActionValue:"",r=0;r<this.handles.length;r++){var i=this.handles[r];this._bindStartEvent(i,t),i[n]("click",this),e.PointerEvent&&(i.style.touchAction=o)}},o._touchActionValue="none",o.pointerDown=function(e,t){this.okayPointerDown(e)&&(this.pointerDownPointer={pageX:t.pageX,pageY:t.pageY},e.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(e),this.emitEvent("pointerDown",[e,t]))};var r={TEXTAREA:!0,INPUT:!0,SELECT:!0,OPTION:!0},i={radio:!0,checkbox:!0,button:!0,submit:!0,image:!0,file:!0};return o.okayPointerDown=function(e){var t=r[e.target.nodeName],n=i[e.target.type],o=!t||n;return o||this._pointerReset(),o},o.pointerDownBlur=function(){var e=document.activeElement;e&&e.blur&&e!=document.body&&e.blur()},o.pointerMove=function(e,t){var n=this._dragPointerMove(e,t);this.emitEvent("pointerMove",[e,t,n]),this._dragMove(e,t,n)},o._dragPointerMove=function(e,t){var n={x:t.pageX-this.pointerDownPointer.pageX,y:t.pageY-this.pointerDownPointer.pageY};return!this.isDragging&&this.hasDragStarted(n)&&this._dragStart(e,t),n},o.hasDragStarted=function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3},o.pointerUp=function(e,t){this.emitEvent("pointerUp",[e,t]),this._dragPointerUp(e,t)},o._dragPointerUp=function(e,t){this.isDragging?this._dragEnd(e,t):this._staticClick(e,t)},o._dragStart=function(e,t){this.isDragging=!0,this.isPreventingClicks=!0,this.dragStart(e,t)},o.dragStart=function(e,t){this.emitEvent("dragStart",[e,t])},o._dragMove=function(e,t,n){this.isDragging&&this.dragMove(e,t,n)},o.dragMove=function(e,t,n){e.preventDefault(),this.emitEvent("dragMove",[e,t,n])},o._dragEnd=function(e,t){this.isDragging=!1,setTimeout(function(){delete this.isPreventingClicks}.bind(this)),this.dragEnd(e,t)},o.dragEnd=function(e,t){this.emitEvent("dragEnd",[e,t])},o.onclick=function(e){this.isPreventingClicks&&e.preventDefault()},o._staticClick=function(e,t){this.isIgnoringMouseUp&&"mouseup"==e.type||(this.staticClick(e,t),"mouseup"!=e.type&&(this.isIgnoringMouseUp=!0,setTimeout(function(){delete this.isIgnoringMouseUp}.bind(this),400)))},o.staticClick=function(e,t){this.emitEvent("staticClick",[e,t])},n.getPointerPoint=t.getPointerPoint,n}(i,e)}.apply(t,o),void 0===r||(e.exports=r)}(window)},function(e,t,n){"use strict";var o,r;"function"===typeof Symbol&&Symbol.iterator,function(i,a){o=[n(100)],r=function(e){return function(e,t){function n(){}function o(){}var r=o.prototype=Object.create(t.prototype);r.bindStartEvent=function(e){this._bindStartEvent(e,!0)},r.unbindStartEvent=function(e){this._bindStartEvent(e,!1)},r._bindStartEvent=function(t,n){var o=(n=void 0===n||n)?"addEventListener":"removeEventListener",r="mousedown";"ontouchstart"in e?r="touchstart":e.PointerEvent&&(r="pointerdown"),t[o](r,this)},r.handleEvent=function(e){var t="on"+e.type;this[t]&&this[t](e)},r.getTouch=function(e){for(var t=0;t<e.length;t++){var n=e[t];if(n.identifier==this.pointerIdentifier)return n}},r.onmousedown=function(e){var t=e.button;t&&0!==t&&1!==t||this._pointerDown(e,e)},r.ontouchstart=function(e){this._pointerDown(e,e.changedTouches[0])},r.onpointerdown=function(e){this._pointerDown(e,e)},r._pointerDown=function(e,t){e.button||this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=void 0!==t.pointerId?t.pointerId:t.identifier,this.pointerDown(e,t))},r.pointerDown=function(e,t){this._bindPostStartEvents(e),this.emitEvent("pointerDown",[e,t])};var i={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]};return r._bindPostStartEvents=function(t){if(t){var n=i[t.type];n.forEach((function(t){e.addEventListener(t,this)}),this),this._boundPointerEvents=n}},r._unbindPostStartEvents=function(){this._boundPointerEvents&&(this._boundPointerEvents.forEach((function(t){e.removeEventListener(t,this)}),this),delete this._boundPointerEvents)},r.onmousemove=function(e){this._pointerMove(e,e)},r.onpointermove=function(e){e.pointerId==this.pointerIdentifier&&this._pointerMove(e,e)},r.ontouchmove=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerMove(e,t)},r._pointerMove=function(e,t){this.pointerMove(e,t)},r.pointerMove=function(e,t){this.emitEvent("pointerMove",[e,t])},r.onmouseup=function(e){this._pointerUp(e,e)},r.onpointerup=function(e){e.pointerId==this.pointerIdentifier&&this._pointerUp(e,e)},r.ontouchend=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerUp(e,t)},r._pointerUp=function(e,t){this._pointerDone(),this.pointerUp(e,t)},r.pointerUp=function(e,t){this.emitEvent("pointerUp",[e,t])},r._pointerDone=function(){this._pointerReset(),this._unbindPostStartEvents(),this.pointerDone()},r._pointerReset=function(){this.isPointerDown=!1,delete this.pointerIdentifier},r.pointerDone=n,r.onpointercancel=function(e){e.pointerId==this.pointerIdentifier&&this._pointerCancel(e,e)},r.ontouchcancel=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerCancel(e,t)},r._pointerCancel=function(e,t){this._pointerDone(),this.pointerCancel(e,t)},r.pointerCancel=function(e,t){this.emitEvent("pointerCancel",[e,t])},o.getPointerPoint=function(e){return{x:e.pageX,y:e.pageY}},o}(i,e)}.apply(t,o),void 0===r||(e.exports=r)}(window)},function(e,t,n){"use strict";var o,r;"function"===typeof Symbol&&Symbol.iterator,"undefined"!=typeof window&&window,void 0===(r="function"===typeof(o=function(){function e(){}var t=e.prototype;return t.on=function(e,t){if(e&&t){var n=this._events=this._events||{},o=n[e]=n[e]||[];return-1==o.indexOf(t)&&o.push(t),this}},t.once=function(e,t){if(e&&t){this.on(e,t);var n=this._onceEvents=this._onceEvents||{};return(n[e]=n[e]||{})[t]=!0,this}},t.off=function(e,t){var n=this._events&&this._events[e];if(n&&n.length){var o=n.indexOf(t);return-1!=o&&n.splice(o,1),this}},t.emitEvent=function(e,t){var n=this._events&&this._events[e];if(n&&n.length){n=n.slice(0),t=t||[];for(var o=this._onceEvents&&this._onceEvents[e],r=0;r<n.length;r++){var i=n[r];o&&o[i]&&(this.off(e,i),delete o[i]),i.apply(this,t)}return this}},t.allOff=function(){delete this._events,delete this._onceEvents},e})?o.call(t,n,t,e):o)||(e.exports=r)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"pip",method:function(){var e=this,t=this;function n(){t.video!==document.pictureInPictureElement?t.video.requestPictureInPicture():document.exitPictureInPicture()}t.on("pipBtnClick",n);var r=function(n){var r=t.video.webkitPresentationMode;e.pMode=r,r===o.PresentationMode.PIP?t.emit("requestPictureInPicture",n.pictureInPictureWindow):r===o.PresentationMode.INLINE&&t.emit("exitPictureInPicture")};t.video.addEventListener("enterpictureinpicture",(function(e){(0,o.addClass)(t.root,"xgplayer-pip-active"),t.emit("requestPictureInPicture",e)})),t.video.addEventListener("leavepictureinpicture",(function(){(0,o.removeClass)(t.root,"xgplayer-pip-active"),t.emit("exitPictureInPicture")})),(0,o.checkWebkitSetPresentationMode)(t.video)&&t.video.addEventListener("webkitpresentationmodechanged",r),t.once("destroy",(function e(){t.off("pipBtnClick",n),t.off("destroy",e),(0,o.checkWebkitSetPresentationMode)(t.video)&&t.video.removeEventListener("webkitpresentationmodechanged",r)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"playNext",method:function(){var e=this,t=e.config.playNext;function n(){e.currentVideoIndex+1<t.urlList.length&&(e.currentVideoIndex++,e.video.autoplay=!0,e.src=t.urlList[e.currentVideoIndex],e.emit("playerNext",e.currentVideoIndex+1),e.currentVideoIndex+1===t.urlList.length&&e.emit("urlListEnd"))}e.currentVideoIndex=-1,e.on("playNextBtnClick",n),e.once("destroy",(function t(){e.off("playNextBtnClick",n),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"reload",method:function(){var e=this;function t(){(0,o.removeClass)(e.root,"xgplayer-is-error"),e.src=e.config.url}e.config.reload&&(e.on("reloadBtnClick",t),e.once("destroy",(function n(){e.off("reloadBtnClick",t),e.off("destroy",n)})))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"rotate",method:function(){var e=this,t=e.config.rotate;function n(){e.rotate(t.clockwise,t.innerRotate)}t&&(e.on("rotateBtnClick",n),e.once("destroy",(function t(){e.off("rotateBtnClick",n),e.off("destroy",t)})),e.updateRotateDeg=function(){var e=this;e.rotateDeg||(e.rotateDeg=0);var t=e.root.offsetWidth,n=e.root.offsetHeight,o=e.video.videoWidth,r=e.video.videoHeight;!e.config.rotate.innerRotate&&e.config.rotate.controlsFix&&(e.root.style.width=n+"px",e.root.style.height=t+"px");var i=void 0;.25===e.rotateDeg||.75===e.rotateDeg?(i=e.config.rotate.innerRotate?o/r>n/t?n/(r/o>n/t?n*o/r:t):t/(r/o>n/t?n:t*r/o):t>=n?t/n:n/t,i=Number(i.toFixed(5))):i=1,e.config.rotate.innerRotate||e.config.rotate.controlsFix?(e.video.style.transformOrigin="center center",e.video.style.transform="rotate("+e.rotateDeg+"turn) scale("+i+")",e.video.style.webKitTransform="rotate("+e.rotateDeg+"turn) scale("+i+")"):(e.root.style.transformOrigin="center center",e.root.style.transform="rotate("+e.rotateDeg+"turn) scale(1)",e.root.style.webKitTransform="rotate("+e.rotateDeg+"turn) scale(1)")},e.rotate=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this;n.rotateDeg||(n.rotateDeg=0);var o=e?1:-1;n.rotateDeg=(n.rotateDeg+1+.25*o*t)%1,this.updateRotateDeg(),n.emit("rotate",360*n.rotateDeg)})}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={name:"screenShot",method:function(){var e=this,t=e.config.screenShot,n=null;if(t){e.video.setAttribute("crossOrigin","anonymous");var o=.92;(t.quality||0===t.quality)&&(o=t.quality);var r=void 0===t.type?"image/png":t.type,i=void 0===t.format?".png":t.format,a=document.createElement("canvas"),s=a.getContext("2d"),l=new Image;a.width=this.config.width||600,a.height=this.config.height||337.5,e.screenShot=function(){var c=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];c=void 0===t.saveImg?c:t.saveImg,a.width=e.video.videoWidth||600,a.height=e.video.videoHeight||337.5,n=t.callBack,l.onload=function(){s.drawImage(e.video,0,0,a.width,a.height),l.src=a.toDataURL(r,o).replace(r,"image/octet-stream");var d=l.src.replace(/^data:image\/[^;]+/,"data:application/octet-stream"),u=t.fileName||e.lang.SCREENSHOT;e.emit("screenShot",d),c&&n?n(d,u,i):c&&function(e,t){var n=document.createElement("a");n.href=e,n.download=t;var o=document.createEvent("MouseEvents");o.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),n.dispatchEvent(o)}(d,u+i)}()},e.on("screenShotBtnClick",e.screenShot),e.once("destroy",(function t(){e.off("screenShotBtnClick",e.screenShot),e.off("destroy",t)}))}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(4),i=(o=r)&&o.__esModule?o:{default:o};t.default={name:"stallCheck",method:function(){var e=this;if(e.config.enableStallCheck){var t=0,n=void 0,o=void 0;e.once("complete",(function(){setInterval((function(){e.currentTime-(t||0)>.1||e.paused?1!==n&&2!==n||(n=0,clearTimeout(o),o=null):n||(n=1,o=setTimeout((function(){1===n&&(n=2,e.emit("error",new i.default("STALL"))),o=null}),2e4)),t=e.currentTime}),1e3)}))}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(5),a=(o=i)&&o.__esModule?o:{default:o};t.default={name:"volume",method:function(){var e=this,t=e.root,n=void 0,o=void 0,i=void 0,s=void 0;function l(){e.controls&&(e.volume=e.config.volume,(n=e.controls.querySelector(".xgplayer-volume"))&&(o=n.querySelector(".xgplayer-slider"),i=n.querySelector(".xgplayer-bar"),s=n.querySelector(".xgplayer-drag"),"mobile"===a.default.device&&(0===e.volume&&(e.video.muted=!0),h())))}function c(t){if(o){e.video.muted=!1,o.focus(),(0,r.event)(t);var n=i.getBoundingClientRect(),a=(t.clientX,t.clientY),l=s.getBoundingClientRect().height,c=!1,d=function(t){t.preventDefault(),t.stopPropagation(),(0,r.event)(t),c=!0;var o=l-t.clientY+a,i=o/n.height;s.style.height=o+"px",e.volume=Math.max(Math.min(i,1),0)},u=function t(i){if(i.preventDefault(),i.stopPropagation(),(0,r.event)(i),window.removeEventListener("mousemove",d),window.removeEventListener("touchmove",d),window.removeEventListener("mouseup",t),window.removeEventListener("touchend",t),!c){var a=n.height-(i.clientY-n.top),l=a/n.height;s.style.height=a+"px",l<=0&&(e.volume>0?s.volume=e.video.volume:l=s.volume),e.volume=Math.max(Math.min(l,1),0)}o.volume=e.volume,c=!1};return window.addEventListener("mousemove",d),window.addEventListener("touchmove",d),window.addEventListener("mouseup",u),window.addEventListener("touchend",u),!1}}function d(){if("mobile"===a.default.device)e.video.muted?(e.video.muted=!1,e.emit("unmute"),e.volume=1):(e.video.muted=!0,e.emit("mute"),e.volume=0);else{if(!o)return;e.video.muted=!1,e.volume<.1?(o.volume<.1?e.volume=.6:e.volume=o.volume,e.emit("unmute")):(e.volume=0,e.emit("mute"))}}function u(){(0,r.addClass)(t,"xgplayer-volume-active"),n&&n.focus()}function p(){(0,r.removeClass)(t,"xgplayer-volume-active")}e.once("canplay",l),e.on("volumeBarClick",c),e.on("volumeIconClick",d),e.on("volumeIconEnter",u),e.on("volumeIconLeave",p);var f=null;function h(){f&&clearTimeout(f),f=setTimeout((function(){if("mobile"===a.default.device)(0,r.removeClass)(t,"xgplayer-volume-muted"),(0,r.removeClass)(t,"xgplayer-volume-large"),e.video.muted||e.video.defaultMuted?(e.video.muted||(e.video.muted=!0),e.video.defaultMuted=!1,(0,r.addClass)(t,"xgplayer-volume-muted")):(0,r.addClass)(t,"xgplayer-volume-large");else{if((0,r.removeClass)(t,"xgplayer-volume-muted"),(0,r.removeClass)(t,"xgplayer-volume-small"),(0,r.removeClass)(t,"xgplayer-volume-large"),0===e.volume||e.muted?(0,r.addClass)(t,"xgplayer-volume-muted"):e.volume<.5?(0,r.addClass)(t,"xgplayer-volume-small"):(0,r.addClass)(t,"xgplayer-volume-large"),!i)return;var n=i.getBoundingClientRect().height||76;s.style.height=e.volume*n+"px"}}),50)}e.on("volumechange",h),e.once("destroy",(function t(){e.off("canplay",l),e.off("volumeBarClick",c),e.off("volumeIconClick",d),e.off("volumeIconEnter",u),e.off("volumeIconLeave",p),e.off("volumechange",h),e.off("destroy",t),f&&(clearTimeout(f),f=null)}))}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=n(109),a=(o=i)&&o.__esModule?o:{default:o},s={follow:!0,mode:"stroke",followBottom:50,fitVideo:!0,offsetBottom:2,baseSizeX:49,baseSizeY:28,minSize:16,minMobileSize:13,line:"double",fontColor:"#fff"},l=function(){function e(t,n,o){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var i=this.create(n,o,t.textTrackShowDefault);i.attachPlayer(t),this.subtitle=i,this.player=t,this.positionData={vBottom:0,marginBottom:0},this.isActive=!1,this.followBottom=o.followBottom,["onSubtitleResize","onFocus","onBlur"].map((function(e){r[e]=r[e].bind(r)})),t.controls&&o.follow&&(this.subtitle.on("resize",this.onSubtitleResize),t.on("focus",this.onFocus),t.on("blur",this.onBlur))}return r(e,[{key:"create",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n={subTitles:e,defaultOpen:!(arguments.length>2&&void 0!==arguments[2])||arguments[2]};return Object.keys(t).map((function(e){n[e]=t[e]})),new a.default(n)}},{key:"switch",value:function(e){return this.subtitle.switch(e)}},{key:"switchOff",value:function(){return this.subtitle.switchOff()}},{key:"setSubTitles",value:function(e,t,n){return this.subtitle.setSubTitles(e,t,n)}},{key:"onFocus",value:function(){var e=this.positionData,t=e.marginBottom,n=e.vBottom;if(!this.isActive&&t){this.isActive=!0;var o=t+n;this.followBottom>o&&(o=this.followBottom),this.subtitle&&(this.subtitle.root.style.bottom=o+"px")}}},{key:"onBlur",value:function(){this.isActive=!1;var e=this.positionData.vBottom+this.positionData.marginBottom;this.subtitle&&(this.subtitle.root.style.bottom=e+"px")}},{key:"onSubtitleResize",value:function(e){this.positionData.vBottom=e.vBottom,this.positionData.marginBottom=e.marginBottom}},{key:"destroy",value:function(){this.subtitle.off("resize",this.onSubtitleResize),this.player.off("focus",this.onFocus),this.player.off("blur",this.onBlur),this.subtitle.destroy(),this.subtitle=null}}]),e}();t.default={name:"textTrack",method:function(){var e=this,t=this,n=t.config.textTrack;if(n){var o=t.config.textTrackStyle||{};Object.keys(s).map((function(e){void 0===o[e]&&(o[e]=s[e])})),t.textTrackShowDefault=!1,t.config.textTrack.map((function(e,n){e.id||e.language||(e.id=n),!e.url&&(e.url=e.src),!e.language&&(e.language=e.srclang),void 0===e.isDefault&&(e.isDefault=e.default),!t.textTrackShowDefault&&(t.textTrackShowDefault=e.isDefault||e.default)})),this.subTitles=new l(t,t.config.textTrack,o),t.setSubTitles=function(n){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=!1;n.map((function(e,t){e.id||e.language||(e.id=t),!e.url&&(e.url=e.src),!e.language&&(e.language=e.srclang),void 0===e.isDefault&&(e.isDefault=e.default),e.isDefault&&(r=!0)})),t.textTrackShowDefault=r,e.subTitles.setSubTitles(n,r,o),t.emit("subtitle_change",{off:!1,isListUpdate:!0,list:n})},t.switchSubTitle=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{id:"",language:""};e.subTitles.switch(n).then((function(e){0===e.code&&(n.off=!1,n.isListUpdate=!1,n.list=[],t.emit("subtitle_change",n))}))},t.switchOffSubtile=function(){e.subTitles.switchOff(),t.emit("subtitle_change",{off:!0,isListUpdate:!1,list:[]})},t.once("destroy",(function(){this.subTitles.destroy(),this.subTitles=null}))}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(11),r=d(n(110)),i=n(111),a=n(114),s=n(115),l=n(79),c=d(n(80));function d(e){return e&&e.__esModule?e:{default:e}}function u(e){var t=[];return e&&"String"===(0,i.typeOf)(e)?t.push({url:e,index:0,start:-1,end:-1}):"Array"===(0,i.typeOf)(e)&&e.forEach((function(e,n){t.push({url:e.url||e.src,index:n,start:e.start||-1,end:e.end||-1})})),t}var p=!1,f=function(e){(0,o.inherits)(n,e);var t=(0,o.createSuper)(n);function n(e){var r;return(0,o.classCallCheck)(this,n),r=t.call(this),(0,o.defineProperty)((0,o.assertThisInitialized)(r),"_onTimeupdate",(function(){if(r._isOpen){var e=r.player.video,t=e.videoWidth,n=e.videoHeight;!r._videoMeta.scale&&t&&n&&r._onResize(r.player.root);var o=r._getPlayerCurrentTime();Math.round(Math.abs(1e3*o-r._ctime))<200||(r._ctime=1e3*o,r.currentText&&r.currentText.list&&("live"===r.config.updateMode?r._liveUpdate(o):r._update(o)))}})),(0,o.defineProperty)((0,o.assertThisInitialized)(r),"getItemsByIndex",void 0),(0,o.defineProperty)((0,o.assertThisInitialized)(r),"_onResize",(function(e){var t=(0,o.assertThisInitialized)(r),n=t._videoMeta;if(t.config.domRender){if(e&&e instanceof window.Element||(e=r.player.root),r._iId&&(clearTimeout(r._iId),r._iId=null),!n.scale){if(!r.player.video)return;var i=r.player.video,a=i.videoWidth,s=i.videoHeight;if(!a||!s)return;n.videoWidth=a,n.videoHeight=s,n.scale=parseInt(s/a*100,10)}r.__startResize(e)}})),(0,o.defineProperty)((0,o.assertThisInitialized)(r),"_onCoreEvents",(function(e){try{switch(e.eventName){case"core.subtitlesegments":r._onSubtitleSegment(e.list||[]);break;case"core.subtitleplaylist":r._onSubtitlePlaylist(e.list||[]);break;case"core.seipayloadtime":r._onCoreSeiintime(e)}}catch(t){console.error(t)}})),(0,o.defineProperty)((0,o.assertThisInitialized)(r),"destroy",(function(){r.detachPlayer(),r.removeAllListeners(),r.player=null,r.textTrack=null})),p=(0,i.isMobile)(),r.currentText=null,r.currentExtText=null,r.textTrack=[],r._cid=-1,r._gid=-1,r._cids=[],r._iId=null,r._iC=0,r.player=null,r.root=null,r.config={line:"double",bottom:0,mode:"stroke",defaultOpen:!1,baseSizeX:49,baseSizeY:28,minSize:16,minMobileSize:13,fitVideo:!0,offsetBottom:2,fontColor:"#fff",domRender:!0,updateMode:"vod",debugger:!1},r._ctime=0,r._loadingTrack={},Object.keys(r.config).map((function(t){void 0!==e[t]&&null!==e[t]&&(r.config[t]=e[t])})),r._isOpen=r.config.defaultOpen||!1,r._videoMeta={scale:0,videoHeight:0,videoWidth:0,lwidth:0,lheight:0,vWidth:0,vHeight:0,vBottom:0,vLeft:0,marginBottom:0},e.subTitles&&"Array"===(0,i.typeOf)(e.subTitles)?(e.player&&r.attachPlayer(e.player),r.seiTime=0,r.lastSeiTime=0,r._curTexts=[],r.setSubTitles(e.subTitles,r.config.defaultOpen),r):(0,o.possibleConstructorReturn)(r)}return(0,o.createClass)(n,[{key:"version",get:function(){return"1.1.1"}},{key:"setSubTitles",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this._isOpen||n;o&&this.innerRoot&&this.switchOff(),this.currentText=null,this.textTrack=[],t.forEach((function(t){var n={};Object.keys(t).map((function(e){n[e]=t[e]})),n.url=u(n.url),n.isDefault&&(e.currentText=n),e.textTrack.push(n)})),this._log("setSubTitles",r),r&&this.switch().catch((function(t){e._log("[switch]",t)})),this.currentText&&this._loadTrack(this.currentText).then((function(t){e.addStyles(t)})),this.emit("reset",{list:this.textTrack,isOpen:r})}},{key:"updateSubTitle",value:function(e){for(var t=this,n=-1,o=0;o<this.textTrack.length;o++)if((0,i.checkSubtitle)(e,this.textTrack[o])){n=o;break}if(this._log("updateSubTitle",n,e),n>-1){var r=(0,i.checkSubtitle)(this.currentText,this.textTrack[n]);if(this._log("updateSubTitle","_isCurrent",r,"this.isOpen",this.isOpen,this.currentText),!r)return;var a=u(e.url);this.isOpen?(a.forEach((function(e){t.textTrack[n].url.push(e)})),this._log("updateSubTitle _loadTrackUrls",this.textTrack[n]),this._loadTrackUrls(this.currentText,2)):this.textTrack[n].url=a}}},{key:"addStyles",value:function(e){var t=e.styles,n=e.format;t&&"vtt"===n&&(t.map((function(e){e.key||(e.key="xg-text-track-span")})),(0,i.addCSS)(t,"xg-text-track"))}},{key:"attachPlayer",value:function(e){var t=this;if(this._log("attachPlayer"),e){this.player&&this.detachPlayer();var n=this.config,o=n.fontColor,r=n.mode,s=n.fitVideo,l=n.domRender;this.player=e,l&&(this.root=document.createElement("xg-text-track"),this.root.className="xg-text-track",!this._isOpen&&(0,i.addClass)(this.root,"text-track-hide"),!s&&(0,i.addClass)(this.root,"text-track-no-fitvideo"),r&&(0,i.addClass)(this.root,"text-track-".concat(r)),this.innerRoot=document.createElement("xg-text-track-inner"),this.root.appendChild(this.innerRoot),o&&(this.root.style.color=o),this.currentText&&["language","id","label"].map((function(e){t.root.setAttribute("data-".concat(e),t.currentText[e]||"")})),this.player.root.appendChild(this.root),(0,a.addObserver)(e.root,this._onResize)),this.player.on("destroy",this.destroy),this.player.on("timeupdate",this._onTimeupdate),this.player.on("core_event",this._onCoreEvents),this._isOpen&&this.switch().catch((function(e){t._log("[switch]",e)}))}}},{key:"detachPlayer",value:function(){var e=this.player,t=this.config;e&&(e.off("destroy",this.destroy),e.off("timeupdate",this._onTimeupdate),e.on("core_event",this._onCoreEvents),t.domRender&&(e.root&&((0,a.unObserver)(e.root,this._onResize),e.root.removeChild(this.root)),this.innerRoot=null,this.root=null),this.player=null)}},{key:"switch",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{id:"",language:""};return this._log("switch",t),this._loadingTrack=t,new Promise((function(n,o){if(t.id||t.language)if(e.currentText&&(0,i.checkSubtitle)(t,e.currentText))e._loadingTrack={},e._updateCurrent(e.currentText),e.switchOn(),n((0,l._ERROR)(0));else{var r=null;e.__removeByTime(e._curTexts,0);for(var a=0;a<e.textTrack.length;a++)if((0,i.checkSubtitle)(t,e.textTrack[a])){r=e.textTrack[a];break}if(e._log("nextSubtitle",r),r)e._emitPlayerSwitch(e.currentText,r),r.list?(e._loadingTrack={},e._updateCurrent(r),e.switchOn(),n((0,l._ERROR)(0))):(e._log("this._loadTrack",r),e._updateCurrent(r),e._loadTrack(r).then((function(t){if(e.addStyles(t),e._loadingTrack.id===r.id||e._loadingTrack.language===t.language)e._loadingTrack={},e._updateCurrent(t),e.switchOn(),n((0,l._ERROR)(0));else{var i=(0,l._ERROR)(6,{message:"check _loadingTrack fail id: ".concat(e._loadingTrack.id,"  nextSubtitle:").concat(t.id)});o(i)}})).catch((function(e){o(e)})));else{var s=(0,l._ERROR)(4,new Error("The is no subtitle with id:[{".concat(t.id,"}] or language:[").concat(t.language,"]")));o(s)}}else{if(e.currentText){e._loadingTrack={},e._updateCurrent(e.currentText),e.switchOn();var c=(0,l._ERROR)(0,{message:"switch default subtitle success"});return void n(c)}var d=(0,l._ERROR)(5,{message:"no default subtitle"});o(d)}}))}},{key:"switchExt",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{id:"",language:""};return new Promise((function(n,o){if(t.id||t.language){for(var r=null,a=0;a<e.textTrack.length;a++)if((0,i.checkSubtitle)(t,e.textTrack[a])){r=e.textTrack[a];break}r&&!(0,i.checkSubtitle)(r,e.currentText)&&e._loadTrack(r).then((function(t){e.currentExtText=t,n((0,l._ERROR)(0))}))}else e.currentExtText=null,n((0,l._ERROR)(0))}))}},{key:"switchOn",value:function(){this._log("switchOn"),this._isOpen=!0,this.show(),this.emit(s.EVENTS.CHANGE,this.currentText)}},{key:"switchOff",value:function(){this._isOpen=!1,this.hide(),this.emit(s.EVENTS.OFF)}},{key:"isOpen",get:function(){return this._isOpen}},{key:"_log",value:function(){if(this.config.debugger){for(var e,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];(e=console).log.apply(e,["[xgSubtitle]"].concat(n))}}},{key:"_loadTrack",value:function(e){var t=this;this._log("_loadTrack",e.language,e);var n=new c.default,o="",r="";if(e.json?(o="json",r=e.json):e.stringContent&&!e.url&&(o="string",r=e.stringContent),r)return(0,i.parse)(r,o).then((function(t){e.format=t.format,e.styles=t.styles,e.list=t.list,n.resolve(e)})).catch((function(e){n.reject(e)})),n;var a=e.url;if(0===a.length)return n.resolve(e),n;var s=a.splice(0,1);return(0,i.loadSubTitle)(s[0]).then((function(o){e.format=o.format,e.styles=o.styles,e.list||(e.list=[]),t._pushList(e.list,o.list),a.length>1&&t._loadTrackUrls(e,2),n.resolve(e)})).catch((function(e){n.reject(e)})),n}},{key:"_emitPlayerSwitch",value:function(e,t){e&&"live"===this.config.updateMode&&(e.list=[],e.url=[]);var n=(0,o.objectSpread2)({lang:t.language},t);this._log("emit subtile_switch ",t,n),this.player&&this.player.emit("switch_subtitle",n)}},{key:"_loadTrackUrls",value:function(e,t,n){var r=this,a=e.url.length,s=a>t?e.url.splice(0,t):e.url.splice(0,a),l=s.length;this._log("_loadTrackUrls",e.language,a,s.length,l),s.forEach((function(t,a){var s=(0,o.objectSpread2)((0,o.objectSpread2)({},t),{},{index:a});(0,i.loadSubTitle)(s).then((function(t){e.format=t.format,e.styles=t.format,e.list||(e.list=[]),r._pushList(e.list,t.list),l--})).catch((function(e){l--})).finally((function(t){0===l&&(n&&n.resolve(e),r._loadTrackUrls(e,2))}))}))}},{key:"_freshUrl",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{url:""},n=-1;e.url.forEach((function(e,o){e.url===t.url&&(n=o)})),n>-1&&e.url.splice(n,1)}},{key:"_pushList",value:function(e,t){var n=t[0].start,o=t[t.length-1].end;if(0===e.length||n>=e[e.length-1].end)t.forEach((function(t){e.push(t)}));else{for(var r=-1,i=0;i<e.length;i++)if(e[i].start>o){r=i;break}r>-1&&t.forEach((function(t,n){e.splice(r+n,0,t)}))}return e}},{key:"_updateCurrent",value:function(e){var t=this;this.currentText=e,this.config.domRender&&this.root&&(["language","id","label"].map((function(e){t.root.setAttribute("data-".concat(e),t.currentText[e]||"")})),this.__remove(this._cids)),this._cids=[],this._gid=-1,this._cid=-1,this._curTexts=[];var n=this._getPlayerCurrentTime();n&&("live"===this.config.updateMode?this._liveUpdate(n):this._update(n))}},{key:"__loadAll",value:function(){var e=this;this.textTrack.forEach((function(t){e._loadTrack(t)}))}},{key:"getDelCid",value:function(e,t){for(var n=[],o=0;o<e.length;o++)t.includes(e[o])||n.push(e[o]);return n}},{key:"getNewCid",value:function(e,t){for(var n=[],o=0;o<t.length;o++)e.includes(t[o])||n.push(t[o]);return n}},{key:"_liveUpdate",value:function(e){var t=this;if(this.currentText&&this.currentText.list&&this.currentText.list.length){var n=[],o=(0,i.findIndexByTime)(e,this.currentText.list,this._gid);if(o>-1&&(n=(0,i.findCIndexByTime)(e,this.currentText.list[o].list,this._cid)),this.__removeByTime(this._curTexts,e),this._log("_liveUpdate",e,o,n,this.currentText.list[0].list[0].start,this.currentText.list[0].list[0].end),n.length>0){var r=(0,i.getItemsByIndex)(this.currentText.list,o,n),a=this._curTexts.length,s=a>0?this._curTexts[a-1].index:0;r.forEach((function(e,n){e.index=n+s,t._curTexts.push(e)})),this.__render(r)}this.emit("update",this._curTexts)}}},{key:"_update",value:function(e){var t=this;if(this.currentText&&this.currentText.list&&this.currentText.list.length){var n=(0,i.findIndexByTime)(e,this.currentText.list,this._gid),o=[];if(n>-1&&(o=(0,i.findCIndexByTime)(e,this.currentText.list[n].list,this._cid)),o.length<1)return this._cids.length>0&&this.config.domRender&&this.__remove(this._cids),void(this._cids=[]);if(this._cids!==o||n!==this._gid){this._gid=n,this._cid=o[0];var r=this.getDelCid(this._cids,o),a=this.getNewCid(this._cids,o);this._cids=o,this.config.domRender&&this.__remove(r);var s=[];a.map((function(e){var o=t.currentText.list[n].list[e];o.index=e,s.push(o)})),this.currentExtText&&a.map((function(e){var o=t.currentText.list[n].list[e];o.index=e,s.push(o)})),this.emit("update",s),this.__render(s,e)}}}},{key:"_getPlayerCurrentTime",value:function(){if(!this.player)return 0;var e=this.player.currentTime;return parseInt(1e3*e+1e3*this.seiTime-1e3*this.lastSeiTime,10)/1e3}},{key:"_onSubtitlePlaylist",value:function(e){this._log("_onSubtitlePlaylist",e);var t=e.map((function(e){return{label:e.name,language:e.lang,id:e.id,isDefault:e.default,url:[],mUrl:e.url}}));this.setSubTitles(t)}},{key:"_onSubtitleSegment",value:function(e){if(this._log("_onSubtitleSegment",e.length,e[0].lang,e[0].sn,e[e.length-1].sn,e[0].start,e[e.length-1].end),e&&0!==e.length){var t={language:e[0].lang,url:e.map((function(e){return{id:e.sn,url:e.url,duration:e.duration,start:e.start,end:e.end}}))};(0,i.checkSubtitle)(t,this.currentText)&&this.updateSubTitle(t)}}},{key:"_onCoreSeiintime",value:function(e){try{var t=e.time/1e3;this._log("_onCoreSeiintime sei",t,this.seiTime,this.lastSeiTime),this.seiTime=t,this.lastSeiTime=this.player?this.player.currentTime:0}catch(n){}}},{key:"resize",value:function(e,t){var n,o=this,r=this.config,i=r.baseSizeX,a=r.baseSizeY,l=r.minMobileSize,c=r.minSize,d=r.fitVideo,u=r.offsetBottom,f=this._videoMeta.scale;this._videoMeta.lwidth=e,this._videoMeta.lheight=t;var h=0;t/e*100>=f?(h=parseInt(f*e,10)/100,n=e):(h=t,n=parseInt(t/f*100,10)),this._videoMeta.vWidth=n,this._videoMeta.vHeight=h;var g=0,m=0;f>120?(g=a,m=parseInt(g*h/1080,10)):(g=i,m=parseInt(g*n/1920,10));var y=p?l:c,v={fontSize:m=m<y?y:m>g?g:m},x=parseInt((t-h)/2,10),b=parseInt((e-n)/2,10),_=parseInt(h*u,10)/100;this._videoMeta.vBottom=x,this._videoMeta.vLeft=b,this._videoMeta.marginBottom=_,d&&(v.bottom=x+_,v.left=v.right=b),Object.keys(v).map((function(e){o.root.style[e]="".concat(v[e],"px")})),this.emit(s.EVENTS.RESIZE,{vLeft:b,vBottom:x,marginBottom:_,vWidth:n,vHeight:h,fontSize:m,scale:f})}},{key:"__startResize",value:function(e){var t=this,n=e.getBoundingClientRect(),o=this._videoMeta,r=n.width,i=n.height;if(this._iId&&(clearTimeout(this._iId),this._iId=null),r>0&&i>0&&(r!==o.lwidth||i!==o.lheight))this._iC=0,this.resize(r,i);else{if(this._iC>=5)return void(this._iC=0);this._iC++,this._iId=setTimeout((function(){t.__startResize(e)}),50)}}},{key:"__removeByTime",value:function(e,t){for(var n=[],o=0;o<e.length;o++)(!t||e[o].end<t)&&n.push(o);0!==n.length&&(e.splice(n[0],n.length),this.config.domRender&&this.__remove(n))}},{key:"__remove",value:function(e){var t=this;if(e&&!(e.length<1)){for(var n=this.innerRoot.children,o=[],r=0;r<n.length;r++){var i=Number(n[r].getAttribute("data-index"));e.includes(i)&&o.push(n[r])}o.map((function(e){t.innerRoot.removeChild(e)}))}}},{key:"__render",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this._log("__render",t.length,this.config.domRender),t.length>0&&this.config.domRender&&t.map((function(t){var n="text-track-".concat(e.config.line);t.text.map((function(o,r){r>0&&(n+=" text-track-deputy");var a={"data-start":t.start,"data-end":t.end,"data-index":t.index};e.innerRoot.appendChild((0,i.createDom)("xg-text-track-span",o,a,n))}))}))}},{key:"show",value:function(){this.config.domRender&&(0,i.removeClass)(this.root,"text-track-hide")}},{key:"hide",value:function(){this.config.domRender&&((0,i.addClass)(this.root,"text-track-hide"),this.innerRoot.innerHTML="")}},{key:"emit",value:function(e,t){for(var r,i=arguments.length,a=new Array(i>2?i-2:0),s=2;s<i;s++)a[s-2]=arguments[s];(r=(0,o.get)((0,o.getPrototypeOf)(n.prototype),"emit",this)).call.apply(r,[this,e,t].concat(a))}},{key:"on",value:function(e,t){for(var r,i=arguments.length,a=new Array(i>2?i-2:0),s=2;s<i;s++)a[s-2]=arguments[s];(r=(0,o.get)((0,o.getPrototypeOf)(n.prototype),"on",this)).call.apply(r,[this,e,t].concat(a))}},{key:"once",value:function(e,t){for(var r,i=arguments.length,a=new Array(i>2?i-2:0),s=2;s<i;s++)a[s-2]=arguments[s];(r=(0,o.get)((0,o.getPrototypeOf)(n.prototype),"once",this)).call.apply(r,[this,e,t].concat(a))}},{key:"off",value:function(e,t){for(var r,i=arguments.length,a=new Array(i>2?i-2:0),s=2;s<i;s++)a[s-2]=arguments[s];(r=(0,o.get)((0,o.getPrototypeOf)(n.prototype),"off",this)).call.apply(r,[this,e,t].concat(a))}},{key:"offAll",value:function(){(0,o.get)((0,o.getPrototypeOf)(n.prototype),"removeAllListeners",this).call(this)}},{key:"marginBottom",get:function(){var e=this._videoMeta,t=e.bottom,n=e.marginBottom;return this.config.fitVideo?t+n:n}}]),n}(r.default);t.default=f},function(e,t,n){"use strict";var o=Object.prototype.hasOwnProperty,r="~";function i(){}function a(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function s(e,t,n,o,i){if("function"!==typeof n)throw new TypeError("The listener must be a function");var s=new a(n,o||e,i),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function l(e,t){0===--e._eventsCount?e._events=new i:delete e._events[t]}function c(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(r=!1)),c.prototype.eventNames=function(){var e,t,n=[];if(0===this._eventsCount)return n;for(t in e=this._events)o.call(e,t)&&n.push(r?t.slice(1):t);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(e)):n},c.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},c.prototype.emit=function(e,t,n,o,i,a){var s=r?r+e:e;if(!this._events[s])return!1;var l,c,d=this._events[s],u=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),u){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,n),!0;case 4:return d.fn.call(d.context,t,n,o),!0;case 5:return d.fn.call(d.context,t,n,o,i),!0;case 6:return d.fn.call(d.context,t,n,o,i,a),!0}for(c=1,l=new Array(u-1);c<u;c++)l[c-1]=arguments[c];d.fn.apply(d.context,l)}else{var p,f=d.length;for(c=0;c<f;c++)switch(d[c].once&&this.removeListener(e,d[c].fn,void 0,!0),u){case 1:d[c].fn.call(d[c].context);break;case 2:d[c].fn.call(d[c].context,t);break;case 3:d[c].fn.call(d[c].context,t,n);break;case 4:d[c].fn.call(d[c].context,t,n,o);break;default:if(!l)for(p=1,l=new Array(u-1);p<u;p++)l[p-1]=arguments[p];d[c].fn.apply(d[c].context,l)}}return!0},c.prototype.on=function(e,t,n){return s(this,e,t,n,!1)},c.prototype.once=function(e,t,n){return s(this,e,t,n,!0)},c.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return l(this,i),this;var a=this._events[i];if(a.fn)a.fn!==t||o&&!a.once||n&&a.context!==n||l(this,i);else{for(var s=0,c=[],d=a.length;s<d;s++)(a[s].fn!==t||o&&!a[s].once||n&&a[s].context!==n)&&c.push(a[s]);c.length?this._events[i]=1===c.length?c[0]:c:l(this,i)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&l(this,t)):(this._events=new i,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,e.exports=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.typeOf=t.removeClass=t.parse=t.loadSubTitle=t.isMobile=t.hasClass=t.getItemsByIndex=t.findIndexByTime=t.findCIndexByTime=t.createDom=t.checkSubtitle=t.addClass=t.addCSS=void 0;var o=n(11),r=l(n(112)),i=n(79),a=l(n(113)),s=l(n(80));function l(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!e)return!1;if(e.classList)return Array.prototype.some.call(e.classList,(function(e){return e===t}));var n=e.className&&"object"===(0,o.typeof)(e.className)?e.getAttribute("class"):e.className;return n&&!!n.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}function d(e,t,n){if(n||(n=new s.default),"json"===t){var o=r.default.parseJson(e);n.resolve({list:o,format:"json"})}else"string"===t&&r.default.parse(e,(function(e,t){if(t){var o=(0,i._ERROR)(2,t);n.reject(o,{format:e.format})}else if(e.format)n.resolve({styles:e.styles,list:e.list,format:e.format});else{var r=(0,i._ERROR)(3);n.reject(r)}}));return n}t.addCSS=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n="";e.map((function(e){n+=" ".concat(t," ").concat(e.key," {").concat(e.style,"}")}));var o=document.createElement("style"),r=document.head||document.getElementsByTagName("head")[0];if(o.type="text/css",o.id="ssss",o.styleSheet){var i=function(){try{o.styleSheet.cssText=n}catch(e){}};o.styleSheet.disabled?setTimeout(i,10):i()}else{var a=document.createTextNode(n);o.appendChild(a)}r.appendChild(o)},t.addClass=function(e,t){e&&(e.classList?t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)})):c(e,t)||(e.className&&"object"===(0,o.typeof)(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t))},t.checkSubtitle=function(e,t){return!!(e.id&&t.id&&e.id===t.id||e.language&&t.language&&e.language===t.language)},t.createDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=document.createElement(e);return r.className=o,r.innerHTML=t,Object.keys(n).forEach((function(t){var o=t,i=n[t];"video"===e||"audio"===e||"live-video"===e?i&&r.setAttribute(o,i):r.setAttribute(o,i)})),r},t.findCIndexByTime=function(e,t,n){var o=t.length;if(o<1)return[];var r=[];if((n=n<0?0:n>=o?o-1:n)<o)for(var i=t[n].end<=e?n:0;i<o&&(t[i].start<=e&&e<t[i].end&&r.push(i),!(e<t[i].start));i++);return r},t.findIndexByTime=function(e,t,n){var o=t.length;if(o<1)return-1;if(t[n=n<0?0:n>=o?o-1:n].start<=e&&e<t[n].end)return n;for(var r=t[n].end<=e?n+1:0;r<o;r++){if(t[r].start<=e&&e<t[r].end)return r;if(e>t[r].end&&r+1<o&&e<t[r+1].start)return-1;if(e>t[r].end&&r+1>=o)return-1}return-1},t.getItemsByIndex=function(e,t,n){if(0===e.length||t<0||t>e.length-1)return[];var o=e[t].list;if(0===n.length)return[];var r=o.splice(n[0],n.length);return t>0&&e.splice(0,t),0===o.length&&e.splice(0,1),r},t.hasClass=c,t.isMobile=function(){var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),n=/(?:SymbianOS)/.test(e)||t,o=/(?:Android)/.test(e),r=/(?:Firefox)/.test(e),i=/(?:iPad|PlayBook)/.test(e)||o&&!/(?:Mobile)/.test(e)||r&&/(?:Tablet)/.test(e);return/(?:iPhone)/.test(e)&&!i||o||n||i},t.loadSubTitle=function(e,t){return t||(t=new s.default),new a.default({url:e.url,type:"text"}).then((function(n){d(n.res.response,"string").then((function(n){t.resolve((0,o.objectSpread2)((0,o.objectSpread2)({},n),e))})).catch((function(e){t.reject(e)}))})).catch((function(n){var r=(0,i._ERROR)(1,(0,o.objectSpread2)({statusText:n.statusText,status:n.status,type:n.type,message:"http load error",url:e.src},e));t.reject(r)})),t},t.parse=d,t.removeClass=function(e,t){e&&(e.classList?t.split(/\s+/g).forEach((function(t){e.classList.remove(t)})):c(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===(0,o.typeof)(e.className)?e.setAttribute("class",e.getAttribute("class").replace(n," ")):e.className=e.className.replace(n," ")})))},t.typeOf=function(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(11),r=/^WEBVTT/,i=/^STYLE+$/,a=/^\:\:cue/,s=/^}+$/,l=/^\[Script Info\].*/,c=[/[0-9]{1,3}:[0-9]{2}:[0-9]{2}\.[0-9]{1,3}-->[0-9]{1,3}:[0-9]{2}:[0-9]{2}\.[0-9]{1,3}/,/[0-9]{1,2}:[0-9]{2}\.[0-9]{1,3}-->[0-9]{1,2}:[0-9]{2}\.[0-9]{1,3}/,/[0-9]{1,2}\.[0-9]{1,3}-->[0-9]{1,2}\.[0-9]{1,3}/],d=/^Format:\s/,u=/^Style:\s/,p=/^Dialogue:\s/;function f(e){var t=e.length;return 3===t?(60*(60*Number(e[0])+Number(e[1]))*1e3+1e3*Number(e[2]))/1e3:2===t?(60*Number(e[0])*1e3+1e3*Number(e[1]))/1e3:Number(e[0])}function h(e){return/^(\-|\+)?\d+(\.\d+)?$/.test(e)}function g(e,t){return e>=0&&e<t.length?t[e]:""}var m=function(){function e(){(0,o.classCallCheck)(this,e)}return(0,o.createClass)(e,null,[{key:"parseJson",value:function(e){for(var t=[],n=0,o=0;o<e.length;o++){if(n>=50&&(n=0),0===n){var r={start:e[o].start,list:[e[o]],end:e[o].end};t.push(r)}else t[t.length-1].list.push(e[o]),t[t.length-1].end=e[o].end;n++}return t}},{key:"parse",value:function(t,n){var o=e.checkFormat(t);o||n({format:o});try{var r=[];"ass"===o?r=e.parseASS(t):"vtt"===o&&(r=e.parseVTT(t)),n({format:o,list:r.list,styles:r.styles})}catch(i){console.error(i),n({format:o},i)}}},{key:"parseASSItem",value:function(){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split(","),n={},o="";try{var r=t.length-e.length;return o=(o=r>0?t.splice(e.length-1,r+1).join(",")+"":t[t.length-1]+"").replace(/\\n+/g,""),t[e.length-1]=o,e.map((function(e,o){"end"===e||"start"===e?n[e]=f(t[o].split(":")):"text"===e?n[e]=[t[o]]:"layer"===e?(n[e]=[t[o]],n.textTag=[t[o]]):n[e]="style"===e?[t[o]]:Number(t[o])?Number(t[o]):t[o]})),n}catch(i){return console.error(i),{}}}},{key:"parseASS",value:function(t){for(var n=t.split("\n"),o=[],r=0,i=0,a=[],s=[],l=null;r<n.length;){if(d.test(n[r]))s=(s=n[r].replace(d,"").replace(/\s+/g,"").split(",")).map((function(e){return e.toLocaleLowerCase()}));else if(u.test(n[r]))a.push(n[r].replace(u,"").replace(/\s+/g,""));else if(p.test(n[r])){var c=e.parseASSItem(n[r].replace(p,""),s);if(l&&c.start===l.start&&c.end===l.end)try{var f=l,h=f.text,g=f.textTag,m=f.style;h.push(c.text[0]),g.push(c.textTag[0]),m.push(c.style[0])}catch(v){console.error(v)}else{l=c;var y=null;i%50===0?((y={start:l.start,end:l.end,list:[]}).list.push(l),o.push(y)):((y=o[o.length-1]).end=l.end,y.list.push(l)),i++}}r++}return{list:o,style:{}}}},{key:"parseVTTStyle",value:function(e,t){var n=e.split(":");if(n.length>1){var o=n[0].trim().split("-"),r="";o.length>1?o.map((function(e,t){r+=0===t?e:e.charAt(0).toUpperCase()+e.slice(1)})):r=o[0],t[r]=n[1].trim().replace(/;$/,"")}return t}},{key:"parseVTT",value:function(e){for(var t=(e=e.replace(r,"")).split("\n"),n=[],o=0,l=0,c=null,d=!1,u=!1,p=null,f=null,m=[];o<t.length;){var y=g(o,t).trim();if(!y||d&&h(y))d=!y;else if(a.test(y)&&i.test(g(o-1,t).trim())){u=!0;var v=/\((.+?)\)/g.exec(y);f=v?v[1]:"",p=""}else if(u)s.test(y)?(m.push({key:f,style:p}),p=null,f=null,u=!1):p+=y;else if(y){d=!1;var x=this.checkIsTime(t[o]);if(x){var b=this.parseVttTime(x);if(!c||b.start!==c.start||b.end!==c.end){(c=b).text=[],c.textTag=[];var _=null;l%50===0?((_={start:c.start,end:c.end,list:[]}).list.push(c),n.push(_)):((_=n[n.length-1]).end=c.end,_.list.push(c)),l++}}else if(c){var k=c,w=k.text,C=k.textTag,S=this.parseVttText(t[o]);w.push(S.text),C.push(S.tag)}d=!1}o++}return{list:n,styles:m}}},{key:"checkIsTime",value:function(e){e=e.replace(/\s+/g,"");for(var t=0,n=null;t<c.length&&!(n=c[t].exec(e));)t++;return n?n[0]:null}},{key:"parseVttText",value:function(e){var t=/^(<?.+?>)/g.exec(e),n="",o="default";if(t){o=t[0].replace(/\<|\>|\&/g,"");var r=RegExp("^<".concat(o,">(([\\s\\S])*?)</").concat(o,">$")).exec(e);r?n=r[1]:(n=e,o="")}else n=e;for(var i=/<(\w+).(\w+)>/g,a=i.exec(n);a&&a.length>2;)n=n.replace(a[0],"<".concat(a[1],' class="').concat(a[2],'">')),a=i.exec(n);return{tag:o,text:n.replace(/\\n+/g,"<br/>")}}},{key:"parseVttTime",value:function(e){var t,n=e.split("--\x3e"),o=0;if(2===n.length){var r=n[0].split(":"),i=n[1].split(":");t=f(r),o=f(i)}return{start:t,end:o,time:e}}},{key:"isVTT",value:function(e){return r.test(e)}},{key:"isASS",value:function(e){return l.test(e)}},{key:"checkFormat",value:function(e){return e?r.test(e)?"vtt":l.test(e)?"ass":null:null}}]),e}();t.default=m},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(11),r=(0,o.createClass)((function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.url,r=t.method,i=void 0===r?"GET":r,a=t.type,s=void 0===a?"arraybuffer":a,l=t.timeout,c=void 0===l?1e4:l,d=t.data,u=void 0===d?{}:d,p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,o.classCallCheck)(this,e),new Promise((function(e,t){var o=new window.XMLHttpRequest,r=i.toUpperCase(),a=[];if(s&&(o.responseType=s),c&&(o.timeout=c),Object.keys(u).forEach((function(e){a.push("k=".concat(u[e]))})),o.onload=function(){200===o.status||206===o.status?e({context:p,res:o}):t(new Error({context:p,res:o,type:"error"}))},o.onerror=function(e){t(new Error({context:p,res:o,type:"error"}))},o.ontimeout=function(e){t(new Error({context:p,res:o,type:"error"}))},o.onabort=function(){t(new Error({context:p,res:o,type:"error"}))},"GET"===r)o.open(r,"".concat(n)),o.send();else{if("post"!==r)throw new Error("xhr ".concat(r," is not supported"));o.open(r,n),o.setRequestHeader("Content-type","application/x-www-form-urlencoded"),o.send(a.join("&"))}}))}));t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unObserver=t.addObserver=void 0;var o=n(11),r=function(){function e(){var t=this;(0,o.classCallCheck)(this,e),this.__handlers=[],window.ResizeObserver&&(this.observer=new window.ResizeObserver((function(e){t.__trigger(e)})))}return(0,o.createClass)(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer&&this.observer.observe(e);for(var n=this.__handlers,o=-1,r=0;r<n.length;r++)n[r]&&e===n[r].target&&(o=r);o>-1?this.__handlers[o].handler.push(t):this.__handlers.push({target:e,handler:[t]})}}},{key:"unObserver",value:function(e){var t=-1;this.__handlers.map((function(n,o){e===n.target&&(t=o)})),this.observer&&this.observer.unobserve(e),t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){this.observer&&this.observer.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e,t){for(var n=this.__handlers,o=0;o<n.length;o++)if(n[o]&&e===n[o].target){n[o].handler&&n[o].handler.forEach((function(n){try{n(e,t)}catch(o){console.error(o)}}));break}}},{key:"__trigger",value:function(e){var t=this;e.map((function(e){var n=e.contentRect;t.__runHandler(e.target,n)}))}}]),e}(),i=null;t.addObserver=function(e,t){i||(i=new r),i.addObserver(e,t)},t.unObserver=function(e,t){i&&i.unObserver(e,t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EVENTS={RESIZE:"resize",CHANGE:"change",OFF:"off",UPDATE:"update"}},function(e,t,n){"use strict";var o=O(n(9)),r=O(n(10)),i=O(n(117)),a=O(n(49)),s=O(n(41)),l=O(n(54)),c=O(n(57)),d=O(n(60)),u=O(n(120)),p=O(n(125)),f=O(n(131)),h=O(n(65)),g=O(n(69)),m=O(n(72)),y=O(n(75)),v=O(n(134)),x=O(n(137)),b=O(n(138)),_=O(n(142)),k=O(n(148)),w=O(n(151)),C=O(n(154)),S=O(n(158)),E=O(n(162)),T=O(n(166)),D=O(n(169)),R=O(n(171)),P=O(n(172)),I=O(n(175)),M=O(n(178));function O(e){return e&&e.__esModule?e:{default:e}}o.default.installAll([r.default,i.default,a.default,s.default,l.default,c.default,d.default,u.default,p.default,f.default,h.default,g.default,m.default,y.default,v.default,x.default,b.default,_.default,k.default,w.default,C.default,S.default,E.default,T.default,D.default,R.default,P.default,I.default,M.default])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(118),t.default={name:"s_enter",method:function(){for(var e=this.root,t="",n=1;n<=12;n++)t+='<div class="xgplayer-enter-bar'+n+'"></div>';var r=(0,o.createDom)("xg-enter",'<div class="xgplayer-enter-spinner">\n                                                  '+t+"\n                                                </div>",{},"xgplayer-enter");e.appendChild(r)}},e.exports=t.default},function(e,t,n){var o=n(119);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-enter{display:none;position:absolute;left:0;top:0;width:100%;height:100%;background:#000;z-index:120}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner{display:block;position:absolute;left:50%;top:50%;height:100px;width:100px;position:relative;-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div{width:12%;height:26%;background-color:hsla(0,0%,100%,.7);position:absolute;left:44%;top:37%;opacity:0;border-radius:30px;-webkit-animation:fade 1s linear infinite;animation:fade 1s linear infinite}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar1{-webkit-transform:rotate(0deg) translateY(-142%);-ms-transform:rotate(0deg) translateY(-142%);transform:rotate(0deg) translateY(-142%);-webkit-animation-delay:0s;animation-delay:0s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar2{-webkit-transform:rotate(30deg) translateY(-142%);-ms-transform:rotate(30deg) translateY(-142%);transform:rotate(30deg) translateY(-142%);-webkit-animation-delay:-.9163s;animation-delay:-.9163s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar3{-webkit-transform:rotate(60deg) translateY(-142%);-ms-transform:rotate(60deg) translateY(-142%);transform:rotate(60deg) translateY(-142%);-webkit-animation-delay:-.833s;animation-delay:-.833s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar4{-webkit-transform:rotate(90deg) translateY(-142%);-ms-transform:rotate(90deg) translateY(-142%);transform:rotate(90deg) translateY(-142%);-webkit-animation-delay:-.7497s;animation-delay:-.7497s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar5{-webkit-transform:rotate(120deg) translateY(-142%);-ms-transform:rotate(120deg) translateY(-142%);transform:rotate(120deg) translateY(-142%);-webkit-animation-delay:-.6664s;animation-delay:-.6664s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar6{-webkit-transform:rotate(150deg) translateY(-142%);-ms-transform:rotate(150deg) translateY(-142%);transform:rotate(150deg) translateY(-142%);-webkit-animation-delay:-.5831s;animation-delay:-.5831s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar7{-webkit-transform:rotate(180deg) translateY(-142%);-ms-transform:rotate(180deg) translateY(-142%);transform:rotate(180deg) translateY(-142%);-webkit-animation-delay:-.4998s;animation-delay:-.4998s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar8{-webkit-transform:rotate(210deg) translateY(-142%);-ms-transform:rotate(210deg) translateY(-142%);transform:rotate(210deg) translateY(-142%);-webkit-animation-delay:-.4165s;animation-delay:-.4165s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar9{-webkit-transform:rotate(240deg) translateY(-142%);-ms-transform:rotate(240deg) translateY(-142%);transform:rotate(240deg) translateY(-142%);-webkit-animation-delay:-.3332s;animation-delay:-.3332s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar10{-webkit-transform:rotate(270deg) translateY(-142%);-ms-transform:rotate(270deg) translateY(-142%);transform:rotate(270deg) translateY(-142%);-webkit-animation-delay:-.2499s;animation-delay:-.2499s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar11{-webkit-transform:rotate(300deg) translateY(-142%);-ms-transform:rotate(300deg) translateY(-142%);transform:rotate(300deg) translateY(-142%);-webkit-animation-delay:-.1666s;animation-delay:-.1666s}.xgplayer-skin-default .xgplayer-enter .xgplayer-enter-spinner div.xgplayer-enter-bar12{-webkit-transform:rotate(330deg) translateY(-142%);-ms-transform:rotate(330deg) translateY(-142%);transform:rotate(330deg) translateY(-142%);-webkit-animation-delay:-.0833s;animation-delay:-.0833s}@-webkit-keyframes fade{0%{opacity:1}to{opacity:.25}}@keyframes fade{0%{opacity:1}to{opacity:.25}}.xgplayer-skin-default.xgplayer-is-enter .xgplayer-enter{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=a(n(121)),i=a(n(122));function a(e){return e&&e.__esModule?e:{default:e}}n(123),t.default={name:"s_cssFullscreen",method:function(){var e=this;if(e.config.cssFullscreen){var t=(0,o.createDom)("xg-cssfullscreen",'<xg-icon class="xgplayer-icon">\n                                             <div class="xgplayer-icon-requestfull">'+r.default+'</div>\n                                             <div class="xgplayer-icon-exitfull">'+i.default+"</div>\n                                           </xg-icon>",{},"xgplayer-cssfullscreen"),n={};n.requestfull=e.lang.CSSFULLSCREEN_TIPS,n.exitfull=e.lang.EXITCSSFULLSCREEN_TIPS;var a=(0,o.createDom)("xg-tips",'<span class="xgplayer-tip-requestfull">'+n.requestfull+'</span>\n                                        <span class="xgplayer-tip-exitfull">'+n.exitfull+"</span>",{},"xgplayer-tips");t.appendChild(a),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("cssFullscreenBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.028 0.028)" d="M843.617212 67.898413 175.411567 67.898413c-61.502749 0-111.367437 49.856501-111.367437 111.367437l0 668.205645c0 61.510936 49.864688 111.367437 111.367437 111.367437L843.617212 958.838931c61.510936 0 111.367437-49.856501 111.367437-111.367437L954.984648 179.26585C954.984648 117.754914 905.12917 67.898413 843.617212 67.898413zM398.146441 736.104057c15.380292 0 27.842115 12.461823 27.842115 27.842115 0 15.379269-12.461823 27.841092-27.842115 27.841092L259.725858 791.787264c-7.785314 0-14.781658-3.217275-19.838837-8.365528-5.383614-4.577249-8.791224-11.228739-8.791224-19.475564L231.095797 624.736621c0-15.371082 12.471033-27.842115 27.842115-27.842115 15.380292 0 27.842115 12.471033 27.842115 27.842115l-0.61603 71.426773 133.036969-133.037992 39.378869 39.378869L324.962651 736.113267 398.146441 736.104057zM419.199942 463.611943 286.162974 330.565764l0.61603 71.435982c0 15.380292-12.461823 27.842115-27.842115 27.842115-15.371082 0-27.842115-12.461823-27.842115-27.842115L231.094774 262.791172c0-8.256034 3.40761-14.908548 8.791224-19.476587 5.057179-5.148253 12.053524-8.374738 19.838837-8.374738l138.420583 0.00921c15.380292 0 27.842115 12.461823 27.842115 27.842115s-12.461823 27.842115-27.842115 27.842115l-73.175603-0.00921 133.607974 133.607974L419.199942 463.611943zM787.932981 763.946172c0 8.247848-3.40761 14.899338-8.791224 19.475564-5.057179 5.148253-12.053524 8.365528-19.839861 8.365528L620.881314 791.787264c-15.379269 0-27.841092-12.461823-27.841092-27.841092 0-15.380292 12.461823-27.842115 27.841092-27.842115l73.185836 0.00921L560.449967 602.50427l39.378869-39.378869L732.875015 696.163393l-0.62524-71.426773c0-15.371082 12.462846-27.842115 27.842115-27.842115 15.380292 0 27.842115 12.471033 27.842115 27.842115L787.934005 763.946172zM787.932981 402.000724c0 15.380292-12.461823 27.842115-27.842115 27.842115-15.379269 0-27.842115-12.461823-27.842115-27.842115l0.62524-71.435982L599.828836 463.611943l-39.378869-39.378869 133.617184-133.607974-73.185836 0.00921c-15.379269 0-27.841092-12.461823-27.841092-27.842115s12.461823-27.842115 27.841092-27.842115l138.421606-0.00921c7.785314 0 14.781658 3.226484 19.839861 8.374738 5.383614 4.568039 8.791224 11.219529 8.791224 19.476587L787.934005 402.000724z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.028 0.028)" d="M834.56 81.92H189.44c-59.392 0-107.52 48.128-107.52 107.52v645.12c0 59.392 48.128 107.52 107.52 107.52h645.12c59.392 0 107.52-48.128 107.52-107.52V189.44c0-59.392-48.128-107.52-107.52-107.52zM458.24 727.04c0 14.848-12.288 26.624-26.624 26.624S404.48 741.888 404.48 727.04v-69.632L289.28 773.12c-10.752 10.24-27.648 10.24-37.888 0-10.24-10.752-10.24-27.648 0-37.888L366.592 619.52H296.96c-14.848 0-26.624-12.288-26.624-26.624s12.288-26.624 26.624-26.624h134.144c14.848 0 26.624 12.288 26.624 26.624V727.04z m0-295.936c0 14.848-12.288 26.624-26.624 26.624H296.96c-14.848 0-26.624-12.288-26.624-26.624S282.112 404.48 296.96 404.48h69.632L251.392 289.28c-10.24-10.752-10.24-27.648 0-37.888 5.12-5.12 12.288-7.68 18.944-7.68 6.656 0 13.824 2.56 18.944 7.68L404.48 366.592V296.96c0-14.848 12.288-26.624 26.624-26.624s26.624 12.288 26.624 26.624v134.144zM773.12 773.12c-10.752 10.24-27.648 10.24-37.888 0L619.52 657.408V727.04c0 14.848-12.288 26.624-26.624 26.624s-26.624-11.776-26.624-26.624v-134.144c0-14.848 12.288-26.624 26.624-26.624H727.04c14.848 0 26.624 12.288 26.624 26.624s-12.288 26.624-26.624 26.624h-69.632l115.2 115.2c10.752 10.752 10.752 27.648 0.512 38.4z m0-483.84L657.408 404.48H727.04c14.848 0 26.624 12.288 26.624 26.624 0 14.848-12.288 26.624-26.624 26.624h-134.144c-14.848 0-26.624-12.288-26.624-26.624V296.96c0-14.848 12.288-26.624 26.624-26.624s26.624 12.288 26.624 26.624v69.632L734.72 250.88c5.12-5.12 12.288-7.68 18.944-7.68s13.824 2.56 18.944 7.68c10.752 10.752 10.752 27.648 0.512 38.4z"></path>\n</svg>\n'},function(e,t,n){var o=n(124);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-cssfullscreen,.xgplayer-skin-default .xgplayer-cssfullscreen-img{position:relative;-webkit-order:12;-moz-box-ordinal-group:13;order:12;display:block;cursor:pointer}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon{width:32px;margin-top:5px}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon div,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:block}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:none}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-40px}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:block}.xgplayer-skin-default .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:none}.xgplayer-skin-default .xgplayer-cssfullscreen-img:hover,.xgplayer-skin-default .xgplayer-cssfullscreen:hover{opacity:.85}.xgplayer-skin-default .xgplayer-cssfullscreen-img:hover .xgplayer-tips,.xgplayer-skin-default .xgplayer-cssfullscreen:hover .xgplayer-tips{display:block}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-requestfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-icon .xgplayer-icon-exitfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-icon .xgplayer-icon-exitfull{display:block}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-47px}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-requestfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-requestfull{display:none}.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips .xgplayer-tip-exitfull,.xgplayer-skin-default.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips .xgplayer-tip-exitfull{display:block}.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-cssfullscreen,.xgplayer-skin-default.xgplayer-is-fullscreen .xgplayer-cssfullscreen-img{display:none}.xgplayer-skin-default.xgplayer-is-cssfullscreen{position:fixed!important;left:0!important;top:0!important;width:100%!important;height:100%!important;z-index:99999!important}.xgplayer-lang-is-en .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-lang-is-en .xgplayer-cssfullscreen .xgplayer-tips,.xgplayer-lang-is-en.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips,.xgplayer-lang-is-en.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-46px}.lang-is-jp .xgplayer-cssfullscreen-img .xgplayer-tips,.lang-is-jp .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-120px}.lang-is-jp.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen-img .xgplayer-tips,.lang-is-jp.xgplayer-is-cssfullscreen .xgplayer-cssfullscreen .xgplayer-tips{margin-left:-60px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=s(n(126)),i=s(n(127)),a=s(n(128));function s(e){return e&&e.__esModule?e:{default:e}}n(129),t.default={name:"s_volume",method:function(){var e=this,t=(0,o.createDom)("xg-volume",'<xg-icon class="xgplayer-icon">\n                                         <div class="xgplayer-icon-large">'+a.default+'</div>\n                                         <div class="xgplayer-icon-small">'+i.default+'</div>\n                                         <div class="xgplayer-icon-muted">'+r.default+'</div>\n                                       </xg-icon>\n                                       <xg-slider class="xgplayer-slider" tabindex="2">\n                                         <xg-bar class="xgplayer-bar">\n                                           <xg-drag class="xgplayer-drag"></xg-drag>\n                                         </xg-bar>\n                                       </xg-slider>',{},"xgplayer-volume");e.once("ready",(function(){e.controls&&e.controls.appendChild(t)}));var n=t.querySelector(".xgplayer-slider"),s=t.querySelector(".xgplayer-bar"),l=t.querySelector(".xgplayer-drag"),c=t.querySelector(".xgplayer-icon");l.style.height=100*e.config.volume+"%",n.volume=e.config.volume,s.addEventListener("mousedown",(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeBarClick",t)})),["click","touchend"].forEach((function(t){c.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeIconClick")}))})),c.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeIconEnter")})),["blur","mouseleave"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("volumeIconLeave")}))}))}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n  <path transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path transform="scale(0.0220625 0.0220625)" d="M920.4 439.808l-108.544-109.056-72.704 72.704 109.568 108.544-109.056 108.544 72.704 72.704 108.032-109.568 108.544 109.056 72.704-72.704-109.568-108.032 109.056-108.544-72.704-72.704-108.032 109.568z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n  <path transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path transform="scale(0.0220625 0.0220625)" d="M795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n'},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n  <path transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path transform="scale(0.0220625 0.0220625)" d="M940.632 837.632l-72.192-72.192c65.114-64.745 105.412-154.386 105.412-253.44s-40.299-188.695-105.396-253.424l-0.016-0.016 72.192-72.192c83.639 83.197 135.401 198.37 135.401 325.632s-51.762 242.434-135.381 325.612l-0.020 0.020zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n'},function(e,t,n){var o=n(130);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,'.xgplayer-skin-default .xgplayer-volume{outline:none;-webkit-order:4;-moz-box-ordinal-group:5;order:4;width:40px;height:40px;display:block;position:relative;z-index:18}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon{margin-top:8px;cursor:pointer;position:absolute;bottom:-9px}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon .xgplayer-icon-large{display:block}.xgplayer-skin-default .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted,.xgplayer-skin-default .xgplayer-volume .xgplayer-icon .xgplayer-icon-small{display:none}.xgplayer-skin-default .xgplayer-slider{display:none;position:absolute;width:28px;height:92px;background:rgba(0,0,0,.54);border-radius:1px;bottom:42px;outline:none}.xgplayer-skin-default .xgplayer-slider:after{content:" ";display:block;height:15px;width:28px;position:absolute;bottom:-15px;left:0;z-index:20}.xgplayer-skin-default .xgplayer-bar,.xgplayer-skin-default .xgplayer-drag{display:block;position:absolute;bottom:6px;left:12px;background:hsla(0,0%,100%,.3);border-radius:100px;width:4px;height:76px;outline:none;cursor:pointer}.xgplayer-skin-default .xgplayer-drag{bottom:0;left:0;background:#fa1f41;max-height:76px}.xgplayer-skin-default .xgplayer-drag:after{content:" ";display:inline-block;width:8px;height:8px;background:#fff;box-shadow:0 0 5px 0 rgba(0,0,0,.26);position:absolute;border-radius:50%;left:-2px;top:-6px}.xgplayer-skin-default.xgplayer-volume-active .xgplayer-slider,.xgplayer-skin-default.xgplayer-volume-large .xgplayer-volume .xgplayer-icon .xgplayer-icon-large{display:block}.xgplayer-skin-default.xgplayer-volume-large .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted,.xgplayer-skin-default.xgplayer-volume-large .xgplayer-volume .xgplayer-icon .xgplayer-icon-small,.xgplayer-skin-default.xgplayer-volume-small .xgplayer-volume .xgplayer-icon .xgplayer-icon-large{display:none}.xgplayer-skin-default.xgplayer-volume-small .xgplayer-volume .xgplayer-icon .xgplayer-icon-small{display:block}.xgplayer-skin-default.xgplayer-volume-muted .xgplayer-volume .xgplayer-icon .xgplayer-icon-large,.xgplayer-skin-default.xgplayer-volume-muted .xgplayer-volume .xgplayer-icon .xgplayer-icon-small,.xgplayer-skin-default.xgplayer-volume-small .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted{display:none}.xgplayer-skin-default.xgplayer-volume-muted .xgplayer-volume .xgplayer-icon .xgplayer-icon-muted{display:block}.xgplayer-skin-default.xgplayer-mobile .xgplayer-volume .xgplayer-slider{display:none}',""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(5),a=(o=i)&&o.__esModule?o:{default:o};n(132),t.default={name:"s_definition",method:function(){var e=this,t=e.root,n=void 0,o=(0,r.createDom)("xg-definition","",{tabindex:3},"xgplayer-definition");function i(){var n=e.definitionList,i=["<ul>"],a=e.config.url,s=document.createElement("a");e.switchURL?["mp4","hls","__flv__","dash"].every((function(t){return!e[t]||(e[t].url&&(s.href=e[t].url),"__flv__"===t&&(e[t]._options?s.href=e[t]._options.url:s.href=e[t]._mediaDataSource.url),"hls"===t&&(s.href=e[t].originUrl||e[t].url,a=s.href),a=s.href,!1)})):a=e.currentSrc||e.src,n.forEach((function(t){s.href=t.url,e.dash?i.push("<li url='"+t.url+"' cname='"+t.name+"' class='"+(t.selected?"selected":"")+"'>"+t.name+"</li>"):i.push("<li url='"+t.url+"' cname='"+t.name+"' class='"+(s.href===a?"selected":"")+"'>"+t.name+"</li>")}));var l=n.filter((function(t){return s.href=t.url,e.dash?!0===t.selected:s.href===a}));console.warn("cursrc:",l,"src:",a,"list:",n),i.push("</ul><p class='name'>"+(l[0]||{name:""}).name+"</p>");var c=t.querySelector(".xgplayer-definition");if(c){c.innerHTML=i.join("");var d=c.querySelector(".name");e.config.definitionActive&&"hover"!==e.config.definitionActive||d.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-definition-active"),c.focus()}))}else{o.innerHTML=i.join("");var u=o.querySelector(".name");e.config.definitionActive&&"hover"!==e.config.definitionActive||u.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-definition-active"),o.focus()})),e.controls.appendChild(o)}}function s(n){e.definitionList=n,n&&n instanceof Array&&n.length>0&&((0,r.addClass)(t,"xgplayer-is-definition"),e.once("canplay",i))}function l(){if(e.currentTime=e.curTime,n)e.pause();else{var t=e.play();void 0!==t&&t&&t.catch((function(e){}))}}function c(){e.once("timeupdate",l)}function d(){(0,r.removeClass)(t,"xgplayer-definition-active")}"mobile"===a.default.device&&(e.config.definitionActive="click"),e.on("resourceReady",s),["touchend","click"].forEach((function(t){o.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation();var i=e.definitionList,s=t.target||t.srcElement,d=document.createElement("a");if(s&&"li"===s.tagName.toLocaleLowerCase()){var u,p=void 0;if(Array.prototype.forEach.call(s.parentNode.childNodes,(function(t){(0,r.hasClass)(t,"selected")&&(p=t.getAttribute("cname"),(0,r.removeClass)(t,"selected"),e.emit("beforeDefinitionChange",t.getAttribute("url")))})),e.dash&&i.forEach((function(e){e.selected=!1,e.name===s.innerHTML&&(e.selected=!0)})),(0,r.addClass)(s,"selected"),u=s.getAttribute("cname"),s.parentNode.nextSibling.innerHTML=""+s.getAttribute("cname"),d.href=s.getAttribute("url"),n=e.paused,e.switchURL){var f=document.createElement("a");["mp4","hls","__flv__","dash"].every((function(t){return!e[t]||(e[t].url&&(f.href=e[t].url),"__flv__"===t&&(e[t]._options?f.href=e[t]._options.url:f.href=e[t]._mediaDataSource.url),"hls"===t&&(f.href=e[t].originUrl||e[t].url),!1)})),f.href===d.href||e.ended||e.switchURL(d.href)}else e.hls&&(document.createElement("a"),e.hls.url),d.href!==e.currentSrc&&(e.curTime=e.currentTime,e.ended||(e.src=d.href));navigator.userAgent.toLowerCase().indexOf("android")>-1?e.once("timeupdate",c):e.once("loadedmetadata",l),e.emit("definitionChange",{from:p,to:u}),"mobile"===a.default.device&&(0,r.removeClass)(e.root,"xgplayer-definition-active")}else"click"!==e.config.definitionActive||!s||"p"!==s.tagName.toLocaleLowerCase()&&"em"!==s.tagName.toLocaleLowerCase()||("mobile"===a.default.device?(0,r.toggleClass)(e.root,"xgplayer-definition-active"):(0,r.addClass)(e.root,"xgplayer-definition-active"),o.focus());e.emit("focus")}),!1)})),o.addEventListener("mouseleave",(function(e){e.preventDefault(),e.stopPropagation(),(0,r.removeClass)(t,"xgplayer-definition-active")})),e.on("blur",d),e.once("destroy",(function t(){e.off("resourceReady",s),e.off("canplay",i),navigator.userAgent.toLowerCase().indexOf("android")>-1?(e.off("timeupdate",c),e.off("timeupdate",l)):e.off("loadedmetadata",l),e.off("blur",d),e.off("destroy",t)})),e.getCurrentDefinition=function(){for(var t=e.controls.querySelectorAll(".xgplayer-definition ul li"),n=0;n<t.length;n++)if(t[n].className&&t[n].className.indexOf("selected")>-1)return{name:t[n].getAttribute("cname"),url:t[n].getAttribute("url")};return{name:t[0].getAttribute("cname"),url:t[0].getAttribute("url")}},e.switchDefinition=function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.controls.querySelectorAll(".xgplayer-definition ul li"),o=0;o<n.length;o++)n[o].getAttribute("cname")!==t.name&&n[o].getAttribute("url")!==t.url&&o!==t.index||n[o].click()}}},e.exports=t.default},function(e,t,n){var o=n(133);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-definition{-webkit-order:5;-moz-box-ordinal-group:6;order:5;width:60px;height:150px;z-index:18;position:relative;outline:none;display:none;cursor:default;margin-left:10px;margin-top:-119px}.xgplayer-skin-default .xgplayer-definition ul{display:none;list-style:none;width:78px;background:rgba(0,0,0,.54);border-radius:1px;position:absolute;bottom:30px;left:0;text-align:center;white-space:nowrap;margin-left:-10px;z-index:26;cursor:pointer}.xgplayer-skin-default .xgplayer-definition ul li{opacity:.7;font-family:PingFangSC-Regular;font-size:11px;color:hsla(0,0%,100%,.8);padding:6px 13px}.xgplayer-skin-default .xgplayer-definition ul li.selected,.xgplayer-skin-default .xgplayer-definition ul li:hover{color:#fff;opacity:1}.xgplayer-skin-default .xgplayer-definition .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;cursor:pointer;color:hsla(0,0%,100%,.8);position:absolute;bottom:0;width:60px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-skin-default.xgplayer-definition-active .xgplayer-definition ul,.xgplayer-skin-default.xgplayer-is-definition .xgplayer-definition{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(5),a=(o=i)&&o.__esModule?o:{default:o};n(135),t.default={name:"s_playbackRate",method:function(){var e=this,t=[];if(!e.config.playbackRate)return!1;(t=[].concat(e.config.playbackRate)).sort((function(e,t){return t-e}));var n=void 0!==e.config.playbackRateUnit?e.config.playbackRateUnit:"x",o=(0,r.createDom)("xg-playbackrate"," ",{},"xgplayer-playbackrate");"mobile"===a.default.device&&(e.config.playbackRateActive="click");var i=[];t.forEach((function(e){i.push({name:""+e,rate:""+e+n,selected:!1})}));var s=1,l=["<ul>"];i.forEach((function(t){e.config.defaultPlaybackRate&&e.config.defaultPlaybackRate.toString()===t.name?(t.selected=!0,s=e.config.defaultPlaybackRate,e.once("playing",(function(){e.video.playbackRate=e.config.defaultPlaybackRate}))):"1.0"!==t.name&&"1"!==t.name||e.config.defaultPlaybackRate&&1!==e.config.defaultPlaybackRate||(t.selected=!0),l.push("<li cname='"+t.name+"' class='"+(t.selected?"selected":"")+"'>"+t.rate+"</li>")})),l.push("</ul><p class='name'>"+s+n+"</p>");var c=e.root.querySelector(".xgplayer-playbackrate");if(c){c.innerHTML=l.join("");var d=c.querySelector(".name");e.config.playbackRateActive&&"hover"!==e.config.playbackRateActive||d.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-playbackrate-active"),c.focus()}))}else{o.innerHTML=l.join("");var u=o.querySelector(".name");e.config.playbackRateActive&&"hover"!==e.config.playbackRateActive||u.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-playbackrate-active"),o.focus()})),e.once("ready",(function(){e.controls.appendChild(o)}))}["touchend","click"].forEach((function(t){o.addEventListener(t,(function(t){t.stopPropagation(),t.preventDefault();var l=t.target;if(l&&"li"===l.tagName.toLocaleLowerCase()){var c,d=void 0;i.forEach((function(t){t.selected=!1,l.textContent.replace(/\s+/g,"")===t.rate&&(Array.prototype.forEach.call(l.parentNode.childNodes,(function(e){(0,r.hasClass)(e,"selected")&&(d=Number(e.getAttribute("cname")),(0,r.removeClass)(e,"selected"))})),t.selected=!0,e.video.playbackRate=1*t.name,s=1*t.name)})),(0,r.addClass)(l,"selected"),c=Number(l.getAttribute("cname")),l.parentNode.nextSibling.innerHTML=""+l.getAttribute("cname")+n,e.emit("playbackrateChange",{from:d,to:c}),"mobile"===a.default.device&&(0,r.removeClass)(e.root,"xgplayer-playbackrate-active")}else"click"!==e.config.playbackRateActive||!l||"p"!==l.tagName.toLocaleLowerCase()&&"span"!==l.tagName.toLocaleLowerCase()||("mobile"===a.default.device?(0,r.toggleClass)(e.root,"xgplayer-playbackrate-active"):(0,r.addClass)(e.root,"xgplayer-playbackrate-active"),o.focus());e.emit("focus")}),!1)})),o.addEventListener("mouseleave",(function(t){t.preventDefault(),t.stopPropagation(),(0,r.removeClass)(e.root,"xgplayer-playbackrate-active")})),e.on("blur",(function(){(0,r.removeClass)(e.root,"xgplayer-playbackrate-active")})),e.on("play",(function(){e.video.playbackRate.toFixed(1)!==s.toFixed(1)&&(e.video.playbackRate=s)})),e.switchPlaybackRate=function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.controls.querySelectorAll(".xgplayer-playbackrate ul li"),o=0;o<n.length;o++)(0,r.hasClass)(n[o],"selected")||n[o].getAttribute("cname")!==""+t.playbackRate&&o!==t.index||n[o].click()},e.on("ratechange",(function(){e.switchPlaybackRate({playbackRate:e.playbackRate})}))}},e.exports=t.default},function(e,t,n){var o=n(136);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-playbackrate{-webkit-order:8;-moz-box-ordinal-group:9;order:8;width:60px;height:150px;z-index:18;position:relative;display:inline-block;cursor:default;margin-top:-119px}.xgplayer-skin-default .xgplayer-playbackrate ul{display:none;list-style:none;width:78px;background:rgba(0,0,0,.54);border-radius:1px;position:absolute;bottom:30px;left:50%;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);text-align:left;white-space:nowrap;z-index:26;cursor:pointer}.xgplayer-skin-default .xgplayer-playbackrate ul li{opacity:.7;font-family:PingFangSC-Regular;font-size:11px;color:hsla(0,0%,100%,.8);position:relative;padding:4px 0;text-align:center}.xgplayer-skin-default .xgplayer-playbackrate ul li.selected,.xgplayer-skin-default .xgplayer-playbackrate ul li:hover{color:#fff;opacity:1}.xgplayer-skin-default .xgplayer-playbackrate ul li:first-child{position:relative;margin-top:12px}.xgplayer-skin-default .xgplayer-playbackrate ul li:last-child{position:relative;margin-bottom:12px}.xgplayer-skin-default .xgplayer-playbackrate .name{width:60px;height:20px;position:absolute;bottom:0;text-align:center;font-family:PingFangSC-Regular;font-size:13px;background:rgba(0,0,0,.38);color:hsla(0,0%,100%,.8);border-radius:10px;line-height:20px}.xgplayer-skin-default .xgplayer-playbackrate span{position:relative;top:19px;font-weight:700;text-shadow:0 0 4px rgba(0,0,0,.6)}.xgplayer-skin-default .xgplayer-playbackrate:hover{opacity:1}.xgplayer-skin-default.xgplayer-playbackrate-active .xgplayer-playbackrate ul{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);t.default={name:"s_localPreview",method:function(){var e=this;if(e.config.preview&&e.config.preview.uploadEl){var t=(0,o.createDom)("xg-preview",'<input type="file">',{},"xgplayer-preview"),n=t.querySelector("input");e.config.preview.uploadEl.appendChild(t),n.onchange=function(){e.emit("upload",n)}}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(139),a=(o=i)&&o.__esModule?o:{default:o};n(140),t.default={name:"s_download",method:function(){var e=this;if(e.config.download){var t=(0,r.createDom)("xg-download",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-download"),n=e.lang.DOWNLOAD_TIPS,o=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-download">'+n+"</span>",{},"xgplayer-tips");t.appendChild(o),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("downloadBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">\n  <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n    <g transform="translate(-488.000000, -340.000000)" fill="#FFFFFF">\n      <g id="Group-2">\n        <g id="volme_big-copy" transform="translate(488.000000, 340.000000)">\n          <rect id="Rectangle-18" x="11" y="4" width="2" height="12" rx="1"></rect>\n          <rect id="Rectangle-2" x="3" y="18" width="18" height="2" rx="1"></rect>\n          <rect id="Rectangle-2" transform="translate(4.000000, 17.500000) rotate(90.000000) translate(-4.000000, -17.500000) " x="1.5" y="16.5" width="5" height="2" rx="1"></rect><rect id="Rectangle-2-Copy-3" transform="translate(20.000000, 17.500000) rotate(90.000000) translate(-20.000000, -17.500000) " x="17.5" y="16.5" width="5" height="2" rx="1"></rect>\n          <path d="M9.48791171,8.26502656 L9.48791171,14.2650266 C9.48791171,14.8173113 9.04019646,15.2650266 8.48791171,15.2650266 C7.93562696,15.2650266 7.48791171,14.8173113 7.48791171,14.2650266 L7.48791171,7.26502656 C7.48791171,6.71274181 7.93562696,6.26502656 8.48791171,6.26502656 L15.4879117,6.26502656 C16.0401965,6.26502656 16.4879117,6.71274181 16.4879117,7.26502656 C16.4879117,7.81731131 16.0401965,8.26502656 15.4879117,8.26502656 L9.48791171,8.26502656 Z" id="Combined-Shape" transform="translate(11.987912, 10.765027) scale(1, -1) rotate(45.000000) translate(-11.987912, -10.765027) "></path>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n'},function(e,t,n){var o=n(141);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-download{position:relative;-webkit-order:9;-moz-box-ordinal-group:10;order:9;display:block;cursor:pointer}.xgplayer-skin-default .xgplayer-download .xgplayer-icon{margin-top:3px}.xgplayer-skin-default .xgplayer-download .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-download .xgplayer-icon svg{position:relative;top:5px;left:5px}.xgplayer-skin-default .xgplayer-download .xgplayer-tips{margin-left:-20px}.xgplayer-skin-default .xgplayer-download .xgplayer-tips .xgplayer-tip-download{display:block}.xgplayer-skin-default .xgplayer-download:hover{opacity:.85}.xgplayer-skin-default .xgplayer-download:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-download .xgplayer-tips{margin-left:-32px}.xgplayer-lang-is-jp .xgplayer-download .xgplayer-tips{margin-left:-40px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=a(n(143)),i=a(n(145));function a(e){return e&&e.__esModule?e:{default:e}}n(146),t.default={name:"s_danmu",method:function(){var e=this,t=e.root;if(e.config.danmu){var n=(0,o.createDom)("xg-danmu","",{},"xgplayer-danmu");e.once("ready",(function(){t.appendChild(n)}));var a=(0,o.deepCopy)({container:n,player:e.video,comments:[],area:{start:0,end:1}},e.config.danmu),s=void 0;e.config.danmu.panel&&(s=(0,o.createDom)("xg-panel",'<xg-panel-icon class="xgplayer-panel-icon">\n                                                '+i.default+'\n                                              </xg-panel-icon>\n                                              <xg-panel-slider class="xgplayer-panel-slider">\n                                                <xg-hidemode class="xgplayer-hidemode">\n                                                  <p class="xgplayer-hidemode-font">\u5c4f\u853d\u7c7b\u578b</p>\n                                                  <ul class="xgplayer-hidemode-radio">\n                                                    <li class="xgplayer-hidemode-scroll" id="false">\u6eda\u52a8</li><li class="xgplayer-hidemode-top" id="false">\u9876\u90e8</li><li class="xgplayer-hidemode-bottom" id="false">\u5e95\u90e8</li><li class="xgplayer-hidemode-color" id="false">\u8272\u5f69</li>\n                                                  </ul>\n                                                </xg-hidemode>\n                                                <xg-transparency class="xgplayer-transparency">\n                                                  <span>\u4e0d\u900f\u660e\u5ea6</span>\n                                                  <input class="xgplayer-transparency-line xgplayer-transparency-color xgplayer-transparency-bar xgplayer-transparency-gradient" type="range" min="0" max="100" step="0.1" value="50"></input>\n                                                </xg-transparency>\n                                                <xg-showarea class="xgplayer-showarea">\n                                                  <div class="xgplayer-showarea-name">\u663e\u793a\u533a\u57df</div>\n                                                  <div class="xgplayer-showarea-control">\n                                                    <div class="xgplayer-showarea-control-up">\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-onequarters">1/4</span>\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-twoquarters selected-color">1/2</span>\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-threequarters">3/4</span>\n                                                      <span class="xgplayer-showarea-control-up-item xgplayer-showarea-full">1</span>\n                                                    </div>\n                                                    <div class="xgplayer-showarea-control-down">\n                                                      <div class="xgplayer-showarea-control-down-dots">\n                                                        <span class="xgplayer-showarea-onequarters-dot"></span>\n                                                        <span class="xgplayer-showarea-twoquarters-dot"></span>\n                                                        <span class="xgplayer-showarea-threequarters-dot"></span>\n                                                        <span class="xgplayer-showarea-full-dot"></span>\n                                                      </div>\n                                                      <input class="xgplayer-showarea-line xgplayer-showarea-color xgplayer-showarea-bar xgplayer-gradient" type="range" min="1" max="4" step="1" value="1">\n                                                    </div>\n                                                  </div>\n                                                </xg-showarea>\n                                                <xg-danmuspeed class="xgplayer-danmuspeed">\n                                                  <div class="xgplayer-danmuspeed-name">\u5f39\u5e55\u901f\u5ea6</div>\n                                                  <div class="xgplayer-danmuspeed-control">\n                                                    <div class="xgplayer-danmuspeed-control-up">\n                                                      <span class="xgplayer-danmuspeed-control-up-item xgplayer-danmuspeed-small">\u6162</span>\n                                                      <span class="xgplayer-danmuspeed-control-up-item xgplayer-danmuspeed-middle selected-color">\u4e2d</span>\n                                                      <span class="xgplayer-danmuspeed-control-up-item xgplayer-danmuspeed-large">\u5feb</span>\n                                                    </div>\n                                                    <div class="xgplayer-danmuspeed-control-down">\n                                                      <div class="xgplayer-danmuspeed-control-down-dots">\n                                                        <span class="xgplayer-danmuspeed-small-dot"></span>\n                                                        <span class="xgplayer-danmuspeed-middle-dot"></span>\n                                                        <span class="xgplayer-danmuspeed-large-dot"></span>\n                                                      </div>\n                                                      <input class="xgplayer-danmuspeed-line xgplayer-danmuspeed-color xgplayer-danmuspeed-bar xgplayer-gradient" type="range" min="50" max="150" step="50" value="100">\n                                                    </div>\n                                                  </div>\n                                                </xg-danmuspeed>\n                                                <xg-danmufont class="xgplayer-danmufont">\n                                                  <div class="xgplayer-danmufont-name">\u5b57\u4f53\u5927\u5c0f</div>\n                                                  <div class="xgplayer-danmufont-control">\n                                                    <div class="xgplayer-danmufont-control-up">\n                                                      <span class="xgplayer-danmufont-control-up-item xgplayer-danmufont-small">\u5c0f</span>\n                                                      <span class="xgplayer-danmufont-control-up-item xgplayer-danmufont-middle">\u4e2d</span>\n                                                      <span class="xgplayer-danmufont-control-up-item xgplayer-danmufont-large selected-color">\u5927</span>\n                                                    </div>\n                                                    <div class="xgplayer-danmufont-control-down">\n                                                      <div class="xgplayer-danmufont-control-down-dots">\n                                                        <span class="xgplayer-danmufont-small-dot"></span>\n                                                        <span class="xgplayer-danmufont-middle-dot"></span>\n                                                        <span class="xgplayer-danmufont-large-dot"></span>\n                                                      </div>\n                                                      <input class="xgplayer-danmufont-line xgplayer-danmufont-color xgplayer-danmufont-bar xgplayer-gradient" type="range" min="20" max="30" step="5" value="25">\n                                                    </div>\n                                                  </div>\n                                                </xg-danmufont>\n                                              </xg-panel-slider>',{tabindex:7},"xgplayer-panel"),e.once("ready",(function(){e.controls.appendChild(s)}))),e.once("complete",(function(){var t=new r.default(a);if(e.emit("initDefaultDanmu",t),e.danmu=t,e.config.danmu.panel){var n=s.querySelector(".xgplayer-panel-slider"),i=void 0;["mouseenter","touchend","click"].forEach((function(e){s.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),(0,o.addClass)(n,"xgplayer-panel-active"),s.focus(),i=!0}))})),s.addEventListener("mouseleave",(function(e){e.preventDefault(),e.stopPropagation(),(0,o.removeClass)(n,"xgplayer-panel-active"),i=!1})),n.addEventListener("mouseleave",(function(e){e.preventDefault(),e.stopPropagation(),!1===i&&(0,o.removeClass)(n,"xgplayer-panel-active")}));var l=e.config.danmu,c={scroll:s.querySelector(".xgplayer-hidemode-scroll"),top:s.querySelector(".xgplayer-hidemode-top"),bottom:s.querySelector(".xgplayer-hidemode-bottom"),color:s.querySelector(".xgplayer-hidemode-color")},d=function(t){var n=t;["touchend","click"].forEach((function(t){c[n].addEventListener(t,(function(t){"true"!==c[n].getAttribute("id")?(c[n].style.color="#f85959",c[n].setAttribute("id","true"),e.danmu.hide(n)):(c[n].style.color="#aaa",c[n].setAttribute("id","false"),e.danmu.show(n))}))}))};for(var u in c)d(u);var p=s.querySelector(".xgplayer-transparency-line"),f=s.querySelector(".xgplayer-transparency-gradient"),h=50;if(f.style.background="linear-gradient(to right, #f85959 0%, #f85959 "+h+"%, #aaa "+h+"%, #aaa)",p.addEventListener("input",(function(e){e.preventDefault(),e.stopPropagation(),h=e.target.value,f.style.background="linear-gradient(to right, #f85959 0%, #f85959 "+h+"%, #aaa "+h+"%, #aaa)",l.comments.forEach((function(e){e.style.opacity=h/100}))})),s.querySelector(".xgplayer-showarea-line").addEventListener("input",(function(t){t.preventDefault(),t.stopPropagation();var n=t.target.value;e.danmu.config.area.end=n/100,e.config.danmu.area.end=n/100,e.danmu.bulletBtn.main.channel.resize()})),s.querySelector(".xgplayer-danmuspeed-line").addEventListener("input",(function(e){e.preventDefault(),e.stopPropagation();var t=e.target.value;l.comments.forEach((function(e){e.duration=100*(200-t)}))})),s.querySelector(".xgplayer-danmufont-line").addEventListener("input",(function(e){e.preventDefault(),e.stopPropagation();var t=e.target.value;l.comments.forEach((function(e){e.style.fontSize=t+"px"}))})),navigator.userAgent.indexOf("Firefox")>-1)for(var g=0;g<n.querySelectorAll("input").length;g++)n.querySelectorAll("input")[g].style.marginTop="10px"}}))}}},e.exports=t.default},function(e,t,n){"use strict";(function(e){var n,o,r,i,a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};window,i=function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==("undefined"===typeof e?"undefined":a(e))&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=4)}([function(e,t,n){function o(e,t){return e.classList?Array.prototype.some.call(e.classList,(function(e){return e===t})):!!e.className.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}function r(e,t){e.classList?t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)})):o(e,t)||(e.className+=" "+t)}function i(e,t){e.classList?t.split(/\s+/g).forEach((function(t){e.classList.remove(t)})):o(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ")}))}function a(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]}Object.defineProperty(t,"__esModule",{value:!0}),t.createDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=document.createElement(e);return r.className=o,r.innerHTML=t,Object.keys(n).forEach((function(t){var o=t,i=n[t];"video"===e||"audio"===e?i&&r.setAttribute(o,i):r.setAttribute(o,i)})),r},t.hasClass=o,t.addClass=r,t.removeClass=i,t.toggleClass=function(e,t){t.split(/\s+/g).forEach((function(t){o(e,t)?i(e,t):r(e,t)}))},t.findDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=arguments[1],n=void 0;try{n=e.querySelector(t)}catch(o){t.startsWith("#")&&(n=e.getElementById(t.slice(1)))}return n},t.deepCopy=function e(t,n){if("Object"===a(n)&&"Object"===a(t))return Object.keys(n).forEach((function(o){"Object"!==a(n[o])||n[o]instanceof Node?"Array"===a(n[o])?t[o]="Array"===a(t[o])?t[o].concat(n[o]):n[o]:t[o]=n[o]:t[o]?e(t[o],n[o]):t[o]=n[o]})),t},t.typeOf=a,t.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},t.attachEventListener=function(e,t,n,o){o?(e.on(t,n),function(e,t,n,o){e.once(o,(function r(){e.off(t,n),e.off(o,r)}))}(e,t,n,o)):e.on(t,(function o(r){n(r),e.off(t,o)}))},t.styleUtil=function(e,t,n){var o=e.style;try{o[t]=n}catch(e){o.setProperty(t,n)}},t.styleCSSText=function(e,t){var n=e.style;try{n.cssText=t}catch(e){}},t.isNumber=function(e){return"number"==typeof e&&!Number.isNaN(e)},t.throttle=function(e,t){var n=this,o=0;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];clearTimeout(o),o=setTimeout((function(){return e.apply(n,i)}),t)}},t.hasOwnProperty=Object.prototype.hasOwnProperty},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o,r=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=(o=n(26))&&o.__esModule?o:{default:o},a=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return r(e,[{key:"setLogger",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.logger=new i.default(e+".js")}}]),e}();t.default=a,e.exports=t.default},function(e,t,n){var o=n(18)();e.exports=function(e){return e!==o&&null!==e}},function(e,t,n){e.exports=function(e){return null!=e}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o,r=(o=n(5))&&o.__esModule?o:{default:o};n(34),t.default=r.default,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.DanmuJs=void 0;var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=p(n(6)),i=n(25),s=p(n(1)),l=p(n(27)),c=p(n(32)),d=n(33),u=n(0);function p(e){return e&&e.__esModule?e:{default:e}}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}var h=t.DanmuJs=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=f(this,(t.__proto__||Object.getPrototypeOf(t)).call(this)),o=n;o.setLogger("danmu"),o.logger&&o.logger.info("danmu.js version: "+i.version);var a=o.config={overlap:!1,area:{start:0,end:1,lines:void 0},live:!1,comments:[],direction:"r2l",needResizeObserver:!1,dropStaleComments:!1,channelSize:void 0,maxCommentsLength:void 0,bulletOffset:void 0,interval:2e3,highScorePriority:!0};if((0,u.deepCopy)(a,e),(0,r.default)(o),o.hideArr=[],o.domObj=new c.default,o.freezeId=null,a.comments.forEach((function(e){e.duration=e.duration?e.duration:5e3,e.mode||(e.mode="scroll")})),o.container=a.container&&1===a.container.nodeType?a.container:null,!o.container)return o.emit("error","container id can't be empty"),f(n,!1);if(a.containerStyle){var s=a.containerStyle;Object.keys(s).forEach((function(e){o.container.style[e]=s[e]}))}return o.live=a.live,o.player=a.player,o.direction=a.direction,(0,u.addClass)(o.container,"danmu"),o.bulletBtn=new l.default(o),o.main=o.bulletBtn.main,o.isReady=!0,o.emit("ready"),n.logger&&n.logger.info("ready"),n.addResizeObserver(),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"addResizeObserver",value:function(){var e=this;this.config.needResizeObserver&&(0,d.addObserver)(this.container,(function(){e.logger&&e.logger.info("needResizeObserver"),e.resize()}))}},{key:"start",value:function(){this.logger&&this.logger.info("start"),this.main.start()}},{key:"pause",value:function(){this.logger&&this.logger.info("pause"),this.main.pause()}},{key:"play",value:function(){this.logger&&this.logger.info("play"),this.main.play()}},{key:"stop",value:function(){this.logger&&this.logger.info("stop"),this.main.stop()}},{key:"clear",value:function(){this.logger&&this.logger.info("clear"),this.main.clear()}},{key:"destroy",value:function(){for(var e in(0,d.unObserver)(this.container),this.logger&&this.logger.info("destroy"),this.stop(),this.bulletBtn.destroy(),this.domObj.destroy(),this)delete this[e];this.emit("destroy")}},{key:"sendComment",value:function(e){this.logger&&this.logger.info("sendComment: "+(e.txt||"[DOM Element]"));var t=this.main;e.duration||(e.duration=15e3),e&&e.id&&e.duration&&(e.el||e.txt)&&(e.duration=e.duration?e.duration:5e3,e.style||(e.style={opacity:void 0,fontSize:void 0}),e.style&&(this.opacity&&this.opacity!==e.style.opacity&&(e.style.opacity=this.opacity),this.fontSize&&this.fontSize!==e.style.fontSize&&(e.style.fontSize=this.fontSize)),e.prior||e.realTime?(t.data.unshift(e),e.realTime&&(t.readData(),t.dataHandle())):t.data.push(e),t.keepPoolWatermark())}},{key:"setCommentID",value:function(e,t){var n=this;this.logger&&this.logger.info("setCommentID: oldID "+e+" newID "+t),e&&t&&(this.main.data.some((function(n){return n.id===e&&(n.id=t,!0)})),this.main.queue.some((function(o){return o.id===e&&(o.id=t,o.pauseMove(),"paused"!==n.main.status&&o.startMove(),!0)})))}},{key:"setCommentDuration",value:function(e,t){var n=this;this.logger&&this.logger.info("setCommentDuration: id "+e+" duration "+t),e&&t&&(t=t||5e3,this.main.data.some((function(n){return n.id===e&&(n.duration=t,!0)})),this.main.queue.some((function(o){return o.id===e&&(o.duration=t,o.pauseMove(),"paused"!==n.main.status&&o.startMove(),!0)})))}},{key:"setCommentLike",value:function(e,t){this.logger&&this.logger.info("setCommentLike: id "+e+" like "+t),e&&t&&(this.main.data.some((function(n){return n.id===e&&(n.like=t,!0)})),this.main.queue.some((function(n){return n.id===e&&(n.pauseMove(),n.setLikeDom(t.el,t.style),"paused"!==n.danmu.main.status&&n.startMove(),!0)})))}},{key:"restartComment",value:function(e){if(this.logger&&this.logger.info("restartComment: id "+e),e){var t=this.main;if(this._releaseCtrl(e),"closed"===t.status)return;t.queue.some((function(n){return n.id===e&&("paused"!==t.status?n.startMove(!0):n.status="paused",!0)}))}}},{key:"_releaseCtrl",value:function(e){this.freezeId&&e===this.freezeId&&(this.mouseControl=!1,this.freezeId=null)}},{key:"_freezeCtrl",value:function(e){this.mouseControl=!0,this.freezeId=e}},{key:"freezeComment",value:function(e){this.logger&&this.logger.info("freezeComment: id "+e),e&&(this._freezeCtrl(e),this.main.queue.some((function(t){return t.id===e&&(t.status="forcedPause",t.pauseMove(),t.el&&t.el.style&&(0,u.styleUtil)(t.el,"zIndex",10),!0)})))}},{key:"removeComment",value:function(e){if(this.logger&&this.logger.info("removeComment: id "+e),e){var t=this;t._releaseCtrl(e),t.main.queue.some((function(t){return t.id===e&&(t.remove(),!0)})),t.main.data=t.main.data.filter((function(n){var o=n.id!==e;return o||t.main.dataElHandle([n]),o}))}}},{key:"updateComments",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.logger&&this.logger.info("updateComments: "+e.length+", isClear "+t);var n=this.main;"boolean"==typeof t&&t&&(n.dataElHandle(n.data),n.data=[]),n.data=n.data.concat(e),n.sortData(),n.keepPoolWatermark()}},{key:"setAllDuration",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll",t=this,n=arguments[1],o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];this.logger&&this.logger.info("setAllDuration: mode "+e+" duration "+n+" force "+o),n&&(n=n||5e3,o&&(this.main.forceDuration=n),this.main.data.forEach((function(t){e===t.mode&&(t.duration=n)})),this.main.queue.forEach((function(o){e===o.mode&&(o.duration=n,o.pauseMove(),"paused"!==t.main.status&&o.startMove())})))}},{key:"setPlayRate",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll",n=arguments[1];this.logger&&this.logger.info("setPlayRate: "+n),(0,u.isNumber)(n)&&n>0&&(this.main.playRate=n,this.main.queue.forEach((function(n){t===n.mode&&(n.pauseMove(),"paused"!==e.main.status&&n.startMove())})))}},{key:"setOpacity",value:function(e){this.logger&&this.logger.info("setOpacity: opacity "+e),this.container.style.opacity=e}},{key:"setFontSize",value:function(e,t){var n=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{reflow:!0};this.logger&&this.logger.info("setFontSize: size "+e+" channelSize "+t),this.fontSize=e+"px",e&&(this.main.data.forEach((function(e){e.style&&(e.style.fontSize=n.fontSize)})),this.main.queue.forEach((function(e){e.options.style||(e.options.style={}),e.options.style.fontSize=n.fontSize,e.setFontSize(n.fontSize),t&&(e.top=e.channel_id[0]*t,e.topInit())}))),t&&(this.config.channelSize=t,o.reflow&&this.main.channel.resizeSync())}},{key:"setArea",value:function(e){this.logger&&this.logger.info("setArea: area "+e),this.config.area=e,!1!==e.reflow&&this.main.channel.resizeSync()}},{key:"hide",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll";this.logger&&this.logger.info("hide: mode "+e),this.hideArr.indexOf(e)<0&&this.hideArr.push(e),this.main.queue.filter((function(t){return e===t.mode||"color"===e&&t.color})).forEach((function(e){return e.remove()}))}},{key:"show",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"scroll";this.logger&&this.logger.info("show: mode "+e);var t=this.hideArr.indexOf(e);t>-1&&this.hideArr.splice(t,1)}},{key:"setDirection",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"r2l";this.logger&&this.logger.info("setDirection: direction "+e),this.emit("changeDirection",e)}},{key:"resize",value:function(){this.logger&&this.logger.info("resize"),this.emit("channel_resize")}},{key:"status",get:function(){return this.main.status}},{key:"state",get:function(){var e=this.main;return{status:e.status,comments:e.data,bullets:e.queue,displayArea:e.channel.getRealOccupyArea()}}},{key:"containerPos",get:function(){return this.main.channel.containerPos}}]),t}(s.default);t.default=h},function(e,t,n){var o,r,i,s,l,c,d,u=n(7),p=n(24),f=Function.prototype.apply,h=Function.prototype.call,g=Object.create,m=Object.defineProperty,y=Object.defineProperties,v=Object.prototype.hasOwnProperty,x={configurable:!0,enumerable:!1,writable:!0};r=function(e,t){var n,r;return p(t),r=this,o.call(this,e,n=function(){i.call(r,e,n),f.call(t,this,arguments)}),n.__eeOnceListener__=t,this},l={on:o=function(e,t){var n;return p(t),v.call(this,"__ee__")?n=this.__ee__:(n=x.value=g(null),m(this,"__ee__",x),x.value=null),n[e]?"object"==a(n[e])?n[e].push(t):n[e]=[n[e],t]:n[e]=t,this},once:r,off:i=function(e,t){var n,o,r,i;if(p(t),!v.call(this,"__ee__"))return this;if(!(n=this.__ee__)[e])return this;if("object"==a(o=n[e]))for(i=0;r=o[i];++i)r!==t&&r.__eeOnceListener__!==t||(2===o.length?n[e]=o[i?0:1]:o.splice(i,1));else o!==t&&o.__eeOnceListener__!==t||delete n[e];return this},emit:s=function(e){var t,n,o,r,i;if(v.call(this,"__ee__")&&(r=this.__ee__[e]))if("object"==("undefined"===typeof r?"undefined":a(r))){for(n=arguments.length,i=new Array(n-1),t=1;t<n;++t)i[t-1]=arguments[t];for(r=r.slice(),t=0;o=r[t];++t)f.call(o,this,i)}else switch(arguments.length){case 1:h.call(r,this);break;case 2:h.call(r,this,arguments[1]);break;case 3:h.call(r,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,i=new Array(n-1),t=1;t<n;++t)i[t-1]=arguments[t];f.call(r,this,i)}}},c={on:u(o),once:u(r),off:u(i),emit:u(s)},d=y({},c),e.exports=t=function(e){return null==e?g(d):y(Object(e),c)},t.methods=l},function(e,t,n){var o=n(3),r=n(8),i=n(12),a=n(20),s=n(21);(e.exports=function(e,t){var n,r,l,c,d;return arguments.length<2||"string"!=typeof e?(c=t,t=e,e=null):c=arguments[2],o(e)?(n=s.call(e,"c"),r=s.call(e,"e"),l=s.call(e,"w")):(n=l=!0,r=!1),d={value:t,configurable:n,enumerable:r,writable:l},c?i(a(c),d):d}).gs=function(e,t,n){var l,c,d,u;return"string"!=typeof e?(d=n,n=t,t=e,e=null):d=arguments[3],o(t)?r(t)?o(n)?r(n)||(d=n,n=void 0):n=void 0:(d=t,t=n=void 0):t=void 0,o(e)?(l=s.call(e,"c"),c=s.call(e,"e")):(l=!0,c=!1),u={get:t,set:n,configurable:l,enumerable:c},d?i(a(d),u):u}},function(e,t,n){var o=n(9),r=/^\s*class[\s{/}]/,i=Function.prototype.toString;e.exports=function(e){return!!o(e)&&!r.test(i.call(e))}},function(e,t,n){var o=n(10);e.exports=function(e){if("function"!=typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!=typeof e.length)return!1;if("function"!=typeof e.call)return!1;if("function"!=typeof e.apply)return!1}catch(e){return!1}return!o(e)}},function(e,t,n){var o=n(11);e.exports=function(e){if(!o(e))return!1;try{return!!e.constructor&&e.constructor.prototype===e}catch(e){return!1}}},function(e,t,n){var o=n(3),r={object:!0,function:!0,undefined:!0};e.exports=function(e){return!!o(e)&&hasOwnProperty.call(r,"undefined"===typeof e?"undefined":a(e))}},function(e,t,n){e.exports=n(13)()?Object.assign:n(14)},function(e,t,n){e.exports=function(){var e,t=Object.assign;return"function"==typeof t&&(t(e={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}},function(e,t,n){var o=n(15),r=n(19),i=Math.max;e.exports=function(e,t){var n,a,s,l=i(arguments.length,2);for(e=Object(r(e)),s=function(o){try{e[o]=t[o]}catch(e){n||(n=e)}},a=1;a<l;++a)o(t=arguments[a]).forEach(s);if(void 0!==n)throw n;return e}},function(e,t,n){e.exports=n(16)()?Object.keys:n(17)},function(e,t,n){e.exports=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}},function(e,t,n){var o=n(2),r=Object.keys;e.exports=function(e){return r(o(e)?Object(e):e)}},function(e,t,n){e.exports=function(){}},function(e,t,n){var o=n(2);e.exports=function(e){if(!o(e))throw new TypeError("Cannot use null or undefined");return e}},function(e,t,n){var o=n(2),r=Array.prototype.forEach,i=Object.create;e.exports=function(e){var t=i(null);return r.call(arguments,(function(e){o(e)&&function(e,t){var n;for(n in e)t[n]=e[n]}(Object(e),t)})),t}},function(e,t,n){e.exports=n(22)()?String.prototype.contains:n(23)},function(e,t,n){var o="razdwatrzy";e.exports=function(){return"function"==typeof o.contains&&!0===o.contains("dwa")&&!1===o.contains("foo")}},function(e,t,n){var o=String.prototype.indexOf;e.exports=function(e){return o.call(this,e,arguments[1])>-1}},function(e,t,n){e.exports=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e}},function(e){e.exports=JSON.parse('{"version":"1.1.8"}')},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r="undefined"!=typeof window&&window.location.href.indexOf("danmu-debug")>-1,i=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.constructorName=t||""}return o(e,[{key:"info",value:function(e){for(var t,n=arguments.length,o=Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];r&&(t=console).log.apply(t,["[Danmu Log]["+this.constructorName+"]",e].concat(o))}}]),e}();t.default=i,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=l(n(1)),i=l(n(28)),s=n(0);function l(e){return e&&e.__esModule?e:{default:e}}var c=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.setLogger("control"),n.danmu=e,n.main=new i.default(e),e.config.defaultOff||n.main.start(),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"createSwitch",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.logger&&this.logger.info("createSwitch"),this.switchBtn=(0,s.createDom)("dk-switch",'<span class="txt">\u5f39</span>',{},"danmu-switch "+(e?"danmu-switch-active":"")),this.switchBtn}},{key:"destroy",value:function(){for(var e in this.logger&&this.logger.info("destroy"),this.main.destroy(),this)s.hasOwnProperty.call(this,e)&&delete this[e]}}]),t}(r.default);t.default=c,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=c(n(1)),i=c(n(29)),s=c(n(30)),l=n(0);function c(e){return e&&e.__esModule?e:{default:e}}var d=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.setLogger("main"),n.danmu=e,n.container=e.container,n.channel=new s.default(e),n.data=[].concat(e.config.comments),n.playedData=[],n.queue=[],n.timer=null,n.playRate=1,n.retryStatus="normal",n.interval=e.config.interval,n._status="idle",(0,l.attachEventListener)(e,"bullet_remove",n.updateQueue.bind(n),"destroy"),(0,l.attachEventListener)(e,"changeDirection",(function(e){n.danmu.direction=e}),"destroy"),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"_cancelDataHandleTimer",value:function(){this.handleId&&(clearTimeout(this.handleId),this.handleId=null),this.handleTimer&&(clearTimeout(this.handleTimer),this.handleTimer=null)}},{key:"destroy",value:function(){for(var e in this.logger&&this.logger.info("destroy"),this._cancelDataHandleTimer(),this.channel.destroy(),this.dataElHandle(this.data),this.data=[],this)delete this[e]}},{key:"updateQueue",value:function(e){this.logger&&this.logger.info("updateQueue");var t=this;t.queue.some((function(n,o){return n.id===e.bullet.id&&(t.queue.splice(o,1),!0)})),t.data.some((function(t){return t.id===e.bullet.id&&(t.attached_=!1,!0)}))}},{key:"init",value:function(){var e=this;e.retryStatus="normal",e.sortData(),function t(){"closed"!==e._status||"stop"!==e.retryStatus?("playing"===e._status&&(e.readData(),e.dataHandle()),"stop"===e.retryStatus&&"paused"!==e._status||(e.handleTimer=setTimeout((function(){e.handleId=requestAnimationFrame((function(){t()}))}),250))):e._cancelDataHandleTimer()}()}},{key:"start",value:function(){this.logger&&this.logger.info("start"),"playing"!==this._status&&(this._status="playing",this.queue=[],this.container.innerHTML="",this.channel.reset(),this.init())}},{key:"stop",value:function(){this.logger&&this.logger.info("stop"),"closed"!==this._status&&(this._status="closed",this.retryStatus="stop",this.queue=[],this.container.innerHTML="",this.channel.reset())}},{key:"clear",value:function(){this.logger&&this.logger.info("clear"),this.channel.reset(),this.dataElHandle(this.data),this.data=[],this.queue=[],this.container.innerHTML=""}},{key:"play",value:function(){var e=this;if("closed"!==this._status){this.logger&&this.logger.info("play"),this._status="playing";var t=this.channel.channels;t&&t.length>0&&["scroll","top","bottom"].forEach((function(n){e.queue.forEach((function(e){e.startMove(),e.resized=!0}));for(var o=0;o<t.length;o++)t[o].queue[n].forEach((function(e){e.resized=!1}))}))}else this.logger&&this.logger.info("play ignored")}},{key:"pause",value:function(){if("closed"!==this._status){this.logger&&this.logger.info("pause"),this._status="paused";var e=this.channel.channels;e&&e.length>0&&this.queue.forEach((function(e){e.pauseMove()}))}else this.logger&&this.logger.info("pause ignored")}},{key:"dataHandle",value:function(){"paused"!==this._status&&"closed"!==this._status&&this.queue.length&&this.queue.forEach((function(e){"waiting"===e.status&&e.startMove()}))}},{key:"readData",value:function(){var e=this.danmu,t=this.interval,n=this.channel,o=e.player,r=void 0,a=void 0;if(e.isReady){if(o){var s=o.currentTime?Math.floor(1e3*o.currentTime):0;a=this.data.filter((function(n){return!n.start&&e.hideArr.indexOf(n.mode)<0&&(!n.color||e.hideArr.indexOf("color")<0)&&(n.start=s),!n.attached_&&e.hideArr.indexOf(n.mode)<0&&(!n.color||e.hideArr.indexOf("color")<0)&&n.start-t<=s&&s<=n.start+t})),e.config.highScorePriority&&a.sort((function(e,t){return t.prior&&!e.prior||(t.score||-1)-(e.score||-1)})),e.live&&(this.dataElHandle(this.data),this.data=[])}else 0===(a=this.data.splice(0,1)).length&&(a=this.playedData.splice(0,1));if(a.length>0){n.updatePos();var c=2;e:for(var d,u=0;u<a.length;u++)if(d=a[u],this.forceDuration&&this.forceDuration!==d.duration&&(d.duration=this.forceDuration),(r=new i.default(e,d))&&!r.bulletCreateFail)if(r.attach(),d.attached_=!0,n.addBullet(r).result)this.queue.push(r),r.topInit(),c=2;else{for(var p in r.detach(),r)l.hasOwnProperty.call(r,p)&&delete r[p];if(r=null,d.attached_=!1,d.noDiscard&&(d.prior?this.data.unshift(d):this.data.push(d)),0===c)break e;c--}else{if(0===c)break e;c--}}}}},{key:"sortData",value:function(){this.data.sort((function(e,t){return(e.start||-1)-(t.start||-1)}))}},{key:"keepPoolWatermark",value:function(){var e=this.danmu,t=e.config,n=e.player,o=this.data,r=[],i=0;if("number"==typeof t.maxCommentsLength&&o.length>t.maxCommentsLength){i=o.length-t.maxCommentsLength;for(var a,s=0;s<i;s++)(a=o[s]).prior&&!a.attached_&&r.push(o[s])}else if(t.dropStaleComments&&n&&n.currentTime){var l=Math.floor(1e3*n.currentTime)-t.interval;if(l>0)for(var c,d=0;d<o.length;d++)if((c=o[d]).prior&&!c.attached_&&r.push(o[d]),c.start>l){i=d;break}}i>0&&(this.dataElHandle(o,0,i),o.splice(0,i),this.data=r.concat(o))}},{key:"dataElHandle",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments[2];if(Number.isNaN(n))n=e.length;else if(n>e.length)throw"dataElHandle invalid range: "+t+"-"+n;for(var o=t;o<n;o++){var r=e[o];if(r&&"function"==typeof r.onElDestroy)try{r.onElDestroy(r),r.onElDestroy=null}catch(e){console.error("danmu onElDestroy fail:",e)}}}},{key:"status",get:function(){return this._status}}]),t}(r.default);t.default=d,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Bullet=void 0;var o,r=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=(o=n(1))&&o.__esModule?o:{default:o},s=n(0);function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}var c=t.Bullet=function(e){function t(e,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var o=l(this,(t.__proto__||Object.getPrototypeOf(t)).call(this)),r=o,i=void 0,a="",c=n.style||{};if(o.setLogger("bullet"),o.danmu=e,o.options=n,o.duration=n.duration,o.id=n.id,o.container=e.container,o.start=n.start,o.prior=n.prior,o.realTime=n.realTime,o.color=n.color,o.bookChannelId=n.bookChannelId,o.direction=e.direction,o.reuseDOM=!0,o.domObj=e.domObj,n.el&&1===n.el.nodeType){if(n.el.parentNode)return l(o,{bulletCreateFail:!0});if(e.config.disableCopyDOM||n.disableCopyDOM)o.reuseDOM=!1,i=n.el;else{var d=(0,s.copyDom)(n.el);n.eventListeners&&n.eventListeners.length>0&&n.eventListeners.forEach((function(e){d.addEventListener(e.event,e.listener,e.useCapture||!1)})),(i=o.domObj.use()).childNodes.length>0&&(i.innerHTML=""),i.textContent&&(i.textContent=""),i.appendChild(d)}}else(i=o.domObj.use()).textContent=n.txt;o.onChangeDirection=function(e){r.direction=e},o.danmu.on("changeDirection",o.onChangeDirection);var u=void 0;if((0,s.isNumber)(e.config.bulletOffset)&&e.config.bulletOffset>=0)u=e.config.bulletOffset;else{var p=e.containerPos;u=p.width/10>100?100:p.width/10}var f=n.realTime?0:Math.floor(Math.random()*u),h=o.updateOffset(f,!0);return c.left=h,Object.keys(c).forEach((function(e){a+=e+":"+c[e]+";"})),(0,s.styleCSSText)(i,a),"top"===n.mode||"bottom"===n.mode?o.mode=n.mode:o.mode="scroll",o.el=i,n.like&&n.like.el&&o.setLikeDom(n.like.el,n.like.style),o.status="waiting",o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"updateOffset",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.random=e;var n=this.danmu.containerPos.width+e+"px";return t||(0,s.styleUtil)(this.el,"left",this.danmu.containerPos.width+e+"px"),n}},{key:"attach",value:function(){var e=this.el;this.container.contains(e)||this.container.appendChild(e),this.elPos=e.getBoundingClientRect(),"b2t"===this.direction?(this.width=this.elPos.height,this.height=this.elPos.width):(this.width=this.elPos.width,this.height=this.elPos.height),this.moveV&&(this.duration=(this.danmu.containerPos.width+this.random+this.width)/this.moveV*1e3),this.danmu.config&&(this.danmu.config.mouseControl&&(this.mouseoverFunWrapper=this.mouseoverFun.bind(this),e.addEventListener("mouseover",this.mouseoverFunWrapper,!1)),this.danmu.config.mouseEnterControl&&(this.mouseEnterFunWrapper=this.mouseoverFun.bind(this),e.addEventListener("mouseenter",this.mouseEnterFunWrapper,!1))),this._onTransitionEnd=this._onTransitionEnd.bind(this),e.addEventListener("transitionend",this._onTransitionEnd,!1)}},{key:"detach",value:function(){var e=this.el;if(e){var t=this.danmu.config;t&&(t.mouseControl&&e.removeEventListener("mouseover",this.mouseoverFunWrapper,!1),t.mouseEnterControl&&e.removeEventListener("mouseenter",this.mouseEnterFunWrapper,!1)),e.removeEventListener("transitionend",this._onTransitionEnd,!1),this.reuseDOM?this.domObj.unused(e):e.parentNode&&e.parentNode.removeChild(e),this.el=null}this.elPos=void 0,this.danmu.off("changeDirection",this.onChangeDirection)}},{key:"mouseoverFun",value:function(e){this.danmu&&this.danmu.mouseControl&&this.danmu.config.mouseControlPause||"waiting"===this.status||"end"===this.status||this.danmu&&this.danmu.emit("bullet_hover",{bullet:this,event:e})}},{key:"_onTransitionEnd",value:function(){this.status="end",this.remove(!1)}},{key:"topInit",value:function(){this.logger&&this.logger.info("topInit #"+(this.options.txt||"[DOM Element]")+"#"),"b2t"===this.direction?((0,s.styleUtil)(this.el,"transformOrigin","left top"),(0,s.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY("+this.danmu.containerPos.height+"px) translateZ(0px) rotate(90deg)"),(0,s.styleUtil)(this.el,"transition","transform 0s linear 0s")):(0,s.styleUtil)(this.el,"top",this.top+"px")}},{key:"pauseMove",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this;if("paused"!==t.status&&("forcedPause"!==t.status&&(this.status="paused"),t._moveV=void 0,this.el))if("scroll"===this.mode){var n=t.danmu.containerPos;if(e){var o,r=((new Date).getTime()-t.moveTime)/1e3*this.moveV;o=t.moveMoreS-r>=0?"b2t"===this.direction?(t.moveMoreS-r)/t.moveContainerHeight*n.height:(t.moveMoreS-r)/t.moveContainerWidth*n.width:t.moveMoreS-r,"b2t"===this.direction?(0,s.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY("+o+"px) translateZ(0px) rotate(90deg)"):(0,s.styleUtil)(this.el,"left",o+"px")}else"b2t"===this.direction?(0,s.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY("+(this.el.getBoundingClientRect().top-n.top)+"px) translateZ(0px) rotate(90deg)"):(0,s.styleUtil)(this.el,"left",this.el.getBoundingClientRect().left-n.left+"px");"b2t"===this.direction||(0,s.styleUtil)(this.el,"transform","translateX(0px) translateY(0px) translateZ(0px)"),(0,s.styleUtil)(this.el,"transition","transform 0s linear 0s")}else this.pastDuration&&this.startTime?this.pastDuration=this.pastDuration+(new Date).getTime()-this.startTime:this.pastDuration=1}},{key:"startMove",value:function(e){if(this.hasMove||(this.danmu.emit("bullet_start",this),this.hasMove=!0),("forcedPause"!==this.status||e)&&this.el&&"start"!==this.status)if(this.status="start",(0,s.styleUtil)(this.el,"backface-visibility","hidden"),(0,s.styleUtil)(this.el,"perspective","500em"),"scroll"===this.mode){var t=this.danmu.containerPos;if("b2t"===this.direction){var n=(this.el.getBoundingClientRect().bottom-t.top)/this.moveV;(0,s.styleUtil)(this.el,"transition","transform "+n+"s linear 0s"),(0,s.styleUtil)(this.el,"transform","translateX(-"+this.top+"px) translateY(-"+this.height+"px) translateZ(0px) rotate(90deg)"),this.moveTime=(new Date).getTime(),this.moveMoreS=this.el.getBoundingClientRect().top-t.top,this.moveContainerHeight=t.height}else{if(!this.el)return;var o=this.el.getBoundingClientRect(),r=o.right-t.left,i=r/this.moveV;o.right>t.left?((0,s.styleUtil)(this.el,"transition","transform "+i+"s linear 0s"),(0,s.styleUtil)(this.el,"transform","translateX(-"+r+"px) translateY(0px) translateZ(0px)"),this.moveTime=(new Date).getTime(),this.moveMoreS=o.left-t.left,this.moveContainerWidth=t.width):(this.status="end",this.remove())}}else{var a=(new Date).getTime(),l=(this.startTime&&a-this.startTime>this.duration?a-this.startTime:this.duration)/1e3;(0,s.styleUtil)(this.el,"left","50%"),(0,s.styleUtil)(this.el,"margin","0 0 0 -"+this.width/2+"px"),(0,s.styleUtil)(this.el,"visibility","hidden"),(0,s.styleUtil)(this.el,"transition","visibility "+l+"s 0s"),this.pastDuration||(this.pastDuration=1),this.startTime=a}}},{key:"remove",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.logger&&this.logger.info("remove #"+(this.options.txt||"[DOM Element]")+"#");var t=this;e&&t.pauseMove(),t.el&&t.el.parentNode&&(t.detach(),this.options.el&&1===this.options.el.nodeType&&this.danmu.config.disableCopyDOM&&(0,s.styleUtil)(this.options.el,"transform","none"),t.danmu.emit("bullet_remove",{bullet:t}))}},{key:"setFontSize",value:function(e){this.el&&(this.el.style.fontSize=e)}},{key:"setLikeDom",value:function(e,t){if(e&&(Object.keys(t).forEach((function(n){e.style[n]=t[n]})),e.className="danmu-like",this.el)){var n=this.el.querySelector(".danmu-like");n&&this.el.removeChild(n),this.el.innerHTML=""+this.el.innerHTML+e.outerHTML}return e}},{key:"moveV",get:function(){var e=this._moveV;if(!e){if(this.options.moveV)e=this.options.moveV;else if(this.elPos){var t=this.danmu.containerPos;e=("b2t"===this.direction?t.height+this.height:t.width+this.width)/this.duration*1e3}e&&(e*=this.danmu.main.playRate,this._moveV=e)}return e}}]),t}(i.default);t.default=c},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o,r=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=(o=n(1))&&o.__esModule?o:{default:o},s=n(0),l=n(31),c=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=("undefined"===typeof t?"undefined":a(t))&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this)),o=n;return o.setLogger("channel"),o.danmu=e,o.width=0,o.height=0,o.reset(!0),o.direction=e.direction,o.channels=[],o.updatePos(),(0,s.attachEventListener)(n.danmu,"bullet_remove",(function(e){o.removeBullet(e.bullet)}),"destroy"),(0,s.attachEventListener)(n.danmu,"changeDirection",(function(e){o.direction=e}),"destroy"),(0,s.attachEventListener)(n.danmu,"channel_resize",(function(){o.resize()}),"destroy"),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"===typeof t?"undefined":a(t)));e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"destroy",value:function(){for(var e in this.logger&&this.logger.info("destroy"),this.channels.splice(0,this.channels.length),this._cancelResizeTimer(),this)s.hasOwnProperty.call(this,e)&&delete this[e]}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.logger&&this.logger.info("reset");var t=this,n=t.danmu,o=n.container,r=n.bulletBtn;function i(){var e=o.getBoundingClientRect();t.width=e.width,t.height=e.height,t.resetId&&(cancelAnimationFrame(t.resetId),t.resetId=null);var n=t._initChannels(),r=n.channelSize,i=n.channelCount,a=n.channels;t.channelCount=i,t.channels=a,"b2t"===t.direction?t.channelWidth=r:t.channelHeight=r}t.container=o,r&&r.main&&r.main.queue.forEach((function(e){e.remove()})),t.channels&&t.channels.length>0&&["scroll","top","bottom"].forEach((function(e){for(var n=0;n<t.channels.length;n++)t.channels[n].queue[e].forEach((function(e){e.remove()}))})),r&&r.main&&r.main.data&&r.main.data.forEach((function(e){e.attached_=!1})),e?this.resetId=requestAnimationFrame(i):i()}},{key:"getRealOccupyArea",value:function(){return{width:this.width,height:this.height}}},{key:"updatePos",value:function(){var e=this.container.getBoundingClientRect();this.containerPos=e,this.containerWidth=e.width,this.containerHeight=e.height,this.containerTop=e.top,this.containerBottom=e.bottom,this.containerLeft=e.left,this.containerRight=e.right}},{key:"addBullet",value:function(e){var t=this,n=this.danmu,o=this.channels,r=void 0,i=void 0,a=void 0;if("b2t"===t.direction?(i=this.channelWidth,a=Math.ceil(e.width/i)):(r=this.channelHeight,a=Math.ceil(e.height/r)),a>o.length)return{result:!1,message:"exceed channels.length, occupy="+a+",channelsSize="+o.length};for(var s=!0,l=void 0,c=-1,d=0,u=o.length;d<u;d++)if(o[d].queue[e.mode].some((function(t){return t.id===e.id})))return{result:!1,message:"exited, channelOrder="+d+",danmu_id="+e.id};if("scroll"===e.mode)for(var p=0,f=o.length-a;p<=f;p++){s=!0;for(var h=p;h<p+a;h++){if((l=o[h]).operating.scroll){s=!1;break}if(l.bookId.scroll&&l.bookId.scroll!==e.id){s=!1;break}l.operating.scroll=!0;var g=l.queue.scroll[0];if(g){var m=g.el.getBoundingClientRect();if("b2t"===t.direction){if(m.bottom>=t.containerPos.bottom){s=!1,l.operating.scroll=!1;break}}else if(m.right>=t.containerPos.right){s=!1,l.operating.scroll=!1;break}var y=void 0,v=g.moveV,x=void 0,b=e.moveV,_=void 0;if("b2t"===t.direction?(x=(y=m.bottom-t.containerTop)/v,_=t.containerHeight+e.random-y):(x=(y=m.right-t.containerLeft)/v,_=t.containerWidth+e.random-y),b>v){var k=_/(b-v);if(n.config.bOffset||(n.config.bOffset=0),x+n.config.bOffset>=k){var w=x*b-t.containerPos.width;w>0&&e.updateOffset(w+(1+Math.ceil(5*Math.random())))}}}l.operating.scroll=!1}if(s){c=p;break}}else if("top"===e.mode)for(var C=0,S=o.length-a;C<=S;C++){s=!0;for(var E=C;E<C+a;E++){if(E>Math.floor(o.length/2)){s=!1;break}if((l=o[E]).operating[e.mode]){s=!1;break}if((l.bookId[e.mode]||e.prior)&&l.bookId[e.mode]!==e.id){s=!1;break}if(l.operating[e.mode]=!0,l.queue[e.mode].length>0){s=!1,l.operating[e.mode]=!1;break}l.operating[e.mode]=!1}if(s){c=C;break}}else if("bottom"===e.mode)for(var T=o.length-a;T>=0;T--){s=!0;for(var D=T;D<T+a;D++){if(D<=Math.floor(o.length/2)){s=!1;break}if((l=o[D]).operating[e.mode]){s=!1;break}if((l.bookId[e.mode]||e.prior)&&l.bookId[e.mode]!==e.id){s=!1;break}if(l.operating[e.mode]=!0,l.queue[e.mode].length>0){s=!1,l.operating[e.mode]=!1;break}l.operating[e.mode]=!1}if(s){c=T;break}}if(-1!==c){for(var R=c,P=c+a;R<P;R++)(l=o[R]).operating[e.mode]=!0,l.queue[e.mode].unshift(e),e.prior&&(delete l.bookId[e.mode],t.logger&&t.logger.info(R+"\u53f7\u8f68\u9053\u6062\u590d\u6b63\u5e38\u4f7f\u7528")),l.operating[e.mode]=!1;return e.prior&&(t.logger&&t.logger.info(e.id+"\u53f7\u4f18\u5148\u5f39\u5e55\u8fd0\u884c\u5b8c\u6bd5"),delete e.bookChannelId,n.player&&n.bulletBtn.main.data.some((function(t){return t.id===e.id&&(delete t.bookChannelId,!0)}))),e.channel_id=[c,a],e.el.setAttribute("data-line-index",c+1),"b2t"===t.direction?(e.top=c*i,t.danmu.config.area&&t.danmu.config.area.start&&(e.top+=t.containerWidth*t.danmu.config.area.start)):(e.top=c*r,t.danmu.config.area&&t.danmu.config.area.start&&(e.top+=t.containerHeight*t.danmu.config.area.start)),{result:e,message:"success"}}if(e.options.realTime){var I=0,M=-1,O=null;if(t.danmu.bulletBtn.main.queue.forEach((function(e,n){!e.prior&&!e.options.realTime&&e.el&&e.el.getBoundingClientRect().left>t.containerPos.right&&e.start>=I&&(I=e.start,M=n,O=e)})),O){O.remove(),t.removeBullet(O),t.danmu.bulletBtn.main.queue.splice(M,1),e.channel_id=O.channel_id;for(var L=O.channel_id[0],A=O.channel_id[0]+O.channel_id[1];L<A;L++)(l=o[L]).operating[e.mode]=!0,l.queue[e.mode].unshift(e),e.prior&&delete l.bookId[e.mode],l.operating[e.mode]=!1;return e.top=O.top,t.danmu.config.area&&t.danmu.config.area.start&&(e.top+=t.containerHeight*t.danmu.config.area.start),{result:e,message:"success"}}}if(e.prior)if(e.bookChannelId||t.danmu.live)n.player&&n.bulletBtn.main.data.some((function(n){return n.id===e.id&&(t.logger&&t.logger.info(e.id+"\u53f7\u4f18\u5148\u5f39\u5e55\u5c06\u4e8e2\u79d2\u540e\u518d\u6b21\u8bf7\u6c42\u6ce8\u518c"),n.start+=2e3,!0)}));else{c=-1;for(var z=0,N=o.length-a;z<=N;z++){s=!0;for(var H=z;H<z+a;H++)if(o[H].bookId[e.mode]){s=!1;break}if(s){c=z;break}}if(-1!==c){for(var F=c;F<c+a;F++)o[F].bookId[e.mode]=e.id,t.logger&&t.logger.info(F+"\u53f7\u8f68\u9053\u88ab"+e.id+"\u53f7\u4f18\u5148\u5f39\u5e55\u9884\u5b9a");n.player&&n.bulletBtn.main.data.some((function(n){return n.id===e.id&&(t.logger&&t.logger.info(e.id+"\u53f7\u4f18\u5148\u5f39\u5e55\u5c06\u4e8e2\u79d2\u540e\u518d\u6b21\u8bf7\u6c42\u6ce8\u518c"),n.start+=2e3,n.bookChannelId=[c,a],t.logger&&t.logger.info(e.id+"\u53f7\u4f18\u5148\u5f39\u5e55\u9884\u5b9a\u4e86"+c+"~"+(c+a-1)+"\u53f7\u8f68\u9053"),!0)}))}}return{result:!1,message:"no surplus will right"}}},{key:"removeBullet",value:function(e){this.logger&&this.logger.info("removeBullet "+(e.options.txt||"[DOM Element]"));for(var t=this.channels,n=e.channel_id,o=void 0,r=n[0],i=n[0]+n[1];r<i;r++)if(o=t[r]){o.operating[e.mode]=!0;var a=-1;o.queue[e.mode].some((function(t,n){return t.id===e.id&&(a=n,!0)})),a>-1&&o.queue[e.mode].splice(a,1),o.operating[e.mode]=!1}e.options.loop&&this.danmu.bulletBtn.main.playedData.push(e.options)}},{key:"resizeSync",value:function(){this.resize(!0)}},{key:"_initChannels",value:function(){var e=this.danmu.config,t=e.channelSize||(/mobile/gi.test(navigator.userAgent)?10:12),n=void 0;if(e.area){var o=e.area,r=o.lines,i=o.start,a=o.end;if((0,l.validAreaLineRule)(r))n=r,"b2t"===this.direction?this.width=n*t:this.height=n*t;else if(i>=0&&a>=i){var c=a-i;"b2t"===this.direction?this.width=Math.floor(this.width*c):this.height=Math.floor(this.height*c)}}(0,s.isNumber)(n)||(n="b2t"===this.direction?Math.floor(this.width/t):Math.floor(this.height/t));for(var d=[],u=0;u<n;u++)d[u]={id:u,queue:{scroll:[],top:[],bottom:[]},operating:{scroll:!1,top:!1,bottom:!1},bookId:{}};return{channelSize:t,channelCount:n,channels:d}}},{key:"resize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.logger&&this.logger.info("resize");var t=this;function n(e,t){e[t]={id:t,queue:{scroll:[],top:[],bottom:[]},operating:{scroll:!1,top:!1,bottom:!1},bookId:{}}}function o(e,o){function r(n){["scroll","top"].forEach((function(o){t.channels[n].queue[o].forEach((function(t){t.el&&e[n].queue[o].push(t)}))}))}function i(n){t.channels[n].queue.bottom.forEach((function(r){if(r.el&&(e[n+e.length-t.channels.length].queue.bottom.push(r),r.channel_id[0]+r.channel_id[1]-1===n)){var i=[].concat(r.channel_id);r.channel_id=[i[0]-t.channels.length+e.length,i[1]],r.top=r.channel_id[0]*o,t.danmu.config.area&&t.danmu.config.area.start&&(r.top+=t.containerHeight*t.danmu.config.area.start),r.topInit()}}))}for(var a=0;a<t.channels.length;a++)n(e,a),r(a),i(a);for(var s=function(t){["scroll","top","bottom"].forEach((function(n){e[t].queue[n].forEach((function(e){e.resized=!1}))}))},l=0;l<e.length;l++)s(l);t.channels=e,"b2t"===t.direction?t.channelWidth=o:t.channelHeight=o}function r(e,o){for(var r=["scroll","top","bottom"],i=function(i){n(e,i),r.forEach((function(n){if("top"===n&&i>Math.floor(e.length/2));else if("bottom"===n&&i<=Math.floor(e.length/2));else{var r="bottom"===n?i-e.length+t.channels.length:i;t.channels[r].queue[n].forEach((function(a,s){if(a.el){if(e[i].queue[n].push(a),"bottom"===n&&a.channel_id[0]+a.channel_id[1]-1===r){var l=[].concat(a.channel_id);a.channel_id=[l[0]-t.channels.length+e.length,l[1]],a.top=a.channel_id[0]*o,t.danmu.config.area&&t.danmu.config.area.start&&(a.top+=t.containerHeight*t.danmu.config.area.start),a.topInit()}t.channels[r].queue[n].splice(s,1)}}))}}))},a=0;a<e.length;a++)i(a);var s=function(t){r.forEach((function(n){e[t].queue[n].forEach((function(e){e.resized=!1}))}))};for(a=0;a<e.length;a++)s(a);t.channels=e,"b2t"===t.direction?t.channelWidth=o:t.channelHeight=o}function i(){var e=t.danmu,n=e.container,i=e.bulletBtn;t.container=n,t.updatePos(),t._cancelResizeTimer(),i.main.data&&i.main.data.forEach((function(e){e.bookChannelId&&(delete e.bookChannelId,t.logger&&t.logger.info("resize\u5bfc\u81f4"+e.id+"\u53f7\u4f18\u5148\u5f39\u5e55\u9884\u5b9a\u53d6\u6d88"))})),t.logger&&t.logger.info("resize\u5bfc\u81f4\u6240\u6709\u8f68\u9053\u6062\u590d\u6b63\u5e38\u4f7f\u7528"),t.width=t.containerWidth,t.height=t.containerHeight;var a=t._initChannels(),s=a.channelSize,l=a.channels;t.channels&&(t.channels.length<=l.length?o(l,s):r(l,s)),t.resizing=!1}t.resizing||(t.resizing=!0,e?i():(this._cancelResizeTimer(),this.resizeId=requestAnimationFrame(i)))}},{key:"_cancelResizeTimer",value:function(){this.resizeId&&(cancelAnimationFrame(this.resizeId),this.resizeId=null)}}]),t}(i.default);t.default=c,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.validAreaLineRule=function(e){return"number"==typeof e&&e>=0&&Number.isInteger(e)}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=n(0),i=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t={initDOM:function(){return document.createElement("div")},initSize:10},this.init(t)}return o(e,[{key:"init",value:function(e){this.idleList=[],this.usingList=[],this._id=0,this.options=e,this._expand(e.initSize)}},{key:"use",value:function(){this.idleList.length||this._expand(1);var e=this.idleList.shift();return this.usingList.push(e),e}},{key:"unused",value:function(e){var t=this.usingList.indexOf(e);t<0||(this.usingList.splice(t,1),e.style.opacity=0,this.idleList.push(e))}},{key:"_expand",value:function(e){for(var t=0;t<e;t++)this.idleList.push(this.options.initDOM(this._id++))}},{key:"destroy",value:function(){for(var e=0;e<this.idleList.length;e++)this.idleList[e].innerHTML="",this.idleList[e].textcontent="",this.clearElementStyle(this.idleList[e]);for(var t=0;t<this.usingList.length;t++)this.usingList[t].innerHTML="",this.usingList[t].textcontent="",this.clearElementStyle(this.usingList[t]);for(var n in this)r.hasOwnProperty.call(this,n)&&delete this[n]}},{key:"clearElementStyle",value:function(e){var t="undefined"!=typeof window?window.navigator.userAgent:null;t&&(t.indexOf("MSIE ")>-1||t.indexOf("Trident/")>-1?(0,r.styleUtil)(e,"transform","none"):e.setAttribute("style",""))}}]),e}();t.default=i,e.exports=t.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.destroyObserver=t.unObserver=t.addObserver=void 0;var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),r=n(0),i=new(function(){function e(){var t=this;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.__handlers=[],window.ResizeObserver)try{this.observer=new window.ResizeObserver((0,r.throttle)((function(e){t.__trigger(e)}),100))}catch(e){}}return o(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer&&this.observer.observe(e);for(var n=this.__handlers,o=-1,r=0;r<n.length;r++)n[r]&&e===n[r].target&&(o=r);o>-1?this.__handlers[o].handler.push(t):this.__handlers.push({target:e,handler:[t]})}}},{key:"unObserver",value:function(e){var t=-1;this.__handlers.map((function(n,o){e===n.target&&(t=o)})),this.observer&&this.observer.unobserve(e),t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){this.observer&&this.observer.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e){for(var t=this.__handlers,n=0;n<t.length;n++)if(t[n]&&e===t[n].target){t[n].handler&&t[n].handler.map((function(e){try{e()}catch(e){console.error(e)}}));break}}},{key:"__trigger",value:function(e){var t=this;e.map((function(e){t.__runHandler(e.target)}))}}]),e}());t.addObserver=function(e,t){i.addObserver(e,t)},t.unObserver=function(e,t){i.unObserver(e,t)},t.destroyObserver=function(e,t){i.destroyObserver(e,t)}},function(e,t,n){var o=n(35);"string"==typeof o&&(o=[[e.i,o,""]]),n(37)(o,{hmr:!0,transform:void 0,insertInto:void 0}),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(36)(!1)).push([e.i,".danmu{overflow:hidden;-webkit-user-select:none;-moz-user-select:none;user-select:none;-ms-user-select:none}.danmu>*{position:absolute;white-space:nowrap}.danmu-switch{width:32px;height:20px;border-radius:100px;background-color:#ccc;-webkit-box-sizing:border-box;box-sizing:border-box;outline:none;cursor:pointer;position:relative;text-align:center;margin:10px auto}.danmu-switch.danmu-switch-active{padding-left:12px;background-color:#f85959}.danmu-switch span.txt{width:20px;height:20px;line-height:20px;text-align:center;display:block;border-radius:100px;background-color:#fff;-webkit-box-shadow:-2px 0 0 0 rgba(0, 0, 0, .04);box-shadow:-2px 0 0 0 rgba(0, 0, 0, .04);font-family:PingFangSC;font-size:10px;font-weight:500;color:#f44336}",""])},function(e,t){e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n,o=e[1]||"",r=e[3];if(!r)return o;if(t&&"function"==typeof btoa){var i=(n=r,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */"),a=r.sources.map((function(e){return"/*# sourceURL="+r.sourceRoot+e+" */"}));return[o].concat(a).concat([i]).join("\n")}return[o].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n})).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var o={},r=0;r<this.length;r++){var i=this[r][0];"number"==typeof i&&(o[i]=!0)}for(r=0;r<e.length;r++){var a=e[r];"number"==typeof a[0]&&o[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),t.push(a))}},t}},function(e,t,n){var o,r,i={},s=(o=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===r&&(r=o.apply(this,arguments)),r}),l=function(e){return document.querySelector(e)},c=function(e){var t={};return function(e){if("function"==typeof e)return e();if(void 0===t[e]){var n=l.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}}(),d=null,u=0,p=[],f=n(38);function h(e,t){for(var n=0;n<e.length;n++){var o=e[n],r=i[o.id];if(r){r.refs++;for(var a=0;a<r.parts.length;a++)r.parts[a](o.parts[a]);for(;a<o.parts.length;a++)r.parts.push(b(o.parts[a],t))}else{var s=[];for(a=0;a<o.parts.length;a++)s.push(b(o.parts[a],t));i[o.id]={id:o.id,refs:1,parts:s}}}}function g(e,t){for(var n=[],o={},r=0;r<e.length;r++){var i=e[r],a=t.base?i[0]+t.base:i[0],s={css:i[1],media:i[2],sourceMap:i[3]};o[a]?o[a].parts.push(s):n.push(o[a]={id:a,parts:[s]})}return n}function m(e,t){var n=c(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var o=p[p.length-1];if("top"===e.insertAt)o?o.nextSibling?n.insertBefore(t,o.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),p.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=a(e.insertAt)||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var r=c(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,r)}}function y(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=p.indexOf(e);t>=0&&p.splice(t,1)}function v(e){var t=document.createElement("style");return void 0===e.attrs.type&&(e.attrs.type="text/css"),x(t,e.attrs),m(e,t),t}function x(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}function b(e,t){var n,o,r,i;if(t.transform&&e.css){if(!(i=t.transform(e.css)))return function(){};e.css=i}if(t.singleton){var a=u++;n=d||(d=v(t)),o=w.bind(null,n,a,!1),r=w.bind(null,n,a,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(e){var t=document.createElement("link");return void 0===e.attrs.type&&(e.attrs.type="text/css"),e.attrs.rel="stylesheet",x(t,e.attrs),m(e,t),t}(t),o=S.bind(null,n,t),r=function(){y(n),n.href&&URL.revokeObjectURL(n.href)}):(n=v(t),o=C.bind(null,n),r=function(){y(n)});return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else r()}}e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=("undefined"===typeof document?"undefined":a(document)))throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"==a(t.attrs)?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=s()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=g(e,t);return h(n,t),function(e){for(var o=[],r=0;r<n.length;r++){var a=n[r];(s=i[a.id]).refs--,o.push(s)}for(e&&h(g(e,t),t),r=0;r<o.length;r++){var s;if(0===(s=o[r]).refs){for(var l=0;l<s.parts.length;l++)s.parts[l]();delete i[s.id]}}}};var _,k=(_=[],function(e,t){return _[e]=t,_.filter(Boolean).join("\n")});function w(e,t,n,o){var r=n?"":o.css;if(e.styleSheet)e.styleSheet.cssText=k(t,r);else{var i=document.createTextNode(r),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function C(e,t){var n=t.css,o=t.media;if(o&&e.setAttribute("media",o),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function S(e,t,n){var o=n.css,r=n.sourceMap,i=void 0===t.convertToAbsoluteUrls&&r;(t.convertToAbsoluteUrls||i)&&(o=f(o)),r&&(o+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */");var a=new Blob([o],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(a),s&&URL.revokeObjectURL(s)}},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,o=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(e,t){var r,i=t.trim().replace(/^"(.*)"$/,(function(e,t){return t})).replace(/^'(.*)'$/,(function(e,t){return t}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(i)?e:(r=0===i.indexOf("//")?i:0===i.indexOf("/")?n+i:o+i.replace(/^\.\//,""),"url("+JSON.stringify(r)+")")}))}}])},"object"==a(t)&&"object"==a(e)?e.exports=i():(o=[],void 0===(r="function"===typeof(n=i)?n.apply(t,o):n)||(e.exports=r))}).call(this,n(144)(e))},function(e,t,n){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">\n  <path fill="#f85959" transform="scale(0.8 0.8)" d="M36.5,18.73a1.19,1.19,0,0,0,1-1.14V16.33a1.2,1.2,0,0,0-1-1.13l-.61-.08a1.75,1.75,0,0,1-1.3-.86l-.21-.36-.2-.36A1.72,1.72,0,0,1,34,12l.23-.58a1.18,1.18,0,0,0-.5-1.42l-1.1-.62a1.18,1.18,0,0,0-1.47.3l-.39.51a1.82,1.82,0,0,1-1.41.72c-.44,0-1.88-.27-2.22-.7l-.39-.49a1.18,1.18,0,0,0-1.48-.28l-1.09.64a1.19,1.19,0,0,0-.47,1.43l.25.59a1.87,1.87,0,0,1-.08,1.58c-.26.37-1.17,1.5-1.71,1.58l-.63.09a1.19,1.19,0,0,0-1,1.14l0,1.27a1.17,1.17,0,0,0,1,1.12l.61.08a1.74,1.74,0,0,1,1.3.87l.21.36.2.35A1.69,1.69,0,0,1,24,22.08l-.23.59a1.19,1.19,0,0,0,.5,1.42l1.1.62a1.19,1.19,0,0,0,1.48-.31l.38-.5a1.83,1.83,0,0,1,1.41-.72c.44,0,1.88.25,2.22.69l.39.49a1.18,1.18,0,0,0,1.48.28L33.86,24a1.19,1.19,0,0,0,.47-1.43L34.09,22a1.84,1.84,0,0,1,.07-1.58c.26-.37,1.17-1.5,1.72-1.58ZM31,18.94a2.76,2.76,0,0,1-4.65-1.2A2.71,2.71,0,0,1,27,15.13a2.76,2.76,0,0,1,4.64,1.2A2.7,2.7,0,0,1,31,18.94Z"/>\n  <path fill="#f85959" transform="scale(0.8 0.8)" d="M32,0H3.59A3.59,3.59,0,0,0,0,3.59v17A3.59,3.59,0,0,0,3.59,24.2H19.72a12.59,12.59,0,0,1-.81-1.2A11.73,11.73,0,0,1,35.54,7.28V3.59A3.59,3.59,0,0,0,32,0ZM13,14.18H4.29a1.52,1.52,0,0,1,0-3H13a1.52,1.52,0,0,1,0,3ZM16.45,8H4.29a1.51,1.51,0,0,1,0-3H16.45a1.51,1.51,0,1,1,0,3Z"/>\n</svg>\n'},function(e,t,n){var o=n(147);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .danmu-switch{-webkit-order:6;-moz-box-ordinal-group:7;order:6;z-index:26}.xgplayer-skin-default .xgplayer-danmu{display:none;position:absolute;top:0;left:0;right:0;height:100%;overflow:hidden;z-index:9;outline:none;pointer-events:none}.xgplayer-skin-default .xgplayer-danmu>*{position:absolute;white-space:nowrap;z-index:9;pointer-events:auto}.xgplayer-skin-default .xgplayer-danmu.xgplayer-has-danmu{display:block}.xgplayer-skin-default .xgplayer-panel{outline:none;-webkit-order:7;-moz-box-ordinal-group:8;order:7;width:40px;height:40px;display:inline-block;position:relative;font-family:PingFangSC-Regular;font-size:13px;color:hsla(0,0%,100%,.8);z-index:36}.xgplayer-skin-default .xgplayer-panel .xgplayer-panel-icon{cursor:pointer;position:absolute;margin-left:5px;top:10px}.xgplayer-skin-default .xgplayer-panel-active{display:block!important;bottom:30px}.xgplayer-skin-default .xgplayer-panel-slider{z-index:36;display:none;position:absolute;width:230px;height:230px;background:rgba(0,0,0,.54);border-radius:1px;padding:10px 20px;outline:none;left:-115px;bottom:40px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode{padding-bottom:10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode-radio li{display:inline;list-style:none;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode ul{display:-webkit-flex;display:-moz-box;display:flex;-webkit-justify-content:space-around;justify-content:space-around}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode li{margin:0 12px;font-size:11px;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-hidemode-font{margin-bottom:10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency{display:block;margin-top:10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-transparency .xgplayer-transparency-bar::-moz-range-progress{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;height:4px;border-radius:4px;background:linear-gradient(90deg,#f85959,#f85959 100%,#aaa)}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea{display:block;margin-top:8px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-name{display:inline-block;position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control{display:inline-block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control-up{width:150px;margin-left:10px;display:-moz-box;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control-down{position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-control-down-dots{display:-webkit-flex;display:-moz-box;display:flex;width:150px;margin-left:10px;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-threequarters,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-twoquarters{margin-left:-6px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea-full{margin-right:3px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-full-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-onequarters-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-threequarters-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-twoquarters-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-showarea .xgplayer-showarea-zero-dot{width:3px;height:3px;border:3px solid #aaa;border-radius:50%;background-color:#aaa;position:relative;top:16px;z-index:-1}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed{display:block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-name{display:inline-block;position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control{display:inline-block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control-up{width:150px;margin-left:10px;display:-moz-box;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control-down{position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed-control-down-dots{display:-webkit-flex;display:-moz-box;display:flex;width:150px;margin-left:10px;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-large-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-middle-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmuspeed .xgplayer-danmuspeed-small-dot{width:3px;height:3px;border:3px solid #aaa;border-radius:50%;background-color:#aaa;position:relative;top:16px;z-index:-1}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont{display:block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-name{display:inline-block;position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control{display:inline-block}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control-up{width:150px;margin-left:10px;display:-moz-box;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between;color:#aaa}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control-down{position:relative;top:-10px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont-control-down-dots{display:-webkit-flex;display:-moz-box;display:flex;width:150px;margin-left:10px;-webkit-justify-content:space-between;-moz-box-pack:justify;justify-content:space-between}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-line{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;outline:none;width:150px;height:4px;background:#aaa;border-radius:4px;border-style:none;margin-left:10px;margin-top:-2px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-line::-moz-focus-outer{border:0!important}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-color::-webkit-slider-runnable-track{outline:none;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-color::-moz-range-track{outline:none;background-color:#aaa;border-color:transparent;cursor:pointer;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-color::-ms-track{outline:none;background-color:#aaa;color:transparent;border-color:transparent;width:150px;height:4px;border-radius:4px}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-bar::-webkit-slider-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;margin-top:-4px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-bar::-moz-range-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:0;width:0;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-bar::-ms-thumb{outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:6px solid #f85959;height:6px;width:6px;border-radius:6px;cursor:pointer}.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-large-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-middle-dot,.xgplayer-skin-default .xgplayer-panel-slider .xgplayer-danmufont .xgplayer-danmufont-small-dot{width:3px;height:3px;border:3px solid #aaa;border-radius:50%;background-color:#aaa;position:relative;top:16px;z-index:-1}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(149),t.default={name:"s_pip",method:function(){var e=this;if(e.config.pip&&"function"===typeof e.video.requestPictureInPicture){var t=e.lang.PIP,n=(0,o.createDom)("xg-pip",'<p class="name"><span>'+t+"</span></p>",{tabindex:9},"xgplayer-pip");e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("pipBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){var o=n(150);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-pip{-webkit-order:9;-moz-box-ordinal-group:10;order:9;position:relative;outline:none;display:block;cursor:pointer;height:20px;top:10px}.xgplayer-skin-default .xgplayer-pip .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;line-height:20px;height:20px;color:hsla(0,0%,100%,.8)}.xgplayer-skin-default .xgplayer-pip .name span{width:60px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.lang-is-jp .xgplayer-pip .name span{width:70px;height:20px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(152),t.default={name:"s_miniplayer",method:function(){var e=this;if(e.config.miniplayer){var t=e.lang.MINIPLAYER,n=(0,o.createDom)("xg-miniplayer",'<p class="name"><span>'+t+"</span></p>",{tabindex:9},"xgplayer-miniplayer");e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("miniplayerBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){var o=n(153);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-miniplayer{-webkit-order:9;-moz-box-ordinal-group:10;order:9;position:relative;outline:none;display:block;cursor:pointer;height:20px;top:10px}.xgplayer-skin-default .xgplayer-miniplayer .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;line-height:20px;height:20px;color:hsla(0,0%,100%,.8)}.xgplayer-skin-default .xgplayer-miniplayer .name span{width:80px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-skin-default .xgplayer-miniplayer-lay{position:absolute;top:26px;left:0;width:100%;height:100%;z-index:130;cursor:pointer;background-color:transparent;display:none}.xgplayer-skin-default .xgplayer-miniplayer-lay div{width:100%;height:100%}.xgplayer-skin-default .xgplayer-miniplayer-drag{cursor:move;position:absolute;top:0;left:0;width:100%;height:26px;line-height:26px;background-image:linear-gradient(rgba(0,0,0,.3),transparent);z-index:130;display:none}.xgplayer-skin-default .xgplayer-miniplayer-drag .drag-handle{width:100%}.xgplayer-skin-default.xgplayer-miniplayer-active{position:fixed;right:0;bottom:200px;width:320px;height:180px;z-index:110}.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-controls,.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-danmu{display:none}.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-miniplayer-lay{display:block}.xgplayer-skin-default.xgplayer-miniplayer-active .xgplayer-miniplayer-drag{display:-webkit-flex;display:-moz-box;display:flex}.xgplayer-skin-default.xgplayer-inactive .xgplayer-miniplayer-drag{display:none}.lang-is-jp .xgplayer-miniplayer .name span{width:70px;height:20px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(155),a=(o=i)&&o.__esModule?o:{default:o};n(156),t.default={name:"s_playNext",method:function(){var e=this,t=e.config.playNext;if(t&&t.urlList){var n=(0,r.createDom)("xg-playnext",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-playnext"),o=e.lang.PLAYNEXT_TIPS,i=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-playnext">'+o+"</span>",{},"xgplayer-tips");n.appendChild(i),e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),(0,r.addClass)(e.root,"xgplayer-is-enter"),e.userGestureTrigEvent("playNextBtnClick")}))}));var s=function(){(0,r.addClass)(e.root,"xgplayer-playnext-inactive")};e.on("urlListEnd",s),e.once("destroy",(function t(){e.off("urlListEnd",s),e.off("destroy",t)}))}}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n  <path transform="scale(0.038 0.028)" d="M800 380v768h-128v-352l-320 320v-704l320 320v-352z"></path>\n</svg>\n'},function(e,t,n){var o=n(157);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-playnext{position:relative;-webkit-order:1;-moz-box-ordinal-group:2;order:1;display:block;cursor:pointer;top:-2px}.xgplayer-skin-default .xgplayer-playnext .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-playnext .xgplayer-tips .xgplayer-tip-playnext{display:block}.xgplayer-skin-default .xgplayer-playnext:hover{opacity:.85}.xgplayer-skin-default .xgplayer-playnext:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-playnext .xgplayer-tips{margin-left:-25px}.xgplayer-lang-is-jp .xgplayer-playnext .xgplayer-tips{margin-left:-38px}.xgplayer-skin-default.xgplayer-playnext-inactive .xgplayer-playnext{display:none}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(159),a=(o=i)&&o.__esModule?o:{default:o};n(160),t.default={name:"s_rotate",method:function(){var e=this;if(e.config.rotate){var t=(0,r.createDom)("xg-rotate",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-rotate"),n=e.lang.ROTATE_TIPS,o=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-rotate">'+n+"</span>",{},"xgplayer-tips");t.appendChild(o),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("rotateBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 40 40" fill="none">\n  <g clip-path="url(#clip0)">\n    <path transform="scale(1.5 1.5)" d="M11.6665 9.16663H4.1665C2.78579 9.16663 1.6665 10.2859 1.6665 11.6666V15.8333C1.6665 17.214 2.78579 18.3333 4.1665 18.3333H11.6665C13.0472 18.3333 14.1665 17.214 14.1665 15.8333V11.6666C14.1665 10.2859 13.0472 9.16663 11.6665 9.16663Z" fill="white"/>\n    <path transform="scale(1.5 1.5)" fill-rule="evenodd" clip-rule="evenodd" d="M3.88148 4.06298C3.75371 4.21005 3.67667 4.40231 3.67749 4.61242C3.67847 4.87253 3.79852 5.10435 3.98581 5.25646L6.99111 8.05895C7.32771 8.37283 7.85502 8.35443 8.16891 8.01782C8.48279 7.68122 8.46437 7.15391 8.12778 6.84003L6.62061 5.43457L9.8198 5.4224C9.82848 5.42239 9.8372 5.42221 9.84591 5.4219C10.9714 5.38233 12.0885 5.6285 13.0931 6.13744C14.0976 6.64635 14.957 7.40148 15.5908 8.33234C16.2246 9.2632 16.6122 10.3394 16.7177 11.4606C16.823 12.5819 16.6427 13.7115 16.1934 14.7442C16.0098 15.1661 16.203 15.6571 16.6251 15.8408C17.0471 16.0243 17.5381 15.8311 17.7216 15.4091C18.2833 14.1183 18.5087 12.7063 18.3771 11.3047C18.2453 9.90318 17.7607 8.55792 16.9684 7.39433C16.1761 6.23073 15.1021 5.28683 13.8463 4.65065C12.5946 4.01651 11.203 3.70872 9.80072 3.75583L6.43415 3.76862L7.96326 2.12885C8.27715 1.79225 8.25872 1.26494 7.92213 0.951061C7.58553 0.63718 7.05822 0.655585 6.74433 0.99219L3.90268 4.0395C3.89545 4.04724 3.88841 4.05509 3.88154 4.06303L3.88148 4.06298Z" fill="white"/>\n  </g>\n  <defs>\n    <clipPath id="clip0">\n      <rect width="40" height="40" fill="white"/>\n    </clipPath>\n  </defs>\n</svg>\n'},function(e,t,n){var o=n(161);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-rotate{position:relative;-webkit-order:10;-moz-box-ordinal-group:11;order:10;display:block;cursor:pointer}.xgplayer-skin-default .xgplayer-rotate .xgplayer-icon{margin-top:7px;width:26px}.xgplayer-skin-default .xgplayer-rotate .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-rotate .xgplayer-tips{margin-left:-22px}.xgplayer-skin-default .xgplayer-rotate .xgplayer-tips .xgplayer-tip-rotate{display:block}.xgplayer-skin-default .xgplayer-rotate:hover{opacity:.85}.xgplayer-skin-default .xgplayer-rotate:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-rotate .xgplayer-tips{margin-left:-26px}.xgplayer-lang-is-jp .xgplayer-rotate .xgplayer-tips{margin-left:-38px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(163),a=(o=i)&&o.__esModule?o:{default:o};n(164),t.default={name:"s_reload",method:function(){var e=this;if(e.config.reload){var t=(0,r.createDom)("xg-reload",'<xg-icon class="xgplayer-icon">'+a.default+"</xg-icon>",{},"xgplayer-reload"),n=e.lang.RELOAD_TIPS,o=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-reload">'+n+"</span>",{},"xgplayer-tips");t.appendChild(o),e.once("ready",(function(){e.controls.appendChild(t)})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("reloadBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">\n    <path fill="#FFF" fill-opacity="1" fill-rule="nonzero" d="M18.17 19.988a7.182 7.182 0 0 1-4.256 1.318 7.806 7.806 0 0 1-.595-.03c-.08-.008-.16-.021-.242-.031a8.004 8.004 0 0 1-.458-.071c-.094-.018-.185-.042-.276-.063a7.743 7.743 0 0 1-.439-.113c-.068-.022-.136-.047-.205-.07a7.03 7.03 0 0 1-.492-.181c-.037-.015-.072-.032-.108-.049a7.295 7.295 0 0 1-.554-.269l-.025-.012a7.347 7.347 0 0 1-2.111-1.753c-.03-.036-.057-.074-.086-.11a7.305 7.305 0 0 1-1.594-4.557h1.686a.123.123 0 0 0 .108-.064.119.119 0 0 0-.006-.125L5.684 9.532a.123.123 0 0 0-.103-.056.123.123 0 0 0-.102.056l-2.834 4.276a.121.121 0 0 0-.005.125c.022.04.064.064.107.064h1.687c0 2.025.627 3.902 1.693 5.454.013.021.022.044.037.066.11.159.233.305.352.455.043.057.085.116.13.171.175.213.36.413.55.61.02.018.036.038.054.055a9.447 9.447 0 0 0 2.91 1.996c.058.026.115.054.175.079.202.084.41.158.619.228.098.034.196.069.296.1.183.054.37.1.558.145.125.029.249.06.376.085.052.01.102.027.155.035.177.032.355.05.533.071.064.007.128.018.19.026.32.03.639.052.956.052a9.46 9.46 0 0 0 5.47-1.746 1.16 1.16 0 0 0 .282-1.608 1.143 1.143 0 0 0-1.6-.283zm5.397-5.991a9.604 9.604 0 0 0-1.685-5.441c-.016-.027-.026-.054-.043-.078-.132-.19-.276-.366-.419-.543-.017-.022-.032-.044-.05-.065a9.467 9.467 0 0 0-3.571-2.7l-.114-.051a11.2 11.2 0 0 0-.673-.248c-.082-.027-.163-.057-.247-.082a9.188 9.188 0 0 0-.6-.156c-.113-.026-.224-.055-.337-.077-.057-.011-.109-.028-.164-.037-.151-.027-.304-.039-.455-.058-.104-.013-.208-.03-.313-.04a10.05 10.05 0 0 0-.759-.039c-.045 0-.09-.007-.136-.007l-.025.003a9.45 9.45 0 0 0-5.46 1.737 1.16 1.16 0 0 0-.284 1.608c.363.523 1.08.65 1.6.284a7.182 7.182 0 0 1 4.222-1.32c.217.002.429.013.639.033.065.007.129.017.193.025.173.021.344.046.513.08.075.014.149.033.221.05.166.037.331.077.494.127l.152.051c.185.061.366.127.545.201l.054.025a7.308 7.308 0 0 1 2.741 2.067l.013.018a7.302 7.302 0 0 1 1.652 4.633h-1.686a.123.123 0 0 0-.108.064.12.12 0 0 0 .006.124l2.834 4.277c.022.033.06.054.103.054.042 0 .08-.021.102-.054l2.833-4.277a.12.12 0 0 0 .005-.124.123.123 0 0 0-.108-.064h-1.685z"/>\n</svg>\n'},function(e,t,n){var o=n(165);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-reload{position:relative;-webkit-order:1;-moz-box-ordinal-group:2;order:1;display:block;width:40px;height:40px;cursor:pointer}.xgplayer-skin-default .xgplayer-reload .xgplayer-icon{margin-top:7px;width:26px}.xgplayer-skin-default .xgplayer-reload .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-reload .xgplayer-tips{margin-left:-22px}.xgplayer-skin-default .xgplayer-reload .xgplayer-tips .xgplayer-tip-reload{display:block}.xgplayer-skin-default .xgplayer-reload:hover{opacity:.85}.xgplayer-skin-default .xgplayer-reload:hover .xgplayer-tips{display:block}.xgplayer-lang-is-en .xgplayer-reload .xgplayer-tips{margin-left:-26px}.xgplayer-lang-is-jp .xgplayer-reload .xgplayer-tips{margin-left:-38px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(167),t.default={name:"s_screenShot",method:function(){var e=this;if(e.config.screenShot&&!e.config.screenShot.hideButton){var t=e.lang.SCREENSHOT,n=(0,o.createDom)("xg-screenshot",'<p class="name"><span>'+(e.config.screenShot.iconText||t)+"</span></p>",{tabindex:11},"xgplayer-screenshot");e.once("ready",(function(){e.controls.appendChild(n)})),["click","touchend"].forEach((function(t){n.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("screenShotBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){var o=n(168);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-screenshot{-webkit-order:11;-moz-box-ordinal-group:12;order:11;position:relative;outline:none;display:block;cursor:pointer;height:20px;top:10px}.xgplayer-skin-default .xgplayer-screenshot .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;line-height:20px;height:20px;color:hsla(0,0%,100%,.8)}.xgplayer-skin-default .xgplayer-screenshot .name span{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;padding:0 10px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-lang-is-en .xgplayer-screenshot .name span,.xgplayer-lang-is-jp .xgplayer-screenshot .name span{width:75px;height:20px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(9),i=(o=r)&&o.__esModule?o:{default:o};n(81),t.default={name:"s_nativeTextTrack",method:function(){if(this.config.nativeTextTrack){var e=this,t=e.root,n=i.default.util,o=n.createDom("xg-texttrack","",{tabindex:7},"xgplayer-texttrack"),r=e.config.nativeTextTrack;r&&Array.isArray(r)&&r.length>0&&(n.addClass(e.root,"xgplayer-is-texttrack"),e.once("canplay",(function(){var i=this,a=["<ul>"];a.push("<li class='"+(this.textTrackShowDefault?"":"selected")+"'}'>"+e.lang.OFF+"</li>"),r.forEach((function(e){a.push("<li class='"+(e.default&&i.textTrackShowDefault?"selected":"")+"'>"+e.label+"</li>")}));var s=e.lang.TEXTTRACK;a.push('</ul><p class="name">'+s+"</p>");var l=t.querySelector(".xgplayer-texttrack");if(l){l.innerHTML=a.join("");var c=l.querySelector(".name");e.config.textTrackActive&&"hover"!==e.config.textTrackActive||c.addEventListener("mouseenter",(function(e){e.preventDefault(),e.stopPropagation(),n.addClass(t,"xgplayer-texttrack-active"),l.focus()}))}else{o.innerHTML=a.join("");var d=o.querySelector(".name");e.config.textTrackActive&&"hover"!==e.config.textTrackActive||d.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),n.addClass(e.root,"xgplayer-texttrack-active"),o.focus()})),e.controls.appendChild(o)}}))),["touchend","click"].forEach((function(t){o.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation();var i=t.target||t.srcElement;if(i&&"li"===i.tagName.toLocaleLowerCase()){Array.prototype.forEach.call(i.parentNode.childNodes,(function(e){n.removeClass(e,"selected")})),n.addClass(i,"selected");var a=e.root.getElementsByTagName("Track");i.innerHTML===e.lang.OFF?(a[0].track.mode="hidden",a[0].src="",n.removeClass(e.root,"xgplayer-texttrack-active")):(a[0].style.display="block",n.addClass(e.root,"xgplayer-texttrack-active"),a[0].track.mode="showing",r.some((function(e){if(e.label===i.innerHTML)return a[0].src=e.src,e.kind&&(a[0].kind=e.kind),a[0].label=e.label,e.srclang&&(a[0].srclang=e.srclang),!0})),e.emit("textTrackChange",i.innerHTML))}else"click"!==e.config.textTrackActive||!i||"p"!==i.tagName.toLocaleLowerCase()&&"em"!==i.tagName.toLocaleLowerCase()||(n.addClass(e.root,"xgplayer-texttrack-active"),o.focus())}),!1)})),e.on("play",(function(){var o=t.querySelector(".xgplayer-texttrack ul"),i=t.getElementsByTagName("Track");e.hls&&o&&i&&(i[0].src="",Array.prototype.forEach.call(o.childNodes,(function(t){n.hasClass(t,"selected")&&(t.innerHTML===e.lang.OFF?(i[0].track.mode="hidden",i[0].src=""):(i[0].track.mode="hidden",r.some((function(e){if(e.label!==t.innerHTML)return i[0].src=e.src,e.kind&&(i[0].kind=e.kind),i[0].label=e.label,e.srclang&&(i[0].srclang=e.srclang),!0})),r.some((function(e){if(e.label===t.innerHTML)return setTimeout((function(){i[0].src=e.src,e.kind&&(i[0].kind=e.kind),i[0].label=e.label,e.srclang&&(i[0].srclang=e.srclang),i[0].track.mode="showing"})),!0}))))})),n.removeClass(e.root,"xgplayer-texttrack-active"))})),o.addEventListener("mouseleave",(function(t){t.preventDefault(),t.stopPropagation(),n.removeClass(e.root,"xgplayer-texttrack-active")}))}}},e.exports=t.default},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-texttrack{-webkit-order:7;-moz-box-ordinal-group:8;order:7;width:60px;height:150px;z-index:18;position:relative;outline:none;display:none;cursor:default;margin-top:-119px}.xgplayer-skin-default .xgplayer-texttrack ul{display:none;list-style:none;min-width:78px;background:rgba(0,0,0,.54);border-radius:1px;position:absolute;bottom:30px;text-align:center;white-space:nowrap;left:50%;-webkit-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;z-index:26;cursor:pointer}.xgplayer-skin-default .xgplayer-texttrack ul li{opacity:.7;font-family:PingFangSC-Regular;font-size:11px;color:hsla(0,0%,100%,.8);width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;margin:auto;padding:6px 13px}.xgplayer-skin-default .xgplayer-texttrack ul li.selected,.xgplayer-skin-default .xgplayer-texttrack ul li:hover{color:#fff;opacity:1}.xgplayer-skin-default .xgplayer-texttrack .name{text-align:center;font-family:PingFangSC-Regular;font-size:13px;cursor:pointer;color:hsla(0,0%,100%,.8);position:absolute;bottom:0;width:60px;height:20px;line-height:20px;background:rgba(0,0,0,.38);border-radius:10px;display:inline-block;vertical-align:middle}.xgplayer-skin-default .xgplayer-texttrack.xgplayer-texttrack-hide{display:none}xg-text-track{transition:bottom .3s ease}.xgplayer-skin-default.xgplayer-is-texttrack .xgplayer-texttrack,.xgplayer-skin-default.xgplayer-texttrack-active .xgplayer-texttrack ul{display:block}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);function r(e,t,n,o){if(0!==t.length){var r=[];r.push('<li data-type="off" class="'+(o?"":"selected")+'">'+n+"</li>"),t.forEach((function(e){r.push("<li data-id="+e.id+" data-language="+e.language+' class="'+(e.isDefault&&o?"selected":"")+'">'+e.label+"</li>")})),e.innerHTML=r.join("")}else e.innerHTML=""}n(81),t.default={name:"s_textTrack",method:function(){var e=this;if(this.config.textTrack){var t=this.config.textTrack,n=e.lang.TEXTTRACK,i=(0,o.createDom)("xg-texttrack",'<ul></ul><p class="name">'+n+"</p>",{tabindex:7},"xgplayer-texttrack");t&&Array.isArray(t)&&(t.length>0&&(0,o.addClass)(e.root,"xgplayer-is-texttrack"),e.once("canplay",(function(){if(!e.root.querySelector(".xgplayer-texttrack")){e.controls.appendChild(i);var n=i.querySelector(".name");e.config.textTrackActive&&"hover"!==e.config.textTrackActive?n.addEventListener("click",(function(t){t.preventDefault(),t.stopPropagation(),(0,o.hasClass)(e.root,"xgplayer-texttrack-active")?(0,o.removeClass)(e.root,"xgplayer-texttrack-active"):(0,o.addClass)(e.root,"xgplayer-texttrack-active")})):(n.addEventListener("mouseenter",(function(t){t.preventDefault(),t.stopPropagation(),(0,o.addClass)(e.root,"xgplayer-texttrack-active"),i.focus()})),i.addEventListener("mouseleave",(function(t){t.preventDefault(),t.stopPropagation(),(0,o.removeClass)(e.root,"xgplayer-texttrack-active")})))}["touchend","click"].forEach((function(t){i.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation();var n=t.target||t.srcElement;if(n&&"li"===n.tagName.toLocaleLowerCase()){var r=n.getAttribute("data-id"),i=n.getAttribute("data-type"),a=n.getAttribute("data-language");Array.prototype.forEach.call(n.parentNode.childNodes,(function(e){(0,o.removeClass)(e,"selected")})),(0,o.addClass)(n,"selected"),"off"===i?(e.switchOffSubtile(),(0,o.removeClass)(e.root,"xgplayer-texttrack-active")):(e.switchSubTitle({id:r,language:a}),(0,o.addClass)(e.root,"xgplayer-texttrack-active"))}}))})),r(i.getElementsByTagName("ul")[0],t,e.lang.OFF,e.textTrackShowDefault),0===t.length?(0,o.addClass)(i,"xgplayer-texttrack-hide"):(0,o.removeClass)(i,"xgplayer-texttrack-hide")})),e.on("subtitle_change",(function(n){if(n.isListUpdate){var a=i.getElementsByTagName("ul")[0];t=n.list,r(a,n.list,e.lang.OFF,e.textTrackShowDefault),n.list.length>0?(0,o.addClass)(e.root,"xgplayer-is-texttrack"):(0,o.removeClass)(e.root,"xgplayer-is-texttrack"),0===n.list.length?(0,o.addClass)(i,"xgplayer-texttrack-hide"):(0,o.removeClass)(i,"xgplayer-texttrack-hide")}})))}}},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(173),t.default={name:"s_error",method:function(){var e=this,t=e.root,n=(0,o.createDom)("xg-error",'<span class="xgplayer-error-text">\u8bf7<span class="xgplayer-error-refresh">\u5237\u65b0</span>\u8bd5\u8bd5</span>',{},"xgplayer-error");e.once("ready",(function(){t.appendChild(n)}));var r=n.querySelector(".xgplayer-error-text");e.config.lang&&"zh-cn"===e.config.lang?r.innerHTML=e.config.errorTips||'\u8bf7<span class="xgplayer-error-refresh">\u5237\u65b0</span>\u8bd5\u8bd5':r.innerHTML=e.config.errorTips||'please try to <span class="xgplayer-error-refresh">refresh</span>';var i=null;function a(){(0,o.addClass)(e.root,"xgplayer-is-error"),(i=n.querySelector(".xgplayer-error-refresh"))&&["touchend","click"].forEach((function(t){i.addEventListener(t,(function(t){t.preventDefault(),t.stopPropagation(),e.autoplay=!0;var n=e.currentTime;e.once("playing",(function(){e.currentTime=n,(0,o.removeClass)(e.root,"xgplayer-is-error")})),e.src=e.config.url}))}))}e.on("error",a),e.once("destroy",(function t(){e.off("error",a),e.off("destroy",t)}))}},e.exports=t.default},function(e,t,n){var o=n(174);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-error{background:#000;display:none;position:absolute;left:0;top:0;width:100%;height:100%;z-index:125;font-family:PingFangSC-Regular;font-size:14px;color:#fff;text-align:center;line-height:100%;-webkit-justify-content:center;-moz-box-pack:center;justify-content:center;-webkit-align-items:center;-moz-box-align:center;align-items:center}.xgplayer-skin-default .xgplayer-error .xgplayer-error-refresh{color:#fa1f41;padding:0 3px;cursor:pointer}.xgplayer-skin-default .xgplayer-error .xgplayer-error-text{line-height:18px;margin:auto 6px}.xgplayer-skin-default.xgplayer-is-error .xgplayer-error{display:-webkit-flex;display:-moz-box;display:flex}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(0);n(176),t.default={name:"s_memoryPlay",method:function(){var e=this,t=e.config.lastPlayTime||0,n=e.config.lastPlayTimeHideDelay||0,r=null;if(!(t<=0)){(r=(0,o.createDom)("xg-memoryplay",'<div class="xgplayer-memoryplay-spot"><div class="xgplayer-progress-tip">\u60a8\u4e0a\u6b21\u89c2\u770b\u5230 <span class="xgplayer-lasttime">'+(0,o.format)(t)+'</span> \uff0c\u4e3a\u60a8\u81ea\u52a8\u7eed\u64ad <span class="btn-close"><svg viewBox="64 64 896 896" focusable="false" class="" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"></path></svg></span></div></div>',{},"xgplayer-memoryplay")).addEventListener("mouseover",(function(e){e.stopPropagation()}));var i=function(){r&&r.parentNode&&r.parentNode.removeChild(r),r=null};r.querySelector(".xgplayer-progress-tip .btn-close").addEventListener("click",i),e.once("playing",(function(){n>0&&e.root.appendChild(r),e.emit("memoryPlayStart",t),n>0&&setTimeout((function(){i()}),1e3*n)})),e.once("ended",i)}}},e.exports=t.default},function(e,t,n){var o=n(177);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-memoryplay-spot{position:absolute;height:32px;left:10px;bottom:46px;background:rgba(0,0,0,.5);border-radius:32px;line-height:32px;color:#ddd;z-index:15;padding:0 32px 0 16px}.xgplayer-skin-default .xgplayer-memoryplay-spot .xgplayer-lasttime{color:red;font-weight:700}.xgplayer-skin-default .xgplayer-memoryplay-spot .btn-close{position:absolute;width:16px;height:16px;right:10px;top:2px;cursor:pointer;color:#fff;font-size:16px}",""])},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o,r=n(0),i=n(179),a=(o=i)&&o.__esModule?o:{default:o};n(180),t.default={name:"s_airplay",method:function(){var e=this;if(e.config.airplay&&window.WebKitPlaybackTargetAvailabilityEvent){var t=(0,r.createDom)("xg-airplay",'<xg-icon class="xgplayer-icon">\n    <div class="xgplayer-icon-airplay">'+a.default+"</div>\n  </xg-icon>",{},"xgplayer-airplay"),n=(0,r.createDom)("xg-tips",'<span class="xgplayer-tip-airplay">'+e.lang.AIRPLAY_TIPS+"</span>",{},"xgplayer-tips");t.appendChild(n),e.once("ready",(function(){e.controls.appendChild(t),e.video.addEventListener("webkitplaybacktargetavailabilitychanged",(function(e){switch(e.availability){case"available":t.hidden=!1,t.disabled=!1;break;case"not-available":t.hidden=!0,t.disabled=!0}}))})),["click","touchend"].forEach((function(n){t.addEventListener(n,(function(t){t.preventDefault(),t.stopPropagation(),e.userGestureTrigEvent("airplayBtnClick")}))}))}}},e.exports=t.default},function(e,t,n){"use strict";n.r(t),t.default='<svg t="1600422191774" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3100" width="28" height="28"><path d="M256 938.666667h512L512 597.333333 256 938.666667z m170.666667-85.333334l85.333333-113.781333L597.333333 853.333333H426.666667zM853.333333 85.333333H170.666667C99.946667 85.333333 42.666667 142.613333 42.666667 213.333333v426.666667c0 70.72 57.28 128 128 128h106.666666l64-85.333333H170.666667c-23.573333 0-42.666667-19.093333-42.666667-42.666667V213.333333c0-23.573333 19.093333-42.666667 42.666667-42.666666h682.666666c23.573333 0 42.666667 19.093333 42.666667 42.666666v426.666667c0 23.573333-19.093333 42.666667-42.666667 42.666667H682.666667l64 85.333333h106.666666c70.72 0 128-57.28 128-128V213.333333c0-70.72-57.28-128-128-128z" p-id="3101" fill="#ffffff"></path></svg>'},function(e,t,n){var o=n(181);"string"===typeof o&&(o=[[e.i,o,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};n(2)(o,r),o.locals&&(e.exports=o.locals)},function(e,t,n){(e.exports=n(1)(!1)).push([e.i,".xgplayer-skin-default .xgplayer-airplay{position:relative;-webkit-order:11;-moz-box-ordinal-group:12;order:11;display:block;cursor:pointer;margin-left:5px;margin-right:3px}.xgplayer-skin-default .xgplayer-airplay .xgplayer-icon{margin-top:6px;margin-left:6px}.xgplayer-skin-default .xgplayer-airplay .xgplayer-icon div{position:absolute}.xgplayer-skin-default .xgplayer-airplay .xgplayer-icon .xgplayer-icon-airplay{display:block}.xgplayer-skin-default .xgplayer-airplay .xgplayer-tips{position:absolute;right:0;left:auto}.xgplayer-skin-default .xgplayer-airplay .xgplayer-tips .xgplayer-tip-airplay{display:block}.xgplayer-skin-default .xgplayer-airplay:hover{opacity:.85}.xgplayer-skin-default .xgplayer-airplay:hover .xgplayer-tips{display:block}",""])}])},e.exports=t()}}]);