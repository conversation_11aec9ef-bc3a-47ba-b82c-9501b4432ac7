
import requests
import os

clearml_api_host = os.environ.get("CLEARML_API_HOST", "")

# 获取算法版本详情
def get_algo_version_detail(algo_id:str, algo_version:str):
    url = f"{clearml_api_host}/algo.get_all_release"
    payload = {
        "algo": algo_id,
    }
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        versions = response.json().get("data",{}).get("list",[])
        for version in versions:
            if version.get("version") == algo_version:
                return version
        return None
    else:
        return None
    
def update_algo_version(algo_id:str, algo_version, params:dict):
    url = f"{clearml_api_host}/algo.storage_training_result"
    payload = {
         "algo_id": algo_id,
        "version": algo_version,
        "release":{
            "args": {
                "input_train": params,
            }
        }
    }

    print(f'update algo payload {payload}')

    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return True
    else:
        return False



