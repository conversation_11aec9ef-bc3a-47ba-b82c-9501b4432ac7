{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "sum by (pod) (container_memory_working_set_bytes{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod=~\".*$pod.*\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "__auto", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Memory Usage", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "min": 0, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 7}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "exemplar": true, "expr": "sum by (pod) (rate(container_cpu_usage_seconds_total{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod=~\".*$pod.*\"}[2m]))", "format": "time_series", "instant": false, "interval": "30s", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "", "format": "time_series", "intervalFactor": 1, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "CPU Usage", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "min": 0, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 14}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "DCGM_FI_DEV_GPU_UTIL{pod=~\".*$pod.*\"}", "format": "time_series", "instant": false, "interval": "30s", "intervalFactor": 1, "legendFormat": "{{instance}},{{pod}} GPU {{gpu}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "pod GPU Utilization", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "min": 0, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 21}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "DCGM_FI_DEV_FB_USED{pod=~'.*$pod.*'}/(DCGM_FI_DEV_FB_USED{pod=~'.*$pod.*'} + DCGM_FI_DEV_FB_FREE{pod=~'.*$pod.*'})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}},{{pod}} GPU {{gpu}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "pod GPU Memory Usage Util", "tooltip": {"shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:57", "format": "percentunit", "logBase": 1, "max": "1", "min": "0", "show": true}, {"$$hashKey": "object:58", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 29}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "exemplar": true, "expr": "sum by (pod) (rate(container_network_receive_bytes_total{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod=~\".*$pod.*\"}[2m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}} receive", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "exemplar": true, "expr": "- sum by (pod) (rate(container_network_transmit_bytes_total{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod=~\".*$pod.*\"}[2m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}} send", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Network I/O", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "bytes", "logBase": 1, "min": 0, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-text", "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 79.9998}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 474}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 36}, "id": 2, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "memory request (max)"}]}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "container_spec_memory_limit_bytes{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod!=\"\",namespace=~\"pipeline|jupyter|service|automl|aihub\"}/1024/1024/1024", "format": "table", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "container_memory_working_set_bytes{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod!=\"\",namespace=~\"pipeline|jupyter|service|automl|aihub\"}/1024/1024/1024", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "sum by (node,namespace,pod) (rate(container_cpu_usage_seconds_total{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod!=\"\",namespace=~\"pipeline|jupyter|service|automl|aihub\"}[2m]))", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "container_spec_cpu_quota{job=\"kubelet\", image!=\"\",container_name!=\"POD\",pod!=\"\",namespace=~\"pipeline|jupyter|service|automl|aihub\"}/100000", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}], "title": "All pod", "transformations": [{"id": "labelsToFields", "options": {}}, {"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "container": true, "endpoint": true, "id": true, "image": true, "instance": true, "job": true, "name": true, "service": true}, "indexByName": {"Time": 0, "Value #A": 13, "Value #B": 12, "Value #C": 14, "Value #D": 15, "container": 1, "endpoint": 2, "id": 3, "image": 4, "instance": 5, "job": 6, "name": 7, "namespace": 8, "node": 9, "pod": 10, "service": 11}, "renameByName": {"Value": "cpu-used", "Value #A": "memory-request", "Value #B": "memory-used", "Value #C": "cpu-used", "Value #D": "cpu-request"}}}, {"id": "groupBy", "options": {"fields": {"Value #C": {"aggregations": ["max"], "operation": "aggregate"}, "Value #D": {"aggregations": ["max"], "operation": "aggregate"}, "cpu-used": {"aggregations": ["max"], "operation": "aggregate"}, "cpu-used-ratio": {"aggregations": ["max"], "operation": "aggregate"}, "cpu申请值": {"aggregations": ["max"], "operation": "aggregate"}, "memory-request": {"aggregations": ["max"], "operation": "aggregate"}, "memory-used": {"aggregations": ["max"], "operation": "aggregate"}, "namespace": {"aggregations": [], "operation": "groupby"}, "node": {"aggregations": [], "operation": "groupby"}, "pod": {"aggregations": [], "operation": "groupby"}, "request": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "used": {"aggregations": ["lastNotNull"], "operation": "aggregate"}}}}], "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "-", "value": "-"}, "hide": 0, "label": "Pod", "name": "pod", "options": [{"selected": true, "text": "rikochen-mtl-daily-usermode-abt", "value": "rikochen-mtl-daily-usermode-abt"}], "query": "-", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "pod-info", "uid": "pod-info", "version": 1, "weekStart": ""}