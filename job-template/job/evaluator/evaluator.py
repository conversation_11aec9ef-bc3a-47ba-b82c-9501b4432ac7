import requests
import argparse
import os


def get_best_task(metric_name: str, metric_weight=None):

    project_name = os.environ.get("KFJ_TASK_NAME", "")
    clearml_api_host = os.environ.get("CLEARML_API_HOST", "")

    project_payload = {"name": project_name}
    get_all_uri = f"{clearml_api_host}/projects.get_all"
    resp = requests.post(get_all_uri, json = project_payload).json()
    print(resp)
    if resp.get("data").get("projects"):
        project_id = resp.get("data").get("projects")[0].get("id")

        get_best_task_payload = {"project": project_id, "metric_name":metric_name}
        uri = f"{clearml_api_host}/projects.get_best_task"
        resp = requests.post(uri, json = get_best_task_payload).json()
        print(resp)
        print(resp.get('data').get('task'))

        # TODO:最优Task评估完成,下一步处理

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="算法评估配置")
    parser.add_argument('--metric_name', type=str, required=True, help="评估指标")

    args = parser.parse_args()

    get_best_task(metric_name=args.metric_name)