{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 11, "iteration": 1636078836746, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 8, "panels": [], "repeat": null, "title": "Headlines", "type": "row"}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 1}, "id": 0, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_requests_cpu_cores) / sum(node:node_num_cpu:sum)", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "CPU Requests Commitment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 1}, "id": 1, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_limits_cpu_cores) / sum(node:node_num_cpu:sum)", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "CPU Limits Commitment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 1}, "id": 2, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_requests_memory_bytes) / sum(node_memory_MemTotal_bytes)", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "Memory Requests Commitment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 1}, "id": 3, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_limits_memory_bytes) / sum(node_memory_MemTotal_bytes)", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "Memory Limits Commitment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 9, "panels": [], "repeat": null, "title": "CPU", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 10, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 5}, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(irate(container_cpu_usage_seconds_total[1m])) by (namespace)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{namespace}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 10, "panels": [], "repeat": null, "title": "CPU Quota", "type": "row"}, {"aliasColors": {}, "bars": false, "columns": [], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 13}, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "pageSize": null, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scroll": true, "seriesOverrides": [], "showHeader": true, "sort": {"col": 0, "desc": true}, "spaceLength": 10, "stack": false, "steppedLine": false, "styles": [{"alias": "Time", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "CPU Usage", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #A", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "CPU Requests", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #B", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "CPU Requests %", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #C", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "CPU Limits", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #D", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "CPU Limits %", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #E", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "Namespace", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": true, "linkTooltip": "Drill down", "linkUrl": "/d/85a562078cdf77779eaa1add43ccec1e/k8s-resources-namespace?var-datasource=$datasource&var-namespace=$__cell", "pattern": "namespace", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "string", "unit": "short"}], "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total[5m])) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 10}, {"expr": "sum(kube_pod_container_resource_requests_cpu_cores) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "B", "step": 10}, {"expr": "sum(rate(container_cpu_usage_seconds_total[5m])) by (namespace) / sum(kube_pod_container_resource_requests_cpu_cores) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "C", "step": 10}, {"expr": "sum(kube_pod_container_resource_limits_cpu_cores) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "D", "step": 10}, {"expr": "sum(rate(container_cpu_usage_seconds_total[5m])) by (namespace) / sum(kube_pod_container_resource_limits_cpu_cores) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "E", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "CPU Quota", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transform": "table", "type": "table", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 11, "panels": [], "repeat": null, "title": "Memory", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 10, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 21}, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(container_memory_rss) by (namespace)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{namespace}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage (w/o cache)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 12, "panels": [], "repeat": null, "title": "Memory Requests", "type": "row"}, {"aliasColors": {}, "bars": false, "columns": [], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 29}, "id": 7, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "pageSize": null, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scroll": true, "seriesOverrides": [], "showHeader": true, "sort": {"col": 0, "desc": true}, "spaceLength": 10, "stack": false, "steppedLine": false, "styles": [{"alias": "Time", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "Memory Usage", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #A", "thresholds": [], "type": "number", "unit": "decbytes"}, {"alias": "Memory Requests", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #B", "thresholds": [], "type": "number", "unit": "decbytes"}, {"alias": "Memory Requests %", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #C", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "Memory Limits", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #D", "thresholds": [], "type": "number", "unit": "decbytes"}, {"alias": "Memory Limits %", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #E", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "Namespace", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": true, "linkTooltip": "Drill down", "linkUrl": "/d/85a562078cdf77779eaa1add43ccec1e/k8s-resources-namespace?var-datasource=$datasource&var-namespace=$__cell", "pattern": "namespace", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "string", "unit": "short"}], "targets": [{"expr": "sum(container_memory_rss) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 10}, {"expr": "sum(kube_pod_container_resource_requests_memory_bytes) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "B", "step": 10}, {"expr": "sum(container_memory_rss) by (namespace) / sum(kube_pod_container_resource_requests_memory_bytes) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "C", "step": 10}, {"expr": "sum(kube_pod_container_resource_limits_memory_bytes) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "D", "step": 10}, {"expr": "sum(container_memory_rss) by (namespace) / sum(kube_pod_container_resource_limits_memory_bytes) by (namespace)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "E", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Requests by Namespace", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transform": "table", "type": "table", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "schemaVersion": 18, "style": "dark", "tags": [], "templating": {"list": [{"current": {"text": "prometheus", "value": "prometheus"}, "hide": 0, "label": null, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Cluster Resource Request Rate", "uid": "cluster-req-util", "version": 1}