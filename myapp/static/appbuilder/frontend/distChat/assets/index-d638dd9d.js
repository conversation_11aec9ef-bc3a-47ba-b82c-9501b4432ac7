var u=Object.defineProperty;var p=(d,e,t)=>e in d?u(d,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):d[e]=t;var h=(d,e,t)=>(p(d,typeof e!="symbol"?e+"":e,t),t);import{j as f,aU as y,r as k,R as v,am as g,av as r,aw as w,aB as C,an as b,al as x,aM as L,aN as S}from"./index-34463448.js";const _="/frontend/distChat/assets/404-b0d1a3d9.svg";class E{constructor(e){h(this,"container");h(this,"option");h(this,"searchContent");h(this,"scene");h(this,"isDragging",!1);h(this,"mouseOffset",{x:0,y:0});const t={containerId:"",rtx:"",scene:"",entryType:"input",contentType:"side",isMark:!0,isClose:!0,isShowConfig:!1},s=document.getElementById(e.containerId);if(s)this.container=s;else{const o=document.createElement("div");o.id="aitalk_container",document.body.appendChild(o),this.container=o,console.warn("请检查填写正确的容器Id")}const a={...t,...e};this.scene=e.scene||"starTagGPT",this.option=a,this.init()}init(){switch(this.option.contentType){case"drag":this.initDrag(this.option);break;case"side":this.initSide(this.option);break;default:this.initSide(this.option);break}}show(e,t){switch(this.option.contentType){case"drag":this.showDrag(e,t);break;case"side":this.showSide(e);break;default:this.showSide(e);break}}close(e){switch(this.option.contentType){case"drag":this.closeDrag(e);break;case"side":this.closeSide(e);break;default:this.closeSide(e);break}}initEntry(){switch(this.option.entryType){case"input":this.initSearch();break;case"bubble":this.initBubble();break}}initSearch(){this.container.innerHTML=`
    <div class='tmeaitalk-search'>
        <div class="default-search-bar">
            <svg viewBox="64 64 896 896" class="default-search-bar-svg cp">
                <path d="M909.6 854.5 649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path>
            </svg>
            <input class="default-search-bar-input" id="tmeaitalk_search_input" placeholder="输入关键字搜索..." />
            <span class="default-search-shortcut">⌘/Win + K</span>
        </div>
    </div>
        `}initBubble(){const e=document.createElement("div");e.id="aitalk_container",e.className="tmeaitalk-bubble",e.innerHTML="?",document.body.appendChild(e),this.container=e;const t=()=>{this.show()},s=document.querySelector(".tmeaitalk-bubble");s&&s.addEventListener("click",t)}initSide(e){this.initEntry();const t=document.createElement("div");t.id="tmeaitalk_slidecontainer",t.innerHTML=`
    <div class="tmeaitalk-slide-container">
      <div class="tmeaitalk-slide-container-mask ${e.isMark?"":"d-n"}"></div>
      <div class="tmeaitalk-slide-container-sidebar">
          <div class="close tmeaitalk-slide-container-close ${e.isClose?"":"d-n"}">&times;</div>
          <div class="tmeaitalk-slide-container-content">
            <iframe id="starTalkIframe"
              title='AIChat'
              src='${this.createChatUrl()}'
              allowFullScreen
              allow="microphone;camera;midi;encrypted-media;"
              class="starTalkIframe fade-in"
              >
            </iframe>
          </div>
      </div>
    </div>
        `;const s=i=>{i.metaKey&&i.keyCode===75&&this.show(this.searchContent),i.keyCode===27&&this.close()},a=()=>{this.close()},o=i=>{this.searchContent=i.target.value},m=i=>{i.key==="Enter"&&this.show(this.searchContent)};document.addEventListener("keydown",s),document.body.appendChild(t);const n=document.getElementById("tmeaitalk_search_input"),l=document.querySelector(".tmeaitalk-slide-container-mask"),c=document.querySelector(".tmeaitalk-slide-container-close");c&&c.addEventListener("click",a),l&&l.addEventListener("click",a),n&&n.addEventListener("input",o),n&&n.addEventListener("keydown",m)}initDrag(e){this.initEntry();const t=document.createElement("div");t.id="tmeaitalk_slidecontainer",t.innerHTML=`
    <div class="tmeaitalk-slide-container-drag close-drag">
      <div class="tmeaitalk-slide-container-sidebar">
          <div class="tmeaitalk-slide-tool">
            <div class="tmeaitalk-tool-drag">
              <svg t="1685006041565" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2234" width="20" height="20"><path d="M945.96 478.06L814.09 346.19c-12.5-12.5-32.75-12.5-45.25 0s-12.5 32.75 0 45.25L857.4 480H544V166.6l88.56 88.56c6.25 6.25 14.44 9.38 22.62 9.38s16.38-3.12 22.62-9.38c12.5-12.5 12.5-32.75 0-45.25L545.94 78.04c-18.69-18.72-49.19-18.72-67.88 0L346.19 209.91c-12.5 12.5-12.5 32.75 0 45.25s32.75 12.5 45.25 0L480 166.6V480H166.6l88.56-88.56c12.5-12.5 12.5-32.75 0-45.25s-32.75-12.5-45.25 0L78.04 478.06c-18.72 18.69-18.72 49.19 0 67.88l131.88 131.88c12.5 12.5 32.75 12.5 45.25 0 6.25-6.25 9.38-14.44 9.38-22.62s-3.12-16.38-9.38-22.62L166.6 544H480v313.4l-88.56-88.56c-12.5-12.5-32.75-12.5-45.25 0s-12.5 32.75 0 45.25l131.88 131.88c18.69 18.72 49.19 18.72 67.88 0l131.88-131.88c12.5-12.5 12.5-32.75 0-45.25-6.25-6.25-14.44-9.38-22.62-9.38s-16.38 3.12-22.62 9.38L544 857.4V544h313.4l-88.56 88.56c-6.25 6.25-9.38 14.44-9.38 22.62s3.12 16.38 9.38 22.62c12.5 12.5 32.75 12.5 45.25 0l131.88-131.88c18.71-18.67 18.71-49.17-0.01-67.86z" fill="#333333" p-id="2235"></path></svg>
            </div>
            <div class="close tmeaitalk-slide-container-close ${e.isClose?"":"d-n"}">
              <svg t="1685006125877" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3229" width="20" height="20"><path d="M571.733333 512l268.8-268.8c17.066667-17.066667 17.066667-42.666667 0-59.733333-17.066667-17.066667-42.666667-17.066667-59.733333 0L512 452.266667 243.2 183.466667c-17.066667-17.066667-42.666667-17.066667-59.733333 0-17.066667 17.066667-17.066667 42.666667 0 59.733333L452.266667 512 183.466667 780.8c-17.066667 17.066667-17.066667 42.666667 0 59.733333 8.533333 8.533333 19.2 12.8 29.866666 12.8s21.333333-4.266667 29.866667-12.8L512 571.733333l268.8 268.8c8.533333 8.533333 19.2 12.8 29.866667 12.8s21.333333-4.266667 29.866666-12.8c17.066667-17.066667 17.066667-42.666667 0-59.733333L571.733333 512z" fill="#2c2c2c" p-id="3230"></path></svg>
            </div>
          </div>
          <div class="tmeaitalk-slide-container-content">
            <iframe id="starTalkIframe"
              title='AIChat'
              src='${this.createChatUrl()}'
              allowFullScreen
              allow="microphone;camera;midi;encrypted-media;"
              class="starTalkIframe fade-in"
              >
            </iframe>
          </div>
      </div>
    </div>
        `;const s=i=>{i.metaKey&&i.keyCode===75&&this.show(this.searchContent),i.keyCode===27&&this.close()},a=()=>{this.close()},o=i=>{this.searchContent=i.target.value},m=i=>{i.key==="Enter"&&this.show(this.searchContent)};document.addEventListener("keydown",s),document.body.appendChild(t);const n=document.getElementById("tmeaitalk_search_input"),l=document.querySelector(".tmeaitalk-slide-container-close"),c=document.querySelector(".tmeaitalk-slide-container-drag");c.addEventListener("mousedown",i=>{this.isDragging=!0,this.mouseOffset.x=i.clientX-c.offsetLeft,this.mouseOffset.y=i.clientY-c.offsetTop}),document.addEventListener("mousemove",i=>{this.isDragging&&(c.style.left=`${i.clientX-this.mouseOffset.x}px`,c.style.top=`${i.clientY-this.mouseOffset.y}px`)}),document.addEventListener("mouseup",()=>{this.isDragging=!1}),l&&l.addEventListener("click",a),n&&n.addEventListener("input",o),n&&n.addEventListener("keydown",m),setTimeout(()=>{this.initDragPosition()},100)}initDragPosition(){const e=document.querySelector(".tmeaitalk-slide-container-drag"),t=e.clientWidth,s=e.clientHeight,a=window.innerWidth,o=window.innerHeight,m=this.container.offsetTop,n=this.container.offsetLeft;let l,c;n+t<a?l=`${n+40}px`:l=`${n-t}px`,m+s<o?c=`${this.container.offsetTop+40}px`:c=`${this.container.offsetTop-s-20}px`,e.style.left=l,e.style.top=c}createChatUrl(e){const{rtx:t,isShowConfig:s}=this.option;return t||console.error("请填写rtx"),`//star.tme.woa.com/frontend/distChat/#/chat/${this.scene}?content=${e}&rtx=${t}&isShowConfig=${s}&scene=${this.scene}`}showDrag(e,t){const s=document.querySelector(".tmeaitalk-slide-container-drag"),a=document.getElementById("starTalkIframe");s&&(s.classList.add("show-drag"),s.classList.remove("close-drag")),s&&t&&(s.style.left=`${t.x}px`,s.style.top=`${t.y}px`),a&&(a.src=this.createChatUrl(e))}closeDrag(e){const t=document.querySelector(".tmeaitalk-slide-container-drag");t&&(t.classList.remove("show-drag"),t.classList.add("close-drag"),e&&e())}showSide(e){const t=document.querySelector(".tmeaitalk-slide-container-sidebar"),s=document.querySelector(".tmeaitalk-slide-container-mask"),a=document.getElementById("starTalkIframe");t&&t.classList.add("show"),s&&(s.style.display="block",s.style.opacity="1"),a&&(a.src=this.createChatUrl(e))}closeSide(e){const t=document.querySelector(".tmeaitalk-slide-container-sidebar"),s=document.querySelector(".tmeaitalk-slide-container-mask");t&&t.classList.remove("show"),s&&(s.style.display="none",s.style.opacity="0",e&&e())}}const T={class:"flex h-full"},I={class:"px-4 m-auto space-y-4 text-center max-[400px]"},$=r("div",{class:"w-[300px]"},[r("div",{id:"container_test"})],-1),B=r("p",{class:"text-base text-slate-500 dark:text-neutral-400"}," Sorry, we couldn’t find the page you’re looking for. Perhaps you’ve mistyped the URL? Be sure to check your spelling. ",-1),D=r("div",{class:"flex items-center justify-center text-center"},[r("div",{class:"w-[300px]"},[r("img",{src:_,alt:"404"})])],-1),q=f({__name:"index",setup(d){const e=y(),t=k();function s(){e.push("/")}function a(){t.value.show("",{x:0,y:0})}return v(()=>{const o=new E({containerId:"container_test",contentType:"drag",entryType:"bubble",rtx:"admin"});t.value=o,console.log("aitalk",t)}),(o,m)=>(x(),g("div",T,[r("div",I,[$,r("h1",{class:"text-4xl text-slate-800 dark:text-neutral-200",onClick:a}," Sorry, page not found! "),B,D,w(b(S),{type:"primary",onClick:s},{default:C(()=>[L(" Go to Home ")]),_:1})])]))}});export{q as default};
