{
  visualMap: {
    type: 'continuous',
    min: 0,
    max: 10,
    inRange: {
      color: ['#2F93C8', '#AEC48F', '#FFDB5C', '#F98862']
    }
  },
  series: {
    type: 'sunburst',
    data: [
  {
    name: 'Grandpa',
    children: [
      {
        name: 'Uncle <PERSON>',
        value: 15,
        children: [
          {
            name: 'Cousin <PERSON>',
            value: 2
          },
          {
            name: 'Cousin <PERSON>',
            value: 5,
            children: [
              {
                name: '<PERSON>',
                value: 2
              }
            ]
          },
          {
            name: 'Cousin <PERSON>',
            value: 4
          }
        ]
      },
      {
        name: 'Aunt <PERSON>',
        children: [
          {
            name: 'Cousin <PERSON>',
            value: 4
          }
        ]
      },
      {
        name: 'Father',
        value: 10,
        children: [
          {
            name: 'Me',
            value: 5,
            itemStyle: {
              color: 'red'
            }
          },
          {
            name: 'Brother <PERSON>',
            value: 1
          }
        ]
      }
    ]
  },
  {
    name: '<PERSON>',
    children: [
      {
        name: 'Uncle <PERSON>',
        children: [
          {
            name: 'Cousin <PERSON>',
            value: 3
          },
          {
            name: 'Co<PERSON>in <PERSON>',
            value: 4,
            children: [
              {
                name: '<PERSON><PERSON><PERSON><PERSON>',
                value: 2
              }
            ]
          }
        ]
      }
    ]
  },
  {
    name: '<PERSON>',
    children: [
      {
        name: 'Uncle <PERSON>',
        children: [
          {
            name: 'Cousin <PERSON>',
            value: 1
          },
          {
            name: 'Cousin <PERSON>',
            value: 2
          }
        ]
      }
    ]
  }
],
    radius: [0, '90%'],
    label: {
      rotate: 'radial'
    }
  }
}