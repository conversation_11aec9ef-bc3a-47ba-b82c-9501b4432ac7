{% extends "appbuilder/baselayout.html" %}

{% block head_css %}
{{super()}}
<link rel="icon" type="image/png" href="/static/appbuilder/assets/favicon.ico">

{% for entry in get_unloaded_chunks(css_manifest('theme'), loaded_chunks) %}
<link rel="stylesheet" type="text/css" href="{{ entry }}" />
{% endfor %}
{% endblock %}

{% block head_js %}
{{super()}}
{% with filename="theme" %}
{% include "myapp/partials/_script_tag.html" %}
{% endwith %}
{% endblock %}

{% block tail_js %}
{{super()}}
{% with filename="preamble" %}
{% include "myapp/partials/_script_tag.html" %}
{% endwith %}
{% endblock %}