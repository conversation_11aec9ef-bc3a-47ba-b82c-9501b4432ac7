
ARG FROM_IMAGES
FROM $FROM_IMAGES

ARG PYTHONVERSION
ARG CONDAENV
ARG CUDAVERSION=cu118
ARG TARGETARCH=amd64

ENV TZ Asia/Shanghai
ENV DEBIAN_FRONTEND noninteractive

RUN apt update -y
# 安装运维工具
RUN apt install -y -f --no-install-recommends vim apt-transport-https gnupg2 ca-certificates-java rsync jq  wget git dnsutils iputils-ping net-tools curl mysql-client locales zip unzip nginx lsof

# 安装中文
RUN apt install -y -f --no-install-recommends fontconfig locales ttf-wqy-microhei ttf-wqy-zenhei xfonts-wqy && locale-gen zh_CN && locale-gen zh_CN.utf8
ENV LANG zh_CN.UTF-8
ENV LC_ALL zh_CN.UTF-8
ENV LANGUAGE zh_CN.UTF-8

# 便捷操作
RUN echo "alias ll='ls -alF'" >> ~/.bashrc && \
	echo "alias la='ls -A'" >> ~/.bashrc && \
	echo "alias vi='vim'" >> ~/.bashrc

# 安装amd64的conda
RUN if [ "$TARGETARCH" = "amd64" ]; then \
    wget https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh; \
    mkdir /root/.conda && bash Miniconda3-latest-Linux-x86_64.sh -b ; \
    rm -f Miniconda3-latest-Linux-x86_64.sh ; \
fi
# 安装arm64的conda
RUN if [ "$TARGETARCH" = "arm64" ]; then \
     wget https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-aarch64.sh ; \
    mkdir /root/.conda && bash Miniconda3-latest-Linux-aarch64.sh -b ; \
    rm -f Miniconda3-latest-Linux-aarch64.sh ; \
fi

# 激活python3
ENV PATH "/root/miniconda3/bin":$PATH
RUN conda create -y -n ${CONDAENV} python=${PYTHONVERSION}
ENV PATH /root/miniconda3/envs/${CONDAENV}/bin:$PATH
ENV CONDA_DEFAULT_ENV ${CONDAENV}


# 安装最新版的nodejs
RUN curl -sL https://deb.nodesource.com/setup_16.x | bash -
RUN apt-get install -y nodejs && npm config set unicode false

# 安装启动sshd
RUN apt install -y openssh-server openssh-client

# 安装
RUN apt install -y -f python3-opencv ffmpeg
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple
RUN pip3 install celery redis pyarrow requests_toolbelt cryptography tqdm fsspec aiohttp librosa flask werkzeug requests tornado Pillow pysnooper opencv-python numpy scipy matplotlib scikit-image ipython pyyaml visualdl filelock && rm -rf ~/.cache

RUN pip install --upgrade pip && pip install tornado==6.2 gsutil simplejson sqlalchemy joblib scikit-learn jinja2 requests numpy pandas flask pymysql pysnooper pyyaml jupyterlab==3.4.8 voila notebook==6.4.12 && rm -rf /tmp/* /var/tmp/* /root/.cache
RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/${CUDAVERSION} && rm -rf /tmp/* /var/tmp/* /root/.cache
#RUN jupyter labextension install @jupyter-widgets/jupyterlab-manager
#RUN pip install --upgrade jupyterlab-git jupyterlab_tensorboard_pro && jupyter lab build && jupyter labextension install jupyterlab_tensorboard_pro && jupyter serverextension enable jupyterlab_tensorboard_pro --sys-prefix

# 环境变量
ENV NODE_HOME /usr/local
ENV PATH $PATH:/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ENV NODE_PATH $NODE_HOME/lib/node_modules:$PATH
ENV SHELL /bin/bash

# 拷贝examples
COPY examples/* /examples/

# 环境初始化配置
COPY init.sh /init.sh

RUN echo "export PATH=/root/miniconda3/envs/python39/bin:/root/miniconda3/bin:/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" >> /etc/profile
