#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{- if not (eq .Values.master.enabled false) -}}

{{ $masterCount := int .Values.master.count }}
{{- $release := .Release }}
{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}
{{- $extraServicePorts := .Values.master.extraServicePorts }}
{{- range $i := until $masterCount }}
  # Set scope back to root for variable access in values.yaml.
  # See https://github.com/helm/helm/issues/1311#issuecomment-625976875
  {{- with $ }}
  {{- $masterName := printf "master-%v" $i }}
  {{- $masterJavaOpts := printf " -Dalluxio.master.hostname=%v-%v " $fullName $masterName }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $fullName }}-{{ $masterName }}
  labels:
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ $release.Name }}
    heritage: {{ $release.Service }}
    role: alluxio-master
spec:
  ports:
    - port: {{ int .Values.master.ports.rpc }}
      name: rpc
    - port: {{ int .Values.master.ports.web }}
      name: web
    - port: {{ int .Values.jobMaster.ports.rpc }}
      name: job-rpc
    - port: {{ int .Values.jobMaster.ports.web }}
      name: job-web
    - port: {{ int .Values.master.ports.embedded }}
      name: embedded
    - port: {{ int .Values.jobMaster.ports.embedded }}
      name: job-embedded
    {{- if $extraServicePorts }}
{{- include "alluxio.extraServicePorts" (dict "extraServicePorts" $extraServicePorts) | indent 4 }}
    {{- end }}
  clusterIP: None
  selector:
    role: alluxio-master
    app: {{ $name }}
    release: {{ $release.Name }}
    statefulset.kubernetes.io/pod-name: {{ $fullName }}-{{ $masterName }}
---
  {{- end }}
{{- end }}
{{- end }}
