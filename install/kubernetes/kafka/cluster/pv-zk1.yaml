apiVersion: v1
kind: PersistentVolume
metadata:
  name: zk1-pv
spec:
  capacity:
    storage: 2000Gi
  # volumeMode field requires BlockVolume Alpha feature gate to be enabled.
  volumeMode: Filesystem
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-zk
  local:
    path: /data/zk1
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: pvrole
          operator: In
          values:
          - node1
