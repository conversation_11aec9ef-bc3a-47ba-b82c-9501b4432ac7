docker pull volcanosh/vc-scheduler:v1.7.0 &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:jupyter-ubuntu-cpu-1.0.0 &
docker pull ubuntu:20.04 &
docker pull prom/node-exporter:v1.5.0 &
docker pull argoproj/workflow-controller:v3.4.3 &
docker pull ccr.ccs.tencentyun.com/cube-studio/model_register:20230501 &
docker pull bitnami/redis:6.2.12 &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:vscode-ubuntu-cpu-base &
docker pull ccr.ccs.tencentyun.com/cube-studio/datax:20240501 &
docker pull ccr.ccs.tencentyun.com/cube-studio/dataset:20240501 &
docker pull ccr.ccs.tencentyun.com/cube-studio/patrikx3:latest &
docker pull ccr.ccs.tencentyun.com/cube-studio/ray-sklearn:20240101 &
docker pull ccr.ccs.tencentyun.com/cube-studio/prometheus-adapter:v0.9.1 &
docker pull volcanosh/vc-webhook-manager:v1.7.0 &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:jupyter-ubuntu-deeplearning &
docker pull ccr.ccs.tencentyun.com/cube-studio/tfserving:2.3.4 &
docker pull ccr.ccs.tencentyun.com/cube-studio/model_download:20240501 &
docker pull ccr.ccs.tencentyun.com/cube-studio/video-audio:20210601 &
docker pull ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9 &
docker pull ccr.ccs.tencentyun.com/cube-studio/offline-predict:20230801 &
docker pull ccr.ccs.tencentyun.com/cube-studio/python:strong &
docker pull quay.io/prometheus-operator/prometheus-config-reloader:v0.46.0 &
docker pull grafana/grafana:9.5.20 &
docker pull volcanosh/vc-controller-manager:v1.7.0 &
docker pull nvidia/dcgm-exporter:3.1.7-3.1.4-ubuntu20.04 &
docker pull busybox:1.36.0 &
docker pull ccr.ccs.tencentyun.com/cube-studio/torchserve:0.7.1-cpu &
docker pull docker:23.0.4 &
docker pull prom/prometheus:v2.27.1 &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:jupyter-ubuntu22.04 &
docker pull ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9-dev &
docker pull kubeflow/training-operator:v1-8a066f9 &
docker pull ccr.ccs.tencentyun.com/cube-studio/volcano:20230601 &
docker pull istio/proxyv2:1.15.0 &
docker pull minio/minio:RELEASE.2023-04-20T17-56-55Z &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:jupyter-ubuntu-bigdata &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:jupyter-ubuntu22.04-cuda11.8.0-cudnn8 &
docker pull carlosedp/addon-resizer:v1.8.4 &
docker pull ccr.ccs.tencentyun.com/cube-studio/pytorch:20230801 &
docker pull ccr.ccs.tencentyun.com/cube-studio/k8s-dashboard:v2.6.1 &
docker pull ccr.ccs.tencentyun.com/cube-studio/phpmyadmin &
docker pull kubernetesui/dashboard:v2.6.1 &
docker pull ccr.ccs.tencentyun.com/cube-studio/yolov8:2024.07 &
docker pull python:3.9 &
docker pull ccr.ccs.tencentyun.com/cube-studio/neo4j:4.4 &
docker pull kubernetesui/metrics-scraper:v1.0.8 &
docker pull nvidia/k8s-device-plugin:v0.11.0-ubuntu20.04 &
docker pull bitnami/kube-rbac-proxy:0.14.1 &
docker pull ccr.ccs.tencentyun.com/cube-studio/nni:20240501 &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:vscode-ubuntu-gpu-base &
docker pull ccr.ccs.tencentyun.com/cube-studio/deploy-service:20240601 &
docker pull ccr.ccs.tencentyun.com/cube-studio/tf:20230801 &
docker pull argoproj/argocli:v3.4.3 &
docker pull ccr.ccs.tencentyun.com/cube-studio/tritonserver:22.07-py3 &
docker pull ccr.ccs.tencentyun.com/cube-studio/notebook:jupyter-ubuntu-machinelearning &
docker pull ccr.ccs.tencentyun.com/cube-studio/yolov7:2024.01 &
docker pull ccr.ccs.tencentyun.com/cube-studio/ray:gpu-20240101 &
docker pull alpine:3.10 &
docker pull argoproj/argoexec:v3.4.3 &
docker pull mysql:8.0.32 &
docker pull quay.io/prometheus-operator/prometheus-operator:v0.46.0 &
docker pull istio/pilot:1.15.0 &

wait
