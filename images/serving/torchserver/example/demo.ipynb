{"cells": [{"cell_type": "code", "execution_count": 8, "id": "735ef6e7-3ede-43a0-8210-73ca53b730a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hit:1 http://storage.googleapis.com/tensorflow-serving-apt stable InRelease\n", "Ign:2 https://mirrors.tencent.com/debian stretch InRelease                     \u001b[0m\u001b[33m\n", "Get:3 https://mirrors.tencent.com/debian stretch-updates InRelease [93.6 kB]   \u001b[0m\u001b[33m\n", "Err:3 https://mirrors.tencent.com/debian stretch-updates InRelease             \u001b[0m\u001b[33m\n", "  The following signatures couldn't be verified because the public key is not available: NO_PUBKEY 04EE7237B7D453EC NO_PUBKEY 648ACFD622F3D138\n", "Get:4 https://mirrors.tencent.com/debian stretch Release [118 kB]              \u001b[0m\n", "Get:5 https://mirrors.tencent.com/debian stretch Release.gpg [3,177 B]         \u001b[0m\u001b[33m\n", "Ign:5 https://mirrors.tencent.com/debian stretch Release.gpg                   \u001b[0m\u001b[33m\n", "Hit:6 https://deb.nodesource.com/node_16.x bionic InRelease                \u001b[0m\u001b[0m\u001b[33m\u001b[33m\n", "Hit:7 http://ppa.launchpad.net/deadsnakes/ppa/ubuntu bionic InRelease          \u001b[0m\u001b[33m\u001b[33m\u001b[33m\u001b[33m\u001b[33m\u001b[33m\n", "Reading package lists... Done\u001b[0m\u001b[33m\n", "W: GPG error: https://mirrors.tencent.com/debian stretch-updates InRelease: The following signatures couldn't be verified because the public key is not available: NO_PUBKEY 04EE7237B7D453EC NO_PUBKEY 648ACFD622F3D138\n", "E: The repository 'https://mirrors.tencent.com/debian stretch-updates InRelease' is not signed.\n", "N: Updating from such a repository can't be done securely, and is therefore disabled by default.\n", "N: See apt-secure(8) manpage for repository creation and user configuration details.\n", "W: GPG error: https://mirrors.tencent.com/debian stretch Release: The following signatures couldn't be verified because the public key is not available: NO_PUBKEY 04EE7237B7D453EC NO_PUBKEY 648ACFD622F3D138 NO_PUBKEY 0E98404D386FA1D9 NO_PUBKEY EF0F382A1A7B6500\n", "E: The repository 'https://mirrors.tencent.com/debian stretch Release' is not signed.\n", "N: Updating from such a repository can't be done securely, and is therefore disabled by default.\n", "N: See apt-secure(8) manpage for repository creation and user configuration details.\n", "Reading package lists... Done\n", "Building dependency tree       \n", "Reading state information... Done\n", "E: Unable to locate package unzip\n"]}], "source": ["! apt update -y\n", "! apt install wget unzip\n", "! pip install torchvision torch\n"]}, {"cell_type": "code", "execution_count": 9, "id": "ea00b4be-38c9-4f75-8bc9-cfa15d938e1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2022-07-29 16:29:48--  https://cube-studio.oss-cn-hangzhou.aliyuncs.com/inference/resnet50.pth\n", "Resolving docker-76009.sz.gfp.tencent-cloud.com (docker-76009.sz.gfp.tencent-cloud.com)... **********, **************, *************, ...\n", "Connecting to docker-76009.sz.gfp.tencent-cloud.com (docker-76009.sz.gfp.tencent-cloud.com)|**********|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 102530333 (98M) [application/octet-stream]\n", "Saving to: ‘resnet50.pth’\n", "\n", "resnet50.pth        100%[===================>]  97.78M  5.96MB/s    in 15s     \n", "\n", "2022-07-29 16:30:03 (6.44 MB/s) - ‘resnet50.pth’ saved [102530333/102530333]\n", "\n"]}], "source": ["! wget https://cube-studio.oss-cn-hangzhou.aliyuncs.com/inference/resnet50.pth"]}, {"cell_type": "code", "execution_count": 12, "id": "b2e7cee0-61d7-4548-a94d-91db198da932", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.8/dist-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and will be removed in 0.15, please use 'weights' instead.\n", "  warnings.warn(\n", "/usr/local/lib/python3.8/dist-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and will be removed in 0.15. The current behavior is equivalent to passing `weights=None`.\n", "  warnings.warn(msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ResNet(\n", "  (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)\n", "  (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "  (relu): ReLU(inplace=True)\n", "  (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)\n", "  (layer1): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "        (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", "  (layer2): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)\n", "        (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (3): Bottleneck(\n", "      (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", "  (layer3): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)\n", "        (1): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (3): Bottleneck(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (4): <PERSON>ttle<PERSON>(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (5): <PERSON>ttle<PERSON>(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", "  (layer4): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)\n", "        (1): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "      (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", "  (avgpool): AdaptiveAvgPool2d(output_size=(1, 1))\n", "  (fc): Linear(in_features=2048, out_features=1000, bias=True)\n", ")\n", "conv1.weight : torch.<PERSON><PERSON>([64, 3, 7, 7])\n", "bn1.weight : torch.<PERSON><PERSON>([64])\n", "bn1.bias : <PERSON><PERSON><PERSON><PERSON>([64])\n", "layer1.0.conv1.weight : torch.Size([64, 64, 1, 1])\n", "layer1.0.bn1.weight : torch.Size([64])\n", "layer1.0.bn1.bias : <PERSON><PERSON><PERSON>([64])\n", "layer1.0.conv2.weight : torch.Size([64, 64, 3, 3])\n", "layer1.0.bn2.weight : torch.Size([64])\n", "layer1.0.bn2.bias : <PERSON><PERSON><PERSON>([64])\n", "layer1.0.conv3.weight : torch.Size([256, 64, 1, 1])\n", "layer1.0.bn3.weight : torch.Size([256])\n", "layer1.0.bn3.bias : <PERSON>.Size([256])\n", "layer1.0.downsample.0.weight : torch.Size([256, 64, 1, 1])\n", "layer1.0.downsample.1.weight : torch.Size([256])\n", "layer1.0.downsample.1.bias : torch.Size([256])\n", "layer1.1.conv1.weight : torch.Size([64, 256, 1, 1])\n", "layer1.1.bn1.weight : torch.Size([64])\n", "layer1.1.bn1.bias : <PERSON><PERSON><PERSON>([64])\n", "layer1.1.conv2.weight : torch.Size([64, 64, 3, 3])\n", "layer1.1.bn2.weight : torch.Size([64])\n", "layer1.1.bn2.bias : <PERSON><PERSON><PERSON>([64])\n", "layer1.1.conv3.weight : torch.Size([256, 64, 1, 1])\n", "layer1.1.bn3.weight : torch.Size([256])\n", "layer1.1.bn3.bias : <PERSON>.Size([256])\n", "layer1.2.conv1.weight : torch.Size([64, 256, 1, 1])\n", "layer1.2.bn1.weight : torch.Size([64])\n", "layer1.2.bn1.bias : <PERSON><PERSON><PERSON>([64])\n", "layer1.2.conv2.weight : torch.Size([64, 64, 3, 3])\n", "layer1.2.bn2.weight : torch.Size([64])\n", "layer1.2.bn2.bias : <PERSON><PERSON><PERSON>([64])\n", "layer1.2.conv3.weight : torch.Size([256, 64, 1, 1])\n", "layer1.2.bn3.weight : torch.Size([256])\n", "layer1.2.bn3.bias : <PERSON>.Size([256])\n", "layer2.0.conv1.weight : torch.Size([128, 256, 1, 1])\n", "layer2.0.bn1.weight : torch.Size([128])\n", "layer2.0.bn1.bias : <PERSON><PERSON>Si<PERSON>([128])\n", "layer2.0.conv2.weight : torch.Size([128, 128, 3, 3])\n", "layer2.0.bn2.weight : torch.Size([128])\n", "layer2.0.bn2.bias : <PERSON>.Si<PERSON>([128])\n", "layer2.0.conv3.weight : torch.Size([512, 128, 1, 1])\n", "layer2.0.bn3.weight : torch.Size([512])\n", "layer2.0.bn3.bias : torch.Size([512])\n", "layer2.0.downsample.0.weight : torch.Size([512, 256, 1, 1])\n", "layer2.0.downsample.1.weight : torch.Size([512])\n", "layer2.0.downsample.1.bias : torch.Size([512])\n", "layer2.1.conv1.weight : torch.Size([128, 512, 1, 1])\n", "layer2.1.bn1.weight : torch.Size([128])\n", "layer2.1.bn1.bias : <PERSON><PERSON><PERSON>([128])\n", "layer2.1.conv2.weight : torch.Size([128, 128, 3, 3])\n", "layer2.1.bn2.weight : torch.Size([128])\n", "layer2.1.bn2.bias : <PERSON>.<PERSON>([128])\n", "layer2.1.conv3.weight : torch.Size([512, 128, 1, 1])\n", "layer2.1.bn3.weight : torch.Size([512])\n", "layer2.1.bn3.bias : torch.Size([512])\n", "layer2.2.conv1.weight : torch.Size([128, 512, 1, 1])\n", "layer2.2.bn1.weight : torch.Size([128])\n", "layer2.2.bn1.bias : <PERSON>.<PERSON>([128])\n", "layer2.2.conv2.weight : torch.Size([128, 128, 3, 3])\n", "layer2.2.bn2.weight : torch.Size([128])\n", "layer2.2.bn2.bias : <PERSON>.<PERSON>([128])\n", "layer2.2.conv3.weight : torch.Size([512, 128, 1, 1])\n", "layer2.2.bn3.weight : torch.Size([512])\n", "layer2.2.bn3.bias : torch.Size([512])\n", "layer2.3.conv1.weight : torch.Size([128, 512, 1, 1])\n", "layer2.3.bn1.weight : torch.Size([128])\n", "layer2.3.bn1.bias : <PERSON><PERSON><PERSON>([128])\n", "layer2.3.conv2.weight : torch.Size([128, 128, 3, 3])\n", "layer2.3.bn2.weight : torch.Size([128])\n", "layer2.3.bn2.bias : <PERSON>.<PERSON>([128])\n", "layer2.3.conv3.weight : torch.Size([512, 128, 1, 1])\n", "layer2.3.bn3.weight : torch.Size([512])\n", "layer2.3.bn3.bias : torch.Size([512])\n", "layer3.0.conv1.weight : torch.Size([256, 512, 1, 1])\n", "layer3.0.bn1.weight : torch.Size([256])\n", "layer3.0.bn1.bias : <PERSON>.Size([256])\n", "layer3.0.conv2.weight : torch.Size([256, 256, 3, 3])\n", "layer3.0.bn2.weight : torch.Size([256])\n", "layer3.0.bn2.bias : <PERSON>.Size([256])\n", "layer3.0.conv3.weight : torch.Size([1024, 256, 1, 1])\n", "layer3.0.bn3.weight : torch.Size([1024])\n", "layer3.0.bn3.bias : <PERSON><PERSON>Si<PERSON>([1024])\n", "layer3.0.downsample.0.weight : torch.Size([1024, 512, 1, 1])\n", "layer3.0.downsample.1.weight : torch.Size([1024])\n", "layer3.0.downsample.1.bias : torch.Size([1024])\n", "layer3.1.conv1.weight : torch.Size([256, 1024, 1, 1])\n", "layer3.1.bn1.weight : torch.Size([256])\n", "layer3.1.bn1.bias : <PERSON>.Size([256])\n", "layer3.1.conv2.weight : torch.Size([256, 256, 3, 3])\n", "layer3.1.bn2.weight : torch.Size([256])\n", "layer3.1.bn2.bias : <PERSON>.Size([256])\n", "layer3.1.conv3.weight : torch.Size([1024, 256, 1, 1])\n", "layer3.1.bn3.weight : torch.Size([1024])\n", "layer3.1.bn3.bias : <PERSON>.<PERSON>([1024])\n", "layer3.2.conv1.weight : torch.Size([256, 1024, 1, 1])\n", "layer3.2.bn1.weight : torch.Size([256])\n", "layer3.2.bn1.bias : <PERSON>.Size([256])\n", "layer3.2.conv2.weight : torch.Size([256, 256, 3, 3])\n", "layer3.2.bn2.weight : torch.Size([256])\n", "layer3.2.bn2.bias : <PERSON>.Size([256])\n", "layer3.2.conv3.weight : torch.Size([1024, 256, 1, 1])\n", "layer3.2.bn3.weight : torch.Size([1024])\n", "layer3.2.bn3.bias : <PERSON>.<PERSON>([1024])\n", "layer3.3.conv1.weight : torch.Size([256, 1024, 1, 1])\n", "layer3.3.bn1.weight : torch.Size([256])\n", "layer3.3.bn1.bias : <PERSON>.Size([256])\n", "layer3.3.conv2.weight : torch.Size([256, 256, 3, 3])\n", "layer3.3.bn2.weight : torch.Size([256])\n", "layer3.3.bn2.bias : <PERSON>.Size([256])\n", "layer3.3.conv3.weight : torch.Size([1024, 256, 1, 1])\n", "layer3.3.bn3.weight : torch.Size([1024])\n", "layer3.3.bn3.bias : <PERSON>.<PERSON>([1024])\n", "layer3.4.conv1.weight : torch.Size([256, 1024, 1, 1])\n", "layer3.4.bn1.weight : torch.Size([256])\n", "layer3.4.bn1.bias : <PERSON>.Size([256])\n", "layer3.4.conv2.weight : torch.Size([256, 256, 3, 3])\n", "layer3.4.bn2.weight : torch.Size([256])\n", "layer3.4.bn2.bias : <PERSON>.Size([256])\n", "layer3.4.conv3.weight : torch.Size([1024, 256, 1, 1])\n", "layer3.4.bn3.weight : torch.Size([1024])\n", "layer3.4.bn3.bias : <PERSON>.<PERSON>([1024])\n", "layer3.5.conv1.weight : torch.Size([256, 1024, 1, 1])\n", "layer3.5.bn1.weight : torch.Size([256])\n", "layer3.5.bn1.bias : <PERSON>.<PERSON>([256])\n", "layer3.5.conv2.weight : torch.Size([256, 256, 3, 3])\n", "layer3.5.bn2.weight : torch.Size([256])\n", "layer3.5.bn2.bias : <PERSON>.<PERSON>ze([256])\n", "layer3.5.conv3.weight : torch.Size([1024, 256, 1, 1])\n", "layer3.5.bn3.weight : torch.Size([1024])\n", "layer3.5.bn3.bias : <PERSON><PERSON><PERSON>([1024])\n", "layer4.0.conv1.weight : torch.Size([512, 1024, 1, 1])\n", "layer4.0.bn1.weight : torch.Size([512])\n", "layer4.0.bn1.bias : torch.Size([512])\n", "layer4.0.conv2.weight : torch.Size([512, 512, 3, 3])\n", "layer4.0.bn2.weight : torch.Size([512])\n", "layer4.0.bn2.bias : torch.Size([512])\n", "layer4.0.conv3.weight : torch.Size([2048, 512, 1, 1])\n", "layer4.0.bn3.weight : torch.Size([2048])\n", "layer4.0.bn3.bias : <PERSON>.<PERSON>([2048])\n", "layer4.0.downsample.0.weight : torch.Size([2048, 1024, 1, 1])\n", "layer4.0.downsample.1.weight : torch.Size([2048])\n", "layer4.0.downsample.1.bias : torch.Size([2048])\n", "layer4.1.conv1.weight : torch.Size([512, 2048, 1, 1])\n", "layer4.1.bn1.weight : torch.Size([512])\n", "layer4.1.bn1.bias : torch.Size([512])\n", "layer4.1.conv2.weight : torch.Size([512, 512, 3, 3])\n", "layer4.1.bn2.weight : torch.Size([512])\n", "layer4.1.bn2.bias : torch.Size([512])\n", "layer4.1.conv3.weight : torch.Size([2048, 512, 1, 1])\n", "layer4.1.bn3.weight : torch.Size([2048])\n", "layer4.1.bn3.bias : <PERSON>.<PERSON>([2048])\n", "layer4.2.conv1.weight : torch.Size([512, 2048, 1, 1])\n", "layer4.2.bn1.weight : torch.Size([512])\n", "layer4.2.bn1.bias : torch.Size([512])\n", "layer4.2.conv2.weight : torch.Size([512, 512, 3, 3])\n", "layer4.2.bn2.weight : torch.Size([512])\n", "layer4.2.bn2.bias : torch.Size([512])\n", "layer4.2.conv3.weight : torch.Size([2048, 512, 1, 1])\n", "layer4.2.bn3.weight : torch.Size([2048])\n", "layer4.2.bn3.bias : <PERSON>.<PERSON>([2048])\n", "fc.weight : torch.Size([1000, 2048])\n", "fc.bias : torch.<PERSON>ze([1000])\n"]}], "source": ["from torchvision import models\n", "# from torchsummary import summary\n", "import torch\n", "from torchvision.models.resnet import resnet50\n", "device = torch.device('cpu')\n", "\n", "model = resnet50(pretrained=False, progress=True)   # 创建模型\n", "model.load_state_dict(torch.load(\"resnet50.pth\",map_location='cpu')) # 加载模型参数\n", "\n", "# 查看模型结构\n", "print(model)\n", "\n", "# 查看模型参数\n", "for name, parameters in model.named_parameters():\n", "    print(name, ':', parameters.size())\n"]}, {"cell_type": "code", "execution_count": 13, "id": "9c473188-ec09-499f-9e11-2980f96b57f0", "metadata": {}, "outputs": [], "source": ["\n", "model.eval()   # 转为推理模式\n", "example_input = torch.rand(1,3,224,224)\n", "traced_model = torch.jit.trace(model, example_input)\n", "traced_model.save('./resnet50_all.pt')\n", "\n", "# model=torch.load('resnet50_all.pt')  # 加载全部模型\n"]}, {"cell_type": "code", "execution_count": 17, "id": "33ba2e85-5e6d-412a-9d2d-1a95dbf062f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://mirrors.tencent.com/pypi/simple/, https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple\n", "Requirement already satisfied: torch-model-archiver in /usr/local/lib/python3.8/dist-packages (0.6.0)\n", "Requirement already satisfied: future in /usr/local/lib/python3.8/dist-packages (from torch-model-archiver) (0.18.2)\n", "Requirement already satisfied: enum-compat in /usr/local/lib/python3.8/dist-packages (from torch-model-archiver) (0.0.3)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\n", "\u001b[33mWARNING: You are using pip version 21.3.1; however, version 22.2 is available.\n", "You should consider upgrading via the '/usr/bin/python -m pip install --upgrade pip' command.\u001b[0m\n"]}], "source": ["# 将pt模型转为服务包\n", "! pip install torch-model-archiver\n", "! mkdir 20220801\n", "! torch-model-archiver --model-name resnet50 --version 1.0 --handler image_classifier --serialized-file resnet50_all.pt --export-path ./20220801 -f\n", "\n", "# http 输入输出 https://pytorch.org/serve/default_handlers.html\n"]}, {"cell_type": "code", "execution_count": 5, "id": "f1b407f3-ba66-46f5-a5e1-d23d4f152f3e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/bin/bash: unzip: command not found\n"]}], "source": ["# 看看压缩后文件\n", "! mv resnet50.mar resnet50.zip\n", "! unzip resnet50.zip"]}, {"cell_type": "code", "execution_count": null, "id": "74f9ecf1-8fd9-4e53-9a32-5c4500b003f6", "metadata": {}, "outputs": [], "source": ["! pip install requests\n", "# 客户端请求服务端\n", "# # pytorch\n", "import requests\n", "import base64\n", "SERVER_URL = 'http://xx.xx.xx.xx:8080/predictions/resnet50'\n", "# IMAGE_PATH = 'smallcat.jpg'\n", "# files = {'data': open(IMAGE_PATH, 'rb')}\n", "image_base64 ='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'\n", "files={'data':base64.b64decode(image_base64)}\n", "response = requests.post(SERVER_URL, files=files)\n", "print(response.json())\n", "print(response.content)\n", "print(response.status_code)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}