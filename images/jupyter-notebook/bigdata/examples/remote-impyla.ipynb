{"cells": [{"cell_type": "markdown", "id": "2e9ff853-1e15-4889-beb6-58a5c00ed0b1", "metadata": {}, "source": ["### 介绍\n", "Python连接Impala\n", "### 安装依赖\n", "```\n", "pip install impyla\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "64b7006b-fd16-4957-814a-ef9231338733", "metadata": {}, "outputs": [], "source": ["from impala.dbapi import connect\n", "conn = connect(host='*********',port = 10000,auth_mechanism='NOSASL')\n", "cur=conn.cursor()\n", "cur.execute('show databases')\n", "print(cur.fetchall())\n", "cur.close()\n", "conn.close()"]}, {"cell_type": "code", "execution_count": null, "id": "72c28438-0355-47a6-82be-e321b4f358fc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.14"}}, "nbformat": 4, "nbformat_minor": 5}