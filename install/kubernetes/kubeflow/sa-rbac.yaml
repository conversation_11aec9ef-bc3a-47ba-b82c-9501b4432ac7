
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubeflow-admin
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: jupyter-user
  namespace: jupyter
---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: jupyter-user
rules:
#- apiGroups: ["*"]
#  resources: ["tfjobs",'xgbjobs','pytorchjobs','inferenceservices','pods','services','pods/exec','pods/log']
#  verbs: ["create",'delete','list','get','watch']
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: jupyter-user
  namespace: pipeline      # 生效空间
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: jupyter-user   # 仅pipeline空间可以有权限
subjects:
- kind: ServiceAccount
  name: jupyter-user
  namespace: jupyter

