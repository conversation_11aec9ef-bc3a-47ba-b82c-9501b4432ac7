apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: prometheus-pushgateway   # 这里必须是k8s-app  不然prometheus采集不了该资源
  name: prometheus-pushgateway
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
    #因为 Prometheus 配置 pushgateway 的时候，也会指定 job 和 instance, 但是它只表示 pushgateway 实例，不能真正表达收集数据的含义。所以在 prometheus 中配置 pushgateway 的时候，需要添加 
    honorLabels: true
#    kubernetes_sd_config:    # 不是这种格式
#    - role: service      # 不是这种格式
#    metricRelabelings:
#    - action: keep
#      regex: pushgateway
#      sourceLabels:
#      - __meta_kubernetes_service_annotation_prometheus_io_probe
    port: http    # 端口号只能是字符串, 在pod中定义的端口name
  jobLabel: k8s-app
  namespaceSelector:
    matchNames:
    - monitoring
  selector:
    matchLabels:
      k8s-app: prometheus-pushgateway   # 匹配service
