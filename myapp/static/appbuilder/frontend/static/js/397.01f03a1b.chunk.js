"use strict";(self.webpackChunkkubeflow_frontend=self.webpackChunkkubeflow_frontend||[]).push([[397],{54189:function(e,n,t){t.d(n,{Z:function(){return d}});var a=t(1413),r=t(29439),i=t(4519),l=t(1631),c=t(93849),o=t(81748),s=t(2556),u={height:300};function d(e){var n=(0,i.useState)(),t=(0,r.Z)(n,2),d=t[0],_=t[1],m=Math.random().toString(36).substring(2),f=(0,o.$G)(),p=f.t,h=(f.i18n,{});return(0,i.useEffect)((function(){var n=document.getElementById(m);if(n){var t=l.S1(n);t.setOption((0,a.Z)((0,a.Z)({},h),e.option)),d||_(t)}}),[e.option,e.data]),(0,s.jsx)(c.Z,{spinning:e.loading,children:(0,s.jsxs)("div",{className:"chart-container",children:[(0,s.jsx)("div",{id:m,style:(0,a.Z)((0,a.Z)({},u),e.style)}),e.isNoData?(0,s.jsx)("div",{className:"chart-nodata",children:(0,s.jsx)("div",{children:p("\u6682\u65e0\u6570\u636e")})}):null]})})}},6857:function(e,n,t){t.d(n,{Z:function(){return o}});var a=t(29439),r=t(7517),i=t(10007),l=t(4519),c=t(2556);function o(e){var n,t=Math.random().toString(36).substring(2),o=(0,l.useState)(e.options||[]),s=(0,a.Z)(o,2),u=s[0],d=s[1],_=(0,l.useState)(e.value||""),m=(0,a.Z)(_,2),f=m[0],p=m[1];(0,l.useEffect)((function(){var n=e.isOpenSearchMatch?(e.options||[]).filter((function(e){return-1!==e.indexOf(f)})):e.options||[];d(n)}),[e.options]),(0,l.useEffect)((function(){p(e.value||"")}),[e.value]);var h=function(n){p(n),e.onChange&&e.onChange(n)},x=function(e){var n=f,t=e.indexOf(f);if(-1===t)return(0,c.jsx)("span",{children:e});var a=e.substring(0,t),r=e.substring(t+f.length);return(0,c.jsxs)("span",{children:[a,(0,c.jsx)("span",{className:"highlight",children:n}),r]})},j=function(e){var n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500;return function(){for(var a=arguments.length,r=new Array(a),i=0;i<a;i++)r[i]=arguments[i];clearTimeout(n),n=setTimeout((function(){e&&e.apply(null,[].concat(r))}),t)}}(e.onScrollButtom);return(0,c.jsxs)("div",{className:"select-down-modern",children:[e.labelName?(0,c.jsx)("label",{htmlFor:t,className:"pb4 mb0 fs12 d-b",children:e.labelName}):null,(0,c.jsxs)("div",{className:"p-r d-f ac",style:{width:e.width||"100%"},children:[(0,c.jsx)(i.Z,{style:{width:"100%"},disabled:e.disabled,id:t,placeholder:e.placeholder||"",maxLength:e.maxLength||200,onChange:function(e){return h(e.target.value)},onKeyPress:function(t){13===t.nativeEvent.keyCode&&(n.blur&&n.blur(),e.onSearch&&e.onSearch(t.currentTarget.value))},value:f,ref:function(e){return n=e}}),(0,c.jsx)(r.Z,{className:"p-a r0 mr8"})]}),u.length?(0,c.jsxs)("ul",{className:"select-option shadow",onScroll:function(n){n.stopPropagation();var t=n.currentTarget,a=t.scrollTop,r=t.clientHeight;t.scrollHeight-r-a<20&&e.onScrollButtom&&j()},style:{maxHeight:"".concat(e.maxHeight,"px")},children:[e.loading?(0,c.jsx)("div",{className:"p-s z9 ta-r",style:{right:"".concat(0,"px"),top:"".concat(0,"px")},children:(0,c.jsx)("div",{className:"d-il p-a",style:{right:"".concat(8,"px"),top:"".concat(0,"px")}})}):null,u.map((function(n,t){return(0,c.jsx)("li",{className:"ellip1",onMouseDown:function(){return function(n){h(n),e.onClick&&e.onClick(n)}(n)},children:x(n)},t)}))]}):null]})}},76877:function(e,n,t){t.d(n,{Z:function(){return O}});var a=t(93433),r=t(29439),i=t(1413),l=t(45987),c=t(4519),o=t(35492),s=t(20011),u=t(79551),d=t(28532),_=t(12513),m=t(80211),f=t(10089),p=t(2704),h=t(25738),x=t(1126),j=t.p+"static/media/emptyBg.15fdf5f39309784ac66e.png",Z=t(74308),v=t(81748),g=t(2556),y=["onResize","width"],b=t(17972),E=function(e){var n=e.onResize,t=e.width,a=(0,l.Z)(e,y);return t?(0,g.jsx)(Z.Resizable,{width:t,height:0,handle:(0,g.jsx)("span",{className:"react-resizable-handle",onClick:function(e){e.stopPropagation()}}),onResize:n,draggableOpts:{enableUserSelectHack:!1},children:(0,g.jsx)("th",(0,i.Z)((0,i.Z)({},a),{},{style:(0,i.Z)((0,i.Z)({},null===a||void 0===a?void 0:a.style),{},{userSelect:"none"})}))}):(0,g.jsx)("th",(0,i.Z)({},a))},O=function(e){var n=(0,c.useState)(!1),t=(0,r.Z)(n,2),l=t[0],Z=t[1],y=(0,c.useState)({header:[],data:[]}),O=(0,r.Z)(y,2),C=O[0],M=O[1],S=(0,c.useState)([]),P=(0,r.Z)(S,2),w=P[0],k=P[1],D=(0,c.useState)(e.columns),N=(0,r.Z)(D,2),T=N[0],I=N[1],L=function(n){return function(t,r){var l=r.size;if(!(l.width<100)){var c=(0,a.Z)(T);c[n]=(0,i.Z)((0,i.Z)({},c[n]),{},{width:l.width});var o=c.reduce((function(e,n){return e+n.width||100}),0)+200;localStorage.setItem(e.tableKey||"",JSON.stringify(c)),K((0,i.Z)((0,i.Z)({},U),{},{x:o})),I(c)}}},R=T.map((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},{width:e.width||200,onHeaderCell:function(e){return{width:e.width,onResize:L(n)}}})})),A=(0,c.useState)(e.scroll),B=(0,r.Z)(A,2),U=B[0],K=B[1],W=(0,v.$G)(),F=W.t;W.i18n;(0,c.useEffect)((function(){I(e.columns)}),[e.columns]),(0,c.useEffect)((function(){K(e.scroll)}),[e.scroll]),(0,c.useEffect)((function(){if(e.dataSource){var n=e.columns.filter((function(e){return~w.indexOf(e.dataIndex)}));H(n,e.dataSource)}}),[e.dataSource,e.columns]);var H=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,t=e.map((function(e){return e.dataIndex})).filter((function(e){return"handle"!==e})),a=e.map((function(e){return e.title})).filter((function(e){return e!==F("\u64cd\u4f5c")})),r=[];n.forEach((function(e){var n={};t.map((function(t){var a=e[t];n[t]=a||""})),r.push(n)})),M({header:a,data:r})},V=function(){var e=C.header,n=C.data,t="";return e.length&&n.length?(t="|"+e.join("|")+"|\n",n.forEach((function(e){var n=Object.values(e).map((function(e){return""===e?" ":e}));t=t+"|"+n.join("|")+"|\n"}))):t="",t},q=function(){var e=C.header,n=C.data,t="";return e.length&&n.length?(t=e.join("\t")+"\n",n.forEach((function(e){var n=Object.values(e).map((function(e){return""===e?" ":e}));t=t+n.join("\t")+"\n"}))):t="",t};return(0,g.jsxs)(s.Z,{className:"tablebox",direction:"vertical",size:"middle",children:[(0,g.jsxs)(u.Z,{width:1e3,maskClosable:!1,centered:!0,bodyStyle:{maxHeight:500,overflow:"auto"},visible:l,title:F("\u5bfc\u51fa\u6570\u636e"),onCancel:function(){Z(!1)},footer:null,children:[(0,g.jsxs)("div",{style:{position:"relative"},children:[(0,g.jsxs)("div",{className:"mb16",children:[(0,g.jsxs)("span",{className:"pr8",children:[F("\u9009\u62e9\u9700\u8981\u5bfc\u51fa\u7684\u5217"),"\uff1a"]}),(0,g.jsx)(d.Z.Group,{options:e.columns.map((function(e){return{label:e.title,value:e.dataIndex}})).filter((function(e){return"handle"!==e.value})),defaultValue:[],value:w,onChange:function(n){k(n);var t=e.columns.filter((function(e){return~n.indexOf(e.dataIndex)}));H(t,e.dataSource)}})]}),(0,g.jsxs)("div",{style:{position:"absolute",right:0,bottom:0},children:[(0,g.jsx)(_.Z,{size:"small",type:"link",onClick:function(){k(e.columns.map((function(e){return e.dataIndex})).filter((function(e){return"handle"!==e}))),H(e.columns,e.dataSource)},children:F("\u5168\u9009")}),(0,g.jsx)(_.Z,{size:"small",type:"link",onClick:function(){k([]),H([],e.dataSource)},children:F("\u53cd\u9009")})]})]}),(0,g.jsxs)(m.Z,{children:[(0,g.jsx)(m.Z.TabPane,{tab:"Wiki\u683c\u5f0f",children:(0,g.jsx)(b,{text:V(),onCopy:function(){return f.ZP.success(F("\u5df2\u590d\u5236\u5230\u7c98\u8d34\u677f"))},children:(0,g.jsx)("pre",{style:{cursor:"pointer",minHeight:100},children:(0,g.jsx)("code",{children:V()})})})},"jira"),(0,g.jsx)(m.Z.TabPane,{tab:"Text\u683c\u5f0f",children:(0,g.jsx)(b,{text:q(),onCopy:function(){return f.ZP.success(F("\u5df2\u590d\u5236\u5230\u7c98\u8d34\u677f"))},children:(0,g.jsx)("pre",{style:{cursor:"pointer",minHeight:100},children:(0,g.jsx)("code",{children:q()})})})},"test")]})]}),e.titleNode||e.buttonNode||!e.cancelExportData?(0,g.jsxs)(o.Z,{justify:"space-between",align:"middle",children:[(0,g.jsx)(p.Z,{children:(0,g.jsx)(s.Z,{align:"center",children:e.titleNode})}),(0,g.jsx)(p.Z,{children:(0,g.jsxs)(s.Z,{align:"center",children:[e.buttonNode,e.cancelExportData?null:(0,g.jsx)(_.Z,{style:{marginLeft:6},onClick:function(){return Z(!0)},children:F("\u5bfc\u51fa\u6570\u636e")})]})})]}):null,(0,g.jsx)(h.ZP,{renderEmpty:function(){return(0,g.jsxs)(o.Z,{justify:"center",align:"middle",style:{height:360,flexDirection:"column"},children:[(0,g.jsx)("img",{src:j,style:{width:266},alt:""}),(0,g.jsx)("div",{children:F("\u6682\u65e0\u6570\u636e")})]})},children:(0,g.jsx)(x.Z,{size:e.size||"middle",rowKey:e.rowKey?e.rowKey:"id",dataSource:e.dataSource,components:{header:{cell:E}},columns:R,pagination:!1!==e.pagination&&(0,i.Z)({},e.pagination),scroll:U,loading:e.loading,onChange:e.onChange,rowSelection:e.rowSelection})})]})}},54627:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){__webpack_require__.d(__webpack_exports__,{Z:function(){return TabsDetail}});var _home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__(93433),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(74165),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__(4942),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__(1413),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(15861),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(29439),antd__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__(35492),antd__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__(2704),antd__WEBPACK_IMPORTED_MODULE_13__=__webpack_require__(80211),antd__WEBPACK_IMPORTED_MODULE_14__=__webpack_require__(12513),react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(4519),_EchartCore_EchartCore__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(54189),_TabsDetail_less__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(48773),marked__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(99625),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(2556);function TabsDetail(props){var _useState=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}),_useState2=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_5__.Z)(_useState,2),markdownMap=_useState2[0],setMarkdownMap=_useState2[1];(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((function(){var e=function(){var e=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__.Z)((0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__.Z)().mark((function e(n,t,a){var r;return(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,marked__WEBPACK_IMPORTED_MODULE_3__.TU)(a);case 2:r=e.sent,setMarkdownMap((function(e){return(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__.Z)((0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__.Z)({},e),{},(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_9__.Z)({},n,(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__.Z)((0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__.Z)({},e[n]||{}),{},(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_9__.Z)({},t,r))))}));case 4:case"end":return e.stop()}}),e)})));return function(n,t,a){return e.apply(this,arguments)}}();props.data.forEach((function(n){n.content.forEach((function(t){"markdown"===t.groupContent.type&&e(n.tabName,t.groupName,t.groupContent.value)}))}))}),[props.data]);var handleGroupContent=function(e,n,t,a){switch(e){case"map":return renderMapComponent(n);case"echart":return renderEchart(n);case"text":return renderMapText(n);case"iframe":return renderMapIframe(n);case"html":return renderHtml(n);case"markdown":return renderMarkdown(t,a);default:return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span",{style:{wordBreak:"break-word",whiteSpace:"pre-wrap"},children:n})}},renderHtml=function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{dangerouslySetInnerHTML:{__html:e}})},renderMapComponent=function(e){var n=Object.entries(e).reduce((function(e,n){var t=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_5__.Z)(n,2),a=t[0],r=t[1];return[].concat((0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__.Z)(e),[{label:a,value:r}])}),[]);return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{className:"bg-title p16",children:n.map((function(e,n){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__.Z,{className:"mb8 w100",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__.Z,{span:8,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{className:"ta-l",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("strong",{children:[e.label,"\uff1a"]})})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__.Z,{span:16,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span",{style:{wordBreak:"break-word",whiteSpace:"pre-wrap"},children:e.value})})]},"tabsDetailItem_".concat(n))}))})},renderEchart=function renderEchart(data){var currentOps={};return eval("currentOps=".concat(data)),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{className:"bg-title p16",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_EchartCore_EchartCore__WEBPACK_IMPORTED_MODULE_1__.Z,{option:currentOps,loading:!1})})},renderMapText=function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{className:"p16 bg-title",style:{wordBreak:"break-word",whiteSpace:"pre-wrap"},children:e})},renderMapIframe=function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("iframe",{src:e.url,allowFullScreen:!0,allow:"microphone;camera;midi;encrypted-media;",className:"w100 fade-in",style:{border:0,height:500}})},renderMarkdown=function(e,n){var t,a=null===(t=markdownMap[e])||void 0===t?void 0:t[n];return a?(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{dangerouslySetInnerHTML:{__html:a}}):null};return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment,{children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__.Z,{className:"tabsdetail-tab",children:props.data.map((function(e,n){var t;return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__.Z.TabPane,{tab:e.tabName,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div",{className:"d-f fd-c jc-b h100",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{className:"flex1",children:e.content.map((function(n,t){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div",{className:"mb32",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{className:"fs16 mb16 bor-l b-theme pl4",style:{borderLeftWidth:2},dangerouslySetInnerHTML:{__html:n.groupName}}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{children:handleGroupContent(n.groupContent.type,n.groupContent.value,e.tabName,n.groupName)})]},"tabsGroup".concat(t))}))}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div",{className:"tabsdetail-tool",children:null===(t=e.bottomButton)||void 0===t?void 0:t.map((function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__.Z,{className:"mr12 icon-tool-wrapper",onClick:function(){window.open(e.url,"blank")},children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span",{className:"icon-tool",dangerouslySetInnerHTML:{__html:e.icon}}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span",{className:"ml6",children:e.text})]})}))})]})},"tabsDetailTab".concat(n))}))})})}},56051:function(e,n,t){t.r(n),t.d(n,{default:function(){return Ee}});var a=t(93433),r=t(4942),i=t(1413),l=t(29439),c=t(4519),o=t(10388),s=t(20011),u=t(42496),d=t(34519),_=t(79551),m=t(10089),f=t(1449),p=t(93849),h=t(12513),x=t(2704),j=t(86457),Z=t(1140),v=t(82085),g=t(37167),y=t(72577),b=t(35492),E=t(2556),O=y.Z.Title,C=function(e){var n={position:"sticky",top:0};return(0,E.jsxs)(b.Z,{className:"title-header",justify:"space-between",align:"middle",style:e.noBorderBottom?(0,i.Z)({borderBottom:"none"},n):n,children:[(0,E.jsxs)("div",{children:[(0,E.jsx)(O,{className:"d-il mr12",level:5,style:{marginBottom:10},children:e.title}),(0,E.jsx)("div",{className:"d-il",children:e.breadcrumbs})]}),(0,E.jsx)(x.Z,{children:(0,E.jsx)(s.Z,{children:e.children?e.children:null})})]})},M=t(76877),S=t(43077),P=t.n(S),w=t(42335),k=t(33405),D=t(17669),N=t(33531),T=t(56713),I=t(20558),L=t(8718),R=t(48633),A=t(85351),B=t(16548),U=t(8230),K=t(81748),W=function(e){var n=(0,K.$G)(),t=n.t,a=(n.i18n,U.Z.useForm()),r=(0,l.Z)(a,1)[0],i=(0,c.useState)(),o=(0,l.Z)(i,2)[1];c.useCallback((function(){return o({})}),[]);(0,c.useEffect)((function(){e.formData&&r.setFieldsValue(e.formData)}),[e]);var s=(0,c.useState)({currentChange:{},allValues:{}}),u=(0,l.Z)(s,2),d=u[0],m=u[1];return(0,E.jsx)(_.Z,{destroyOnClose:!0,maskClosable:!1,width:e.width||680,visible:e.visible,title:e.title,okText:t("\u786e\u5b9a"),cancelText:t("\u53d6\u6d88"),onCancel:function(){r.resetFields(),e.onCancel()},onOk:function(){console.log(r.getFieldsValue()),r.validateFields().then((function(n){e.onCreate(n,r)})).catch((function(e){}))},children:(0,E.jsx)(p.Z,{spinning:e.loading,children:(0,E.jsx)(U.Z,{onValuesChange:function(e,n){m({currentChange:e,allValues:n})},labelCol:{span:5},wrapperCol:{span:19},form:r,layout:"horizontal",name:"form_in_modal",children:e.children&&"[object Function]"===Object.prototype.toString.call(e.children)?e.children(r,d):e.children})})})},F=t(30506),H=t(74165),V=t(15861),q=t(51222),z=t(10007),J=t(28501),G=t(74030),$=q.Z.Option,X=function(e){var n=U.Z.useForm(),t=(0,l.Z)(n,1)[0],r=(0,c.useState)(!1),o=(0,l.Z)(r,2),s=o[0],u=o[1],d=(0,c.useState)([]),_=(0,l.Z)(d,2),m=(_[0],_[1],function(e){return(e||[]).map((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},{indexKey:n})}))}),f=(0,c.useState)(m(e.params)),p=(0,l.Z)(f,2),j=p[0],Z=p[1],v=(0,c.useState)(m(e.params)),g=(0,l.Z)(v,2),y=g[0],O=g[1],C=(0,c.useState)(new Map),M=(0,l.Z)(C,2),S=M[0],P=M[1],w=(0,K.$G)(),k=w.t;w.i18n;(0,c.useEffect)((function(){if(e.values){var n=e.values.length?e.values:[{key:void 0,value:void 0}];t.setFieldsValue({group:n});for(var r=(0,a.Z)(y),i=0;i<r.length;i++)for(var l=0;l<n.length;l++){void 0!==n[l]&&n[l].key===r[i].name&&(r[i].used=!0)}O(r)}}),[e.values]),(0,c.useEffect)((function(){if(e.params&&e.params.length){var n=m(e.params);Z(n);for(var t=S,a=0;a<n.length;a++){var r=n[a];t.set(r.name,r)}P(t)}}),[e.params]);var N=function(){var n=(0,V.Z)((0,H.Z)().mark((function n(t){var a,r;return(0,H.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:console.log(t),a=t.group.filter((function(e){return!!e.key})),r=a.map((function(e){return{key:e.key,value:e.value}})),e.onChange(r);case 4:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}(),T=function(e){var n=t.getFieldValue(["group",e,"key"]);if(!n)return(0,E.jsx)(z.Z,{style:{width:"65%"},onPressEnter:function(){return L()}});var a=S.get(n);if("input"===(null===a||void 0===a?void 0:a.type))return(0,E.jsx)(z.Z,{style:{width:"65%"},defaultValue:a.defalutValue,placeholder:a.placeHolder,onPressEnter:function(){return L()}});if("select"===(null===a||void 0===a?void 0:a.type)){var r=(null===a||void 0===a?void 0:a.option)||[];return(0,E.jsx)(q.Z,{style:{width:"65%"},dropdownMatchSelectWidth:500,showSearch:!0,mode:"label"===n?"multiple":void 0,optionFilterProp:"label",options:r.map((function(e){return{label:e.label,value:e.value}}))})}},L=function(){t.validateFields()};return(0,E.jsxs)(U.Z,{className:"cmdb-mixsearch bg-title",form:t,onFinish:N,initialValues:{group:[{key:void 0,value:void 0}]},children:[(0,E.jsxs)(b.Z,{className:"cmdb-mixsearch-content",gutter:16,style:(0,i.Z)({marginLeft:0,marginRight:0},s?{height:70}:{height:"auto"}),children:[(0,E.jsx)(U.Z.List,{name:"group",children:function(e,n){var r=n.add,i=n.remove;return(0,E.jsxs)(E.Fragment,{children:[e.map((function(e,n){return(0,E.jsx)(x.Z,{span:8,children:(0,E.jsxs)(b.Z,{align:"middle",gutter:8,children:[(0,E.jsx)(x.Z,{className:"cmdb-mixsearch-group",children:(0,E.jsxs)(z.Z.Group,{compact:!0,children:[(0,E.jsx)(U.Z.Item,{noStyle:!0,name:[e.name,"key"],rules:[{required:!1,message:k("\u8bf7\u9009\u62e9key")}],children:(0,E.jsx)(q.Z,{style:{width:"35%"},placeholder:k("\u8bf7\u9009\u62e9"),onChange:function(e){!function(e){for(var n=(0,a.Z)(y),r=(t.getFieldValue("group")||[]).filter((function(e){return!!e})).map((function(e){return e.key})),i=0;i<n.length;i++){var l=n[i];l.name===e?n[i].used=!0:r.includes(l.name)||(n[i].used=!1)}O(n)}(e)},children:y.map((function(e,n){return(0,E.jsx)($,{style:{display:e.used?"none":"inherit"},value:e.name,children:e.title||e.name},"mixSearch_".concat(e.name,"_").concat(n))}))})}),(0,E.jsx)(U.Z.Item,{noStyle:!0,shouldUpdate:!0,name:[e.name,"value"],rules:[{required:!1,message:k("\u8bf7\u586b\u5199value")}],children:T(n)})]})}),(0,E.jsx)(x.Z,{className:"cmdb-mixsearch-delete",onClick:function(){var e=(t.getFieldValue("group")||[]).map((function(e){return e?e.key:void 0})),r=(0,a.Z)(y);if(e[n])for(var l=0;l<r.length;l++){r[l].name===e[n]&&(r[l].used=!1)}O(r),i(n)},children:(0,E.jsx)(J.Z,{})})]})},"mixSearch_".concat(e.key,"_").concat(n))})),j.length!==e.length&&(0,E.jsx)(x.Z,{className:"cmdb-mixsearch-add d-il",onClick:function(){r()},children:(0,E.jsx)(I.Z,{})})]})}}),(0,E.jsx)(x.Z,{flex:1,children:(0,E.jsx)(b.Z,{justify:"end",children:(0,E.jsx)(h.Z,{type:"primary",htmlType:"submit",children:k("\u67e5\u8be2")})})})]}),(0,E.jsx)(b.Z,{className:"cmdb-mixsearch-collapsed",children:(0,E.jsx)(b.Z,{onClick:function(){u(!s)},justify:"center",align:"middle",children:s?(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(x.Z,{children:k("\u5c55\u5f00")}),(0,E.jsx)(x.Z,{children:(0,E.jsx)(D.Z,{})})]}):(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(x.Z,{children:k("\u6536\u8d77")}),(0,E.jsx)(x.Z,{children:(0,E.jsx)(G.Z,{})})]})})})]})},Q=t(45987),Y=t(97709),ee=t(73031),ne=t(69994),te=t(68553),ae=t(75262),re=t(65505),ie=t(74860),le=t(6857),ce=(t(90746),t(32085)),oe=t(3571),se=t(22914),ue=t(78602),de=t(96903);function _e(e){var n=(0,c.useState)(!1),t=(0,l.Z)(n,2),r=(t[0],t[1],(0,c.useState)(!1)),i=(0,l.Z)(r,2),o=(i[0],i[1]),s=(0,c.useState)(""),u=(0,l.Z)(s,2),d=(u[0],u[1],(0,c.useState)([])),_=(0,l.Z)(d,2),f=(_[0],_[1],(0,c.useState)(!0)),p=(0,l.Z)(f,2),h=(p[0],p[1],(0,c.useState)([])),x=(0,l.Z)(h,2),j=x[0],Z=x[1];var v=function(n,t,r){var i=function(e){var n=null;return void 0!=window.createObjectURL?n=window.createObjectURL(e):void 0!=window.URL?n=window.URL.createObjectURL(e):void 0!=window.webkitURL&&(n=window.webkitURL.createObjectURL(e)),n}(n),l=Math.random().toString(36).substring(2);return"video"===r?(0,E.jsxs)("div",{className:"p-r",children:[(0,E.jsx)("span",{onClick:function(){var n=(0,a.Z)(j);n.splice(t,1),Z(n),e.onChange&&e.onChange(n)},className:"d-il p-a plr8 ptb2 bg-fail",style:{top:0,right:0,borderBottomLeftRadius:6,zIndex:9},children:(0,E.jsx)(J.Z,{style:{color:"#fff"}})}),(0,E.jsx)("video",{className:"w100 mb8",src:i,controls:!0})]},l):"audio"===r?(0,E.jsxs)("div",{className:"d-f ac mb8",children:[(0,E.jsx)("audio",{className:"w100 flex1",src:i,controls:!0}),(0,E.jsx)("span",{onClick:function(){var n=(0,a.Z)(j);n.splice(t,1),Z(n),e.onChange&&e.onChange(n)},className:"d-il plr8 ptb2 bg-fail",style:{borderRadius:6},children:(0,E.jsx)(J.Z,{style:{color:"#fff"}})})]},l):n};return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)("div",{children:j.map((function(n,t){return v(n,t,e.filetype||"file")}))}),(0,E.jsxs)(de.Z.Dragger,{fileList:j,showUploadList:!1,customRequest:function(n){console.log("options.file",n.file);var t=[].concat((0,a.Z)(j),[n.file]);console.log("tarList",t),Z(t),Promise.all(t.map((function(e){return n=e,console.log("file2Bin",n),new Promise((function(e,t){if(n){n.name.replace(/.+\./,""),n.name;var a=new FileReader;a.readAsDataURL(n),a.onload=function(){e(a.result)}}else t(void 0)}));var n}))).then((function(n){console.log(n),e.onChange&&e.onChange(n)}))},beforeUpload:function(n){var t,a=e.maxCount||1;if(j.length>=a)return m.ZP.error("\u8d85\u51fa\u6587\u4ef6\u6570\u91cf\u9650\u5236"),!1;var r=null===(t=e.format)||void 0===t?void 0:t.includes(n.type);r||m.ZP.error("\u6587\u4ef6\u683c\u5f0f\u9519\u8bef");var i=n.size<(e.maxSize||2097152);return i||m.ZP.error("\u6587\u4ef6\u5927\u5c0f\u9650\u5236"),r&&i},onChange:function(n){if(console.log(n),"uploading"!==n.file.status)return"done"===n.file.status&&(o(!1),Z(n.fileList),e.onChange&&e.onChange(n.fileList)),"removed"===n.file.status?(Z(n.fileList),void(e.onChange&&e.onChange(n.fileList))):void 0;o(!0)},children:[(0,E.jsxs)("p",{className:"ant-upload-drag-icon",children:["file"!==e.filetype&&e.filetype?null:(0,E.jsx)(oe.Z,{}),"video"===e.filetype?(0,E.jsx)(se.Z,{}):null,"audio"===e.filetype?(0,E.jsx)(ue.Z,{}):null]}),(0,E.jsx)("p",{className:"ant-upload-text",children:"\u70b9\u51fb\u6216\u62d6\u62fd\u6587\u4ef6\u4e0a\u4f20"})]})]})}var me=t(57796),fe=t(75526),pe=t(41660);function he(e){var n=(0,K.$G)(),t=n.t,a=(n.i18n,(0,c.useState)(!0)),r=(0,l.Z)(a,2),i=r[0],o=r[1];return(0,E.jsxs)("div",{children:[(0,E.jsx)(me.ZP,{value:function(e){try{var n=JSON.parse(e);return JSON.stringify(n,null,2)}catch(t){return e}}(e.value||"{}"),onChange:function(n){return function(n){try{var t=JSON.parse(n),a=JSON.stringify(t,null,2);e.onChange&&e.onChange(a),o(!0)}catch(r){o(!1),e.onChange&&e.onChange(n)}}(n)},readOnly:e.readOnly,maxHeight:"300px",extensions:[(0,fe.AV)(),pe.tk.lineWrapping],placeholder:e.placeholder,basicSetup:{lineNumbers:!1}}),!i&&(0,E.jsx)("div",{style:{color:"red",marginTop:"10px"},children:t("json\u683c\u5f0f\u9519\u8bef")})]})}var xe=["key","name"];function je(e){return e.reduce((function(e,n){return e+(n||"").split("").reduce((function(e,n){return e+n.charCodeAt(0)}),0)}),0)}function Ze(e){var n=e.dataOptions,t=(0,K.$G)(),s=t.t,u=(t.i18n,(0,c.useState)(0)),d=(0,l.Z)(u,2),_=d[0],m=d[1],f=(0,c.useState)(e.config),p=(0,l.Z)(f,2),x=p[0],j=p[1],Z=(0,c.useRef)(e.config),v=function(e){Z.current=e,j(e)},g=(0,c.useState)(e.configGroup),y=(0,l.Z)(g,2),b=y[0],O=y[1],C=(0,c.useRef)(e.configGroup),M=function(e){C.current=e,O(e)},S=function(n,t){var l=function(n,t){return t.filter((function(e){return e.dep.includes(n)})).map((function(n){var t=je(n.dep.map((function(n){var t;return null===(t=e.form)||void 0===t?void 0:t.getFieldValue(n)})).filter((function(e){return!(void 0===e||null===e)})));return{effect:n.effect,option:n.effectOption[t]||[]}}))}(n,t);l.forEach((function(n){var t;null===(t=e.form)||void 0===t||t.setFieldsValue((0,r.Z)({},n.effect,void 0)),function(e,n){var t=Z.current?(0,a.Z)(Z.current):[];if(t)for(var r=0;r<t.length;r++){var l=t[r];l.name===e&&(t[r]=(0,i.Z)((0,i.Z)({},l),n))}v(t)}(n.effect,{options:n.option}),function(e,n){for(var t=C.current?(0,a.Z)(C.current):[],r=0;r<t.length;r++){for(var l=(0,a.Z)(t[r].config),c=0;c<l.length;c++){var o=l[c];o.name===e&&(l[c]=(0,i.Z)((0,i.Z)({},o),n))}t[r]=(0,i.Z)((0,i.Z)({},t[r]),{},{config:l})}M(t)}(n.effect,{options:n.option})}))};(0,c.useEffect)((function(){if(e.formChangeRes&&e.linkageConfig){var n=e.formChangeRes.currentChange;S(Object.keys(n)[0],e.linkageConfig)}}),[e.formChangeRes]),(0,c.useEffect)((function(){var n;v(e.config),M(e.configGroup);var t=(null===(n=e.form)||void 0===n?void 0:n.getFieldsValue())||{};Object.entries(t).forEach((function(n){var t=(0,l.Z)(n,2),a=t[0];void 0!==t[1]&&S(a,e.linkageConfig||[])}))}),[e.configGroup,e.config,n]);var w=function(){m(_+1)},k=function(e,n){return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:e.rules,initialValue:e.defaultValue,extra:(0,E.jsxs)(E.Fragment,{children:[e.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null]})},n),{},{children:(0,E.jsx)(z.Z,{disabled:e.disable,placeholder:e.placeHolder||"".concat(s("\u8bf7\u9009\u62e9")).concat(e.label)})}),"dynamicForm_".concat(e.name))},D=function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};switch(n.type){case"input":case"match-input":return k(n,t);case"input-select":return function(n,t){var a=(n.options||[]).map((function(e){return e.label}));return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:n.label,name:n.name,rules:n.rules,initialValue:n.defaultValue,extra:(0,E.jsxs)(E.Fragment,{children:[n.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:n.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,n.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:n.description}}):null]})},t),{},{children:(0,E.jsx)(le.Z,{onClick:function(t){n.data.retry_info&&e.onRetryInfoChange&&e.onRetryInfoChange(t)},isOpenSearchMatch:!0,disabled:n.disable,placeholder:"".concat(s("\u8bf7\u9009\u62e9")).concat(n.label),options:a})}),"dynamicForm_".concat(n.name))}(n,t);case"cascader":return function(e,n){var t=e.options||[];return console.log(t),(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:e.rules,initialValue:e.defaultValue,extra:(0,E.jsxs)(E.Fragment,{children:[e.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null]})},n),{},{children:(0,E.jsx)(ne.Z,{placeholder:"select",options:t})}),"dynamicForm_".concat(e.name))}(n,t);case"textArea":return function(e,n){return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:e.rules,initialValue:e.defaultValue,extra:(0,E.jsxs)(E.Fragment,{children:[e.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null]})},n),{},{children:(0,E.jsx)(z.Z.TextArea,{autoSize:{minRows:4},disabled:e.disable,placeholder:e.placeHolder||"".concat(s("\u8bf7\u9009\u62e9")).concat(e.label)})}),"dynamicForm_".concat(e.name))}(n,t);case"json":return function(e,n){return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:e.rules,initialValue:e.defaultValue,extra:(0,E.jsxs)(E.Fragment,{children:[e.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null]})},n),{},{children:(0,E.jsx)(he,{readOnly:e.disable,placeholder:e.placeHolder||"".concat(s("\u8bf7\u9009\u62e9")).concat(e.label)})}),"dynamicForm_".concat(e.name))}(n,t);case"select":return function(n,t){var a=n.options||[];return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:n.label,name:n.name,rules:n.rules,initialValue:n.defaultValue,extra:(0,E.jsxs)(E.Fragment,{children:[n.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:n.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,n.description?(0,E.jsx)("span",{className:"pr4",dangerouslySetInnerHTML:{__html:n.description}}):null,n.data.isRefresh?(0,E.jsxs)("div",{className:"cp d-il",onClick:function(){e.onRetryInfoChange&&e.onRetryInfoChange()},children:[(0,E.jsx)(re.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u5237\u65b0\u5217\u8868")})]}):null]})},t),{},{children:(0,E.jsx)(ae.Z,{style:{width:"100%"},mode:n.multiple?"multiple":void 0,onChange:function(t){n.data.retry_info&&e.onRetryInfoChange&&e.onRetryInfoChange(t)},showSearch:!0,disabled:n.disable,optionFilterProp:"label",placeholder:n.placeHolder||"".concat(s("\u8bf7\u9009\u62e9")).concat(n.label),options:a})}),"dynamicForm_".concat(n.name))}(n,t);case"datePicker":return function(e,n){return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:[{required:!0,message:s("\u8bf7\u9009\u62e9\u65f6\u95f4")}],extra:(0,E.jsxs)(E.Fragment,{children:[e.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null]})},n),{},{children:(0,E.jsx)(ee.Z,{style:{width:"100%"},locale:ce.Z,showTime:!!e.data.showTime,disabledDate:function(e){return e&&e>P()().endOf("day")}})}),"dynamicForm_".concat(e.name))}(n,t);case"rangePicker":return function(e,n){return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:[{required:!0,message:s("\u8bf7\u9009\u62e9\u65f6\u95f4\u8303\u56f4")}],extra:(0,E.jsxs)(E.Fragment,{children:[e.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null]})},n),{},{children:(0,E.jsx)(ee.Z,{style:{width:"100%"},locale:ce.Z,showTime:!!e.data.showTime,disabledDate:function(e){return e&&e>P()().endOf("day")}})}),"dynamicForm_".concat(e.name))}(n,t);case"radio":return function(e,n){var t=e.options||[];return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:e.rules,initialValue:e.defaultValue,extra:(0,E.jsxs)(E.Fragment,{children:[e.data.tips?(0,E.jsx)(o.Z,{className:"mr8",placement:"bottom",title:(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.data.tips}}),children:(0,E.jsxs)("div",{className:"cp d-il",children:[(0,E.jsx)(T.Z,{style:{color:"#1672fa"}}),(0,E.jsx)("span",{className:"pl4 c-theme",children:s("\u8be6\u60c5")})]})}):null,e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null]})},n),{},{children:(0,E.jsx)(Y.ZP.Group,{options:t})}),"dynamicForm_".concat(e.name))}(n,t);case"fileUpload":return function(e,n){return(0,E.jsx)(U.Z.Item,(0,i.Z)((0,i.Z)({label:e.label,name:e.name,rules:e.rules,initialValue:e.defaultValue,extra:e.description?(0,E.jsx)("span",{dangerouslySetInnerHTML:{__html:e.description}}):null},n),{},{children:(0,E.jsx)(_e,{filetype:e.data.type,format:e.data.format,maxCount:e.data.maxCount||1})}),"dynamicForm_".concat(e.name))}(n,t);default:return null}},N=function(e){return(e||[]).map((function(e){return e.list&&e.list.length?(0,E.jsx)(U.Z.List,{name:e.name,children:function(n,t){var a=t.add,r=t.remove;return(0,E.jsxs)(E.Fragment,{children:[n.map((function(n){var t=n.key,a=n.name,l=(0,Q.Z)(n,xe);return(0,E.jsxs)("div",{className:"bor b-side pt8 plr16 mb8 d-f",style:{alignItems:"start",minWidth:1600},children:[e.list&&e.list.map((function(e){return D(e,(0,i.Z)((0,i.Z)({},l),{},{name:[a,e.name],labelAlign:"left",labelCol:24,style:{flexDirection:"column",flex:1,marginBottom:8}}))})),(0,E.jsx)(U.Z.Item,{children:(0,E.jsx)(h.Z,{danger:!0,onClick:function(){return r(a)},block:!0,icon:(0,E.jsx)(ie.Z,{}),style:{width:120},children:s("\u5220\u9664\u8be5\u9879")})})]},t)})),(0,E.jsx)(U.Z.Item,{noStyle:!0,className:"w100",label:"",children:(0,E.jsx)(h.Z,{type:"dashed",className:"w100",onClick:function(){return a()},block:!0,icon:(0,E.jsx)(I.Z,{}),children:s("\u589e\u52a0\u4e00\u9879")})})]})}},"dynamicForm_".concat(e.name)):(0,E.jsx)("div",{style:{width:680},children:D(e)})}))};return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(U.Z.Item,{name:e.primaryKey||"id",noStyle:!0,hidden:!0,children:(0,E.jsx)(z.Z,{})},"dynamicForm_id"),b&&b.length?(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(te.Z,{current:_,children:(b||[]).map((function(e,n){return(0,E.jsx)(te.Z.Step,{title:e.group},n)}))}),(0,E.jsx)("div",{className:"pt32",children:(b||[]).map((function(e,n){return(0,E.jsx)("div",{className:[_===n?"p-r z9":"p-a z-99 v-h l-10000"].join(" "),children:N(e.config)},n)}))}),(0,E.jsxs)("div",{className:"ta-c pt32",children:[_>0&&(0,E.jsx)(h.Z,{onClick:function(){m(_-1)},children:s("\u4e0a\u4e00\u6b65")}),_<(b||[]).length-1&&(0,E.jsx)(h.Z,{type:"primary",className:"ml16",onClick:function(){if(e.form){var n=b[_].config.map((function(e){return e.name}));e.form.validateFields(n).then((function(){w()})).catch((function(e){console.log(e)}))}else w()},children:s("\u4e0b\u4e00\u6b65")}),(0,E.jsx)("div",{children:_===(b||[]).length-1&&(0,E.jsx)("div",{className:"pt8 c-hint-b",children:s("\u70b9\u51fb\u786e\u5b9a\u5b8c\u6210\u63d0\u4ea4")})})]})]}):(0,E.jsx)("div",{style:{width:680},children:N(x||[])})]})}var ve=t(4269),ge=t(54627),ye=t(64447);function be(e){var n,t=(0,c.useState)(!1),a=(0,l.Z)(t,2),r=a[0],i=a[1],o=(0,c.useState)(),s=(0,l.Z)(o,2),u=s[0],d=s[1];(0,c.useEffect)((function(){m(e.url)}),[e.url]);var m=function(e){e&&(i(!0),(0,F.ci)(e).then((function(e){e.data?d(e.data.result):(console.error("Invalid data format:",e.data),d(void 0))})).catch((function(e){console.error("Error fetching data:",e)})).finally((function(){i(!1)})))};return(0,E.jsx)(_.Z,{destroyOnClose:!0,maskClosable:!1,width:680,open:e.visible,title:null===u||void 0===u?void 0:u.title,onCancel:function(){return e.onVisibilityChange(!1)},onOk:function(){return e.onVisibilityChange(!1)},footer:null!==u&&void 0!==u&&null!==(n=u.bottomButton)&&void 0!==n&&n.length?(0,E.jsx)("div",{className:"flex justify-end",children:u.bottomButton.map((function(e,n){return(0,E.jsxs)(h.Z,{type:"primary",onClick:function(){return(0,F.bZ)(e.method,e.url,e.arg)},className:"ml-2",children:[e.icon&&(0,E.jsx)("span",{className:"mr-2",dangerouslySetInnerHTML:{__html:e.icon}}),e.text]},"footerButton_".concat(n))}))}):null,children:(0,E.jsx)(p.Z,{spinning:r,children:u?(0,E.jsx)(ge.Z,{data:u.content||[]}):(0,E.jsx)("div",{children:(0,ye.t)("No data available")})})})}function Ee(e){var n=(0,A.s0)(),t=(0,A.TH)(),y=(0,c.useState)([]),b=(0,l.Z)(y,2),O=b[0],S=b[1],U=(0,c.useState)(!0),H=(0,l.Z)(U,2),V=H[0],q=H[1],z=(0,c.useState)(!1),J=(0,l.Z)(z,2),G=J[0],$=J[1],Q=(0,c.useState)("true"===(0,B.jS)("isVisableAdd")||!1),Y=(0,l.Z)(Q,2),ee=Y[0],ne=Y[1],te=(0,c.useState)(!1),ae=(0,l.Z)(te,2),re=ae[0],ie=ae[1],le=(0,c.useState)(!1),ce=(0,l.Z)(le,2),oe=ce[0],se=ce[1],ue=(0,c.useState)(!1),de=(0,l.Z)(ue,2),_e=de[0],me=de[1],fe=(0,c.useState)(""),pe=(0,l.Z)(fe,2),he=pe[0],xe=pe[1],ge=(0,c.useState)(!1),ye=(0,l.Z)(ge,2),Ee=ye[0],Oe=ye[1],Ce=(0,c.useState)(!1),Me=(0,l.Z)(Ce,2),Se=Me[0],Pe=Me[1],we=(0,c.useState)([]),ke=(0,l.Z)(we,2),De=ke[0],Ne=ke[1],Te={current:1,pageSize:20,total:0,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:[20,50,100,500],showTotal:function(e){return"".concat(fa("\u5171")).concat(e).concat(fa("\u6761"))}},Ie=(0,c.useState)(Te),Le=(0,l.Z)(Ie,2),Re=Le[0],Ae=Le[1],Be=(0,c.useState)([]),Ue=(0,l.Z)(Be,2),Ke=Ue[0],We=Ue[1],Fe=(0,c.useState)([]),He=(0,l.Z)(Fe,2),Ve=He[0],qe=He[1],ze=(0,c.useState)([]),Je=(0,l.Z)(ze,2),Ge=Je[0],$e=Je[1],Xe=(0,c.useRef)(Ge),Qe=function(e){Xe.current=e,$e(e)},Ye=(0,c.useState)([]),en=(0,l.Z)(Ye,2),nn=en[0],tn=en[1],an=(0,c.useState)([]),rn=(0,l.Z)(an,2),ln=rn[0],cn=rn[1],on=(0,c.useState)([]),sn=(0,l.Z)(on,2),un=sn[0],dn=sn[1],_n=(0,c.useState)([]),mn=(0,l.Z)(_n,2),fn=mn[0],pn=mn[1],hn={};try{hn=JSON.parse((0,B.jS)("formData")||"{}")}catch(Oa){}var xn=(0,c.useState)(hn),jn=(0,l.Z)(xn,2),Zn=jn[0],vn=jn[1],gn=(0,c.useState)({}),yn=(0,l.Z)(gn,2),bn=yn[0],En=yn[1],On=(0,c.useState)({}),Cn=(0,l.Z)(On,2),Mn=Cn[0],Sn=Cn[1],Pn=(0,c.useRef)(Mn),wn=(0,c.useState)([]),kn=(0,l.Z)(wn,2),Dn=kn[0],Nn=kn[1],Tn=(0,c.useState)(1e3),In=(0,l.Z)(Tn,2),Ln=In[0],Rn=In[1],An=(0,c.useState)([]),Bn=(0,l.Z)(An,2),Un=Bn[0],Kn=Bn[1],Wn=(0,c.useState)({}),Fn=(0,l.Z)(Wn,2),Hn=Fn[0],Vn=Fn[1],qn=(0,c.useState)(),zn=(0,l.Z)(qn,2),Jn=zn[0],Gn=zn[1],$n=(0,c.useState)(),Xn=(0,l.Z)($n,2),Qn=Xn[0],Yn=Xn[1],et=(0,c.useRef)(Qn),nt=(0,c.useState)(!1),tt=(0,l.Z)(nt,2),at=tt[0],rt=tt[1],it=(0,c.useState)(!1),lt=(0,l.Z)(it,2),ct=lt[0],ot=lt[1],st=(0,c.useState)([]),ut=(0,l.Z)(st,2),dt=ut[0],_t=ut[1],mt=(0,c.useState)([]),ft=(0,l.Z)(mt,2),pt=ft[0],ht=ft[1],xt=(0,c.useState)(),jt=(0,l.Z)(xt,2),Zt=jt[0],vt=jt[1],gt=(0,c.useState)(""),yt=(0,l.Z)(gt,2),bt=yt[0],Et=yt[1],Ot=(0,c.useState)(""),Ct=(0,l.Z)(Ot,2),Mt=Ct[0],St=Ct[1],Pt=(0,c.useState)(),wt=(0,l.Z)(Pt,2),kt=wt[0],Dt=wt[1],Nt=(0,c.useState)(),Tt=(0,l.Z)(Nt,2),It=Tt[0],Lt=Tt[1],Rt=(0,c.useState)([]),At=(0,l.Z)(Rt,2),Bt=At[0],Ut=At[1],Kt=(0,c.useState)([]),Wt=(0,l.Z)(Kt,2),Ft=Wt[0],Ht=Wt[1],Vt=(0,c.useState)(!0),qt=(0,l.Z)(Vt,2),zt=qt[0],Jt=qt[1],Gt=(0,c.useRef)(zt),$t=(0,c.useState)(!1),Xt=(0,l.Z)($t,2),Qt=Xt[0],Yt=Xt[1],ea=(0,c.useRef)(Qt),na=(0,c.useState)(!1),ta=(0,l.Z)(na,2),aa=ta[0],ra=ta[1],ia=(0,c.useState)(20),la=(0,l.Z)(ia,2),ca=la[0],oa=la[1],sa=(0,c.useState)(),ua=(0,l.Z)(sa,2),da=ua[0],_a=ua[1],ma=(0,K.$G)(),fa=ma.t,pa=(ma.i18n,(0,c.useState)("")),ha=(0,l.Z)(pa,2),xa=ha[0],ja=ha[1],Za={pageConf:Te,params:[],paramsMap:Hn,sorter:void 0};(0,c.useEffect)((function(){}),[ca]),(0,c.useEffect)((function(){ja((0,B.W9)())}),[]),(0,c.useEffect)((function(){e&&e.disable&&n("/404")}),[]);var va=function e(n,t,a){return n.map((function(n,r){var l=n["ui-type"]||"input";"select2"===l&&(l="select"),"file"===l&&(l="fileUpload");var c=n.label||t[n.name],o=(n.validators||[]).map((function(e){if("select"===l)return"DataRequired"===e.type?{required:!0,message:"".concat(fa("\u8bf7\u9009\u62e9")," ").concat(c)}:void 0;switch(e.type){case"DataRequired":return{required:!0,message:"".concat(fa("\u8bf7\u8f93\u5165")," ").concat(c)};case"Regexp":return{pattern:new RegExp("".concat(e.regex)),message:"".concat(fa("\u8bf7\u6309\u6b63\u786e\u7684\u89c4\u5219\u8f93\u5165"))};case"Length":return{min:e.min||0,max:e.max,message:"".concat(fa("\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u957f\u5ea6"))};default:return}})).filter((function(e){return!!e})),s=e(n.info||[],t,a);return{label:c,type:l,rules:o,list:s,name:n.name,disable:n.disable,description:n.description||a[n.name]||void 0,required:n.required,defaultValue:""===n.default?void 0:n.default,multiple:n["ui-type"]&&"select2"===n["ui-type"],options:(n.values||[]).map((function(e){return{label:e.value,value:e.id,children:e.children}})),data:(0,i.Z)({},n)}}))};(0,c.useEffect)((function(){var c=(0,B.jS)("targetId"),f=c?"/dimension_remote_table_modelview/".concat(c,"/api/"):null===e||void 0===e?void 0:e.url;$(!0),(0,F.BQ)(f).then((function(c){var f=c.data,p=f.list_columns,h=f.label_columns,x=f.filters,j=f.add_columns,Z=f.edit_columns,v=f.permissions,g=f.description_columns,y=f.add_fieldsets,b=(f.edit_fieldsets,f.help_url),O=f.order_columns,C=f.action,M=f.route_base,S=f.column_related,P=f.primary_key,N=f.label_title,T=f.cols_width,I=f.import_data,L=f.download_data,R=f.list_ui_type,A=f.list_ui_args,U=f.ops_link,K=f.enable_favorite,W=f.echart,H=f.page_size,V=f.list_title,q=(null===e||void 0===e?void 0:e.related)||v.includes("can_show")||v.includes("can_edit")||v.includes("can_delete"),z=localStorage.getItem("tablebox_".concat(t.pathname)),J=JSON.parse(z||"[]").reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.dataIndex,n.width))}),{}),G=Object.entries(S||{}).reduce((function(e,n){var t=(0,l.Z)(n,2),c=(t[0],t[1]);return[].concat((0,a.Z)(e),[{dep:c.src_columns,effect:c.des_columns.join(""),effectOption:c.related.reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},je(n.src_value),n.des_value.map((function(e){return{label:e,value:e}}))))}),{})}])}),[]),X=p.map((function(e){var n=(h[e]||e).split(":"),t=(0,l.Z)(n,2),r=t[0],i=t[1];return{title:i?(0,E.jsxs)(o.Z,{placement:"top",title:i,children:[r,(0,E.jsx)(w.Z,{})]}):r,dataIndex:e,key:e,sorter:O.includes(e)?function(n,t){return n[e]-t[e]}:void 0,render:function(n,t){var r;if(void 0===n||""===n)return"-";if((0,B.in)(n)){var i=document.createElement("div");i.innerHTML=n;var c=i.firstElementChild,s=null===c||void 0===c?void 0:c.getAttribute("type"),u=null===c||void 0===c?void 0:c.getAttribute("addedValue");if(console.log("type:",s),console.log("addedValue:",u),s&&u){if("tips"===s)return(0,E.jsx)(o.Z,{title:(0,E.jsx)("span",{className:"tips-content",dangerouslySetInnerHTML:{__html:u}}),placement:"topLeft",children:(0,E.jsx)("div",{className:T[e].type||"ellip2",dangerouslySetInnerHTML:{__html:n}})});if("enhancedDetails"===s)return(0,E.jsx)("div",{onClick:function(){me(!0),xe(u)},dangerouslySetInnerHTML:{__html:n}})}}if(T[e]&&-1!==(null===(r=T[e].type)||void 0===r?void 0:r.indexOf("ellip")))return(0,E.jsx)(o.Z,{title:(0,E.jsx)("span",{className:"tips-content",dangerouslySetInnerHTML:{__html:n}}),placement:"topLeft",children:(0,E.jsx)("div",{className:T[e].type,dangerouslySetInnerHTML:{__html:n}})});if("[object Object]"===Object.prototype.toString.call(n)){var d=Object.entries(n).reduce((function(e,n){var t=(0,l.Z)(n,2),r=t[0],i=t[1];return[].concat((0,a.Z)(e),[{label:r,value:i}])}),[]);return d.length?(0,E.jsx)("div",{style:{overflow:"auto",maxHeight:100},children:d.map((function(e,n){return(0,E.jsxs)("div",{children:[h[e.label]||e.label,":",e.value]},"table_itemvalue_".concat(n))}))}):"-"}return(0,E.jsx)("div",{style:{overflow:"auto",maxHeight:100},dangerouslySetInnerHTML:{__html:n}})},width:J[e]||T[e]&&T[e].width||100}})),Q=Object.entries(C||{}).reduce((function(e,n){var t=(0,l.Z)(n,2),r=(t[0],t[1]);return[].concat((0,a.Z)(e),[(0,i.Z)({},r)])}),[]),Y=Q.filter((function(e){return!!e.multiple})),ee=Q.filter((function(e){return!!e.single})),ne={title:fa("\u64cd\u4f5c"),width:80,dataIndex:"handle",key:"handle",align:"right",fixed:"right",render:function(a,l){var c;return(0,E.jsx)(s.Z,{size:"middle",children:q?(0,E.jsx)(u.Z,{overlay:(0,E.jsxs)(d.Z,{children:[ea.current&&Gt.current?(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("div",{className:"link",onClick:function(){_.Z.confirm({title:fa("\u6536\u85cf"),icon:(0,E.jsx)(k.Z,{}),content:"".concat(fa("\u786e\u5b9a\u6536\u85cf"),"?"),okText:fa("\u786e\u8ba4\u6536\u85cf"),cancelText:fa("\u53d6\u6d88"),onOk:function(){return new Promise((function(e,n){(0,F.gA)("".concat(M,"favorite/").concat(l[P])).then((function(n){e("")})).catch((function(e){n()}))})).then((function(e){m.ZP.success(fa("\u6536\u85cf\u6210\u529f")),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Re,params:Xe.current,paramsMap:x}))})).catch((function(){m.ZP.error(fa("\u6536\u85cf\u5931\u8d25"))}))},onCancel:function(){}})},children:fa("\u6536\u85cf")})}):null,ea.current&&!Gt.current?(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("div",{className:"link",onClick:function(){_.Z.confirm({title:fa("\u53d6\u6d88\u6536\u85cf"),icon:(0,E.jsx)(k.Z,{}),content:"".concat(fa("\u786e\u5b9a\u53d6\u6d88\u6536\u85cf"),"?"),okText:fa("\u786e\u8ba4\u53d6\u6d88\u6536\u85cf"),cancelText:fa("\u53d6\u6d88"),onOk:function(){return new Promise((function(e,n){(0,F.aJ)("".concat(M,"favorite/").concat(l[P])).then((function(n){e("")})).catch((function(e){n()}))})).then((function(e){m.ZP.success(fa("\u64cd\u4f5c\u6210\u529f")),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Re,params:Xe.current,paramsMap:x}))})).catch((function(){m.ZP.error(fa("\u64cd\u4f5c\u5931\u8d25"))}))},onCancel:function(){}})},children:fa("\u53d6\u6d88\u6536\u85cf")})}):null,v.includes("can_show")?(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("div",{className:"link",onClick:function(){Pe(!0),ba(l[P])},children:fa("\u8be6\u60c5")})}):null,v.includes("can_edit")?(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("div",{className:"link",onClick:function(){se(!0),(0,F.BQ)(M,l[P]).then((function(e){var n=e.data,t=n.edit_columns,a=n.label_columns,c=n.description_columns,o=n.edit_fieldsets,s=va(t,a,c),u=t.reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.name,n))}),{});t.forEach((function(e){"list"===e["ui-type"]&&e.info.forEach((function(e){u[e.name]=e}))})),En(u);var d=o.map((function(e){var n=e.fields.map((function(e){return u[e]})).filter((function(e){return!!e}));return{group:e.group,expanded:e.expanded,config:va(n,a,c)}}));cn(s),pn(d),ba(l[P])})).catch((function(){m.ZP.warn(fa("\u7528\u6237\u6ca1\u6709\u4fee\u6539\u6743\u9650"))}))},children:fa("\u4fee\u6539")})}):null,v.includes("can_delete")?(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("div",{className:"c-fail cp",onClick:function(){_.Z.confirm({title:fa("\u5220\u9664"),icon:(0,E.jsx)(k.Z,{}),content:"".concat(fa("\u786e\u5b9a\u5220\u9664"),"?"),okText:fa("\u786e\u8ba4\u5220\u9664"),cancelText:fa("\u53d6\u6d88"),okButtonProps:{danger:!0},onOk:function(){return new Promise((function(e,n){(0,F.aM)("".concat(M).concat(l[P])).then((function(n){e("")})).catch((function(e){n()}))})).then((function(e){m.ZP.success(fa("\u5220\u9664\u6210\u529f")),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Re,params:Xe.current,paramsMap:x}))})).catch((function(){m.ZP.error(fa("\u5220\u9664\u5931\u8d25"))}))},onCancel:function(){}})},children:fa("\u5220\u9664")})}):null,null===e||void 0===e||null===(c=e.related)||void 0===c?void 0:c.map((function(e,a){return(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("div",{className:"link",onClick:function(){n("".concat(t.pathname,"/").concat(e.name,"?id=").concat(l[P]))},children:e.title})},"moreAction_".concat(a))})),!!ee.length&&ee.map((function(e,n){return(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("div",{className:"link",onClick:function(){_.Z.confirm({title:e.confirmation,icon:(0,E.jsx)(k.Z,{}),content:"",okText:fa("\u786e\u8ba4"),cancelText:fa("\u53d6\u6d88"),onOk:function(){return new Promise((function(n,t){(0,F.y5)("".concat(M,"action/").concat(e.name,"/").concat(l[P])).then((function(e){n(e)})).catch((function(e){t(e)}))})).then((function(e){m.ZP.success(fa("\u64cd\u4f5c\u6210\u529f")),e.data.result.link&&window.open(e.data.result.link,"bank"),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Re,params:Xe.current,paramsMap:x}))})).catch((function(){m.ZP.error(fa("\u64cd\u4f5c\u5931\u8d25"))}))},onCancel:function(){}})},children:fa("".concat(e.text))})},"table_action_".concat(n))}))]}),children:(0,E.jsxs)("div",{className:"link",children:[fa("\u66f4\u591a"),(0,E.jsx)(D.Z,{})]})}):null})}},te=(0,a.Z)(X);q&&te.push(ne);var ae=j.reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.name,n))}),{});if(hn&&Object.keys(hn).length){var re=(Object.keys(hn)||[]).filter((function(e){return hn[e]&&ae[e]&&ae[e].retry_info})),ie=re.length;if(re.length){var le=hn;for(var ce in le){if(Object.prototype.hasOwnProperty.call(le,ce))void 0===le[ce]&&delete le[ce]}!function e(n){ie-=1,(0,F.y1)("".concat(M,"_info"),{exist_add_args:n}).then((function(n){var t=n.data,a=t.add_columns,l=t.label_columns,c=t.description_columns,o=t.add_fieldsets,s=a.reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.name,n))}),{}),u=va(a,l,c),d=o.map((function(e){var n=e.fields.map((function(e){return s[e]})).filter((function(e){return!!e}));return{group:e.group,expanded:e.expanded,config:va(n,l,c)}})),_=a.filter((function(e){return""!==e.default})).map((function(e){return(0,r.Z)({},e.name,e.default)})).reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),n)}),{});if(vn(_),tn(u),dn(d),ie){var m=JSON.stringify(_);e(m)}})).catch((function(e){m.ZP.error(fa("\u5b57\u6bb5\u5207\u6362\u9519\u8bef"))})).finally((function(){$(!1)}))}(JSON.stringify(le))}}var oe=Z.reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.name,n))}),{});Z.forEach((function(e){"list"===e["ui-type"]&&e.info.forEach((function(e){oe[e.name]=e}))}));var ue=va(j,h,g),de=y.map((function(e){var n=e.fields.map((function(e){return ae[e]})).filter((function(e){return!!e}));return{group:e.group,expanded:e.expanded,config:va(n,h,g)}})),_e=Object.entries(x).reduce((function(e,n){var t=(0,l.Z)(n,2),r=t[0],i=t[1];return[].concat((0,a.Z)(e),[{name:r,type:i["ui-type"]||"input",title:h[r],oprList:i.filter.map((function(e){return e.operator})),defalutValue:""===i.default?void 0:i.default,option:i.values?i.values.map((function(e){return{label:e.value,value:e.id}})):void 0}])}),[]),fe=Object.entries(x).reduce((function(e,n){var t=(0,l.Z)(n,2),r=t[0],i=t[1];return[].concat((0,a.Z)(e),[{key:r,value:i.default}])}),[]).filter((function(e){return e.value})),pe=JSON.parse(localStorage.getItem("filter_".concat(t.pathname).concat(t.search))||"[]"),he=void 0;if((0,B.jS)("filter"))try{he=JSON.parse((0,B.jS)("filter")||"[]")}catch(ye){m.ZP.error(fa("filter\u89e3\u6790\u5f02\u5e38"))}var Ze,ve=he||pe;ve&&ve.length&&(fe=ve),_a(V),oa(H),ra(W),Ze=K,ea.current=Ze,Yt(Ze),Ut(U),Ht(p),Dt(R),Lt(A),ot(L),rt(I),St(N),Et(P),_t(G),ht(Y),function(e){et.current=e,Yn(e)}(M),En(oe),Vn(x),We(te),qe(_e),tn(ue),dn(de),function(e){Pn.current=e,Sn(e)}(h),Kn(v);var ge=z?te.reduce((function(e,n){return e+n.width||100}),0)+80:100*te.length+80+80;Rn(ge),Gn(b),Qe(fe),ya({pageConf:(0,i.Z)((0,i.Z)({},Te),{},{pageSize:H}),params:fe,paramsMap:x,sorter:void 0})})).catch((function(e){console.log(e)})).finally((function(){q(!1),$(!1)}))}),[]);var ga=function(n,t){var r=(0,B.jS)("id");return{filters:[r?{col:null===e||void 0===e?void 0:e.model_name,opr:"rel_o_m",value:+r}:void 0].concat((0,a.Z)(n.filter((function(e){return void 0!==e.value})).map((function(e){for(var n="",a=["rel_o_m","ct","eq"],r=t[e.key].filter.map((function(e){return e.operator}))||[],i=0;i<a.length;i++){var l=a[i];if(r.includes(l)){n=l;break}}return{col:e.key,opr:n,value:e.value}})))).filter((function(e){return!!e}))}},ya=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageConf:Te,params:Ge,paramsMap:Hn,sorter:void 0,only_favorite:!1},n=e.pageConf,t=e.params,a=e.paramsMap,r=e.sorter,l=e.only_favorite;q(!0);var c=JSON.stringify((0,i.Z)((0,i.Z)({},ga(t,a)),{},{only_favorite:l,str_related:1,page:(n.current||1)-1,page_size:n.pageSize||10},r));(0,F.jm)(et.current,{form_data:c}).then((function(e){var t=e.data.result,a=t.count,l=t.data;S(l),Ne([]),Ae((0,i.Z)((0,i.Z)((0,i.Z)({},Te),n),{},{total:a})),vt(r)})).catch((function(e){console.log(e)})).finally((function(){return q(!1)}))},ba=function(e){Oe(!0),Nn([]),(0,F.kI)("".concat(et.current).concat(e)).then((function(e){var n=e.data.result,t=[],a=function(e){return"[object Object]"===Object.prototype.toString.call(e)?e.last_name:e};Object.keys(n).forEach((function(e){t.push({label:Pn.current[e]||e,value:a(n[e]),key:e})})),Nn(t)})).catch((function(e){})).finally((function(){Oe(!1)}))},Ea={name:"csv_file",maxCount:1,action:"".concat(Qn,"upload/"),headers:{authorization:"authorization-text"},beforeUpload:function(e){var n=-1!==e.name.indexOf(".csv"),t=-1!==e.name.indexOf(".xls"),a=-1!==e.name.indexOf(".json"),r=-1!==e.name.indexOf(".xlsx");if(n||a||t||r)return!0;m.ZP.error("\u6587\u4ef6\u683c\u5f0f\u652f\u6301CSV/JSON/XLS/XLSX")},onChange:function(e){"done"===e.file.status?f.Z.success({message:fa("\u5bfc\u5165\u6210\u529f"),description:JSON.stringify(e.file.response)}):"error"===e.file.status&&f.Z.error({message:fa("\u5bfc\u5165\u5931\u8d25"),description:JSON.stringify(e.file.response)})}};return(0,E.jsxs)("div",{className:"fade-in h100 d-f fd-c",children:[(0,E.jsx)(W,{title:"".concat(fa("\u6dfb\u52a0")," ").concat(Mt),formData:Zn,loading:G,visible:ee,onCancel:function(){ne(!1)},onCreate:function(e,n){for(var t in $(!0),e)if(Object.prototype.hasOwnProperty.call(e,t)){var a=e[t];if(Array.isArray(a)){if(a[0]&&"[object Object]"===Object.prototype.toString.call(a[0]))continue;e[t]=e[t].join(",")}}(0,F.P9)(et.current,e).then((function(e){m.ZP.success("".concat(fa("\u6dfb\u52a0")," ").concat(Mt," ").concat(fa("\u6210\u529f"))),n.resetFields(),ne(!1),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Re,params:Ge,sorter:Zt,paramsMap:Hn}))})).catch((function(e){m.ZP.error("".concat(fa("\u6dfb\u52a0")," ").concat(Mt," ").concat(fa("\u5931\u8d25")))})).finally((function(){$(!1)}))},children:function(e,n){return(0,E.jsx)(Ze,{form:e,onRetryInfoChange:function(n){$(!0);var t=e.getFieldsValue();for(var a in t){if(Object.prototype.hasOwnProperty.call(t,a))void 0===t[a]&&delete t[a]}var l=JSON.stringify(t);e.resetFields(),(0,F.y1)("".concat(et.current,"_info"),{exist_add_args:l}).then((function(n){var t=n.data,a=t.add_columns,l=t.label_columns,c=t.description_columns,o=t.add_fieldsets,s=a.reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.name,n))}),{}),u=va(a,l,c),d=o.map((function(e){var n=e.fields.map((function(e){return s[e]})).filter((function(e){return!!e}));return{group:e.group,expanded:e.expanded,config:va(n,l,c)}})),_=a.filter((function(e){return""!==e.default})).map((function(e){return(0,r.Z)({},e.name,e.default)})).reduce((function(e,n){return(0,i.Z)((0,i.Z)({},e),n)}),{});e.setFieldsValue(_),tn(u),dn(d)})).catch((function(e){m.ZP.error(fa("\u5b57\u6bb5\u5207\u6362\u9519\u8bef"))})).finally((function(){$(!1)}))},formChangeRes:n,linkageConfig:dt,config:nn,configGroup:un})}}),(0,E.jsx)(W,{title:"".concat(fa("\u4fee\u6539")," ").concat(Mt),formData:Dn.reduce((function(e,n){if("select"===(bn[n.key]||{})["ui-type"]){var t=n.value,a=(bn[n.key]||{}).values||[],l=a.map((function(e){return e.value})).indexOf(n.value);return~l&&(t=a[l].id),(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.key,t))}var c;if("select2"===(bn[n.key]||{})["ui-type"])return c=Array.isArray(n.value)?n.value.map((function(e){return"object"===typeof e&&null!==e&&"id"in e?e.id:e})):(n.value||"").split(","),(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.key,c));if("datePicker"===(bn[n.key]||{})["ui-type"]){var o=n.value;return o=P()(o),(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.key,o))}if("list"===(bn[n.key]||{})["ui-type"]){var s=(n.value||[]).map((function(e){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var t=e[n];"datePicker"===(bn[n]||{})["ui-type"]&&(e[n]=P()(t))}return e}));return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.key,s))}return(0,i.Z)((0,i.Z)({},e),{},(0,r.Z)({},n.key,n.value))}),{}),loading:re||Ee,visible:oe,onCancel:function(){se(!1)},onCreate:function(e){for(var n in ie(!0),Nn([]),e)if(Object.prototype.hasOwnProperty.call(e,n)){var t=e[n];if(Array.isArray(t)){if(t[0]&&"[object Object]"===Object.prototype.toString.call(t[0]))continue;e[n]=e[n].join(",")}}(0,F.a1)("".concat(et.current).concat(e[bt]),e).then((function(e){m.ZP.success("".concat(fa("\u66f4\u65b0")," ").concat(Mt," ").concat(fa("\u6210\u529f"))),se(!1),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Re,params:Ge,sorter:Zt,paramsMap:Hn}))})).catch((function(e){m.ZP.error("".concat(fa("\u66f4\u65b0")," ").concat(Mt," ").concat(fa("\u5931\u8d25")))})).finally((function(){ie(!1)}))},children:function(e){return(0,E.jsx)(Ze,{form:e,primaryKey:bt,config:ln,linkageConfig:dt,configGroup:fn})}}),(0,E.jsx)(_.Z,{title:"".concat(Mt," ").concat(fa("\u8be6\u60c5")),visible:Se,footer:null,width:800,destroyOnClose:!0,onCancel:function(){Pe(!1)},children:(0,E.jsx)(p.Z,{spinning:Ee,children:(0,E.jsx)("div",{className:"pb8",style:{paddingBottom:"8px",minHeight:"400px",maxWidth:"70vw",margin:"0 auto",overflowX:"auto"},children:Dn.map((function(e,n){return(0,E.jsxs)("div",{style:{display:"flex",gap:"1rem",marginBottom:"1rem"},children:[(0,E.jsx)("div",{style:{display:"flex",justifyContent:"right",width:"130px"},children:(0,E.jsxs)("strong",{children:[e.label,"\uff1a"]})}),(0,E.jsx)("div",{style:{flex:"1",display:"flex",justifyContent:"flex-start"},children:(0,E.jsx)("pre",{style:{whiteSpace:"pre-wrap",wordBreak:"break-word",overflowX:"auto",margin:0},dangerouslySetInnerHTML:{__html:function(){var n=e.value;if("[object Object]"===Object.prototype.toString.call(e.value)||"[object Array]"===Object.prototype.toString.call(e.value))try{n=JSON.stringify(e.value,null,2)}catch(t){}return n}()}})})]},"dataDetail_".concat(n))}))})})}),(0,E.jsx)(be,{visible:_e,url:he,onVisibilityChange:me}),(0,E.jsx)(C,{title:(0,E.jsxs)(E.Fragment,{children:[null!==e&&void 0!==e&&e.isSubRoute?(0,E.jsxs)(h.Z,{className:"mr16",onClick:function(){window.history.back()},children:[(0,E.jsx)(N.Z,{}),fa("\u8fd4\u56de")]}):null,(0,E.jsx)("span",{children:Mt})]}),breadcrumbs:((null===e||void 0===e?void 0:e.breadcrumbs)||[]).map((function(n,t){return(0,E.jsxs)("span",{className:"c-icon-b fs12",children:["/",(0,E.jsx)("span",{className:"plr2",children:n})]},"templateTitle_".concat(null===e||void 0===e?void 0:e.name,"_").concat(t))})),children:Jn?(0,E.jsxs)("div",{className:"link",children:[(0,E.jsx)("span",{className:"pr4",onClick:function(){window.open(Jn,"blank")},children:fa("\u5e2e\u52a9\u94fe\u63a5")}),(0,E.jsx)(T.Z,{})]}):null}),(0,E.jsx)(g.VY,{className:"appmgmt-content bg-title h100 d-f fd-c",children:(0,E.jsxs)("div",{className:"mlr16 mb16 flex1 bg-w",children:[!!Ve.length&&(0,E.jsx)(X,{values:Ge,params:Ve,onChange:function(e){localStorage.setItem("filter_".concat(t.pathname).concat(t.search),JSON.stringify(e)),Qe(e),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Te,params:e,sorter:Zt,paramsMap:Hn}))}}),aa?(0,E.jsx)(ve.Z,{url:Qn}):null,"card"!==kt?(0,E.jsx)(M.Z,{cancelExportData:!0,tableKey:"tablebox_".concat(t.pathname),titleNode:(0,E.jsx)(x.Z,{className:"tablebox-title",children:(0,E.jsxs)("div",{className:"d-f ac",children:[(0,E.jsx)("div",{className:"mr8",children:da||""}),Qt?(0,E.jsx)("div",{className:"pb2",children:(0,E.jsx)(j.Z,{checked:zt,checkedChildren:fa("\u5168\u90e8"),unCheckedChildren:fa("\u6211\u7684\u6536\u85cf"),defaultChecked:!0,onChange:function(e){var n;n=e,Gt.current=n,Jt(n),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Te,params:Ge,sorter:Zt,paramsMap:Hn,only_favorite:!e}))}})}):null]})}),buttonNode:(0,E.jsxs)("div",{className:"d-f ac",children:[Bt&&Bt.length?Bt.map((function(e){return(0,E.jsx)(h.Z,{type:"primary",className:"mr16",onClick:function(){window.open(e.url,"bank")},children:e.text})})):null,Un.includes("can_add")?(0,E.jsxs)(h.Z,{className:"mr16",type:"primary",onClick:function(){return ne(!0)},children:[fa("\u6dfb\u52a0"),Mt,(0,E.jsx)(I.Z,{})]}):null,pt&&pt.length?(0,E.jsx)("div",{children:(0,E.jsx)(u.Z,{overlay:(0,E.jsx)(d.Z,{children:pt.map((function(e,n){return(0,E.jsx)(d.Z.Item,{children:(0,E.jsx)("span",{className:"link",onClick:function(){return function(e){De.length?_.Z.confirm({title:e.confirmation,icon:(0,E.jsx)(k.Z,{}),content:"",okText:fa("\u786e\u8ba4"),cancelText:fa("\u53d6\u6d88"),onOk:function(){return new Promise((function(n,t){(0,F.Eb)("".concat(et.current,"multi_action/").concat(e.name),{ids:De.map((function(e){return JSON.parse(e||"{}")[bt]}))}).then((function(e){n("")})).catch((function(e){t()}))})).then((function(e){m.ZP.success(fa("\u64cd\u4f5c\u6210\u529f")),ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:Re,params:Ge,sorter:Zt,paramsMap:Hn}))})).catch((function(){m.ZP.error(fa("\u64cd\u4f5c\u5931\u8d25"))}))},onCancel:function(){}}):m.ZP.warn(fa("\u8bf7\u5148\u9009\u62e9"))}(e)},children:"".concat(fa("\u6279\u91cf")," ").concat(e.text)})},"table_muliple_".concat(n))}))}),children:(0,E.jsxs)(h.Z,{children:[fa("\u6279\u91cf\u64cd\u4f5c")," ",(0,E.jsx)(D.Z,{})]})})}):null,at?(0,E.jsx)("div",{className:"d-f ml16",children:(0,E.jsx)(o.Z,{color:"#fff",title:(0,E.jsxs)("span",{className:"tips-content-b",children:[(0,E.jsxs)("div",{children:[fa("\u6ce8\u610f\uff1acsv\u9017\u53f7\u5206\u9694"),"\uff0c"]}),(0,E.jsx)("div",{children:fa("\u7b2c\u4e00\u884c\u4e3a\u5217\u7684\u82f1\u6587\u540d")})," ",(0,E.jsx)("div",{className:"link",onClick:function(){window.open("".concat(window.location.origin).concat(et.current,"download_template"))},children:fa("\u4e0b\u8f7d\u5bfc\u5165\u6a21\u677f")})]}),placement:"topLeft",children:(0,E.jsx)(Z.Z,(0,i.Z)((0,i.Z)({},Ea),{},{children:(0,E.jsx)(h.Z,{className:"",icon:(0,E.jsx)(L.Z,{}),children:fa("\u6279\u91cf\u5bfc\u5165\u6570\u636e")})}))})}):null,ct?(0,E.jsxs)(h.Z,{className:"ml16",onClick:function(){_.Z.confirm({title:fa("\u5bfc\u51fa\u6570\u636e"),icon:(0,E.jsx)(k.Z,{}),content:"",okText:fa("\u786e\u8ba4\u5bfc\u51fa\u6570\u636e"),cancelText:fa("\u53d6\u6d88"),onOk:function(){var e=ga(Ge,Hn),n=JSON.stringify(e);window.open("".concat(window.location.origin).concat(et.current,"download?form_data=").concat(n)),m.ZP.success(fa("\u5bfc\u51fa\u6210\u529f"))},onCancel:function(){}})},children:[fa("\u6279\u91cf\u5bfc\u51fa"),"  ",(0,E.jsx)(R.Z,{})]}):null]}),rowKey:function(e){return JSON.stringify(e)},columns:Ke,loading:V,pagination:Re,dataSource:O,onChange:function(e,n,t){var a=t.order?{order_column:t.columnKey,order_direction:"ascend"===t.order?"asc":"desc"}:void 0;ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:e,params:Ge,paramsMap:Hn,sorter:a}))},rowSelection:{type:"checkbox",fixed:"left",columnWidth:32,selectedRowKeys:De,onChange:function(e){Ne(e)}},scroll:{x:Ln,y:xa}}):(0,E.jsxs)("div",{className:"bg-w p16",children:[(0,E.jsx)("div",{className:"d-f fw",children:O.map((function(e,n){return(0,E.jsx)("div",{style:{overflowY:"auto",width:null===It||void 0===It?void 0:It.card_width,height:null===It||void 0===It?void 0:It.card_height},className:"card-row mr16 mb16",children:Object.keys(e).map((function(t,a){return Ft.includes(t)?(0,E.jsx)("div",{style:{wordBreak:"break-all"},dangerouslySetInnerHTML:{__html:e[t]}},"row".concat(n).concat(a)):null}))},"card".concat(n))}))}),(0,E.jsx)("div",{className:"ta-r",children:(0,E.jsx)(v.Z,(0,i.Z)((0,i.Z)({},Re),{},{onChange:function(e,n){ya((0,i.Z)((0,i.Z)({},Za),{},{pageConf:(0,i.Z)((0,i.Z)({},Re),{},{current:e,pageSize:n}),params:Ge,paramsMap:Hn}))}}))})]})]})})]})}},4269:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){__webpack_require__.d(__webpack_exports__,{Z:function(){return ChartOptionTempalte}});var _home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(29439),react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(4519),_api_kubeflowApi__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(30506),_components_EchartCore_EchartCore__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(54189),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(2556);function ChartOptionTempalte(props){var _useState=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}),_useState2=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__.Z)(_useState,2),option=_useState2[0],setOption=_useState2[1],_useState3=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!0),_useState4=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__.Z)(_useState3,2),loading=_useState4[0],setLoading=_useState4[1];return(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((function(){props.url&&(0,_api_kubeflowApi__WEBPACK_IMPORTED_MODULE_1__.VL)("".concat(props.url,"echart"),{}).then((function(res){var option=res.data.result,currentOps={};eval("currentOps=".concat(option)),setOption(currentOps)})).catch((function(e){})).finally((function(){setLoading(!1)}))}),[props.url]),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_components_EchartCore_EchartCore__WEBPACK_IMPORTED_MODULE_2__.Z,{option:option,loading:loading})}},48773:function(){}}]);
//# sourceMappingURL=397.01f03a1b.chunk.js.map