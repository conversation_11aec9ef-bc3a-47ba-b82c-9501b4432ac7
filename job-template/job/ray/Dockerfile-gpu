#FROM ccr.ccs.tencentyun.com/cube-studio/ray:gpu
ARG BASE_IMAGE=rayproject/ray:nightly-gpu
FROM ${BASE_IMAGE}

USER root

ENV TZ Asia/Shanghai
ENV DEBIAN_FRONTEND noninteractive

# 安装调试相关工具
RUN apt update -y && apt install -y --force-yes --fix-missing --no-install-recommends apt-utils ca-certificates software-properties-common vim apt-transport-https gnupg2 ca-certificates-java rsync jq  wget git dnsutils iputils-ping net-tools curl mysql-client locales zip unzip

# 安装开发相关工具
RUN apt install -y python3.8-dev gcc automake autoconf libtool make ffmpeg build-essential

# 安装pip库
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple
RUN pip install kubernetes==21.7.0 pysnooper psutil requests numpy  pyinstaller argparse numba cython

# 安装当前代码
COPY job/ray/* /app/
COPY job/pkgs /app/job/pkgs

ENV PYTHONPATH=/app:$PYTHONPATH

ENV LD_LIBRARY_PATH /usr/local/cuda/lib64:$LD_LIBRARY_PATH
WORKDIR /app
# ENTRYPOINT ["bash", "/app/start.sh"]
ENTRYPOINT ["python", "launcher.py"]



