{% import 'appbuilder/general/lib.html' as lib %}

<div class="list-search-container">
    <form id="filter_form" class="form-search" method="get">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-filter text-primary" aria-hidden="true"></i> {{_("Filter List")}}
        </button>

        <ul class="dropdown-menu">
            {% for col in include_cols %}
            <li><a href="javascript:void(0)" name={{col}} class="filter btn" onclick="return false;">
                    {{ label_columns[col] }}</a>
            </li>
            {% endfor %}
        </ul>
        <div class="filters-container">
            <table class="table table-responsive table-hover filters">

            </table>
            <div class="filter-action" style="display:none">
                <button type="submit" class="btn btn-sm btn-primary" id="search-action">
                    Search&nbsp;&nbsp;<i class="fa fa-search"></i>
                </button>
                {#
            </div>#}
            {# <div class="filter-action" style="display:none">#}
                {% if help_url %}
                <a target="_blank" href="{{ help_url }}" class="btn btn-sm btn-primary" style="color: yellow" id="help-action">
                    帮助文档&nbsp;&nbsp;<i class="fa fa-link"></i>
                </a>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<script>
    (function ($) {
        function checkSearchButton() {
            var hasFilter = $('.filters tr').length;
            if (hasFilter) {
                $('.filters a.remove-filter').off('click', checkSearchButton);
                $('.filters a.remove-filter').on('click', checkSearchButton);
                $('.filter-action').toggle(true);
            } else {
                $('.filter-action').toggle(true);
                $('.filter-action > button').html('Refresh&nbsp;&nbsp;<i class="fa fa-refresh"></i>');
            }
        }

        $('a.btn.remove-filter').on('click', checkSearchButton);
        $(document).ready(function () {
            checkSearchButton();
        });

        var filter = new AdminFilters(
            '#filter_form',
            {{ label_columns | tojson | safe }},
            {{ form_fields | tojson | safe }},
            {{ search_filters | tojson | safe }},
            {{ active_filters | tojson | safe }}
        );
    }) (jQuery);

</script>