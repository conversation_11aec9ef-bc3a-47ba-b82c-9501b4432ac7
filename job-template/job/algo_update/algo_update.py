import os
import argparse
from common import info
from common import algo
from common import task

def update_algo_version():
    task = info.get_from_json("task.evaluate.best")

    algo_id = info.get_from_json("algo.id")
    algo_version = info.get_from_json("algo.version")

    print(f"algo {algo_id} version {algo_version}")
    print(task)

    algo.update_algo_version(algo_id, algo_version, task.get('hyperparams').get('Args'))

if __name__ == "__main__":
    # parser = argparse.ArgumentParser(description="算法更新配置")
    # parser.add_argument('--algo_version', type=str, required=False, help="保存算法版本，非必须")
    # args = parser.parse_args()

    update_algo_version()