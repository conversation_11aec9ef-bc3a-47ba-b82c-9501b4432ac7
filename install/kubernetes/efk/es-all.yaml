apiVersion: v1
kind: ConfigMap
metadata:
  name: es-log-config
  namespace: logging
data:
  elasticsearch.yml: |-
    http.host: 0.0.0.0
    transport.host: 0.0.0.0
    cluster.name: "my-log-es"
    discovery.type: "single-node"
    bootstrap.memory_lock: false
    xpack.license.self_generated.type: basic
  log4j2.properties: |-
    status = error
    appender.console.type = Console
    appender.console.name = console
    appender.console.layout.type = PatternLayout
    appender.console.layout.pattern = [%d{ISO8601}][%-5p][%-25c{1.}] %marker%m%n

    rootLogger.level = info
    rootLogger.appenderRef.console.ref = console
  ES_JAVA_OPTS: -Xms1G -Xmx1G
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: elasticsearch-logging
  namespace: logging
  labels:
    k8s-app: elasticsearch-logging
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: elasticsearch-logging
  labels:
    k8s-app: elasticsearch-logging
rules:
- apiGroups:
  - ""
  resources:
  - "services"
  - "namespaces"
  - "endpoints"
  verbs:
  - "get"
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  namespace: logging
  name: elasticsearch-logging
  labels:
    k8s-app: elasticsearch-logging
subjects:
- kind: ServiceAccount
  name: elasticsearch-logging
  namespace: logging
  apiGroup: ""
roleRef:
  kind: ClusterRole
  name: elasticsearch-logging
  apiGroup: ""
---
apiVersion: v1
kind: Service
metadata:
  labels:
    component: elasticsearch-log
    role: master
  name: elasticsearch-log
  namespace: logging
spec:
  ports:
  - name: http
    port: 9200
    protocol: TCP
    targetPort: 9200
  selector:
    component: elasticsearch-log
    role: master
  sessionAffinity: None
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  labels:
    component: elasticsearch-log
    role: master
  name: elasticsearch-log-discovery
  namespace: logging
spec:
  clusterIP: None
  ports:
  - name: transport
    port: 9300
    protocol: TCP
    targetPort: 9300
  selector:
    component: elasticsearch-log
    role: master
  sessionAffinity: None
  type: ClusterIP
---
#apiVersion: extensions/v1beta1
#kind: Ingress
#metadata:
#  annotations:
#    kubernetes.io/ingress.class: nginx
#    nginx.ingress.kubernetes.io/configuration-snippet: |-
#      rewrite /loges/(.*)  $1 break;
#      rewrite /loges(.*)  $1 break;
#  name: elasticsearch-logging
#  namespace: logging
#spec:
#  rules:
#  - http:
#      paths:
#      - backend:
#          serviceName: elasticsearch-log
#          servicePort: 9200
#        path: /loges/?(.*)
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    component: elasticsearch-log
    role: master
  name: es-log-master
  namespace: logging
spec:
  selector:
    matchLabels:
      component: elasticsearch-log
      role: master
  serviceName: elasticsearch-log-discovery
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        component: elasticsearch-log
        role: master
    spec:
      securityContext:
        fsGroup: 1000
      initContainers:
      - name: init-sysctl
        image: busybox:1.36.0
        imagePullPolicy: IfNotPresent
        securityContext:
          privileged: true
        command: ["sysctl", "-w", "vm.max_map_count=262144"]
      containers:
      - name: elasticsearch
        resources:
            requests:
                cpu: "1"
                memory: "2Gi"
            limits:
               cpu: "2"
               memory: "4Gi"
        securityContext:
          privileged: true
          runAsUser: 1000
          capabilities:
            add:
            - IPC_LOCK
            - SYS_RESOURCE
        image: docker.io/elasticsearch:7.16.2
        env:
        - name: ES_JAVA_OPTS
          valueFrom:
              configMapKeyRef:
                  name: es-log-config
                  key: ES_JAVA_OPTS
        readinessProbe:
          httpGet:
            scheme: HTTP
            path: /_cluster/health?local=true
            port: 9200
          initialDelaySeconds: 5
        ports:
        - containerPort: 9200
          name: es-http
        - containerPort: 9300
          name: es-transport
        volumeMounts:
        - name: storage
          mountPath: /usr/share/elasticsearch/data
        - name: elasticsearch-config
          mountPath: /usr/share/elasticsearch/config/elasticsearch.yml
          subPath: elasticsearch.yml
      volumes:
        - name: elasticsearch-config
          configMap:
            name: es-log-config
            items:
              - key: elasticsearch.yml
                path: elasticsearch.yml
  volumeClaimTemplates:
  - metadata:
      creationTimestamp: null
      name: storage
    spec:
      accessModes:
      -  ReadWriteOnce
      dataSource: null
      resources:
        requests:
          storage: 2000Gi
      storageClassName: local-efk-es
      volumeMode: Filesystem
