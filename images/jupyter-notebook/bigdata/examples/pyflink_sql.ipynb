{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyflink.table import EnvironmentSettings, TableEnvironment\n", "\n", "env_settings = EnvironmentSettings.in_streaming_mode()\n", "t_env = TableEnvironment.create(env_settings)\n", "\n", "t_env.execute_sql(\"\"\"\n", "    CREATE TABLE random_source(\n", "        id BIGINT, \n", "        data TINYINT\n", "    ) WITH (\n", "        'connector' = 'datagen',\n", "        'fields.id.kind'='sequence',\n", "        'fields.id.start'='1',\n", "        'fields.id.end'='8',\n", "        'fields.data.kind'='sequence',\n", "        'fields.data.start'='4',\n", "        'fields.data.end'='11'\n", "    )\n", "\"\"\")\n", "\n", "t_env.execute_sql(\"\"\"\n", "    CREATE TABLE print_sink (\n", "        id BIGINT, \n", "        data_sum TINYINT \n", "    ) WITH (\n", "        'connector' = 'print'\n", "    )\n", "\"\"\")\n", "\n", "t_env.execute_sql(\"\"\"\n", "    INSERT INTO print_sink \n", "        SELECT id, sum(data) as data_sum FROM \n", "            (SELECT id / 2 as id, data FROM random_source )\n", "        WHERE id > 1\n", "        GROUP BY id\n", "\"\"\").wait()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3.0}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 0}