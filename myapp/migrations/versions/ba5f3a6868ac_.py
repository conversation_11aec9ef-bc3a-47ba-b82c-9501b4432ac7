"""empty message

Revision ID: ba5f3a6868ac
Revises: 7f780f1c8d40
Create Date: 2022-09-30 07:47:42.904037

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ba5f3a6868ac'
down_revision = '7f780f1c8d40'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('aihub',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('uuid', sa.String(length=2000), nullable=True),
    sa.Column('status', sa.String(length=200), nullable=True),
    sa.Column('doc', sa.String(length=200), nullable=True),
    sa.Column('name', sa.String(length=200), nullable=True),
    sa.Column('field', sa.String(length=200), nullable=True),
    sa.Column('scenes', sa.String(length=200), nullable=True),
    sa.Column('type', sa.String(length=200), nullable=True),
    sa.Column('label', sa.String(length=200), nullable=True),
    sa.Column('describe', sa.String(length=2000), nullable=True),
    sa.Column('source', sa.String(length=200), nullable=True),
    sa.Column('pic', sa.String(length=500), nullable=True),
    sa.Column('dataset', sa.Text(), nullable=True),
    sa.Column('notebook', sa.String(length=2000), nullable=True),
    sa.Column('job_template', sa.Text(), nullable=True),
    sa.Column('pre_train_model', sa.String(length=2000), nullable=True),
    sa.Column('inference', sa.Text(), nullable=True),
    sa.Column('service', sa.Text(), nullable=True),
    sa.Column('version', sa.String(length=200), nullable=True),
    sa.Column('hot', sa.Integer(), nullable=True),
    sa.Column('price', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_unique_constraint(None, 'dimension', ['sqllchemy_uri', 'table_name'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'dimension', type_='unique')
    op.drop_table('aihub')
    # ### end Alembic commands ###
