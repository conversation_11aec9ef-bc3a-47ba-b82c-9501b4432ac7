apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: kubelet
  name: kubelet
  namespace: monitoring
spec:
  endpoints:
  - bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    honorLabels: true
    interval: 30s
    port: https-metrics   # https-metrics
    scheme: https    # https
    tlsConfig:
      insecureSkipVerify: true
      caFile: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  - bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    honorLabels: true
    interval: 30s
    path: /metrics/cadvisor
    port: https-metrics   # https-metrics
    scheme: https   #  https
    tlsConfig:
      insecureSkipVerify: true
      caFile: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  jobLabel: k8s-app
  namespaceSelector:
    matchNames:
    - kube-system
  selector:
    matchLabels:
      k8s-app: kubelet
