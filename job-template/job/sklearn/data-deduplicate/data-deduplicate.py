import pandas as pd
import argparse
from sklearn.utils import Bunch
from sklearn.preprocessing import LabelEncoder

def load_and_deduplicate_data(
    path: str,
    output_path: str,
    columns: list = None,
    na_values: list = None
) -> Bunch:
    """
    加载 CSV 数据，去除重复行，并保存。

    参数：
    - path: 原始数据路径
    - output_path: 去重后保存路径
    - columns: 列名列表（最后一列为 target）
    - na_values: 自定义缺失值标记

    返回：
    - sklearn.utils.Bunch 格式数据结构
    """
    if columns is None:
        columns = ['sepal length (cm)', 'sepal width (cm)',
                   'petal length (cm)', 'petal width (cm)', 'target']
    if na_values is None:
        na_values = ["?", ""]

    # 加载数据
    df = pd.read_csv(path, header=None, names=columns, na_values=na_values)
    print(f"[INFO] 原始数据行数: {len(df)}")

    # 去除重复
    df_deduped = df.drop_duplicates().reset_index(drop=True)
    print(f"[INFO] 去重后数据行数: {len(df_deduped)}")

    # 编码目标列
    le = LabelEncoder()
    df_deduped['target_encoded'] = le.fit_transform(df_deduped[columns[-1]])

    # 保存去重后的数据
    df_deduped[columns].to_csv(output_path, index=False, header=False)
    print(f"[INFO] 去重后的数据已保存至: {output_path}")

    feature_cols = columns[:-1]
    return Bunch(
        data=df_deduped[feature_cols].values,
        target=df_deduped['target_encoded'].values,
        target_names=le.classes_,
        feature_names=feature_cols,
        DESCR="Deduplicated dataset",
        filename=path,
        frame=df_deduped
    )



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="数据清洗")
    parser.add_argument('--input_path', type=str, required=True, help="输入数据路径")
    parser.add_argument('--output_path', type=str, required=True, help="输出数据文件")
    parser.add_argument('--columns', type=str, required=True, help="列头")
    args = parser.parse_args()

    # ✅ 示例调用
    iris_deduped = load_and_deduplicate_data(
        path=args.input_path,
        output_path=args.output_path,
        columns=str.split(args.columns, ",")
    )

    print(iris_deduped.frame.head())