"use strict";(self.webpackChunkvite_ml_platform=self.webpackChunkvite_ml_platform||[]).push([[369],{45117:function(e,n,i){i.d(n,{C:function(){return l},T:function(){return t}});var o=i(16030),t=function(){return(0,o.I0)()},l=o.v9},45062:function(e,n,i){i.r(n),i.d(n,{default:function(){return Qe}});var o=i(72791),t=i(59332),l=i(29439),a=i(29093),r=i(18511),s=i(50199),d=i(77291),c=i(70927),u=i(81318),p=i(15853),m=i(45117),x=i(16097);function f(e,n){var i=null;return function(){for(var o=this,t=arguments.length,l=new Array(t),a=0;a<t;a++)l[a]=arguments[a];clearTimeout(i),i=window.setTimeout((function(){return e.apply(o,l)}),n)}}var h=i(40528),g={modalStyles:(0,h.y0)({position:"fixed",width:"100%",height:"100vh",backgroundColor:"rgba(0, 0, 0, .5)",zIndex:999,display:"flex",justifyContent:"center",alignItems:"center"}),cancelIcon:{iconName:"Cancel"},contentStyles:(0,h.ZC)({container:{minWidth:"75%",minHeight:"80%",overflow:"hidden",display:"flex",borderRadius:2,backgroundColor:"#fff",padding:"0 24px"},header:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"10px 0 14px 0"},body:{flex:1,height:0,position:"relative",display:"flex"},footer:{position:"relative",display:"flex",padding:"24px 0",justifyContent:"flex-end"}}),resizeLine:(0,h.y0)({width:8,height:"100%",cursor:"col-resize",background:"#eee",transition:"background 0.2s",margin:"0 15px 0 2px",display:"flex",alignItems:"center",justifyContent:"center",overflow:"hidden"}),resizeLineIconStyles:(0,h.y0)({display:"flex",justifyContent:"center",alignItems:"center",boxSizing:"border-box",paddingRight:"19px",width:8,height:20,fontSize:20,pointerEvents:"none"})},v=i(39230),y=i(80184),b=function(e){var n=(0,m.T)(),i=(0,m.C)(x.ld),f=(0,m.C)(x.PF),h=(0,m.C)(x.lr),b=(0,o.useState)(""),j=(0,l.Z)(b,2),w=j[0],C=j[1],k=(0,o.useState)("100%"),S=(0,l.Z)(k,2),N=S[0],_=S[1],L=(0,o.useState)(!1),Z=(0,l.Z)(L,2),D=Z[0],T=Z[1],I=(0,o.useState)(!1),O=(0,l.Z)(I,2),B=O[0],z=O[1],H=(0,v.$G)(),W=H.t,E=(H.i18n,function(){z(!1),n((0,x.y7)(!1))}),M=function(){T(!1)};return(0,o.useEffect)((function(){C(f)}),[i]),(0,y.jsx)("div",{className:g.modalStyles,style:{display:i?"flex":"none"},children:(0,y.jsxs)(t.K,{className:g.contentStyles.container,children:[(0,y.jsxs)("div",{className:g.contentStyles.header,children:[(0,y.jsx)("span",{children:W("\u7f16\u8f91")}),(0,y.jsx)(a.h,{iconProps:g.cancelIcon,onClick:E})]}),(0,y.jsxs)("div",{className:g.contentStyles.body,onMouseMove:function(e){D&&e.clientX>370&&_(e.clientX-270)},onMouseUp:M,onMouseLeave:M,children:[(0,y.jsx)("div",{style:{height:"100%",width:N},children:(0,y.jsx)(p.ZP,{language:h||"json",theme:"vs",value:w,onChange:function(e){z(!1),C(e)},height:500,options:{renderValidationDecorations:"on",automaticLayout:!0,autoClosingOvertype:"always",cursorStyle:"block",quickSuggestions:!1,scrollBeyondLastLine:!1,snippetSuggestions:"none",minimap:{enabled:!1}}})}),(0,y.jsx)("div",{className:g.resizeLine,onMouseDown:function(){T(!0)},children:(0,y.jsx)(r.J,{iconName:"BulletedListBulletMirrored",className:g.resizeLineIconStyles})}),(0,y.jsx)("div",{style:{flex:1}})]}),(0,y.jsxs)("div",{className:g.contentStyles.footer,children:[B?(0,y.jsx)(s.c,{messageBarType:d.f.error,styles:{root:{position:"absolute",width:"50%",left:0}},isMultiline:!1,children:W("\u683c\u5f0f\u9519\u8bef")}):null,(0,y.jsx)(c.K,{styles:{root:{marginRight:10}},onClick:function(){"json"!==h||function(e){try{return JSON.parse(e),!0}catch(n){return!1}}(w)?(n((0,x.sW)(w)),n((0,x.y7)(!1))):z(!0)},children:W("\u786e\u8ba4")}),(0,y.jsx)(u.a,{onClick:E,children:W("\u53d6\u6d88")})]})]})})},j=i(48636),w=i(4942),C=i(2692),k=i(97708),S=i(90110),N=i(17504),_=i(53997),L=i(64011),Z=i(42081),D=i(65223),T=i(57217),I=i(1413),O=i(56756),B=i(70586),z={settingContainer:(0,h.y0)({display:"flex",flexDirection:"column",width:350,position:"absolute",top:0,right:0,bottom:0,height:"auto",backgroundColor:"#fff",borderLeft:"1px solid rgb(234, 234, 234)",boxShadow:"rgb(0 0 0 / 18%) 0px 1.6px 3.6px 0px, rgb(0 0 0 / 22%) 0px 0.3px 0.9px 0px",zIndex:999}),settingHeader:(0,h.y0)({display:"flex",width:"100%",alignItems:"center",justifyContent:"space-between",color:"balck",boxSizing:"border-box",padding:"10px 20px"}),headerTitle:(0,h.y0)({fontSize:20,fontWeight:600,textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",color:"black"}),settingContent:(0,h.y0)({flex:"1 1 0%",overflowY:"auto","&::-webkit-scrollbar":{width:4},"&::-webkit-scrollbar-thumb":{minHeight:"15px",border:"6px solid transparent",backgroundClip:"padding-box",backgroundColor:"rgb(200, 200, 200)"}}),contentWrapper:(0,h.y0)({padding:"0 20px 20px",overflowX:"hidden",wordBreak:"break-word",userSelect:"text",borderTop:"1px solid #eaeaea"}),splitLine:(0,h.y0)({margin:"10px -20px 0 -20px",borderTop:"1px solid #eaeaea"})},H=i(27675),W=i(76483),E=(i(42173),i(2924)),M=i(72426),R=i.n(M),P=function(){var e=(0,m.T)(),n=(0,m.C)(j.H6),i=(0,m.C)(D.RU),t=(0,m.C)(j.mg),r=(0,m.C)(Z.hr),s=(0,o.useState)({}),d=(0,l.Z)(s,2),c=d[0],u=d[1],p=(0,o.useState)([]),x=(0,l.Z)(p,2),f=x[0],h=x[1],g=(0,o.useState)(),b=(0,l.Z)(g,2),w=b[0],C=b[1],k=(0,v.$G)(),S=k.t,L=(k.i18n,function(i,o){var l={};if(l[i]=o,"project"===i){var a=o;C(a),l[i]=a.key}n&&(u((0,I.Z)((0,I.Z)({},c),l)),e((0,j.v_)((0,I.Z)((0,I.Z)({},t),l))))});return(0,o.useEffect)((function(){if(n){var e,i;u(n);var o={key:null===n||void 0===n||null===(e=n.project)||void 0===e?void 0:e.id,text:null===n||void 0===n||null===(i=n.project)||void 0===i?void 0:i.name};C(o)}}),[n]),(0,o.useEffect)((function(){Object.keys(t).length>1&&e((0,j.u6)(!0))}),[t]),(0,o.useEffect)((function(){_.Z.project_modelview().then((function(e){var n=null===e||void 0===e?void 0:e.result.data.reduce((function(e,n){if("org"===n.type){var i={key:n.id,text:n.name};e.push(i)}return e}),[]);h(n)}))}),[]),(0,o.useEffect)((function(){var n={};r.forEach((function(e){if((0,N.un)(e)){var i,o=r.filter((function(n){return n.id===e.source}))[0],t=r.filter((function(n){return n.id===e.target}))[0];null!==(i=n["".concat(t.data.name)])&&void 0!==i&&i.upstream?n["".concat(t.data.name)].upstream.push("".concat(o.data.name)):(n["".concat(t.data.name)]={},n["".concat(t.data.name)].upstream=["".concat(o.data.name)])}}));var i=(0,I.Z)({},n);u((0,I.Z)((0,I.Z)({},c),{dag_json:JSON.stringify(i,void 0,4)})),e((0,j.v_)((0,I.Z)((0,I.Z)({},t),{dag_json:JSON.stringify(i)})))}),[r]),(0,y.jsxs)("div",{style:{visibility:i?"visible":"hidden"},className:z.settingContainer,children:[(0,y.jsxs)("div",{className:z.settingHeader,children:[(0,y.jsx)("div",{className:z.headerTitle,children:S("\u6d41\u6c34\u7ebf\u8bbe\u7f6e")}),(0,y.jsx)(a.h,{iconProps:{iconName:"ChromeClose",styles:{root:{fontSize:12,color:"#000"}}},onClick:function(){e((0,D.ZN)())}})]}),(0,y.jsx)("div",{className:z.settingContent,children:(0,y.jsxs)("div",{className:z.contentWrapper,children:[(0,y.jsx)(O.L,{label:S("\u9879\u76ee\u7ec4"),onChange:function(e,n){L("project",n||"")},selectedKey:w?w.key:void 0,placeholder:"Select an option",options:f}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u540d\u79f0"),description:S("\u82f1\u6587\u540d(\u5b57\u6bcd\u3001\u6570\u5b57\u3001- \u7ec4\u6210)\uff0c\u6700\u957f50\u4e2a\u5b57\u7b26"),onChange:function(e,n){L("name",n||"")},value:(null===c||void 0===c?void 0:c.name)||"",disabled:!0}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u63cf\u8ff0"),required:!0,onChange:function(e,n){L("describe",n||"")},value:(null===c||void 0===c?void 0:c.describe)||""}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(O.L,{label:S("\u8c03\u5ea6\u4f18\u5148\u7ea7"),options:[{key:"high",text:"high"},{key:"low",text:"low"}],selectedKey:(null===c||void 0===c?void 0:c.priority)||"high",onChange:function(e,n){L("priority","".concat(null===n||void 0===n?void 0:n.text)||"")},disabled:!0}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(O.L,{label:S("\u8c03\u5ea6\u7c7b\u578b"),options:[{key:"once",text:"once"},{key:"crontab",text:"crontab"}],selectedKey:null===c||void 0===c?void 0:c.schedule_type,onChange:function(e,n){L("schedule_type","".concat(null===n||void 0===n?void 0:n.text)||"")}}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u8c03\u5ea6\u5468\u671f"),description:S("\u5468\u671f\u4efb\u52a1\u7684\u65f6\u95f4\u8bbe\u5b9a\u6309\u7167crontab\u4e66\u5199\u683c\u5f0f"),onChange:function(e,n){L("cron_time",n||"")},value:(null===c||void 0===c?void 0:c.cron_time)||""}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)("div",{style:{fontWeight:600,padding:"5px 0px"},children:S("\u76d1\u63a7\u72b6\u6001")}),(0,y.jsx)(H.Z,{style:{width:"100%",border:"1px solid rgb(55, 55, 55)"},value:null!==c&&void 0!==c&&c.alert_status?((null===c||void 0===c?void 0:c.alert_status)||"").split(","):void 0,onChange:function(e){L("alert_status",(e||[]).join(","))},mode:"multiple",options:[{label:"Created",value:"Created"},{label:"Pending",value:"Pending"},{label:"Running",value:"Running"},{label:"Succeeded",value:"Succeeded"},{label:"Failed",value:"Failed"},{label:"Unknown",value:"Unknown"},{label:"Waiting",value:"Waiting"},{label:"Terminated",value:"Terminated"}]}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u62a5\u8b66\u4eba"),description:S("\u6bcf\u4e2a\u7528\u6237\u4f7f\u7528\u82f1\u6587\u9017\u53f7\u5206\u9694"),onChange:function(e,n){L("alert_user",n||"")},value:(null===c||void 0===c?void 0:c.alert_user)||""}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)("div",{style:{fontWeight:600,padding:"5px 0px"},children:S("\u8865\u5f55\u8d77\u70b9")}),(0,y.jsx)(W.Z,{style:{width:"100%",border:"1px solid rgb(55, 55, 55)"},locale:E.Z,showTime:!0,value:null!==c&&void 0!==c&&c.cronjob_start_time?R()(null===c||void 0===c?void 0:c.cronjob_start_time):void 0,onChange:function(e,n){L("cronjob_start_time",n)},disabledDate:function(e){return e&&e>R()().endOf("day")}}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(O.L,{label:S("\u8fc7\u5f80\u4f9d\u8d56"),options:[{key:"true",text:S("\u662f"),data:!0},{key:"false",text:S("\u5426"),data:!1}],selectedKey:"".concat(c.depends_on_past),onChange:function(e,n){L("depends_on_past",null===n||void 0===n?void 0:n.data)}}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u6700\u5927\u6fc0\u6d3b\u8fd0\u884c\u6570"),description:S("\u5f53\u524dpipeline\u53ef\u540c\u65f6\u8fd0\u884c\u7684\u4efb\u52a1\u6d41\u5b9e\u4f8b\u6570\u76ee"),onChange:function(e,n){L("max_active_runs",n?+n:"")},value:(null===c||void 0===c?void 0:c.max_active_runs)||"",required:!0}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u4fdd\u7559\u5b9e\u4f8b\u6570\u76ee"),description:S("\u5b9a\u65f6\u8c03\u5ea6\u53ea\u4fdd\u7559\u6700\u65b0\u7684n\u6b21\u5b9e\u4f8b\u8fd0\u884c\uff0c0\u8868\u793a\u4e0d\u9650\u5236"),onChange:function(e,n){L("expired_limit",n?+n:"0")},value:(null===c||void 0===c?void 0:c.expired_limit)||"0"}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u4efb\u52a1\u5e76\u884c\u6570"),description:S("pipeline\u4e2d\u53ef\u540c\u65f6\u8fd0\u884c\u7684task\u6570\u76ee"),onChange:function(e,n){L("parallelism",n?+n:"")},value:(null===c||void 0===c?void 0:c.parallelism)||"",required:!0}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u6d41\u5411\u56fe"),onChange:function(e,n){L("dag_json",n||"{}")},value:(null===c||void 0===c?void 0:c.dag_json)||"{}",multiline:!0,autoAdjustHeight:!0,disabled:!0}),(0,y.jsx)("div",{className:z.splitLine}),(0,y.jsx)(B.n,{label:S("\u5168\u5c40\u73af\u5883\u53d8\u91cf"),description:S("\u4e3a\u6bcf\u4e2atask\u90fd\u6dfb\u52a0\u7684\u516c\u5171\u53c2\u6570"),onChange:function(e,n){L("global_env",n||"")},multiline:!0,autoAdjustHeight:!0,value:(null===c||void 0===c?void 0:c.global_env)||""})]})})]})},F=i(25443),G=i(93433),K=i(97776),A=i(95141),J=i(67808),$=i(67422),U=i(54794),X=i(63934),V={modelContainer:(0,h.y0)({display:"flex",flexDirection:"column",width:350,position:"absolute",top:0,right:0,bottom:0,height:"auto",backgroundColor:"#fff",borderLeft:"1px solid rgb(234, 234, 234)",boxShadow:"rgb(0 0 0 / 18%) 0px 1.6px 3.6px 0px, rgb(0 0 0 / 22%) 0px 0.3px 0.9px 0px",zIndex:998}),modelHeader:(0,h.y0)({display:"flex",width:"100%",alignItems:"center",justifyContent:"space-between",color:"balck",boxSizing:"border-box",padding:"10px 20px"}),headerTitle:(0,h.y0)({fontSize:20,textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",color:"black",fontWeight:600}),modelContent:(0,h.y0)({flex:"1 1 0%",overflowY:"auto","&::-webkit-scrollbar":{width:4},"&::-webkit-scrollbar-thumb":{minHeight:"15px",border:"6px solid transparent",backgroundClip:"padding-box",backgroundColor:"rgb(200, 200, 200)"}}),contentWrapper:(0,h.y0)({padding:"0 20px 20px",overflowX:"hidden",wordBreak:"break-word",userSelect:"text",borderTop:"1px solid #eaeaea"}),splitLine:(0,h.y0)({margin:"10px -20px 0 -20px",borderTop:"1px solid #eaeaea"}),settingControl:(0,h.y0)({height:"auto",display:"flex",flexDirection:"column",paddingTop:20}),saveButton:(0,h.y0)({alignSelf:"center"}),debugButton:(0,h.y0)({display:"flex",flexDirection:"row",justifyContent:"space-evenly",paddingTop:20}),templateConfig:(0,h.y0)({color:"rgb(96, 94, 92)",fontSize:10,paddingTop:5,paddingLeft:2,"> a":{textDecoration:"none"}}),argsDescription:(0,h.y0)({color:"rgb(96, 94, 92)",fontSize:10,"> a":{textDecoration:"none"}}),textLabelStyle:(0,h.y0)({display:"flex",alignItems:"center",justifyContent:"space-between",fontWeight:600,color:"rgb(50, 49, 48)",boxSizing:"border-box",boxShadow:"none",margin:"0px",padding:"5px 0px",overflowWrap:"break-word",lineHeight:"30px"}),tipStyle:(0,h.y0)({maxWidth:"500px !important",maxHeight:"400px",overflowY:"auto"})},q=i(6401),Y=i(99917),Q=i(73712),ee=(i.p,function(e){var n=(0,m.T)(),i=(0,m.C)(Z.hr),t=(0,m.C)(T.Aj),a=(0,m.C)(x.ld),r=(0,m.C)(x.OA),s=(0,m.C)(x.PF),d=(0,o.useState)({}),c=(0,l.Z)(d,2),u=c[0],p=c[1],f=(0,o.useState)({}),h=(0,l.Z)(f,2),g=h[0],b=h[1],j=(0,o.useState)({}),w=(0,l.Z)(j,2),C=w[0],k=w[1],S=(0,o.useState)({}),N=(0,l.Z)(S,2),D=N[0],z=N[1],H=(0,o.useState)({}),W=(0,l.Z)(H,2),E=W[0],M=W[1],R=(0,v.$G)(),P=R.t,F=(R.i18n,(0,o.useState)("clear")),ee=(0,l.Z)(F,2),ne=ee[0],ie=ee[1],oe=[{key:"debug",text:"debug",onClick:function(){return te("debug")},iconProps:{iconName:"Bug"}},{key:"run",text:"run",onClick:function(){return te("run")},iconProps:{iconName:"Play"}},{key:"log",text:"log",onClick:function(){return te("log")},iconProps:{iconName:"TimeEntry"}},{key:"clear",text:ne||"clear",onClick:function(){return le("clear")},iconProps:{iconName:"Unsubscribe"}}],te=function(n){ie("clear"),e.model.id&&window.open("".concat(window.location.origin,"/task_modelview/api/").concat(n,"/").concat(e.model.id))},le=function(n){e.model.id&&(ie("cleaning"),_.Z.task_modelview_clear(e.model.id).then((function(e){ie("cleared")})))},ae=function(e,n,i){var o={},t=null;switch(i){case"json":try{t=JSON.parse("".concat(n))}catch(a){t=n}break;case"int":case"float":t=+n;break;default:t=n}if(i){var l=JSON.parse(JSON.stringify(E));l[e]=t,o.args=JSON.stringify(l)}else o[e]=n;b((0,I.Z)((0,I.Z)({},g),o)),p((0,I.Z)((0,I.Z)({},u),o)),null!==o&&void 0!==o&&o.args&&M((0,I.Z)((0,I.Z)({},E),JSON.parse(o.args)))};return(0,o.useEffect)((function(){e.model.selected&&(n((0,T.kQ)(+e.model.id)),0===Object.keys(u).length&&_.Z.task_modelview_get(+e.model.id).then((function(e){if(0===e.status){var n=JSON.parse(e.result.args),i=e.result.job_template,o=null!==i&&void 0!==i&&i.args?JSON.parse(i.args):{},t=Object.keys(o).reduce((function(e,n){var i=o[n];return Object.keys(i).forEach((function(n){e[n]=i[n].default})),e}),{});p(e.result),M(Object.assign(t,n)),z(o),k(i)}})).catch((function(e){e.response&&n((0,L.L$)({msg:e.response.data.message}))})))}),[e.model.selected]),(0,o.useEffect)((function(){if(e.model.id&&n((0,T.hs)({id:+e.model.id,changed:g})),null!==g&&void 0!==g&&g.label){var o=i.map((function(n){if(n.id===e.model.id){var i=(0,I.Z)((0,I.Z)({},n.data),{label:g.label});return(0,I.Z)((0,I.Z)({},n),{data:i})}return n}));n((0,Z.OV)(o))}}),[g]),(0,o.useEffect)((function(){Object.keys(D).length>0&&+e.model.id===t&&!a&&ae(r,s,"json")}),[s]),(0,y.jsxs)("div",{className:V.modelContainer,style:{visibility:e.model.selected?"visible":"hidden"},children:[(0,y.jsx)("div",{className:V.modelHeader,children:(0,y.jsx)("div",{className:V.headerTitle,children:(null===u||void 0===u?void 0:u.name)||""})}),(0,y.jsx)("div",{className:V.modelContent,children:(0,y.jsxs)("div",{className:V.contentWrapper,children:[(0,y.jsx)(K.X,{items:oe,overflowItems:[],styles:{root:{padding:0}}}),(0,y.jsx)(B.n,{label:P("\u4efb\u52a1\u6a21\u677f"),value:(null===C||void 0===C?void 0:C.name)||"",disabled:!0,readOnly:!0,onRenderDescription:function(){var e="";try{var n;if(C.expand)e=(null===(n=JSON.parse(C.expand))||void 0===n?void 0:n.help_url)||""}catch(i){console.error(i)}return(0,y.jsx)("div",{className:V.templateConfig,children:e?(0,y.jsx)("a",{href:e,target:"_blank",rel:"noreferrer",children:P("\u914d\u7f6e\u6587\u6863")}):null})}}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(q.Z,{title:(null===C||void 0===C?void 0:C.describe)||"",className:"mr8",placement:"bottom",children:(0,y.jsx)("span",{children:(0,y.jsx)(B.n,{label:P("\u6a21\u677f\u63cf\u8ff0"),value:(null===C||void 0===C?void 0:C.describe)||"",disabled:!0,readOnly:!0,style:{pointerEvents:"none"}})})}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("\u540d\u79f0"),onChange:function(e,n){ae("name",n||"")},value:(null===u||void 0===u?void 0:u.name)||"",disabled:!0}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("\u6807\u7b7e"),description:P("\u8282\u70b9\u6807\u7b7e"),required:!0,onChange:function(e,n){ae("label",n||"")},value:(null===u||void 0===u?void 0:u.label)||""}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("\u5185\u5b58\u7533\u8bf7"),description:P("\u5185\u5b58\u7684\u8d44\u6e90\u4f7f\u7528\u9650\u5236\uff0c\u793a\u4f8b1G\uff0c10G\uff0c \u6700\u5927100G\uff0c\u5982\u9700\u66f4\u591a\u8054\u7cfb\u7ba1\u7406\u5458"),onChange:function(e,n){ae("resource_memory",n||"")},value:(null===u||void 0===u?void 0:u.resource_memory)||"",required:!0}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("CPU\u7533\u8bf7"),description:P("CPU\u7684\u8d44\u6e90\u4f7f\u7528\u9650\u5236(\u5355\u4f4d\u6838)\uff0c\u793a\u4f8b 0.4\uff0c10\uff0c\u6700\u592750\u6838\uff0c\u5982\u9700\u66f4\u591a\u8054\u7cfb\u7ba1\u7406\u5458"),onChange:function(e,n){ae("resource_cpu",n||"")},value:(null===u||void 0===u?void 0:u.resource_cpu)||"",required:!0}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("GPU\u7533\u8bf7"),description:P("gpu\u7684\u8d44\u6e90\u4f7f\u7528\u9650\u5236(\u5355\u4f4d\u5361)\uff0c\u793a\u4f8b:1\uff0c2\uff0c\u8bad\u7ec3\u4efb\u52a1\u6bcf\u4e2a\u5bb9\u5668\u72ec\u5360\u6574\u5361\u3002\u7533\u8bf7\u5177\u4f53\u7684\u5361\u578b\u53f7\uff0c\u53ef\u4ee5\u7c7b\u4f3c 1(V100)"),onChange:function(e,n){ae("resource_gpu",n||"")},value:(null===u||void 0===u?void 0:u.resource_gpu)||""}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("RDMA\u7533\u8bf7"),description:P("RDMA\u7684\u8d44\u6e90\u4f7f\u7528\u9650\u5236\uff0c\u793a\u4f8b 0\uff0c1\uff0c10\uff0c\u586b\u5199\u65b9\u5f0f\u54a8\u8be2\u7ba1\u7406\u5458"),onChange:function(e,n){ae("resource_rdma",n||"")},value:(null===u||void 0===u?void 0:u.resource_rdma)||"",required:!0}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("\u8d85\u65f6\u4e2d\u65ad"),description:P("task\u8fd0\u884c\u65f6\u957f\u9650\u5236\uff0c\u4e3a0\u8868\u793a\u4e0d\u9650\u5236(\u5355\u4f4ds)"),onChange:function(e,n){ae("timeout",n||"")},value:(null===u||void 0===u?void 0:u.timeout)||0}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)(B.n,{label:P("\u91cd\u8bd5\u6b21\u6570"),description:P("task\u91cd\u8bd5\u6b21\u6570"),onChange:function(e,n){ae("retry",n||"")},value:(null===u||void 0===u?void 0:u.retry)||0}),(0,y.jsx)("div",{className:V.splitLine}),(0,y.jsx)("div",{style:{fontWeight:600,padding:"5px 0px"},children:P("\u662f\u5426\u8df3\u8fc7")}),(0,y.jsx)(Y.Z,{checkedChildren:P("\u662f"),unCheckedChildren:P("\u5426"),checked:!(null===u||void 0===u||!u.skip),onChange:function(e){ae("skip",e)}}),Object.keys(D).reduce((function(e,i){var t=D[i],l=Object.keys(t).map((function(e){var i=t[e],l=(0,Q.TU)(i.tip||"",{async:!1}),a=(0,y.jsx)(q.Z,{overlayClassName:V.tipStyle,title:(0,y.jsx)("span",{className:"cube-tip",dangerouslySetInnerHTML:{__html:l}}),children:(0,y.jsx)("span",{style:{fontWeight:"bold",color:"#0078d4",marginLeft:"10px"},children:P("\u8be6\u60c5")})}),r=i.choice,s=[];Array.isArray(r)?s=r.map((function(e){return{key:e,text:e}})):"object"===typeof r&&null!==r&&(s=Object.keys(r).map((function(e){return{key:e,text:r[e]}}))),console.log(s);var d=E&&E[e],c="json"===i.type?JSON.stringify(d,void 0,4):d;if("float"===i.type){var u="string"===typeof i.range?i.range.split(","):i.range;return(0,y.jsx)(o.Fragment,{children:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(A.i,{styles:{valueLabel:{margin:"0",width:"auto"}},label:"".concat(e),min:u[0]||0,max:u[1]||1,step:i.step||.1,onChange:function(n){ae(e,n||0,i.type)},value:c,disabled:1!==i.editable,showValue:!0}),(0,y.jsxs)("div",{className:V.argsDescription,children:[(0,y.jsx)("span",{dangerouslySetInnerHTML:{__html:i.describe}}),i.tip?a:null]})]})},e)}if("int"===i.type){var p="string"===typeof i.range?i.range.split(","):i.range;return(0,y.jsx)(o.Fragment,{children:(0,y.jsxs)("div",{style:{width:"100%"},children:[(0,y.jsx)(J.k,{styles:{root:{display:"flex",flexDirection:"column",alignItems:"flex-start",width:"100%"},spinButtonWrapper:{width:"100%"},labelWrapper:{marginBottom:"4px"}},label:"".concat(e),min:p||0,max:p||100,step:i.step||1,onIncrement:function(n){ae(e,n?parseInt(n,10)+1:1,i.type)},onDecrement:function(n){ae(e,n?parseInt(n,10)-1:-1,i.type)},value:c,disabled:1!==i.editable}),(0,y.jsxs)("div",{className:V.argsDescription,children:[(0,y.jsx)("span",{dangerouslySetInnerHTML:{__html:i.describe}}),i.tip?a:null]})]})},e)}if("bool"===i.type)return(0,y.jsx)(o.Fragment,{children:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)($.Z,{label:"".concat(e),checked:c,onChange:function(n,o){ae(e,o||!1,i.type)},onText:"On",offText:"Off",disabled:1!==i.editable}),(0,y.jsxs)("div",{className:V.argsDescription,children:[(0,y.jsx)("span",{dangerouslySetInnerHTML:{__html:i.describe}}),i.tip?a:null]})]})},e);if("list"===i.type){var m=("string"===typeof c?c:i.default).split(",");return(0,y.jsx)(o.Fragment,{children:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(O.L,{label:"".concat(e),onChange:function(n,o){var t,l=("string"===typeof c?c:i.default).split(","),a=l=l.filter((function(e){return""!==e.trim()}));null!==o&&void 0!==o&&o.selected?l.includes(o.key)||(a=[].concat((0,G.Z)(l),[o.key])):a=l.filter((function(e){return e!==(null===o||void 0===o?void 0:o.key)}));var r=null===(t=a)||void 0===t?void 0:t.join(",");ae(e,r||"",i.type)},selectedKeys:m,options:s,required:1===i.require,disabled:1!==i.editable,multiSelect:!0}),(0,y.jsxs)("div",{className:V.argsDescription,children:[(0,y.jsx)("span",{dangerouslySetInnerHTML:{__html:i.describe}}),i.tip?a:null]})]})},e)}return(0,y.jsx)(o.Fragment,{children:s.length>0?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(O.L,{label:"".concat(e),onChange:function(n,o){ae(e,"".concat(null===o||void 0===o?void 0:o.key)||"",i.type)},defaultSelectedKey:c||i.default,options:s,required:1===i.require,disabled:1!==i.editable}),(0,y.jsxs)("div",{className:V.argsDescription,children:[(0,y.jsx)("span",{dangerouslySetInnerHTML:{__html:i.describe}}),i.tip?a:null]})]}):(0,y.jsx)(B.n,{onRenderLabel:function(){return(0,y.jsxs)("div",{className:V.textLabelStyle,children:["".concat(e),"json"===i.type||"text"===i.type?(0,y.jsx)(U.K,{iconProps:{iconName:"FullWidthEdit"},onClick:function(){n((0,x.vh)({key:e,value:c,type:"json"===i.type?"json":i.item_type||"str"})),n((0,x.y7)(!0))},children:P("\u7f16\u8f91")}):"workdir"===(i.item_type||"str")?(0,y.jsx)(U.K,{iconProps:{iconName:"FabricFolder"},onClick:function(){window.open("/notebook_modelview/api/entry/jupyter?file_path="+c,"_blank")},children:P("\u6253\u5f00\u76ee\u5f55")}):"image"===(i.item_type||"str")?(0,y.jsx)(U.K,{iconProps:{iconName:"Bug"},onClick:function(){window.open("/docker_modelview/api/entry/docker?image="+c,"_blank")},children:P("\u8c03\u8bd5\u955c\u50cf")}):null]})},onRenderDescription:function(){return(0,y.jsxs)("div",{className:V.argsDescription,children:[(0,y.jsx)("span",{dangerouslySetInnerHTML:{__html:i.describe}}),i.tip?a:null]})},multiline:"json"===i.type||"text"===i.type,autoAdjustHeight:"json"===i.type||"text"===i.type,onChange:function(n,o){ae(e,o||"",i.type)},value:c,required:1===i.require,disabled:1!==i.editable||"json"===i.type})},e)}));return e.push((0,y.jsxs)(o.Fragment,{children:[(0,y.jsxs)(X._,{children:[P("\u53c2\u6570")," ",i]}),l.flat(),(0,y.jsx)("div",{className:V.splitLine})]},i)),e}),[])]})})]})}),ne={height:54,minWidth:272,borderRadius:100,borderStyle:"solid",display:"flex",flexDirection:"row",backgroundColor:"#fff",fontSize:12,cursor:"move",boxSizing:"border-box",transition:"all 0.3s"},ie=(0,h.y0)((0,I.Z)((0,I.Z)({},ne),{borderWidth:1,borderColor:"#b1b1b7"})),oe=(0,h.y0)((0,I.Z)((0,I.Z)({},ne),{borderWidth:1,borderColor:"#006dce",backgroundColor:"#f1f7fd"})),te=(0,h.y0)({width:8,flexShrink:0,borderRadius:"3px 0 0 3px",borderRight:"1px solid #8a8886",margin:"-8px 0 -8px -8px"}),le=(0,h.y0)({boxSizing:"border-box",display:"flex",flexDirection:"column",overflow:"hidden"}),ae=(0,h.y0)({display:"flex",marginLeft:7,minHeight:26}),re=(0,h.y0)({fontSize:16,width:64,display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgb(0, 120, 212)",borderTopLeftRadius:100,borderBottomLeftRadius:100,margin:"-1px 0 -1px -1px"}),se=(0,h.y0)({userSelect:"none",boxSizing:"border-box",color:"#fff",fontSize:20,marginLeft:8}),de=(0,h.y0)({whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",paddingLeft:8,paddingBottom:2,marginBottom:2,fontWeight:600,fontSize:14,borderBottom:"1px dashed #c1c1c1",userSelect:"none"}),ce=(0,h.y0)({width:"12px !important",height:"12px !important",bottom:"-7px !important",borderColor:"#b1b1b7 !important",backgroundColor:"#fff !important",transition:"all 0.3s","&:hover":{borderWidth:"2px !important",borderColor:"var(--hover-color) !important",cursor:"pointer !important"}}),ue=(0,h.y0)({visibility:"hidden"}),pe=(0,h.y0)({height:"100%",width:"100%",display:"flex",justifyContent:"center",flexDirection:"column"}),me={nodeTips:(0,h.y0)({color:"rgb(177, 177, 183)",paddingLeft:8,maxWidth:"250px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}),nodeContentWrapper:pe,nodeContainer:ie,nodeOnSelect:oe,nodeBar:te,nodeContent:le,nodeConentTitleBar:ae,nodeIconWrapper:re,nodeIcon:se,nodeTitle:de,handleStyle:ce,hidden:ue},xe=i(81065),fe=i(15669),he=i(91115),ge=i(25303),ve={get:function(e){var n=window.localStorage.getItem(e);return n&&n.match(/^(\{|\[).*(?=[}\]])/)?JSON.parse(n):n||""},set:function(e,n){window.localStorage.setItem(e,"string"===typeof n?n:JSON.stringify(n))}},ye={dataSet:function(e){var n,i,t,a,s,d,c,u,p,x,f,h,g=(0,o.useState)(!1),b=(0,l.Z)(g,2),C=b[0],k=b[1],S=(0,o.useState)([]),D=(0,l.Z)(S,2),O=D[0],B=D[1],z=(0,o.useState)(),H=(0,l.Z)(z,2),W=H[0],E=H[1],M=(ve.get("job_template").value||[]).reduce((function(e,n){return(0,I.Z)((0,I.Z)({},e),{},(0,w.Z)({},n.name,n))}),{}),R=(0,m.C)(j.Bn),P=(0,m.C)(Z.hr),G=(0,m.T)(),K=(0,v.$G)(),A=K.t;K.i18n;return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(xe.Z,{title:A("\u667a\u80fd\u63a8\u8350\u4e0b\u6e38\u8282\u70b9"),visible:C,onCancel:function(){E(void 0),k(!1)},onOk:function(){if(W){var n=M[W],i=JSON.parse(n.args),o={};Object.keys(i).reduce((function(e,n){var o={};return Object.keys(i[n]).reduce((function(e,o){var t=i[n][o].default;return Object.assign(e,(0,w.Z)({},o,t)),e}),o),Object.assign(e,o),e}),o);var t={x:e.xPos||0,y:+((null===e||void 0===e?void 0:e.yPos)||0)+100};if(R){G((0,T.Xt)(!0));var l="".concat(n.name.replace(/\.|[\u4e00-\u9fa5]/g,"").replace(/_|\s/g,"-")||"task","-").concat(Date.now()).substring(0,49);_.Z.task_modelview_add(R,{job_template:n.id,pipeline:+R,name:l,label:"".concat(A("\u65b0\u5efa")," ").concat(n.name," ").concat(A("\u4efb\u52a1")),volume_mount:"kubeflow-user-workspace(pvc):/mnt",image_pull_policy:"Always",working_dir:"",command:"",overwrite_entrypoint:0,node_selector:"cpu=true,train=true",resource_memory:"2G",resource_cpu:"2",resource_gpu:"0",resource_rdma:"0",timeout:0,retry:0,args:JSON.stringify(o)}).then((function(e){var i;if(null!==e&&void 0!==e&&null!==(i=e.result)&&void 0!==i&&i.id){var o={id:"".concat(e.result.id),type:"dataSet",position:t,data:{info:n,name:l,label:"".concat(A("\u65b0\u5efa")," ").concat(n.name," ").concat(A("\u4efb\u52a1"))}};G((0,j.u6)(!0)),G((0,Z.OV)(P.concat(o))),setTimeout((function(){G((0,j.oL)())}),2e3)}})).catch((function(e){e.response&&G((0,L.L$)({msg:e.response.data.message}))})).finally((function(){G((0,T.Xt)(!1)),k(!1)}))}}else fe.ZP.warn(A("\u8bf7\u5148\u9009\u62e9\u63a8\u8350\u8282\u70b9"))},children:(0,y.jsx)("div",{children:(0,y.jsx)(he.ZP.Group,{onChange:function(e){E(e.target.value)},value:W,children:(0,y.jsx)(ge.Z,{direction:"vertical",children:O.map((function(e){return(0,y.jsx)(he.ZP,{value:e.name,children:e.name},"recommend_".concat(e.name))}))})})})}),(0,y.jsx)(N.HH,{type:"target",position:N.Ly.Top,className:me.handleStyle}),(0,y.jsxs)("div",{className:e.selected?me.nodeOnSelect:me.nodeContainer,style:{borderColor:e.selected?null===e||void 0===e||null===(n=e.data)||void 0===n||null===(i=n.info)||void 0===i||null===(t=i.color)||void 0===t?void 0:t.color:"",backgroundColor:e.selected?null===e||void 0===e||null===(a=e.data)||void 0===a||null===(s=a.info)||void 0===s||null===(d=s.color)||void 0===d?void 0:d.bg:""},children:[(0,y.jsx)("div",{className:me.nodeIconWrapper,style:{backgroundColor:null===e||void 0===e||null===(c=e.data)||void 0===c||null===(u=c.info)||void 0===u||null===(p=u.color)||void 0===p?void 0:p.color},children:(0,y.jsx)(r.J,{iconName:"Database",className:me.nodeIcon})}),(0,y.jsxs)("div",{className:me.nodeContentWrapper,children:[(0,y.jsx)("div",{className:me.nodeTitle,children:null===e||void 0===e||null===(x=e.data)||void 0===x?void 0:x.label}),null!==e&&void 0!==e&&null!==(f=e.data)&&void 0!==f&&f.info?(0,y.jsx)("div",{className:me.nodeTips,children:null===e||void 0===e||null===(h=e.data)||void 0===h?void 0:h.info.describe}):null]})]}),(0,y.jsx)(N.HH,{type:"source",onClick:function(){var n,i;console.log("props",e);var o,t,l=null===(n=e.data)||void 0===n||null===(i=n.info)||void 0===i?void 0:i.expand;"[object String]"===Object.prototype.toString.call(l)&&(l=JSON.parse((null===(o=e.data)||void 0===o||null===(t=o.info)||void 0===t?void 0:t.expand)||"{}"));var a=l.rec_job_template;a&&(B([M[a]]),k(!0))},position:N.Ly.Bottom,className:me.handleStyle}),(0,y.jsx)(F.m,{hostId:"hostId_model",className:"data-set-layer",children:(0,y.jsx)(ee,{model:e})})]})}},be={itemStyles:(0,h.y0)({position:"relative"}),flowWrapper:(0,h.y0)({width:"100%",height:"100%"}),spinnerWrapper:(0,h.y0)({position:"absolute",top:0,left:0,display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",overflow:"hidden",backgroundColor:"rgba(255, 255, 255, 0.6)",zIndex:9999})},je=t.K.Item,we=function(){var e=(0,m.T)(),n=(0,m.C)(Z.hr),i=(0,m.C)(j.Bn),t=(0,m.C)(j.H6),a=(0,m.C)(D.RU),r=(0,m.C)(T.NH),s=(0,o.useRef)(null),d=(0,o.useState)(null),c=(0,l.Z)(d,2),u=c[0],p=c[1],x=(0,v.$G)(),f=x.t;x.i18n;return(0,o.useEffect)((function(){var n=JSON.parse((null===t||void 0===t?void 0:t.expand)||"[]").map((function(e){return!(0,N.un)(e)||null!==e&&void 0!==e&&e.arrowHeadType?((0,N.UG)(e)&&!e.data.label&&(e.data.label=e.data.name),e):Object.assign(e,{arrowHeadType:"arrow"})}));e((0,Z.OV)(n))}),[t]),(0,y.jsx)(je,{shrink:!0,grow:1,className:be.itemStyles,children:(0,y.jsxs)("div",{className:be.flowWrapper,ref:s,children:[(0,y.jsxs)(N.ZP,{elements:n,nodeTypes:ye,snapToGrid:!0,snapGrid:[16,16],defaultZoom:1,onLoad:function(e){p(e)},onDrop:function(o){o.preventDefault();var t=s.current.getBoundingClientRect(),l=JSON.parse(o.dataTransfer.getData("application/reactflow")),a=u.project({x:o.clientX-t.left,y:o.clientY-t.top}),r=l.args,d={};if(Object.keys(r).reduce((function(e,n){var i={};return Object.keys(r[n]).reduce((function(e,i){var o=r[n][i].default;return Object.assign(e,(0,w.Z)({},i,o)),e}),i),Object.assign(e,i),e}),d),i){e((0,T.Xt)(!0));var c="".concat(l.name.replace(/\.|[\u4e00-\u9fa5]/g,"").replace(/_|\s/g,"-")||"task","-").concat(Date.now()).substring(0,49);_.Z.task_modelview_add(i,{job_template:l.id,pipeline:+i,name:c,label:"".concat(f("\u65b0\u5efa")," ").concat(l.name," ").concat(f("\u4efb\u52a1")),volume_mount:"kubeflow-user-workspace(pvc):/mnt",image_pull_policy:"Always",working_dir:"",command:"",overwrite_entrypoint:0,node_selector:"cpu=true,train=true",resource_memory:"2G",resource_cpu:"2",resource_gpu:"0",resource_rdma:"0",timeout:0,retry:0,args:JSON.stringify(d)}).then((function(i){var o;if(null!==i&&void 0!==i&&null!==(o=i.result)&&void 0!==o&&o.id){var t={id:"".concat(i.result.id),type:"dataSet",position:a,data:{info:l,name:c,label:"".concat(f("\u65b0\u5efa")," ").concat(l.name," ").concat(f("\u4efb\u52a1"))}};e((0,j.u6)(!0)),e((0,Z.OV)(n.concat(t))),setTimeout((function(){e((0,j.oL)())}),2e3)}})).catch((function(n){n.response&&e((0,L.L$)({msg:n.response.data.message}))})).finally((function(){e((0,T.Xt)(!1))}))}},onDragOver:function(e){e.preventDefault(),e.dataTransfer.dropEffect="move"},onElementClick:function(n,i){a&&i.data&&e((0,D.ZN)())},onElementsRemove:function(i){i.forEach((function(n){null!==n&&void 0!==n&&n.id&&(0,N.UG)(n)&&_.Z.task_modelview_del(+n.id).then((function(){setTimeout((function(){e((0,j.oL)())}),2e3)})).catch((function(n){n.response&&e((0,L.L$)({msg:n.response.data.message}))}))})),e((0,j.u6)(!0)),e((0,Z.OV)((0,N.d0)(i,n)))},onConnect:function(i){e((0,j.u6)(!0)),e((0,Z.OV)((0,N.Z_)(Object.assign(i,{arrowHeadType:"arrow"}),n)))},onNodeDragStop:function(i,o){e((0,Z.OV)(n.map((function(e){return(null===e||void 0===e?void 0:e.id)===(null===o||void 0===o?void 0:o.id)?o:e})))),e((0,j.u6)(!0))},onSelectionChange:function(n){n&&e((0,Z.pM)(n))},children:[(0,y.jsx)(P,{}),(0,y.jsx)(N.a9,{nodeStrokeColor:"#8a8886",nodeColor:"#c8c8c8"}),(0,y.jsx)(N.ZX,{}),(0,y.jsx)(C.Z,{id:"hostId_model",className:"layer-host"})]}),(0,y.jsx)("div",{className:be.spinnerWrapper,style:{visibility:r?"visible":"hidden"},children:(0,y.jsx)(k.$,{size:S.E.large,label:"Loading"})})]})})},Ce=i(74165),ke=i(15861),Se=i(93657),Ne=function(){var e=(0,m.T)(),n=(0,m.C)(L.V0);return(0,o.useEffect)((function(){n&&setTimeout((function(){e((0,L.L$)(null))}),3e3)}),[n]),n&&(0,y.jsx)(s.c,{messageBarType:d.f.error,styles:{root:{minHeight:32,width:"80%",margin:"0 auto"}},dismissButtonAriaLabel:"Close",onDismiss:function(){e((0,L.L$)(null))},isMultiline:!1,children:(null===n||void 0===n?void 0:n.msg)||""})},_e={headStyle:{root:{backgroundColor:"#fff",padding:"10px 16px",borderBottom:"1px solid #dadada"}},headNameStyle:(0,h.y0)({boxSizing:"border-box",marginLeft:"8px",padding:"0 8px",height:"32px",lineHeight:"32px",cursor:"text",marginBottom:"4px",fontSize:"16px",fontWeight:600,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:"black"}),buttonItemStyle:{root:{marginRight:"16px"}},hidden:(0,h.y0)({visibility:"hidden"})},Le=t.K.Item,Ze=function(){var e=(0,m.T)(),n=(0,m.C)(j.Bn),i=(0,m.C)(j.H6),o=(0,m.C)(L.zr),l=(0,v.$G)(),r=l.t,s=(l.i18n,function(){var i=(0,ke.Z)((0,Ce.Z)().mark((function i(){return(0,Ce.Z)().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(!n){i.next=9;break}return i.t0=e,i.next=4,(0,T.C)();case 4:return i.t1=i.sent,i.next=7,(0,i.t0)(i.t1);case 7:e((0,j.oL)()),window.open("".concat(window.location.origin,"/pipeline_modelview/api/run_pipeline/").concat(n));case 9:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}()),d=function(){var e=(0,ke.Z)((0,Ce.Z)().mark((function e(){return(0,Ce.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n&&window.open("".concat(window.location.origin,"/pipeline_modelview/api/copy_pipeline/").concat(n));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,y.jsx)(Le,{className:"editor-head",children:(0,y.jsxs)(t.K,{horizontal:!0,horizontalAlign:"space-between",verticalAlign:"center",styles:_e.headStyle,children:[(0,y.jsx)(Le,{grow:1,children:(0,y.jsxs)(t.K,{horizontal:!0,children:[(0,y.jsx)(Le,{children:i.name?(0,y.jsx)("div",{className:_e.headNameStyle,children:i.describe}):(0,y.jsx)(c.K,{styles:{root:{padding:"17px 16px"}},iconProps:{iconName:"Add",styles:{root:{fontSize:10}}},onClick:function(){_.Z.pipeline_modelview_add({describe:"".concat(r("\u65b0\u5efa\u6d41\u6c34\u7ebf"),"-").concat(Date.now()),name:"".concat(o,"-pipeline-").concat(Date.now()),node_selector:"cpu=true,train=true",schedule_type:"once",image_pull_policy:"Always",parallelism:1,project:7}).then((function(e){var n;0===(null===e||void 0===e?void 0:e.status)&&"success"===(null===e||void 0===e?void 0:e.message)&&(window.location.search="?pipeline_id=".concat(null===e||void 0===e||null===(n=e.result)||void 0===n?void 0:n.id))})).catch((function(n){n.response&&e((0,L.L$)({msg:n.response.data.message}))}))},children:r("\u65b0\u5efa\u6d41\u6c34\u7ebf")})}),(0,y.jsx)(Le,{className:i.name?"":_e.hidden,children:(0,y.jsx)(Se.G,{content:r("\u8bbe\u7f6e"),children:(0,y.jsx)(a.h,{iconProps:{iconName:"Settings"},onClick:function(){e((0,D.ZN)())}})})}),(0,y.jsx)(Le,{grow:1,align:"center",children:(0,y.jsx)(Ne,{})})]})}),(0,y.jsx)(Le,{className:i.name?"":_e.hidden,children:(0,y.jsxs)(t.K,{horizontal:!0,children:[(0,y.jsx)(Le,{styles:_e.buttonItemStyle,children:(0,y.jsx)(c.K,{onClick:s,children:r("\u8fd0\u884c")})}),(0,y.jsx)(Le,{styles:_e.buttonItemStyle,children:(0,y.jsx)(c.K,{onClick:d,children:r("\u590d\u5236")})})]})})]})})},De=i(51244),Te={editorToolStyle:{root:{display:"flex",flexDirection:"row",backgroundColor:"#fff",padding:"2px 0px",borderBottom:"1px solid #dadada","#authoring-page-toolbar":{flex:1}}},autoSavedTips:(0,h.y0)({paddingRight:20,lineHeight:"1"}),commandBarStyle:{root:{height:"40px",padding:"0px 14px 0px 8px",borderBottom:"none",backgroundColor:"#fff"}},commonButton:{root:{width:"40px",height:"40px",borderRadius:"2px",minWidth:"40px"}},commonIcon:{root:{color:"#015cda",fontSize:18}},toggleStyle:{root:{display:"flex",alignItems:"center",padding:"8px 0px 8px 8px",height:"100%",boxSizing:"border-box",borderLeft:"1px solid rgb(234, 234, 234)",marginLeft:"8px"},container:{display:"inline-flex"}},comboBoxStyle:{container:{borderLeft:"1px solid #eaeaea",marginLeft:"4px",padding:"0px 5px 0px 10px",width:"auto"},root:{backgroundColor:"#ffffff",padding:"0px 20px 0px 4px",selectors:{"&.is-open::after":{borderBottom:"2px solid #015cda"},"&::after":{border:"none",borderBottom:0,borderRadius:"none"},".ms-Button":{width:20},".ms-Icon":{fontSize:8}}},input:{width:35,backgroundColor:"#ffffff"}}},Ie=t.K.Item,Oe=function(){var e=(0,m.T)(),n=(0,m.C)(Z.hr),i=(0,m.C)(j.N1),l=(0,m.C)(j.H6),a=(0,m.C)(j.Bn),s=(0,m.C)(T.BX),d=(0,m.C)(j.B5),c=(0,m.C)(Z.tT),u=(0,v.$G)(),p=u.t,x=(u.i18n,[{key:"expand",iconProps:{iconName:"Library",styles:Te.commonIcon},text:p("\u5c55\u5f00/\u5173\u95ed\u83dc\u5355"),onClick:function(n){null===n||void 0===n||n.preventDefault(),e((0,De.ZN)())}},{key:"save",iconProps:{iconName:"save",styles:Te.commonIcon},text:p("\u4fdd\u5b58"),onClick:function(){var n=(0,ke.Z)((0,Ce.Z)().mark((function n(i){return(0,Ce.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return null===i||void 0===i||i.preventDefault(),n.t0=e,n.next=4,(0,T.C)();case 4:n.t1=n.sent,(0,n.t0)(n.t1),e((0,j.oL)());case 7:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()},{key:"example",iconProps:{iconName:"FastForward",styles:Te.commonIcon},text:p("\u8c03\u5ea6\u5b9e\u4f8b"),onClick:function(){a&&window.open("".concat(window.location.origin,"/pipeline_modelview/api/web/workflow/").concat(a))}},{key:"log",iconProps:{iconName:"ComplianceAudit",styles:Te.commonIcon},text:p("\u65e5\u5fd7"),onClick:function(){null!==l&&void 0!==l&&l.id&&window.open("".concat(window.location.origin,"/pipeline_modelview/api/web/log/").concat(a))}},{key:"docker",iconProps:{iconName:"WebAppBuilderFragment",styles:Te.commonIcon},text:p("\u5bb9\u5668"),onClick:function(){null!==l&&void 0!==l&&l.name&&window.open("".concat(window.location.origin,"/pipeline_modelview/api/web/pod/").concat(a))}},{key:"timer",iconProps:{iconName:"TimeEntry",styles:Te.commonIcon},text:p("\u5b9a\u65f6\u8bb0\u5f55"),onClick:function(){null!==l&&void 0!==l&&l.name&&window.open("".concat(window.location.origin,"/pipeline_modelview/api/web/runhistory/").concat(a))}},{key:"delete",iconProps:{iconName:"Delete",styles:Te.commonIcon},text:p("\u5220\u9664\u8282\u70b9"),onClick:function(){if((0,N.UG)(c[0])){var i,o=+(null===(i=c[0])||void 0===i?void 0:i.id);_.Z.task_modelview_del(o).then((function(){setTimeout((function(){e((0,j.oL)())}),2e3)})).catch((function(n){n.response&&e((0,L.L$)({msg:n.response.data.message}))}))}e((0,j.u6)(!0)),e((0,Z.OV)((0,N.d0)(c,n)))}},{key:"monitor",iconProps:{iconName:"NetworkTower",styles:Te.commonIcon},text:p("\u76d1\u63a7"),onClick:function(){null!==l&&void 0!==l&&l.id&&window.open("".concat(window.location.origin,"/pipeline_modelview/api/web/monitoring/").concat(a))}}]);return(0,o.useEffect)((function(){s&&s.forEach((function(n){Object.keys(n).length>0&&e((0,j.u6)(!0))}))}),[s]),(0,y.jsxs)(Ie,{shrink:!0,styles:Te.editorToolStyle,children:[(0,y.jsx)(K.X,{id:"authoring-page-toolbar",styles:Te.commandBarStyle,items:x}),(0,y.jsx)(t.K,{horizontal:!0,verticalAlign:"center",className:Te.autoSavedTips,style:{visibility:a?"visible":"hidden"},children:i?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(r.J,{iconName:d?"AlertSolid":"SkypeCircleCheck",styles:{root:{color:d?"#e95f39":"#8cb93c",marginRight:5}}}),p(d?"\u672a\u4fdd\u5b58":"\u5df2\u4fdd\u5b58")]}):(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(k.$,{styles:{root:{marginRight:5}},size:S.E.small}),p("\u4fdd\u5b58\u4e2d")]})})]})},Be=t.K.Item,ze=function(){var e=(0,m.T)();return(0,o.useEffect)((function(){e((0,j.e4)())}),[]),(0,y.jsx)(Be,{className:"flow-editor",styles:{root:{flexGrow:1,flexShrink:1,background:"#f4f4f4",overflow:"hidden",display:"flex",flexDirection:"column"}},children:(0,y.jsxs)(t.K,{grow:1,children:[(0,y.jsx)(Ze,{}),(0,y.jsx)(Oe,{}),(0,y.jsx)(we,{})]})})},He=i(54039),We=i(87023),Ee=i(4026),Me=i(65260),Re={calloutContent:(0,h.y0)({display:"flex",flexFlow:"column nowrap",width:"360px",height:"auto",boxSizing:"border-box",maxHeight:"inherit",padding:"16px 0px",fontSize:"12px",background:"#fff",borderRadius:"2px"}),moduleDetailItemStyle:(0,h.y0)({fontSize:"12px",padding:"0 16px",overflowX:"visible",overflowY:"hidden"}),moduleDetailTitle:(0,h.y0)({padding:"0px 16px",fontWeight:600,fontSize:"14px",lineHeight:"1",marginTop:"0px",marginBottom:"8px"}),moduleDetailLabel:(0,h.y0)({fontSize:"12px",fontWeight:"bold",lineHeight:"16px",color:"rgb(89, 89, 89)",padding:"0px",margin:"0px 0px 4px"}),moduleDetailBody:(0,h.y0)({lineHeight:"14px",marginBottom:"22px",p:{margin:"0",padding:"0"}}),moduleButton:(0,h.y0)({display:"flex",justifyContent:"center",alignItems:"center"})},Pe=t.K.Item,Fe=function(){var e,n=(0,m.T)(),i=(0,m.C)(De.p),o=(0,m.C)(De.b2),l=(0,m.C)(De.H6),a=(0,v.$G)(),r=a.t,s=(a.i18n,function(e){n((0,De.DH)("mouseenter"!==e.type))});return(0,y.jsx)(We.U,{gapSpace:-10,hidden:i,hideOverflow:!0,calloutMaxHeight:480,isBeakVisible:!1,preventDismissOnLostFocus:!0,target:{current:o},directionalHint:12,children:(0,y.jsxs)(t.K,{className:Re.calloutContent,onMouseEnter:s,onMouseLeave:s,children:[(0,y.jsx)(Pe,{children:(0,y.jsx)("h3",{className:Re.moduleDetailTitle,children:l.name})}),(0,y.jsx)(Pe,{grow:1,shrink:1,className:Re.moduleDetailItemStyle,children:(0,y.jsxs)("div",{children:[(0,y.jsx)(X._,{title:"Description",className:Re.moduleDetailLabel,children:r("\u63cf\u8ff0")}),(0,y.jsx)("div",{className:Re.moduleDetailBody,children:(0,y.jsx)("p",{children:l.describe})})]})}),(0,y.jsx)(Pe,{grow:1,shrink:1,className:Re.moduleDetailItemStyle,children:(0,y.jsxs)("div",{children:[(0,y.jsx)(X._,{title:"Description",className:Re.moduleDetailLabel,children:r("\u521b\u5efa\u4eba")}),(0,y.jsx)("div",{className:Re.moduleDetailBody,children:(0,y.jsx)("p",{children:l.createdBy})})]})}),(0,y.jsx)(Pe,{grow:1,shrink:1,className:Re.moduleDetailItemStyle,children:(0,y.jsxs)("div",{children:[(0,y.jsx)(X._,{title:"Description",className:Re.moduleDetailLabel,children:r("\u955c\u50cf")}),(0,y.jsx)("div",{className:Re.moduleDetailBody,children:(0,y.jsx)("p",{children:l.imagesName})})]})}),(0,y.jsx)(Pe,{grow:1,shrink:1,className:Re.moduleDetailItemStyle,children:(0,y.jsxs)("div",{children:[(0,y.jsx)(X._,{title:"Description",className:Re.moduleDetailLabel,children:r("\u4e0a\u6b21\u4fee\u6539\u65f6\u95f4")}),(0,y.jsx)("div",{className:Re.moduleDetailBody,children:(0,y.jsx)("p",{children:l.lastChanged})})]})}),(0,y.jsx)(Pe,{grow:1,shrink:1,className:Re.moduleDetailItemStyle,children:(0,y.jsxs)("div",{children:[(0,y.jsx)(X._,{title:"Description",className:Re.moduleDetailLabel,children:r("\u7248\u672c")}),(0,y.jsx)("div",{className:Re.moduleDetailBody,children:(0,y.jsx)("p",{children:l.version})})]})}),null!==l&&void 0!==l&&null!==(e=l.expand)&&void 0!==e&&e.help_url?(0,y.jsx)(Pe,{grow:1,shrink:1,className:Re.moduleButton,children:(0,y.jsx)(c.K,{onClick:function(){window.open(l.expand.help_url)},children:r("\u914d\u7f6e\u6587\u6863")})}):null]})})},Ge={moduleItem:(0,h.y0)({width:"100%",userSelect:"none",cursor:"pointer"}),moduleListModuleCard:(0,h.y0)({margin:"8px 16px",border:"1px solid rgb(234, 234, 234)",background:"rgb(248, 248, 248)",color:"rgb(55, 55, 55)",borderRadius:"2px",padding:"8px",display:"flex",fontSize:"12px",flexDirection:"column","&:hover":{backgroundColor:"#eee"},"> div":{pointerEvents:"none",display:"flex",flexDirection:"row",flexGrow:1,alignItems:"center","> header":{flexGrow:1,color:"rgb(0, 0, 0)",fontWeight:600,fontSize:"12px",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",lineHeight:"16px"},"> summary":{flexGrow:1,color:"rgb(55, 55, 55)",fontSize:"12px"}}})},Ke=function(e){var n=(0,m.T)(),i=(0,o.useState)(e.model),t=(0,l.Z)(i,2),a=t[0],s=t[1],d=function(e){"mouseenter"===e.type&&n((0,De.VF)(e.target)),n((0,De.DH)("mouseenter"!==e.type)),n((0,De.rn)(a))};return(0,o.useEffect)((function(){s(e.model)}),[e.model]),(0,y.jsx)("div",{className:Ge.moduleItem,children:(0,y.jsx)("div",{className:"module-card-with-hover-wrap",children:(0,y.jsx)("div",{className:"module-card-content-wrap",children:(0,y.jsxs)("div",{className:Ge.moduleListModuleCard,onMouseEnter:d,onMouseLeave:d,onMouseDown:function(){n((0,De.DH)(!0))},onDragStart:function(e){e.dataTransfer.setData("application/reactflow",JSON.stringify(a)),e.dataTransfer.effectAllowed="move"},draggable:!0,children:[(0,y.jsxs)("div",{children:[(0,y.jsx)(r.J,{iconName:"Database",styles:{root:{marginRight:"4px",lineHeight:"16px"}}}),(0,y.jsx)("header",{children:a.name})]}),(0,y.jsx)("div",{children:(0,y.jsx)("summary",{children:(0,y.jsx)("span",{children:a.describe})})})]})})})})},Ae={moduleItem:(0,h.y0)({width:"100%",userSelect:"none",cursor:"pointer"}),moduleListModuleCard:(0,h.y0)({margin:"4px 8px",border:"1px solid rgb(234, 234, 234)",background:"rgb(248, 248, 248)",color:"rgb(55, 55, 55)",borderRadius:"2px",padding:"5px 8px",display:"flex",fontSize:"12px",flexDirection:"column","&:hover":{backgroundColor:"#eee"},"> div":{pointerEvents:"none",display:"flex",flexDirection:"row",flexGrow:1,alignItems:"center","> header":{flexGrow:1,color:"rgb(0, 0, 0)",fontWeight:600,fontSize:"12px",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",lineHeight:"16px"},"> summary":{flexGrow:1,color:"rgb(55, 55, 55)",fontSize:"12px"}}})},Je=function(e){var n=(0,o.useState)(e.model),i=(0,l.Z)(n,2),t=i[0],a=i[1];return(0,o.useEffect)((function(){a(e.model)}),[e.model]),(0,y.jsx)("div",{className:Ae.moduleItem,children:(0,y.jsx)("div",{className:"module-card-with-hover-wrap",children:(0,y.jsx)("div",{className:"module-card-content-wrap",children:(0,y.jsxs)("div",{className:Ae.moduleListModuleCard,onClick:e.onClick,children:[(0,y.jsxs)("div",{children:[(0,y.jsx)(r.J,{iconName:"Database",styles:{root:{marginRight:"4px",lineHeight:"16px"}}}),(0,y.jsx)("header",{children:t.name})]}),(0,y.jsx)("div",{children:(0,y.jsx)("summary",{children:(0,y.jsx)("span",{children:t.describe})})})]})})})})},$e={"::-webkit-scrollbar":{width:4},"::-webkit-scrollbar-thumb":{minHeight:"15px",border:"6px solid transparent",backgroundClip:"padding-box",backgroundColor:"rgb(200, 200, 200)"}},Ue={showModuleTree:(0,h.y0)({width:"320px",height:"100%",transition:"all 0.35s ease 0s",overflowX:"hidden",visibility:"visible"}),hideModuleTree:(0,h.y0)({width:"0px",height:"100%",transition:"width 0.35s ease 0s",overflowX:"hidden",visibility:"hidden"}),treeContainer:(0,h.y0)({width:"320px",boxSizing:"border-box",height:"100%",display:"flex",flexDirection:"column",borderRight:"1px solid rgb(234, 234, 234)",backgroundColor:"#fff"}),searchBoxStyle:(0,h.y0)({margin:"12px 16px",padding:"1px 0px",color:"rgb(1, 92, 218)",lineHeight:"32px",minHeight:"32px",flexGrow:"1"}),searchCallout:(0,h.y0)({".ms-Callout-main":(0,I.Z)({overflowY:"overlay",willChange:"transform"},$e)}),searchListStyle:(0,h.y0)({width:"287px",padding:"8px 0"}),summaryStyle:(0,h.y0)({display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderTop:"1px solid rgb(218, 218, 218)"}),moduleTreeStyle:(0,h.y0)({position:"relative",display:"flex",flexDirection:"column",overflowX:"hidden",overflowY:"auto",flexGrow:"1"}),moduleTreeBody:(0,h.y0)({position:"relative",height:"100%"}),listIconStyle:(0,h.y0)({display:"inline-flex",fontSize:"12px",lineHeight:"12px",marginRight:"4px",height:"6px",color:"rgb(55, 55, 55)",userSelect:"none"}),spinnerContainer:(0,h.y0)({width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center"}),moduleListStyle:(0,h.y0)((0,I.Z)({padding:0,margin:"0 auto",overflowY:"overlay",height:"100%",willChange:"transform"},$e)),moduleListItem:(0,h.y0)({listStyle:"none",outline:"none"}),itemFolderNode:(0,h.y0)({display:"flex",alignItems:"center",cursor:"pointer",padding:"7px 15px",border:"1px solid transparent",fontFamily:"Segoe UI,sans-serif",fontSize:12,lineHeight:16,fontWeight:600,color:"black","&:hover":{backgroundColor:"#eaeaea"}})},Xe=t.K.Item,Ve=[{color:"rgba(0,120,212,1)",bg:"rgba(0,120,212,0.02)"},{color:"rgba(0,170,200,1)",bg:"rgba(0,170,200,0.02)"},{color:"rgba(0,200,153,1)",bg:"rgba(0,200,153,0.02)"},{color:"rgba(0,6,200,1)",bg:"rgba(0,6,200,0.02)"},{color:"rgba(212,65,0,1)",bg:"rgba(212,65,0,0.02)"},{color:"rgba(212,176,0,1)",bg:"rgba(212,176,0,0.02)"}],qe=function(){var e=(0,m.T)(),n=(0,m.C)(De.RU),i=(0,o.useState)(new Set),s=(0,l.Z)(i,2),d=s[0],c=s[1],u=(0,o.useState)(new Map),p=(0,l.Z)(u,2),x=p[0],h=p[1],g=(0,o.useState)(0),b=(0,l.Z)(g,2),j=b[0],w=b[1],C=(0,o.useState)(!1),N=(0,l.Z)(C,2),Z=N[0],D=N[1],T=(0,o.useState)(!1),I=(0,l.Z)(T,2),O=I[0],B=I[1],z=(0,o.useState)(new Map),H=(0,l.Z)(z,2),W=H[0],E=H[1],M=(0,v.$G)(),R=M.t,P=(M.i18n,function(e){var n=new Map,i=0;e.forEach((function(e){if("Release"===e.version){i+=1;var o={id:e.id,args:e.args?JSON.parse(e.args):{},name:e.name,version:e.version,describe:e.describe,imagesName:e.images.name,createdBy:e.created_by.username,lastChanged:e.changed_on,expand:JSON.parse(e.expand),color:Ve[e.project.id%Ve.length]};n.has(e.project.id)?n.get(e.project.id).children.push(o):n.set(e.project.id,{id:e.project.id,title:e.project.name,children:[o]})}})),w(i),h(n)}),F=function(){D(!0),w(0),_.Z.job_template_modelview().then((function(e){if(0===(null===e||void 0===e?void 0:e.status)&&"success"===(null===e||void 0===e?void 0:e.message)){P(null===e||void 0===e?void 0:e.result.data);var n=Date.now();ve.set("job_template",{update:n,value:null===e||void 0===e?void 0:e.result.data,expire:864e5})}})).catch((function(n){var i,o;n.response&&e((0,L.L$)({msg:null===(i=n.response)||void 0===i||null===(o=i.data)||void 0===o?void 0:o.message}))})).finally((function(){D(!1)}))};return(0,o.useEffect)((function(){var e=ve.get("job_template");e&&Date.now()-(null===e||void 0===e?void 0:e.update)<(null===e||void 0===e?void 0:e.expire)?P(e.value):F()}),[]),(0,y.jsxs)(Xe,{shrink:!0,children:[(0,y.jsx)("div",{className:n?Ue.showModuleTree:Ue.hideModuleTree,children:(0,y.jsxs)("div",{className:Ue.treeContainer,children:[(0,y.jsxs)(t.K,{horizontal:!0,horizontalAlign:"space-between",children:[(0,y.jsx)(He.R,{placeholder:R("\u641c\u7d22\u6a21\u677f\u540d\u79f0\u6216\u63cf\u8ff0"),role:"search",className:Ue.searchBoxStyle,onChange:f((function(e,n){if(n){console.log(n);var i=new Map;x.forEach((function(e,o){var t=e.children;t.length>0&&t.forEach((function(e){var t,l;((null===e||void 0===e||null===(t=e.name)||void 0===t?void 0:t.indexOf(n))>-1||(null===e||void 0===e||null===(l=e.describe)||void 0===l?void 0:l.indexOf(n))>-1)&&(i.has(o)?i.set(o,i.get(o).concat(e)):i.set(o,[e]))}))})),E(i),B(!0)}else B(!1)}),300),onBlur:function(){setTimeout((function(){B(!1)}),300)}}),(0,y.jsx)(We.U,{className:Ue.searchCallout,isBeakVisible:!1,preventDismissOnLostFocus:!0,hidden:!O,calloutMaxHeight:300,target:".ms-SearchBox",children:(0,y.jsxs)(t.K,{className:Ue.searchListStyle,children:[0===Array.from(W.keys()).length?(0,y.jsx)("div",{style:{textAlign:"center"},children:R("\u6682\u65e0\u5339\u914d")}):"",Array.from(W.keys()).map((function(e){var n=W.get(e);return(0,y.jsx)(o.Fragment,{children:null===n||void 0===n?void 0:n.map((function(n){return(0,y.jsx)(Je,{model:n,onClick:function(){console.log(d),d.has(e)||(d.add(e),c(new Set(d)))}},n.id)}))},e)}))]})})]}),(0,y.jsxs)("div",{className:Ue.summaryStyle,children:[(0,y.jsxs)(Ee.x,{children:[j," assets in total"]}),(0,y.jsxs)(Me.k,{children:[(0,y.jsx)(a.h,{iconProps:{iconName:"Refresh"},onClick:function(){Z||F()}}),(0,y.jsx)(a.h,{iconProps:{iconName:"Add"},onClick:function(){window.open("/frontend/train/train_template/job_template?isVisableAdd=true")}})]})]}),(0,y.jsx)("div",{className:Ue.moduleTreeStyle,children:(0,y.jsx)("div",{className:Ue.moduleTreeBody,children:Z?(0,y.jsx)(t.K,{className:Ue.spinnerContainer,children:(0,y.jsx)(k.$,{size:S.E.large,label:"Loading"})}):(0,y.jsx)("ul",{className:Ue.moduleListStyle,children:Array.from(x.keys()).map((function(e){var n,i=x.get(e);return(0,y.jsxs)("li",{className:Ue.moduleListItem,children:[(0,y.jsx)("div",{role:"button",onClick:function(){d.has(e)?d.delete(e):d.add(e),c(new Set(d))},children:(0,y.jsxs)("div",{className:Ue.itemFolderNode,children:[(0,y.jsx)(r.J,{iconName:d.has(e)?"FlickUp":"FlickLeft",styles:{root:{alignItems:d.has(e)?"baseline":"center"}},className:Ue.listIconStyle}),i.title]})}),d.has(e)?(0,y.jsx)("ul",{role:"group",style:{paddingLeft:"0px"},children:null===(n=i.children)||void 0===n?void 0:n.map((function(e){return(0,y.jsx)("li",{className:Ue.moduleListItem,children:(0,y.jsx)("div",{role:"button",children:(0,y.jsx)(Ke,{model:e})})},e.id)}))}):null]},e)}))})})})]})}),(0,y.jsx)(Fe,{})]})},Ye={root:{width:"100%",height:"100%",overflow:"hidden"}},Qe=function(){return(0,y.jsxs)(t.K,{className:"app-container",horizontal:!0,styles:Ye,children:[(0,y.jsx)(qe,{}),(0,y.jsx)(ze,{}),(0,y.jsx)(b,{})]})}}}]);