{"kind": "Policy", "apiVersion": "v1", "priorities": [{"name": "SelectorSpreadPriority", "weight": 1}, {"name": "InterPodAffinityPriority", "weight": 1}, {"name": "RequestedToCapacityRatioPriority", "weight": 1, "argument": {"requestedToCapacityRatioArguments": {"shape": [{"utilization": 0, "score": 10}, {"utilization": 100, "score": 0}], "resources": [{"name": "cpu", "weight": 1}, {"name": "memory", "weight": 1}]}}}, {"name": "BalancedResourceAllocation", "weight": 1}, {"name": "NodePreferAvoidPodsPriority", "weight": 10000}, {"name": "NodeAffinityPriority", "weight": 1}, {"name": "TaintTolerationPriority", "weight": 1}, {"name": "ImageLocalityPriority", "weight": 1}], "alwaysCheckAllPredicates": false}