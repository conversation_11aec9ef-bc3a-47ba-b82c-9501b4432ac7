import pandas as pd
import numpy as np
from sklearn.utils import Bunch
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
import argparse

def load_and_sample_data(
    path: str,
    output_path: str,
    columns: list = None,
    strategy: str = 'random',
    sample_fraction: float = 0.5,
    fixed_n_per_class: int = 10,
    random_state: int = 42,
    na_values: list = None
) -> Bunch:
    """
    加载数据并根据采样策略进行采样

    参数：
    - path: 原始数据路径
    - output_path: 采样后保存路径
    - columns: 列名列表（最后一列为 target）
    - strategy: 采样策略 ['random', 'stratified', 'fixed']
    - sample_fraction: 当 strategy 为 'random' 或 'stratified' 时使用的采样比例
    - fixed_n_per_class: 当 strategy 为 'fixed' 时，每类采样数量
    - random_state: 随机种子
    - na_values: 缺失值标识符
    """
    if columns is None:
        columns = ['sepal length (cm)', 'sepal width (cm)',
                   'petal length (cm)', 'petal width (cm)', 'target']
    if na_values is None:
        na_values = ["?", ""]

    # 加载数据
    df = pd.read_csv(path, header=None, names=columns, na_values=na_values)
    feature_cols = columns[:-1]
    label_col = columns[-1]

    # 编码 target
    le = LabelEncoder()
    df['target_encoded'] = le.fit_transform(df[label_col])

    # 采样策略
    if strategy == 'random':
        df_sampled = df.sample(frac=sample_fraction, random_state=random_state)

    elif strategy == 'stratified':
        # 分层采样
        df_sampled, _ = train_test_split(
            df,
            stratify=df['target_encoded'],
            test_size=1 - sample_fraction,
            random_state=random_state
        )

    elif strategy == 'fixed':
        # 每个类别采样固定数量
        df_sampled = df.groupby('target_encoded', group_keys=False).apply(
            lambda x: x.sample(n=min(fixed_n_per_class, len(x)), random_state=random_state)
        )

    else:
        raise ValueError(f"未知采样策略: {strategy}。支持 'random', 'stratified', 'fixed'")

    # 重置索引
    df_sampled = df_sampled.reset_index(drop=True)

    # 保存
    df_sampled[columns].to_csv(output_path, index=False, header=False)
    print(f"[INFO] 采样策略：{strategy} -> 数据保存至: {output_path}")

    return Bunch(
        data=df_sampled[feature_cols].values,
        target=df_sampled['target_encoded'].values,
        target_names=le.classes_,
        feature_names=feature_cols,
        DESCR=f"Sampled using '{strategy}' strategy",
        filename=path,
        frame=df_sampled
    )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="数据清洗")
    parser.add_argument('--input_path', type=str, required=True, help="输入数据路径")
    parser.add_argument('--output_path', type=str, required=True, help="输出数据文件")
    parser.add_argument('--columns', type=str, required=True, help="列头")
    parser.add_argument('--strategy', type=str, required=True, help="填充策略 random \ fixed ")
    parser.add_argument('--sample_fraction', type=float, required=False, help="sample_fraction")
    parser.add_argument('--fixed_n_per_class', type=int, required=False, help="round_digits")
    args = parser.parse_args()

    # ✅ 示例使用
    # 采样策略	         描述
    # 'random'	        简单随机采样（不分层）
    # 'stratified'	    分层随机采样（保持类别分布）
    # 'fixed'	        每类固定数量采样（例如每类取 10 条）
    iris_sampled = load_and_sample_data(
        path = args.input_path,
        output_path = args.output_path,
        columns = str.split(args.columns, ","),
        strategy = args.strategy,  # 可改为 'random' 或 'fixed'  
        sample_fraction=args.sample_fraction,     # 仅在 'random' 或 'stratified' 有效
        fixed_n_per_class=args.fixed_n_per_class     # 仅在 'fixed' 有效
    )
    print(iris_sampled.frame.head())