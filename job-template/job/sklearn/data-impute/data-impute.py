import pandas as pd
import numpy as np
from sklearn.impute import SimpleImputer, KNNImputer
from sklearn.utils import Bunch
from sklearn.preprocessing import LabelEncoder
import argparse

def load_and_impute_data(
    path: str,
    output_path: str,
    columns: list = None,
    strategy: str = 'mean',
    fill_value: float = 0.0,
    round_digits: int = 2,
    na_values: list = None,
    knn_k: int = 5
) -> Bunch:
    """
    加载带缺失值的数据，并进行填充，支持 KNN 和 SimpleImputer 的策略。

    参数：
    - strategy: 填充策略 ['mean', 'median', 'most_frequent', 'constant', 'knn']
    - knn_k: 使用 KNN 填充时的邻居数量
    """
    if columns is None:
        columns = ['sepal length (cm)', 'sepal width (cm)',
                   'petal length (cm)', 'petal width (cm)', 'target']
    if na_values is None:
        na_values = ["?", ""]

    df = pd.read_csv(path, header=None, names=columns, na_values=na_values)
    feature_cols = columns[:-1]
    label_col = columns[-1]

    # 分离特征和标签
    X = df[feature_cols]
    y = df[label_col]

    # 选择填充器
    if strategy == 'knn':
        imputer = KNNImputer(n_neighbors=knn_k)
    elif strategy in ['mean', 'median', 'most_frequent', 'constant']:
        imputer = SimpleImputer(strategy=strategy, fill_value=fill_value)
    else:
        raise ValueError(f"不支持的填充策略: {strategy}")

    # 拟合并填充
    X_imputed = imputer.fit_transform(X)

    # 仅对填充的值进行 round
    mask = X.isna().to_numpy()
    X_imputed[mask] = np.round(X_imputed[mask], round_digits)
    X_filled = pd.DataFrame(X_imputed, columns=feature_cols)

    # 标签编码
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)

    # 合并并保存
    df_filled = X_filled.copy()
    df_filled[label_col] = y
    df_filled['target_encoded'] = y_encoded
    df_filled[columns].to_csv(output_path, index=False, header=False)
    print(f"[INFO] 使用 '{strategy}' 策略填充完成，保存到: {output_path}")

    return Bunch(
        data=X_filled.values,
        target=y_encoded,
        target_names=le.classes_,
        feature_names=feature_cols,
        DESCR=f"Imputed with '{strategy}' strategy",
        filename=path,
        frame=df_filled
    )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="数据填充")
    parser.add_argument('--input_path', type=str, required=True, help="输入数据路径")
    parser.add_argument('--output_path', type=str, required=True, help="输出数据文件")
    parser.add_argument('--columns', type=str, required=True, help="列头")
    parser.add_argument('--strategy', type=str, required=True, help="填充策略 mean / median / constant / knn ")
    parser.add_argument('--knn_k', type=int, required=False, help="knn_k")
    parser.add_argument('--round_digits', type=int, required=False, help="round_digits")

    args = parser.parse_args()

    # ✅ 示例调用：使用 KNN 策略
    # columns: ['sepal length (cm)', 'sepal width (cm)', 'petal length (cm)', 'petal width (cm)', 'target']
    iris_filled = load_and_impute_data(
        path = args.input_path,
        output_path = args.output_path,
        columns=str.split(args.columns, ","),
        strategy=args.strategy,
        knn_k=args.knn_k,
        round_digits=args.round_digits
    )
    print(iris_filled.frame.head())