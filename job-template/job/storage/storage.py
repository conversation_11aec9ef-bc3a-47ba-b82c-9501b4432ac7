import requests
import argparse
# from clearml import Task
from boto3.session import Session
import boto3
import re


# 整体流程：从上一个任务获取必要信息——algo_id  algo_version 更新的model path(在train中先将其写入某临时目录)  存入对象存储
# 当前问题：拿不到数据，文件目录未知
def get_trained_algo():
    
    pass

def gene_new_version():

    pass

# 入参：临时文件地址     
def storage_traning_result(
        
):
    
    pass

if __name__ == "__main__":
    # 可被统一环境变量替代，临时性写入
    parser = argparse.ArgumentParser(description="存储配置")
    parser.add_argument('--s3_url', type=str, required=True, help="对象存储地址")
    parser.add_argument('--s3_access_key', type=str, required=True, help="对象存储访问密钥")
    parser.add_argument('--s3_secret_key', type=str, required=True, help="对象存储密钥")
    parser.add_argument('--bucket_name', type=str, required=True, help="对象存储桶名称")
    parser.add_argument('--object_name', type=str, required=True, help="对象存储对象名称")