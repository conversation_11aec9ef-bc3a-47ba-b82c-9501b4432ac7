#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ if .Values.fuse.enabled -}}
{{- $shortCircuitEnabled := .Values.shortCircuit.enabled -}}
{{- $needDomainSocketVolume := and $shortCircuitEnabled (eq .Values.shortCircuit.policy "uuid") }}

apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ template "alluxio.fullname" . }}-fuse
  labels:
    app: {{ template "alluxio.name" . }}
    chart: {{ template "alluxio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-fuse
spec:
  selector:
    matchLabels:
      app: {{ template "alluxio.name" . }}
      chart: {{ template "alluxio.chart" . }}
      release: {{ .Release.Name }}
      heritage: {{ .Release.Service }}
      role: alluxio-fuse
  template:
    metadata:
      labels:
        app: {{ template "alluxio.name" . }}
        chart: {{ template "alluxio.chart" . }}
        release: {{ .Release.Name }}
        heritage: {{ .Release.Service }}
        role: alluxio-fuse
    spec:
      hostNetwork: {{ .Values.fuse.hostNetwork }}
      hostPID: {{ .Values.fuse.hostPID }}
      dnsPolicy: {{ .Values.fuse.dnsPolicy }}
      nodeSelector:
      {{- if .Values.fuse.nodeSelector }}
{{ toYaml .Values.fuse.nodeSelector | trim | indent 8  }}
      {{- else if .Values.nodeSelector }}
{{ toYaml .Values.nodeSelector | trim | indent 8  }}
      {{- end }}
      tolerations:
      {{- if .Values.fuse.tolerations }}
{{ toYaml .Values.fuse.tolerations | trim | indent 8  }}
      {{- end }}
      {{- if .Values.tolerations }}
{{ toYaml .Values.tolerations | trim | indent 8  }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.fuse.user }}
        runAsGroup: {{ .Values.fuse.group }}
        fsGroup: {{ .Values.fuse.fsGroup }}
    {{- if .Values.fuse.serviceAccount }}
      serviceAccountName: {{ .Values.fuse.serviceAccount }}
    {{- else if .Values.serviceAccount }}
      serviceAccountName: {{ .Values.serviceAccount }}
    {{- end }}
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - {{ template "alluxio.name" . }}
              - key: role
                operator: In
                values:
                - alluxio-worker
            topologyKey: kubernetes.io/hostname
      {{- if .Values.imagePullSecrets }}
{{ include "alluxio.imagePullSecrets" . | indent 6 }}
      {{- end}}
      containers:
        - name: alluxio-fuse
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          {{- if .Values.fuse.resources  }}
          resources:
            {{- if .Values.fuse.resources.limits }}
            limits:
              cpu: {{ .Values.fuse.resources.limits.cpu }}
              memory: {{ .Values.fuse.resources.limits.memory }}
            {{- end }}
            {{- if .Values.fuse.resources.requests }}
            requests:
              cpu: {{ .Values.fuse.resources.requests.cpu }}
              memory: {{ .Values.fuse.resources.requests.memory }}
            {{- end }}
          {{- end }}
          command: ["/entrypoint.sh"]
          args:
            - fuse
            {{- if .Values.fuse.mountOptions }}
            - --fuse-opts={{ .Values.fuse.mountOptions }}
            {{- end }}
            {{- if not .Values.fuse.mountPoint }}
            {{- fail "mountPoint must be set to launch Alluxio Fuse." }}
            {{- end }}
            - {{ .Values.fuse.mountPoint }}
            {{- if .Values.fuse.alluxioPath }}
            - {{ .Values.fuse.alluxioPath }}
            {{- end }}
          env:
          - name: ALLUXIO_CLIENT_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          {{- range $key, $value := .Values.fuse.env }}
          - name: "{{ $key }}"
            value: "{{ $value }}"
          {{- end }}
          securityContext:
            privileged: true
            capabilities:
              add:
                - SYS_ADMIN
          lifecycle:
            preStop:
              exec:
                command: ["/opt/alluxio/integration/fuse/bin/alluxio-fuse", "unmount", "{{ .Values.fuse.mountPoint }}"]
          envFrom:
          - configMapRef:
              name: {{ template "alluxio.fullname" . }}-config
          {{- if .Values.fuse.livenessProbeEnabled }}
          livenessProbe:
            exec:
              command:
              - /bin/bash
              - -c
              - cd {{ .Values.fuse.mountPoint }}
            initialDelaySeconds: {{ .Values.fuse.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.fuse.livenessProbe.periodSeconds }}
            failureThreshold: {{ .Values.fuse.livenessProbe.failureThreshold }}
          {{- end }}
          volumeMounts:
            - name: alluxio-fuse-device
              mountPath: /dev/fuse
            - name: alluxio-fuse-mount
              mountPath: {{ .Values.fuse.mountPoint | dir }}
              mountPropagation: Bidirectional
            {{- if $shortCircuitEnabled}}
              {{- if eq .Values.shortCircuit.policy "uuid" }}
            - name: alluxio-domain
              mountPath: /opt/domain
              {{- end }}
              {{- if eq .Values.shortCircuit.policy "local" }}
{{- include "alluxio.worker.tieredstoreVolumeMounts" . }}
              {{- end }}
            {{- end }}
      restartPolicy: Always
      volumes:
        - name: alluxio-fuse-device
          hostPath:
            path: /dev/fuse
            type: CharDevice
        - name: alluxio-fuse-mount
          hostPath:
            path: {{ .Values.fuse.mountPoint | dir }}
            type: DirectoryOrCreate
        {{- if $shortCircuitEnabled}}
          {{- if eq .Values.shortCircuit.policy "uuid" }}
{{- include "alluxio.worker.shortCircuit.volume" . }}
          {{- end }}
          {{- if eq .Values.shortCircuit.policy "local" }}
{{- include "alluxio.worker.tieredstoreVolumes" . }}
          {{- end }}
        {{- end }}
{{- end }}
