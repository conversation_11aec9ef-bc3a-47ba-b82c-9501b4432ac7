apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: kafka-cluster
spec:
  kafka:
    version: 3.3.1
    replicas: 3
    listeners:
      - name: plain
        port: 9092
        type: internal
        tls: false
      - name: tls
        port: 9093
        type: internal
        tls: true
      - name: external
        port: 9094
        type: nodeport
        tls: false
    config:
      offsets.topic.replication.factor: 2
      transaction.state.log.replication.factor: 2
      transaction.state.log.min.isr: 2
      default.replication.factor: 2
      min.insync.replicas: 2
      num.partitions: 3
      inter.broker.protocol.version: "3.3"
      log.retention.hours: 168
      log.segment.bytes: 1073741824
      log.retention.check.interval.ms: 300000
      num.network.threads: 3
      num.io.threads: 8
      socket.send.buffer.bytes: 102400
      socket.receive.buffer.bytes: 102400
      socket.request.max.bytes: 104857600
#      zookeeper.connection.timeout.ms: 6000
    storage:
      type: persistent-claim
      size: 500Gi
      deleteClaim: false
      class: local-kafka
      overrides:
        - broker: 0
          class: local-kafka
        - broker: 1
          class: local-kafka
        - broker: 2
          class: local-kafka
  zookeeper:
    replicas: 3
    storage:
      type: persistent-claim
      size: 500Gi
      deleteClaim: false
      class: local-zk
      overrides:
        - broker: 0
          class: local-zk
        - broker: 1
          class: local-zk
        - broker: 2
          class: local-zk
  entityOperator:
    topicOperator: {}
    userOperator: {}
