apiVersion: v1
kind: PersistentVolume
metadata:
  name: kafka3-pv
spec:
  capacity:
    storage: 2000Gi
  # volumeMode field requires BlockVolume Alpha feature gate to be enabled.
  volumeMode: Filesystem
  accessModes:
  - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-kafka
  local:
    path: /data/kafka3
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: pvrole
          operator: In
          values:
          - node3
