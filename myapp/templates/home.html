{% extends "appbuilder/base.html" %}

{% block add_tail_js %}
<script src="{{url_for('appbuilder.static',filename='js/marked.min.js')}}"></script>
{% endblock %}

{% block content %}
<div class="di-home" style="position: absolute;width: 100%;height: 100%;left: 0;top: 0px;padding-top:15px;background-color: #fff;overflow: hidden;">
    <iframe src="/static/appbuilder/vison/index.html#/home" style="width:100%;height:100%;border: none;"></iframe>
</div>
<script>
    window.onload = function () {
        document.querySelector('.di-home').addEventListener('click', function (e) {
            const event = e || window.event;
            const target = event.target || event.srcElement;
            console.log(target.dataset.url);
            if (target.nodeName === 'LI' && target.dataset.url) {
                if (target.dataset.open)
                {
                    console.log(target.dataset.url);
                    location.href = target.dataset.url;
                }
                else
                {
                  console.log(target.dataset.url);
                  open(target.dataset.url);
                }

            }
        });

        document.querySelectorAll('.markdown').forEach(item => {
            item.innerHTML = marked(item.textContent.trim())
            item.classList.remove('loading');
        });
    }
    // postMessage 事件
    function appframeListener(event) {
        if (event.data.type) {
            switch (event.data.type) {
                case 'link':
                    location.pathname = '/pipeline_modelview/api/web/' + event.data.message.pipelineId;
                    break;
                default:
                    break;
            }
        }
    }
    window.addEventListener('message', appframeListener, false);
</script>
{% endblock %}