apiVersion: v1
data:
  monitoring-kube-prometheus-alertmanager.yaml: |
    groups:
    - name: alertmanager.rules
      rules:
      - alert: AlertmanagerConfigInconsistent
        annotations:
          description: The configuration of the instances of the Alertmanager cluster
            `{{$labels.service}}` are out of sync.
          summary: Configuration out of sync
        expr: count_values("config_hash", alertmanager_config_hash) BY (service) / ON(service)
          GROUP_LEFT() label_replace(prometheus_operator_alertmanager_spec_replicas, "service",
          "alertmanager-$1", "alertmanager", "(.*)") != 1
        for: 5m
        labels:
          severity: critical
      - alert: AlertmanagerDownOrMissing
        annotations:
          description: An unexpected number of Alertmanagers are scraped or Alertmanagers
            disappeared from discovery.
          summary: Alertmanager down or missing
        expr: label_replace(prometheus_operator_alertmanager_spec_replicas, "job", "alertmanager-$1",
          "alertmanager", "(.*)") / ON(job) GROUP_RIGHT() sum(up) BY (job) != 1
        for: 5m
        labels:
          severity: warning
      - alert: AlertmanagerFailedReload
        annotations:
          description: Reloading Alertmanager's configuration has failed for {{ $labels.namespace
            }}/{{ $labels.pod}}.
          summary: Alertmanager's configuration reload failed
        expr: alertmanager_config_last_reload_successful == 0
        for: 10m
        labels:
          severity: warning
  monitoring-kube-prometheus-exporter-kube-controller-manager.yaml: |
    groups:
    - name: kube-controller-manager.rules
      rules:
      - alert: K8SControllerManagerDown
        annotations:
          description: There is no running K8S controller manager. Deployments and replication
            controllers are not making progress.
          runbook: https://coreos.com/tectonic/docs/latest/troubleshooting/controller-recovery.html#recovering-a-controller-manager
          summary: Controller manager is down
        expr: absent(up{job="kube-controller-manager"} == 1)
        for: 5m
        labels:
          severity: critical
  monitoring-kube-prometheus-exporter-kube-etcd.yaml: |
    groups:
    - name: ./etcd3.rules
      rules:
      - alert: InsufficientMembers
        annotations:
          description: If one more etcd member goes down the cluster will be unavailable
          summary: etcd cluster insufficient members
        expr: count(up{job="kube-etcd"} == 0) > (count(up{job="kube-etcd"}) / 2 - 1)
        for: 3m
        labels:
          severity: critical
      - alert: NoLeader
        annotations:
          description: etcd member {{ $labels.instance }} has no leader
          summary: etcd member has no leader
        expr: etcd_server_has_leader{job="kube-etcd"} == 0
        for: 1m
        labels:
          severity: critical
      - alert: HighNumberOfLeaderChanges
        annotations:
          description: etcd instance {{ $labels.instance }} has seen {{ $value }} leader
            changes within the last hour
          summary: a high number of leader changes within the etcd cluster are happening
        expr: increase(etcd_server_leader_changes_seen_total{job="kube-etcd"}[1h]) > 3
        labels:
          severity: warning
      - alert: HighNumberOfFailedGRPCRequests
        annotations:
          description: '{{ $value }}% of requests for {{ $labels.grpc_method }} failed
            on etcd instance {{ $labels.instance }}'
          summary: a high number of gRPC requests are failing
        expr: sum(rate(grpc_server_handled_total{grpc_code!="OK",job="kube-etcd"}[5m]))
          BY (grpc_service, grpc_method) / sum(rate(grpc_server_handled_total{job="kube-etcd"}[5m]))
          BY (grpc_service, grpc_method) > 0.01
        for: 10m
        labels:
          severity: warning
      - alert: HighNumberOfFailedGRPCRequests
        annotations:
          description: '{{ $value }}% of requests for {{ $labels.grpc_method }} failed
            on etcd instance {{ $labels.instance }}'
          summary: a high number of gRPC requests are failing
        expr: sum(rate(grpc_server_handled_total{grpc_code!="OK",job="kube-etcd"}[5m]))
          BY (grpc_service, grpc_method) / sum(rate(grpc_server_handled_total{job="kube-etcd"}[5m]))
          BY (grpc_service, grpc_method) > 0.05
        for: 5m
        labels:
          severity: critical
      - alert: GRPCRequestsSlow
        annotations:
          description: on etcd instance {{ $labels.instance }} gRPC requests to {{ $labels.grpc_method
            }} are slow
          summary: slow gRPC requests
        expr: histogram_quantile(0.99, sum(rate(grpc_server_handling_seconds_bucket{job="kube-etcd",grpc_type="unary"}[5m]))
          by (grpc_service, grpc_method, le)) > 0.15
        for: 10m
        labels:
          severity: critical
      - alert: HighNumberOfFailedHTTPRequests
        annotations:
          description: '{{ $value }}% of requests for {{ $labels.method }} failed on etcd
            instance {{ $labels.instance }}'
          summary: a high number of HTTP requests are failing
        expr: sum(rate(etcd_http_failed_total{job="kube-etcd"}[5m])) BY (method) / sum(rate(etcd_http_received_total{job="kube-etcd"}[5m]))
          BY (method) > 0.01
        for: 10m
        labels:
          severity: warning
      - alert: HighNumberOfFailedHTTPRequests
        annotations:
          description: '{{ $value }}% of requests for {{ $labels.method }} failed on etcd
            instance {{ $labels.instance }}'
          summary: a high number of HTTP requests are failing
        expr: sum(rate(etcd_http_failed_total{job="kube-etcd"}[5m])) BY (method) / sum(rate(etcd_http_received_total{job="kube-etcd"}[5m]))
          BY (method) > 0.05
        for: 5m
        labels:
          severity: critical
      - alert: HTTPRequestsSlow
        annotations:
          description: on etcd instance {{ $labels.instance }} HTTP requests to {{ $labels.method
            }} are slow
          summary: slow HTTP requests
        expr: histogram_quantile(0.99, rate(etcd_http_successful_duration_seconds_bucket[5m]))
          > 0.15
        for: 10m
        labels:
          severity: warning
      - alert: EtcdMemberCommunicationSlow
        annotations:
          description: etcd instance {{ $labels.instance }} member communication with
            {{ $labels.To }} is slow
          summary: etcd member communication is slow
        expr: histogram_quantile(0.99, rate(etcd_network_peer_round_trip_time_seconds_bucket[5m]))
          > 0.15
        for: 10m
        labels:
          severity: warning
      - alert: HighNumberOfFailedProposals
        annotations:
          description: etcd instance {{ $labels.instance }} has seen {{ $value }} proposal
            failures within the last hour
          summary: a high number of proposals within the etcd cluster are failing
        expr: increase(etcd_server_proposals_failed_total{job="kube-etcd"}[1h]) > 5
        labels:
          severity: warning
      - alert: HighFsyncDurations
        annotations:
          description: etcd instance {{ $labels.instance }} fync durations are high
          summary: high fsync durations
        expr: histogram_quantile(0.99, rate(etcd_disk_wal_fsync_duration_seconds_bucket[5m]))
          > 0.5
        for: 10m
        labels:
          severity: warning
      - alert: HighCommitDurations
        annotations:
          description: etcd instance {{ $labels.instance }} commit durations are high
          summary: high commit durations
        expr: histogram_quantile(0.99, rate(etcd_disk_backend_commit_duration_seconds_bucket[5m]))
          > 0.25
        for: 10m
        labels:
          severity: warning
  monitoring-kube-prometheus-exporter-kube-scheduler.yaml: |
    groups:
    - name: kube-scheduler.rules
      rules:
      - expr: histogram_quantile(0.99, sum(scheduler_e2e_scheduling_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.99"
        record: cluster:scheduler_e2e_scheduling_latency_seconds:quantile
      - expr: histogram_quantile(0.9, sum(scheduler_e2e_scheduling_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.9"
        record: cluster:scheduler_e2e_scheduling_latency_seconds:quantile
      - expr: histogram_quantile(0.5, sum(scheduler_e2e_scheduling_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.5"
        record: cluster:scheduler_e2e_scheduling_latency_seconds:quantile
      - expr: histogram_quantile(0.99, sum(scheduler_scheduling_algorithm_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.99"
        record: cluster:scheduler_scheduling_algorithm_latency_seconds:quantile
      - expr: histogram_quantile(0.9, sum(scheduler_scheduling_algorithm_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.9"
        record: cluster:scheduler_scheduling_algorithm_latency_seconds:quantile
      - expr: histogram_quantile(0.5, sum(scheduler_scheduling_algorithm_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.5"
        record: cluster:scheduler_scheduling_algorithm_latency_seconds:quantile
      - expr: histogram_quantile(0.99, sum(scheduler_binding_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.99"
        record: cluster:scheduler_binding_latency_seconds:quantile
      - expr: histogram_quantile(0.9, sum(scheduler_binding_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.9"
        record: cluster:scheduler_binding_latency_seconds:quantile
      - expr: histogram_quantile(0.5, sum(scheduler_binding_latency_microseconds_bucket)
          BY (le, cluster)) / 1e+06
        labels:
          quantile: "0.5"
        record: cluster:scheduler_binding_latency_seconds:quantile
      - alert: K8SSchedulerDown
        annotations:
          description: There is no running K8S scheduler. New pods are not being assigned
            to nodes.
          runbook: https://coreos.com/tectonic/docs/latest/troubleshooting/controller-recovery.html#recovering-a-scheduler
          summary: Scheduler is down
        expr: absent(up{job="kube-scheduler"} == 1)
        for: 5m
        labels:
          severity: critical
  monitoring-kube-prometheus-exporter-kube-state.yaml: |
    groups:
    - name: kube-state-metrics.rules
      rules:
      - alert: DeploymentGenerationMismatch
        annotations:
          description: Observed deployment generation does not match expected one for
            deployment {{$labels.namespace}}/{{$labels.deployment}}
          summary: Deployment is outdated
        expr: kube_deployment_status_observed_generation != kube_deployment_metadata_generation
        for: 15m
        labels:
          severity: warning
      - alert: DeploymentReplicasNotUpdated
        annotations:
          description: Replicas are not updated and available for deployment {{$labels.namespace}}/{{$labels.deployment}}
          summary: Deployment replicas are outdated
        expr: ((kube_deployment_status_replicas_updated != kube_deployment_spec_replicas)
          or (kube_deployment_status_replicas_available != kube_deployment_spec_replicas))
          unless (kube_deployment_spec_paused == 1)
        for: 15m
        labels:
          severity: warning
      - alert: DaemonSetRolloutStuck
        annotations:
          description: Only {{$value}}% of desired pods scheduled and ready for daemon
            set {{$labels.namespace}}/{{$labels.daemonset}}
          summary: DaemonSet is missing pods
        expr: kube_daemonset_status_number_ready / kube_daemonset_status_desired_number_scheduled
          * 100 < 100
        for: 15m
        labels:
          severity: warning
      - alert: K8SDaemonSetsNotScheduled
        annotations:
          description: A number of daemonsets are not scheduled.
          summary: Daemonsets are not scheduled correctly
        expr: kube_daemonset_status_desired_number_scheduled - kube_daemonset_status_current_number_scheduled
          > 0
        for: 10m
        labels:
          severity: warning
      - alert: DaemonSetsMissScheduled
        annotations:
          description: A number of daemonsets are running where they are not supposed
            to run.
          summary: Daemonsets are not scheduled correctly
        expr: kube_daemonset_status_number_misscheduled > 0
        for: 10m
        labels:
          severity: warning
      - alert: PodFrequentlyRestarting
        annotations:
          description: Pod {{$labels.namespace}}/{{$labels.pod}} was restarted {{$value}}
            times within the last hour
          summary: Pod is restarting frequently
        expr: increase(kube_pod_container_status_restarts_total[1h]) > 5
        labels:
          severity: warning
  monitoring-kube-prometheus-exporter-kubelets.yaml: |
    groups:
    - name: kubelet.rules
      rules:
      - alert: K8SNodeNotReady
        annotations:
          description: The Kubelet on {{ $labels.node }} has not checked in with the API,
            or has set itself to NotReady, for more than an hour
          summary: Node status is NotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 1h
        labels:
          severity: warning
      - alert: K8SManyNodesNotReady
        annotations:
          description: '{{ $value }}% of Kubernetes nodes are not ready'
        expr: count(kube_node_status_condition{condition="Ready",status="true"} == 0)
          > 1 and (count(kube_node_status_condition{condition="Ready",status="true"} ==
          0) / count(kube_node_status_condition{condition="Ready",status="true"})) * 100
          > 20
        for: 1m
        labels:
          severity: critical
      - alert: K8SKubeletDown
        annotations:
          description: Prometheus failed to scrape {{ $value }}% of kubelets.
          summary: Prometheus failed to scrape
        expr: count(up{job="kubelet"} == 0) / count(up{job="kubelet"}) * 100 > 3
        for: 1h
        labels:
          severity: warning
      - alert: K8SKubeletDown
        annotations:
          description: Prometheus failed to scrape {{ $value }}% of kubelets, or all Kubelets
            have disappeared from service discovery.
          summary: Many Kubelets cannot be scraped
        expr: (absent(up{job="kubelet"} == 1) or count(up{job="kubelet"} == 0) / count(up{job="kubelet"}))
          * 100 > 10
        for: 1h
        labels:
          severity: critical
      - alert: K8SKubeletTooManyPods
        annotations:
          description: Kubelet {{$labels.instance}} is running {{$value}} pods, close
            to the limit of 110
          summary: Kubelet is close to pod limit
        expr: kubelet_running_pod_count > 100
        for: 10m
        labels:
          severity: warning
  monitoring-kube-prometheus-exporter-kubernetes.yaml: |
    groups:
    - name: kubernetes.rules
      rules:
      - expr: sum(container_memory_working_set_bytes{container_name!="POD",pod_name!=""}) BY
          (pod_name)
        record: pod_name:container_memory_working_set_bytes:sum
      - expr: sum(container_spec_cpu_shares{container_name!="POD",pod_name!=""}) BY (pod_name)
        record: pod_name:container_spec_cpu_shares:sum
      - expr: sum(rate(container_cpu_usage_seconds_total{container_name!="POD",pod_name!=""}[5m]))
          BY (pod_name)
        record: pod_name:container_cpu_usage:sum
      - expr: sum(container_fs_usage_bytes{container_name!="POD",pod_name!=""}) BY (pod_name)
        record: pod_name:container_fs_usage_bytes:sum
      - expr: sum(container_memory_working_set_bytes{container_name!=""}) BY (namespace)
        record: namespace:container_memory_working_set_bytes:sum
      - expr: sum(container_spec_cpu_shares{container_name!=""}) BY (namespace)
        record: namespace:container_spec_cpu_shares:sum
      - expr: sum(rate(container_cpu_usage_seconds_total{container_name!="POD"}[5m]))
          BY (namespace)
        record: namespace:container_cpu_usage:sum
      - expr: sum(container_memory_working_set_bytes{container_name!="POD",pod_name!=""}) BY
          (cluster) / sum(machine_memory_bytes) BY (cluster)
        record: cluster:memory_usage:ratio
      - expr: sum(container_spec_cpu_shares{container_name!="POD",pod_name!=""}) / 1000
          / sum(machine_cpu_cores)
        record: cluster:container_spec_cpu_shares:ratio
      - expr: sum(rate(container_cpu_usage_seconds_total{container_name!="POD",pod_name!=""}[5m]))
          / sum(machine_cpu_cores)
        record: cluster:container_cpu_usage:ratio
      - expr: histogram_quantile(0.99, rate(apiserver_request_latencies_bucket[5m])) /
          1e+06
        labels:
          quantile: "0.99"
        record: apiserver_latency_seconds:quantile
      - expr: histogram_quantile(0.9, rate(apiserver_request_latencies_bucket[5m])) /
          1e+06
        labels:
          quantile: "0.9"
        record: apiserver_latency:quantile_seconds
      - expr: histogram_quantile(0.5, rate(apiserver_request_latencies_bucket[5m])) /
          1e+06
        labels:
          quantile: "0.5"
        record: apiserver_latency_seconds:quantile
      - alert: APIServerLatencyHigh
        annotations:
          description: the API server has a 99th percentile latency of {{ $value }} seconds
            for {{$labels.verb}} {{$labels.resource}}
          summary: API server high latency
        expr: apiserver_latency_seconds:quantile{quantile="0.99",subresource!="log",verb!~"^(?:WATCH|WATCHLIST|PROXY|CONNECT)$"}
          > 1
        for: 10m
        labels:
          severity: warning
      - alert: APIServerLatencyHigh
        annotations:
          description: the API server has a 99th percentile latency of {{ $value }} seconds
            for {{$labels.verb}} {{$labels.resource}}
          summary: API server high latency
        expr: apiserver_latency_seconds:quantile{quantile="0.99",subresource!="log",verb!~"^(?:WATCH|WATCHLIST|PROXY|CONNECT)$"}
          > 4
        for: 10m
        labels:
          severity: critical
      - alert: APIServerErrorsHigh
        annotations:
          description: API server returns errors for {{ $value }}% of requests
          summary: API server request errors
        expr: rate(apiserver_request_count{code=~"^(?:5..)$"}[5m]) / rate(apiserver_request_count[5m])
          * 100 > 2
        for: 10m
        labels:
          severity: warning
      - alert: APIServerErrorsHigh
        annotations:
          description: API server returns errors for {{ $value }}% of requests
        expr: rate(apiserver_request_count{code=~"^(?:5..)$"}[5m]) / rate(apiserver_request_count[5m])
          * 100 > 5
        for: 10m
        labels:
          severity: critical
      - alert: K8SApiserverDown
        annotations:
          description: No API servers are reachable or all have disappeared from service
            discovery
          summary: No API servers are reachable
        expr: absent(up{job="apiserver"} == 1)
        for: 20m
        labels:
          severity: critical
      - alert: K8sCertificateExpirationNotice
        annotations:
          description: Kubernetes API Certificate is expiring soon (less than 7 days)
          summary: Kubernetes API Certificate is expiering soon
        expr: sum(apiserver_client_certificate_expiration_seconds_bucket{le="604800"})
          > 0
        labels:
          severity: warning
      - alert: K8sCertificateExpirationNotice
        annotations:
          description: Kubernetes API Certificate is expiring in less than 1 day
          summary: Kubernetes API Certificate is expiering
        expr: sum(apiserver_client_certificate_expiration_seconds_bucket{le="86400"})
          > 0
        labels:
          severity: critical
  monitoring-kube-prometheus-exporter-node.yaml: |
    groups:
    - name: node.rules
      rules:
      - expr: sum(rate(node_cpu_seconds_total{mode!="idle",mode!="iowait"}[3m])) BY (instance)
        record: instance:node_cpu_seconds_total:rate:sum
      - expr: sum((node_filesystem_size_bytes{mountpoint="/"} - node_filesystem_free{mountpoint="/"}))
          BY (instance)
        record: instance:node_filesystem_usage:sum
      - expr: sum(rate(node_network_receive_bytes_total[3m])) BY (instance)
        record: instance:node_network_receive_bytes_total:rate:sum
      - expr: sum(rate(node_network_transmit_bytes_total[3m])) BY (instance)
        record: instance:node_network_transmit_bytes_total:rate:sum
      - expr: sum(rate(node_cpu_seconds_total{mode!="idle",mode!="iowait"}[5m])) WITHOUT (cpu, mode)
          / ON(instance) GROUP_LEFT() count(sum(node_cpu_seconds_total) BY (instance, cpu)) BY (instance)
        record: instance:node_cpu_seconds_total:ratio
      - expr: sum(rate(node_cpu_seconds_total{mode!="idle",mode!="iowait"}[5m]))
        record: cluster:node_cpu_seconds_total:sum_rate5m
      - expr: cluster:node_cpu_seconds_total:rate5m / count(sum(node_cpu_seconds_total) BY (instance, cpu))
        record: cluster:node_cpu_seconds_total:ratio
      - alert: NodeExporterDown
        annotations:
          description: Prometheus could not scrape a node-exporter for more than 10m,
            or node-exporters have disappeared from discovery
          summary: Prometheus could not scrape a node-exporter
        expr: absent(up{job="node-exporter"} == 1)
        for: 10m
        labels:
          severity: warning
      - alert: NodeDiskRunningFull
        annotations:
          description: device {{$labels.device}} on node {{$labels.instance}} is running
            full within the next 24 hours (mounted at {{$labels.mountpoint}})
          summary: Node disk is running full within 24 hours
        expr: predict_linear(node_filesystem_free{job="node-exporter",mountpoint!~"^/etc/(?:resolv.conf|hosts|hostname)$"}[6h],
          3600 * 24) < 0 and on(instance) up{job="node-exporter"}
        for: 30m
        labels:
          severity: warning
      - alert: NodeDiskRunningFull
        annotations:
          description: device {{$labels.device}} on node {{$labels.instance}} is running
            full within the next 2 hours (mounted at {{$labels.mountpoint}})
          summary: Node disk is running full within 2 hours
        expr: predict_linear(node_filesystem_free{job="node-exporter",mountpoint!~"^/etc/(?:resolv.conf|hosts|hostname)$"}[30m],
          3600 * 2) < 0 and on(instance) up{job="node-exporter"}
        for: 10m
        labels:
          severity: critical
      - alert: NodeCPURunningFull
        annotations:
          description: node {{$labels.instance}} cpu is used with {{$value}} %
          summary: Node cup is overload
        expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
      - alert: NodeMemoryRunningFull
        annotations:
          description: node {{$labels.instance}} memory is used more than 90%, and the
            current value is {{$value}}%.
          summary: Node memory useage is high
        expr: ((node_memory_MemTotal_bytes - node_memory_MemFree_bytes  - node_memory_Buffers_bytes - node_memory_Cached_bytes)
          / node_memory_MemTotal_bytes) * 100 > 90
  monitoring-kube-prometheus-rules.yaml: |
    groups:
    - name: prometheus.rules
      rules:
      - alert: PrometheusConfigReloadFailed
        annotations:
          description: Reloading Prometheus' configuration has failed for {{$labels.namespace}}/{{$labels.pod}}
          summary: Reloading Promehteus' configuration failed
        expr: prometheus_config_last_reload_successful == 0
        for: 10m
        labels:
          severity: warning
      - alert: PrometheusNotificationQueueRunningFull
        annotations:
          description: Prometheus' alert notification queue is running full for {{$labels.namespace}}/{{
            $labels.pod}}
          summary: Prometheus' alert notification queue is running full
        expr: predict_linear(prometheus_notifications_queue_length[5m], 60 * 30) > prometheus_notifications_queue_capacity
        for: 10m
        labels:
          severity: warning
      - alert: PrometheusErrorSendingAlerts
        annotations:
          description: Errors while sending alerts from Prometheus {{$labels.namespace}}/{{
            $labels.pod}} to Alertmanager {{$labels.Alertmanager}}
          summary: Errors while sending alert from Prometheus
        expr: rate(prometheus_notifications_errors_total[5m]) / rate(prometheus_notifications_sent_total[5m])
          > 0.01
        for: 10m
        labels:
          severity: warning
      - alert: PrometheusErrorSendingAlerts
        annotations:
          description: Errors while sending alerts from Prometheus {{$labels.namespace}}/{{
            $labels.pod}} to Alertmanager {{$labels.Alertmanager}}
          summary: Errors while sending alerts from Prometheus
        expr: rate(prometheus_notifications_errors_total[5m]) / rate(prometheus_notifications_sent_total[5m])
          > 0.03
        for: 10m
        labels:
          severity: critical
      - alert: PrometheusNotConnectedToAlertmanagers
        annotations:
          description: Prometheus {{ $labels.namespace }}/{{ $labels.pod}} is not connected
            to any Alertmanagers
          summary: Prometheus is not connected to any Alertmanagers
        expr: prometheus_notifications_alertmanagers_discovered < 1
        for: 10m
        labels:
          severity: warning
      - alert: PrometheusTSDBReloadsFailing
        annotations:
          description: '{{$labels.job}} at {{$labels.instance}} had {{$value | humanize}}
            reload failures over the last four hours.'
          summary: Prometheus has issues reloading data blocks from disk
        expr: increase(prometheus_tsdb_reloads_failures_total[2h]) > 0
        for: 12h
        labels:
          severity: warning
      - alert: PrometheusTSDBCompactionsFailing
        annotations:
          description: '{{$labels.job}} at {{$labels.instance}} had {{$value | humanize}}
            compaction failures over the last four hours.'
          summary: Prometheus has issues compacting sample blocks
        expr: increase(prometheus_tsdb_compactions_failed_total[2h]) > 0
        for: 12h
        labels:
          severity: warning
      - alert: PrometheusTSDBWALCorruptions
        annotations:
          description: '{{$labels.job}} at {{$labels.instance}} has a corrupted write-ahead
            log (WAL).'
          summary: Prometheus write-ahead log is corrupted
        expr: tsdb_wal_corruptions_total > 0
        for: 4h
        labels:
          severity: warning
      - alert: PrometheusNotIngestingSamples
        annotations:
          description: Prometheus {{ $labels.namespace }}/{{ $labels.pod}} isn't ingesting
            samples.
          summary: Prometheus isn't ingesting samples
        expr: rate(prometheus_tsdb_head_samples_appended_total[5m]) <= 0
        for: 10m
        labels:
          severity: warning
      - alert: PrometheusTargetScapesDuplicate
        annotations:
          description: '{{$labels.namespace}}/{{$labels.pod}} has many samples rejected
            due to duplicate timestamps but different values'
          summary: Prometheus has many samples rejected
        expr: increase(prometheus_target_scrapes_sample_duplicate_timestamp_total[5m])
          > 0
        for: 10m
        labels:
          severity: warning
      - alert: PodRestartingTooMuch
        annotations:
          description: Pod {{$labels.namespace}} / {{ $labels.name }} restarting too much.
          summary: Pod {{ $labels.namespace }} / {{ $labels.name }} restarting too much.
        expr: rate(kube_pod_container_status_restarts[2m]) > 1 / (5 * 60)
        for: 30m
        labels:
          severity: critical
      - alert: PodCpuRunningFull
        annotations:
          description: Pod {{ $labels.pod_name }} cpu usage is more than 95%, and the
            current value is {{ $value }.
          summary: PodCpuRunningFull
        expr: pod_name:container_cpu_usage:sum * 100 > 95
        for: 5m
        labels:
          severity: critical
      - alert: ContainerMemoryRunningFull
        annotations:
          description: Pod:{{$labels.pod_name}},container:{{$labels.container}} memory
            useage exceeded 95% for 5 minutes, and the current value for useage is {{$value}}%.
          summary: ContainerMemoryRunningFull
        expr: label_replace(container_memory_working_set_bytes, "container", "$1", "container_name",  "(.*)")
          / on(container, pod_name) label_replace(kube_pod_container_resource_limits_memory_bytes,
          "pod_name", "$1", "pod",  "(.*)") * 100 > 95
        for: 5m
        labels:
          severity: critical
    - name: normal.rules
      rules:
      - alert: InstanceDown
        annotations:
          text: |
            {{ $labels.instance }} of job {{ $labels.job }} has been down for more than 5 minute.
        expr: up == 0
        for: 5m
        labels:
          severity: critical
  monitoring-kube-prometheus.yaml: |
    groups:
    - name: general.rules
      rules:
      - alert: TargetDown
        annotations:
          description: '{{ $value }}% of {{ $labels.job }} targets are down.'
          summary: Targets are down
        expr: 100 * (count(up == 0) BY (job) / count(up) BY (job)) > 10
        for: 10m
        labels:
          severity: warning
      - alert: DeadMansSwitch
        annotations:
          description: This is a DeadMansSwitch meant to ensure that the entire Alerting
            pipeline is functional.
          summary: Alerting DeadMansSwitch
        expr: vector(1)
        labels:
          severity: none
      - expr: process_open_fds / process_max_fds
        record: fd_utilization
      - alert: FdExhaustionClose
        annotations:
          description: '{{ $labels.job }}: {{ $labels.namespace }}/{{ $labels.pod }} instance
            will exhaust in file/socket descriptors within the next 4 hours'
          summary: file descriptors soon exhausted
        expr: predict_linear(fd_utilization[1h], 3600 * 4) > 1
        for: 10m
        labels:
          severity: warning
      - alert: FdExhaustionClose
        annotations:
          description: '{{ $labels.job }}: {{ $labels.namespace }}/{{ $labels.pod }} instance
            will exhaust in file/socket descriptors within the next hour'
          summary: file descriptors soon exhausted
        expr: predict_linear(fd_utilization[10m], 3600) > 1
        for: 10m
        labels:
          severity: critical
  monitoring-prometheus-rule-kafka.yaml: |
    groups:
    - name: kafka.rules
      rules:
      - alert: KafkaNoControllerActive
        annotations:
          description: kafka 集群没有controller节点
          summary: kafka no controller is actived
        expr: sum(kafka_controller_kafkacontroller_activecontrollercount_value) < 1
        for: 5m
        labels:
          severity: critical
      - alert: KafkaBrokersTooLittle
        annotations:
          description: kafka brokers is less than 3, and the current value is {{$value}}
          summary: KafkaBrokersLess3
        expr: kafka_brokers < 1
        for: 10m
        labels:
          severity: critical
      - alert: KafkaPartitionOffline
        annotations:
          description: kafka超过存在下线的partition，当前已下线的partition数量为：{{$value}}
          summary: HasPartitionOffline
        expr: kafka_controller_kafkacontroller_offlinepartitionscount_value > 0
        labels:
          severity: critical
      - alert: KafkaProduceFailed
        annotations:
          description: kafka失败请求数大于0，instance：{{$lables.instance}},topic:{{$lables.topic}}
            在5分钟内产生了{{$value}}次produce错误
          summary: KafkaProduceFailed
        expr: increase(kafka_server_brokertopicmetrics_failedproducerequestspersec_count[5m])
          > 0
        labels:
          severity: warnning
      - alert: KafkaFetchFailed
        annotations:
          description: kafka失败消费数大于0，instance：{{$lables.instance}},topic:{{$lables.topic}}
            在5分钟内产生了{{$value}}次fetch错误
          summary: KafkaFetchFailed
        expr: increase(kafka_server_brokertopicmetrics_failedfetchrequestspersec_count[5m])
          > 0
        labels:
          severity: warnning
      - alert: KafkaUncleanLeaderElectionbusy
        annotations:
          description: kafka争议的leader选举次数过于繁忙，pod:{{$labels.pod}}在5分钟内发生了{{$value}}次选举
          summary: KafkaUncleanLeaderElectionbusy
        expr: increase(kafka_controller_controllerstats_uncleanleaderelectionspersec_count[5m])
          > 0
        labels:
          severity: critical
      - alert: JvmHeapRunningFull
        annotations:
          description: namespace:{{$labels.namespace}},pod:{{$labels.pod}} jvm heap 内存不足，当前已使用堆内存{{$value}}%.
          summary: JvmHeapRunningFull
        expr: 100 * (java_lang_memory_heapmemoryusage_used / java_lang_memory_heapmemoryusage_max)
          > 95
        for: 5m
        labels:
          severity: critical
  monitoring-prometheus-rule-mysql.yaml: |
    groups:
    - name: mysql.rules
      rules:
      - alert: QuestionsSuddenChange
        annotations:
          description: mysql (pod {{$labels.pod}}) 吞吐量骤变，可能出现某个严重问题
          summary: MySQL throughput is sudden change
        expr: irate(mysql_global_status_questions[5m]) > 50
        labels:
          severity: critical
      - alert: TooManyThreadWaitedLock
        annotations:
          description: mysql (pod {{$labels.pod}}) 超过20个连接在等待行级锁，当前值：{{$value}}，持续了5分钟
          summary: MySQL throughput is sudden change
        expr: mysql_global_status_innodb_row_lock_current_waits > 20
        for: 5m
        labels:
          severity: critical
      - alert: TooManySlowQueriesFromMysql
        annotations:
          description: mysql一小时内产生了{{ $value }}条慢查询日志.
          summary: TooManySlowQueriesFromMysql
        expr: increase(mysql_global_status_slow_queries[1h]) > 20
        labels:
          severity: critical
      - alert: AvailableConnectionsNotEnough
        annotations:
          description: mysql (pod {{$labels.pod}}) 可用连接数低于5%，当前已使用{{$value}}%
          summary: Mysql available connections are not enough
        expr: (mysql_global_status_threads_connected/mysql_global_variables_max_connections)
          *100 > 95
        for: 5m
        labels:
          severity: warnning
      - alert: MysqlAbortedTooMuch
        annotations:
          description: mysql (pod {{$labels.pod}}) 大量连接被拒绝，可能原因：mysql可用连接数已耗尽
          summary: Mysql available connections are not enough
        expr: irate(mysql_global_status_connection_errors_total{error="max_connections"}[5m])
          > 10
        for: 5m
        labels:
          severity: critical
      - alert: MysqlManyDiskRequest
        annotations:
          description: mysql (pod {{$labels.pod}}) 大量请求无法走缓冲池，因而从磁盘中读取
          summary: Mysql available connections are not enough
        expr: irate(mysql_global_status_innodb_buffer_pool_reads[1m]) > 10
        for: 5m
        labels:
          severity: critical
      - alert: MysqlBufferPoolTooSmall
        annotations:
          description: mysql pod {{$labels.pod}} 缓冲池利用率已超过90%，当前值{{$value}}%，请结合Innodb_buffer_pool_reads（磁盘读取的请求数）增长率情况来判断缓冲池大小是否合理
          summary: Mysql Buffer Pool maybe very small
        expr: 100 * mysql_global_status_innodb_page_size{} * on (instance) mysql_global_status_buffer_pool_pages{state="data"}
          / on(instance) mysql_global_variables_innodb_buffer_pool_size{} > 90
        for: 5m
        labels:
          severity: warnning
kind: ConfigMap
metadata:
  labels:
    managed-by: prometheus-operator
  name: prometheus-kube-prometheus-rulefiles
  namespace: monitoring
  ownerReferences:
  - apiVersion: monitoring.coreos.com/v1
    blockOwnerDeletion: true
    controller: true
    kind: Prometheus
    name: kube-prometheus
  resourceVersion: "2469"
