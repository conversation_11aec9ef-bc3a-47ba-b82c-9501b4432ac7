"use strict";(self.webpackChunkkubeflow_frontend=self.webpackChunkkubeflow_frontend||[]).push([[204],{6857:function(e,n,t){t.d(n,{Z:function(){return o}});var a=t(29439),r=t(7517),s=t(10007),i=t(4519),c=t(2556);function o(e){var n,t=Math.random().toString(36).substring(2),o=(0,i.useState)(e.options||[]),l=(0,a.Z)(o,2),u=l[0],d=l[1],f=(0,i.useState)(e.value||""),h=(0,a.Z)(f,2),m=h[0],p=h[1];(0,i.useEffect)((function(){var n=e.isOpenSearchMatch?(e.options||[]).filter((function(e){return-1!==e.indexOf(m)})):e.options||[];d(n)}),[e.options]),(0,i.useEffect)((function(){p(e.value||"")}),[e.value]);var x=function(n){p(n),e.onChange&&e.onChange(n)},v=function(e){var n=m,t=e.indexOf(m);if(-1===t)return(0,c.jsx)("span",{children:e});var a=e.substring(0,t),r=e.substring(t+m.length);return(0,c.jsxs)("span",{children:[a,(0,c.jsx)("span",{className:"highlight",children:n}),r]})},g=function(e){var n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500;return function(){for(var a=arguments.length,r=new Array(a),s=0;s<a;s++)r[s]=arguments[s];clearTimeout(n),n=setTimeout((function(){e&&e.apply(null,[].concat(r))}),t)}}(e.onScrollButtom);return(0,c.jsxs)("div",{className:"select-down-modern",children:[e.labelName?(0,c.jsx)("label",{htmlFor:t,className:"pb4 mb0 fs12 d-b",children:e.labelName}):null,(0,c.jsxs)("div",{className:"p-r d-f ac",style:{width:e.width||"100%"},children:[(0,c.jsx)(s.Z,{style:{width:"100%"},disabled:e.disabled,id:t,placeholder:e.placeholder||"",maxLength:e.maxLength||200,onChange:function(e){return x(e.target.value)},onKeyPress:function(t){13===t.nativeEvent.keyCode&&(n.blur&&n.blur(),e.onSearch&&e.onSearch(t.currentTarget.value))},value:m,ref:function(e){return n=e}}),(0,c.jsx)(r.Z,{className:"p-a r0 mr8"})]}),u.length?(0,c.jsxs)("ul",{className:"select-option shadow",onScroll:function(n){n.stopPropagation();var t=n.currentTarget,a=t.scrollTop,r=t.clientHeight;t.scrollHeight-r-a<20&&e.onScrollButtom&&g()},style:{maxHeight:"".concat(e.maxHeight,"px")},children:[e.loading?(0,c.jsx)("div",{className:"p-s z9 ta-r",style:{right:"".concat(0,"px"),top:"".concat(0,"px")},children:(0,c.jsx)("div",{className:"d-il p-a",style:{right:"".concat(8,"px"),top:"".concat(0,"px")}})}):null,u.map((function(n,t){return(0,c.jsx)("li",{className:"ellip1",onMouseDown:function(){return function(n){x(n),e.onClick&&e.onClick(n)}(n)},children:v(n)},t)}))]}):null]})}},76877:function(e,n,t){t.d(n,{Z:function(){return k}});var a=t(93433),r=t(29439),s=t(1413),i=t(45987),c=t(4519),o=t(35492),l=t(20011),u=t(79551),d=t(28532),f=t(12513),h=t(80211),m=t(10089),p=t(2704),x=t(25738),v=t(1126),g=t.p+"static/media/emptyBg.15fdf5f39309784ac66e.png",Z=t(74308),j=t(81748),b=t(2556),N=["onResize","width"],S=t(17972),y=function(e){var n=e.onResize,t=e.width,a=(0,i.Z)(e,N);return t?(0,b.jsx)(Z.Resizable,{width:t,height:0,handle:(0,b.jsx)("span",{className:"react-resizable-handle",onClick:function(e){e.stopPropagation()}}),onResize:n,draggableOpts:{enableUserSelectHack:!1},children:(0,b.jsx)("th",(0,s.Z)((0,s.Z)({},a),{},{style:(0,s.Z)((0,s.Z)({},null===a||void 0===a?void 0:a.style),{},{userSelect:"none"})}))}):(0,b.jsx)("th",(0,s.Z)({},a))},k=function(e){var n=(0,c.useState)(!1),t=(0,r.Z)(n,2),i=t[0],Z=t[1],N=(0,c.useState)({header:[],data:[]}),k=(0,r.Z)(N,2),C=k[0],E=k[1],w=(0,c.useState)([]),I=(0,r.Z)(w,2),T=I[0],O=I[1],P=(0,c.useState)(e.columns),R=(0,r.Z)(P,2),M=R[0],D=R[1],A=function(n){return function(t,r){var i=r.size;if(!(i.width<100)){var c=(0,a.Z)(M);c[n]=(0,s.Z)((0,s.Z)({},c[n]),{},{width:i.width});var o=c.reduce((function(e,n){return e+n.width||100}),0)+200;localStorage.setItem(e.tableKey||"",JSON.stringify(c)),_((0,s.Z)((0,s.Z)({},U),{},{x:o})),D(c)}}},L=M.map((function(e,n){return(0,s.Z)((0,s.Z)({},e),{},{width:e.width||200,onHeaderCell:function(e){return{width:e.width,onResize:A(n)}}})})),H=(0,c.useState)(e.scroll),F=(0,r.Z)(H,2),U=F[0],_=F[1],z=(0,j.$G)(),B=z.t;z.i18n;(0,c.useEffect)((function(){D(e.columns)}),[e.columns]),(0,c.useEffect)((function(){_(e.scroll)}),[e.scroll]),(0,c.useEffect)((function(){if(e.dataSource){var n=e.columns.filter((function(e){return~T.indexOf(e.dataIndex)}));G(n,e.dataSource)}}),[e.dataSource,e.columns]);var G=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,t=e.map((function(e){return e.dataIndex})).filter((function(e){return"handle"!==e})),a=e.map((function(e){return e.title})).filter((function(e){return e!==B("\u64cd\u4f5c")})),r=[];n.forEach((function(e){var n={};t.map((function(t){var a=e[t];n[t]=a||""})),r.push(n)})),E({header:a,data:r})},V=function(){var e=C.header,n=C.data,t="";return e.length&&n.length?(t="|"+e.join("|")+"|\n",n.forEach((function(e){var n=Object.values(e).map((function(e){return""===e?" ":e}));t=t+"|"+n.join("|")+"|\n"}))):t="",t},Y=function(){var e=C.header,n=C.data,t="";return e.length&&n.length?(t=e.join("\t")+"\n",n.forEach((function(e){var n=Object.values(e).map((function(e){return""===e?" ":e}));t=t+n.join("\t")+"\n"}))):t="",t};return(0,b.jsxs)(l.Z,{className:"tablebox",direction:"vertical",size:"middle",children:[(0,b.jsxs)(u.Z,{width:1e3,maskClosable:!1,centered:!0,bodyStyle:{maxHeight:500,overflow:"auto"},visible:i,title:B("\u5bfc\u51fa\u6570\u636e"),onCancel:function(){Z(!1)},footer:null,children:[(0,b.jsxs)("div",{style:{position:"relative"},children:[(0,b.jsxs)("div",{className:"mb16",children:[(0,b.jsxs)("span",{className:"pr8",children:[B("\u9009\u62e9\u9700\u8981\u5bfc\u51fa\u7684\u5217"),"\uff1a"]}),(0,b.jsx)(d.Z.Group,{options:e.columns.map((function(e){return{label:e.title,value:e.dataIndex}})).filter((function(e){return"handle"!==e.value})),defaultValue:[],value:T,onChange:function(n){O(n);var t=e.columns.filter((function(e){return~n.indexOf(e.dataIndex)}));G(t,e.dataSource)}})]}),(0,b.jsxs)("div",{style:{position:"absolute",right:0,bottom:0},children:[(0,b.jsx)(f.Z,{size:"small",type:"link",onClick:function(){O(e.columns.map((function(e){return e.dataIndex})).filter((function(e){return"handle"!==e}))),G(e.columns,e.dataSource)},children:B("\u5168\u9009")}),(0,b.jsx)(f.Z,{size:"small",type:"link",onClick:function(){O([]),G([],e.dataSource)},children:B("\u53cd\u9009")})]})]}),(0,b.jsxs)(h.Z,{children:[(0,b.jsx)(h.Z.TabPane,{tab:"Wiki\u683c\u5f0f",children:(0,b.jsx)(S,{text:V(),onCopy:function(){return m.ZP.success(B("\u5df2\u590d\u5236\u5230\u7c98\u8d34\u677f"))},children:(0,b.jsx)("pre",{style:{cursor:"pointer",minHeight:100},children:(0,b.jsx)("code",{children:V()})})})},"jira"),(0,b.jsx)(h.Z.TabPane,{tab:"Text\u683c\u5f0f",children:(0,b.jsx)(S,{text:Y(),onCopy:function(){return m.ZP.success(B("\u5df2\u590d\u5236\u5230\u7c98\u8d34\u677f"))},children:(0,b.jsx)("pre",{style:{cursor:"pointer",minHeight:100},children:(0,b.jsx)("code",{children:Y()})})})},"test")]})]}),e.titleNode||e.buttonNode||!e.cancelExportData?(0,b.jsxs)(o.Z,{justify:"space-between",align:"middle",children:[(0,b.jsx)(p.Z,{children:(0,b.jsx)(l.Z,{align:"center",children:e.titleNode})}),(0,b.jsx)(p.Z,{children:(0,b.jsxs)(l.Z,{align:"center",children:[e.buttonNode,e.cancelExportData?null:(0,b.jsx)(f.Z,{style:{marginLeft:6},onClick:function(){return Z(!0)},children:B("\u5bfc\u51fa\u6570\u636e")})]})})]}):null,(0,b.jsx)(x.ZP,{renderEmpty:function(){return(0,b.jsxs)(o.Z,{justify:"center",align:"middle",style:{height:360,flexDirection:"column"},children:[(0,b.jsx)("img",{src:g,style:{width:266},alt:""}),(0,b.jsx)("div",{children:B("\u6682\u65e0\u6570\u636e")})]})},children:(0,b.jsx)(v.Z,{size:e.size||"middle",rowKey:e.rowKey?e.rowKey:"id",dataSource:e.dataSource,components:{header:{cell:y}},columns:L,pagination:!1!==e.pagination&&(0,s.Z)({},e.pagination),scroll:U,loading:e.loading,onChange:e.onChange,rowSelection:e.rowSelection})})]})}},90756:function(e,n,t){t.r(n),t.d(n,{default:function(){return ie}});var a=t(1413),r=t(93433),s=t(29439),i=t(4942),c=t(33405),o=t(39625),l=t(18346),u=t(10089),d=t(79551),f=t(80211),h=t(86457),m=t(12513),p=t(4519),x=t(68739),v=function(e){return x.Z.post("/idex/submit_task",e)},g=function(e){return x.Z.get("/idex/look/".concat(e))},Z=function(e,n){return x.Z.get("/idex/download_url/".concat(e),{params:{separator:n}})},j=function(e){return x.Z.get("/idex/result/".concat(e))},b=function(e){return x.Z.get("/idex/stop/".concat(e))},N=function(){return x.Z.get("/idex/config")},S=t(57796),y=t(37090),k=t(81535),C=t(65726),E=t(2556);function w(e){return(0,E.jsx)(E.Fragment,{children:(0,E.jsx)(S.ZP,{theme:y.F,value:e.value,onChange:e.onChange,extensions:[(0,C.i6)(),(0,k.r)({step:2,lightColor:"#fafafa",darkColor:"#fafafa"})]})})}var I=t(6857),T=t(51216),O=t.n(T),P=t(32064),R=t(81866),M=t(56713),D=t(28501),A=t(89923),L=t(51052),H=t(51222),F=t(34745),U=t(68553),_=t(10388),z=t(35492),B=t(2704),G=t(76877),V=t(81748),Y={start:0,parse:1,execute:2,end:3},q=function(e){switch(e){case"init":case"running":return(0,E.jsx)(P.Z,{});case"failure":return(0,E.jsx)(R.Z,{style:{color:"#ff4444"}});case"stop":return(0,E.jsx)(M.Z,{});default:return null}};function X(e){var n=(0,V.$G)(),t=n.t,c=(n.i18n,(0,p.useState)(!1)),o=(0,s.Z)(c,2),l=o[0],f=o[1],h=(0,p.useState)(!1),x=(0,s.Z)(h,2),v=(x[0],x[1]),g=(0,p.useState)(!0),N=(0,s.Z)(g,2),S=(N[0],N[1]),y=(0,p.useState)("|"),k=(0,s.Z)(y,2),C=k[0],w=k[1],I=(0,p.useRef)(C),T=function(e){I.current=e,w(e)},O=(0,p.useState)("|"),P=(0,s.Z)(O,2),R=P[0],M=P[1],X=(0,p.useRef)(R),K=function(e){var n=e[0]||[],t=e.slice(1).map((function(e){return e.reduce((function(e,t,r){return(0,a.Z)((0,a.Z)({},e),{},(0,i.Z)({},n[r],t))}),{})}));return{config:n.map((function(e){return{title:e,dataIndex:e,key:e,width:120}})),data:t}};return(0,E.jsxs)("div",{children:[(0,E.jsx)(d.Z,{title:t("\u7ed3\u679c"),visible:l,footer:null,destroyOnClose:!0,onCancel:function(){T("|"),f(!1)},children:(0,E.jsxs)("div",{children:[(0,E.jsxs)("div",{className:"d-f ac pt8",children:[(0,E.jsxs)("div",{className:"w96",children:[t("\u9009\u62e9\u5206\u9694\u7b26"),"\uff1a"]}),(0,E.jsx)(H.Z,{style:{width:256},value:I.current,options:[{label:"|",value:"|"},{label:",",value:","},{label:"TAB",value:"TAB"}],onChange:function(e){T(e)}})]}),(0,E.jsx)("div",{className:"ta-r pt16",children:(0,E.jsx)(m.Z,{type:"primary",onClick:function(){Z(X.current,I.current).then((function(e){window.open(e.data.download_url,"bank")})).catch((function(e){console.log(e)}))},children:t("\u4e0b\u8f7d")})})]})}),(0,E.jsx)(F.Z,{className:"site-collapse-custom-collapse",defaultActiveKey:["task_0"],onChange:function(e){console.log(e)},children:(Object.entries(e.option).reduce((function(e,n){var t=(0,s.Z)(n,2),a=(t[0],t[1]);return[].concat((0,r.Z)(e),[a])}),[])||[]).reverse().filter((function(e){return!!e.reqId})).map((function(n,a){return(0,E.jsxs)(F.Z.Panel,{className:["site-collapse-custom-panel","status-".concat(n.status)].join(" "),header:"".concat(t("\u5b50\u4efb\u52a1")).concat(n.reqId),extra:(0,E.jsxs)(E.Fragment,{children:[(0,E.jsxs)(m.Z,{className:"mr16",type:"default",size:"small",onClick:function(t){t.stopPropagation(),e.onDelete(n.reqId)},children:[t("\u5220\u9664"),(0,E.jsx)(D.Z,{})]}),(0,E.jsxs)(m.Z,{type:"primary",size:"small",onClick:function(t){t.stopPropagation(),e.onRetry(n.reqId)},children:[t("\u91cd\u8bd5"),(0,E.jsx)(A.Z,{})]})]}),children:[(0,E.jsxs)(U.Z,{size:"small",current:Y[n.step],children:[(0,E.jsx)(U.Z.Step,{title:t("\u51c6\u5907\u5f00\u59cb"),icon:0===Y[n.step]?q(n.status):null}),(0,E.jsx)(U.Z.Step,{title:t("\u89e3\u6790"),icon:1===Y[n.step]?q(n.status):null}),(0,E.jsx)(U.Z.Step,{title:t("\u6267\u884c"),icon:2===Y[n.step]?q(n.status):null}),(0,E.jsx)(U.Z.Step,{title:t("\u8f93\u51fa\u7ed3\u679c"),icon:3===Y[n.step]?q(n.status):null})]}),(0,E.jsx)(G.Z,{size:"small",loading:!1,cancelExportData:!0,rowKey:function(e){return JSON.stringify(e)},columns:[{title:t("\u5b50\u4efb\u52a1"),dataIndex:"content",key:"content",render:function(e){return(0,E.jsx)(_.Z,{placement:"top",title:e,children:(0,E.jsx)("div",{className:"ellip1 w256",children:e})})}},{title:t("\u5f00\u59cb\u65f6\u95f4"),dataIndex:"startime",key:"startime"},{title:t("\u8fd0\u884c\u65f6\u957f"),dataIndex:"duration",key:"duration"},{title:t("\u72b6\u6001"),dataIndex:"status",key:"status",render:function(e){return(0,E.jsx)("span",{className:["c-".concat(n.status)].join(" "),children:e})}},{title:t("\u64cd\u4f5c"),dataIndex:"action",key:"action",render:function(){return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)("span",{className:"link mr16",onClick:function(){d.Z.info({title:t("\u4efb\u52a1\u8be6\u60c5"),width:600,okText:t("\u5173\u95ed"),content:(0,E.jsxs)("div",{children:[(0,E.jsxs)(z.Z,{className:"mb16",children:[(0,E.jsx)(B.Z,{span:6,children:(0,E.jsx)("div",{className:"ta-r",children:(0,E.jsxs)("strong",{children:[t("\u5f00\u59cb\u65f6\u95f4"),"\uff1a"]})})}),(0,E.jsx)(B.Z,{span:18,children:n.startTime})]}),(0,E.jsxs)(z.Z,{className:"mb16",children:[(0,E.jsx)(B.Z,{span:6,children:(0,E.jsx)("div",{className:"ta-r",children:(0,E.jsxs)("strong",{children:[t("\u8fd0\u884c\u65f6\u957f"),"\uff1a"]})})}),(0,E.jsx)(B.Z,{span:18,children:n.duration})]}),(0,E.jsxs)(z.Z,{className:"mb16",children:[(0,E.jsx)(B.Z,{span:6,children:(0,E.jsx)("div",{className:"ta-r",children:(0,E.jsxs)("strong",{children:[t("\u72b6\u6001"),"\uff1a"]})})}),(0,E.jsx)(B.Z,{span:18,children:n.status})]}),(0,E.jsxs)(z.Z,{className:"mb16",children:[(0,E.jsx)(B.Z,{span:6,children:(0,E.jsx)("div",{className:"ta-r",children:(0,E.jsxs)("strong",{children:[t("\u5b50\u4efb\u52a1\u5185\u5bb9"),"\uff1a"]})})}),(0,E.jsx)(B.Z,{span:18,children:n.content})]}),(0,E.jsxs)(z.Z,{className:"mb16",children:[(0,E.jsx)(B.Z,{span:6,children:(0,E.jsx)("div",{className:"ta-r",children:(0,E.jsxs)("strong",{children:[t("\u4efb\u52a1\u4fe1\u606f"),"\uff1a"]})})}),(0,E.jsx)(B.Z,{span:18,children:n.message})]})]}),onOk:function(){}})},children:t("\u8be6\u60c5")}),"end"!==n.step||"success"!==n.status?(0,E.jsx)("span",{className:"link mr16",onClick:function(){d.Z.confirm({title:t("\u7ec8\u6b62\u4efb\u52a1"),icon:(0,E.jsx)(L.Z,{}),content:"",okText:t("\u786e\u8ba4"),cancelText:t("\u53d6\u6d88"),onOk:function(){return new Promise((function(e,t){b(n.reqId).then((function(n){e("")})).catch((function(e){t()}))})).then((function(e){u.ZP.success(t("\u7ec8\u6b62\u6210\u529f"))})).catch((function(){u.ZP.error(t("\u7ec8\u6b62\u5931\u8d25"))}))},onCancel:function(){}})},children:t("\u7ec8\u6b62")}):null,n.log?(0,E.jsx)("span",{className:"link mr16",onClick:function(){window.open(n.log,"bank")},children:t("\u65e5\u5fd7")}):null,"end"===n.step&&"success"===n.status?(0,E.jsx)("span",{className:"link mr16",onClick:function(){S(!0),j(n.reqId).then((function(e){v(!0);var n=e.data.result;K(n);var a=K(n);d.Z.info({title:t("\u7ed3\u679c\u67e5\u770b"),content:(0,E.jsx)("div",{children:(0,E.jsx)(G.Z,{size:"small",loading:!1,cancelExportData:!0,rowKey:function(e){return JSON.stringify(e)},columns:a.config,pagination:!1,dataSource:a.data,scroll:{x:"auto"}})}),onOk:function(){}})})).catch((function(e){})).finally((function(){S(!1)}))},children:t("\u7ed3\u679c")}):null,"end"===n.step&&"success"===n.status?(0,E.jsx)("span",{className:"link",onClick:function(){var e;e=n.reqId,X.current=e,M(e),f(!0)},children:t("\u4e0b\u8f7d")}):null]})}}],pagination:!1,dataSource:[{content:n.content,database:n.database||"-",table:n.table||"-",startime:n.startTime,duration:n.duration||"-",status:n.status}]})]},"task_".concat(a))}))})]})}var K=t(43077),W=t.n(K),J=t(16548),$=t(27305),Q=t(60685),ee=t(8230),ne=t(75262),te=p.forwardRef((function(e,n){var t=(0,V.$G)(),r=t.t,i=(t.i18n,ee.Z.useForm()),c=(0,s.Z)(i,1)[0],o=(0,p.useState)();(0,s.Z)(o,2)[1];(0,p.useImperativeHandle)(n,(function(){return{onSubmit:function(){return new Promise((function(e,n){c.validateFields().then((function(n){e(n)})).catch((function(e){n(e)}))}))},setData:function(e){c.setFieldsValue(e)}}})),(0,p.useEffect)((function(){e.dataValue&&c.setFieldsValue(e.dataValue)}),[e.option]);var l=function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};switch(n.type){case"input":return(0,E.jsx)("div",{});case"select":return function(n,t){var s=n.value||[];return(0,E.jsx)(ee.Z.Item,(0,a.Z)((0,a.Z)({label:n.label,name:n.id,rules:[{required:!0,message:"".concat(r("\u8bf7\u9009\u62e9")).concat(n.label)}],initialValue:n.defaultValue,style:{marginBottom:0,marginRight:16}},t),{},{children:(0,E.jsx)(ne.Z,{style:{width:200},mode:n.multiple?"multiple":void 0,showSearch:!0,disabled:n.disable,optionFilterProp:"label",placeholder:n.placeHolder||"".concat(r("\u8bf7\u9009\u62e9")," ").concat(n.label),options:s,onChange:function(n,t){if(t.relate){for(var a=t.relate.relateId,r=t.relate.value,s=e.option,i=0;i<s.length;i++){var o=s[i];o.id===a&&(o.value=r)}e.onConfigChange&&e.onConfigChange(s)}e.onChange&&e.onChange(c.getFieldsValue())}})}),"configFormData_".concat(n.id))}(n,t);case"input-select":return function(n,t){var s=(n.value||[]).map((function(e){return e.value}));return(0,E.jsx)(ee.Z.Item,(0,a.Z)((0,a.Z)({label:n.label,name:n.id,rules:[{required:!0,message:"".concat(r("\u8bf7\u9009\u62e9")).concat(n.label)}],initialValue:n.defaultValue,style:{marginBottom:0}},t),{},{children:(0,E.jsx)(I.Z,{isOpenSearchMatch:!0,onChange:function(){e.onChange&&e.onChange(c.getFieldsValue())},options:s,width:"500px"})}),"configFormData_".concat(n.id))}(n,t);default:return null}};return(0,E.jsx)("div",{className:"configformdata-container d-f ac",children:(0,E.jsxs)(ee.Z,{form:c,component:!1,children:[console.log("props.option",e.option),e.option.map((function(e){return l(e)}))]})})})),ae=te,re=(Q.Z.get("myapp_username"),function(){return Math.random().toString(36).substring(2)}),se={test:"CREATE TABLE dbo.EmployeePhoto\n    (\n        EmployeeId INT NOT NULL PRIMARY KEY,\n        Photo VARBINARY(MAX) FILESTREAM NULL,\n        MyRowGuidColumn UNIQUEIDENTIFIER NOT NULL ROWGUIDCOL\n                        UNIQUE DEFAULT NEWID()\n    );\n    \n    GO\n    \n    /*\n    text_of_comment\n    /* nested comment */\n    */\n    \n    -- line comment\n    \n    CREATE NONCLUSTERED INDEX IX_WorkOrder_ProductID\n        ON Production.WorkOrder(ProductID)\n        WITH (FILLFACTOR = 80,\n            PAD_INDEX = ON,\n            DROP_EXISTING = ON);\n    GO\n    \n    WHILE (SELECT AVG(ListPrice) FROM Production.Product) < $300\n    BEGIN\n       UPDATE Production.Product\n          SET ListPrice = ListPrice * 2\n       SELECT MAX(ListPrice) FROM Production.Product\n       IF (SELECT MAX(ListPrice) FROM Production.Product) > $500\n          BREAK\n       ELSE\n          CONTINUE\n    END\n    PRINT 'Too much for the market to bear';\n    \n    MERGE INTO Sales.SalesReason AS [Target]\n    USING (VALUES ('Recommendation','Other'), ('Review', 'Marketing'), ('Internet', 'Promotion'))\n           AS [Source] ([NewName], NewReasonType)\n    ON [Target].[Name] = [Source].[NewName]\n    WHEN MATCHED\n    THEN UPDATE SET ReasonType = [Source].NewReasonType\n    WHEN NOT MATCHED BY TARGET\n    THEN INSERT ([Name], ReasonType) VALUES ([NewName], NewReasonType)\n    OUTPUT $action INTO @SummaryOfChanges;\n    \n    SELECT ProductID, OrderQty, SUM(LineTotal) AS Total\n    FROM Sales.SalesOrderDetail\n    WHERE UnitPrice < $5.00\n    GROUP BY ProductID, OrderQty\n    ORDER BY ProductID, OrderQty\n    OPTION (HASH GROUP, FAST 10);    \n"};function ie(){var e=(0,V.$G)(),n=e.t,t=(e.i18n,{tabId:re(),title:"".concat(n("\u65b0\u67e5\u8be2")," 1"),status:"init",smartShow:!1,smartContent:"",smartCache:"",smartTimer:void 0,loading:!1,taskMap:{}}),x=JSON.parse(localStorage.getItem("dataSearch2")||JSON.stringify((0,i.Z)({},t.tabId,t))),Z=Object.entries(x).reduce((function(e,n){var t=(0,s.Z)(n,2),i=(t[0],t[1]);return[].concat((0,r.Z)(e),[(0,a.Z)({},i)])}),[]),j=(0,p.useState)(Z[0].tabId),b=(0,s.Z)(j,2),S=b[0],y=b[1],k=(0,p.useRef)(S),C=function(e){k.current=e,y(e)},T=(0,p.useState)(x),P=(0,s.Z)(T,2),R=P[0],M=P[1],D=(0,p.useRef)(R),A=function(e){D.current=e,M(e)},L=(0,p.useState)([]),H=(0,s.Z)(L,2),F=H[0],U=H[1],_=(0,p.useRef)(F),z=function(e){_.current=e,U(e)},B=Object.entries(R).reduce((function(e,n){var t=(0,s.Z)(n,2),i=(t[0],t[1]);return[].concat((0,r.Z)(e),[(0,a.Z)({},i)])}),[]),G=(0,p.useState)(B),Y=(0,s.Z)(G,2),q=Y[0],K=Y[1],Q=(0,p.useRef)(Z.length),ee=(0,p.useState)([]),ne=(0,s.Z)(ee,2),te=(ne[0],ne[1]),ie=(0,p.useState)([]),ce=(0,s.Z)(ie,2),oe=(ce[0],ce[1]),le=(0,p.useRef)(null),ue=function(e,n){var t=(0,a.Z)({},D.current),r={};e.taskMap&&(r={taskMap:(0,a.Z)((0,a.Z)({},D.current[S].taskMap),e.taskMap)}),t[n||S]=(0,a.Z)((0,a.Z)((0,a.Z)({},t[n||S]),e),r),localStorage.setItem("dataSearch2",JSON.stringify(t)),D.current=t,A(t)};(0,p.useEffect)((function(){N().then((function(e){var n=e.data.result;z(n)}))}),[]),(0,p.useEffect)((function(){var e,n=document.getElementById("buttonDrag");n&&((e=n).onmousedown=function(n){var t="",a=n.clientX,r=n.clientY,s=e.offsetWidth,i=e.offsetHeight,c=e.offsetLeft,o=e.offsetTop;return a>c+s-30?t="right":a<c+30&&(t="left"),r>o+i-30?t="down":r<o+30&&(t="top"),document.onmousemove=function(n){switch(t){case"right":e.style.width=s+(n.clientX-a)+"px";break;case"left":e.style.width=s-(n.clientX-a)+"px",e.style.left=c+(n.clientX-a)+"px";break;case"top":e.style.height=i-(n.clientY-r)+"px",e.style.top=o+(n.clientY-r)+"px";break;case"down":e.style.height=i+(n.clientY-r)+"px"}},e.onmouseup=function(){document.onmousemove=null},!1})}),[]);var de=function(e){Object.entries(D.current[e]).reduce((function(e,n){var t=(0,s.Z)(n,2),i=(t[0],t[1]);return[].concat((0,r.Z)(e),[(0,a.Z)({},i)])}),[]).forEach((function(e){clearInterval(e.smartTimer)})),Object.entries(D.current[e].taskMap).reduce((function(e,n){var t=(0,s.Z)(n,2),i=(t[0],t[1]);return[].concat((0,r.Z)(e),[(0,a.Z)({},i)])}),[]).forEach((function(e){clearInterval(e.timer)}))},fe=function(e,n){var t=D.current[e].taskMap[n];t&&clearInterval(t.timer)};(0,p.useEffect)((function(){return ue({loading:!1}),function(){Object.entries(R).forEach((function(e){var n=(0,s.Z)(e,1)[0];de(n)}))}}),[]),(0,p.useEffect)((function(){var e=R[S];Object.entries(e.taskMap).reduce((function(e,n){var t=(0,s.Z)(n,2),i=(t[0],t[1]);return[].concat((0,r.Z)(e),[(0,a.Z)({},i)])}),[]).forEach((function(e){"running"===e.status&&me(e.reqId)}))}),[S]);var he=function(e){g(e).then((function(n){var t=n.data,r=t.state,s=t.result,c=t.err_msg,o=t.result_url,l=t.spark_log_url,u=t.stage,d=(0,a.Z)((0,a.Z)({},D.current[S].taskMap[e]),{},{status:r,step:u,log:l,downloadUrl:o,result:s,message:c});if("success"===r||"failure"===r){var f=new Date(d.startTime||"").valueOf(),h=(new Date).valueOf(),m=(0,J.Xj)((h-f)/1e3);d.duration=m,ue({status:"success",taskMap:(0,i.Z)({},e,d)}),fe(S,e)}else ue({status:"success",taskMap:(0,i.Z)({},e,d)})})).catch((function(){fe(S,e),u.ZP.error(n("\u67e5\u8be2\u7ed3\u679c\u5931\u8d25\uff0c\u5c1d\u8bd5\u91cd\u65b0\u8fd0\u884c")),ue({status:"failure",taskMap:(0,i.Z)({},e,(0,a.Z)((0,a.Z)({},D.current[S].taskMap[e]),{},{status:"failure",step:"end"}))})}))},me=function(e){fe(S,e);var t=setInterval((function(){he(e)}),5e3);ue({taskMap:(0,i.Z)({},e,{reqId:e,status:"init",content:R[S].content,name:"".concat(n("\u4efb\u52a1")).concat(e),step:"start",startTime:W()().format("YYYY-MM-DD HH:mm:ss"),database:R[S].database,table:R[S].table,timer:t,message:""})}),he(e)};return(0,E.jsx)("div",{className:"datasearch-container fade-in d-f",children:(0,E.jsx)("div",{className:"flex1 ptb16 pl16",children:(0,E.jsx)(f.Z,{type:"editable-card",onChange:function(e){Object.entries(R).forEach((function(n){var t=(0,s.Z)(n,1)[0];t!==e&&de(t)})),te([]),oe([]),C(e)},activeKey:S,onEdit:function(e,t){"add"===t?function(){de(S);var e=++Q.current;if(e>10)u.ZP.warn(n("\u6807\u7b7e\u6570\u76ee\u8fbe\u5230\u9650\u5236"));else{var t=re(),s="".concat(n("\u65b0\u67e5\u8be2")," ").concat(e),c=(0,r.Z)(q),o={title:s,tabId:t,status:"init",smartShow:!1,smartContent:"",smartTimer:void 0,smartCache:"",loading:!1,taskMap:{}};c.push(o),K(c),C(t);var l=(0,a.Z)((0,a.Z)({},R),{},(0,i.Z)({},t,o));A(l),localStorage.setItem("dataSearch2",JSON.stringify(l))}}():function(e){var n=S,t=-1;q.forEach((function(n,a){n.tabId===e&&(t=a-1)}));var r=q.filter((function(n){return n.tabId!==e}));r.length&&n===e&&(n=t>=0?r[t].tabId:r[0].tabId),K(r),C(n);var s=(0,a.Z)({},R);delete s[e],A(s),localStorage.setItem("dataSearch2",JSON.stringify(s))}(e)},children:q.map((function(e,t){var r,s;return(0,E.jsx)(f.Z.TabPane,{tab:"".concat(n("\u65b0\u67e5\u8be2")," ").concat(t+1),closable:0!==t,children:(0,E.jsxs)("div",{className:"d-f fd-c h100",children:[(0,E.jsxs)("div",{className:"flex2 s0 ov-a",children:[null!==(r=R[S])&&void 0!==r&&r.loading?(0,E.jsx)("div",{className:"codeedit-mark",children:(0,E.jsxs)("div",{className:"d-f jc ac fd-c",children:[(0,E.jsx)($.Z,{}),(0,E.jsx)("div",{children:n("\u7ed3\u679c\u751f\u6210\u4e2d")})]})}):null,(0,E.jsx)(w,{value:null===(s=R[S])||void 0===s?void 0:s.content,onChange:function(n){ue({content:""===n?void 0:n,title:e.title})}})]}),(0,E.jsxs)("div",{className:"ov-h",id:"showBox",style:{height:500},children:[(0,E.jsx)(O(),{axis:"y",onStart:function(){},onDrag:function(e){var n=document.getElementById("showBox");if(n){var t=document.body.clientHeight-e.y;n.style.height="".concat(t,"px")}},onStop:function(){},children:(0,E.jsx)("div",{className:"ta-c",style:{cursor:"ns-resize"},children:(0,E.jsx)(o.Z,{})})}),(0,E.jsxs)("div",{className:"ptb8 plr16 bor-l bor-r b-side d-f ac jc-b bg-w",children:[(0,E.jsxs)("div",{className:"d-f ac",children:[(0,E.jsx)(h.Z,{className:"mr8",checked:R[S].smartShow,unCheckedChildren:n("\u6b63\u5e38\u6a21\u5f0f"),checkedChildren:n("\u667a\u80fd\u6a21\u5f0f"),onChange:function(e){ue({smartShow:e})}}),R[S].smartShow?(0,E.jsx)(I.Z,{value:R[S].smartContent,isOpenSearchMatch:!0,onChange:function(e){ue({smartContent:e})},onSearch:function(e){ue({smartCache:se[e],loading:!0});var n=setInterval((function(){var e=D.current[S].content||"";if(D.current[S].smartCache){var n=D.current[S].smartCache||"",t=n.substr(0,20);n=n.replace(t,""),ue({smartCache:n,content:e+t})}else clearInterval(D.current[S].smartTimer),ue({smartCache:"",smartTimer:void 0,loading:!1})}),800);ue({smartTimer:n})},options:["test"],placeholder:n("AI\u667a\u80fd\u751f\u6210"),width:"240px"}):null]}),(0,E.jsxs)("div",{className:"d-f ac",children:[(0,E.jsx)(ae,{ref:le,dataValue:R[S],onChange:function(e){ue(e)},onConfigChange:function(e){z(e),ue({database:"db"})},option:_.current}),(0,E.jsxs)(m.Z,{className:"ml16",type:"primary",loading:"running"===R[S].status,onClick:function(){le.current.onSubmit().then((function(e){!function(){var e;ue({status:"running"});var t=F.map((function(e){return e.id})).reduce((function(e,n){return(0,a.Z)((0,a.Z)({},e),{},(0,i.Z)({},n,R[S][n]))}),{});v((0,a.Z)({sql:(null===(e=R[S])||void 0===e?void 0:e.content)||""},t)).then((function(e){var t=e.data,a=t.err_msg,r=t.task_id;a?(ue({status:"failure"}),d.Z.error({title:n("\u8fd0\u884c\u5931\u8d25"),icon:(0,E.jsx)(c.Z,{}),width:1e3,content:a,okText:n("\u5173\u95ed")})):r&&me(r)})).catch((function(e){ue({status:"failure"})}))}()}))},children:[n("\u8fd0\u884c"),(0,E.jsx)(l.Z,{})]})]})]}),(0,E.jsxs)("div",{className:"flex1 bor b-side s0 bg-w p-r ov-a",style:{height:"calc(100% - 80px)"},children:[(0,E.jsx)("div",{className:"pt8",children:(0,E.jsx)("div",{className:"tag-result bg-theme c-text-w mr16",children:n("\u7ed3\u679c")})}),(0,E.jsx)("div",{className:"plr16 pt8",children:(0,E.jsx)(X,{option:R[S].taskMap,onDelete:function(e){d.Z.confirm({title:n("\u5220\u9664"),icon:(0,E.jsx)(c.Z,{}),content:"".concat(n("\u786e\u5b9a\u5220\u9664"),"?"),okText:n("\u786e\u8ba4\u5220\u9664"),cancelText:n("\u53d6\u6d88"),okButtonProps:{danger:!0},onOk:function(){var n=R[S].taskMap;fe(S,e),delete n[e],ue({taskMap:n})},onCancel:function(){}})},onRetry:function(e){me(e)}})})]})]})]})},e.tabId)}))})})})}}}]);
//# sourceMappingURL=204.aa9db224.chunk.js.map