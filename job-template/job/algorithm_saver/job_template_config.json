{"algorithm_saver": {"project_name": "算法管理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/algorithm_saver:latest", "gitpath": "/job-template/job/algorithm_saver", "job_template_name": "algorithm_saver", "job_template_describe": "算法保存算子 - 将训练后的模型保存回算法仓并生成新版本", "job_template_workdir": "/app", "job_template_command": "python algorithm_saver.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 10, "rec_job_template": "算法管理", "help_url": "/job-template/job/algorithm_saver"}, "job_template_args": {"算法信息": {"--algo_id": {"type": "str", "item_type": "str", "label": "算法ID", "require": 1, "choice": [], "range": "", "default": "", "placeholder": "algorithm_123", "describe": "原始算法的ID，用于标识算法来源", "editable": 1}, "--algo_version": {"type": "str", "item_type": "str", "label": "算法版本", "require": 1, "choice": [], "range": "", "default": "v1.0", "placeholder": "v1.0", "describe": "原始算法的版本号", "editable": 1}, "--model_path": {"type": "str", "item_type": "str", "label": "模型路径", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/models/trained_model.pt", "placeholder": "/mnt/admin/models/model.pt", "describe": "训练后的模型文件路径，支持本地路径或URL", "editable": 1}}, "框架配置": {"--framework": {"type": "str", "item_type": "str", "label": "训练框架", "require": 0, "choice": ["pytorch", "tensorflow", "sklearn", "xgboost", "lightgbm", "custom"], "range": "", "default": "pytorch", "placeholder": "pytorch", "describe": "模型训练使用的框架", "editable": 1}, "--inference_framework": {"type": "str", "item_type": "str", "label": "推理框架", "require": 0, "choice": ["pytorch", "tensorflow", "onnx", "tensorrt", "triton", "custom"], "range": "", "default": "custom", "placeholder": "custom", "describe": "模型推理使用的框架", "editable": 1}}, "模型信息": {"--description": {"type": "str", "item_type": "str", "label": "模型描述", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "基于深度学习训练的分类模型", "describe": "模型的详细描述信息", "editable": 1}, "--model_metrics": {"type": "str", "item_type": "str", "label": "模型指标", "require": 0, "choice": [], "range": "", "default": "{}", "placeholder": "{\"accuracy\": 0.95, \"loss\": 0.05}", "describe": "模型评估指标，JSON格式", "editable": 1}}, "ClearML集成": {"--clearml_project_name": {"type": "str", "item_type": "str", "label": "ClearML项目名", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "my_training_project", "describe": "ClearML中的项目名称，用于获取训练任务信息", "editable": 1}, "--clearml_task_id": {"type": "str", "item_type": "str", "label": "ClearML任务ID", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "task_abc123def456", "describe": "指定的ClearML任务ID，优先级高于最佳任务选择", "editable": 1}, "--clearml_metric_name": {"type": "str", "item_type": "str", "label": "评估指标名", "require": 0, "choice": ["accuracy", "loss", "f1_score", "precision", "recall", "auc"], "range": "", "default": "accuracy", "placeholder": "accuracy", "describe": "用于选择最佳任务的评估指标名称", "editable": 1}, "--use_best_task": {"type": "bool", "item_type": "bool", "label": "使用最佳任务", "require": 0, "choice": [], "range": "", "default": false, "placeholder": "", "describe": "是否自动选择ClearML项目中性能最佳的任务", "editable": 1}}}}}