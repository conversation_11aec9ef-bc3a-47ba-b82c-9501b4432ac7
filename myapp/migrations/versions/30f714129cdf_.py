"""empty message

Revision ID: 30f714129cdf
Revises: d327fd496e47
Create Date: 2022-08-02 07:38:39.893848

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '30f714129cdf'
down_revision = 'd327fd496e47'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dimension',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('sqllchemy_uri', sa.String(length=255), nullable=True),
    sa.Column('table_name', sa.String(length=255), nullable=True),
    sa.Column('label', sa.String(length=255), nullable=True),
    sa.Column('describe', sa.String(length=2000), nullable=True),
    sa.Column('app', sa.String(length=255), nullable=True),
    sa.Column('owner', sa.String(length=2000), nullable=True),
    sa.Column('columns', sa.Text(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('table_name')
    )
    op.create_table('metadata_table',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('node_id', sa.String(length=200), nullable=True),
    sa.Column('app', sa.String(length=200), nullable=True),
    sa.Column('c_org_fullname', sa.String(length=255), nullable=True),
    sa.Column('db', sa.String(length=200), nullable=True),
    sa.Column('table', sa.String(length=400), nullable=True),
    sa.Column('metadata_column', sa.Text(), nullable=True),
    sa.Column('describe', sa.String(length=200), nullable=True),
    sa.Column('owner', sa.String(length=200), nullable=True),
    sa.Column('lifecycle', sa.Integer(), nullable=True),
    sa.Column('rec_lifecycle', sa.Integer(), nullable=True),
    sa.Column('storage_size', sa.String(length=200), nullable=True),
    sa.Column('storage_cost', sa.Float(), nullable=True),
    sa.Column('visits_seven', sa.Integer(), nullable=True),
    sa.Column('visits_thirty', sa.BigInteger(), nullable=True),
    sa.Column('visits_sixty', sa.BigInteger(), nullable=True),
    sa.Column('recent_visit', sa.Date(), nullable=True),
    sa.Column('partition_start', sa.String(length=255), nullable=True),
    sa.Column('partition_end', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=255), nullable=True),
    sa.Column('creator', sa.String(length=255), nullable=True),
    sa.Column('create_table_ddl', sa.Text(), nullable=True),
    sa.Column('col_info', sa.Text(), nullable=True),
    sa.Column('partition_update_mode', sa.Integer(), nullable=True),
    sa.Column('is_privilege', sa.Integer(), nullable=False),
    sa.Column('data_source', sa.Integer(), nullable=True),
    sa.Column('field', sa.String(length=200), nullable=True),
    sa.Column('security_level', sa.String(length=200), nullable=True),
    sa.Column('value_score', sa.String(length=200), nullable=True),
    sa.Column('warehouse_level', sa.String(length=200), nullable=True),
    sa.Column('ttl', sa.String(length=200), nullable=True),
    sa.Column('expand', sa.Text(length=65536), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dataset',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=True),
    sa.Column('label', sa.String(length=200), nullable=True),
    sa.Column('describe', sa.String(length=2000), nullable=True),
    sa.Column('source_type', sa.String(length=200), nullable=True),
    sa.Column('source', sa.String(length=200), nullable=True),
    sa.Column('industry', sa.String(length=200), nullable=True),
    sa.Column('icon', sa.String(length=2000), nullable=True),
    sa.Column('field', sa.String(length=200), nullable=True),
    sa.Column('usage', sa.String(length=200), nullable=True),
    sa.Column('research', sa.String(length=200), nullable=True),
    sa.Column('storage_class', sa.String(length=200), nullable=True),
    sa.Column('file_type', sa.String(length=200), nullable=True),
    sa.Column('status', sa.String(length=200), nullable=True),
    sa.Column('years', sa.String(length=200), nullable=True),
    sa.Column('url', sa.String(length=1000), nullable=True),
    sa.Column('path', sa.String(length=400), nullable=True),
    sa.Column('download_url', sa.String(length=1000), nullable=True),
    sa.Column('storage_size', sa.String(length=200), nullable=True),
    sa.Column('entries_num', sa.String(length=200), nullable=True),
    sa.Column('duration', sa.String(length=200), nullable=True),
    sa.Column('price', sa.String(length=200), nullable=True),
    sa.Column('owner', sa.String(length=200), nullable=True),
    sa.Column('expand', sa.Text(length=65536), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('metadata_metric',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('app', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=300), nullable=True),
    sa.Column('label', sa.String(length=300), nullable=True),
    sa.Column('describe', sa.String(length=500), nullable=False),
    sa.Column('caliber', sa.Text(length=65536), nullable=True),
    sa.Column('metric_type', sa.String(length=100), nullable=True),
    sa.Column('metric_level', sa.String(length=100), nullable=True),
    sa.Column('metric_dim', sa.String(length=100), nullable=True),
    sa.Column('metric_data_type', sa.String(length=100), nullable=True),
    sa.Column('metric_responsible', sa.String(length=200), nullable=True),
    sa.Column('status', sa.String(length=100), nullable=True),
    sa.Column('task_id', sa.String(length=200), nullable=True),
    sa.Column('public', sa.Boolean(), nullable=True),
    sa.Column('expand', sa.Text(length=65536), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('etl_pipeline',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('describe', sa.String(length=200), nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('dag_json', sa.Text(length=65536), nullable=True),
    sa.Column('config', sa.Text(length=65536), nullable=True),
    sa.Column('expand', sa.Text(length=65536), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('model',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('version', sa.String(length=100), nullable=True),
    sa.Column('describe', sa.String(length=1000), nullable=True),
    sa.Column('path', sa.String(length=200), nullable=True),
    sa.Column('download_url', sa.String(length=200), nullable=True),
    sa.Column('project_id', sa.Integer(), nullable=True),
    sa.Column('pipeline_id', sa.Integer(), nullable=True),
    sa.Column('run_id', sa.String(length=100), nullable=False),
    sa.Column('run_time', sa.String(length=100), nullable=True),
    sa.Column('framework', sa.String(length=100), nullable=True),
    sa.Column('metrics', sa.Text(), nullable=True),
    sa.Column('md5', sa.String(length=200), nullable=True),
    sa.Column('api_type', sa.String(length=100), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('etl_task',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('describe', sa.String(length=200), nullable=False),
    sa.Column('etl_pipeline_id', sa.Integer(), nullable=False),
    sa.Column('template', sa.String(length=100), nullable=False),
    sa.Column('task_args', sa.Text(length=65536), nullable=True),
    sa.Column('etl_task_id', sa.String(length=100), nullable=False),
    sa.Column('expand', sa.Text(length=65536), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['etl_pipeline_id'], ['etl_pipeline.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.alter_column('images', 'describe',
               existing_type=mysql.TEXT(),
               nullable=False)
    op.add_column('inferenceservice', sa.Column('inference_config', sa.Text(length=65536), nullable=True))
    op.add_column('inferenceservice', sa.Column('priority', sa.Integer(), nullable=True))
    op.alter_column('job_template', 'describe',
               existing_type=mysql.TEXT(),
               nullable=False)
    op.add_column('nni', sa.Column('job_worker_command', sa.String(length=200), nullable=True))
    op.add_column('nni', sa.Column('job_worker_image', sa.String(length=200), nullable=True))
    op.alter_column('nni', 'job_type',
               existing_type=mysql.ENUM('Job'),
               nullable=True)
    op.alter_column('nni', 'namespace',
               existing_type=mysql.VARCHAR(length=200),
               nullable=True)
    op.alter_column('project', 'describe',
               existing_type=mysql.TEXT(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('project', 'describe',
               existing_type=mysql.TEXT(),
               nullable=True)
    op.alter_column('nni', 'namespace',
               existing_type=mysql.VARCHAR(length=200),
               nullable=False)
    op.alter_column('nni', 'job_type',
               existing_type=mysql.ENUM('Job'),
               nullable=False)
    op.drop_column('nni', 'job_worker_image')
    op.drop_column('nni', 'job_worker_command')
    op.alter_column('job_template', 'describe',
               existing_type=mysql.TEXT(),
               nullable=True)
    op.drop_column('inferenceservice', 'priority')
    op.drop_column('inferenceservice', 'inference_config')
    op.alter_column('images', 'describe',
               existing_type=mysql.TEXT(),
               nullable=True)
    op.drop_table('etl_task')
    op.drop_table('model')
    op.drop_table('etl_pipeline')
    op.drop_table('metadata_metric')
    op.drop_table('dataset')
    op.drop_table('metadata_table')
    op.drop_table('dimension')
    # ### end Alembic commands ###
