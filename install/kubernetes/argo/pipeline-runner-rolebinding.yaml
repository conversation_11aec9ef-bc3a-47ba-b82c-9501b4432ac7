---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: pipeline-runner
  namespace: pipeline
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: pipeline-runner-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: pipeline-runner
subjects:
- kind: ServiceAccount
  name: pipeline-runner
  namespace: pipeline


---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: pipeline-runner
  name: pipeline-runner
rules:
  - apiGroups: ["*"]
    resources: ["*"]
    verbs: ["*"]

