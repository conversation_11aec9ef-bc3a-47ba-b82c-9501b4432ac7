import os
import json

json_path = f'/mnt/{os.environ.get("USERNAME", "admin")}/{os.environ.get("KFJ_RUN_ID")}/info.json'

def _ensure_json_file():
    """确保JSON文件存在，如果不存在则创建"""
    os.makedirs(os.path.dirname(json_path), exist_ok=True)
    if not os.path.exists(json_path):
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False, indent=2)

def _load_json():
    """加载JSON文件"""
    _ensure_json_file()
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}

def _save_json(data):
    """保存数据到JSON文件"""
    _ensure_json_file()
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def _get_nested_value(data, key_path):
    """获取嵌套键的值"""
    keys = key_path.split('.')
    current = data
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return None
    return current

def _set_nested_value(data, key_path, value):
    """设置嵌套键的值"""
    keys = key_path.split('.')
    current = data
    for key in keys[:-1]:
        if key not in current or not isinstance(current[key], dict):
            current[key] = {}
        current = current[key]
    current[keys[-1]] = value

# 从全局的json中获取值
def get_from_json(key: str, default_value=None):
    """
    从JSON文件中获取值
    
    Args:
        key: 键名，支持点号分隔的嵌套键，如 'user.name'
        default_value: 如果键不存在时返回的默认值
    
    Returns:
        键对应的值，如果不存在则返回默认值
    """
    data = _load_json()
    if "." in key:
        result = _get_nested_value(data, key)
        return result if result is not None else default_value
    else:
        return data.get(key, default_value)

def set_json_value(key: str, value):
    """
    设置JSON文件中的值
    
    Args:
        key: 键名，支持点号分隔的嵌套键，如 'user.name'
        value: 要设置的值
    """
    data = _load_json()
    if "." in key:
        _set_nested_value(data, key, value)
    else:
        data[key] = value
    _save_json(data)