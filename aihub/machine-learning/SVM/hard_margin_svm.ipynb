{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 生成模拟数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXwAAAD8CAYAAAB0IB+mAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDMuMC4zLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvnQurowAAIABJREFUeJzsnXd4VNXWxt89vaRRQkILLXSIlCAdBQQBKQKCIChiodj5LKgI9yo2iuAFEakqRelIlyJgQIoQeqih9wQSSJvJtP39sUmZzJlkUqYkWT+feWTOnLP3mhHX2Wfttd7FOOcgCIIgSj4ybxtAEARBeAZy+ARBEKUEcvgEQRClBHL4BEEQpQRy+ARBEKUEcvgEQRClBHL4BEEQpQRy+ARBEKUEcvgEQRClBIW3DchO+fLlefXq1b1tBkEQRLEiOjr6Huc8OK/zfMrhV69eHYcPH/a2GQRBEMUKxthVV86jkA5BEEQpgRw+QRBEKYEcPkEQRCmBHD5BEEQpgRw+QRBEKYEcPkEQHoNzju8PfI/QqaGQfyFHox8bYdvFbd42q9RADp8gCI8xMWoixu0ch7upd2HjNsTEx6Dvsr7Yc3WPt00rFZDDJwjCI6Rb0jFl3xSkmdPsjqdZ0jB+13gvWVW6IIdPEIRHiE+Lh81mk/zszL0zHramdEIOnyAIjxCsC4ZMJu1y6pev72Fr3EuiIRFvb34boVNDUWVaFYzfOR4Gs8HbZpHDJwjCM6gVanzQ5gPolDq741qFFl90/MJLVhU9JqsJLee3xNwjc3E39S5uJt/E1P1T0W1pN3DOvWobOXyCIDzGhA4T8GXHL1FBXwEMDA3KN8Da59eiQ7UO3jatyFh9ejVup9yGyWrKPGa0GBF9Kxr7b+z3omU+Jp5GEETJhjGGMa3HYEzrMd42xW0cuHkAKaYUh+MWmwXRt6LRpmobL1gloBU+QRBEEVK7bG2HsBUAqOQqVA+q7nmDskEOnyAIoggZGjEUKrnK7picyVFGWwbda3f3klUCcvgEQRBFSJAmCHuG70HT0KZQypRQypRoF9YOe4fvhULm3Sg6xfAJgvBJzsSfwcSoiTh06xDqlKuDz9p/htZVW3vbLJdoVKERjow8gkRDIuQyOQLUAd42CQA5fIIo9lhtVvx28jcsOLoAnHO83ORlvPjYi15fTRaGY3eOof3C9kizpMHGbYhNiMXuK7ux/Lnl6Fmnp7fNc5ky2jLeNsEOt/2NYIxNAdALgAnARQDDOecP3DUfQZRGOOcYtGoQtsRuQao5FQAQfTsaq8+sxobBG8AY87KFBePD7R8ixWyf6ZJmTsPbm9/GM7WfKbbfy9u4M4a/HUAjznkEgPMAPnHjXARRKjl06xA2x27OdPYAkGpOxe4ruxF1NcqLlhWOgzcOSh6/kXwDyaZkD1tTcnCbw+ecb+OcWx69PQCgirvmIojSyu4ru+0KfDJIM6dh5+WdXrCoaAjWBUseV8lVkimPxY341Hh8u/dbDFk9BP878D88ND70yLyeCvK9AmC51AeMsREARgBAWFiYh8whiJJBeV15qOVqWGwWu+MahQbBemmn6YtwzvHj4R/xZdSXiEuNQ7AuGGq5GunW9MxztAotXmv6mtf3Jk7ePYmVp1eCgWFAwwFoVKFRvq6PiYtB24VtkW5Nh9FixB/n/sDXe7/G4dcPo2pgVTdZLWCF0XZgjO0AECrx0TjO+bpH54wDEAmgH89jssjISH748OEC20MQpY2HxoeoOr2qQ5jDT+mHy+9dRnldeS9Zlj8m/zMZn//9uZ10skKmgAwyqBVqmG1mDG40GD/1/Mkhx92TfP7355i0d1LmU5VKrsK49uMwrsM4l8dos6ANDtw4AI4sdyhncvSt3xcrB6wskF2MsWjOeWSe57lTzIcxNgzAKACdOedpeZ1PDp8g8s/+6/vRd3nfTGepVqixasAqPFH9CS9b5hoWmwXlJpdDUnqSw2ctKrXAwj4LUcm/Espqy3rBuizO3juLZnOawWCxV73UKDQ4MeoEapernecYJqsJ2q+0sHFHmWi9Uo+UTx0lGVzBVYfvziydbgDGAnjCFWdPEETBaF21NW7+300cuX0ENm5DZKVIyGVyb5vlMgmGBMl9CACITYjNd8jEXaw7u84hdAYANpsN686twwdtPshzDBmTQc7kkg5frVAXiZ25zu/GsX8A4A9gO2PsGGPsJzfORRClGrlMjhaVW6BllZbFytkDQFltWadhmjrl6njYGsFD40Ocjj+NVFNW9pNcJpdMB2WMubyvoJAp0K9+P6hk9t9Xo9BgeJPhhTPaBdyZpRPOOa/KOW/y6DXKXXMRBFF8UcgU+KTdJ9Ar9XbHtQotvuz0ZeZ7G7dh95XdWBmzEreSb7nFFrPVjFEbRyF0aihazW+F4CnBGL9zPDjn6F+/P+TM8WbKGEO/+v1cnmP2M7PRsEJD+Kn84Kfyg06pQ+sqrTGx48Si/CqSFN9SPIIgSgxj246FXqnHV3u+QlxqHGqXq41pXafhqZpPARChnc6LOiPRkAgGhnRrOt5r9R6+6fwNbiXfwrE7x1AtqFqhwz+f/vUpFh9fDKPVCKPVCACYdmAaKvlXwugWozGt6zSM2TYGDGKlz8Exo/sMhAW6nmFYRlsG0SOiceDGAZy/fx4RIRFoWrFpoex2Fbdu2uYX2rQlCIJzbhc64Zyj/qz6OH//vF1mi06hQ7uwdoi6GpWZyRNRIQKbhmwq0Aav1WZF4LeBdkVsGVQLrIYr710BANxMuon159YDAPrU64NK/pXyPVdR4/VNW4IgiIKQM05+Ov40biTdsHP2AJBmScOOyztg47bM1fiR20cw7I9h2DB4Q77nzciLlyI+LT7zz5UDKmN0i9H5Ht8XIHlkgiB8mmRTstON6JzZLiabCdsubpNM8cwLrUKLaoHVJD9rUalFvsfzRcjhEwTh0zQNzV98W8Zkki0G84Ixhpk9ZtpJN8iYDDqlDlO7TnV5HBu3YWXMSvT+vTf6Le+H9efWe715eQbk8AmC8GnUCjXm9ZoHnVKXmSWjV+oRpAmCgjlGpUP0IajoV7FAc/Wo3QM7XtyBbuHdUCOoBvrV64eDrx1EZKU8w+MAstRLh68bjg3nN2Dt2bV4YfULGLFhRIHsKWpo05YgiGJBTFwMfjr8E24m30TPOj3xRLUn0HpBaySbkmG0GKFgCshlcvSu2xs1gmrghcYv4LHQxzxq495re9FtSTeHjV+dUocDrx5A45DGbpmXNm0JgihRNKzQEDN7zLQ7dubNM5h9eDZ2X9mNe2n3cO7+Oaw6vQqMMfzw7w8Y12EcPm3/qcds3HZxm50eUAYWqwXbL213m8N3FQrpEARRbCmnK4fPOnyGKV2m4ELCBRgtRnBw2LgNaZY0TIyaiEuJlzxmT5AmSLJqWClXIlAd6DE7nEEOnyCIYs+6c+skUyrTLen46bDnVF0GNRoEGXN0q4wx9G/Q32N2OIMcPkEQxR6VXCUpe8DBMePgDFxOvOwROyr5V8LKASvhr/JHgDoAAeoABGmCsH7QegRpgnDlwRVsOr8JF+5f8Ig9OaFNW4Igij2xCbFo9GMju4YpGSiYAiMjR+KHHj94zB6jxYg9V/dALpOjXVg7AMDQNUOx4fwGqOVqmKwmdKjWAWueX1MkHbxc3bSlFT5BEMWe8LLhGBUprc9o4RYcvX3Uo/ZoFBp0qdUFnWp0gkquwhd/f4GN5zfCaDHiYfpDGCwG/H31b7z353setYscPkGUcmzchmWnlqHLoi7o/GtnLDq+CFab1dtm5Zv3W78PtUxaU/5m8k2v9vidEz3HoXGK0WLEouOLJLXx3QU5fIIo5QxdMxSvrX8NOy7vwM4rO/HGpjfw7LJnfaY61FWqBlZF73q9oVVoHT67+vAqnl7yNF5d/yruptz1uG3OKn9NVpNHb67k8AmiFBN9Kxrrzq2zKxRKNadi15VdiLoa5TE7rjy4gqFrhiJkagjqzqyL2YdnF+iGs6TfErz1+FsODUYA0Upx4dGFqDKtCob9MQxmq9nu8xtJNzB2+1h0W9IN43eOx+3k2wX+Pjl5stqTmZLK2WlesTmUcmWRzZMX5PAJohSz68ouB8cHCKf/1+W/PGLDnZQ7aD63OX4/9TviUuNwPuE8Ptj2Ad798918j6WSqzC5y+RcC5ws3IKVMSsxYdeEzGPH7xxHg1kN8P3B77H14lZM2TcF9WfVx9l7Zwv0nXLyfbfvEaAOgFouQk5KmRJ+Kj/M7jm7SMZ3FXL4BOEFbiXfwrJTy7A1dqtkn1RP4ay9oEahQXldeY/Y8P2B75FiSrGLZaeZ0zDvyDzEpcYVaEyp1XR2DBYDfjz8Y+b7UZtGIdmUnNlbN92ajqT0pCLbVK1bvi5Ov3kaY1qNQafqnfDW42/h5OiTLmv0FBUkrUAQHmb8zvGYun8qlDLxKK9VarHjxR1eKbvvX7+/pFOTMzkGNRrkERv2XN0j2cRcLVfj5N2T6Fyzc77HfKL6Ezh8O/cU7+T0ZHAuqnL/vfGvw+ccHLuu7LI7djHhIib/MxmHbh1CowqN8HG7j9EguIFLNlXyr4RvnvrG9S/hBmiFTxAeZNvFbZh+YDqMFiOSTclINiUjLjUOPZb28Gi2RgaBmkBsGbIFwbpg+Kv84a/yR1ltWawbtA4V9BU8YkPtcrUli6ZMVhOqBUnr0+fFmy3ehCwP99asYjMwxiBjMqgU0k3Us+fIH79zHE3mNMHCowtx9M5R/HbyNzw+73Hsvba3QDZ6A3L4BOFBfjr8k2QLvYfpD/HvTcdVpidoG9YWt9+/ja1Dt2LLkC248/6dAq2qC8r7rd+HWmGfTqmWq9G6SmuElw13OP/qg6vYGrsVVx5ccTpmjTI1MKb1GMkbiQwy6JX6zEIsxhheeuwlaBQau/O0Ci1eb/Z65vv3tr6HFFMKLFyE4KzcilRzKt7Y9IbL39XbUEiHIDyIs05MjDGkmhxvBJ5CLpOjddXWXpm7cUhjrBm4BiM2jkBcahw45+hVpxcW9Flgd57JasKQ1UOw8cJGqOVqpFvT0T28O37v/7vDDQMApnSZgqdqPoVp+6fh8oPLUMqUUMqUaFqxKT5u9zHqla+Xee70p6fjUsIl/HP9HyjlSpitZnSp2QUTO07MPGf/9f2S9p+KOwWz1ezRbJuCQtIKBOFB5kbPxZitYxwkdHVKHeI/jC+SMvuCwjmHyWqCSq5y6Cvrqfnvpt6Fn8oPfio/h8/Hbh+Lmf/OtCtg0iq0GBk5EtOfnl4kNpyJP4Pz98+jQXAD1C5X2+6z0KmhuJvqmMOvU+qQ8kmKV36zDEhagSB8kGGPDUNESAT0Sj0AsTmqU+jwU8+fPOLsOeeYfXg2wmeEo+yksui7rC/Oxp/FpL2TUG5yOei+1qHa99WwPGZ5kc9ttBgx+9BsdPy1I55d9iy2xm61+5wxhlC/UElnD4ibZc5qVYPFgPlH5heZjfWD66NPvT4Ozh4A3mn5jsN/o4ywjzedfX6gFT5BeBiz1YzVZ1Zj3VmxMfp689fRqEIjj8z9wbYPMPvw7MwnDAYGpVwJOZPbOVOdQocVA1bgmTrPFMm8JqsJbRe2xen405lz65V6/F/r/8MXHb9waQzlRKVkCquMyWAZb3G707XarHhj0xtYdGKRCClZ0tGnXh/8+uyvkiElV+GcI9WcCo1CA4WsYFF2V1f45PAJopSQaEhEpWmVJHXjpWga2hRHRh7J9RzOOVafWY3Zh2cj1ZSKwY0GY0TzEdAq7eUNFh9fjNGbRjtsWKvlalx+9zIq+ufeg/ZG0g20XtAaN5JuOHzWukpr7Ht1n0vfqSiIT43H+fvnUbNMzTztzoutsVvxxuY3cO3hNShlSoxoPgKTu0yWrI3IDWpxSBCEHWfvnYVarnbZ4bvSKeqdP9/Bz0d/znTkJ+6ewJKTS7DvlX12m5gbzm+QzE5SyVXYc20PBjYc6HSOmLgYtFnYxmHfQyFTQKPQYFaPWS59n6IiWB+MYH1wocc5dPMQ+q3ol/m9LDYL5kbPRaIxEb8++2uhx5eCYvgEUUoICwyT1It3Rl4FRZcTL2P+kfl2jtxgMeBs/FmsPrPa7twQfYhkiiQgqn1z450/30FyerJDOKe8tjxOjj6JphWb5nq9r/LVnq9gMDvuSayIWYH7affdMqfbHT5j7APGGGeMeaZOmyAISSoHVEa3Wt0c8s2VMqXDMZ1Sh286514VGnU1CgrmGCRIMadgS+wWu2Mjmo+QDFPoVXp0rN4x13n2XN0DDsfQc1xqHMICw3K9tqDcT7uPc/fOSeoMFRVn752V/F4quQrXHl5zy5xudfiMsaoAugBwj/UEQeSLpf2XYlDDQVDL1VDJVagWWA3rB6/HnJ5zULNMTWgUGjQLbYb1g9bjiepP5DpWsD5Ysn+rUqZEJb9KdscahzTGnF5zoFfqEaAOgJ/KD2GBYfjrpb8gl0mv/DPQq/SSxzVKTZ6aOfklOT0Z/Zb3Q+VplRE5LxIVplTAz0d/dulazjlO3D2Bo7ePulQ13aJyC6cVxrXK1sq37a7g1k1bxtgqABMBrAMQyTm/l9v5tGlLEJ7BaDEi1ZSKstqyBc5uMVvNqDq9qiiWyrZS1Sl0ODH6hKTTSjOn4eCNg/BT+SGyUqRLc0vl32sUGrzW9DXM7DGzQLY7o+dvPbH94naYbFnaPjqlDusHrc+1+jj6VjT6Lu+LRGMiAMBP6YcVA1agfbX2Tq85d+8cIudGIsWcpZWvU+rwVou3MKnLpHzZ7fU8fMZYbwA3OefH3TUHQRAFQ6PQoJyuXKFSGY/eOYrONTrDT+UHjVwDf6U/AtWB+P25352uUHVKHTrW6IgWlVu4PPfEThPRPbw7NAoNAtWB0Cq06FyjMyZ3mVxg23Ni4zaM+XMMNl3YZOfsAXGTmvSPcwecYkpB50WdcT3pOlJMKUgxpeBO6h30WNoD99Kcr3Hrlq+LPa/sQecanaFX6hEWGIZJT03Ct099W2TfKyeFytJhjO0AECrx0TgAnwLo6sIYIwCMAICwMPfE4wiCKFom7JqA7/Z/B6PFCM45NAoNOtfqjOXPLc93SmFeqOQqrH5+NS4nXsaZe2dQp1wdSY2dwjDx74n4Kfonp5/nFlNfc2YNrNyxa5WVW/H7yd/xdsu3nV7bJLQJdry0I3/GFoJCOXzO+VNSxxljjQHUAHD80V28CoAjjLHHOed3cowxF8BcQIR0CmMPQRCFJzYhFgmGBESERDhs5gJCInjKvil26Z0GiwHbLm7DsTvH8Hjlx/Oc49idYzh59yTCy4ajVZVWLq32a5SpgRplauTvy7iAjdsyb15SKGSKXDeW41LjkG5xzH4yWAy4k3JH4grv4ZY8fM75SQCZ2qqMsStwIYZPEIT3uJV8C32W9UFMXAyUciVs3Ibvn/4erzZ71e68zRc2QyK5BAazAevPrc/V4RstRvT6rRf23dgHGZOBc4465ergr5f+QhltmXzbzDnHz8d+xjd7v0F8ajxaVWmFyV0mIyIkwuUx0sxpDpIN2fFT+eGT9p84/bxDtQ5CcM1mn9Hjp/LDk9WfdNkOT0B5+AThw1hsFiQaEj2ild9jaQ8cvX0UBosBSelJSDGl4J0/38G+6/ZVrBqFRjKzRiFT5KkHNGHXBOy9vhdp5jSkmFKQak5FTFwMRm0aVSCbv4j6Am9veRuxCbF4mP4QWy9uRduFbXHu3jmXx9Ar9U61/4N1wTg68miu6Z8tKrVAt1rdMvWRALFX0bJyS4/KTLuCRxw+57w6re4JwnVs3IYJuyagzKQyCP0uFKFTQ/HL0V/cNl9MXAwuJFxwiEUbzAZM3z/d7obTt35fyRuQQqZw2iXrUuIldF3c1SEUBAAmmwlrz6yF2WoG5xzzjsxD7Rm1EfRtELov6Y6Td09KjplqSsXkfyY7VOAazAZMjJooeY0UjDFM7TrV4WalU+iweuBqVA+qnuf1KwaswMzuM9G6Sms8XvlxTO0yFZuHbJZMW/UmJK1AED7If3b9B9MOTMt0ZvFp8Xhzy5sI0gbh2XrPFvl8calxmS0Xs8PBsebsGii+UKBZxWaY1WMWWlZpid/6/4Yha4ZAzuTg4LDYLJjVYxZqlqmZeW1SehKWnFiCI7eOYHnMcklphQxs3AYrt+LL3V9i6v6pmd/7z4t/Yu/1vTj8+mHULV/X7ppLiZck89it3IqDNw/m6/sPbjQYgepATNg1AZcfXEbjCo3xdeev0aZqG5eul8vkGN50OIY3HZ6veT0NiacRhI9htppRdnJZpJhSHD5rXKExTow+UajxN57fiCn/TMHtlNvoWqsrPm3/KfRKPUK/C81TZ0ev1OPIyCOoU64OktKTsOXCFlhsFnQL74ZyunKZ511MuIhWC1ohzZzmsAKXomXlltjx0g5UmFLBIZ4uZ3IMbjwYi/sutjueYEhApe8qScpFdAvvhi1DtjgcL6l4PQ+fIIiCkZSe5LSkv7Al99P2T8Pzq55H1LUoXEi4gLnRc/HYT48hzZyGL578wi4OLYXRYsTUfVMBAAHqADzf6HkMiRhi5+wBYOTGkUgwJOTp7JUyJQLUAZjXax4uJV6S7Bpl5Va7JuNxqXF4df2rqD2zNmRM5iDvoFPq8Fn7z3Kd917aPVx9cBW+tOD1BBTSIQgfo4y2DPzV/khPc1y5Ng5pXOBxU02pGLdznN0q3mwz46HxIabum4rvnv4OjUMa4/sD3+Ni4kVcf3jdYfVs5VYcu3Ms13msNit2X9md50azDDJ0qdUFC3ovQKhfKO6n3ZdMbwSQ2ZAkzZyGFvNa4Hby7cysGDmTQwYZFHIFKugr4IfuP6BtWFvJceJS4/DC6hew99peyJgMZbVl8XOfn9GlVpdcbS0p0AqfIHwMGZPh685fS24iTnoqfyX32dlxaYdkyMZsM2P7pe0ARCjkz6F/IurlKMnceIVMgeYVm+c6D2PMpc1KG2yIuhqFlvNa4sL9CyinK4eBDQdCq7DX0tcpdRjXfhwAYOmJpbifdt8uBdLKrdAoNdj8wmZce+8a+tTrIzkf5xxdF3dF1NUopFvTYbAYcDP5Jp5d/izO3z8vec21h9dw4u4Jt4qoeRJy+AThQXZf2Y32P7dHyNQQdP61s0PKYwavN3sdi55dhIbBDRGgDkDbqm2x7cVtLm8iSrHq9Cqnn4X4hdi9r+hfEQMaDIBOYX/T0Sg0+KDNB7nOI2My9KvfT3ITOCcpphRcT7qOZ357RmTo9JqH4U2GQ6PQQCVToWpAVSzrvyyzwfqBmwecbv5efnDZ4SZltpoRlxoHq82KI7ePIDYh1iFf3mQxYea/9po8d1LuoM2CNqj7Q120W9gOFaZWwIqYFXl+H1+HQjoE4SE2nd+EgSsHIs0i4to7U3fiwOID2DB4AzrV6ORwfv8G/dG/Qf8imz8mPsbpZ73r9nY4tqD3AlQPqo5Zh2YhKT0Jrau0xozuM1xScpzVYxZOxZ3C1YdXJTefs8PBcSv5Fk7GnURESARmPTML07tNR6opFUGaIDsnXq9cPWgVWsmN3RpBogr3bspdrDq9CuvOrcM/1/+BjdugVWjxXIPnJOsHLNyCiwkXAYhw1L83/8VLa1/C5cTLsMIKI8RT0fB1wxFeNhzNKjbL8/v7KpSlQxAeovbM2ohNiHU43iS0CY6OPOr2+YeuGYrfT/3uEFtXypS4/O5lVA6oXKTz2bgNu6/sxrzoeVh7dm2uzVcC1AHY9MImtAtrl+uY99LuIXxGOB6mP8w8ppApULNMTZx58wxWn16NYX8Mg9lqhoXbN0zRKrROj3/R8Qu0rtIafZf3Rao5VXKzWcZkGBox1G3dqAoDZekQhA9htVklnT0AnIo75REbxrYd66CNo1Vo0btu7yJ39oBwkJ1qdMLvz/2ODYM3oGvNrgjVh0o2TeGcI7JSnv4K5XXlETU8Cs1Cm0EpU0IpU6JLzS6IejkKD40PMeyPYTBYDA5OHRDaNjqVzm5vRClTooy2DAY0HIDuS7sjPi3eaWaRjdtw7UHxbu1BDp8gPIBcJkcZjbRWTIg+RPJ4UdM4pDE2Dt6IuuXqZvaDHfbYMCzpt8Ttc3ep1QVbX9yKi+9eRP3g+pnpn3Imh06pw4/P/Cgp1CZFREgEokdG495H9/Dg4wfYPGQzQvxCsOnCpjybqVhsFkx/ejoaBjdE1YCqGNl8JI6MOIIdF3fkmVWkVWjRLbyba1/YR6EYPkF4iI/afoSJURPtVpB6pR6ftv/UYzZ0rNERZ986i1RTKtQKNRQyz7oAnVKHg68dxNKTS7Hx/EaE+oVidORoPBb6WL7HClAH2L232hwlinPSqEIjjGg+AiOaj7A7ft9wP9eaAQaG8rryGBk5Mt92+hLk8AnCQ3zU9iOkmlIx/cB0cHDImAwft/0YoyNHe9wWZ20DPYFWqcVrzV7Da81eK9Jxe9TuAcsmx1BO5rwKLaZ0mSL5WacanST7y2agkqtwdORRBGmCCm2nN6FNW4LwMOmWdMSlxiHEL6TIm4WUdhYeXYg3N78Jq80Ki004f5VchccrP45vOn/jtCALAAK+CUCyKVnys7DAMFx976pbbC4KXN20pRU+QXgYtUKNqoFVvW1GieSVpq+gY/WOWBGzAgaLAX3q9kHTik3zvO5m0k2h53PZsfsUA8PYtmPdYa7HoRU+QRA+A+cce67twe4ruxGsC8bzjZ5HWW3ZAo1lMBtw6NYh+Kv80SS0idOuWoduHkKnRZ1gspgc+tkCwIsRL+LXZ38tVP9fd0MrfIIg8sRqsyLdmp5n4xJPYLFZ8OyyZ7H7ym6kmdOgVWjx0Y6PsPmFzWhfrX2+xlpyYglGbxoNGZPBxm0I0Ydg85DNqFOujsO5w9cNdygOY2CICInAhsEbStTTGKVlEkQpxGQ14d0t78L/G38EfBOAOjPrYMclzzXTlmLx8cXYdWUXUs2p4OBIs4iuWM+tfM6lDJwMTtw9gZEbRyLFlJLZuetS4iU8tegph9TLREOipI4OB8fNpJslytkD5PAJolTy6rpXMe/IPBgsBli5FRcSLqDPsj44cvuI12xaeGyhZGqkwWzIl12zD812UN3k4HhgfICoq1F2x3PbNNcqtU4f8HZHAAAgAElEQVQ/K66QwyeIUkZ8ajxWnV7loEdjMBvwzZ5vPGLDxYSLeGvzW+jwcwd8uO1D3Ey6CQbnMfKM+DnnHJcTL+NOyh2n595JuePQqjGD+2n37d7rVXo8Hf60g9CbVqHFqMiC9dn1ZcjhE0Qp4+rDq1ApHFe2HByn7512+/wHbxzEYz89hjnRc7Dn2h7M+HcGGv7YEN3Du0s2YNEr9WhWsRkWHV8Ev2/8UGtGLVT6rhIazmqI6w+vO5zfq24vyXHMVrOkVs/C3gtRP7g+/FR+8FP5QavQ4unwp/Fhmw+L5gv7EOTwCaKUEV42HCarYzaKnMnxeKXHCzwu5xw//PsDanxfA4HfBuKZpc/gdLzjDWTkxpFINadm5smbrCYkpSch6moUutTqAr1SDzmTQ6/Uw1/ljzXPr8HOSzsx7I9hSDOngT/65/S904icF+kQl3+h8QsILxtup6uvV+rxUduPHGSgASBYH4xjI49h29BtmNtzLqJHRGPt82slu28VdygtkyBKIe9vfR8/Rf9kFzP3U/khekS0ZCaLK/zf1v/DnOg5mWMyMPip/HB81HHUKCOki9Mt6dB9rZPUrVHJVLjxfzcQmxAr0jL1wRjQYAACNYGoM6MOLiRecLiGgWHHSzsc5KXTzGlYcGQBlscsR5AmCG+2eBPda3cv0PcqDpBaJkEQTpnSdQq+6vQVqgRUgU6pQ+canbF3+N4CO/tEQyJmH55tdwPh4DCYDfj2n28zjylkCqcbpSabCWHfh+HC/Qv4pP0neK3ZawjUBAIAriVJq1RycJyOc3yKuJd2Dzsu78DhW4ex+8pu/HH2DySnS1fRliYoD58gSiEyJsN7rd7De63eK5Lxzt0/B7Vc7dBC0cItOHjjYOZ7uUyOFyNexOITiyXbLRotRozaNAoRIRE4FX8KGoUG3cO7o5y2HG6l3JKcu0O1Dnbvk9OT0WJeC9xLuwcbtyHdmo5fj/+Ko3eO4uBrB326gMrdkMMnCKLQVAusJunAZUyGeuXr2R37vtv3uJl8E9svbndoNwiImH6L+S2gUWjAwMDBMbr5aEzdP9VB4KxWmVqICI2wO7b4xGKkmFLswkbp1nScuXcG/1z7B8mmZCw8thBmqxlDI4aiX/1+LvXgLQmUjm9JEIRbqehfEb3q9HJoQK5RaBx0aHRKHTa9sAn/1+r/IGeO+vVWLoTPUkwpSDYlI8WUgh8P/4hP230KpUwJ9uiflpVb4sSoEw7XH71zVDKf38ZtGL97PAasHJDZAvHlP17Gcyuegy/tZboTcvgEQRQJi/stxosRL0Kj0EApU6JmUE2sfX6tU/Gy3vV655p7nx0Zk6FRSCMkfZKE02+eRsLYBBx47QB0KkdJiIgKEU6lIvZd32fXBD3VnIptF7fh76t/u2RHcYccPkEQRYJGocGcXnPw8OOHiP8wHrHvxKJrra6S5564ewLdlzpmzUit+AGxOjeYDdAoNKhXvl6uuvQvPfYSdEqdXZhGJVeJhugSN5g0cxo2X9ic19crEZDDJwiiSFHJVQjUBOa6OfrKuleQlJ5k13uWgTl15BabBU+HP42k9CSsjFmJ5aeW44HxgeS5gZpAHHj1ADrX6Aw5k0MlV2Fgw4H4rP1nDhW1AKCUK522n8yuq18ScGsePmPsbQBvAbAA2MQ5/yi38ykPnyCKDw+ND7E8ZjluJt1Em6pt0KVWF5c2P1NMKSgzqYzLjlTGZPi287eoU64OXljzQuZTgMVmwbze8zCk8RCn12b4N8YYktKTUHlaZQdlTK1Ci7NvnUVYYFjmsfjUeIzeNBrrz62HjdvwZPUnMbfXXNQsU9Mlmz2N1+WRGWMdAfQBEME5T2eMVXDXXARBeJYjt4+g468dYbFZkGZOg5/KDxEhEfjrpb/ybEaukClcjt1nnN8tvBtazm/poP/z+vrX0T6svZ2zzk72p4wAdQA2vbAJfZb1yczgsdqsWNR3kd31VpsV7X5uh0uJlzJvSruu7EKr+a1w6d1L8FP5uWy7r+HOkM5oAN9yztMBgHMe58a5CILwEJxzDFw5EEnpSZnZMCmmFBy9fRQzDs7I83qNQoNu4d1cbqCulCmx9uxayRCRlVux/NRyl23vUK0D7n5wF2sGrsHy55Yj7sM49Kvfz+6c7Ze241byLbsnEBu3Ic2cht9P/u7yXL6IOx1+HQDtGWMHGWN/M8ZaSJ3EGBvBGDvMGDscHx/vRnMIgigKLj+4jFvJjkVQBosBvxz7xaUxFvRegDrl6sBf5Q+9Ug+dUodqgdWgkjlW4QZpgqBX6iU18S1Wi0OIJi9UchU61+yMbuHdJLN5zt8/D7PVsT4g1ZyKmPiYfM3laxQqpMMY2wEgVOKjcY/GLgOgFYAWAFYwxmryHJsGnPO5AOYCIoZfGHsIgig4nHPcN9yHVqGFXuWoNpmBKzLGeRGsD8ap0afw99W/cTnxMh4LfQw1gmogcl4k7qTcQZo5DUqZEkq5Er88+wuqBlTF+F3jHcZRypUI9QtFuiUdaoXapbnzolGFRlDKlUi32mvq+yn90DQ07/64vozbNm0ZY39ChHR2P3p/EUArzrnTZTxt2hKEd4i6GoVX1r2C60lCbrhXnV6Y33u+ZNYM5xz1Z9XHufvn7I7rlDp88eQXeL/N+wW2I9WUisUnFmPn5Z2oHlQdoyJHZW6Ujt0+Fj8c+gEGsyGz4lYpU2Y2Kln+3HJ0C+9W4Lkz4Jyj+dzmiImPyVQVVTAFQv1Dcf6t8z7ZGMXVTVt3OvxRACpxzicwxuoA+AtAWM4VfnbI4ROE57lw/wKazGliV52qkqvQolIL7H1lr+Q1J++exBO/PAGzzQyjxQi1XI1WVVph85DNuXaRKiz/XPsH84/Mx5KTSxyyfHRKHS69c0lSAjm/JKUnYez2sfjt1G+w2CzoU7cPvuv6HSr6Vyz02O7A61k6ABYCWMgYOwXABGBYbs6eIAjv8L+D/3PQxzdZTTh65yhi4mLQsEJDh2sahzTG9THXsebMGtxKvoU2VdugXVg7twuTtQ1rizP3zmDl6ZUODt/GbVh2ahnebfVuoecJUAdgds/ZmN1zdqHH8iXc5vA55yYAQ901PkEQRcPZe2clc+KVMiWuPLgi6fAB0R7wxcdedLd5DiQaEiUbuKRb0pFgSHB6Hecc2y5uw5qza+Cn8sOwx4YhIiTC6fklEaq0JYhSTvuw9tDIHXPn063pPukQu9TqItmNSqfUOZVysHEbBq4ciP4r+mNu9Fz878D/0Gp+K8z6d5a7zfUpyOETRCnnjRZvwE/tZ6djo1PoMKjhIFQNrOpFy6RpEtoEAxoMsOtbq1fq0T28O9pUbSN5zdbYrdgSuyVTOM3KrTBYDPhg+weITy096eDk8AmilBOsD0b0iGgMajQI5bXlUSOoBr7s9CXm957vbdOc8nOfn7G472L0rNMTPcJ7YGGfhVg+YLnTPYSVp1faqWRmoJQpsf3Sdneb6zNQAxSCIBAWGIYl/ZZ42wyXYYyhb/2+6Fu/r0vnZ6hnSvXSzanhX5KhFT5BECWe4U2GO9X4eTr8aQ9b4z3I4RMEUeJpXqk5Pn/yc2jkGvip/OCv8oefyg/rBq1z2iylJOJWeeT8QoVXJYzYWGDrVsDfH+jTBwgM9LZFRCnndvJtbLu4DXqV2OTNTUKiOOH1StuCQA6/BPHRR8APPwCcAwqF+Pe6dUDnzt62jCBKHK46fArpFDfu3QMuXgSsjsqBPsPu3cCPPwIGA2A0AikpQGoq0LeveE8QhFcgh19cSEgAuncHqlQBIiKAihWBtWu9bZU0P/8sHHxOGAP++svz9hAEAYAcfvGhTx9g504gPR1ISwPi44GhQ4EjR7xtmSMmx7L3TMyOOuMEQXgGcvjFgfPngehoR0dqNALffecdm3LjhRcAvcRmmMVCMXyC8CLk8IsDN28CKgnJWZsNuHzZ8/bkxTPPAD17CqfPmLBdqwXmzRMZOwRBeAWqtC0ORESIUE5O1GrfXDHLZMDvvwN79gAbNgABASL8VKOGty0jiFINOXxPkJICLFkC7N8PNGgAvPIKEBzs+vXlygHvvQfMmCHi94BIdQwIAN55xz02FxbGgA4dxIsgCJ+AHL67uX0baNECePBAZK5oNMDXX4vVb0Q+pGe//hpo1AiYNg24f19k7Iwf7/qNIy0NWLMGuHEDaNkSePJJ4ZQJgig1kMN3Nx9/DNy9KzYsAbHRajQCw4eLjVhXYQwYMkS88suZM2KlbTSK3HitFmjSBNi+XdwItm0DlErg6acBP7/8j08QRLGAHL672bAhy9ln58QJEerxhIMdNEg8FWRUVaekAIcPi2yaLVuEswfEJvCqVUC3wjeCJgjC9yCH726ksmsAsWJXeODnv3VLpHXmlNAwGoE//hDHs1e/9u8vsoKCgtxvG0EQHoXSMt3Nq6+KuH12lEqxis553B3kppUk9ZlMJjRvCIIocZDDdzfjxwPt2gE6nchL9/MDatcGFizwzPyVKwM1azoel8sdjwFCoycjE4ggiBIFhXTcjUYjNkePHAGOHxfOt0MHz2bILFsm5jSbRaaQn5/Q5Ll2zdG5cy4ygPKLwSCygK5cASIjgS5dxNMCQRA+Azl8T9GsmXh5g8aNhXNfsQK4fl2kZXbtKjKF1qwRNwGZTNycPvoIqF49f+PHxgJt24qbR1qaeJqpWxf4+29piQWCILwC6eGXZjgHduwQTwBqNfDSS0CrVvkfp1Ur4N9/7fcE1GpRLPbtt1nHTCaRBfTXX0BYmNjfqFKl8N+DIEo51ACF8AyJiUBoqLRCZuXKotALEKmgbdoI7Z+UFHFDUCiAjRtFERhBEAWGGqAUd4xGYNYsoH17IUa2cWPuGTf5ITZWaOmfOFG4cXbvBkaNkq4zAOztnTYNuHBBOHtAaAOlpopCMputcHYQBOESFMP3RUwm4ehPn87aVN25Exg5Evj++4KPazaLYquNG0V9gMUCNG0KbNqU/36zn34qtH2kGp0AYgU/dGjW+2XLpLtdPXwInDsH1K+fv/kJgsg3tML3RVauFHII2TNojEbgf/8D/vOfgo/7zTfCuRuNQFKSGP/QIeCNN/I3zqVLwPTpzp29n59w4OPHZ7Vi1Gqlz7XZnH9GEESR4jaHzxhrwhg7wBg7xhg7zBh73F1zlTg2bXLuTL/9VqR5FoSffhLpk9nJ2EjNrUtVTrZvd55y2bKlkEYePBioVk3E6WvUEMd1OvtzZTJRk5DfrCCCIAqEO1f4kwF8zjlvAmDCo/eEK1So4LwwymQSq+v8YjIJxU4pbLb8tR7095d2+EqlSPc8dgz4/HPRhxcQufm//CKyebRakarp7y82e9esye83IQiigLjT4XMAAY/+HAjglhvnKlmMHJm7zs7du/kb7+RJkf7ozKnXquVc80eKXr2kjysUYmU/ebJjQZfBIDZso6NFaGrZMuDqVTE3QRAewZ0O/z0AUxhj1wFMBfCJG+fyPcxmsXr98sv8h0zq1xftAKVQq0X7QFfhXJwfH++YTZNR7XvtmniqmD/ftTH9/cXGb2CgaMISECBW7l9/DcTFOb+xXLggvturrwI9ekjf1DgXm8GVKoknhmbNRAEXQRCFh3Ne4BeAHQBOSbz6AJgBoP+j8wYC2OFkjBEADgM4HBYWxksEd+9yXrMm5/7+nDMm/l29Oue3b+dvnJkzOddoOBduUPw5LIzzhATXxzhyhHM/v6wxcnvpdJxv3er62EYj51u2cD5/PudNmnCu1XKu14vvLDV+mzZ5j/nf/wo7ctp14IDrdhFEKQPAYe6Kz3blpIK8ADxEVmEXA5CU1zXNmzd33y/iSQYN4lyhsHdaCgXn/fvnf6yoKM779uW8VSvOJ07Mn7PnnPN9+zgPCHDN4QOcP/lk/sa32TgPD+dcLs/7ZrJ7d+5jGQzihiF1fdeu+bOLIEoRrjp8d4Z0bgF44tGfOwG44Ma5fIu1ax3DJxYLsH59/oun2rcXoaH9+4HPPgPKlJE+z2YTCpxNmoi4+Pvvi6YnzZvnT6jt2rX82bdvH3DnTlb6ZQaMidCPUilaOf7xB/DEE9JjZHD7tvPPTp3Kn125ERMj+gq3bg18+KHoGUAQpQB3Fl69DuB/jDEFACNE6IZwF2+8IRqlZ6Rz/vADsHq12LBdvBh4/nlx0zGbRZaMweBY4SqX5+2Uc3LnjnTGDudAp07C0ecko9ViUJD9zSg01HnVbb16+bPLGX/9BfTuLSp9rVahYjp/vugARhvIRAnHbSt8zvleznlzzvljnPOWnPN8NHAt5jz7rOOGpEIhslsKK4ucnp4lT5DB9evAr7/a5+6bTGKj9uefxbynTgklzFdfFSmS06bZ58XLZKJg6rPP8mdPy5bCppzodKJHbnaSk0Wlb2AgEBKSpaiZgVYLvPOOY76+TifSPAsL58CIESKDKOOJxGQSRWgff1z48QnC13El7uOpV4mJ4d+9y3mNGlmbtn5+nFerlv9N2+zcvy/2AFQqsR/QtKnYkOWc89Wrncfpe/Z0Pua6dWJvICyM8xdf5Dw2tmC2vfWWfexdrRZx/ZQU+/M6dRKf5Yztnz2bdY7VyvlXX3Fepoz4vG5dsTFcFNy/L34/qd+pTJmimSMvzp7l/OWXOW/UiPPnn+f8+HHPzEuUaODtTduCvEqMw+ecc5OJ85UrOf/iC85XrOA8Pb3gY9lswsErlfZOyt+f81u3RAaLVCaOQsH5u+/mby6rlfNZszivV4/zSpU4f/11MUde9i1dynnLlpzXr8/5+PGcJyban3PunKOzz7Bx1CjnthQlBoO0DYDIqnI30dHiv1PGBrdM5tpmNkHkgasOn8TT3IVSCTz3XNGM9e+/ohF5zvx2kwmYOxeYMEHoy58/b79ZrFLlrZNjMolN5mPHRIglKgpYvjyrcOrnn8Vm8+nTQNmy4hjnwIYNQs3zyhVR1DV4MLBrl3NdnFmzpEM/FovQDZKiqDtmaTSiSfvq1fa26HTAu+8W7VxSjBljH46z2cTv/MYbYiOZINyNK3cFT72K1Qo/IYHzzZs5P3hQrHBd4cQJsXpeuVKsNl1l6VKxmpdamQ4cKM65dYvzyEixasxYPb7wQu5PFnFxYmWb8XSg1UrPodVy/s03WdeNGeOYKy+TiSeCmzel53G2slYqOf/kE9d/i8KSnCxSPLVazgMDRW3DG28U/dOEFM5+A8bEEyFBFBBQSMdNWCwi3i2TiXiwTsd5rVqcX7zo/BqrVThfnU44GH9/zsuV4/zkSdfmPHlS2hnrdJx/95045949zsuXz3L4GY66b1/n4w4b5hgmcvbKyIO/csW+GCyn05eqNViyxHnxl0rl+t7GjRsiXPT885z/8APnSUmuXSfFxYuc79wp9ls8RUiI9G+g17u+aCAICcjhuwOzmfPmzaX/p61b1/n/tL/84rgizogbu/o/+jPP2Dt9uZzzChVErHzLFnHTkapw1WpF/FwKZ08NOV9yOedvvimu+fXX3Ct31WrHeZYvdz7XsGGuff+MfYqMVbJOx3mVKp512IXl228d/x5otZy//763LSOKOa46fNLDzw+rVolYtxTXrzvvIDVnjqOYGCBE0E6fdm3uNWtEkVBoqEhrfP55IUT2yy8iLn3xonAhOVEqndvlaozcahX59AcPAuXK5X6d1Gfdu0vn1+t0wFtvuWbDsGEi/p0Re09LE7/fhAmuXe8LfPihKPjSaMR/Q40GGDBA9CkgCA9ADj8/LF/uWFGaAefO5YedCacx5rossUolctFv3xbzLF0qqm7HjZO+mWRgsQDh4dKfDRrkukrmzZvAU0+JSl61WvocpVLciHLi7y+auuh0ItdfpxPObtw4IDLPNpyinuDyZcfjZrN0YZevIpMBM2eK33LbNlHV/Ouv4ncjCA9QMrJ0rl8H9uwBgoNFdaczLfnC4ufn/DPOgRYtpD8bOlSs5HM2H9HpgMaNReVpdLRwjI0bu16cdfJk7jLKarVw0E2aSH8+aZL43Vx9yrBagRUrRLVq9+5CkiDjqUKtFjeWadOkr804f/168Tt06yYyi1zB2Q0GKJ7dssqWBR6nfkCEF3Al7uOpV75j+DabyBjJ2Aj19xeZItkLeYqS3bulY/EA57NnO7/OYBAFThmxb41GjPPXX2JD099fFE7p9ZzXrs35+fOu2XPpkvPMGsY4HzyY8wcPch9jwwbn30nqNWaMuM5qFXH1yZPFa+vWrEwXo1Fk6xRl5knXro6CdDod55MmFd0cBFFMQanYtF2zxlFdkTHhNN2V9fDf/4qNQ7VabGYqlUIeOC8sFs7/+IPzt98WKY43b4oqy5zOljFR+epqmmC7do6ZNjod59u3u3b9vn2uyyf7+Ymq3vv3Of/oI1FN27y52Mi12YTN48aJ/yYZaY/TphXNf4s7dzhv0EDY4OeXlYFE6YwEUUocfufO0o5Jrxc57+7ixg3h5NauzV8+fU5GjZKWFc7IoZfKac/JvXucP/GEeGoICBBPC3Pm5H7N+fOcv/ce588+y/mMGeIGkzPDRyazzxvXaDhv1kzUH1Svbi9RoNeLXPYvvpDWsl+woOC/UXZsNs737OF88WLOT58umjEJogRQOhz+449LO/yAAM7378/fWN7g2Wedr6aVSs6Dgji/cMG1sa5dE6X7ed2A/vxTOOGM8IhOx3nlyo5PSpGRnE+fLjRf6tYVWvwpKeIG4SwE5EwTv3r1vO3ftk2knrZsKdIXHz507XsTBFFK0jIHDZLetGNMtMbzdXr2FFLFUpjNQsVx7Fjn1ycnA2+/LVIlIyKAH3+0V8zMic0GvPSSyOrJkGBISxOZP0aj/bkxMUL6+ORJ4OxZoaKp1wPbtzvPCnKWwXT1KnDpknO7Jk0SCqObNonUz88/F9k7OVVBCYIoFMXb4Y8cKfRfMpymQiEyXxYuzF9Tbm8xZAhQs6ZIUZTCZgN27HD+2RNPiN63CQkiVXPRIiFX7CwN9MIF6RuCzeborA0GcQPJSfXquWcGScE50KWLdJ1AYiLw3//a30QMBuDGDdd77BIE4RLF2+HrdMCBA8Ds2WK1/847oqFFv37etsw1rFaRxvjWW85TMQMCpI/v3CkceHYRMLNZFCOtXWt/7tmz4kng7belBcyckZLiWDD15psFu5nGxYnVe04OHZJOuzQYRAonILp9DR4MdOwofi9a+RNEgSj+efhqNfDii+JVnJg1SzQkUShEeCUoSKy+s6/OdTpxE7PZRKHV7NliJfzCC+JmIbWST0kBjh7NKoDauFH82WQS8+RHgbJpU7ECj4sTTyJqtXiiWrUKePllURAltWqXQiYD7t1zPF6+vHQoiDGgYkWhBjpmjLgBcC5uGj/9lFW3QBCE67gS6PfUy+e1dIqKXbuk1Sb9/e1VHIcPF+mcw4fbb6pqtWIjVEqfRq/PyoqxWISgmlSOfm5Nx+VyYd+TT4pMnYwah5kzs76D1SpULnMKqSmV0oJsGg3n8fGOv4XNJjaFs4u+ZWwm79wpvUGs0Yjcf4IgOOeub9oW/xW+N7DZhIa7UgnUrp3/toXff++48ZkRR1+zRlQKN2ggwjkvvQT89pv9uQaDWF1rtfbt+mQy8VSQsbo/e9ZxMxYQblOtdr752rKlmHv3bhECyggDjR0LVKsmWibKZMBXX4nvPn26eFIxm4W9e/YInfyMymK9XrQQLF/ecS7GgK1bgWeeEfIJCoX4Pv/7n/gdpGQHjMYsbSGCIFyGHH5++ftvsV+QnCwcZ5UqQs+lfn3Xx7hzR/q4QiGcY/v2YuyWLUV4RorUVOEkHz4UUgcA0KaN2LDO2MT297dviJIdjca5wz95Ujj5nCGjtDTg66+FwweEs/7qK+DTT4UuTJUqYs6UFCEYt2qVyCB6+23H/rbZqVZNzHn6tAghNWsmblwnTzq3v0IF5+MRBCGNK48Bnnr5fEjn1i3pyt7gYCEn4IzYWCHLkJAg3k+cKK0pr9WKBh2cc/7337lXwKpUnE+YIM41Gp3n37do4Ri+0euFJK8zWQaFwrlOflhY0f2eeWGzcd6woXS4Z8eO/I/1yy+irqBSJSHLfPWqW8wmCE+DUpGH72l+/dVxxcm5CDFs2iTeX7wo2uU99ZQIObRtKwTR+vQBKlUSq+E33wRCQuzTMXU6kY+eIdB28qTzvHZAPA288or4s1rtPLVzzRqgVi0xrr+/OO+VV4DJk8UThBQWi7ScsUwmUkE9BWPA5s1AnTriqSUgQNj/+edA5875G2vsWPG7nzolRNyWLBFPEs6etgiiJOLKXcFTL59e4ZtMnPfpI73qVSo5r1FDNPBWqbJWxzlXphmr6yVLROOSiRNFtXDv3mKDMjvbtjlf4ev1QqzMVWw2oZmzejXn169nHZ82zfkTRLVq9humcrmoYI6NLZKfM1/YbJwfOSJ+k5zN0V3h3j3pJyqVSmgCEUQxB6VCWsFTREVxXqaM8xBIfl9NmuQ9p9XKeb169qEVmUxk8GSEhgrLokXSNyWA8wEDxE3liSfEzezll3Nv4+jL7N4tfjep79mypbetI4hC46rDp03bvEhOFpujyclFN6ZUPnpOZDKR7TJ6tNgU5hzo0EFU1pYpUzR2dOoksmByFmNptULDv2tX8SruVKkiXbMgk4lwF0GUEiiGnxfr1glnmxPGRJu6/MoMKBS5Z6xkp3x50SkqPV3sE+zcWbQOqnJl0XVKp8s6pteLG8szz9ifm54OzJghGne0awcsXiwd5/dFatUCWrd2rBDWaID33/eOTQThBWiFnxcPH0pvnnIuHF9UlOurf7VabJz+97/5s0Emy1+FbH4YPx548kmhW5OSInL4+/e37xpmtYqngWPHslI5jx0TbfoWL3aPXUXNmjXA8OFiE1gmE5XNc+YUD5E9gigiyOHnxVNPSR/X60XWx7590p/7+4tCpCefFNklV64ILZgxY0SGjq+wZ4/IWMnI3iJ+pp4AAA5NSURBVOnQwbGQbONG0Qg9e95+aiqwerXIfmnUyLM2F4TAQOH0Hz4UrypV3HcTJQgfhRx+XtStC7z6KvDzz1lKk3q9SAvs1g3YtUukXN67JxyIVivSK4ODhSOsVs279ufG++8LXZqMitilS8UqeOZM+/P++su5YFlUlL3D51wofC5aJEI+Q4eK3ym/1cjuIjBQvAiiNOLKzq6zF4ABAGIA2ABE5vjsEwCxAM4BeNqV8Xw2S8dm43zzZs779+e8Vy/Oly8XOjXZPz9xQmTzjBsnWixGRHD+44/25/kSJ09KZx1ptZwfPWp/7tdf23e/ynj5+3O+cqX9uW++aV+cptcLLSCCINwGPJGWCaA+gLoAdmd3+AAaADgOQA2gBoCLAOR5jeezDt8VjEaRh58931un43zQIG9bJs2kSdLVtHI5519+aX/ujRvSImblytlX+J44IX2eTsf5v/969vsRRCnCVYdfqCAm5/wM5/ycxEd9ACzjnKdzzi8/Wuk/Xpi5fJ4VK4SeTHaxsrQ0keVz+rT37HKGTiedYZTRRCY7lSsLbfry5cXehFYrztFogOeey9K5//NPsW+RE6MR2LKl6GznHLh+XVTMEgThMu7ataoM4Hq29zceHSu57Nwp3U1KJhNNWnyN556TPs4YMHCg4/HOnYUMwcyZwuGmpQE3bwpJiU6dRMaOv7+0uqVSWXTa9UePCqG6OnVEumWTJsA5qTUHQRA5ydPhM8Z2MMZOSbz65HaZxDGJZHaAMTaCMXaYMXY4Pj7eVbt9j+rVpTs3yWRCQ8fXCA0VqZgKRVbap1Ip1DYrO7k3y+UilTGn5HJamlDEdHYTkcmyJJsLw4MHItPp3Dlhg9Eosofat5eWgSYIwo48HT7n/CnOeSOJ17pcLrsBoGq291UASD5/c87ncs4jOeeRwcHB+bPel3j1VccQiUwmMkK6dPGOTblhMgHffSecvM0mXkqlyDrKjSNHpI9fuCDST1euzBI6CwgQf166tGhuer//7hgy4o/E69bl9teRIAjAfSGd9QAGMcbUjLEaAGoD+NdNc/kGVaqIop4qVbLi202aiLTF7EVMvsLq1cD581kpmYBYqS9eDMTGOr9OqokJIBy7Ugn06CFaIi5ZIsa6exfo27dobL5+XVrD32gUTc8JgsiVQjl8xlhfxtgNAK0BbGKMbQUAznkMgBUATgP4E8CbnPNctH5LCB06iI3bY8fEijc6GqhRw71zJiYCb7whHHGFCqKwy1nOfHaJiG3bpM+Ty0UxljM++MBxUxcQq/mEBPFnnU40SendO6sZS1HQqlWWfHR2VCrnUs8EQWRS2CydtZzzKpxzNec8hHP+dLbPvuKc1+Kc1+WcF2GKho/DmGh7WKWK++cym4VGzIIFwP37ou3h7Nmiuje7zs3OnaI4Si4HypYFvvxShFhyassA4pzcKoHffVds0uYkLg4YMqTQXylXnnlGbNZm1/7XaoW+T9u27p2bIEoAVFtenFm3TmTKZFeCTE8Xm5oZbQ///Rfo2ROIiREr/MRE4JtvhIPOuefAmHCgue05MCbmzInFInrguqIEWlDkchEiGztWZOjUrg385z8i5dNXKnkJwochh1+cOXJEOixjNIqwEiC6Q2WP0wMiDr50qYizlysnUib1euFA//5bOrUyOxmhm5zI5UKnxp3o9UJ8LjZW7EGMHSudHUUQhAPk8Isz4eHSMXKtFqhZU/w5Jkb6WrkcaNBA5Nbv3g0cOgScPSu0g/Kie3fpoi0/P5GeShCET0IOvzgzcKBw7tlVH+VysYHaq5d437ixdLjDahX7DAqFkAiuX9/1sMiECWIvICOWLpOJjdp583wzI4kgCADk8Is3fn7A/v1i41apFK8OHYRkc8aG7H/+I24K2dHpgHfeKXgGTcWK4snh449FT4ChQ4F//hFZOQRB+CyMS3Vz8hKRkZH88OHD3jajeJKSIlboUk48Kkqkax4/LmL2H3wgpJFJD54gSgSMsWjOeWRe55EefklBKj89gw4dRE0AQRClGlriEQRBlBJohU/4Flar0PO5fVvsTYSHe9sigigxkMMnpElNBVatEr14IyNFm0J3Z+BcuSKqhBMSRJGYxQIMGiQqiWm/gSAKDTl8wpFz50T2jdEoNoP9/MRKe8+e3PcKCku/fkIgLbssxIoVQv74lVfcNy9BlBJo2UQ4MnSo0ObJqOJNSQHOnBEaPO7i6lUxR3ZnD4iq4B9/dN+8BFGKIIdP2HP/vmgqkjNdNz1dyB27C4PBecjImfonQRD5ghw+YU9u1bbuFCirU0e6DaJGI91ykSCIfEMOn7CnbFnRuCXnJqlGA7z4ovvmlcmEmJtOl1UlrNcLbZ4PPnDfvARRiqBNW8KRJUvEpm1amnjpdEC9esBnn7l33s6dhWTDvHkiY6dLF5Glk13/niCIAkPSCsUZo1FIBS9YIGLs3bsDU6cCVavmeWmeGAzAmjViMzUyEnjqKUqNJAgfxVVpBXL4xZmnnxY6OUajeC+XC62cc+eAoCDv2kYQhMdw1eHTkq24cvw4sHdvlrMHRJVqSgrwyy9eM4sgCN+FHH5x5cQJ6RBLWhpw4IDn7SEIwuchh19cqV3bMVceEBucjRt73h6CIHwecvjFlZYtRTvCjBTGDFQq4LXXvGMTQRA+DTn84gpjwI4dQJ8+otOVXA48/riI64eEeNs6giB8EMrDL86UKSPExcxmsWFL+eoEQeQCOfySQEY/W4IgiFygkI4vkpICTJggJInr1QOmTAFMJm9bRRBEMYdW+L6GxSL038+ezcqx/89/RLz+zz/dK2BGEESJhlb4vsaGDUBsrH1BlcEA/PMPcPCg9+wiCKLYUyiHzxgbwBiLYYzZGGOR2Y53YYxFM8ZOPvp3p8KbWkrYt09a/91sJodPEEShKGxI5xSAfgDm5Dh+D0AvzvktxlgjAFsBVC7kXKWDqlWFOmVamv1xtRqoTD8hQRAFp1ArfM75Gc75OYnjRznntx69jQGgYYypCzNXqWHoUECR4z7MGKDVAr16eccmgiBKBJ6I4fcHcJRznu6BuYo/ZcsCO3cK6QStVuTWN2kiGoir6Z5JEETByTOkwxjbASBU4qNxnPN1eVzbEMAkAF1zOWcEgBEAEBYWlpc5pYPmzYXE8fXrYrVfqZK3LSIIogSQp8PnnD9VkIEZY1UArAXwEuf8Yi7jzwUwFxB6+AWZq0TCGEA3QIIgihC3hHQYY0EANgH4hHP+jzvmIAiCIPJHYdMy+zLGbgBoDWATY2zro4/eAhAOYDxj7NijV4VC2koQBEEUgkKlZXLO10KEbXIe/xLAl4UZmyAIgihaqNKWIAiilEAOnyAIopTAuFSbPC/BGEsG4FDI5eOUh6gsLm4UR7vJZs9RHO0uzTZX45wH53WSr6llnuOcR+Z9mu/AGDtc3GwGiqfdZLPnKI52k815QyEdgiCIUgI5fIIgiFKCrzn8ud42oAAUR5uB4mk32ew5iqPdZHMe+NSmLUEQBOE+fG2FTxAEQbgJn3T4jLG3GWPnHnXTmuxte1yFMfYBY4wzxsp725a8YIxNYYydZYydYIytfaR/5JMwxro9+vsQyxj72Nv2uAJjrCpjbBdj7Myjv8fvetsmV2GMyRljRxljG71tiyswxoIYY6se/X0+wxhr7W2bXIExNubR341TjLHfGWMad8/pcw6fMdYRQB8AEZzzhgCmetkkl2CMVQXQBcA1b9viItsBNOKcRwA4D+D/27ufEKvKOIzj3wcnKwvBTRsnGAecUpJwSJGkCMdFf2TWBoboKjE1CDKN9q4soXY6bmYgchqkRX8MgnZaNCaTziYMdPyDszEFF8Mwj4v3CAOO5x7Te99zu7/Pas5h4DwwZ5573vfcc94DmfMsSNIi4CvgLWA18K6k1XlTVTILfGR7FbAB2N0muQH2AZO5QzyEI8CPtl8EXqYNsktaDuwFXrH9ErAI2Nrs49au8IFdwKF7C6bYvpE5T1WfAx8DbXFTxPYp27PF5mmgO2eeEuuBv21ftD0DfE26IKg129dsjxc/3yaVUO3XqCxea/4OcDR3liokLQVeB44B2J6xfTNvqsq6gKcldQFLgKsNfv+R1bHw+4DXJJ2R9KukdbkDNSJpELhi+1zuLP/RTuCH3CEeYDlwed72FG1QnPNJ6gHWAu2wCv0XpAuXudxBKuoFpoHjxTTUUUnP5A7ViO0rpNmLS8A14F/bp5p93CxP2patokXKtIw0DF4HfCOp15m/TtQg80FKVvXKpcpqZZI+JU0/jLQy20PQAvvaYhQFIOlZ4FvgQ9u3cucpI2kLcMP2H5LeyJ2noi6gH9hj+4ykI8AnwGd5Y5WTtIw0Ul0B3AROSNpme7iZx81S+GWraEnaBYwVBf+bpDnS+yamW5VvIQ/KLGkN6Y92ThKkqZFxSettX29hxPs0Wq1M0nZgCzCQ+wO1xBTw/Lztblow9H0cJD1BKvsR22O581SwERiU9DbwFLBU0rDtbZlzlZkCpmzfGz2Nkgq/7jYD/9ieBpA0BrwKNLXw6zilcxLYBCCpD1hMjV+IZHvC9nO2e2z3kE7A/txl34ikN4H9wKDtO7nzlPgdWClphaTFpBtb32XO1JDSp/8xYNL24dx5qrB9wHZ3cR5vBX6pedlT/J9dlvRCsWsAuJAxUlWXgA2SlhTnygAtuNlct5enAQwBQ5L+AmaA7TW++mxnXwJPAj8XI5PTtt/PG+l+tmclfQD8RPomw5Dt85ljVbEReA+YkPRnse+g7e8zZvq/2gOMFBcEF4EdmfM0VEw/jQLjpCnVs7Tgqdt40jaEEDpEHad0QgghNEEUfgghdIgo/BBC6BBR+CGE0CGi8EMIoUNE4YcQQoeIwg8hhA4RhR9CCB3iLtxoppTS+NmWAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# 导入sklearn模拟二分类数据生成模块\n", "from sklearn.datasets.samples_generator import make_blobs\n", "# 生成模拟二分类数据集\n", "X, y =  make_blobs(n_samples=150, n_features=2, centers=2, cluster_std=1.2, random_state=40)\n", "# 设置颜色参数\n", "colors = {0:'r', 1:'g'}\n", "# 绘制二分类数据集的散点图\n", "plt.scatter(X[:,0], X[:,1], marker='o', c=pd.Series(y).map(colors))\n", "plt.show();"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 将标签转换为1/-1\n", "y_ = y.copy()\n", "y_[y_==0] = -1\n", "y_ = y_.astype(float)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(105, 2) (105,) (45, 2) (45,)\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X, y_, test_size=0.3, random_state=43)\n", "print(X_train.shape, y_train.shape, X_test.shape, y_test.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### cvxopt用法"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from cvxopt import matrix, solvers"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     pcost       dcost       gap    pres   dres\n", " 0:  1.0780e+02 -7.6366e+02  9e+02  0e+00  4e+01\n", " 1:  9.3245e+01  9.7637e+00  8e+01  6e-17  3e+00\n", " 2:  6.7311e+01  3.2553e+01  3e+01  2e-16  1e+00\n", " 3:  2.6071e+01  1.5068e+01  1e+01  2e-16  7e-01\n", " 4:  3.7092e+01  2.3152e+01  1e+01  1e-16  4e-01\n", " 5:  2.5352e+01  1.8652e+01  7e+00  6e-17  3e-16\n", " 6:  2.0062e+01  1.9974e+01  9e-02  9e-17  2e-16\n", " 7:  2.0001e+01  2.0000e+01  9e-04  2e-16  1e-16\n", " 8:  2.0000e+01  2.0000e+01  9e-06  1e-16  3e-16\n", "Optimal solution found.\n"]}], "source": ["# 定义二次规划参数\n", "P = matrix([[1.0,0.0],[0.0,0.0]])\n", "q = matrix([3.0,4.0])\n", "G = matrix([[-1.0,0.0,-1.0,2.0,3.0],[0.0,-1.0,-3.0,5.0,4.0]])\n", "h = matrix([0.0,0.0,-15.0,100.0,80.0])\n", "\n", "# 构建求解\n", "sol = solvers.qp(P, q, G, h)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 7.13e-07]\n", "[ 5.00e+00]\n", " 20.00000617311241\n"]}], "source": ["# 获取最优值\n", "print(sol['x'], sol['primal objective'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 线性可分支持向量机"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["### 实现线性可分支持向量机\n", "### 硬间隔最大化策略\n", "class Hard_Margin_SVM:\n", "    ### 线性可分支持向量机拟合方法\n", "    def fit(self, X, y):\n", "        # 训练样本数和特征数\n", "        m, n = X.shape\n", "\n", "        # 初始化二次规划相关变量：P/q/G/h\n", "        self.P = matrix(np.identity(n + 1, dtype=np.float))\n", "        self.q = matrix(np.zeros((n + 1,), dtype=np.float))\n", "        self.G = matrix(np.zeros((m, n + 1), dtype=np.float))\n", "        self.h = -matrix(np.ones((m,), dtype=np.float))\n", "\n", "        # 将数据转为变量\n", "        self.P[0, 0] = 0\n", "        for i in range(m):\n", "            self.G[i, 0] = -y[i]\n", "            self.G[i, 1:] = -X[i, :] * y[i]\n", "        \n", "        # 构建二次规划求解\n", "        sol = solvers.qp(self.P, self.q, self.G, self.h)\n", "\n", "        # 对权重和偏置寻优\n", "        self.w = np.zeros(n,) \n", "        self.b = sol['x'][0] \n", "        for i in range(1, n + 1):\n", "            self.w[i - 1] = sol['x'][i]\n", "        return self.w, self.b\n", "\n", "    ### 定义模型预测函数\n", "    def predict(self, X):\n", "        return np.sign(np.dot(self.w, X.T) + self.b)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     pcost       dcost       gap    pres   dres\n", " 0:  2.1061e-02  2.0725e+01  3e+02  2e+00  8e+02\n", " 1:  1.7678e-01 -4.0311e+01  5e+01  3e-01  1e+02\n", " 2:  2.5467e-01 -1.3854e+00  2e+00  1e-02  4e+00\n", " 3:  2.1218e-01  4.0205e-02  2e-01  6e-04  2e-01\n", " 4:  1.8309e-01  1.5738e-01  3e-02  8e-05  4e-02\n", " 5:  1.8241e-01  1.8207e-01  3e-04  1e-06  4e-04\n", " 6:  1.8239e-01  1.8239e-01  3e-06  1e-08  4e-06\n", " 7:  1.8239e-01  1.8239e-01  3e-08  1e-10  4e-08\n", "Optimal solution found.\n"]}, {"data": {"text/plain": ["(array([0.40882768, 0.44457681]), 1.8310613288769853)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 创建线性可分支持向量机模型实例\n", "hard_margin_svm = Hard_Margin_SVM()\n", "# 执行训练\n", "hard_margin_svm.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n"]}], "source": ["# 模型预测\n", "y_pred = hard_margin_svm.predict(X_test)\n", "from sklearn.metrics import accuracy_score\n", "# 计算测试集准确率\n", "print(accuracy_score(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from matplotlib.colors import ListedColormap\n", "\n", "### 绘制线性可分支持向量机决策边界图\n", "def plot_classifer(model, X, y):\n", "    # 超参数边界\n", "    x_min = -7\n", "    x_max = 12\n", "    y_min = -12\n", "    y_max = -1\n", "    step = 0.05\n", "    # meshgrid\n", "    xx, yy = np.meshgrid(np.arange(x_min, x_max, step),\n", "                         np.arange(y_min, y_max, step))\n", "    # 模型预测\n", "    z = model.predict(np.c_[xx.ravel(), yy.ravel()])\n", "\n", "    # 定义color map\n", "    cmap_light = ListedColormap(['#FFAAAA', '#AAFFAA'])\n", "    cmap_bold = ListedColormap(['#FF0000', '#003300'])\n", "    z = z.reshape(xx.shape)\n", "\n", "    plt.figure(figsize=(8, 5), dpi=96)\n", "    plt.pcolormesh(xx, yy, z, cmap=cmap_light)\n", "    plt.scatter(X[:, 0], X[:, 1], c=y, cmap=cmap_bold)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 768x480 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_classifer(hard_margin_svm, X_train, y_train)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n"]}], "source": ["# 导入sklearn线性SVM分类模块\n", "from sklearn.svm import LinearSVC\n", "# 创建模型实例\n", "clf = LinearSVC(random_state=0, tol=1e-5)\n", "# 训练\n", "clf.fit(X_train, y_train)\n", "# 预测\n", "y_pred = clf.predict(X_test)\n", "# 计算测试集准确率\n", "print(accuracy_score(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}