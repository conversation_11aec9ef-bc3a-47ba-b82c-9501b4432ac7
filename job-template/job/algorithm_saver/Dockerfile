FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
RUN pip install --no-cache-dir \
    requests==2.31.0 \
    clearml==1.13.2 \
    -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制算法保存器代码
COPY algorithm_saver.py /app/

# 设置执行权限
RUN chmod +x /app/algorithm_saver.py

# 设置默认命令
ENTRYPOINT ["python", "/app/algorithm_saver.py"]
