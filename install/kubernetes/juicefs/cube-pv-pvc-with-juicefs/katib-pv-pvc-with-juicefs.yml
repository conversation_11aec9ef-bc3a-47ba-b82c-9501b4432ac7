

# 模型训练
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: automl-kubeflow-user-workspace
  labels:
    automl-pvname: automl-kubeflow-user-workspace
spec:
  capacity:
    storage: 500Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
  csi:
    driver: csi.juicefs.com
    volumeHandle: automl-kubeflow-user-workspace
    fsType: juicefs
    nodePublishSecretRef:
      name: juicefs-sc-secret
      namespace: kube-system
    volumeAttributes:
      juicefs/mount-cpu-limit: 5000m
      juicefs/mount-memory-limit: 5Gi
      juicefs/mount-cpu-request: 1m
      juicefs/mount-memory-request: 1Mi
  mountOptions:
    - subdir=kubeflow/pipeline/workspace
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kubeflow-user-workspace
  namespace: automl
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      automl-pvname: automl-kubeflow-user-workspace
  storageClassName: ""
  volumeName: automl-kubeflow-user-workspace