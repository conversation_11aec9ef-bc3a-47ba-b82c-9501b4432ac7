---
# Source: gateway/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-ingress
  namespace: istio-ingress
  labels:
    helm.sh/chart: gateway-1.14.1
    app: istio-ingress
    istio: ingress
    app.kubernetes.io/version: "1.14.1"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: istio-ingress
---
# Source: gateway/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istio-ingress
  namespace: istio-ingress
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "watch", "list"]
---
# Source: gateway/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istio-ingress
  namespace: istio-ingress
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istio-ingress
subjects:
- kind: ServiceAccount
  name: istio-ingress
---
# Source: gateway/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: istio-ingress
  namespace: istio-ingress
  labels:
    helm.sh/chart: gateway-1.14.1
    app: istio-ingress
    istio: ingress
    app.kubernetes.io/version: "1.14.1"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: istio-ingress
  annotations:
    {}
spec:
  type: LoadBalancer
  ports:
    - name: status-port
      port: 15021
      protocol: TCP
      targetPort: 15021
    - name: http2
      port: 80
      protocol: TCP
      targetPort: 80
    - name: https
      port: 443
      protocol: TCP
      targetPort: 443
  selector:
    app: istio-ingress
    istio: ingress
---
# Source: gateway/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: istio-ingress
  namespace: istio-ingress
  labels:
    helm.sh/chart: gateway-1.14.1
    app: istio-ingress
    istio: ingress
    app.kubernetes.io/version: "1.14.1"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: istio-ingress
  annotations:
    {}
spec:
  selector:
    matchLabels:
      app: istio-ingress
      istio: ingress
  template:
    metadata:
      annotations:
        inject.istio.io/templates: gateway
        prometheus.io/path: /stats/prometheus
        prometheus.io/port: "15020"
        prometheus.io/scrape: "true"
        sidecar.istio.io/inject: "true"
      labels:
        sidecar.istio.io/inject: "true"
        app: istio-ingress
        istio: ingress
    spec:
      serviceAccountName: istio-ingress
      securityContext:
        # Safe since 1.22: https://github.com/kubernetes/kubernetes/pull/103326
        sysctls:
        - name: net.ipv4.ip_unprivileged_port_start
          value: "0"
      containers:
        - name: istio-proxy
          # "auto" will be populated at runtime by the mutating webhook. See https://istio.io/latest/docs/setup/additional-setup/sidecar-injection/#customizing-injection
          image: auto
          securityContext:
            # Safe since 1.22: https://github.com/kubernetes/kubernetes/pull/103326
            capabilities:
              drop:
              - ALL
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: true
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
          env:
          ports:
          - containerPort: 15090
            protocol: TCP
            name: http-envoy-prom
          resources:
            limits:
              cpu: 2000m
              memory: 1024Mi
            requests:
              cpu: 100m
              memory: 128Mi
---
# Source: gateway/templates/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: istio-ingress
  namespace: istio-ingress
  labels:
    helm.sh/chart: gateway-1.14.1
    app: istio-ingress
    istio: ingress
    app.kubernetes.io/version: "1.14.1"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: istio-ingress
  annotations:
    {}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istio-ingress
  minReplicas: 1
  maxReplicas: 5
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          averageUtilization: 80
          type: Utilization
