if(!self.define){let e,s={};const i=(i,n)=>(i=new URL(i+".js",n).href,s[i]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()})).then((()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e})));self.define=(n,r)=>{const l=e||("document"in self?document.currentScript.src:"")||location.href;if(s[l])return;let t={};const o=e=>i(e,l),c={module:{uri:l},exports:t,require:o};s[l]=Promise.all(n.map((e=>c[e]||o(e)))).then((e=>(r(...e),t)))}}define(["./workbox-2734dd39"],(function(e){"use strict";self.addEventListener("message",(e=>{e.data&&"SKIP_WAITING"===e.data.type&&self.skipWaiting()})),e.precacheAndRoute([{url:"assets/index-136239be.css",revision:null},{url:"assets/index-34463448.js",revision:null},{url:"assets/index-9b29fff7.css",revision:null},{url:"assets/index-c6aca252.js",revision:null},{url:"assets/index-cd402827.css",revision:null},{url:"assets/index-d638dd9d.js",revision:null},{url:"index.html",revision:"074b5482038db08a66550f3fab4b440c"},{url:"registerSW.js",revision:"0df62c0fd12ccfa302985c17b10e417e"},{url:"logo.png",revision:"c9f6e25242944bbea31a96028bb768e7"},{url:"manifest.webmanifest",revision:"97c6f3ecaee5c03e566acd0209114296"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html")))}));
