{"version": 3, "file": "static/js/886.2f1166bd.chunk.js", "mappings": "uNAIe,SAASA,EAAeC,GACnC,OAAsBC,EAAAA,EAAAA,WAASC,EAAAA,EAAAA,IAAS,SAAe,OAALF,QAAK,IAALA,OAAK,EAALA,EAAOG,MAAI,eAAtDA,EAAG,KAAQ,KAClB,OACI,+BACI,mBAAQC,GAAG,2BACPC,IAAKF,EACLG,iBAAe,EACfC,MAAM,0CACNC,UAAU,oBACVC,MAAO,CAAEC,OAAQ,EAAGC,QAAS,YAI7C,C", "sources": ["pages/IframeTemplate.tsx"], "sourcesContent": ["import React, { useState } from 'react'\nimport { IAppMenuItem } from '../api/interface/kubeflowInterface'\nimport { getParam } from '../util'\n\nexport default function IframeTemplate(props?: IAppMenuItem) {\n    const [url, setUrl] = useState(getParam('url') || props?.url)\n    return (\n        <>\n            <iframe id=\"_frontendAppCustomFrame_\"\n                src={url}\n                allowFullScreen\n                allow=\"microphone;camera;midi;encrypted-media;\"\n                className=\"w100 h100 fade-in\"\n                style={{ border: 0, display: 'block'}}>\n            </iframe>\n        </>\n    )\n}\n"], "names": ["IframeTemplate", "props", "useState", "getPara<PERSON>", "url", "id", "src", "allowFullScreen", "allow", "className", "style", "border", "display"], "sourceRoot": ""}