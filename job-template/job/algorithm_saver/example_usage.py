#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
算法保存算子使用示例

本脚本展示了如何在不同场景下使用算法保存算子
"""

import subprocess
import json
import os
from typing import Dict, Any


def run_algorithm_saver(args: Dict[str, Any]) -> bool:
    """
    运行算法保存算子
    
    Args:
        args: 参数字典
        
    Returns:
        bool: 是否成功
    """
    cmd = ["python", "algorithm_saver.py"]
    
    for key, value in args.items():
        if value is not None and value != "":
            if isinstance(value, bool):
                if value:
                    cmd.append(f"--{key}")
            else:
                cmd.extend([f"--{key}", str(value)])
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("ERROR: 执行超时")
        return False
    except Exception as e:
        print(f"ERROR: 执行失败 - {str(e)}")
        return False


def example_basic_usage():
    """示例1: 基本使用方法"""
    print("=" * 60)
    print("示例1: 基本使用方法")
    print("=" * 60)
    
    args = {
        "algo_id": "resnet50_classifier",
        "algo_version": "v1.0",
        "model_path": "/mnt/admin/models/resnet50_trained.pt",
        "framework": "pytorch",
        "inference_framework": "pytorch",
        "description": "基于ResNet50的图像分类模型",
        "model_metrics": json.dumps({
            "accuracy": 0.95,
            "loss": 0.05,
            "f1_score": 0.94
        })
    }
    
    success = run_algorithm_saver(args)
    print(f"执行结果: {'成功' if success else '失败'}")
    print()


def example_clearml_best_task():
    """示例2: 使用ClearML最佳任务"""
    print("=" * 60)
    print("示例2: 使用ClearML最佳任务")
    print("=" * 60)
    
    args = {
        "algo_id": "bert_sentiment",
        "algo_version": "v2.0",
        "model_path": "/mnt/admin/models/bert_model",
        "framework": "tensorflow",
        "inference_framework": "tensorflow",
        "clearml_project_name": "sentiment_analysis_project",
        "use_best_task": True,
        "clearml_metric_name": "f1_score",
        "description": "BERT情感分析模型"
    }
    
    success = run_algorithm_saver(args)
    print(f"执行结果: {'成功' if success else '失败'}")
    print()


def example_specific_clearml_task():
    """示例3: 指定ClearML任务ID"""
    print("=" * 60)
    print("示例3: 指定ClearML任务ID")
    print("=" * 60)
    
    args = {
        "algo_id": "yolo_detection",
        "algo_version": "v3.1",
        "model_path": "/mnt/admin/models/yolo_best.pt",
        "framework": "pytorch",
        "inference_framework": "onnx",
        "clearml_task_id": "abc123def456ghi789",
        "description": "YOLO目标检测模型"
    }
    
    success = run_algorithm_saver(args)
    print(f"执行结果: {'成功' if success else '失败'}")
    print()


def example_sklearn_model():
    """示例4: Scikit-learn模型保存"""
    print("=" * 60)
    print("示例4: Scikit-learn模型保存")
    print("=" * 60)
    
    args = {
        "algo_id": "random_forest_classifier",
        "algo_version": "v1.5",
        "model_path": "/mnt/admin/models/rf_model.pkl",
        "framework": "sklearn",
        "inference_framework": "sklearn",
        "description": "随机森林分类器",
        "model_metrics": json.dumps({
            "accuracy": 0.88,
            "precision": 0.87,
            "recall": 0.89,
            "auc": 0.92
        })
    }
    
    success = run_algorithm_saver(args)
    print(f"执行结果: {'成功' if success else '失败'}")
    print()


def example_with_environment_setup():
    """示例5: 带环境变量设置"""
    print("=" * 60)
    print("示例5: 带环境变量设置")
    print("=" * 60)
    
    # 设置环境变量
    env_vars = {
        "KFJ_TASK_PROJECT_NAME": "ai_research",
        "KFJ_CREATOR": "data_scientist",
        "KFJ_RUN_ID": "run_20231201_001",
        "KFJ_PIPELINE_ID": "pipeline_123",
        "HOST": "http://kubeflow-dashboard.infra",
        "CLEARML_API_HOST": "http://clearml-api:8008"
    }
    
    # 临时设置环境变量
    original_env = {}
    for key, value in env_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    try:
        args = {
            "algo_id": "transformer_nlp",
            "algo_version": "v4.0",
            "model_path": "/mnt/data_scientist/models/transformer_final.bin",
            "framework": "pytorch",
            "inference_framework": "onnx",
            "description": "Transformer自然语言处理模型",
            "model_metrics": json.dumps({
                "bleu_score": 0.78,
                "rouge_l": 0.82,
                "perplexity": 15.6
            })
        }
        
        success = run_algorithm_saver(args)
        print(f"执行结果: {'成功' if success else '失败'}")
        
    finally:
        # 恢复原始环境变量
        for key, value in original_env.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value
    
    print()


def check_result_file():
    """检查结果文件"""
    result_file = "/tmp/algorithm_save_result.json"
    
    if os.path.exists(result_file):
        print("=" * 60)
        print("算法保存结果:")
        print("=" * 60)
        
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        except Exception as e:
            print(f"读取结果文件失败: {str(e)}")
    else:
        print("未找到结果文件")


def main():
    """主函数"""
    print("算法保存算子使用示例")
    print("=" * 60)
    
    # 检查算法保存器是否存在
    if not os.path.exists("algorithm_saver.py"):
        print("ERROR: 未找到 algorithm_saver.py 文件")
        print("请确保在正确的目录下运行此脚本")
        return
    
    # 运行各种示例
    example_basic_usage()
    example_clearml_best_task()
    example_specific_clearml_task()
    example_sklearn_model()
    example_with_environment_setup()
    
    # 检查结果
    check_result_file()
    
    print("所有示例执行完成!")


if __name__ == "__main__":
    main()
