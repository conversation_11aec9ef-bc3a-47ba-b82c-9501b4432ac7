.nodedetail-wapper {

    // .ant-spin-nested-loading{
    //     height: 100%;
    // }
    // .ant-spin-container{
    //     height: 100%;
    // }
    // .ant-tabs{
    //     height: 100%;
    // }
    // .ant-tabs-content{
    //     height: 100%;
    // }
    .ant-drawer-body {
        position: relative;
    }
}

.tree-header {
    // position        : absolute;
    // top             : 0;
    // left            : 0;
    width           : 100%;
    background-color: #434343;
    box-shadow      : 0 1px 4px #0015292b;
    color           : #fff;
    z-index         : 9;

    .header-detail {
        background-color: #f7f9fc;
        color: #000;

        .header-detail-item {
            border-right: 1px solid #cdcdcd;
        }

        .header-detail-item:last-child {
            border-right: none;
        }
    }

    .btn-ghost {
        border       : 1px solid #fff;
        padding      : 2px 16px;
        border-radius: 3px;
        cursor       : pointer;
    }
}