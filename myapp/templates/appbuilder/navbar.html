{% set menu = appbuilder.menu %}
{% set security_manager = appbuilder.sm %}
{% set languages = appbuilder.languages %}
{% set WARNING_MSG = appbuilder.app.config.get('WARNING_MSG') %}
{% set app_icon_height = appbuilder.app.config.get('APP_ICON_HEIGHT', '100%') %}
{% set logo_target_path = appbuilder.app.config.get('LOGO_TARGET_PATH') or '/profile/{}/'.format(current_user.username)
%}

<!-- <div class="navbar navbar-static-top {{menu.extra_classes}}" role="navigation">
  <div class="container-fluid">
    <div class="navbar-header">
      <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
{#      <a class="navbar-brand" href="{{ '/myapp' + logo_target_path if current_user.username is defined else '#' }}">#}
      <a class="navbar-brand" href="/myapp/home">
        <img height="{{ app_icon_height }}" src="/static/assets/images/logo2.svg" alt="{{ appbuilder.app_name }}" />
      </a>
    </div>
    <div class="navbar-collapse collapse">
      <ul class="nav navbar-nav">
        {% if WARNING_MSG %}
        <li class="alert alert-danger">
          {{ WARNING_MSG | safe }}
        </li>
        {% endif %}
        {% include 'appbuilder/navbar_menu.html' %}
      </ul>
      <ul class="nav navbar-nav navbar-right">
        {% include 'appbuilder/navbar_right.html' %}
      </ul>
    </div>
  </div>
</div> -->