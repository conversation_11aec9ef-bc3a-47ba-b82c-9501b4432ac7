apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: clusterworkflowtemplates.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: ClusterWorkflowTemplate
    listKind: ClusterWorkflowTemplateList
    plural: clusterworkflowtemplates
    shortNames:
    - clusterwftmpl
    - cwft
    singular: clusterworkflowtemplate
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: cronworkflows.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: CronWorkflow
    listKind: CronWorkflowList
    plural: cronworkflows
    shortNames:
    - cwf
    - cronwf
    singular: cronworkflow
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
          status:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: workfloweventbindings.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: WorkflowEventBinding
    listKind: WorkflowEventBindingList
    plural: workfloweventbindings
    shortNames:
    - wfeb
    singular: workfloweventbinding
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: workflows.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: Workflow
    listKind: WorkflowList
    plural: workflows
    shortNames:
    - wf
    singular: workflow
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Status of the workflow
      jsonPath: .status.phase
      name: Status
      type: string
    - description: When the workflow was started
      format: date-time
      jsonPath: .status.startedAt
      name: Age
      type: date
    - description: Human readable message indicating details about why the workflow
        is in this condition.
      jsonPath: .status.message
      name: Message
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
          status:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: workflowtaskresults.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: WorkflowTaskResult
    listKind: WorkflowTaskResultList
    plural: workflowtaskresults
    singular: workflowtaskresult
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          message:
            type: string
          metadata:
            type: object
          outputs:
            properties:
              artifacts:
                items:
                  properties:
                    archive:
                      properties:
                        none:
                          type: object
                        tar:
                          properties:
                            compressionLevel:
                              format: int32
                              type: integer
                          type: object
                        zip:
                          type: object
                      type: object
                    archiveLogs:
                      type: boolean
                    artifactGC:
                      properties:
                        strategy:
                          enum:
                          - ""
                          - OnWorkflowCompletion
                          - OnWorkflowDeletion
                          type: string
                      type: object
                    artifactory:
                      properties:
                        passwordSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        url:
                          type: string
                        usernameSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                      required:
                      - url
                      type: object
                    from:
                      type: string
                    fromExpression:
                      type: string
                    gcs:
                      properties:
                        bucket:
                          type: string
                        key:
                          type: string
                        serviceAccountKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                      required:
                      - key
                      type: object
                    git:
                      properties:
                        branch:
                          type: string
                        depth:
                          format: int64
                          type: integer
                        disableSubmodules:
                          type: boolean
                        fetch:
                          items:
                            type: string
                          type: array
                        insecureIgnoreHostKey:
                          type: boolean
                        passwordSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        repo:
                          type: string
                        revision:
                          type: string
                        singleBranch:
                          type: boolean
                        sshPrivateKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        usernameSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                      required:
                      - repo
                      type: object
                    globalName:
                      type: string
                    hdfs:
                      properties:
                        addresses:
                          items:
                            type: string
                          type: array
                        force:
                          type: boolean
                        hdfsUser:
                          type: string
                        krbCCacheSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        krbConfigConfigMap:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        krbKeytabSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        krbRealm:
                          type: string
                        krbServicePrincipalName:
                          type: string
                        krbUsername:
                          type: string
                        path:
                          type: string
                      required:
                      - path
                      type: object
                    http:
                      properties:
                        headers:
                          items:
                            properties:
                              name:
                                type: string
                              value:
                                type: string
                            required:
                            - name
                            - value
                            type: object
                          type: array
                        passwordSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        url:
                          type: string
                        usernameSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                      required:
                      - url
                      type: object
                    mode:
                      format: int32
                      type: integer
                    name:
                      type: string
                    optional:
                      type: boolean
                    oss:
                      properties:
                        accessKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        bucket:
                          type: string
                        createBucketIfNotPresent:
                          type: boolean
                        endpoint:
                          type: string
                        key:
                          type: string
                        lifecycleRule:
                          properties:
                            markDeletionAfterDays:
                              format: int32
                              type: integer
                            markInfrequentAccessAfterDays:
                              format: int32
                              type: integer
                          type: object
                        secretKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        securityToken:
                          type: string
                      required:
                      - key
                      type: object
                    path:
                      type: string
                    raw:
                      properties:
                        data:
                          type: string
                      required:
                      - data
                      type: object
                    recurseMode:
                      type: boolean
                    s3:
                      properties:
                        accessKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        bucket:
                          type: string
                        createBucketIfNotPresent:
                          properties:
                            objectLocking:
                              type: boolean
                          type: object
                        encryptionOptions:
                          properties:
                            enableEncryption:
                              type: boolean
                            kmsEncryptionContext:
                              type: string
                            kmsKeyId:
                              type: string
                            serverSideCustomerKeySecret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                          type: object
                        endpoint:
                          type: string
                        insecure:
                          type: boolean
                        key:
                          type: string
                        region:
                          type: string
                        roleARN:
                          type: string
                        secretKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        useSDKCreds:
                          type: boolean
                      type: object
                    subPath:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              exitCode:
                type: string
              parameters:
                items:
                  properties:
                    default:
                      type: string
                    description:
                      type: string
                    enum:
                      items:
                        type: string
                      type: array
                    globalName:
                      type: string
                    name:
                      type: string
                    value:
                      type: string
                    valueFrom:
                      properties:
                        configMapKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                        default:
                          type: string
                        event:
                          type: string
                        expression:
                          type: string
                        jqFilter:
                          type: string
                        jsonPath:
                          type: string
                        parameter:
                          type: string
                        path:
                          type: string
                        supplied:
                          type: object
                      type: object
                  required:
                  - name
                  type: object
                type: array
              result:
                type: string
            type: object
          phase:
            type: string
          progress:
            type: string
        required:
        - metadata
        type: object
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: workflowtasksets.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: WorkflowTaskSet
    listKind: WorkflowTaskSetList
    plural: workflowtasksets
    shortNames:
    - wfts
    singular: workflowtaskset
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
          status:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: workflowtemplates.argoproj.io
spec:
  group: argoproj.io
  names:
    kind: WorkflowTemplate
    listKind: WorkflowTemplateList
    plural: workflowtemplates
    shortNames:
    - wftmpl
    singular: workflowtemplate
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            x-kubernetes-map-type: atomic
            x-kubernetes-preserve-unknown-fields: true
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo
  namespace: infra
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-server
  namespace: infra
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: github.com
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: agent
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: argo-role
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: argo-server-role
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    workflows.argoproj.io/description: |
      Recomended minimum permissions for the `emissary` executor.
  name: executor
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: pod-manager
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: submit-workflow-template
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    workflows.argoproj.io/description: |
      This is an example of the permissions you would need if you wanted to use a resource template to create and manage
      other workflows. The same pattern would be suitable for other resurces, e.g. a service
  name: workflow-manager
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: argo-clusterworkflowtemplate-role
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: argo-server-clusterworkflowtemplate-role
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: agent-default
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: agent
subjects:
- kind: ServiceAccount
  name: default
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-role
subjects:
- kind: ServiceAccount
  name: argo
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-server-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-server-role
subjects:
- kind: ServiceAccount
  name: argo-server
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: executor-default
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: executor
subjects:
- kind: ServiceAccount
  name: default
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: github.com
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: submit-workflow-template
subjects:
- kind: ServiceAccount
  name: github.com
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: pod-manager-default
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: pod-manager
subjects:
- kind: ServiceAccount
  name: default
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: workflow-manager-default
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: workflow-manager
subjects:
- kind: ServiceAccount
  name: default
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-clusterworkflowtemplate-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-clusterworkflowtemplate-role
subjects:
- kind: ServiceAccount
  name: argo
  namespace: infra
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: argo-server-clusterworkflowtemplate-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: argo-server-clusterworkflowtemplate-role
subjects:
- kind: ServiceAccount
  name: argo-server
  namespace: infra
---
apiVersion: v1
data:
  default-v1: |
    archiveLogs: true
    s3:
      bucket: my-bucket
      endpoint: minio:9000
      insecure: true
      accessKeySecret:
        name: my-minio-cred
        key: accesskey
      secretKeySecret:
        name: my-minio-cred
        key: secretkey
  empty: ""
  my-key: |
    archiveLogs: true
    s3:
      bucket: my-bucket
      endpoint: minio:9000
      insecure: true
      accessKeySecret:
        name: my-minio-cred
        key: accesskey
      secretKeySecret:
        name: my-minio-cred
        key: secretkey
kind: ConfigMap
metadata:
  annotations:
    workflows.argoproj.io/default-artifact-repository: default-v1
  name: artifact-repositories
  namespace: infra
---
apiVersion: v1
data:
  artifactRepository: |
    s3:
      bucket: my-bucket
      endpoint: minio:9000
      insecure: true
      accessKeySecret:
        name: my-minio-cred
        key: accesskey
      secretKeySecret:
        name: my-minio-cred
        key: secretkey
  executor: |
    resources:
      requests:
        cpu: 10m
        memory: 64Mi
  images: |
    docker/whalesay:latest:
       cmd: [cowsay]
  links: |
    - name: Workflow Link
      scope: workflow
      url: /pipeline_modelview/api/web/${workflow.metadata.labels.pipeline-id}
    - name: Pod Link
      scope: pod
      url: /pipeline_modelview/api/web/pod/${workflow.metadata.labels.pipeline-id}
    - name: Pod Logs Link
      scope: pod-logs
      url: http://logging-facility?namespace=${metadata.namespace}&podName=${metadata.name}&startedAt=${status.startedAt}&finishedAt=${status.finishedAt}
  metricsConfig: |
    enabled: true
    path: /metrics
    port: 9090
  namespaceParallelism: "10"
  persistence: |
    connectionPool:
      maxIdleConns: 100
      maxOpenConns: 0
      connMaxLifetime: 0s
    nodeStatusOffLoad: true
    archive: true
    archiveTTL: 7d
    mysql:
      host: mysql-service.infra
      port: 3306
      database: argo
      tableName: argo_workflows
      userNameSecret:
        name: argo-mysql-config
        key: username
      passwordSecret:
        name: argo-mysql-config
        key: password
  retentionPolicy: |
    completed: 10
    failed: 3
    errored: 3
kind: ConfigMap
metadata:
  name: workflow-controller-configmap
  namespace: infra
---
apiVersion: v1
kind: Secret
metadata:
  labels:
    app: mysql
  name: argo-mysql-config
  namespace: infra
stringData:
  password: admin
  username: root
type: Opaque
---
apiVersion: v1
kind: Secret
metadata:
  name: argo-workflows-webhook-clients
  namespace: infra
stringData:
  bitbucket.org: |
    type: bitbucket
    secret: "my-uuid"
  bitbucketserver: |
    type: bitbucketserver
    secret: "shh!"
  github.com: |
    type: github
    secret: "shh!"
  gitlab.com: |-
    type: gitlab
    secret: "shh!"
---
apiVersion: v1
kind: Secret
metadata:
  labels:
    app: minio
  name: my-minio-cred
  namespace: infra
stringData:
  accesskey: admin
  secretkey: password
type: Opaque
---
apiVersion: v1
kind: Service
metadata:
  name: argo-server
  namespace: infra
spec:
  ports:
  - name: web
    port: 2746
    targetPort: 2746
  selector:
    app: argo-server
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: minio
  name: minio
  namespace: infra
spec:
  ports:
  - name: api
    port: 9000
    protocol: TCP
    targetPort: 9000
  - name: dashboard
    port: 9001
    protocol: TCP
    targetPort: 9001
  selector:
    app: minio
---
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    workflows.argoproj.io/description: |
      This service is deprecated. It will be removed in v3.4.

      https://github.com/argoproj/argo-workflows/issues/8441
  labels:
    app: workflow-controller
  name: workflow-controller-metrics
  namespace: infra
spec:
  ports:
  - name: metrics
    port: 9090
    protocol: TCP
    targetPort: 9090
  selector:
    app: workflow-controller
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: argo-server
  namespace: infra
spec:
  selector:
    matchLabels:
      app: argo-server
  template:
    metadata:
      labels:
        app: argo-server
    spec:
      containers:
      - args:
        - server
        - --namespaced
        - --auth-mode
        - server
        - --auth-mode
        - client
        - --managed-namespace
        - pipeline
        env:
        - name: BASE_HREF
          value: /argo/
#        - name: ARGO_HTTP1
#          value: 'true'
        - name: ARGO_SECURE
          value: 'false'
        image: argoproj/argocli:v3.4.3   # v3.3.5
        imagePullPolicy: IfNotPresent
        name: argo-server
        ports:
        - containerPort: 2746
          name: web
#        readinessProbe:
#          httpGet:
#            path: /
#            port: 2746
#            scheme: HTTPS
#          initialDelaySeconds: 10
#          periodSeconds: 20
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
        volumeMounts:
        - mountPath: /tmp
          name: tmp
      nodeSelector:
        kubeflow: 'true'
      securityContext:
        runAsNonRoot: true
      serviceAccountName: argo-server
      volumes:
      - emptyDir: {}
        name: tmp
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: minio
  name: minio
  namespace: infra
spec:
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      nodeSelector:
        kubeflow: 'true'
      containers:
      - command:
        - minio
        - server
        - --console-address
        - :9001
        - /data
        env:
        - name: MINIO_ACCESS_KEY
          value: admin
        - name: MINIO_SECRET_KEY
          value: password
        image: minio/minio:RELEASE.2023-04-20T17-56-55Z
        imagePullPolicy: IfNotPresent
        lifecycle:
          postStart:
            exec:
              command:
              - mkdir
              - -p
              - /data/my-bucket
        livenessProbe:
          httpGet:
            path: /minio/health/live
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 10
        name: main
        ports:
        - containerPort: 9000
          name: api
        - containerPort: 9001
          name: dashboard
        readinessProbe:
          httpGet:
            path: /minio/health/ready
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 10
---

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-controller
  namespace: infra
spec:
  selector:
    matchLabels:
      app: workflow-controller
  template:
    metadata:
      labels:
        app: workflow-controller
    spec:
      containers:
      - args:
        - --namespaced
        - --managed-namespace
        - pipeline
        command:
        - workflow-controller
        env:
        - name: LEADER_ELECTION_IDENTITY
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        image: argoproj/workflow-controller:v3.4.3
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /healthz
            port: 6060
          initialDelaySeconds: 90
          periodSeconds: 60
          timeoutSeconds: 30
        name: workflow-controller
        ports:
        - containerPort: 9090
          name: metrics
        - containerPort: 6060
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
      nodeSelector:
        kubeflow: 'true'
      securityContext:
        runAsNonRoot: true
      serviceAccountName: argo



