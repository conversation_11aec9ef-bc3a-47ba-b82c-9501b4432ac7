---
# Source: alluxio/templates/fuse/daemonset.yaml
#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#



apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: alluxio-fuse
  labels:
    app: alluxio
    chart: alluxio-0.6.53
    release: alluxio
    heritage: Helm
    role: alluxio-fuse
spec:
  selector:
    matchLabels:
      app: alluxio
      chart: alluxio-0.6.53
      release: alluxio
      heritage: Helm
      role: alluxio-fuse
  template:
    metadata:
      labels:
        app: alluxio
        chart: alluxio-0.6.53
        release: alluxio
        heritage: Helm
        role: alluxio-fuse
    spec:
      hostNetwork: true
      hostPID: true
      dnsPolicy: ClusterFirstWithHostNet
      nodeSelector:
      tolerations:
      securityContext:
        runAsUser: 0
        runAsGroup: 0
        fsGroup: 0
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - alluxio
              - key: role
                operator: In
                values:
                - alluxio-worker
            topologyKey: kubernetes.io/hostname
      containers:
        - name: alluxio-fuse
          image: alluxio/alluxio:2.9.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 4
              memory: 4Gi
            requests:
              cpu: 0.5
              memory: 1Gi
          command: ["/entrypoint.sh"]
          args:
            - fuse
            - --fuse-opts=allow_other
            - /mnt/alluxio-fuse
            - /
          env:
          - name: ALLUXIO_CLIENT_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          securityContext:
            privileged: true
            capabilities:
              add:
                - SYS_ADMIN
          lifecycle:
            preStop:
              exec:
                command: ["/opt/alluxio/integration/fuse/bin/alluxio-fuse", "unmount", "/mnt/alluxio-fuse"]
          envFrom:
          - configMapRef:
              name: alluxio-config
          livenessProbe:
            exec:
              command:
              - /bin/bash
              - -c
              - cd /mnt/alluxio-fuse
            initialDelaySeconds: 15
            periodSeconds: 30
            failureThreshold: 2
          volumeMounts:
            - name: alluxio-fuse-device
              mountPath: /dev/fuse
            - name: alluxio-fuse-mount
              mountPath: /mnt
              mountPropagation: Bidirectional
            - name: alluxio-domain
              mountPath: /opt/domain
      restartPolicy: Always
      volumes:
        - name: alluxio-fuse-device
          hostPath:
            path: /dev/fuse
            type: CharDevice
        - name: alluxio-fuse-mount
          hostPath:
            path: /mnt
            type: DirectoryOrCreate
        - name: alluxio-domain
          persistentVolumeClaim:
            claimName: "alluxio-worker-domain-socket"
