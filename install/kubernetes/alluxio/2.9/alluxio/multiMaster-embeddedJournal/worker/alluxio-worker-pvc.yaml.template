---
# Source: alluxio/templates/worker/domain-socket-pvc.yaml
#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: alluxio-worker-domain-socket
  labels:
    app: alluxio
    chart: alluxio-0.6.53
    release: alluxio
    heritage: Helm
    role: alluxio-worker
spec:
  volumeMode: Filesystem
  resources:
    requests:
      storage: 1Mi
  storageClassName: standard
  accessModes:
    - ReadWriteOnce
  selector:
    matchLabels:
      app: alluxio
      release: alluxio
      heritage: Helm
      role: alluxio-worker
