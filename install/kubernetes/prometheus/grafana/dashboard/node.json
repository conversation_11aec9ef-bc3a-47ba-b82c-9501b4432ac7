{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "DCGM_FI_DEV_GPU_UTIL{pod!='',instance=~\"$node.*\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}}，{{pod}} GPU {{gpu}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "node pod GPU Utilization", "tooltip": {"shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "sum by (pod) (rate(container_cpu_usage_seconds_total{job=\"kubelet\", image!=\"\",container_name!=\"POD\",instance=~\"$node.*\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "node pod cpu", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 15}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "exemplar": true, "expr": "sum by(pod) (container_memory_working_set_bytes{job=\"kubelet\", image!=\"\",container_name!=\"POD\",instance=~\"$node.*\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "node pod memory", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 22}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "100 - (avg by (cpu) (irate(node_cpu_seconds_total{job=\"node-exporter\", mode=\"idle\", instance=~\"$node.*\"}[5m])) * 100)\n", "format": "time_series", "interval": "", "intervalFactor": 10, "legendFormat": "{{cpu}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "max": 100, "min": 0, "show": true}, {"format": "percent", "logBase": 1, "max": 100, "min": 0, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 22}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_load1{job=\"node-exporter\", instance=~\"$node.*\"} * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "load 1m", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_load5{job=\"node-exporter\", instance=~\"$node.*\"} * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "load 5m", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_load15{job=\"node-exporter\", instance=~\"$node.*\"} * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "load 15m", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "System load", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "percent", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 29}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_memory_MemTotal_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n- node_memory_MemFree_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n- node_memory_Buffers_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n- node_memory_Cached_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "memory used", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_memory_Buffers_bytes{job=\"node-exporter\", instance=~\"$node.*\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "memory buffers", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_memory_Cached_bytes{job=\"node-exporter\", instance=~\"$node.*\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "memory cached", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_memory_MemFree_bytes{job=\"node-exporter\", instance=~\"$node.*\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "memory free", "refId": "D"}], "thresholds": [], "timeRegions": [], "title": "Memory Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 29}, "id": 5, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(\n  node_memory_MemTotal_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n- node_memory_MemFree_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n- node_memory_Buffers_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n- node_memory_Cached_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n) * 100\n  /\nnode_memory_MemTotal_bytes{job=\"node-exporter\", instance=~\"$node.*\"}\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A"}], "title": "Memory Usage", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 36}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "read", "yaxis": 1}, {"alias": "io time", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum by (instance) (rate(node_disk_bytes_read{job=\"node-exporter\", instance=~\"$node.*\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "read", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum by (instance) (rate(node_disk_bytes_written{job=\"node-exporter\", instance=~\"$node.*\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "written", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum by (instance) (rate(node_disk_io_time_seconds_total{job=\"node-exporter\",  instance=~\"$node.*\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "io time", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Disk I/O", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "ms", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 36}, "id": 7, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "(\n  sum(node_filesystem_size_bytes{job=\"node-exporter\", device!=\"rootfs\", instance=~\"$node.*\"})\n- sum(node_filesystem_avail_bytes{job=\"node-exporter\", device!=\"rootfs\", instance=~\"$node.*\"})\n) * 100\n  /\nsum(node_filesystem_size_bytes{job=\"node-exporter\", device!=\"rootfs\", instance=~\"$node.*\"})\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A"}], "title": "Disk Space Usage", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 43}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(node_network_receive_bytes_total{job=\"node-exporter\", instance=~\"$node.*\", device!~\"lo\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Network Received", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 43}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(node_network_transmit_bytes_total{job=\"node-exporter\", instance=~\"$node.*\", device!~\"lo\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Network Transmitted", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 50}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(device) (rate(node_infiniband_port_data_received_bytes_total{job=\"node-exporter\", instance=~\"$node.*\"}[$__rate_interval]))", "hide": false, "legendFormat": "{{device}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "IB Received", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 50}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(device) (rate(node_infiniband_port_data_transmitted_bytes_total{job=\"node-exporter\", instance=~\"$node.*\"}[$__rate_interval]))", "hide": false, "legendFormat": "{{device}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "IB Transmitted", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "***********", "value": "***********"}, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "definition": "label_values(node_boot_time_seconds{job=\"node-exporter\"}, instance)", "hide": 0, "includeAll": false, "multi": false, "name": "node", "options": [], "query": {"query": "label_values(node_boot_time_seconds{job=\"node-exporter\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "(.*):.*", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "node", "uid": "node", "version": 2, "weekStart": ""}