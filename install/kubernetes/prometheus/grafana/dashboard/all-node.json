{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(instance, gpu) (DCGM_FI_DEV_GPU_UTIL)", "hide": false, "legendFormat": " {{instance}} GPU {{gpu}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "GPU Usage", "tooltip": {"shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [50000], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "5m", "handler": 1, "message": "负载过高", "name": "System load alert", "noDataState": "no_data", "notifications": [{"uid": "FMG-3Bv7k"}]}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 8}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_load1{job=\"node-exporter\"} * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_load5{job=\"node-exporter\"} * 100", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_load15{job=\"node-exporter\"} * 100", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "C"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 50000, "visible": true}], "timeRegions": [], "title": "System load", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "percent", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 8}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{job=\"node-exporter\", mode=\"idle\"}[5m])) * 100)\n", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "CPU Usage", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "max": 100, "min": 0, "show": true}, {"format": "percent", "logBase": 1, "max": 100, "min": 0, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 8}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "node_memory_MemTotal_bytes{job=\"node-exporter\"}\n- node_memory_MemFree_bytes{job=\"node-exporter\"}\n- node_memory_Buffers_bytes{job=\"node-exporter\"}\n- node_memory_Cached_bytes{job=\"node-exporter\"}\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Memory Usage", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 17}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(instance) (rate(node_network_receive_bytes_total{job=\"node-exporter\", device!~\"lo\"}[$__rate_interval]))", "hide": false, "legendFormat": "{{instance}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Network Received", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 17}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(instance) (rate(node_network_transmit_bytes_total{job=\"node-exporter\", device!~\"lo\"}[$__rate_interval]))", "hide": false, "legendFormat": "{{instance}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Network Transmitted", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 24}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(instance, device) (rate(node_infiniband_port_data_received_bytes_total{job=\"node-exporter\"}[$__rate_interval]))", "hide": false, "legendFormat": "{{instance}}:{{device}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "IB Received", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 24}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(instance, device) (rate(node_infiniband_port_data_transmitted_bytes_total{job=\"node-exporter\"}[$__rate_interval]))", "hide": false, "legendFormat": "{{instance}}:{{device}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "IB Transmitted", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 31}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "read", "yaxis": 1}, {"alias": "io time", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(instance) (rate(node_disk_read_bytes_total{job=\"node-exporter\"}[$__rate_interval]))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Disk Read I/O", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "ms", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 31}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "read", "yaxis": 1}, {"alias": "io time", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "builder", "expr": "sum by(instance) (rate(node_disk_written_bytes_total{job=\"node-exporter\"}[$__rate_interval]))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "D"}], "thresholds": [], "timeRegions": [], "title": "Disk Write I/O", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "ms", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "all-node", "uid": "all-node", "version": 1, "weekStart": ""}