"use strict";(self.webpackChunkvite_ml_platform=self.webpackChunkvite_ml_platform||[]).push([[392],{57392:function(e,t,n){n.r(t),n.d(t,{setupMode:function(){return cn}});var r,i=n(38201),o=function(){function e(e){var t=this;this._defaults=e,this._worker=null,this._idleCheckInterval=setInterval((function(){return t._checkIfIdle()}),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange((function(){return t._stopWorker()}))}return e.prototype._stopWorker=function(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null},e.prototype.dispose=function(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()},e.prototype._checkIfIdle=function(){this._worker&&(Date.now()-this._lastUsedTime>12e4&&this._stopWorker())},e.prototype._getClient=function(){return this._lastUsedTime=Date.now(),this._client||(this._worker=i.j6.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client},e.prototype.getLanguageServiceWorker=function(){for(var e,t=this,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return this._getClient().then((function(t){e=t})).then((function(e){return t._worker.withSyncedResources(n)})).then((function(t){return e}))},e}();function a(e,t){void 0===t&&(t=!1);var n=e.length,r=0,i="",o=0,a=16,f=0,l=0,h=0,p=0,d=0;function m(t,n){for(var i=0,o=0;i<t||!n;){var a=e.charCodeAt(r);if(a>=48&&a<=57)o=16*o+a-48;else if(a>=65&&a<=70)o=16*o+a-65+10;else{if(!(a>=97&&a<=102))break;o=16*o+a-97+10}r++,i++}return i<t&&(o=-1),o}function g(){if(i="",d=0,o=r,l=f,p=h,r>=n)return o=n,a=17;var t=e.charCodeAt(r);if(s(t)){do{r++,i+=String.fromCharCode(t),t=e.charCodeAt(r)}while(s(t));return a=15}if(u(t))return r++,i+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,i+="\n"),f++,h=r,a=14;switch(t){case 123:return r++,a=1;case 125:return r++,a=2;case 91:return r++,a=3;case 93:return r++,a=4;case 58:return r++,a=6;case 44:return r++,a=5;case 34:return r++,i=function(){for(var t="",i=r;;){if(r>=n){t+=e.substring(i,r),d=2;break}var o=e.charCodeAt(r);if(34===o){t+=e.substring(i,r),r++;break}if(92!==o){if(o>=0&&o<=31){if(u(o)){t+=e.substring(i,r),d=2;break}d=6}r++}else{if(t+=e.substring(i,r),++r>=n){d=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var a=m(4,!0);a>=0?t+=String.fromCharCode(a):d=4;break;default:d=5}i=r}}return t}(),a=10;case 47:var g=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!u(e.charCodeAt(r));)r++;return i=e.substring(g,r),a=12}if(42===e.charCodeAt(r+1)){r+=2;for(var y=n-1,b=!1;r<y;){var x=e.charCodeAt(r);if(42===x&&47===e.charCodeAt(r+1)){r+=2,b=!0;break}r++,u(x)&&(13===x&&10===e.charCodeAt(r)&&r++,f++,h=r)}return b||(r++,d=1),i=e.substring(g,r),a=13}return i+=String.fromCharCode(t),r++,a=16;case 45:if(i+=String.fromCharCode(t),++r===n||!c(e.charCodeAt(r)))return a=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=function(){var t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&c(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(!(++r<e.length&&c(e.charCodeAt(r))))return d=3,e.substring(t,r);for(r++;r<e.length&&c(e.charCodeAt(r));)r++}var n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if((++r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&c(e.charCodeAt(r))){for(r++;r<e.length&&c(e.charCodeAt(r));)r++;n=r}else d=3;return e.substring(t,n)}(),a=11;default:for(;r<n&&v(t);)r++,t=e.charCodeAt(r);if(o!==r){switch(i=e.substring(o,r)){case"true":return a=8;case"false":return a=9;case"null":return a=7}return a=16}return i+=String.fromCharCode(t),r++,a=16}}function v(e){if(s(e)||u(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,i="",o=0,a=16,d=0},getPosition:function(){return r},scan:t?function(){var e;do{e=g()}while(e>=12&&e<=15);return e}:g,getToken:function(){return a},getTokenValue:function(){return i},getTokenOffset:function(){return o},getTokenLength:function(){return r-o},getTokenStartLine:function(){return l},getTokenStartCharacter:function(){return o-p},getTokenError:function(){return d}}}function s(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function u(e){return 10===e||13===e||8232===e||8233===e}function c(e){return e>=48&&e<=57}function f(e,t,n){void 0===n&&(n=r.DEFAULT);var i=a(e,!1);function o(e){return e?function(){return e(i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}function s(e){return e?function(t){return e(t,i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}var u=o(t.onObjectBegin),c=s(t.onObjectProperty),f=o(t.onObjectEnd),l=o(t.onArrayBegin),h=o(t.onArrayEnd),p=s(t.onLiteralValue),d=s(t.onSeparator),m=o(t.onComment),g=s(t.onError),v=n&&n.disallowComments,y=n&&n.allowTrailingComma;function b(){for(;;){var e=i.scan();switch(i.getTokenError()){case 4:x(14);break;case 5:x(15);break;case 3:x(13);break;case 1:v||x(11);break;case 2:x(12);break;case 6:x(16)}switch(e){case 12:case 13:v?x(10):m();break;case 16:x(1);break;case 15:case 14:break;default:return e}}}function x(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),g(e),t.length+n.length>0)for(var r=i.getToken();17!==r;){if(-1!==t.indexOf(r)){b();break}if(-1!==n.indexOf(r))break;r=b()}}function A(e){var t=i.getTokenValue();return e?p(t):c(t),b(),!0}function C(){switch(i.getToken()){case 3:return function(){l(),b();for(var e=!1;4!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(e||x(4,[],[]),d(","),b(),4===i.getToken()&&y)break}else e&&x(6,[],[]);C()||x(4,[],[4,5]),e=!0}return h(),4!==i.getToken()?x(8,[4],[]):b(),!0}();case 1:return function(){u(),b();for(var e=!1;2!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(e||x(4,[],[]),d(","),b(),2===i.getToken()&&y)break}else e&&x(6,[],[]);(10!==i.getToken()?(x(3,[],[2,5]),0):(A(!1),6===i.getToken()?(d(":"),b(),C()||x(4,[],[2,5])):x(5,[],[2,5]),1))||x(4,[],[2,5]),e=!0}return f(),2!==i.getToken()?x(7,[2],[]):b(),!0}();case 10:return A(!0);default:return function(){switch(i.getToken()){case 11:var e=i.getTokenValue(),t=Number(e);isNaN(t)&&(x(2),t=0),p(t);break;case 7:p(null);break;case 8:p(!0);break;case 9:p(!1);break;default:return!1}return b(),!0}()}}return b(),17===i.getToken()?!!n.allowEmptyContent||(x(4,[],[]),!1):C()?(17!==i.getToken()&&x(9,[],[]),!0):(x(4,[],[]),!1)}!function(e){e.DEFAULT={allowTrailingComma:!1}}(r||(r={}));var l,h,p,d,m,g,v,y,b,x,A,C,S,k,w,I,j,E,T,O,_,M,P,V,N,F,R=a,L=function(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=r.DEFAULT);var i=null,o=[],a=[];function s(e){Array.isArray(o)?o.push(e):null!==i&&(o[i]=e)}return f(e,{onObjectBegin:function(){var e={};s(e),a.push(o),o=e,i=null},onObjectProperty:function(e){i=e},onObjectEnd:function(){o=a.pop()},onArrayBegin:function(){var e=[];s(e),a.push(o),o=e,i=null},onArrayEnd:function(){o=a.pop()},onLiteralValue:s,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},n),o[0]},$=function e(t,n,r){if(void 0===r&&(r=!1),function(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){var i=t.children;if(Array.isArray(i))for(var o=0;o<i.length&&i[o].offset<=n;o++){var a=e(i[o],n,r);if(a)return a}return t}},D=function e(t){if(!t.parent||!t.parent.children)return[];var n=e(t.parent);if("property"===t.parent.type){var r=t.parent.children[0].value;n.push(r)}else if("array"===t.parent.type){var i=t.parent.children.indexOf(t);-1!==i&&n.push(i)}return n},U=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":for(var n=Object.create(null),r=0,i=t.children;r<i.length;r++){var o=i[r],a=o.children[1];a&&(n[o.children[0].value]=e(a))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}};function W(e,t){if(e===t)return!0;if(null===e||void 0===e||null===t||void 0===t)return!1;if(typeof e!==typeof t)return!1;if("object"!==typeof e)return!1;if(Array.isArray(e)!==Array.isArray(t))return!1;var n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!W(e[n],t[n]))return!1}else{var i=[];for(r in e)i.push(r);i.sort();var o=[];for(r in t)o.push(r);if(o.sort(),!W(i,o))return!1;for(n=0;n<i.length;n++)if(!W(e[i[n]],t[i[n]]))return!1}return!0}function q(e){return"number"===typeof e}function B(e){return"undefined"!==typeof e}function K(e){return"boolean"===typeof e}!function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647}(l||(l={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647}(h||(h={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=h.MAX_VALUE),t===Number.MAX_VALUE&&(t=h.MAX_VALUE),{line:e,character:t}},e.is=function(e){var t=e;return we.objectLiteral(t)&&we.uinteger(t.line)&&we.uinteger(t.character)}}(p||(p={})),function(e){e.create=function(e,t,n,r){if(we.uinteger(e)&&we.uinteger(t)&&we.uinteger(n)&&we.uinteger(r))return{start:p.create(e,t),end:p.create(n,r)};if(p.is(e)&&p.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+n+", "+r+"]")},e.is=function(e){var t=e;return we.objectLiteral(t)&&p.is(t.start)&&p.is(t.end)}}(d||(d={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){var t=e;return we.defined(t)&&d.is(t.range)&&(we.string(t.uri)||we.undefined(t.uri))}}(m||(m={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){var t=e;return we.defined(t)&&d.is(t.targetRange)&&we.string(t.targetUri)&&(d.is(t.targetSelectionRange)||we.undefined(t.targetSelectionRange))&&(d.is(t.originSelectionRange)||we.undefined(t.originSelectionRange))}}(g||(g={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return we.numberRange(t.red,0,1)&&we.numberRange(t.green,0,1)&&we.numberRange(t.blue,0,1)&&we.numberRange(t.alpha,0,1)}}(v||(v={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){var t=e;return d.is(t.range)&&v.is(t.color)}}(y||(y={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){var t=e;return we.string(t.label)&&(we.undefined(t.textEdit)||E.is(t))&&(we.undefined(t.additionalTextEdits)||we.typedArray(t.additionalTextEdits,E.is))}}(b||(b={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(x||(x={})),function(e){e.create=function(e,t,n,r,i){var o={startLine:e,endLine:t};return we.defined(n)&&(o.startCharacter=n),we.defined(r)&&(o.endCharacter=r),we.defined(i)&&(o.kind=i),o},e.is=function(e){var t=e;return we.uinteger(t.startLine)&&we.uinteger(t.startLine)&&(we.undefined(t.startCharacter)||we.uinteger(t.startCharacter))&&(we.undefined(t.endCharacter)||we.uinteger(t.endCharacter))&&(we.undefined(t.kind)||we.string(t.kind))}}(A||(A={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){var t=e;return we.defined(t)&&m.is(t.location)&&we.string(t.message)}}(C||(C={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(S||(S={})),function(e){e.Unnecessary=1,e.Deprecated=2}(k||(k={})),function(e){e.is=function(e){var t=e;return void 0!==t&&null!==t&&we.string(t.href)}}(w||(w={})),function(e){e.create=function(e,t,n,r,i,o){var a={range:e,message:t};return we.defined(n)&&(a.severity=n),we.defined(r)&&(a.code=r),we.defined(i)&&(a.source=i),we.defined(o)&&(a.relatedInformation=o),a},e.is=function(e){var t,n=e;return we.defined(n)&&d.is(n.range)&&we.string(n.message)&&(we.number(n.severity)||we.undefined(n.severity))&&(we.integer(n.code)||we.string(n.code)||we.undefined(n.code))&&(we.undefined(n.codeDescription)||we.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(we.string(n.source)||we.undefined(n.source))&&(we.undefined(n.relatedInformation)||we.typedArray(n.relatedInformation,C.is))}}(I||(I={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={title:e,command:t};return we.defined(n)&&n.length>0&&(i.arguments=n),i},e.is=function(e){var t=e;return we.defined(t)&&we.string(t.title)&&we.string(t.command)}}(j||(j={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){var t=e;return we.objectLiteral(t)&&we.string(t.newText)&&d.is(t.range)}}(E||(E={})),function(e){e.create=function(e,t,n){var r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){var t=e;return void 0!==t&&we.objectLiteral(t)&&we.string(t.label)&&(we.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(we.string(t.description)||void 0===t.description)}}(T||(T={})),function(e){e.is=function(e){return"string"===typeof e}}(O||(O={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){var t=e;return E.is(t)&&(T.is(t.annotationId)||O.is(t.annotationId))}}(_||(_={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){var t=e;return we.defined(t)&&H.is(t.textDocument)&&Array.isArray(t.edits)}}(M||(M={})),function(e){e.create=function(e,t,n){var r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"create"===t.kind&&we.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||we.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||we.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||O.is(t.annotationId))}}(P||(P={})),function(e){e.create=function(e,t,n,r){var i={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==r&&(i.annotationId=r),i},e.is=function(e){var t=e;return t&&"rename"===t.kind&&we.string(t.oldUri)&&we.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||we.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||we.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||O.is(t.annotationId))}}(V||(V={})),function(e){e.create=function(e,t,n){var r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"delete"===t.kind&&we.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||we.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||we.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||O.is(t.annotationId))}}(N||(N={})),function(e){e.is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return we.string(e.kind)?P.is(e)||V.is(e)||N.is(e):M.is(e)})))}}(F||(F={}));var J,z,H,G,X,Z,Q,Y,ee,te,ne,re,ie,oe,ae,se,ue,ce,fe,le,he,pe,de,me,ge,ve,ye,be,xe,Ae,Ce=function(){function e(e,t){this.edits=e,this.changeAnnotations=t}return e.prototype.insert=function(e,t,n){var r,i;if(void 0===n?r=E.insert(e,t):O.is(n)?(i=n,r=_.insert(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=_.insert(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.replace=function(e,t,n){var r,i;if(void 0===n?r=E.replace(e,t):O.is(n)?(i=n,r=_.replace(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=_.replace(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.delete=function(e,t){var n,r;if(void 0===t?n=E.del(e):O.is(t)?(r=t,n=_.del(e,t)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(t),n=_.del(e,r)),this.edits.push(n),void 0!==r)return r},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e.prototype.assertChangeAnnotations=function(e){if(void 0===e)throw new Error("Text edit change is not configured to manage change annotations.")},e}(),Se=function(){function e(e){this._annotations=void 0===e?Object.create(null):e,this._counter=0,this._size=0}return e.prototype.all=function(){return this._annotations},Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.manage=function(e,t){var n;if(O.is(e)?n=e:(n=this.nextId(),t=e),void 0!==this._annotations[n])throw new Error("Id "+n+" is already in use.");if(void 0===t)throw new Error("No annotation provided for id "+n);return this._annotations[n]=t,this._size++,n},e.prototype.nextId=function(){return this._counter++,this._counter.toString()},e}();!function(){function e(e){var t=this;this._textEditChanges=Object.create(null),void 0!==e?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new Se(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach((function(e){if(M.is(e)){var n=new Ce(e.edits,t._changeAnnotations);t._textEditChanges[e.textDocument.uri]=n}}))):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new Ce(e.changes[n]);t._textEditChanges[n]=r}))):this._workspaceEdit={}}Object.defineProperty(e.prototype,"edit",{get:function(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(0===this._changeAnnotations.size?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),e.prototype.getTextEditChange=function(e){if(H.is(e)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t={uri:e.uri,version:e.version};if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:i=[]};this._workspaceEdit.documentChanges.push(n),r=new Ce(i,this._changeAnnotations),this._textEditChanges[t.uri]=r}return r}if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var i=[];this._workspaceEdit.changes[e]=i,r=new Ce(i),this._textEditChanges[e]=r}return r},e.prototype.initDocumentChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new Se,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},e.prototype.initChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))},e.prototype.createFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,o;if(T.is(t)||O.is(t)?r=t:n=t,void 0===r?i=P.create(e,n):(o=O.is(r)?r:this._changeAnnotations.manage(r),i=P.create(e,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o},e.prototype.renameFile=function(e,t,n,r){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var i,o,a;if(T.is(n)||O.is(n)?i=n:r=n,void 0===i?o=V.create(e,t,r):(a=O.is(i)?i:this._changeAnnotations.manage(i),o=V.create(e,t,r,a)),this._workspaceEdit.documentChanges.push(o),void 0!==a)return a},e.prototype.deleteFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,o;if(T.is(t)||O.is(t)?r=t:n=t,void 0===r?i=N.create(e,n):(o=O.is(r)?r:this._changeAnnotations.manage(r),i=N.create(e,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o}}();!function(e){e.create=function(e){return{uri:e}},e.is=function(e){var t=e;return we.defined(t)&&we.string(t.uri)}}(J||(J={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return we.defined(t)&&we.string(t.uri)&&we.integer(t.version)}}(z||(z={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return we.defined(t)&&we.string(t.uri)&&(null===t.version||we.integer(t.version))}}(H||(H={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){var t=e;return we.defined(t)&&we.string(t.uri)&&we.string(t.languageId)&&we.integer(t.version)&&we.string(t.text)}}(G||(G={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(X||(X={})),function(e){e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(X||(X={})),function(e){e.is=function(e){var t=e;return we.objectLiteral(e)&&X.is(t.kind)&&we.string(t.value)}}(Z||(Z={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(Q||(Q={})),function(e){e.PlainText=1,e.Snippet=2}(Y||(Y={})),function(e){e.Deprecated=1}(ee||(ee={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){var t=e;return t&&we.string(t.newText)&&d.is(t.insert)&&d.is(t.replace)}}(te||(te={})),function(e){e.asIs=1,e.adjustIndentation=2}(ne||(ne={})),function(e){e.create=function(e){return{label:e}}}(re||(re={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(ie||(ie={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){var t=e;return we.string(t)||we.objectLiteral(t)&&we.string(t.language)&&we.string(t.value)}}(oe||(oe={})),function(e){e.is=function(e){var t=e;return!!t&&we.objectLiteral(t)&&(Z.is(t.contents)||oe.is(t.contents)||we.typedArray(t.contents,oe.is))&&(void 0===e.range||d.is(e.range))}}(ae||(ae={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(se||(se={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={label:e};return we.defined(t)&&(i.documentation=t),we.defined(n)?i.parameters=n:i.parameters=[],i}}(ue||(ue={})),function(e){e.Text=1,e.Read=2,e.Write=3}(ce||(ce={})),function(e){e.create=function(e,t){var n={range:e};return we.number(t)&&(n.kind=t),n}}(fe||(fe={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(le||(le={})),function(e){e.Deprecated=1}(he||(he={})),function(e){e.create=function(e,t,n,r,i){var o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o}}(pe||(pe={})),function(e){e.create=function(e,t,n,r,i,o){var a={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==o&&(a.children=o),a},e.is=function(e){var t=e;return t&&we.string(t.name)&&we.number(t.kind)&&d.is(t.range)&&d.is(t.selectionRange)&&(void 0===t.detail||we.string(t.detail))&&(void 0===t.deprecated||we.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(de||(de={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(me||(me={})),function(e){e.create=function(e,t){var n={diagnostics:e};return void 0!==t&&null!==t&&(n.only=t),n},e.is=function(e){var t=e;return we.defined(t)&&we.typedArray(t.diagnostics,I.is)&&(void 0===t.only||we.typedArray(t.only,we.string))}}(ge||(ge={})),function(e){e.create=function(e,t,n){var r={title:e},i=!0;return"string"===typeof t?(i=!1,r.kind=t):j.is(t)?r.command=t:r.edit=t,i&&void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return t&&we.string(t.title)&&(void 0===t.diagnostics||we.typedArray(t.diagnostics,I.is))&&(void 0===t.kind||we.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||j.is(t.command))&&(void 0===t.isPreferred||we.boolean(t.isPreferred))&&(void 0===t.edit||F.is(t.edit))}}(ve||(ve={})),function(e){e.create=function(e,t){var n={range:e};return we.defined(t)&&(n.data=t),n},e.is=function(e){var t=e;return we.defined(t)&&d.is(t.range)&&(we.undefined(t.command)||j.is(t.command))}}(ye||(ye={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){var t=e;return we.defined(t)&&we.uinteger(t.tabSize)&&we.boolean(t.insertSpaces)}}(be||(be={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){var t=e;return we.defined(t)&&d.is(t.range)&&(we.undefined(t.target)||we.string(t.target))}}(xe||(xe={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){var n=t;return void 0!==n&&d.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(Ae||(Ae={}));var ke;!function(e){function t(e,n){if(e.length<=1)return e;var r=e.length/2|0,i=e.slice(0,r),o=e.slice(r);t(i,n),t(o,n);for(var a=0,s=0,u=0;a<i.length&&s<o.length;){var c=n(i[a],o[s]);e[u++]=c<=0?i[a++]:o[s++]}for(;a<i.length;)e[u++]=i[a++];for(;s<o.length;)e[u++]=o[s++];return e}e.create=function(e,t,n,r){return new Ie(e,t,n,r)},e.is=function(e){var t=e;return!!(we.defined(t)&&we.string(t.uri)&&(we.undefined(t.languageId)||we.string(t.languageId))&&we.uinteger(t.lineCount)&&we.func(t.getText)&&we.func(t.positionAt)&&we.func(t.offsetAt))},e.applyEdits=function(e,n){for(var r=e.getText(),i=t(n,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=r.length,a=i.length-1;a>=0;a--){var s=i[a],u=e.offsetAt(s.range.start),c=e.offsetAt(s.range.end);if(!(c<=o))throw new Error("Overlapping edit");r=r.substring(0,u)+s.newText+r.substring(c,r.length),o=u}return r}}(ke||(ke={}));var we,Ie=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return p.create(0,e);for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return p.create(o,e-t[o])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),e}();!function(e){var t=Object.prototype.toString;e.defined=function(e){return"undefined"!==typeof e},e.undefined=function(e){return"undefined"===typeof e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"===typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(we||(we={}));var je,Ee,Te,Oe=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(t,n){for(var r=0,i=t;r<i.length;r++){var o=i[r];if(e.isIncremental(o)){var a=Pe(o.range),s=this.offsetAt(a.start),u=this.offsetAt(a.end);this._content=this._content.substring(0,s)+o.text+this._content.substring(u,this._content.length);var c=Math.max(a.start.line,0),f=Math.max(a.end.line,0),l=this._lineOffsets,h=Me(o.text,!1,s);if(f-c===h.length)for(var p=0,d=h.length;p<d;p++)l[p+c+1]=h[p];else h.length<1e4?l.splice.apply(l,[c+1,f-c].concat(h)):this._lineOffsets=l=l.slice(0,c+1).concat(h,l.slice(f+1));var m=o.text.length-(u-s);if(0!==m)for(p=c+1+h.length,d=l.length;p<d;p++)l[p]=l[p]+m}else{if(!e.isFull(o))throw new Error("Unknown change event received");this._content=o.text,this._lineOffsets=void 0}}this._version=n},e.prototype.getLineOffsets=function(){return void 0===this._lineOffsets&&(this._lineOffsets=Me(this._content,!0)),this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return{line:o,character:e-t[o]}},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e.isIncremental=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"===typeof t.rangeLength)},e.isFull=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0===t.range&&void 0===t.rangeLength},e}();function _e(e,t){if(e.length<=1)return e;var n=e.length/2|0,r=e.slice(0,n),i=e.slice(n);_e(r,t),_e(i,t);for(var o=0,a=0,s=0;o<r.length&&a<i.length;){var u=t(r[o],i[a]);e[s++]=u<=0?r[o++]:i[a++]}for(;o<r.length;)e[s++]=r[o++];for(;a<i.length;)e[s++]=i[a++];return e}function Me(e,t,n){void 0===n&&(n=0);for(var r=t?[n]:[],i=0;i<e.length;i++){var o=e.charCodeAt(i);13!==o&&10!==o||(13===o&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,r.push(n+i+1))}return r}function Pe(e){var t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function Ve(e){var t=Pe(e.range);return t!==e.range?{newText:e.newText,range:t}:e}function Ne(e,t){return 0===t.length?e:e.replace(/\{(\d+)\}/g,(function(e,n){var r=n[0];return"undefined"!==typeof t[r]?t[r]:e}))}function Fe(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return Ne(t,n)}function Re(e){return Fe}!function(e){e.create=function(e,t,n,r){return new Oe(e,t,n,r)},e.update=function(e,t,n){if(e instanceof Oe)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){for(var n=e.getText(),r=_e(t.map(Ve),(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=0,o=[],a=0,s=r;a<s.length;a++){var u=s[a],c=e.offsetAt(u.range.start);if(c<i)throw new Error("Overlapping edit");c>i&&o.push(n.substring(i,c)),u.newText.length&&o.push(u.newText),i=e.offsetAt(u.range.end)}return o.push(n.substr(i)),o.join("")}}(je||(je={})),function(e){e[e.Undefined=0]="Undefined",e[e.EnumValueMismatch=1]="EnumValueMismatch",e[e.Deprecated=2]="Deprecated",e[e.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=258]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",e[e.InvalidUnicode=260]="InvalidUnicode",e[e.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",e[e.InvalidCharacter=262]="InvalidCharacter",e[e.PropertyExpected=513]="PropertyExpected",e[e.CommaExpected=514]="CommaExpected",e[e.ColonExpected=515]="ColonExpected",e[e.ValueExpected=516]="ValueExpected",e[e.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",e[e.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",e[e.TrailingComma=519]="TrailingComma",e[e.DuplicateKey=520]="DuplicateKey",e[e.CommentNotPermitted=521]="CommentNotPermitted",e[e.SchemaResolveError=768]="SchemaResolveError"}(Ee||(Ee={})),function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[X.Markdown,X.PlainText],commitCharactersSupport:!0}}}}}(Te||(Te={}));var Le,$e=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),De=Re(),Ue={"color-hex":{errorMessage:De("colorHexFormatWarning","Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:De("dateTimeFormatWarning","String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:De("dateFormatWarning","String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:De("timeFormatWarning","String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:De("emailFormatWarning","String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/}},We=function(){function e(e,t,n){void 0===n&&(n=0),this.offset=t,this.length=n,this.parent=e}return Object.defineProperty(e.prototype,"children",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e}();(function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="null",r.value=null,r}$e(t,e)})(We),function(e){function t(t,n,r){var i=e.call(this,t,r)||this;return i.type="boolean",i.value=n,i}$e(t,e)}(We),function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="array",r.items=[],r}$e(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.items},enumerable:!1,configurable:!0})}(We),function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="number",r.isInteger=!0,r.value=Number.NaN,r}$e(t,e)}(We),function(e){function t(t,n,r){var i=e.call(this,t,n,r)||this;return i.type="string",i.value="",i}$e(t,e)}(We),function(e){function t(t,n,r){var i=e.call(this,t,n)||this;return i.type="property",i.colonOffset=-1,i.keyNode=r,i}$e(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]},enumerable:!1,configurable:!0})}(We),function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="object",r.properties=[],r}$e(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties},enumerable:!1,configurable:!0})}(We);function qe(e){return K(e)?e?{}:{not:{}}:e}!function(e){e[e.Key=0]="Key",e[e.Enum=1]="Enum"}(Le||(Le={}));var Be=function(){function e(e,t){void 0===e&&(e=-1),this.focusOffset=e,this.exclude=t,this.schemas=[]}return e.prototype.add=function(e){this.schemas.push(e)},e.prototype.merge=function(e){Array.prototype.push.apply(this.schemas,e.schemas)},e.prototype.include=function(e){return(-1===this.focusOffset||Ge(e,this.focusOffset))&&e!==this.exclude},e.prototype.newSub=function(){return new e(-1,this.exclude)},e}(),Ke=function(){function e(){}return Object.defineProperty(e.prototype,"schemas",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.add=function(e){},e.prototype.merge=function(e){},e.prototype.include=function(e){return!0},e.prototype.newSub=function(){return this},e.instance=new e,e}(),Je=function(){function e(){this.problems=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}return e.prototype.hasProblems=function(){return!!this.problems.length},e.prototype.mergeAll=function(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];this.merge(r)}},e.prototype.merge=function(e){this.problems=this.problems.concat(e.problems)},e.prototype.mergeEnumValues=function(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(var t=0,n=this.problems;t<n.length;t++){var r=n[t];r.code===Ee.EnumValueMismatch&&(r.message=De("enumWarning","Value is not accepted. Valid values: {0}.",this.enumValues.map((function(e){return JSON.stringify(e)})).join(", ")))}}},e.prototype.mergePropertyMatch=function(e){this.merge(e),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++},e.prototype.compare=function(e){var t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches},e}();function ze(e){return U(e)}function He(e){return D(e)}function Ge(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}!function(){function e(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]),this.root=e,this.syntaxErrors=t,this.comments=n}e.prototype.getNodeFromOffset=function(e,t){if(void 0===t&&(t=!1),this.root)return $(this.root,e,t)},e.prototype.visit=function(e){if(this.root){!function t(n){var r=e(n),i=n.children;if(Array.isArray(i))for(var o=0;o<i.length&&r;o++)r=t(i[o]);return r}(this.root)}},e.prototype.validate=function(e,t,n){if(void 0===n&&(n=S.Warning),this.root&&t){var r=new Je;return Xe(this.root,t,r,Ke.instance),r.problems.map((function(t){var r,i=d.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return I.create(i,t.message,null!==(r=t.severity)&&void 0!==r?r:n,t.code)}))}},e.prototype.getMatchingSchemas=function(e,t,n){void 0===t&&(t=-1);var r=new Be(t,n);return this.root&&e&&Xe(this.root,e,new Je,r),r.schemas}}();function Xe(e,t,n,r){if(e&&r.include(e)){var i=e;switch(i.type){case"object":!function(e,t,n,r){for(var i=Object.create(null),o=[],a=0,s=e.properties;a<s.length;a++){i[$=(g=s[a]).keyNode.value]=g.valueNode,o.push($)}if(Array.isArray(t.required))for(var u=0,c=t.required;u<c.length;u++){if(!i[S=c[u]]){var f=e.parent&&"property"===e.parent.type&&e.parent.keyNode,l=f?{offset:f.offset,length:f.length}:{offset:e.offset,length:1};n.problems.push({location:l,message:De("MissingRequiredPropWarning",'Missing property "{0}".',S)})}}var h=function(e){for(var t=o.indexOf(e);t>=0;)o.splice(t,1),t=o.indexOf(e)};if(t.properties)for(var p=0,d=Object.keys(t.properties);p<d.length;p++){h(S=d[p]);var m=t.properties[S];if(T=i[S])if(K(m))if(m)n.propertiesMatches++,n.propertiesValueMatches++;else{var g=T.parent;n.problems.push({location:{offset:g.keyNode.offset,length:g.keyNode.length},message:t.errorMessage||De("DisallowedExtraPropWarning","Property {0} is not allowed.",S)})}else Xe(T,m,I=new Je,r),n.mergePropertyMatch(I)}if(t.patternProperties)for(var v=0,y=Object.keys(t.patternProperties);v<y.length;v++)for(var b=y[v],x=new RegExp(b),A=0,C=o.slice(0);A<C.length;A++){var S=C[A];if(x.test(S))if(h(S),T=i[S])if(K(m=t.patternProperties[b]))if(m)n.propertiesMatches++,n.propertiesValueMatches++;else{g=T.parent;n.problems.push({location:{offset:g.keyNode.offset,length:g.keyNode.length},message:t.errorMessage||De("DisallowedExtraPropWarning","Property {0} is not allowed.",S)})}else Xe(T,m,I=new Je,r),n.mergePropertyMatch(I)}if("object"===typeof t.additionalProperties)for(var k=0,w=o;k<w.length;k++){if(T=i[S=w[k]]){var I=new Je;Xe(T,t.additionalProperties,I,r),n.mergePropertyMatch(I)}}else if(!1===t.additionalProperties&&o.length>0)for(var j=0,E=o;j<E.length;j++){var T;if(T=i[S=E[j]]){g=T.parent;n.problems.push({location:{offset:g.keyNode.offset,length:g.keyNode.length},message:t.errorMessage||De("DisallowedExtraPropWarning","Property {0} is not allowed.",S)})}}q(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("MaxPropWarning","Object has more properties than limit of {0}.",t.maxProperties)});q(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("MinPropWarning","Object has fewer properties than the required number of {0}",t.minProperties)});if(t.dependencies)for(var O=0,_=Object.keys(t.dependencies);O<_.length;O++){if(i[$=_[O]]){var M=t.dependencies[$];if(Array.isArray(M))for(var P=0,V=M;P<V.length;P++){var N=V[P];i[N]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},message:De("RequiredDependentPropWarning","Object is missing property {0} required by property {1}.",N,$)})}else if(m=qe(M))Xe(e,m,I=new Je,r),n.mergePropertyMatch(I)}}var F=qe(t.propertyNames);if(F)for(var R=0,L=e.properties;R<L.length;R++){var $;($=L[R].keyNode)&&Xe($,F,n,Ke.instance)}}(i,t,n,r);break;case"array":!function(e,t,n,r){if(Array.isArray(t.items)){for(var i=t.items,o=0;o<i.length;o++){var a=qe(i[o]),s=new Je;(h=e.items[o])?(Xe(h,a,s,r),n.mergePropertyMatch(s)):e.items.length>=i.length&&n.propertiesValueMatches++}if(e.items.length>i.length)if("object"===typeof t.additionalItems)for(var u=i.length;u<e.items.length;u++){s=new Je;Xe(e.items[u],t.additionalItems,s,r),n.mergePropertyMatch(s)}else!1===t.additionalItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer.",i.length)})}else{var c=qe(t.items);if(c)for(var f=0,l=e.items;f<l.length;f++){var h;Xe(h=l[f],c,s=new Je,r),n.mergePropertyMatch(s)}}var p=qe(t.contains);if(p){var d=e.items.some((function(e){var t=new Je;return Xe(e,p,t,Ke.instance),!t.hasProblems()}));d||n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||De("requiredItemMissingWarning","Array does not contain required item.")})}q(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("minItemsWarning","Array has too few items. Expected {0} or more.",t.minItems)});q(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("maxItemsWarning","Array has too many items. Expected {0} or fewer.",t.maxItems)});if(!0===t.uniqueItems){var m=ze(e),g=m.some((function(e,t){return t!==m.lastIndexOf(e)}));g&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("uniqueItemsWarning","Array has duplicate items.")})}}(i,t,n,r);break;case"string":!function(e,t,n,r){q(t.minLength)&&e.value.length<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("minLengthWarning","String is shorter than the minimum length of {0}.",t.minLength)});q(t.maxLength)&&e.value.length>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("maxLengthWarning","String is longer than the maximum length of {0}.",t.maxLength)});if(i=t.pattern,"string"===typeof i){new RegExp(t.pattern).test(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||De("patternWarning",'String does not match the pattern of "{0}".',t.pattern)})}var i;if(t.format)switch(t.format){case"uri":case"uri-reference":var o=void 0;if(e.value){var a=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);a?a[2]||"uri"!==t.format||(o=De("uriSchemeMissing","URI with a scheme is expected.")):o=De("uriMissing","URI is expected.")}else o=De("uriEmpty","URI expected.");o&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||De("uriFormatWarning","String is not a URI: {0}",o)});break;case"color-hex":case"date-time":case"date":case"time":case"email":var s=Ue[t.format];e.value&&s.pattern.exec(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||s.errorMessage})}}(i,t,n);break;case"number":!function(e,t,n,r){var i=e.value;function o(e){var t,n=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(e.toString());return n&&{value:Number(n[1]+(n[2]||"")),multiplier:((null===(t=n[2])||void 0===t?void 0:t.length)||0)-(parseInt(n[3])||0)}}if(q(t.multipleOf)){var a=-1;if(Number.isInteger(t.multipleOf))a=i%t.multipleOf;else{var s=o(t.multipleOf),u=o(i);if(s&&u){var c=Math.pow(10,Math.abs(u.multiplier-s.multiplier));u.multiplier<s.multiplier?u.value*=c:s.value*=c,a=u.value%s.value}}0!==a&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("multipleOfWarning","Value is not divisible by {0}.",t.multipleOf)})}function f(e,t){return q(t)?t:K(t)&&t?e:void 0}function l(e,t){if(!K(t)||!t)return e}var h=f(t.minimum,t.exclusiveMinimum);q(h)&&i<=h&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}.",h)});var p=f(t.maximum,t.exclusiveMaximum);q(p)&&i>=p&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}.",p)});var d=l(t.minimum,t.exclusiveMinimum);q(d)&&i<d&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("minimumWarning","Value is below the minimum of {0}.",d)});var m=l(t.maximum,t.exclusiveMaximum);q(m)&&i>m&&n.problems.push({location:{offset:e.offset,length:e.length},message:De("maximumWarning","Value is above the maximum of {0}.",m)})}(i,t,n);break;case"property":return Xe(i.valueNode,t,n,r)}!function(){function e(e){return i.type===e||"integer"===e&&"number"===i.type&&i.isInteger}Array.isArray(t.type)?t.type.some(e)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||De("typeArrayMismatchWarning","Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(e(t.type)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||De("typeMismatchWarning",'Incorrect type. Expected "{0}".',t.type)}));if(Array.isArray(t.allOf))for(var o=0,a=t.allOf;o<a.length;o++){var s=a[o];Xe(i,qe(s),n,r)}var u=qe(t.not);if(u){var c=new Je,f=r.newSub();Xe(i,u,c,f),c.hasProblems()||n.problems.push({location:{offset:i.offset,length:i.length},message:De("notSchemaWarning","Matches a schema that is not allowed.")});for(var l=0,h=f.schemas;l<h.length;l++){var p=h[l];p.inverted=!p.inverted,r.add(p)}}var d=function(e,t){for(var o=[],a=void 0,s=0,u=e;s<u.length;s++){var c=qe(u[s]),f=new Je,l=r.newSub();if(Xe(i,c,f,l),f.hasProblems()||o.push(c),a)if(t||f.hasProblems()||a.validationResult.hasProblems()){var h=f.compare(a.validationResult);h>0?a={schema:c,validationResult:f,matchingSchemas:l}:0===h&&(a.matchingSchemas.merge(l),a.validationResult.mergeEnumValues(f))}else a.matchingSchemas.merge(l),a.validationResult.propertiesMatches+=f.propertiesMatches,a.validationResult.propertiesValueMatches+=f.propertiesValueMatches;else a={schema:c,validationResult:f,matchingSchemas:l}}return o.length>1&&t&&n.problems.push({location:{offset:i.offset,length:1},message:De("oneOfWarning","Matches multiple schemas when only one must validate.")}),a&&(n.merge(a.validationResult),n.propertiesMatches+=a.validationResult.propertiesMatches,n.propertiesValueMatches+=a.validationResult.propertiesValueMatches,r.merge(a.matchingSchemas)),o.length};Array.isArray(t.anyOf)&&d(t.anyOf,!1);Array.isArray(t.oneOf)&&d(t.oneOf,!0);var m=function(e){var t=new Je,o=r.newSub();Xe(i,qe(e),t,o),n.merge(t),n.propertiesMatches+=t.propertiesMatches,n.propertiesValueMatches+=t.propertiesValueMatches,r.merge(o)},g=qe(t.if);g&&function(e,t,n){var o=qe(e),a=new Je,s=r.newSub();Xe(i,o,a,s),r.merge(s),a.hasProblems()?n&&m(n):t&&m(t)}(g,qe(t.then),qe(t.else));if(Array.isArray(t.enum)){for(var v=ze(i),y=!1,b=0,x=t.enum;b<x.length;b++){var A=x[b];if(W(v,A)){y=!0;break}}n.enumValues=t.enum,n.enumValueMatch=y,y||n.problems.push({location:{offset:i.offset,length:i.length},code:Ee.EnumValueMismatch,message:t.errorMessage||De("enumWarning","Value is not accepted. Valid values: {0}.",t.enum.map((function(e){return JSON.stringify(e)})).join(", "))})}if(B(t.const)){W(v=ze(i),t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:i.offset,length:i.length},code:Ee.EnumValueMismatch,message:t.errorMessage||De("constWarning","Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}t.deprecationMessage&&i.parent&&n.problems.push({location:{offset:i.parent.offset,length:i.parent.length},severity:S.Warning,message:t.deprecationMessage,code:Ee.Deprecated})}(),r.add({node:i,schema:t})}}function Ze(e,t,n){if(null!==e&&"object"===typeof e){var r=t+"\t";if(Array.isArray(e)){if(0===e.length)return"[]";for(var i="[\n",o=0;o<e.length;o++)i+=r+Ze(e[o],r,n),o<e.length-1&&(i+=","),i+="\n";return i+=t+"]"}var a=Object.keys(e);if(0===a.length)return"{}";for(i="{\n",o=0;o<a.length;o++){var s=a[o];i+=r+JSON.stringify(s)+": "+Ze(e[s],r,n),o<a.length-1&&(i+=","),i+="\n"}return i+=t+"}"}return n(e)}function Qe(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t}function Ye(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}var et,tt=Re();(function(){function e(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=Promise),void 0===r&&(r={}),this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r}e.prototype.doResolve=function(e){for(var t=this.contributions.length-1;t>=0;t--){var n=this.contributions[t].resolveCompletion;if(n){var r=n(e);if(r)return r}}return this.promiseConstructor.resolve(e)},e.prototype.doComplete=function(e,t,n){var r=this,i={items:[],isIncomplete:!1},o=e.getText(),a=e.offsetAt(t),s=n.getNodeFromOffset(a,!0);if(this.isInComment(e,s?s.offset:0,a))return Promise.resolve(i);if(s&&a===s.offset+s.length&&a>0){var u=o[a-1];("object"===s.type&&"}"===u||"array"===s.type&&"]"===u)&&(s=s.parent)}var c,f=this.getCurrentWord(e,a);if(!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type){var l=a-f.length;l>0&&'"'===o[l-1]&&l--,c=d.create(e.positionAt(l),t)}else c=d.create(e.positionAt(s.offset),e.positionAt(s.offset+s.length));var h={},p={add:function(e){var t=e.label,n=h[t];if(n)n.documentation||(n.documentation=e.documentation),n.detail||(n.detail=e.detail);else{if((t=t.replace(/[\n]/g,"\u21b5")).length>60){var r=t.substr(0,57).trim()+"...";h[r]||(t=r)}c&&void 0!==e.insertText&&(e.textEdit=E.replace(c,e.insertText)),e.label=t,h[t]=e,i.items.push(e)}},setAsIncomplete:function(){i.isIncomplete=!0},error:function(e){console.error(e)},log:function(e){console.log(e)},getNumberOfProposals:function(){return i.items.length}};return this.schemaService.getSchemaForResource(e.uri,n).then((function(t){var u=[],l=!0,d="",m=void 0;if(s&&"string"===s.type){var g=s.parent;g&&"property"===g.type&&g.keyNode===s&&(l=!g.valueNode,m=g,d=o.substr(s.offset+1,s.length-2),g&&(s=g.parent))}if(s&&"object"===s.type){if(s.offset===a)return i;s.properties.forEach((function(e){m&&m===e||(h[e.keyNode.value]=re.create("__"))}));var v="";l&&(v=r.evaluateSeparatorAfter(e,e.offsetAt(c.end))),t?r.getPropertyCompletions(t,n,s,l,v,p):r.getSchemaLessPropertyCompletions(n,s,d,p);var y=He(s);r.contributions.forEach((function(t){var n=t.collectPropertyCompletions(e.uri,y,f,l,""===v,p);n&&u.push(n)})),!t&&f.length>0&&'"'!==o.charAt(a-f.length-1)&&(p.add({kind:Q.Property,label:r.getLabelForValue(f),insertText:r.getInsertTextForProperty(f,void 0,!1,v),insertTextFormat:Y.Snippet,documentation:""}),p.setAsIncomplete())}var b={};return t?r.getValueCompletions(t,n,s,a,e,p,b):r.getSchemaLessValueCompletions(n,s,a,e,p),r.contributions.length>0&&r.getContributedValueCompletions(n,s,a,e,p,u),r.promiseConstructor.all(u).then((function(){if(0===p.getNumberOfProposals()){var t=a;!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type||(t=s.offset+s.length);var n=r.evaluateSeparatorAfter(e,t);r.addFillerValueCompletions(b,n,p)}return i}))}))},e.prototype.getPropertyCompletions=function(e,t,n,r,i,o){var a=this;t.getMatchingSchemas(e.schema,n.offset).forEach((function(e){if(e.node===n&&!e.inverted){var t=e.schema.properties;t&&Object.keys(t).forEach((function(e){var n=t[e];if("object"===typeof n&&!n.deprecationMessage&&!n.doNotSuggest){var s={kind:Q.Property,label:e,insertText:a.getInsertTextForProperty(e,n,r,i),insertTextFormat:Y.Snippet,filterText:a.getFilterTextForValue(e),documentation:a.fromMarkup(n.markdownDescription)||n.description||""};void 0!==n.suggestSortText&&(s.sortText=n.suggestSortText),s.insertText&&Qe(s.insertText,"$1"+i)&&(s.command={title:"Suggest",command:"editor.action.triggerSuggest"}),o.add(s)}}));var s=e.schema.propertyNames;if("object"===typeof s&&!s.deprecationMessage&&!s.doNotSuggest){var u=function(e,t){void 0===t&&(t=void 0);var n={kind:Q.Property,label:e,insertText:a.getInsertTextForProperty(e,void 0,r,i),insertTextFormat:Y.Snippet,filterText:a.getFilterTextForValue(e),documentation:t||a.fromMarkup(s.markdownDescription)||s.description||""};void 0!==s.suggestSortText&&(n.sortText=s.suggestSortText),n.insertText&&Qe(n.insertText,"$1"+i)&&(n.command={title:"Suggest",command:"editor.action.triggerSuggest"}),o.add(n)};if(s.enum)for(var c=0;c<s.enum.length;c++){var f=void 0;s.markdownEnumDescriptions&&c<s.markdownEnumDescriptions.length?f=a.fromMarkup(s.markdownEnumDescriptions[c]):s.enumDescriptions&&c<s.enumDescriptions.length&&(f=s.enumDescriptions[c]),u(s.enum[c],f)}s.const&&u(s.const)}}}))},e.prototype.getSchemaLessPropertyCompletions=function(e,t,n,r){var i=this,o=function(e){e.properties.forEach((function(e){var t=e.keyNode.value;r.add({kind:Q.Property,label:t,insertText:i.getInsertTextForValue(t,""),insertTextFormat:Y.Snippet,filterText:i.getFilterTextForValue(t),documentation:""})}))};if(t.parent)if("property"===t.parent.type){var a=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e!==t.parent&&e.keyNode.value===a&&e.valueNode&&"object"===e.valueNode.type&&o(e.valueNode),!0}))}else"array"===t.parent.type&&t.parent.items.forEach((function(e){"object"===e.type&&e!==t&&o(e)}));else"object"===t.type&&r.add({kind:Q.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",void 0,!0,""),insertTextFormat:Y.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(e,t,n,r,i){var o=this,a=n;if(!t||"string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(a=t.offset+t.length,t=t.parent),!t)return i.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:Y.Snippet,documentation:""}),void i.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:Y.Snippet,documentation:""});var s=this.evaluateSeparatorAfter(r,a),u=function(e){e.parent&&!Ge(e.parent,n,!0)&&i.add({kind:o.getSuggestionKind(e.type),label:o.getLabelTextForMatchingNode(e,r),insertText:o.getInsertTextForMatchingNode(e,r,s),insertTextFormat:Y.Snippet,documentation:""}),"boolean"===e.type&&o.addBooleanValueCompletion(!e.value,s,i)};if("property"===t.type&&n>(t.colonOffset||0)){var c=t.valueNode;if(c&&(n>c.offset+c.length||"object"===c.type||"array"===c.type))return;var f=t.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===f&&e.valueNode&&u(e.valueNode),!0})),"$schema"===f&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(s,i)}if("array"===t.type)if(t.parent&&"property"===t.parent.type){var l=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===l&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(u),!0}))}else t.items.forEach(u)},e.prototype.getValueCompletions=function(e,t,n,r,i,o,a){var s=r,u=void 0,c=void 0;if(!n||"string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type||(s=n.offset+n.length,c=n,n=n.parent),n){if("property"===n.type&&r>(n.colonOffset||0)){var f=n.valueNode;if(f&&r>f.offset+f.length)return;u=n.keyNode.value,n=n.parent}if(n&&(void 0!==u||"array"===n.type)){for(var l=this.evaluateSeparatorAfter(i,s),h=0,p=t.getMatchingSchemas(e.schema,n.offset,c);h<p.length;h++){var d=p[h];if(d.node===n&&!d.inverted&&d.schema){if("array"===n.type&&d.schema.items)if(Array.isArray(d.schema.items)){var m=this.findItemAtOffset(n,i,r);m<d.schema.items.length&&this.addSchemaValueCompletions(d.schema.items[m],l,o,a)}else this.addSchemaValueCompletions(d.schema.items,l,o,a);if(void 0!==u){var g=!1;if(d.schema.properties)(x=d.schema.properties[u])&&(g=!0,this.addSchemaValueCompletions(x,l,o,a));if(d.schema.patternProperties&&!g)for(var v=0,y=Object.keys(d.schema.patternProperties);v<y.length;v++){var b=y[v];if(new RegExp(b).test(u)){g=!0;var x=d.schema.patternProperties[b];this.addSchemaValueCompletions(x,l,o,a)}}if(d.schema.additionalProperties&&!g){x=d.schema.additionalProperties;this.addSchemaValueCompletions(x,l,o,a)}}}}"$schema"!==u||n.parent||this.addDollarSchemaCompletions(l,o),a.boolean&&(this.addBooleanValueCompletion(!0,l,o),this.addBooleanValueCompletion(!1,l,o)),a.null&&this.addNullValueCompletion(l,o)}}else this.addSchemaValueCompletions(e.schema,"",o,a)},e.prototype.getContributedValueCompletions=function(e,t,n,r,i,o){if(t){if("string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(t=t.parent),t&&"property"===t.type&&n>(t.colonOffset||0)){var a=t.keyNode.value,s=t.valueNode;if((!s||n<=s.offset+s.length)&&t.parent){var u=He(t.parent);this.contributions.forEach((function(e){var t=e.collectValueCompletions(r.uri,u,a,i);t&&o.push(t)}))}}}else this.contributions.forEach((function(e){var t=e.collectDefaultCompletions(r.uri,i);t&&o.push(t)}))},e.prototype.addSchemaValueCompletions=function(e,t,n,r){var i=this;"object"===typeof e&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.anyOf)&&e.anyOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.oneOf)&&e.oneOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})))},e.prototype.addDefaultValueCompletions=function(e,t,n,r){var i=this;void 0===r&&(r=0);var o=!1;if(B(e.default)){for(var a=e.type,s=e.default,u=r;u>0;u--)s=[s],a="array";n.add({kind:this.getSuggestionKind(a),label:this.getLabelForValue(s),insertText:this.getInsertTextForValue(s,t),insertTextFormat:Y.Snippet,detail:tt("json.suggest.default","Default value")}),o=!0}Array.isArray(e.examples)&&e.examples.forEach((function(a){for(var s=e.type,u=a,c=r;c>0;c--)u=[u],s="array";n.add({kind:i.getSuggestionKind(s),label:i.getLabelForValue(u),insertText:i.getInsertTextForValue(u,t),insertTextFormat:Y.Snippet}),o=!0})),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach((function(a){var s,u,c=e.type,f=a.body,l=a.label;if(B(f)){e.type;for(var h=r;h>0;h--)f=[f],"array";s=i.getInsertTextForSnippetValue(f,t),u=i.getFilterTextForSnippetValue(f),l=l||i.getLabelForSnippetValue(f)}else{if("string"!==typeof a.bodyText)return;var p="",d="",m="";for(h=r;h>0;h--)p=p+m+"[\n",d=d+"\n"+m+"]",m+="\t",c="array";s=p+m+a.bodyText.split("\n").join("\n"+m)+d+t,l=l||s,u=s.replace(/[\n]/g,"")}n.add({kind:i.getSuggestionKind(c),label:l,documentation:i.fromMarkup(a.markdownDescription)||a.description,insertText:s,insertTextFormat:Y.Snippet,filterText:u}),o=!0})),!o&&"object"===typeof e.items&&!Array.isArray(e.items)&&r<5&&this.addDefaultValueCompletions(e.items,t,n,r+1)},e.prototype.addEnumValueCompletions=function(e,t,n){if(B(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:Y.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(var r=0,i=e.enum.length;r<i;r++){var o=e.enum[r],a=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?a=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(a=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(o),insertText:this.getInsertTextForValue(o,t),insertTextFormat:Y.Snippet,documentation:a})}},e.prototype.collectTypes=function(e,t){if(!Array.isArray(e.enum)&&!B(e.const)){var n=e.type;Array.isArray(n)?n.forEach((function(e){return t[e]=!0})):n&&(t[n]=!0)}},e.prototype.addFillerValueCompletions=function(e,t,n){e.object&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:Y.Snippet,detail:tt("defaults.object","New object"),documentation:""}),e.array&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:Y.Snippet,detail:tt("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:Y.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:Y.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(e,t){var n=this,r=this.schemaService.getRegisteredSchemaIds((function(e){return"http"===e||"https"===e}));r.forEach((function(r){return t.add({kind:Q.Module,label:n.getLabelForValue(r),filterText:n.getFilterTextForValue(r),insertText:n.getInsertTextForValue(r,e),insertTextFormat:Y.Snippet,documentation:""})}))},e.prototype.getLabelForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getLabelForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getInsertTextForPlainText=function(e){return e.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(e,t){var n=JSON.stringify(e,null,"\t");return"{}"===n?"{$1}"+t:"[]"===n?"[$1]"+t:this.getInsertTextForPlainText(n+t)},e.prototype.getInsertTextForSnippetValue=function(e,t){return Ze(e,"",(function(e){return"string"===typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)}))+t},e.prototype.getInsertTextForGuessedValue=function(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":var n=JSON.stringify(e);return n=n.substr(1,n.length-2),'"${1:'+(n=this.getInsertTextForPlainText(n))+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)},e.prototype.getSuggestionKind=function(e){if(Array.isArray(e)){var t=e;e=t.length>0?t[0]:void 0}if(!e)return Q.Value;switch(e){case"string":default:return Q.Value;case"object":return Q.Module;case"property":return Q.Property}},e.prototype.getLabelTextForMatchingNode=function(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:return t.getText().substr(e.offset,e.length)}},e.prototype.getInsertTextForMatchingNode=function(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:var r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}},e.prototype.getInsertTextForProperty=function(e,t,n,r){var i=this.getInsertTextForValue(e,"");if(!n)return i;var o,a=i+": ",s=0;if(t){if(Array.isArray(t.defaultSnippets)){if(1===t.defaultSnippets.length){var u=t.defaultSnippets[0].body;B(u)&&(o=this.getInsertTextForSnippetValue(u,""))}s+=t.defaultSnippets.length}if(t.enum&&(o||1!==t.enum.length||(o=this.getInsertTextForGuessedValue(t.enum[0],"")),s+=t.enum.length),B(t.default)&&(o||(o=this.getInsertTextForGuessedValue(t.default,"")),s++),Array.isArray(t.examples)&&t.examples.length&&(o||(o=this.getInsertTextForGuessedValue(t.examples[0],"")),s+=t.examples.length),0===s){var c=Array.isArray(t.type)?t.type[0]:t.type;switch(c||(t.properties?c="object":t.items&&(c="array")),c){case"boolean":o="$1";break;case"string":o='"$1"';break;case"object":o="{$1}";break;case"array":o="[$1]";break;case"number":case"integer":o="${1:0}";break;case"null":o="${1:null}";break;default:return i}}}return(!o||s>1)&&(o="$1"),a+o+r},e.prototype.getCurrentWord=function(e,t){for(var n=t-1,r=e.getText();n>=0&&-1===' \t\n\r\v":{[,]}'.indexOf(r.charAt(n));)n--;return r.substring(n+1,t)},e.prototype.evaluateSeparatorAfter=function(e,t){var n=R(e.getText(),!0);switch(n.setPosition(t),n.scan()){case 5:case 2:case 4:case 17:return"";default:return","}},e.prototype.findItemAtOffset=function(e,t,n){for(var r=R(t.getText(),!0),i=e.items,o=i.length-1;o>=0;o--){var a=i[o];if(n>a.offset+a.length)return r.setPosition(a.offset+a.length),5===r.scan()&&n>=r.getTokenOffset()+r.getTokenLength()?o+1:o;if(n>=a.offset)return o}return 0},e.prototype.isInComment=function(e,t,n){var r=R(e.getText(),!1);r.setPosition(t);for(var i=r.scan();17!==i&&r.getTokenOffset()+r.getTokenLength()<n;)i=r.scan();return(12===i||13===i)&&r.getTokenOffset()<=n},e.prototype.fromMarkup=function(e){if(e&&this.doesSupportMarkdown())return{kind:X.Markdown,value:e}},e.prototype.doesSupportMarkdown=function(){if(!B(this.supportsMarkdown)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=e&&e.completionItem&&Array.isArray(e.completionItem.documentationFormat)&&-1!==e.completionItem.documentationFormat.indexOf(X.Markdown)}return this.supportsMarkdown},e.prototype.doesSupportsCommitCharacters=function(){if(!B(this.supportsCommitCharacters)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsCommitCharacters=e&&e.completionItem&&!!e.completionItem.commitCharactersSupport}return this.supportsCommitCharacters}})(),function(){function e(e,t,n){void 0===t&&(t=[]),this.schemaService=e,this.contributions=t,this.promise=n||Promise}e.prototype.doHover=function(e,t,n){var r=e.offsetAt(t),i=n.getNodeFromOffset(r);if(!i||("object"===i.type||"array"===i.type)&&r>i.offset+1&&r<i.offset+i.length-1)return this.promise.resolve(null);var o=i;if("string"===i.type){var a=i.parent;if(a&&"property"===a.type&&a.keyNode===i&&!(i=a.valueNode))return this.promise.resolve(null)}for(var s=d.create(e.positionAt(o.offset),e.positionAt(o.offset+o.length)),u=function(e){return{contents:e,range:s}},c=He(i),f=this.contributions.length-1;f>=0;f--){var l=this.contributions[f].getInfoContribution(e.uri,c);if(l)return l.then((function(e){return u(e)}))}return this.schemaService.getSchemaForResource(e.uri,n).then((function(e){if(e&&i){var t=n.getMatchingSchemas(e.schema,i.offset),r=void 0,o=void 0,a=void 0,s=void 0;t.every((function(e){if(e.node===i&&!e.inverted&&e.schema&&(r=r||e.schema.title,o=o||e.schema.markdownDescription||nt(e.schema.description),e.schema.enum)){var t=e.schema.enum.indexOf(ze(i));e.schema.markdownEnumDescriptions?a=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(a=nt(e.schema.enumDescriptions[t])),a&&"string"!==typeof(s=e.schema.enum[t])&&(s=JSON.stringify(s))}return!0}));var c="";return r&&(c=nt(r)),o&&(c.length>0&&(c+="\n\n"),c+=o),a&&(c.length>0&&(c+="\n\n"),c+="`"+function(e){if(-1!==e.indexOf("`"))return"`` "+e+" ``";return e}(s)+"`: "+a),u([c])}return null}))}}();function nt(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3").replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}et=function(){var e={470:function(e){function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,o=-1,a=0,s=0;s<=e.length;++s){if(s<e.length)n=e.charCodeAt(s);else{if(47===n)break;n=47}if(47===n){if(o===s-1||1===a);else if(o!==s-1&&2===a){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var u=r.lastIndexOf("/");if(u!==r.length-1){-1===u?(r="",i=0):i=(r=r.slice(0,u)).length-1-r.lastIndexOf("/"),o=s,a=0;continue}}else if(2===r.length||1===r.length){r="",i=0,o=s,a=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(o+1,s):r=e.slice(o+1,s),i=s-o-1;o=s,a=0}else 46===n&&-1!==a?++a:a=-1}return r}var r={resolve:function(){for(var e,r="",i=!1,o=arguments.length-1;o>=-1&&!i;o--){var a;o>=0?a=arguments[o]:(void 0===e&&(e=process.cwd()),a=e),t(a),0!==a.length&&(r=a+"/"+r,i=47===a.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var o=e.length,a=o-i,s=1;s<n.length&&47===n.charCodeAt(s);++s);for(var u=n.length-s,c=a<u?a:u,f=-1,l=0;l<=c;++l){if(l===c){if(u>c){if(47===n.charCodeAt(s+l))return n.slice(s+l+1);if(0===l)return n.slice(s+l)}else a>c&&(47===e.charCodeAt(i+l)?f=l:0===l&&(f=0));break}var h=e.charCodeAt(i+l);if(h!==n.charCodeAt(s+l))break;47===h&&(f=l)}var p="";for(l=i+f+1;l<=o;++l)l!==o&&47!==e.charCodeAt(l)||(0===p.length?p+="..":p+="/..");return p.length>0?p+n.slice(s+f):(s+=f,47===n.charCodeAt(s)&&++s,n.slice(s))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,o=!0,a=e.length-1;a>=1;--a)if(47===(n=e.charCodeAt(a))){if(!o){i=a;break}}else o=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,o=-1,a=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var s=n.length-1,u=-1;for(r=e.length-1;r>=0;--r){var c=e.charCodeAt(r);if(47===c){if(!a){i=r+1;break}}else-1===u&&(a=!1,u=r+1),s>=0&&(c===n.charCodeAt(s)?-1==--s&&(o=r):(s=-1,o=u))}return i===o?o=u:-1===o&&(o=e.length),e.slice(i,o)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!a){i=r+1;break}}else-1===o&&(a=!1,o=r+1);return-1===o?"":e.slice(i,o)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,o=!0,a=0,s=e.length-1;s>=0;--s){var u=e.charCodeAt(s);if(47!==u)-1===i&&(o=!1,i=s+1),46===u?-1===n?n=s:1!==a&&(a=1):-1!==n&&(a=-1);else if(!o){r=s+1;break}}return-1===n||-1===i||0===a||1===a&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),o=47===i;o?(n.root="/",r=1):r=0;for(var a=-1,s=0,u=-1,c=!0,f=e.length-1,l=0;f>=r;--f)if(47!==(i=e.charCodeAt(f)))-1===u&&(c=!1,u=f+1),46===i?-1===a?a=f:1!==l&&(l=1):-1!==a&&(l=-1);else if(!c){s=f+1;break}return-1===a||-1===u||0===l||1===l&&a===u-1&&a===s+1?-1!==u&&(n.base=n.name=0===s&&o?e.slice(1,u):e.slice(s,u)):(0===s&&o?(n.name=e.slice(1,a),n.base=e.slice(1,u)):(n.name=e.slice(s,a),n.base=e.slice(s,u)),n.ext=e.slice(a,u)),s>0?n.dir=e.slice(0,s-1):o&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r},447:function(e,t,n){var r;if(n.r(t),n.d(t,{URI:function(){return d},Utils:function(){return w}}),"object"==typeof process)r="win32"===process.platform;else if("object"==typeof navigator){var i=navigator.userAgent;r=i.indexOf("Windows")>=0}var o,a,s=(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),u=/^\w[\w\d+.-]*$/,c=/^\//,f=/^\/\//,l="",h="/",p=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,d=function(){function e(e,t,n,r,i,o){void 0===o&&(o=!1),"object"==typeof e?(this.scheme=e.scheme||l,this.authority=e.authority||l,this.path=e.path||l,this.query=e.query||l,this.fragment=e.fragment||l):(this.scheme=function(e,t){return e||t?e:"file"}(e,o),this.authority=t||l,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==h&&(t=h+t):t=h}return t}(this.scheme,n||l),this.query=r||l,this.fragment=i||l,function(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!u.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!c.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(f.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,o))}return e.isUri=function(t){return t instanceof e||!!t&&"string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"function"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString},Object.defineProperty(e.prototype,"fsPath",{get:function(){return x(this,!1)},enumerable:!1,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,i=e.query,o=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=l),void 0===n?n=this.authority:null===n&&(n=l),void 0===r?r=this.path:null===r&&(r=l),void 0===i?i=this.query:null===i&&(i=l),void 0===o?o=this.fragment:null===o&&(o=l),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&o===this.fragment?this:new g(t,n,r,i,o)},e.parse=function(e,t){void 0===t&&(t=!1);var n=p.exec(e);return n?new g(n[2]||l,k(n[4]||l),k(n[5]||l),k(n[7]||l),k(n[9]||l),t):new g(l,l,l,l,l)},e.file=function(e){var t=l;if(r&&(e=e.replace(/\\/g,h)),e[0]===h&&e[1]===h){var n=e.indexOf(h,2);-1===n?(t=e.substring(2),e=h):(t=e.substring(2,n),e=e.substring(n)||h)}return new g("file",t,e,l,l)},e.from=function(e){return new g(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),A(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new g(t);return n._formatted=t.external,n._fsPath=t._sep===m?t.fsPath:null,n}return t},e}(),m=r?1:void 0,g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return s(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=x(this,!1)),this._fsPath},enumerable:!1,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?A(this,!0):(this._formatted||(this._formatted=A(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=m),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(d),v=((a={})[58]="%3A",a[47]="%2F",a[63]="%3F",a[35]="%23",a[91]="%5B",a[93]="%5D",a[64]="%40",a[33]="%21",a[36]="%24",a[38]="%26",a[39]="%27",a[40]="%28",a[41]="%29",a[42]="%2A",a[43]="%2B",a[44]="%2C",a[59]="%3B",a[61]="%3D",a[32]="%20",a);function y(e,t){for(var n=void 0,r=-1,i=0;i<e.length;i++){var o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));var a=v[o];void 0!==a?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=a):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function b(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=v[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function x(e,t){var n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,r&&(n=n.replace(/\//g,"\\")),n}function A(e,t){var n=t?b:y,r="",i=e.scheme,o=e.authority,a=e.path,s=e.query,u=e.fragment;if(i&&(r+=i,r+=":"),(o||"file"===i)&&(r+=h,r+=h),o){var c=o.indexOf("@");if(-1!==c){var f=o.substr(0,c);o=o.substr(c+1),-1===(c=f.indexOf(":"))?r+=n(f,!1):(r+=n(f.substr(0,c),!1),r+=":",r+=n(f.substr(c+1),!1)),r+="@"}-1===(c=(o=o.toLowerCase()).indexOf(":"))?r+=n(o,!1):(r+=n(o.substr(0,c),!1),r+=o.substr(c))}if(a){if(a.length>=3&&47===a.charCodeAt(0)&&58===a.charCodeAt(2))(l=a.charCodeAt(1))>=65&&l<=90&&(a="/"+String.fromCharCode(l+32)+":"+a.substr(3));else if(a.length>=2&&58===a.charCodeAt(1)){var l;(l=a.charCodeAt(0))>=65&&l<=90&&(a=String.fromCharCode(l+32)+":"+a.substr(2))}r+=n(a,!0)}return s&&(r+="?",r+=n(s,!1)),u&&(r+="#",r+=t?u:y(u,!1)),r}function C(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+C(e.substr(3)):e}}var S=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function k(e){return e.match(S)?e.replace(S,(function(e){return C(e)})):e}var w,I=n(470),j=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r},E=I.posix||I;!function(e){e.joinPath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return e.with({path:E.join.apply(E,j([e.path],t))})},e.resolvePath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=e.path||"/";return e.with({path:E.resolve.apply(E,j([r],t))})},e.dirname=function(e){var t=E.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)?e:e.with({path:t})},e.basename=function(e){return E.basename(e.path)},e.extname=function(e){return E.extname(e.path)}}(w||(w={}))}},t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}return n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(447)}();var rt=et,it=rt.URI,ot=(rt.Utils,Re()),at=function(){function e(e,t){this.patternRegExps=[],this.isInclude=[];try{for(var n=0,r=e;n<r.length;n++){var i=r[n],o="!"!==i[0];o||(i=i.substring(1)),this.patternRegExps.push(new RegExp(Ye(i)+"$")),this.isInclude.push(o)}this.uris=t}catch(a){this.patternRegExps.length=0,this.isInclude.length=0,this.uris=[]}}return e.prototype.matchesPattern=function(e){for(var t=!1,n=0;n<this.patternRegExps.length;n++){this.patternRegExps[n].test(e)&&(t=this.isInclude[n])}return t},e.prototype.getURIs=function(){return this.uris},e}(),st=function(){function e(e,t,n){this.service=e,this.url=t,this.dependencies={},n&&(this.unresolvedSchema=this.service.promise.resolve(new ut(n)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.url)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var e=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then((function(t){return e.service.resolveSchemaContent(t,e.url,e.dependencies)}))),this.resolvedSchema},e.prototype.clearSchema=function(){this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies={}},e}(),ut=function(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t},ct=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e.prototype.getSection=function(e){var t=this.getSectionRecursive(e,this.schema);if(t)return qe(t)},e.prototype.getSectionRecursive=function(e,t){if(!t||"boolean"===typeof t||0===e.length)return t;var n=e.shift();if(t.properties&&(t.properties[n],1))return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties)for(var r=0,i=Object.keys(t.patternProperties);r<i.length;r++){var o=i[r];if(new RegExp(o).test(n))return this.getSectionRecursive(e,t.patternProperties[o])}else{if("object"===typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(n.match("[0-9]+"))if(Array.isArray(t.items)){var a=parseInt(n,10);if(!isNaN(a)&&t.items[a])return this.getSectionRecursive(e,t.items[a])}else if(t.items)return this.getSectionRecursive(e,t.items)}},e}(),ft=(function(){function e(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}e.prototype.getRegisteredSchemaIds=function(e){return Object.keys(this.registeredSchemasIds).filter((function(t){var n=it.parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))}))},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(e){for(var t=this,n=!1,r=[e=lt(e)],i=Object.keys(this.schemasById).map((function(e){return t.schemasById[e]}));r.length;)for(var o=r.pop(),a=0;a<i.length;a++){var s=i[a];s&&(s.url===o||s.dependencies[o])&&(s.url!==o&&r.push(s.url),s.clearSchema(),i[a]=void 0,n=!0)}return n},e.prototype.setSchemaContributions=function(e){if(e.schemas){var t=e.schemas;for(var n in t){var r=lt(n);this.contributionSchemas[r]=this.addSchemaHandle(r,t[n])}}if(Array.isArray(e.schemaAssociations))for(var i=0,o=e.schemaAssociations;i<o.length;i++){var a=o[i],s=a.uris.map(lt),u=this.addFilePatternAssociation(a.pattern,s);this.contributionAssociations.push(u)}},e.prototype.addSchemaHandle=function(e,t){var n=new st(this,e,t);return this.schemasById[e]=n,n},e.prototype.getOrAddSchemaHandle=function(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)},e.prototype.addFilePatternAssociation=function(e,t){var n=new at(e,t);return this.filePatternAssociations.push(n),n},e.prototype.registerExternalSchema=function(e,t,n){var r=lt(e);return this.registeredSchemasIds[r]=!0,this.cachedSchemaForResource=void 0,t&&this.addFilePatternAssociation(t,[e]),n?this.addSchemaHandle(r,n):this.getOrAddSchemaHandle(r)},e.prototype.clearExternalSchemas=function(){for(var e in this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0,this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(var t=0,n=this.contributionAssociations;t<n.length;t++){var r=n[t];this.filePatternAssociations.push(r)}},e.prototype.getResolvedSchema=function(e){var t=lt(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(void 0)},e.prototype.loadSchema=function(e){if(!this.requestService){var t=ot("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",ht(e));return this.promise.resolve(new ut({},[t]))}return this.requestService(e).then((function(t){if(!t){var n=ot("json.schema.nocontent","Unable to load schema from '{0}': No content.",ht(e));return new ut({},[n])}var r,i=[];r=L(t,i);var o=i.length?[ot("json.schema.invalidFormat","Unable to parse content from '{0}': Parse error at offset {1}.",ht(e),i[0].offset)]:[];return new ut(r,o)}),(function(t){var n=t.toString(),r=t.toString().split("Error: ");return r.length>1&&(n=r[1]),Qe(n,".")&&(n=n.substr(0,n.length-1)),new ut({},[ot("json.schema.nocontent","Unable to load schema from '{0}': {1}.",ht(e),n)])}))},e.prototype.resolveSchemaContent=function(e,t,n){var r=this,i=e.errors.slice(0),o=e.schema;if(o.$schema){var a=lt(o.$schema);if("http://json-schema.org/draft-03/schema"===a)return this.promise.resolve(new ct({},[ot("json.schema.draft03.notsupported","Draft-03 schemas are not supported.")]));"https://json-schema.org/draft/2019-09/schema"===a&&i.push(ot("json.schema.draft201909.notsupported","Draft 2019-09 schemas are not yet fully supported."))}var s=this.contextService,u=function(e,t,n,r){var o=r?decodeURIComponent(r):void 0,a=function(e,t){if(!t)return e;var n=e;return"/"===t[0]&&(t=t.substr(1)),t.split("/").some((function(e){return!(n=n[e])})),n}(t,o);if(a)for(var s in a)a.hasOwnProperty(s)&&!e.hasOwnProperty(s)&&(e[s]=a[s]);else i.push(ot("json.schema.invalidref","$ref '{0}' in '{1}' can not be resolved.",o,n))},c=function(e,t,n,o,a){s&&!/^\w+:\/\/.*/.test(t)&&(t=s.resolveRelativePath(t,o)),t=lt(t);var c=r.getOrAddSchemaHandle(t);return c.getUnresolvedSchema().then((function(r){if(a[t]=!0,r.errors.length){var o=n?t+"#"+n:t;i.push(ot("json.schema.problemloadingref","Problems loading reference '{0}': {1}",o,r.errors[0]))}return u(e,r.schema,t,n),f(e,r.schema,t,c.dependencies)}))},f=function(e,t,n,i){if(!e||"object"!==typeof e)return Promise.resolve(null);for(var o=[e],a=[],s=[],f=function(e){for(var r=[];e.$ref;){var a=e.$ref,f=a.split("#",2);if(delete e.$ref,f[0].length>0)return void s.push(c(e,f[0],f[1],n,i));-1===r.indexOf(a)&&(u(e,t,n,f[1]),r.push(a))}!function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];"object"===typeof i&&o.push(i)}}(e.items,e.additionalItems,e.additionalProperties,e.not,e.contains,e.propertyNames,e.if,e.then,e.else),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if("object"===typeof i)for(var a in i){var s=i[a];"object"===typeof s&&o.push(s)}}}(e.definitions,e.properties,e.patternProperties,e.dependencies),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if(Array.isArray(i))for(var a=0,s=i;a<s.length;a++){var u=s[a];"object"===typeof u&&o.push(u)}}}(e.anyOf,e.allOf,e.oneOf,e.items)};o.length;){var l=o.pop();a.indexOf(l)>=0||(a.push(l),f(l))}return r.promise.all(s)};return f(o,o,t,n).then((function(e){return new ct(o,i)}))},e.prototype.getSchemaForResource=function(e,t){if(t&&t.root&&"object"===t.root.type){var n=t.root.properties.filter((function(e){return"$schema"===e.keyNode.value&&e.valueNode&&"string"===e.valueNode.type}));if(n.length>0){var r=n[0].valueNode;if(r&&"string"===r.type){var i=ze(r);if(i&&function(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0}(i,".")&&this.contextService&&(i=this.contextService.resolveRelativePath(i,e)),i){var o=lt(i);return this.getOrAddSchemaHandle(o).getResolvedSchema()}}}}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===e)return this.cachedSchemaForResource.resolvedSchema;for(var a=Object.create(null),s=[],u=function(e){try{return it.parse(e).with({fragment:null,query:null}).toString()}catch(t){return e}}(e),c=0,f=this.filePatternAssociations;c<f.length;c++){var l=f[c];if(l.matchesPattern(u))for(var h=0,p=l.getURIs();h<p.length;h++){var d=p[h];a[d]||(s.push(d),a[d]=!0)}}var m=s.length>0?this.createCombinedSchema(e,s).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:e,resolvedSchema:m},m},e.prototype.createCombinedSchema=function(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);var n="schemaservice://combinedSchema/"+encodeURIComponent(e),r={allOf:t.map((function(e){return{$ref:e}}))};return this.addSchemaHandle(n,r)},e.prototype.getMatchingSchemas=function(e,t,n){if(n){var r=n.id||"schemaservice://untitled/matchingSchemas/"+ft++;return this.resolveSchemaContent(new ut(n),r,{}).then((function(e){return t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted}))}))}return this.getSchemaForResource(e.uri,t).then((function(e){return e?t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted})):[]}))}}(),0);function lt(e){try{return it.parse(e).toString()}catch(t){return e}}function ht(e){try{var t=it.parse(e);if("file"===t.scheme)return t.fsPath}catch(n){}return e}var pt=Re(),dt=(function(){function e(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}e.prototype.configure=function(e){e&&(this.validationEnabled=!1!==e.validate,this.commentSeverity=e.allowComments?void 0:S.Error)},e.prototype.doValidation=function(e,t,n,r){var i=this;if(!this.validationEnabled)return this.promise.resolve([]);var o=[],a={},s=function(e){var t=e.range.start.line+" "+e.range.start.character+" "+e.message;a[t]||(a[t]=!0,o.push(e))},u=function(r){var a=n?vt(n.trailingCommas):S.Error,u=n?vt(n.comments):i.commentSeverity,c=(null===n||void 0===n?void 0:n.schemaValidation)?vt(n.schemaValidation):S.Warning,f=(null===n||void 0===n?void 0:n.schemaRequest)?vt(n.schemaRequest):S.Warning;if(r){if(r.errors.length&&t.root&&f){var l=t.root,h="object"===l.type?l.properties[0]:void 0;if(h&&"$schema"===h.keyNode.value){var p=h.valueNode||h,m=d.create(e.positionAt(p.offset),e.positionAt(p.offset+p.length));s(I.create(m,r.errors[0],f,Ee.SchemaResolveError))}else{m=d.create(e.positionAt(l.offset),e.positionAt(l.offset+1));s(I.create(m,r.errors[0],f,Ee.SchemaResolveError))}}else if(c){var g=t.validate(e,r.schema,c);g&&g.forEach(s)}mt(r.schema)&&(u=void 0),gt(r.schema)&&(a=void 0)}for(var v=0,y=t.syntaxErrors;v<y.length;v++){var b=y[v];if(b.code===Ee.TrailingComma){if("number"!==typeof a)continue;b.severity=a}s(b)}if("number"===typeof u){var x=pt("InvalidCommentToken","Comments are not permitted in JSON.");t.comments.forEach((function(e){s(I.create(e,x,u,Ee.CommentNotPermitted))}))}return o};if(r){var c=r.id||"schemaservice://untitled/"+dt++;return this.jsonSchemaService.resolveSchemaContent(new ut(r),c,{}).then((function(e){return u(e)}))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then((function(e){return u(e)}))}}(),0);function mt(e){if(e&&"object"===typeof e){if(K(e.allowComments))return e.allowComments;if(e.allOf)for(var t=0,n=e.allOf;t<n.length;t++){var r=mt(n[t]);if(K(r))return r}}}function gt(e){if(e&&"object"===typeof e){if(K(e.allowTrailingCommas))return e.allowTrailingCommas;var t=e;if(K(t.allowsTrailingCommas))return t.allowsTrailingCommas;if(e.allOf)for(var n=0,r=e.allOf;n<r.length;n++){var i=gt(r[n]);if(K(i))return i}}}function vt(e){switch(e){case"error":return S.Error;case"warning":return S.Warning;case"ignore":return}}function yt(e){return e<48?0:e<=57?e-48:(e<97&&(e+=32),e>=97&&e<=102?e-97+10:0)}function bt(e){if("#"===e[0])switch(e.length){case 4:return{red:17*yt(e.charCodeAt(1))/255,green:17*yt(e.charCodeAt(2))/255,blue:17*yt(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*yt(e.charCodeAt(1))/255,green:17*yt(e.charCodeAt(2))/255,blue:17*yt(e.charCodeAt(3))/255,alpha:17*yt(e.charCodeAt(4))/255};case 7:return{red:(16*yt(e.charCodeAt(1))+yt(e.charCodeAt(2)))/255,green:(16*yt(e.charCodeAt(3))+yt(e.charCodeAt(4)))/255,blue:(16*yt(e.charCodeAt(5))+yt(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*yt(e.charCodeAt(1))+yt(e.charCodeAt(2)))/255,green:(16*yt(e.charCodeAt(3))+yt(e.charCodeAt(4)))/255,blue:(16*yt(e.charCodeAt(5))+yt(e.charCodeAt(6)))/255,alpha:(16*yt(e.charCodeAt(7))+yt(e.charCodeAt(8)))/255}}}!function(){function e(e){this.schemaService=e}e.prototype.findDocumentSymbols=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var o=n.resultLimit||Number.MAX_VALUE,a=e.uri;if(("vscode://defaultsettings/keybindings.json"===a||Qe(a.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var s=[],u=0,c=i.items;u<c.length;u++){var f=c[u];if("object"===f.type)for(var l=0,h=f.properties;l<h.length;l++){var p=h[l];if("key"===p.keyNode.value&&p.valueNode){var d=m.create(e.uri,xt(e,f));if(s.push({name:ze(p.valueNode),kind:le.Function,location:d}),--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),s}}}return s}for(var g=[{node:i,containerName:""}],v=0,y=!1,b=[],x=function(t,n){"array"===t.type?t.items.forEach((function(e){e&&g.push({node:e,containerName:n})})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var a=m.create(e.uri,xt(e,t)),s=n?n+"."+t.keyNode.value:t.keyNode.value;b.push({name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),location:a,containerName:n}),g.push({node:i,containerName:s})}else y=!0}))};v<g.length;){var A=g[v++];x(A.node,A.containerName)}return y&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),b},e.prototype.findDocumentSymbols2=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var o=n.resultLimit||Number.MAX_VALUE,a=e.uri;if(("vscode://defaultsettings/keybindings.json"===a||Qe(a.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var s=[],u=0,c=i.items;u<c.length;u++){var f=c[u];if("object"===f.type)for(var l=0,h=f.properties;l<h.length;l++){var p=h[l];if("key"===p.keyNode.value&&p.valueNode){var d=xt(e,f),m=xt(e,p.keyNode);if(s.push({name:ze(p.valueNode),kind:le.Function,range:d,selectionRange:m}),--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),s}}}return s}for(var g=[],v=[{node:i,result:g}],y=0,b=!1,x=function(t,n){"array"===t.type?t.items.forEach((function(t,i){if(t)if(o>0){o--;var a=xt(e,t),s=a,u={name:String(i),kind:r.getSymbolKind(t.type),range:a,selectionRange:s,children:[]};n.push(u),v.push({result:u.children,node:t})}else b=!0})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var a=xt(e,t),s=xt(e,t.keyNode),u=[],c={name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),range:a,selectionRange:s,children:u,detail:r.getDetail(i)};n.push(c),v.push({result:u,node:i})}else b=!0}))};y<v.length;){var A=v[y++];x(A.node,A.result)}return b&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),g},e.prototype.getSymbolKind=function(e){switch(e){case"object":return le.Module;case"string":return le.String;case"number":return le.Number;case"array":return le.Array;case"boolean":return le.Boolean;default:return le.Variable}},e.prototype.getKeyLabel=function(e){var t=e.keyNode.value;return t&&(t=t.replace(/[\n]/g,"\u21b5")),t&&t.trim()?t:'"'+t+'"'},e.prototype.getDetail=function(e){if(e)return"boolean"===e.type||"number"===e.type||"null"===e.type||"string"===e.type?String(e.value):"array"===e.type?e.children.length?void 0:"[]":"object"===e.type?e.children.length?void 0:"{}":void 0},e.prototype.findDocumentColors=function(e,t,n){return this.schemaService.getSchemaForResource(e.uri,t).then((function(r){var i=[];if(r)for(var o=n&&"number"===typeof n.resultLimit?n.resultLimit:Number.MAX_VALUE,a={},s=0,u=t.getMatchingSchemas(r.schema);s<u.length;s++){var c=u[s];if(!c.inverted&&c.schema&&("color"===c.schema.format||"color-hex"===c.schema.format)&&c.node&&"string"===c.node.type){var f=String(c.node.offset);if(!a[f]){var l=bt(ze(c.node));if(l){var h=xt(e,c.node);i.push({color:l,range:h})}if(a[f]=!0,--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(e.uri),i}}}return i}))},e.prototype.getColorPresentations=function(e,t,n,r){var i,o=[],a=Math.round(255*n.red),s=Math.round(255*n.green),u=Math.round(255*n.blue);function c(e){var t=e.toString(16);return 2!==t.length?"0"+t:t}return i=1===n.alpha?"#"+c(a)+c(s)+c(u):"#"+c(a)+c(s)+c(u)+c(Math.round(255*n.alpha)),o.push({label:i,textEdit:E.replace(r,JSON.stringify(i))}),o}}();function xt(e,t){return d.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}var At=Re(),Ct={schemaAssociations:[],schemas:{"http://json-schema.org/schema#":{$ref:"http://json-schema.org/draft-07/schema#"},"http://json-schema.org/draft-04/schema#":{title:At("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{title:At("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},St={id:At("schema.json.id","A unique identifier for the schema."),$schema:At("schema.json.$schema","The schema to verify this document against."),title:At("schema.json.title","A descriptive title of the element."),description:At("schema.json.description","A long description of the element. Used in hover menus and suggestions."),default:At("schema.json.default","A default value. Used by suggestions."),multipleOf:At("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:At("schema.json.maximum","The maximum numerical value, inclusive by default."),exclusiveMaximum:At("schema.json.exclusiveMaximum","Makes the maximum property exclusive."),minimum:At("schema.json.minimum","The minimum numerical value, inclusive by default."),exclusiveMinimum:At("schema.json.exclusiveMininum","Makes the minimum property exclusive."),maxLength:At("schema.json.maxLength","The maximum length of a string."),minLength:At("schema.json.minLength","The minimum length of a string."),pattern:At("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored."),additionalItems:At("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:At("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:At("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive."),minItems:At("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive."),uniqueItems:At("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false."),maxProperties:At("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive."),minProperties:At("schema.json.minProperties","The minimum number of properties an object can have. Inclusive."),required:At("schema.json.required","An array of strings that lists the names of all properties required on this object."),additionalProperties:At("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:At("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:At("schema.json.properties","A map of property names to schemas for each property."),patternProperties:At("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties."),dependencies:At("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:At("schema.json.enum","The set of literal values that are valid."),type:At("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:At("schema.json.format","Describes the format expected for the value."),allOf:At("schema.json.allOf","An array of schemas, all of which must match."),anyOf:At("schema.json.anyOf","An array of schemas, where at least one must match."),oneOf:At("schema.json.oneOf","An array of schemas, exactly one of which must match."),not:At("schema.json.not","A schema which must not match."),$id:At("schema.json.$id","A unique identifier for the schema."),$ref:At("schema.json.$ref","Reference a definition hosted on any location."),$comment:At("schema.json.$comment","Comments from schema authors to readers or maintainers of the schema."),readOnly:At("schema.json.readOnly","Indicates that the value of the instance is managed exclusively by the owning authority."),examples:At("schema.json.examples","Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:At("schema.json.contains",'An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:At("schema.json.propertyNames","If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:At("schema.json.const","An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:At("schema.json.contentMediaType","Describes the media type of a string property."),contentEncoding:At("schema.json.contentEncoding","Describes the content encoding of a string property."),if:At("schema.json.if",'The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:At("schema.json.then",'The "if" subschema is used for validation when the "if" subschema succeeds.'),else:At("schema.json.else",'The "else" subschema is used for validation when the "if" subschema fails.')};for(var kt in Ct.schemas){var wt=Ct.schemas[kt];for(var It in wt.properties){var jt=wt.properties[It];"boolean"===typeof jt&&(jt=wt.properties[It]={});var Et=St[It];Et?jt.description=Et:console.log(It+": localize('schema.json."+It+'\', "")')}}var Tt=function(){function e(e,t,n){var r=this;this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);var o=function(e){var t,n=e.getModeId();n===r._languageId&&(r._listener[e.uri.toString()]=e.onDidChangeContent((function(){clearTimeout(t),t=setTimeout((function(){return r._doValidate(e.uri,n)}),500)})),r._doValidate(e.uri,n))},a=function(e){i.j6.setModelMarkers(e,r._languageId,[]);var t=e.uri.toString(),n=r._listener[t];n&&(n.dispose(),delete r._listener[t])};this._disposables.push(i.j6.onDidCreateModel(o)),this._disposables.push(i.j6.onWillDisposeModel((function(e){a(e),r._resetSchema(e.uri)}))),this._disposables.push(i.j6.onDidChangeModelLanguage((function(e){a(e.model),o(e.model),r._resetSchema(e.model.uri)}))),this._disposables.push(n.onDidChange((function(e){i.j6.getModels().forEach((function(e){e.getModeId()===r._languageId&&(a(e),o(e))}))}))),this._disposables.push({dispose:function(){for(var e in i.j6.getModels().forEach(a),r._listener)r._listener[e].dispose()}}),i.j6.getModels().forEach(o)}return e.prototype.dispose=function(){this._disposables.forEach((function(e){return e&&e.dispose()})),this._disposables=[]},e.prototype._resetSchema=function(e){this._worker().then((function(t){t.resetSchema(e.toString())}))},e.prototype._doValidate=function(e,t){this._worker(e).then((function(n){return n.doValidation(e.toString()).then((function(n){var r=n.map((function(e){return function(e,t){var n="number"===typeof t.code?String(t.code):t.code;return{severity:Ot(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source}}(0,e)})),o=i.j6.getModel(e);o&&o.getModeId()===t&&i.j6.setModelMarkers(o,t,r)}))})).then(void 0,(function(e){console.error(e)}))},e}();function Ot(e){switch(e){case S.Error:return i.ZL.Error;case S.Warning:return i.ZL.Warning;case S.Information:return i.ZL.Info;case S.Hint:return i.ZL.Hint;default:return i.ZL.Info}}function _t(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function Mt(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function Pt(e){if(e)return new i.e6(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function Vt(e){var t=i.Mj.CompletionItemKind;switch(e){case Q.Text:return t.Text;case Q.Method:return t.Method;case Q.Function:return t.Function;case Q.Constructor:return t.Constructor;case Q.Field:return t.Field;case Q.Variable:return t.Variable;case Q.Class:return t.Class;case Q.Interface:return t.Interface;case Q.Module:return t.Module;case Q.Property:return t.Property;case Q.Unit:return t.Unit;case Q.Value:return t.Value;case Q.Enum:return t.Enum;case Q.Keyword:return t.Keyword;case Q.Snippet:return t.Snippet;case Q.Color:return t.Color;case Q.File:return t.File;case Q.Reference:return t.Reference}return t.Property}function Nt(e){if(e)return{range:Pt(e.range),text:e.newText}}var Ft=function(){function e(e){this._worker=e}return Object.defineProperty(e.prototype,"triggerCharacters",{get:function(){return[" ",":"]},enumerable:!1,configurable:!0}),e.prototype.provideCompletionItems=function(e,t,n,r){var o=e.uri;return this._worker(o).then((function(e){return e.doComplete(o.toString(),_t(t))})).then((function(n){if(n){var r=e.getWordUntilPosition(t),o=new i.e6(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn),a=n.items.map((function(e){var t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,range:o,kind:Vt(e.kind)};return e.textEdit&&(!function(e){return"undefined"!==typeof e.insert&&"undefined"!==typeof e.replace}(e.textEdit)?t.range=Pt(e.textEdit.range):t.range={insert:Pt(e.textEdit.insert),replace:Pt(e.textEdit.replace)},t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Nt)),e.insertTextFormat===Y.Snippet&&(t.insertTextRules=i.Mj.CompletionItemInsertTextRule.InsertAsSnippet),t}));return{isIncomplete:n.isIncomplete,suggestions:a}}}))},e}();function Rt(e){return"string"===typeof e?{value:e}:(t=e)&&"object"===typeof t&&"string"===typeof t.kind?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+"\n"+e.value+"\n```\n"};var t}function Lt(e){if(e)return Array.isArray(e)?e.map(Rt):[Rt(e)]}var $t=function(){function e(e){this._worker=e}return e.prototype.provideHover=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.doHover(r.toString(),_t(t))})).then((function(e){if(e)return{range:Pt(e.range),contents:Lt(e.contents)}}))},e}();function Dt(e){var t=i.Mj.SymbolKind;switch(e){case le.File:return t.Array;case le.Module:return t.Module;case le.Namespace:return t.Namespace;case le.Package:return t.Package;case le.Class:return t.Class;case le.Method:return t.Method;case le.Property:return t.Property;case le.Field:return t.Field;case le.Constructor:return t.Constructor;case le.Enum:return t.Enum;case le.Interface:return t.Interface;case le.Function:return t.Function;case le.Variable:return t.Variable;case le.Constant:return t.Constant;case le.String:return t.String;case le.Number:return t.Number;case le.Boolean:return t.Boolean;case le.Array:return t.Array}return t.Function}var Ut=function(){function e(e){this._worker=e}return e.prototype.provideDocumentSymbols=function(e,t){var n=e.uri;return this._worker(n).then((function(e){return e.findDocumentSymbols(n.toString())})).then((function(e){if(e)return e.map((function(e){return{name:e.name,detail:"",containerName:e.containerName,kind:Dt(e.kind),range:Pt(e.location.range),selectionRange:Pt(e.location.range),tags:[]}}))}))},e}();function Wt(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}var qt=function(){function e(e){this._worker=e}return e.prototype.provideDocumentFormattingEdits=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.format(r.toString(),null,Wt(t)).then((function(e){if(e&&0!==e.length)return e.map(Nt)}))}))},e}(),Bt=function(){function e(e){this._worker=e}return e.prototype.provideDocumentRangeFormattingEdits=function(e,t,n,r){var i=e.uri;return this._worker(i).then((function(e){return e.format(i.toString(),Mt(t),Wt(n)).then((function(e){if(e&&0!==e.length)return e.map(Nt)}))}))},e}(),Kt=function(){function e(e){this._worker=e}return e.prototype.provideDocumentColors=function(e,t){var n=e.uri;return this._worker(n).then((function(e){return e.findDocumentColors(n.toString())})).then((function(e){if(e)return e.map((function(e){return{color:e.color,range:Pt(e.range)}}))}))},e.prototype.provideColorPresentations=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.getColorPresentations(r.toString(),t.color,Mt(t.range))})).then((function(e){if(e)return e.map((function(e){var t={label:e.label};return e.textEdit&&(t.textEdit=Nt(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Nt)),t}))}))},e}(),Jt=function(){function e(e){this._worker=e}return e.prototype.provideFoldingRanges=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.getFoldingRanges(r.toString(),t)})).then((function(e){if(e)return e.map((function(e){var t={start:e.startLine+1,end:e.endLine+1};return"undefined"!==typeof e.kind&&(t.kind=function(e){switch(e){case x.Comment:return i.Mj.FoldingRangeKind.Comment;case x.Imports:return i.Mj.FoldingRangeKind.Imports;case x.Region:return i.Mj.FoldingRangeKind.Region}return}(e.kind)),t}))}))},e}();var zt=function(){function e(e){this._worker=e}return e.prototype.provideSelectionRanges=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.getSelectionRanges(r.toString(),t.map(_t))})).then((function(e){if(e)return e.map((function(e){for(var t=[];e;)t.push({range:Pt(e.range)}),e=e.parent;return t}))}))},e}();function Ht(e){return{getInitialState:function(){return new un(null,null,!1,null)},tokenize:function(t,n,r,i){return function(e,t,n,r,i){void 0===r&&(r=0);var o=0,a=!1;switch(n.scanError){case 2:t='"'+t,o=1;break;case 1:t="/*"+t,o=2}var s=R(t),u=n.lastWasColon,c=n.parents,f={tokens:[],endState:n.clone()};for(;;){var l=r+s.getPosition(),h="",p=s.scan();if(17===p)break;if(l===r+s.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+t.substr(s.getPosition(),3));switch(a&&(l-=o),a=o>0,p){case 1:c=sn.push(c,0),h=Gt,u=!1;break;case 2:c=sn.pop(c),h=Gt,u=!1;break;case 3:c=sn.push(c,1),h=Xt,u=!1;break;case 4:c=sn.pop(c),h=Xt,u=!1;break;case 6:h=Zt,u=!0;break;case 5:h=Qt,u=!1;break;case 8:case 9:h=Yt,u=!1;break;case 7:h=en,u=!1;break;case 10:var d=c?c.type:0;h=u||1===d?tn:rn,u=!1;break;case 11:h=nn,u=!1}if(e)switch(p){case 12:h=an;break;case 13:h=on}f.endState=new un(n.getStateData(),s.getTokenError(),u,c),f.tokens.push({startIndex:l,scopes:h})}return f}(e,t,n,r)}}}var Gt="delimiter.bracket.json",Xt="delimiter.array.json",Zt="delimiter.colon.json",Qt="delimiter.comma.json",Yt="keyword.json",en="keyword.json",tn="string.value.json",nn="number.json",rn="string.key.json",on="comment.block.json",an="comment.line.json",sn=function(){function e(e,t){this.parent=e,this.type=t}return e.pop=function(e){return e?e.parent:null},e.push=function(t,n){return new e(t,n)},e.equals=function(e,t){if(!e&&!t)return!0;if(!e||!t)return!1;for(;e&&t;){if(e===t)return!0;if(e.type!==t.type)return!1;e=e.parent,t=t.parent}return!0},e}(),un=function(){function e(e,t,n,r){this._state=e,this.scanError=t,this.lastWasColon=n,this.parents=r}return e.prototype.clone=function(){return new e(this._state,this.scanError,this.lastWasColon,this.parents)},e.prototype.equals=function(t){return t===this||!!(t&&t instanceof e)&&(this.scanError===t.scanError&&this.lastWasColon===t.lastWasColon&&sn.equals(this.parents,t.parents))},e.prototype.getStateData=function(){return this._state},e.prototype.setStateData=function(e){this._state=e},e}();function cn(e){var t=[],n=[],r=new o(e);t.push(r);var a=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r.getLanguageServiceWorker.apply(r,e)};function s(){var t=e.languageId,r=e.modeConfiguration;ln(n),r.documentFormattingEdits&&n.push(i.Mj.registerDocumentFormattingEditProvider(t,new qt(a))),r.documentRangeFormattingEdits&&n.push(i.Mj.registerDocumentRangeFormattingEditProvider(t,new Bt(a))),r.completionItems&&n.push(i.Mj.registerCompletionItemProvider(t,new Ft(a))),r.hovers&&n.push(i.Mj.registerHoverProvider(t,new $t(a))),r.documentSymbols&&n.push(i.Mj.registerDocumentSymbolProvider(t,new Ut(a))),r.tokens&&n.push(i.Mj.setTokensProvider(t,Ht(!0))),r.colors&&n.push(i.Mj.registerColorProvider(t,new Kt(a))),r.foldingRanges&&n.push(i.Mj.registerFoldingRangeProvider(t,new Jt(a))),r.diagnostics&&n.push(new Tt(t,a,e)),r.selectionRanges&&n.push(i.Mj.registerSelectionRangeProvider(t,new zt(a)))}s(),t.push(i.Mj.setLanguageConfiguration(e.languageId,hn));var u=e.modeConfiguration;return e.onDidChange((function(e){e.modeConfiguration!==u&&(u=e.modeConfiguration,s())})),t.push(fn(n)),fn(t)}function fn(e){return{dispose:function(){return ln(e)}}}function ln(e){for(;e.length;)e.pop().dispose()}var hn={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]}}}]);