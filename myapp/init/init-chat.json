[{"name": "gpt4", "icon": "<svg t=\"1682394317506\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2833\" width=\"50\" height=\"50\"><path d=\"M431.207059 2.199998C335.414129 13.19899 257.420186 72.593947 219.024215 163.78688l-6.199996 14.797989-19.997985 5.799996C104.233299 210.582846 38.840347 279.776795 15.041364 372.369727c-6.999995 27.39698-8.999993 71.393948-4.199997 99.990927 7.399995 44.996967 26.597981 88.592935 53.795961 121.989911l9.198993 11.399991-5.199996 19.597986c-6.799995 26.597981-8.598994 74.593945-3.799997 103.190924 14.799989 87.392936 75.193945 163.58688 155.587886 196.383857 46.395966 18.998986 95.99193 24.797982 142.187895 16.798987l11.599992-1.999998 18.597986 17.598987c30.396978 28.596979 66.593951 48.395965 108.789921 59.994956 25.998981 6.999995 83.193939 8.999993 111.391918 3.599997 53.194961-9.799993 98.391928-33.797975 137.1889-72.794946 27.996979-28.196979 51.194963-64.393953 59.794956-93.591932 2.199998-6.999995 3.599997-8.599994 8.798993-9.799993 12.798991-2.598998 42.595969-13.39799 56.194959-20.196985 35.996974-17.998987 72.793947-49.195964 94.792931-80.593941 19.797985-28.197979 36.196973-65.993952 44.395967-102.990924 1.799999-7.799994 2.799998-24.997982 2.799998-48.995965 0-33.997975-0.6-38.796972-5.799996-58.995956-9.998993-38.795972-25.997981-71.993947-48.395964-100.190927l-10.198993-12.799991 4.399997-17.597987c26.79698-102.790925-16.798988-217.181841-105.391923-276.576797-30.996977-20.598985-58.194957-31.997977-95.59193-40.196971-22.397984-4.999996-70.993948-5.799996-91.991932-1.799998-12.399991 2.399998-12.99999 2.399998-15.799989-1.599999-4.598997-7.199995-34.795975-31.596977-52.794961-42.995969C548.196973 9.598993 486.603019-4.199997 431.207059 2.199998z m45.395967 67.793951c25.197982 2.399998 40.39697 6.399995 61.394955 16.198988 16.797988 7.799994 41.995969 23.397983 41.995969 25.997981 0 0.799999-45.595967 27.79798-101.390926 59.794956-55.995959 32.196976-104.591923 60.794955-108.19092 63.394954-14.799989 10.998992-14.399989 8.399994-14.59999 97.591928-0.2 43.995968-0.999999 110.389919-1.599998 147.387892l-1.199 67.393951-42.596968-24.397982-42.595969-24.397982 0.599999-134.988902c0.799999-154.386887 0.2-147.987892 19.597986-187.383862 29.797978-60.395956 86.792936-100.191927 151.987889-106.591922 8.199994-0.799999 15.398989-1.599999 15.998988-1.599999 0.6-0.2 9.798993 0.6 20.597985 1.599999z m268.977803 82.992939c73.393946 15.399989 132.189903 74.193946 147.387892 147.987892 3.599997 16.998988 4.599997 62.394954 1.599999 67.79495-1.199999 2.399998-22.797983-9.399993-108.590921-59.394957-105.391923-61.394955-107.191921-62.394954-117.989913-62.394954-10.799992 0-13.19999 1.399999-137.989899 73.593946l-126.989907 73.393946-0.599-49.395963c-0.2-27.19798 0.2-49.995963 1-50.795963 3.799997-3.599997 209.182847-121.189911 223.581836-127.989906 35.796974-16.797988 77.992943-21.397984 118.589913-12.798991z m-537.955606 362.369735c3.199998 4.599997 37.596972 25.398981 130.389904 78.993942 69.393949 39.796971 125.988908 72.993947 125.988908 73.593946 0 0.6-5.599996 4.199997-12.598991 8.199994-6.799995 3.799997-25.997981 14.797989-42.596968 24.397982l-30.196978 17.597987-107.790921-62.194954c-59.194957-34.196975-114.589916-67.393951-122.78991-73.793946-29.397978-22.597983-56.395959-63.793953-66.194952-101.190926-6.199995-24.197982-7.199995-60.794955-2.199998-84.992938 7.599994-36.996973 23.397983-66.994951 49.195964-93.792931 17.398987-17.997987 33.197976-29.396978 55.195959-40.195971l16.997988-8.199994 0.999999 127.589907 0.999999 127.589906 4.599997 6.398996zM750.379825 367.169731c56.394959 32.596976 108.389921 62.994954 115.589916 67.593951 43.396968 28.597979 73.593946 75.793944 81.99294 127.989906 3.599997 21.597984 1.599999 61.994955-3.999997 80.992941-8.998993 31.397977-24.996982 58.995957-47.594966 82.593939-17.598987 18.397987-48.195965 38.995971-65.794951 44.395967l-4.599997 1.399999v-124.189909c0-138.188899 0.4-133.389902-13.59899-143.387895-4.399997-2.999998-62.393954-37.196973-128.988906-75.593944-66.594951-38.596972-121.189911-70.393948-121.189911-70.993948-0.2-0.799999 83.592939-49.795964 85.192938-49.995964 0.4 0 46.595966 26.597981 102.991924 59.194957z m-181.385867 50.195963l54.99596 31.596977v127.989906l-55.19596 31.596977-55.194959 31.797977-39.196971-22.598983c-21.797984-12.398991-46.795966-26.99698-55.994959-32.196977l-16.398988-9.799993 0.399999-63.393953 0.6-63.394954 53.99496-31.396977c29.797978-17.198987 54.79596-31.397977 55.59596-31.397977 0.799999-0.2 26.197981 13.99999 56.394958 31.197977z m147.587892 85.592938l41.39697 23.797982v127.389907c0 139.787898-0.4 146.187893-11.999991 178.384869-11.597992 31.796977-36.595973 65.394952-64.593953 86.592937-6.799995 5.199996-21.397984 13.79899-32.396976 18.997986-51.995962 24.997982-109.59092 25.597981-162.586881 1.799999-12.598991-5.799996-40.39697-23.397983-40.396971-25.797982 0-0.6 46.996966-28.196979 104.191924-61.194955 57.394958-32.996976 107.190921-62.794954 110.789919-66.193951 3.799997-3.799997 7.399995-9.999993 8.799993-15.399989 1.599999-6.398995 2.199998-50.994963 2.199999-151.386889 0-78.392943 0.799999-141.987896 1.599999-141.587896 0.799999 0.2 20.197985 11.398992 42.995968 24.597982zM622.590919 732.139464c-3.799997 3.599997-205.38285 119.189913-221.781838 126.989907-26.597981 12.798991-47.995965 17.397987-79.792941 17.397987-19.798985 0-30.197978-0.999999-43.596968-4.199997-68.59395-16.997988-120.589912-66.193952-140.587897-133.787902-5.599996-18.798986-8.599994-57.395958-5.999996-75.193945l1.399999-9.199993 50.395963 29.197979c174.185872 100.391926 165.185879 95.59193 176.185871 95.591929 9.598993-0.2 16.597988-3.799997 137.1879-73.393946l126.989907-73.393946 0.599999 49.395964c0.2 26.99798-0.2 49.795964-0.999999 50.595963z\" p-id=\"2834\"></path></svg>", "label": "gpt4", "doc": "", "session_num": 5, "chat_type": "text", "hello": "我是原生chatgpt，我可以进行多轮对话，快来试试吧", "tips": "", "knowledge": "{}", "prompt": "{{history}}\nHuman:{{query}}\nAI:", "service_type": "openai", "service_config": {"llm_data": {"model": "gpt-4-turbo-2024-04-09"}}, "owner": "admin,*", "expand": {"index": 1, "isPublic": true}}, {"name": "native", "icon": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" width=\"50px\" height=\"50px\" viewBox=\"0 0 512 512\" enable-background=\"new 0 0 512 512\" xml:space=\"preserve\">  <image id=\"image0\" width=\"512\" height=\"512\" x=\"0\" y=\"0\"    xlink:href=\"data:image/png;base64,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\" /></svg>", "label": "gpt4o", "doc": "", "session_num": 5, "chat_type": "text", "hello": "我是原生chatgpt，我可以进行多轮对话，支持图文输入，快来试试吧", "tips": "", "knowledge": "{}", "prompt": "{{history}}\nHuman:{{query}}\nAI:", "service_type": "openai", "service_config": {"llm_data": {"model": "gpt-4o-2024-05-13"}}, "owner": "admin,*", "expand": {"index": 2, "isPublic": true}}, {"name": "deepseek", "icon": "<svg t=\"1738849164154\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"4171\" width=\"50\" height=\"50\"><path d=\"M979.968 219.0336c-10.0864-5.0176-14.592 4.5056-20.5824 9.3184-1.9456 1.6896-3.7888 3.584-5.4784 5.4784-14.848 16.0256-32.0512 26.3168-54.784 25.088-32.9216-1.8944-61.1328 8.6016-86.016 33.9456-5.2224-31.2832-22.9376-49.92-49.664-61.9008-14.1312-6.1952-28.2112-12.4416-37.9904-26.0608-6.9632-9.5232-8.8576-20.2752-12.2368-30.8224-2.0992-6.4512-4.2496-12.9024-11.6736-14.08-7.8848-1.1776-11.008 5.4784-14.1312 11.008-12.3904 22.6816-17.2032 48.0256-16.896 73.3696 1.1264 57.088 25.2928 102.7072 73.3184 135.2192 5.4784 3.84 6.912 7.424 5.2736 12.9024-3.3792 11.264-7.168 22.016-10.5472 33.2288-2.1504 7.168-5.4784 8.8576-13.1584 5.7344a217.344 217.344 0 0 1-69.2736-47.104c-34.1504-32.9728-65.024-69.5296-103.424-97.9456a511.0784 511.0784 0 0 0-27.5456-18.8928c-39.168-38.2464 5.2736-69.5296 15.5648-73.3696 10.752-3.7888 3.7888-17.2032-31.0784-16.9472-34.6624 0-66.4576 11.7248-107.0592 27.2384-5.9904 2.4064-12.1856 4.096-18.432 5.4784a385.9968 385.9968 0 0 0-114.8928-4.0448c-75.0592 8.3456-135.0144 43.9808-179.2 104.6528C37.1712 383.488 24.4736 466.3296 39.7824 552.8576c16.0256 91.0336 62.3616 166.5536 133.8368 225.3312C247.5008 839.3728 332.8 869.2736 430.08 863.5392c59.0336-3.3792 124.7232-11.264 198.8096-74.0864 18.6368 9.3184 38.2464 12.9024 70.9632 15.7696 25.088 2.3552 49.2544-1.2288 67.8912-5.0176 29.3888-6.2464 27.2384-33.2288 16.6912-38.5024-85.76-39.936-66.8672-23.6544-84.1216-36.8128 43.776-51.6096 109.4656-105.3696 135.0144-279.3472 1.9456-13.824 0.256-22.4256 0-33.6896 0-6.656 1.4336-9.5232 9.1136-10.24 21.504-2.1504 42.496-8.6016 61.6448-18.944 55.7056-30.3104 77.9264-80.2304 83.4048-140.2368 0.7168-9.0624 0-18.6368-9.7792-23.3984h0.2048zM495.2576 757.9136c-83.1488-65.536-123.5456-87.04-140.288-86.016-15.5136 0.9216-12.9024 18.5856-9.3184 30.3104 3.584 11.4688 8.3968 19.3536 14.848 29.3888 4.5056 6.656 7.6288 16.7424-4.608 24.1664-26.7264 16.6912-73.3184-5.5296-75.4688-6.7072-54.272-32.0512-99.6352-74.0864-131.4304-131.9424a401.7152 401.7152 0 0 1-51.6096-178.688c-0.7168-15.5648 3.7888-20.8384 18.8928-23.7056a193.024 193.024 0 0 1 60.928-1.6384c84.8384 12.3904 157.184 50.3808 217.7024 110.592 34.6112 34.4576 60.672 75.264 87.7056 115.456 28.672 42.496 59.4944 82.944 98.6624 116.1216 13.824 11.7248 24.8832 20.5824 35.328 27.0336-32 3.584-85.248 4.3008-121.6-24.6272l0.256 0.256z m39.936-256.9216a12.288 12.288 0 0 1 12.1344-12.1856c6.7072 0 12.1856 5.5296 12.1856 12.1856a12.288 12.288 0 0 1-12.3904 12.1856 12.032 12.032 0 0 1-12.1856-11.9296v-0.256h0.2048z m123.7504 63.7952a79.872 79.872 0 0 1-23.3984 6.4512 51.8656 51.8656 0 0 1-31.7952-10.0352c-10.752-9.0624-18.6368-14.336-22.016-30.3104a64.1024 64.1024 0 0 1 0.7168-23.4496c2.8672-13.1072-0.2048-21.248-9.5232-28.928-7.424-6.144-16.9984-7.8848-27.4944-7.8848a18.432 18.432 0 0 1-10.0352-3.072 10.24 10.24 0 0 1-5.376-6.144c-0.4608-1.3824-0.5632-2.816-0.4608-4.1984a10.3424 10.3424 0 0 1 1.28-3.9936c1.2288-2.1504 6.4512-7.424 7.68-8.3968 14.08-8.0896 30.5664-5.4784 45.6192 0.7168 14.08 5.7344 24.576 16.2816 39.936 31.0784 15.5136 17.92 18.3808 22.9376 27.2384 36.5568 6.912 10.5472 13.3632 21.504 17.664 33.9456 2.6112 7.68-0.7168 14.08-10.0352 17.92v-0.256z\" fill=\"#4D6BFE\" p-id=\"4172\"></path></svg>", "label": "deepseek", "doc": "https://www.deepseek.com/", "session_num": 5, "chat_type": "text", "hello": "使用openai协议接口部署deepseek", "tips": "", "knowledge": "{}", "prompt": "{{query}}", "service_type": "openai", "service_config": {"llm_url": "http://deepseek-qwen2-14b-202503011.service:8000/v1/", "llm_tokens": [], "llm_data": {"model": "deepseek-qwen2-14b"}, "stream": "true", "after": [["replace", "<think>\n", "思考中...\n```\n"], ["replace", "</think>\n\n", "```\n回答中...\n\n"]]}, "owner": "admin,*", "expand": {"index": 3, "isPublic": true}}, {"name": "chatglm4", "icon": "<svg t=\"1710751116433\" class=\"icon\" viewBox=\"0 0 1058 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"4248\" width=\"50\" height=\"50\"><path d=\"M589.369 129.889c32.464 7.403 116.326 30.522 183.925 105.95 15.596 17.415 60.803 69.055 77.794 148.973 3.216 14.988 5.219 30.158 6.068 45.39 0.971 6.492 1.7 12.803 2.245 19.175 0.304 6.007 0.486 11.954 0.486 17.961v0.85a256.015 256.015 0 0 1-1.76 29.248l1.214-12.864c-1.275 19.721-4.855 37.744-10.134 53.036l-2.306 3.398-0.303 0.607a45.936 45.936 0 0 1-2.852 3.64l0.667-4.854 1.457-10.74a308.08 308.08 0 0 0-11.59-120.939 303.59 303.59 0 0 0-24.03-57.405C754.3 248.218 641.19 177.948 510.664 177.948c-186.05 0-336.782 142.905-336.782 319.124 0 176.22 150.733 319.124 336.782 319.124h5.947l5.886-0.182a332.899 332.899 0 0 0 33.92-2.73c36.046-8.01 63.11-32.222 65.962-61.47a61.713 61.713 0 0 1 11.711 42.234 125.004 125.004 0 0 0 45.511-22.331l1.82-1.396c3.46-2.67 7.404-5.704 11.955-10.073 4.612-4.308 8.799-9.102 12.561-14.139 2.063-3.095 5.28-8.01 8.01-13.957a57.647 57.647 0 0 0 3.64-29.976 15.049 15.049 0 0 0-14.927-13.654h-62.198l0.121-0.424c4.37-19.418 38.593-34.71 80.221-34.71 26.214 0 49.455 6.068 64.322 15.413a43.812 43.812 0 0 1 6.311 4.672c0.728 0.547 1.396 1.214 2.124 1.942a28.581 28.581 0 0 0 6.068 1.092 388.703 388.703 0 0 1 39.443 0l-0.121 1.093c-0.971 4.915-2.367 12.743-5.037 21.663-15.959 54.856-51.579 88.777-63.837 100.246a235.862 235.862 0 0 1-26.882 21.663c-58.618 41.81-121.363 57.769-141.266 62.441-17.598 4.066-120.999 26.336-228.162-11.53-136.291-47.998-275.252-190.539-271.247-370.52 3.702-168.33 130.466-271.793 157.712-292.788 141.994-109.53 302.982-83.498 329.136-78.886z m-104.98 121.484a90.415 90.415 0 0 1 70.998 28.945 168.937 168.937 0 0 1 171.85 120.15l4.127 27.185 1.092 6.19 1.82 10.922 14.989 97.272 0.364 2.792 1.638 10.133 0.728 5.22v2.002c1.942 14.017 13.957 24.515 28.156 24.515a28.52 28.52 0 0 0 24.637-14.442l0.243-0.607 2.002-4.673 3.216-7.524a5.825 5.825 0 0 1 1.335-2.003 6.068 6.068 0 0 1 6.675-1.395l2.003 0.91 9.284 4.005 13.107 5.461 0.729 0.607c2.184 1.578 3.034 4.43 2.002 6.918l-4.066 9.709-0.06 0.546a60.985 60.985 0 0 1-6.068 14.685l-0.425 0.546-0.364 0.607-0.486 0.85c-12.379 19.539-34.77 32.95-57.526 32.95h-15.292a62.016 62.016 0 0 1-59.225-44.359l-0.242-1.031-0.486-1.7 0.304 1.578-0.486-1.76a17.658 17.658 0 0 1-0.97-4.004l-3.642-15.778-0.121-0.728a18.63 18.63 0 0 1-0.728-3.034l-0.79-3.337 0.062 1.88a2.549 2.549 0 0 1-0.061-1.213l-0.121-0.91v-1.092l-0.182-0.668-0.183-0.91-0.12-0.91-0.426 0.485-0.424-1.213-0.243-1.457a44.54 44.54 0 0 0-20.207-28.338 44.722 44.722 0 0 0-22.452-6.068c-1.457 0-2.913 0-4.309 0.182-0.728 0-1.456 0-2.184 0.182-2.124 0.364-4.248 0.85-6.25 1.456l-1.639 0.546a45.33 45.33 0 0 0-5.825 2.428l-1.153 0.606-0.728 0.425-0.728 0.486-1.578 1.031c-0.182 0-0.364 0.182-0.607 0.364a3.944 3.944 0 0 0-1.396 0.91l-0.789 0.607-0.788 0.607-0.85 0.607-0.789 0.667c-0.546 0.365-1.092 0.91-1.456 1.275l-1.214 1.213-0.485 0.607a4.855 4.855 0 0 1-0.425 0.546c0 0.182-0.182 0.364-0.364 0.547a3.944 3.944 0 0 0-0.728 0.85l-0.546 0.606a9.891 9.891 0 0 0-2.792 3.762 3.155 3.155 0 0 1-0.607 1.032 7.282 7.282 0 0 1-0.85 1.577l-0.485 0.79a4.49 4.49 0 0 1-0.424 0.849 8.556 8.556 0 0 1-0.607 1.335l-0.304 0.789-0.364 1.031a6.493 6.493 0 0 0-0.364 0.971l-0.242 1.092-0.304 0.85-0.243 1.214-0.303 0.97a30.583 30.583 0 0 0-0.546 1.942l-0.06 0.728-0.244 1.457-0.303 1.092-0.182 2.124-0.06 1.092-0.183 1.032v77.672l-0.546 27.732a27.61 27.61 0 0 1-27.428 27.488h-69.905a27.55 27.55 0 0 1-27.428-27.428v-34.77a44.662 44.662 0 0 0-88.959 5.704v1.213l-0.546 28.035a27.307 27.307 0 0 1-27.367 27.428H293.91a27.55 27.55 0 0 1-27.428-27.488v-170.09c0-1.214 0.121-2.428 0.242-3.58a119.543 119.543 0 0 1 103.766-114.567c0 1.213 0.182 2.67 0.364 3.944 1.638 9.345 4.005 18.265 6.371 27.307 1.457 5.46 2.913 10.922 4.248 16.384 2.61 11.347 6.068 20.935 9.891 31.554l2.61 7.221c3.58 10.316 7.281 18.386 10.983 25.79l5.461 10.8c3.58 7.404 7.646 14.564 12.258 21.36l0.364 0.608 0.667 0.97a77.588 77.588 0 0 0 4.309 5.705l1.031 1.213a83.255 83.255 0 0 0 35.924 22.574c6.068 1.942 12.197 2.184 18.326 1.638l65.536-31.979-66.993-69.298c-4.308 13.35-10.922 22.816-19.296 26.214l23.18 41.203a18.326 18.326 0 0 1-9.952 2.427 25.608 25.608 0 0 1-11.226-3.58 10.073 10.073 0 0 1-3.337-1.7 45.268 45.268 0 0 1-11.651-11.225c-12.743-14.867-24.758-38.654-32.768-67.053l-0.668-2.003-0.485-1.456-0.91-2.427a180.467 180.467 0 0 1-7.282-28.4 262.387 262.387 0 0 1-6.736-33.01l-0.121-0.85a30.705 30.705 0 0 1-0.425-2.548l-5.097-68.449a45.997 45.997 0 0 1 42.84-48.97l34.953-2.306a45.33 45.33 0 0 1 11.53 0.607z m172.397 125.49l-5.037 0.485a28.52 28.52 0 0 0-19.114 42.72 28.763 28.763 0 0 1 53.157-13.108v-1.153a28.035 28.035 0 0 0-2.124-10.922 28.52 28.52 0 0 0-21.967-17.659l-4.854-0.364z m221.366 159.834c-2.852-1.456 3.64-13.774 12.44-47.695 0 0 10.194-39.443 25.243-72.818 1.214-2.67 4.248-9.466 11.47-13.047 1.455-0.728 8.434-4.247 13.956-1.092 5.947 3.216 6.31 11.651 6.675 15.777a37.623 37.623 0 0 1-4.49 20.45c-23.12 44.297-27.61 50.73-27.61 50.73-26.519 40.899-35.196 49.152-37.623 47.695z m167.48-101.823c4.855 2.852 10.742 6.25 12.137 12.56 3.641 15.535-24.88 35.62-45.875 44.42-2.852 1.213-5.825 2.245-8.738 3.216-2.549 0.91-6.493 2.366-11.53 3.398-10.558 2.549-17.719 2.003-17.9 3.823-0.365 1.942 8.616 2.67 14.199 10.316 1.031 1.638 3.944 5.583 3.398 10.073-0.364 3.398-3.034 8.495-20.632 15.777a149.088 149.088 0 0 1-30.462 9.527c-24.576 4.673-54.856 8.253-55.22 5.886-0.182-1.031 5.522-1.942 20.996-8.556 6.493-2.791 12.864-5.947 18.932-9.527 4.855-2.852 8.617-5.583 11.712-7.706 6.25-4.49 10.012-7.525 11.105-8.435a50.244 50.244 0 0 0 10.012-10.74 12.986 12.986 0 0 0 2.367-5.037c0.728-3.216-0.607-4.673 0.182-6.797 1.031-2.67 4.126-3.762 5.522-4.308 10.922-3.944 25.971-12.561 32.464-16.87 6.797-4.49 11.833-8.07 13.775-14.684 2.003-6.675 0.728-15.96-3.762-17.72-1.396-0.606-3.762-0.606-4.855-2.184-0.85-1.456-0.182-3.216 0-3.762 2.185-5.886 24.758-12.743 42.174-2.67z m-25.667 136.23c-3.216-8.435-15.413-10.559-24.152-12.015a83.74 83.74 0 0 0-24.272 0 201.463 201.463 0 0 0-28.46 4.854c-22.937 5.583-37.44 8.981-37.44 13.29 0 4.126 20.631 7.524 62.016 14.138 27.732 4.49 39.929 5.704 47.514-1.941 4.49-4.855 7.16-12.44 4.854-18.326z m-20.207 8.252c-2.185 4.673-12.197 4.309-31.919 2.67-27.974-2.488-42.113-3.762-42.113-5.886 0-1.577 8.92-2.912 26.882-5.764 0.364 0 7.707-1.214 17.598-1.942 5.34-0.364 9.466-0.607 14.32-0.182 11.287 0.91 13.35 3.762 13.957 4.672 1.214 1.578 2.306 4.248 1.214 6.432z\" fill=\"#1296db\" p-id=\"4249\"></path></svg>", "label": "chatglm4", "doc": "https://github.com/THUDM/GLM-4", "session_num": 5, "chat_type": "text", "hello": "使用openai协议接口部署chatglm4", "tips": "", "knowledge": "{}", "prompt": "{{query}}", "service_type": "openai", "service_config": {"llm_url": "http://chatglm4-9b-202406011.service:8000/v1/", "llm_tokens": [], "llm_data": {"model": "chatglm4-9b"}, "stream": "true"}, "owner": "admin,*", "expand": {"index": 4, "isPublic": true}}, {"name": "python", "icon": "<svg t=\"1687089367722\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2012\" width=\"50\" height=\"50\"><path d=\"M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z\" fill=\"#3C78AA\" p-id=\"2013\"></path><path d=\"M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z\" fill=\"#FDD835\" p-id=\"2014\"></path></svg>", "label": "python", "doc": "https://github.com/tencentmusic/cube-studio", "session_num": 0, "chat_type": "text", "hello": "给我一段你想要生成的代码的功能描述，我会给出python代码，并会给出思考过程和闭坑指南", "tips": "获取今天凌晨的时间戳", "knowledge": {}, "prompt": "按要求生成python代码：\n\n要求：\n1、先使用markdown形式给出完整连续可复制的代码块，使用最短代码的实现方式\n2、再给出清晰的思考过程\n3、最后给出可能遇到的问题\n\n代码描述：\n{{query}}\n\n", "service_type": "openai", "service_config": {"llm_url": "", "llm_tokens": [], "stream": "true"}, "owner": "admin,*", "expand": {"index": 5, "isPublic": true}}, {"name": "cube-studio", "icon": "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" width=\"50px\" height=\"50px\" viewBox=\"0 0 64 64\" enable-background=\"new 0 0 64 64\" xml:space=\"preserve\">  <image id=\"image0\" width=\"64\" height=\"64\" x=\"0\" y=\"0\"    href=\"data:image/png;base64,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\" /></svg>", "label": "cube-studio", "doc": "https://github.com/tencentmusic/cube-studio", "session_num": 0, "chat_type": "text", "hello": "这里是cube-studio开源社区，请问有什么可以帮你的么？<a target=\"_blank\" href=\"https://github.com/tencentmusic/cube-studio/wiki\">wiki</a>", "tips": "cube studio支持什么版本的k8s系统？\n如何部署cube studio平台？", "knowledge": {"type": "file", "file": "/mnt/admin/pipeline/example/gpt/cube-studio.csv", "upload_url": "http://chat-embedding.aihub:80/aihub/chat-embedding/api/upload_files", "recall_url": "http://chat-embedding.aihub:80/aihub/chat-embedding/api/recall"}, "prompt": "cube-studio是开源一站式机器学习平台，下面[KNOWLEDGE]中包含的内容是你掌握的知识：\n[KNOWLEDGE]\n```\n{{knowledge}}\n```\n[END KNOWLEDGE]\n\n上述是你掌握的信息，根据上述信息，简洁和专业的来回答用户的问题。如果无法从上述信息中得到答案，请说’不知道‘，不允许回复内容以外的信息。\n\n 请问：{{query}}\n", "service_type": "openai", "service_config": {"llm_url": "", "llm_tokens": [], "stream": "true", "temperature": 0, "top_p": 1, "presence_penalty": 2}, "owner": "admin,*", "expand": {"index": 6, "isPublic": true}}, {"name": "aigc", "icon": "<svg t=\"1687241366563\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"1883\" width=\"50\" height=\"50\"><path d=\"M102.47168 0h819.2a102.4 102.4 0 0 1 102.4 102.4v819.2a102.4 102.4 0 0 1-102.4 102.4h-819.2a102.4 102.4 0 0 1-102.4-102.4V102.4a102.4 102.4 0 0 1 102.4-102.4z\" fill=\"#FF875D\" p-id=\"1884\"></path><path d=\"M512 522.24m-409.6 0a409.6 409.6 0 1 0 819.2 0 409.6 409.6 0 1 0-819.2 0Z\" fill=\"#B3B8BD\" p-id=\"1885\"></path><path d=\"M512 491.52m-409.6 0a409.6 409.6 0 1 0 819.2 0 409.6 409.6 0 1 0-819.2 0Z\" fill=\"#FFFFFF\" p-id=\"1886\"></path><path d=\"M261.12 455.68m-97.28 0a97.28 97.28 0 1 0 194.56 0 97.28 97.28 0 1 0-194.56 0Z\" fill=\"#FFD95C\" p-id=\"1887\"></path><path d=\"M404.48 271.36m-97.28 0a97.28 97.28 0 1 0 194.56 0 97.28 97.28 0 1 0-194.56 0Z\" fill=\"#F36C52\" p-id=\"1888\"></path><path d=\"M650.24 271.36m-97.28 0a97.28 97.28 0 1 0 194.56 0 97.28 97.28 0 1 0-194.56 0Z\" fill=\"#7BE3FD\" p-id=\"1889\"></path><path d=\"M711.68 650.24m-97.28 0a97.28 97.28 0 1 0 194.56 0 97.28 97.28 0 1 0-194.56 0Z\" fill=\"#B3B8BD\" p-id=\"1890\"></path><path d=\"M711.68 650.24m-97.28 0a97.28 97.28 0 1 0 194.56 0 97.28 97.28 0 1 0-194.56 0Z\" fill=\"#B3B8BD\" p-id=\"1891\"></path><path d=\"M716.87168 573.44c53.72928 0 92.16 23.07072 92.16 76.8a97.28 97.28 0 0 1-194.56 0c0-53.72928 48.68096-76.8 102.4-76.8z\" fill=\"#FF875D\" p-id=\"1892\"></path><path d=\"M406.92736 601.088c-20.6336 26.74688-63.488 26.7264-86.51776-16.30208-15.9744-29.91104-3.61472-118.29248-3.61472-118.29248a104.25344 104.25344 0 0 0 59.05408 61.85984c41.7792 17.59232 59.0336 36.42368 31.0784 72.73472z\" fill=\"#FBC826\" p-id=\"1893\"></path><path d=\"M472.04352 643.30752l-70.12352 46.75584-42.87488-72.704 59.33056-39.55712z\" fill=\"#DCDDDC\" p-id=\"1894\"></path><path d=\"M407.30624 676.05504l54.5792-46.46912L716.87168 993.28z\" fill=\"#202F4F\" p-id=\"1895\"></path></svg>", "label": "文生图", "doc": "https://prompthero.com/openjourney-prompts", "session_num": 0, "chat_type": "text", "hello": "图片描述，仅支持英文，<a target=\"_blank\" href=\"https://prompthero.com/openjourney-prompts\">提示词参考</a>", "tips": "portrait photograph of <PERSON> as <PERSON><PERSON><PERSON><PERSON>, young beautiful native american woman, perfect symmetrical face, feather jewelry, traditional handmade dress, armed female hunter warrior, (((wild west))) environment, Utah landscape, ultra realistic, concept art, elegant, ((intricate)), ((highly detailed)), depth of field, ((professionally color graded)), 8k, art by art<PERSON><PERSON> and greg rutkowski and alphonse mucha\njapanese style shrine on top of a misty mountain overgrown, hyper realistic, lush gnarly plants, 8 k, denoised, by greg rutkowski, tom bagshaw, james gurney cinematic lighting ", "knowledge": {}, "prompt": "{{query}}", "service_type": "<PERSON><PERSON><PERSON>", "service_config": {"url": "/aihub/stable-diffusion-v3/api/model/stable-diffusion-v3/version/v20240612/", "data": {"prompt": "$text"}, "output": "image", "req_num": 4, "stream": "true"}, "owner": "admin,*", "expand": {"index": 7, "isPublic": true}}, {"name": "tts", "icon": "<svg t=\"1719062268060\" class=\"icon\" viewBox=\"0 0 1027 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2311\" width=\"50\" height=\"50\"><path d=\"M218.906557 482.622009c11.440287 0 20.7215 10.837429 20.7215 24.24051v67.786505c0 13.361021-9.281213 24.24051-20.74954 24.24051s-20.74954-10.837429-20.74954-24.24051v-67.786505c0-13.361021 9.281213-24.24051 20.7916-24.24051z m83.01218-24.871408c11.468327 0 20.7916 9.813972 20.791601 21.829077v122.352187c0 12.057165-9.323273 21.857117-20.791601 21.857117s-20.7916-9.813972-20.7916-21.857117v-122.366207c0-12.057165 9.323273-21.857117 20.7916-21.857117z m83.040221-49.897037c11.468327 0 20.7916 11.019688 20.7916 24.619049v216.496216c0 13.585341-9.323273 24.605029-20.7916 24.605029s-20.7916-11.019688-20.7916-24.605029v-216.468176c0-13.585341 9.323273-24.619049 20.7916-24.619049z m257.406458 0c11.468327 0 20.7916 11.019688 20.7916 24.619049v216.496216c0 13.585341-9.323273 24.605029-20.7916 24.605029s-20.7916-11.019688-20.7916-24.605029v-216.468176c0-13.585341 9.323273-24.619049 20.7916-24.619049z m83.04022 49.812917c11.468327 0 20.7916 9.813972 20.7916 21.871137v122.352187c0 12.071185-9.323273 21.871137-20.7916 21.871137s-20.7916-9.813972-20.7916-21.871137v-122.324147c0-12.057165 9.323273-21.857117 20.7916-21.857117z m83.110321 24.955528c11.468327 0 20.74954 10.837429 20.74954 24.24051v67.786505c0 13.361021-9.281213 24.24051-20.7916 24.24051s-20.7215-10.837429-20.7215-24.24051v-67.786505c0-13.361021 9.281213-24.24051 20.74954-24.24051z m-253.28459 315.44909h-83.012181V308.255772H297.824909l-16.599632-83.040221H746.225276l-16.599632 83.040221H555.231367v489.815327z m0 0\" fill=\"#777777\" p-id=\"2312\"></path><path d=\"M983.849571 313.387077A511.938824 511.938824 0 1 0 1023.960676 512.063924a508.602074 508.602074 0 0 0-40.111105-198.676847zM513.634147 981.970909c-259.116893 0-469.977085-210.818132-469.977085-469.906985S254.517254 42.100859 513.634147 42.100859s469.935025 210.818132 469.935025 469.963065S772.69496 981.970909 513.634147 981.970909z\" fill=\"#777777\" p-id=\"2313\"></path></svg>", "label": "语音合成", "doc": "https://prompthero.com/openjourney-prompts", "session_num": 0, "chat_type": "text", "hello": "输入文字，自动合成为语音文件", "tips": "北京今天天气怎么样。", "knowledge": {}, "prompt": "{{query}}", "service_type": "<PERSON><PERSON><PERSON>", "service_config": {"url": "/aihub/speech-sambert-hifigan-tts-zhitian-emo-zh-cn-16k/api/model/speech-sambert-hifigan-tts-zhitian-emo-zh-cn-16k/version/v20221001/", "data": {"input": "$text"}, "output": "audio", "req_num": 1, "stream": "true"}, "owner": "admin,*", "expand": {"index": 8, "isPublic": true}}, {"name": "chat<PERSON>", "icon": "<svg t=\"1709012691993\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"5140\" width=\"50\" height=\"50\"><path d=\"M896 64a76.8 76.8 0 0 1 76.8 76.8v588.8a76.8 76.8 0 0 1-76.8 76.8H128a76.8 76.8 0 0 1-76.8-76.8V140.8a76.8 76.8 0 0 1 76.8-76.8h768z m-89.2928 208.4608a38.4 38.4 0 0 0-52.5568 0.6912l-1.6896 1.7408L581.76 461.568 480.5632 299.648l-1.3568-2.0224a38.4 38.4 0 0 0-57.856-5.2864l-1.6896 1.7536-204.8 224-1.5872 1.8432a38.4 38.4 0 0 0 2.2016 50.6368l1.8176 1.7664 1.8432 1.5872a38.4 38.4 0 0 0 50.6368-2.2016l1.7664-1.8176L442.24 383.2064l101.2096 161.9456 1.3568 2.0224a38.4 38.4 0 0 0 57.856 5.2864l1.6896-1.7536 204.8-224 1.5872-1.8432a38.4 38.4 0 0 0-4.0192-52.4032z\" fill=\"#6B57FE\" p-id=\"5141\"></path><path d=\"M819.2 883.2a38.4 38.4 0 0 1 2.2528 76.736L819.2 960H204.8a38.4 38.4 0 0 1-2.2528-76.736L204.8 883.2h614.4z\" fill=\"#FFBA00\" p-id=\"5142\"></path></svg>", "label": "chat<PERSON>", "doc": "", "session_num": 0, "chat_type": "chat<PERSON>", "hello": "通过对话查询数据看板，需要二开配置数据接口", "tips": "", "knowledge": {"file": "xx/xx/xx/xx.json"}, "prompt": "{{query}}", "service_type": "openai", "service_config": {"stream": "true"}, "owner": "admin,*", "expand": {"index": 9, "isPublic": true}}, {"name": "wechat", "icon": "<svg t=\"1687331487468\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2448\" width=\"50\" height=\"50\"><path d=\"M337.387283 341.82659c-17.757225 0-35.514451 11.83815-35.514451 29.595375s17.757225 29.595376 35.514451 29.595376 29.595376-11.83815 29.595376-29.595376c0-18.49711-11.83815-29.595376-29.595376-29.595375zM577.849711 513.479769c-11.83815 0-22.936416 12.578035-22.936416 23.6763 0 12.578035 11.83815 23.676301 22.936416 23.676301 17.757225 0 29.595376-11.83815 29.595376-23.676301s-11.83815-23.676301-29.595376-23.6763zM501.641618 401.017341c17.757225 0 29.595376-12.578035 29.595376-29.595376 0-17.757225-11.83815-29.595376-29.595376-29.595375s-35.514451 11.83815-35.51445 29.595375 17.757225 29.595376 35.51445 29.595376zM706.589595 513.479769c-11.83815 0-22.936416 12.578035-22.936416 23.6763 0 12.578035 11.83815 23.676301 22.936416 23.676301 17.757225 0 29.595376-11.83815 29.595376-23.676301s-11.83815-23.676301-29.595376-23.6763z\" fill=\"#28C445\" p-id=\"2449\"></path><path d=\"M510.520231 2.959538C228.624277 2.959538 0 231.583815 0 513.479769s228.624277 510.520231 510.520231 510.520231 510.520231-228.624277 510.520231-510.520231-228.624277-510.520231-510.520231-510.520231zM413.595376 644.439306c-29.595376 0-53.271676-5.919075-81.387284-12.578034l-81.387283 41.433526 22.936416-71.768786c-58.450867-41.433526-93.965318-95.445087-93.965317-159.815029 0-113.202312 105.803468-201.988439 233.803468-201.98844 114.682081 0 216.046243 71.028902 236.023121 166.473989-7.398844-0.739884-14.797688-1.479769-22.196532-1.479769-110.982659 1.479769-198.289017 85.086705-198.289017 188.67052 0 17.017341 2.959538 33.294798 7.398844 49.572255-7.398844 0.739884-15.537572 1.479769-22.936416 1.479768z m346.265896 82.867052l17.757225 59.190752-63.630058-35.514451c-22.936416 5.919075-46.612717 11.83815-70.289017 11.83815-111.722543 0-199.768786-76.947977-199.768786-172.393063-0.739884-94.705202 87.306358-171.653179 198.289017-171.65318 105.803468 0 199.028902 77.687861 199.028902 172.393064 0 53.271676-34.774566 100.624277-81.387283 136.138728z\" fill=\"#28C445\" p-id=\"2450\"></path></svg>", "label": "Wechat", "doc": "", "session_num": 20, "chat_type": "text", "hello": "认证公众号以后，添加接口配置APPID/APPSECRET/TOKEN， 公众号配置<基础配置>中设置服务器地址为http://当前公网域名/wechat/chatgpt/wechat", "tips": "", "knowledge": {}, "prompt": "{{history}}\nHuman:{{query}}\nAI:", "service_type": "openai", "service_config": {"llm_url": "", "llm_tokens": [], "AppID": "", "APPSECRET": "", "TOKEN": "", "stream": "false"}, "owner": "admin,*", "expand": {"index": 10, "isPublic": true}}, {"name": "wework", "icon": "<svg t=\"1691592826041\" class=\"icon\" viewBox=\"0 0 1220 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"4006\" width=\"50\" height=\"50\"><path d=\"M796.081231 792.969846a16.462769 16.462769 0 0 0 2.048 25.127385 211.337846 211.337846 0 0 1 65.102769 126.897231 68.804923 68.804923 0 1 0 73.531077-86.528 211.101538 211.101538 0 0 1-117.405539-65.496616 16.462769 16.462769 0 0 0-23.276307 0z\" fill=\"#FB6500\" p-id=\"4007\"></path><path d=\"M1075.357538 684.032c-11.145846 11.106462-18.116923 25.718154-19.771076 41.353846a211.101538 211.101538 0 0 1-65.299693 117.641846 16.462769 16.462769 0 1 0 25.127385 20.992 211.101538 211.101538 0 0 1 126.897231-65.063384 68.804923 68.804923 0 1 0-66.717539-114.924308h-0.236308z\" fill=\"#0082EF\" p-id=\"4008\"></path><path d=\"M881.545846 489.984a68.804923 68.804923 0 0 0 41.393231 116.972308 211.101538 211.101538 0 0 1 117.602461 65.299692 16.462769 16.462769 0 1 0 21.031385-25.127385A211.337846 211.337846 0 0 1 996.430769 520.270769a68.804923 68.804923 0 0 0-114.924307-30.286769z\" fill=\"#2DBC00\" p-id=\"4009\"></path><path d=\"M847.753846 597.504l-1.220923 1.220923a210.904615 210.904615 0 0 1-128.748308 67.347692 68.371692 68.371692 0 0 0-30.483692 114.963693 68.804923 68.804923 0 0 0 116.972308-41.393231 211.337846 211.337846 0 0 1 65.536-117.641846 16.462769 16.462769 0 0 0-22.055385-24.497231z\" fill=\"#FFCC00\" p-id=\"4010\"></path><path d=\"M432.088615 3.662769C308.775385 17.250462 196.923077 69.986462 116.578462 152.418462A416.689231 416.689231 0 0 0 39.502769 260.726154a377.344 377.344 0 0 0 26.584616 381.046154c21.819077 32.964923 57.659077 74.161231 90.427077 103.424l-14.848 116.578461-1.654154 4.923077c-0.393846 1.457231-0.393846 3.111385-0.59077 4.529231l-0.393846 3.741538 0.393846 3.702154a37.494154 37.494154 0 0 0 56.438154 29.026462h0.59077l2.284307-1.654154 35.446154-17.723077 105.629539-53.129846c50.215385 14.454154 102.242462 21.582769 154.505846 21.228308 64.590769 0.157538 128.708923-11.027692 189.479384-32.964924a68.608 68.608 0 0 1-46.749538-71.876923c-62.857846 20.204308-129.260308 26.781538-194.835692 19.377231l-10.515693-1.457231a471.04 471.04 0 0 1-70.025846-14.611692 47.773538 47.773538 0 0 0-37.494154 3.899077l-2.875077 1.457231L234.338462 811.323077l-3.702154 2.244923c-2.087385 1.220923-3.111385 1.654154-4.135385 1.654154a5.986462 5.986462 0 0 1-5.553231-6.183385l3.268923-13.390769 3.938462-14.611692 6.183385-24.103385 7.207384-26.781538a36.470154 36.470154 0 0 0-13.193846-40.566154 380.061538 380.061538 0 0 1-87.748923-91.451077 297.235692 297.235692 0 0 1-21.425231-300.347077 343.355077 343.355077 0 0 1 61.833846-86.488615c65.890462-67.977846 158.562462-111.222154 261.12-122.368 35.446154-3.899077 71.286154-3.899077 106.732308 0 101.927385 11.736615 194.205538 55.611077 259.741538 123.195076a337.801846 337.801846 0 0 1 61.164308 86.921847 295.187692 295.187692 0 0 1 30.72 130.756923c0 4.765538-0.433231 9.491692-0.669538 14.020923a68.608 68.608 0 0 1 84.48 9.885538l3.072 3.741539a375.296 375.296 0 0 0-37.494154-195.268923 420.627692 420.627692 0 0 0-76.209231-108.347077 517.001846 517.001846 0 0 0-314.289231-149.740308 579.426462 579.426462 0 0 0-127.330461-0.393846z\" fill=\"#0082EF\" p-id=\"4011\"></path></svg>", "label": "企业微信", "doc": "", "session_num": 0, "chat_type": "text", "hello": "企业微信机器人，添加接口配置token和EncodingAESKey，并配置url为当前http://当前公网域名/wework/chatgpt/$chat_name", "tips": "", "knowledge": {}, "prompt": "{{query}}", "service_type": "openai", "service_config": {"llm_url": "", "llm_tokens": [], "Token": "", "EncodingAESKey": "", "stream": "false"}, "owner": "admin,*", "expand": {"index": 11, "isPublic": true}}, {"name": "<PERSON><PERSON><PERSON>", "icon": "<svg t=\"1693821247345\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3978\" width=\"50\" height=\"50\"><path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m227 385.3c-1 4.2-3.5 10.4-7 17.8h0.1l-0.4 0.7c-20.3 43.1-73.1 127.7-73.1 127.7s-0.1-0.2-0.3-0.5l-15.5 26.8h74.5L575.1 810l32.3-128h-58.6l20.4-84.7c-16.5 3.9-35.9 9.4-59 16.8 0 0-31.2 18.2-89.9-35 0 0-39.6-34.7-16.6-43.4 9.8-3.7 47.4-8.4 77-12.3 40-5.4 64.6-8.2 64.6-8.2S422 517 392.7 512.5c-29.3-4.6-66.4-53.1-74.3-95.8 0 0-12.2-23.4 26.3-12.3 38.5 11.1 197.9 43.2 197.9 43.2s-207.4-63.3-221.2-78.7c-13.8-15.4-40.6-84.2-37.1-126.5 0 0 1.5-10.5 12.4-7.7 0 0 153.3 69.7 258.1 107.9 104.8 37.9 195.9 57.3 184.2 106.7z\" fill=\"#1296DB\" p-id=\"3979\"></path></svg>", "label": "钉钉", "doc": "", "session_num": 0, "chat_type": "text", "hello": "钉钉企业内部应用机器人，添加接口配置AppKey，AppSecret，并配置url为当前http://当前公网域名/dingtalk/chatgpt/$chat_name", "tips": "", "knowledge": {}, "prompt": "{{query}}", "service_type": "openai", "service_config": {"llm_url": "", "llm_tokens": [], "AgentId": "", "AppKey": "xx", "AppSecret": "xx", "stream": "false"}, "owner": "admin,*", "expand": {"index": 12, "isPublic": true}}]