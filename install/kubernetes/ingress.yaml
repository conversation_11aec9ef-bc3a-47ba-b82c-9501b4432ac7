# https://kubernetes.github.io/ingress-nginx/user-guide/nginx-configuration/annotations/
# 所有的注释都在上面的网址上

apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: infra-kubeflow-dashboard
  namespace: infra
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3000"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3000"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3000"
    nginx.ingress.kubernetes.io/proxy-body-size: 1g
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*, X-CustomResponseHeader"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"

spec:
  rules:
    - http:
        paths:
          - path: /
            backend:
              serviceName: kubeflow-dashboard
              servicePort: 80
#      host: kubeflow.local.com

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: kubeflow-kubeflow-dashboard
  namespace: kubeflow
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3000"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3000"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3000"
    nginx.ingress.kubernetes.io/proxy-body-size: 1g

    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*, X-CustomResponseHeader"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"

    # nginx的location部分
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Access-Control-Allow-Origin: *";
      more_set_headers "Access-Control-Allow-Methods: *";
      more_set_headers "Access-Control-Allow-Headers: *";
      sub_filter '<head>' '<head><script src="https://docker-76009.sz.gfp.tencent-cloud.com/kubeflow/myapp_into.js"></script>';
      sub_filter_once off;

spec:
  rules:
    - http:
        paths:
          - path: /minio/
            backend:
              serviceName: minio-service
              servicePort: 9000
          - path: /pipeline/
            backend:
              serviceName: ml-pipeline-ui
              servicePort: 80
#      host: kubeflow.local.com

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: kube-system-kubeflow-dashboard
  namespace: kube-system
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-body-size: 50m

    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*, X-CustomResponseHeader"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    # nginx server
#    nginx.ingress.kubernetes.io/server-snippet: |

    # nginx location
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Access-Control-Allow-Origin: *";
      more_set_headers "Access-Control-Allow-Methods: *";
      more_set_headers "Access-Control-Allow-Headers: *";
      sub_filter '<head>' '<head><script src="https://docker-76009.sz.gfp.tencent-cloud.com/kubeflow/myapp_into.js"></script>';
      sub_filter_once off;

spec:
  rules:
    - http:
        paths:
          - path: /
            backend:
              serviceName: kubernetes-dashboard-cluster
              servicePort: 9090
#      host: k8s.local.com      # 这里需要修改为自己的k8s dashboard的域名



---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: monitoring-grafana
  namespace: monitoring
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-body-size: 50m

    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "true"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*, X-CustomResponseHeader"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    # nginx server
#    nginx.ingress.kubernetes.io/server-snippet: |


    # nginx location
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Access-Control-Allow-Origin: *";
      more_set_headers "Access-Control-Allow-Methods: *";
      more_set_headers "Access-Control-Allow-Headers: *";
      sub_filter '<head>' '<head><script src="https://docker-76009.sz.gfp.tencent-cloud.com/kubeflow/myapp_into.js"></script>';
      sub_filter_once off;

spec:
  rules:
    - http:
        paths:
          - path: /
            backend:
              serviceName: grafana
              servicePort: 3000
#      host: kubeflow.local.com       # 这里需要修改为自己的k8s dashboard的域名



