{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "\n", "if __name__ == \"__main__\":\n", "    spark = SparkSession.builder \\\n", "    .appName('spark-hive-demo') \\\n", "    .config(\"hive.metastore.uris\", \"thrift://xxx.xxx.xxx.xxx:9083\") \\\n", "    .enableHiveSupport() \\\n", "    .getOrCreate()\n", "\n", "    spark.sql(\"create table if not exists demo(id bigint,name String)\")\n", "\n", "    spark.sql(\"insert overwrite demo values (1,'hamawhite'),(2,'song.bs')\")\n", "    spark.sql(\"select * from demo\").show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3.0}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 0}