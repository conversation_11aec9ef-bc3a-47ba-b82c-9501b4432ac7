#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{- if .Values.proxy.enabled }}
{{- $hostNetwork := .Values.proxy.hostNetwork }}
{{- $hostPID := .Values.proxy.hostPID }}
{{- $fullName := include "alluxio.fullname" . }}

apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ template "alluxio.fullname" . }}-proxy
  labels:
    app: {{ template "alluxio.name" . }}
    chart: {{ template "alluxio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-proxy
spec:
  selector:
    matchLabels:
      app: {{ template "alluxio.name" . }}
      chart: {{ template "alluxio.chart" . }}
      release: {{ .Release.Name }}
      heritage: {{ .Release.Service }}
      role: alluxio-proxy
  template:
    metadata:
      labels:
        app: {{ template "alluxio.name" . }}
        chart: {{ template "alluxio.chart" . }}
        release: {{ .Release.Name }}
        heritage: {{ .Release.Service }}
        role: alluxio-proxy
    spec:
      hostNetwork: {{ $hostNetwork }}
      dnsPolicy: {{ .Values.proxy.dnsPolicy | default ($hostNetwork | ternary "ClusterFirstWithHostNet" "ClusterFirst") }}
      nodeSelector:
      {{- if .Values.proxy.nodeSelector }}
{{ toYaml .Values.proxy.nodeSelector | trim | indent 8  }}
      {{- else if .Values.nodeSelector }}
{{ toYaml .Values.nodeSelector | trim | indent 8  }}
      {{- end }}
      tolerations:
      {{- if .Values.proxy.tolerations }}
{{ toYaml .Values.proxy.tolerations | trim | indent 8  }}
      {{- end }}
      {{- if .Values.tolerations }}
{{ toYaml .Values.tolerations | trim | indent 8  }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.user }}
        runAsGroup: {{ .Values.group }}
        fsGroup: {{ .Values.fsGroup }}
    {{- if .Values.proxy.serviceAccount }}
      serviceAccountName: {{ .Values.proxy.serviceAccount }}
    {{- else if .Values.serviceAccount }}
      serviceAccountName: {{ .Values.serviceAccount }}
    {{- end }}
      {{- if .Values.imagePullSecrets }}
{{ include "alluxio.imagePullSecrets" . | indent 6 }}
      {{- end}}
      containers:
        - name: alluxio-proxy
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          {{- if .Values.proxy.resources  }}
{{ include "alluxio.proxy.resources" . | indent 10 }}
          {{- end }}
          command: ["/entrypoint.sh"]
          {{- if .Values.proxy.args }}
          args:
{{ toYaml .Values.proxy.args | trim | indent 12 }}
          {{- end }}
          env:
          {{- range $key, $value := .Values.proxy.env }}
          - name: "{{ $key }}"
            value: "{{ $value }}"
          {{- end }}
          - name: ALLUXIO_CLIENT_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          envFrom:
          - configMapRef:
              name: {{ $fullName }}-config
          ports:
            - containerPort: {{ .Values.proxy.ports.web }}
              name: web
{{- end }} # proxy enabled
