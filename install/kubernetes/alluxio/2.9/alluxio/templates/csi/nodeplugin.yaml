#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ if .Values.csi.enabled -}}
{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}

kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: {{ $fullName }}-csi-nodeplugin
  labels:
    name: {{ $fullName }}-csi-nodeplugin
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-csi-nodeplugin
spec:
  selector:
    matchLabels:
      app: {{ $name }}
      role: alluxio-csi-nodeplugin
      name: {{ $fullName }}-csi-nodeplugin
  template:
    metadata:
      labels:
        name: {{ $fullName }}-csi-nodeplugin
        app: {{ $name }}
        chart: {{ $chart }}
        release: {{ .Release.Name }}
        heritage: {{ .Release.Service }}
        role: alluxio-csi-nodeplugin
    spec:
      serviceAccount: csi-controller-sa
      hostNetwork: {{ .Values.csi.nodePlugin.hostNetwork }}
      dnsPolicy: {{ .Values.csi.nodePlugin.dnsPolicy }}
      {{- if .Values.imagePullSecrets }}
{{ include "alluxio.imagePullSecrets" . | indent 6 }}
      {{- end}}
      containers:
        - name: node-driver-registrar
          image: {{ .Values.csi.nodePlugin.driverRegistrar.image }}
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "rm -rf /registration/alluxio /registration/alluxio-reg.sock"]
          args:
            - --v=5
            - --csi-address=/plugin/csi.sock
            - --kubelet-registration-path=/var/lib/kubelet/plugins/csi-alluxio-plugin/csi.sock
          {{- if .Values.csi.nodePlugin.driverRegistrar.resources }}
          resources:
            {{- if .Values.csi.nodePlugin.driverRegistrar.resources.limits }}
            limits:
              cpu: {{ .Values.csi.nodePlugin.driverRegistrar.resources.limits.cpu }}
              memory: {{ .Values.csi.nodePlugin.driverRegistrar.resources.limits.memory }}
            {{- end }}
            {{- if .Values.csi.nodePlugin.driverRegistrar.resources.requests }}
            requests:
              cpu: {{ .Values.csi.nodePlugin.driverRegistrar.resources.requests.cpu }}
              memory: {{ .Values.csi.nodePlugin.driverRegistrar.resources.requests.memory }}
            {{- end }}
          {{- end }}
          env:
            - name: KUBE_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          volumeMounts:
            - name: plugin-dir
              mountPath: /plugin
            - name: registration-dir
              mountPath: /registration
        - name: csi-nodeserver
          # run as root user, mount command need root privilege
          securityContext:
            privileged: true
            runAsUser: 0
            runAsGroup: 0
            capabilities:
              add: ["SYS_ADMIN"]
          {{- if .Values.csi.nodePlugin.nodeserver.resources  }}
          resources:
            {{- if .Values.csi.nodePlugin.nodeserver.resources.limits }}
            limits:
              cpu: {{ .Values.csi.nodePlugin.nodeserver.resources.limits.cpu }}
              memory: {{ .Values.csi.nodePlugin.nodeserver.resources.limits.memory }}
            {{- end }}
            {{- if .Values.csi.nodePlugin.nodeserver.resources.requests }}
            requests:
              cpu: {{ .Values.csi.nodePlugin.nodeserver.resources.requests.cpu }}
              memory: {{ .Values.csi.nodePlugin.nodeserver.resources.requests.memory }}
            {{- end }}
          {{- end }}
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.csi.imagePullPolicy }}
          command: ["tini", "--", "/entrypoint.sh"]
          args :
            - csiserver
            - --v=5
            - "--nodeid=$(NODE_ID)"
            - "--endpoint=$(CSI_ENDPOINT)"
          env:
            - name: ALLUXIO_CLIENT_HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: NODE_ID
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: CSI_ENDPOINT
              value: unix://plugin/csi.sock
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: {{ template "alluxio.fullname" . }}-config
          volumeMounts:
            - name: plugin-dir
              mountPath: /plugin
            - name: pods-mount-dir
              mountPath: /var/lib/kubelet
              mountPropagation: "Bidirectional"
            - name: csi-fuse-config
              mountPath: /opt/alluxio/integration/kubernetes/csi
      volumes:
        - name: plugin-dir
          hostPath:
            path: /var/lib/kubelet/plugins/csi-alluxio-plugin
            type: DirectoryOrCreate
        - name: pods-mount-dir
          hostPath:
            path: /var/lib/kubelet
            type: Directory
        - hostPath:
            path: /var/lib/kubelet/plugins_registry
            type: Directory
          name: registration-dir
        - name: csi-fuse-config
          configMap:
            name: {{ $fullName }}-csi-fuse-config
            items:
              - key: alluxio-csi-fuse-config
                path: alluxio-csi-fuse.yaml
{{- end }}
