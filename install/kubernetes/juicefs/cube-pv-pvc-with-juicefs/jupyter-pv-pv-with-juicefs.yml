
# 模型训练
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: jupyter-kubeflow-user-workspace
  labels:
    jupyter-pvname: jupyter-kubeflow-user-workspace
spec:
  capacity:
    storage: 500Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
  csi:
    driver: csi.juicefs.com
    volumeHandle: jupyter-kubeflow-user-workspace
    fsType: juicefs
    nodePublishSecretRef:
      name: juicefs-sc-secret
      namespace: kube-system
    volumeAttributes:
      juicefs/mount-cpu-limit: 5000m
      juicefs/mount-memory-limit: 5Gi
      juicefs/mount-cpu-request: 1m
      juicefs/mount-memory-request: 1Mi
  mountOptions:
    - subdir=kubeflow/pipeline/workspace
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kubeflow-user-workspace
  namespace: jupyter
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      jupyter-pvname: jupyter-kubeflow-user-workspace
  storageClassName: ""
  volumeName: jupyter-kubeflow-user-workspace

# 模型归档
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: jupyter-kubeflow-archives
  labels:
    jupyter-pvname: jupyter-kubeflow-archives
spec:
  capacity:
    storage: 500Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
  csi:
    driver: csi.juicefs.com
    volumeHandle: jupyter-kubeflow-archives
    fsType: juicefs
    nodePublishSecretRef:
      name: juicefs-sc-secret
      namespace: kube-system
    volumeAttributes:
      juicefs/mount-cpu-limit: 5000m
      juicefs/mount-memory-limit: 5Gi
      juicefs/mount-cpu-request: 1m
      juicefs/mount-memory-request: 1Mi
  mountOptions:
    - subdir=kubeflow/pipeline/archives
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kubeflow-archives
  namespace: jupyter
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      jupyter-pvname: jupyter-kubeflow-archives
  storageClassName: ""
  volumeName: jupyter-kubeflow-archives
