"""empty message
Revision ID: c347941bd412
Revises: 354ff240b2e7
Create Date: 2024-02-27 20:48:27.369990
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
# revision identifiers, used by Alembic.
revision = 'c347941bd412'
down_revision = '354ff240b2e7'
branch_labels = None
depends_on = None
def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chat',
    sa.Column('id', sa.Integer(), nullable=False, comment='id主键'),
    sa.Column('name', sa.String(length=200), nullable=True, comment='英文名'),
    sa.Column('icon', sa.Text(), nullable=True, comment='图标svg内容'),
    sa.Column('label', sa.String(length=200), nullable=True, comment='中文名'),
    sa.Column('doc', sa.String(length=200), nullable=True, comment='文档链接'),
    sa.Column('session_num', sa.String(length=200), nullable=True, comment='会话保持的个数，默认为1，只记录当前的会话'),
    sa.Column('chat_type', sa.String(length=200), nullable=True, comment='聊天会话的界面类型 # text文本对话，speech 语音对话，text_speech文本加语音对话，multi_modal多模态对话，digital_human数字人'),
    sa.Column('hello', sa.String(length=200), nullable=True, comment='欢迎语'),
    sa.Column('tips', sa.String(length=4000), nullable=True, comment='提示词数组'),
    sa.Column('knowledge', sa.Text(), nullable=True, comment='加载问题前面的先验知识'),
    sa.Column('prompt', sa.Text(), nullable=True, comment='提示词模板'),
    sa.Column('service_type', sa.String(length=200), nullable=True, comment=' 推理服务的类型   [chatgpt,gptglm]'),
    sa.Column('service_config', sa.Text(), nullable=True, comment='推理服务的配置，url  header  json等'),
    sa.Column('owner', sa.String(length=2000), nullable=True, comment='可访问用户，* 表示所有用户'),
    sa.Column('expand', sa.Text(), nullable=True, comment='扩展参数'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('chat_log',
    sa.Column('id', sa.Integer(), nullable=False, comment='id主键'),
    sa.Column('username', sa.String(length=400), nullable=False, comment='用户名'),
    sa.Column('chat_id', sa.Integer(), nullable=True, comment='场景id'),
    sa.Column('query', sa.String(length=5000), nullable=False, comment='问题'),
    sa.Column('answer', sa.String(length=5000), nullable=False, comment='回答'),
    sa.Column('manual_feedback', sa.String(length=400), nullable=False, comment='反馈'),
    sa.Column('answer_status', sa.String(length=400), nullable=False, comment='回答状态'),
    sa.Column('answer_cost', sa.String(length=400), nullable=False, comment='话费时长'),
    sa.Column('err_msg', sa.Text(), nullable=True, comment='报错消息'),
    sa.Column('created_on', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('changed_on', sa.DateTime(), nullable=True, comment='修改时间'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('service_pipeline', schema=None) as batch_op:
        batch_op.drop_index('name')
    op.drop_table('service_pipeline')
    try:
        with op.batch_alter_table('ab_user', schema=None) as batch_op:
            batch_op.add_column(sa.Column('quota', sa.String(length=2000), nullable=True))
    except Exception as e:
        pass

    with op.batch_alter_table('project_user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('quota', sa.String(length=2000), nullable=True, comment='用户在该项目中可以使用的资源额度'))

    # ### end Alembic commands ###
def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('project_user', schema=None) as batch_op:
        batch_op.drop_column('quota')

    # with op.batch_alter_table('ab_user', schema=None) as batch_op:
    #     batch_op.drop_column('quota')
    op.create_table('service_pipeline',
    sa.Column('created_on', mysql.DATETIME(), nullable=True),
    sa.Column('changed_on', mysql.DATETIME(), nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('describe', mysql.VARCHAR(length=200), nullable=False),
    sa.Column('project_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('dag_json', mysql.TEXT(), nullable=False),
    sa.Column('namespace', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('env', mysql.VARCHAR(length=500), nullable=True),
    sa.Column('run_id', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('node_selector', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('images', mysql.VARCHAR(length=200), nullable=False),
    sa.Column('working_dir', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('command', mysql.VARCHAR(length=1000), nullable=True),
    sa.Column('volume_mount', mysql.VARCHAR(length=200), nullable=True),
    sa.Column('image_pull_policy', mysql.ENUM('Always', 'IfNotPresent'), nullable=False),
    sa.Column('replicas', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('resource_memory', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('resource_cpu', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('resource_gpu', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('parallelism', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('alert_status', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('alert_user', mysql.VARCHAR(length=300), nullable=True),
    sa.Column('expand', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('parameter', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('created_by_fk', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('changed_by_fk', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], name='service_pipeline_ibfk_1'),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], name='service_pipeline_ibfk_2'),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], name='service_pipeline_ibfk_3'),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8mb3',
    mysql_engine='InnoDB'
    )
    with op.batch_alter_table('service_pipeline', schema=None) as batch_op:
        batch_op.create_index('name', ['name'], unique=False)
    op.drop_table('chat_log')
    op.drop_table('chat')
    # ### end Alembic commands ###
