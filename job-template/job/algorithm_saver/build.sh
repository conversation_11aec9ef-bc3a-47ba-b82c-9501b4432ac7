#!/bin/bash

set -ex

# 构建算法保存器镜像
docker build --network=host -t ccr.ccs.tencentyun.com/cube-studio/algorithm_saver:latest -f job/algorithm_saver/Dockerfile job/algorithm_saver/
docker push ccr.ccs.tencentyun.com/cube-studio/algorithm_saver:latest

# 多架构构建（可选）
# docker buildx build --platform linux/amd64,linux/arm64 -t ccr.ccs.tencentyun.com/cube-studio/algorithm_saver:latest -f job/algorithm_saver/Dockerfile job/algorithm_saver/ --push

echo "算法保存器镜像构建完成!"
