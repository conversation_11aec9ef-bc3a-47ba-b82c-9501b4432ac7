"""empty message

Revision ID: 16f0ba32110a
Revises: eabe90912128
Create Date: 2021-09-07 07:06:20.251679

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '16f0ba32110a'
down_revision = 'eabe90912128'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('docker',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('describe', sa.String(length=200), nullable=True),
    sa.Column('base_image', sa.String(length=200), nullable=True),
    sa.Column('target_image', sa.String(length=200), nullable=True),
    sa.Column('consecutive_build', sa.<PERSON>(), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('pipeline', sa.Column('depends_on_past', sa.Boolean(), nullable=True))
    op.add_column('pipeline', sa.Column('max_active_runs', sa.Integer(), nullable=False))
    op.add_column('pipeline', sa.Column('parameter', sa.Text(length=65536), nullable=True))
    op.add_column('pytorchjob', sa.Column('change_time', sa.String(length=100), nullable=True))
    op.add_column('run', sa.Column('execution_date', sa.String(length=100), nullable=False))
    op.add_column('run', sa.Column('status', sa.String(length=100), nullable=True))
    op.add_column('tfjob', sa.Column('change_time', sa.String(length=100), nullable=True))
    op.add_column('workflow', sa.Column('change_time', sa.String(length=100), nullable=True))
    op.add_column('xgbjob', sa.Column('change_time', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('xgbjob', 'change_time')
    op.drop_column('workflow', 'change_time')
    op.drop_column('tfjob', 'change_time')
    op.drop_column('run', 'status')
    op.drop_column('run', 'execution_date')
    op.drop_column('pytorchjob', 'change_time')
    op.drop_column('pipeline', 'parameter')
    op.drop_column('pipeline', 'max_active_runs')
    op.drop_column('pipeline', 'depends_on_past')
    op.drop_table('docker')
    # ### end Alembic commands ###
