.DS_Store
.idea
__pycache__
install/docker/file
myapp/static/file
myapp/test
#/myapp/static/appbuilder/vison/*
/myapp/static/appbuilder/mnt
# /myapp/static/appbuilder/assets/
/images/web/target-detection/yolo/
/install/docker/data/mysql/
/myapp/visionPlus/node_modules
/myapp/frontend/node_modules
/myapp/frontendSrc/node_modules
/myapp/music/node_modules
/sdk/dist/
/install/docker/data/
/aihub/src/cubestudio/aihub/web/frontend/node_modules
/aihub/modelscope/aihub/
/aihub/modelscope/modelscope/
/aihub/deep-learning/chat-private-knowledge/wiki/
/aihub/deep-learning/chat-private-knowledge/cube-studio/
/myapp/wechat/send/image/
/myapp/wechat/receive/audio/
.ipynb_checkpoints
/job-template/job/*/.ipynb_checkpoints/
/install/docker/kubeconfig/
/aihub/__MACOSX/
