#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ $masterCount := int .Values.master.count }}
{{- $defaultMasterName := "master-0" }}
{{- $isSingleMaster := eq $masterCount 1 }}
{{- $isHaEmbedded := and (eq .Values.journal.type "EMBEDDED") (gt $masterCount 1) }}
{{- $release := .Release }}
{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}

{{- /* ===================================== */}}
{{- /*         ALLUXIO_JAVA_OPTS             */}}
{{- /* ===================================== */}}
{{- $alluxioJavaOpts := list }}
{{- /* Specify master hostname if single master */}}
{{- if $isSingleMaster }}
  {{- $alluxioJavaOpts = printf "-Dalluxio.master.hostname=%v-%v" $fullName $defaultMasterName | append $alluxioJavaOpts }}
{{- end }}
{{- $alluxioJavaOpts = printf "-Dalluxio.master.journal.type=%v" .Values.journal.type | append $alluxioJavaOpts }}
{{- $alluxioJavaOpts = printf "-Dalluxio.master.journal.folder=%v" .Values.journal.folder | append $alluxioJavaOpts }}

{{- /* Generate HA embedded journal address for masters */}}
{{- if $isHaEmbedded }}
  {{- $embeddedJournalAddresses := "-Dalluxio.master.embedded.journal.addresses=" }}
  {{- range $i := until $masterCount }}
  {{- $embeddedJournalAddresses = printf "%v,%v-master-%v:19200" $embeddedJournalAddresses $fullName $i }}
  {{- end }}
  {{- $alluxioJavaOpts = append $alluxioJavaOpts $embeddedJournalAddresses }}
{{- end }}
{{- range $key, $val := .Values.properties }}
  {{- $alluxioJavaOpts = printf "-D%v=%v" $key $val | append $alluxioJavaOpts }}
{{- end }}
{{- if .Values.jvmOptions }}
  {{- $alluxioJavaOpts = concat $alluxioJavaOpts .Values.jvmOptions }}
{{- end }}
{{- if .Values.metrics.enabled }}
  {{- $metricsConfFile := "-Dalluxio.metrics.conf.file=/config/metrics/metrics.properties" }}
  {{- $alluxioJavaOpts = append $alluxioJavaOpts $metricsConfFile }}
{{- end }}

{{- /* ===================================== */}}
{{- /*       ALLUXIO_MASTER_JAVA_OPTS        */}}
{{- /* ===================================== */}}
{{- $masterJavaOpts := list }}
{{- $masterJavaOpts = print "-Dalluxio.master.hostname=${ALLUXIO_MASTER_HOSTNAME}" | append $masterJavaOpts }}
{{- range $key, $val := .Values.master.properties }}
  {{- $masterJavaOpts = printf "-D%v=%v" $key $val | append $masterJavaOpts }}
{{- end }}
{{- if .Values.master.jvmOptions }}
  {{- $masterJavaOpts = concat $masterJavaOpts .Values.master.jvmOptions }}
{{- end }}

{{- /* ===================================== */}}
{{- /*     ALLUXIO_JOB_MASTER_JAVA_OPTS      */}}
{{- /* ===================================== */}}
{{- $jobMasterJavaOpts := list }}
{{- $jobMasterJavaOpts = print "-Dalluxio.master.hostname=${ALLUXIO_MASTER_HOSTNAME}" | append $jobMasterJavaOpts }}
{{- range $key, $val := .Values.jobMaster.properties }}
  {{- $jobMasterJavaOpts = printf "-D%v=%v" $key $val | append $jobMasterJavaOpts }}
{{- end }}
{{- if .Values.jobMaster.jvmOptions }}
  {{- $jobMasterJavaOpts = concat $jobMasterJavaOpts .Values.jobMaster.jvmOptions }}
{{- end }}

{{- /* ===================================== */}}
{{- /*       ALLUXIO_WORKER_JAVA_OPTS        */}}
{{- /* ===================================== */}}
{{- $workerJavaOpts := list }}
{{- $workerJavaOpts = print "-Dalluxio.worker.hostname=${ALLUXIO_WORKER_HOSTNAME}" | append $workerJavaOpts }}
{{- $workerJavaOpts = printf "-Dalluxio.worker.rpc.port=%v" .Values.worker.ports.rpc | append $workerJavaOpts }}
{{- $workerJavaOpts = printf "-Dalluxio.worker.web.port=%v" .Values.worker.ports.web | append $workerJavaOpts }}

{{- /* Worker Fuse configurations */}}
{{- if eq .Values.worker.fuseEnabled true}}
  {{- $workerJavaOpts = print "-Dalluxio.worker.fuse.enabled=true" | append $workerJavaOpts }}
  {{- $workerJavaOpts = printf "-Dalluxio.fuse.mount.alluxio.path=%v" .Values.fuse.alluxioPath | append $workerJavaOpts }}
  {{- $workerJavaOpts = printf "-Dalluxio.fuse.mount.point=%v" .Values.fuse.mountPath | append $workerJavaOpts }}
  {{- if .Values.fuse.mountOptions }}
  {{- $workerJavaOpts = printf "-Dalluxio.fuse.mount.options=%v" .Values.fuse.mountOptions | append $workerJavaOpts }}
  {{- end }}
  {{- $workerJavaOpts = print "-Dalluxio.user.hostname=${ALLUXIO_CLIENT_HOSTNAME}" | append $workerJavaOpts }}
  {{- if .Values.fuse.jvmOptions }}
    {{- $workerJavaOpts = concat $workerJavaOpts .Values.fuse.jvmOptions }}
  {{- end }}
{{- end }}

{{- /* Short circuit configuration */}}
{{- if eq .Values.shortCircuit.enabled false}}
  {{- $workerJavaOpts = print "-Dalluxio.user.short.circuit.enabled=false" | append $workerJavaOpts }}
{{- end }}
{{- if and .Values.shortCircuit.enabled (eq .Values.shortCircuit.policy "uuid") }}
  {{- $workerJavaOpts = print "-Dalluxio.worker.data.server.domain.socket.address=/opt/domain" | append $workerJavaOpts }}
  {{- $workerJavaOpts = print "-Dalluxio.worker.data.server.domain.socket.as.uuid=true" | append $workerJavaOpts }}
{{- end}}
{{- /* Record container hostname if not using host network */}}
{{- if not .Values.worker.hostNetwork }}
  {{- $workerJavaOpts = print "-Dalluxio.worker.container.hostname=${ALLUXIO_WORKER_CONTAINER_HOSTNAME}" | append $workerJavaOpts }}
{{- end}}

{{- /* Resource configuration */}}
{{- if .Values.worker.resources  }}
  {{- if .Values.worker.resources.requests }}
    {{- if .Values.worker.resources.requests.memory }}
          {{- $workerJavaOpts = printf "-Dalluxio.worker.ramdisk.size=%v" .Values.worker.resources.requests.memory | append $workerJavaOpts }}
    {{- end}}
  {{- end}}
{{- end}}

{{- /* Tiered store configuration */}}
{{- if .Values.tieredstore }}
  {{- $workerJavaOpts = printf "-Dalluxio.worker.tieredstore.levels=%v" (len .Values.tieredstore.levels) | append $workerJavaOpts }}
  {{- range .Values.tieredstore.levels }}
    {{- $tierName := printf "-Dalluxio.worker.tieredstore.level%v" .level }}
    {{- if .alias }}
    {{- $workerJavaOpts = printf "%v.alias=%v" $tierName .alias | append $workerJavaOpts }}
    {{- end}}
    {{- $workerJavaOpts = printf "%v.dirs.mediumtype=%v" $tierName .mediumtype | append $workerJavaOpts }}
    {{- if .path }}
      {{- $workerJavaOpts = printf "%v.dirs.path=%v" $tierName .path | append $workerJavaOpts }}
    {{- end}}
    {{- if .quota }}
      {{- $workerJavaOpts = printf "%v.dirs.quota=%v" $tierName .quota | append $workerJavaOpts }}
    {{- end}}
    {{- if .high }}
      {{- $workerJavaOpts = printf "%v.watermark.high.ratio=%v" $tierName .high | append $workerJavaOpts }}
    {{- end}}
    {{- if .low }}
      {{- $workerJavaOpts = printf "%v.watermark.low.ratio=%v" $tierName .low | append $workerJavaOpts }}
    {{- end}}
  {{- end}}
{{- end }}

{{- range $key, $val := .Values.worker.properties }}
  {{- $workerJavaOpts = printf "-D%v=%v" $key $val | append $workerJavaOpts }}
{{- end }}
{{- if .Values.worker.jvmOptions }}
  {{- $workerJavaOpts = concat $workerJavaOpts .Values.worker.jvmOptions }}
{{- end }}

{{- /* ===================================== */}}
{{- /*     ALLUXIO_JOB_WORKER_JAVA_OPTS      */}}
{{- /* ===================================== */}}
{{- $jobWorkerJavaOpts := list }}
{{- $jobWorkerJavaOpts = print "-Dalluxio.worker.hostname=${ALLUXIO_WORKER_HOSTNAME}" | append $jobWorkerJavaOpts }}
{{- $jobWorkerJavaOpts = printf "-Dalluxio.job.worker.rpc.port=%v" .Values.jobWorker.ports.rpc | append $jobWorkerJavaOpts }}
{{- $jobWorkerJavaOpts = printf "-Dalluxio.job.worker.data.port=%v" .Values.jobWorker.ports.data | append $jobWorkerJavaOpts }}
{{- $jobWorkerJavaOpts = printf "-Dalluxio.job.worker.web.port=%v" .Values.jobWorker.ports.web | append $jobWorkerJavaOpts }}
{{- range $key, $val := .Values.jobWorker.properties }}
  {{- $jobWorkerJavaOpts = printf "-D%v=%v" $key $val | append $jobWorkerJavaOpts }}
{{- end }}
{{- if .Values.jobWorker.jvmOptions }}
  {{- $jobWorkerJavaOpts = concat $jobWorkerJavaOpts .Values.jobWorker.jvmOptions }}
{{- end }}

{{- /* ===================================== */}}
{{- /*        ALLUXIO_FUSE_JAVA_OPTS         */}}
{{- /* ===================================== */}}
{{- $fuseJavaOpts := list }}
{{- if eq .Values.worker.fuseEnabled false }}
  {{- $fuseJavaOpts = print "-Dalluxio.user.hostname=${ALLUXIO_CLIENT_HOSTNAME}" | append $fuseJavaOpts }}
  {{- range $key, $val := .Values.fuse.properties }}
    {{- $fuseJavaOpts = printf "-D%v=%v" $key $val | append $fuseJavaOpts }}
  {{- end }}
  {{- $fuseJavaOpts = printf "-Dalluxio.fuse.mount.alluxio.path=%v" .Values.fuse.alluxioPath | append $fuseJavaOpts }}
  {{- $fuseJavaOpts = printf "-Dalluxio.fuse.mount.point=%v" .Values.fuse.mountPath | append $fuseJavaOpts }}
  {{- if .Values.fuse.mountOptions }}
  {{- $fuseJavaOpts = printf "-Dalluxio.fuse.mount.options=%v" .Values.fuse.mountOptions | append $fuseJavaOpts }}
  {{- end }}
  {{- if .Values.fuse.jvmOptions }}
    {{- $fuseJavaOpts = concat $fuseJavaOpts .Values.fuse.jvmOptions }}
  {{- end }}
{{- end }}

{{- /* ===================================== */}}
{{- /*       ALLUXIO_PROXY_JAVA_OPTS        */}}
{{- /* ===================================== */}}
{{- $proxyJavaOpts := list }}
{{- if eq .Values.proxy.enabled true }}
  {{- $proxyJavaOpts = print "-Dalluxio.user.hostname=${ALLUXIO_CLIENT_HOSTNAME}" | append $proxyJavaOpts }}
  {{- range $key, $val := .Values.proxy.properties }}
    {{- $proxyJavaOpts = printf "-D%v=%v" $key $val | append $proxyJavaOpts }}
  {{- end }}
  {{- if .Values.proxy.jvmOptions }}
    {{- $proxyJavaOpts = concat $proxyJavaOpts .Values.proxy.jvmOptions }}
  {{- end }}
{{- end }}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullName }}-config
  labels:
    name: {{ $fullName }}-config
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  ALLUXIO_JAVA_OPTS: |-
    {{- /* Format ALLUXIO_JAVA_OPTS list to one line */}}
    {{ range $key := $alluxioJavaOpts }}{{ printf "%v " $key }}{{ end }}
  ALLUXIO_MASTER_JAVA_OPTS: |-
    {{- /* Format ALLUXIO_MASTER_JAVA_OPTS list to one line */}}
    {{ range $key := $masterJavaOpts }}{{ printf "%v " $key }}{{ end }}
  ALLUXIO_JOB_MASTER_JAVA_OPTS: |-
    {{- /* Format ALLUXIO_JOB_MASTER_JAVA_OPTS list to one line */}}
    {{ range $key := $jobMasterJavaOpts }}{{ printf "%v " $key }}{{ end }}
  ALLUXIO_WORKER_JAVA_OPTS: |-
    {{- /* Format ALLUXIO_WORKER_JAVA_OPTS list to one line */}}
    {{ range $key := $workerJavaOpts }}{{ printf "%v " $key }}{{ end }}
  ALLUXIO_PROXY_JAVA_OPTS: |-
    {{- /* Format ALLUXIO_PROXY_JAVA_OPTS list to one line */}}
    {{ range $key := $proxyJavaOpts }}{{ printf "%v " $key }}{{ end }}
  ALLUXIO_JOB_WORKER_JAVA_OPTS: |-
    {{- /* Format ALLUXIO_JOB_WORKER_JAVA_OPTS list to one line */}}
    {{ range $key := $jobWorkerJavaOpts }}{{ printf "%v " $key }}{{ end }}
  {{- if eq .Values.worker.fuseEnabled false }}
  ALLUXIO_FUSE_JAVA_OPTS: |-
    {{- /* Format ALLUXIO_FUSE_JAVA_OPTS list to one line */}}
    {{ range $key := $fuseJavaOpts }}{{ printf "%v " $key }}{{ end }}
  {{- end }}
  ALLUXIO_WORKER_TIEREDSTORE_LEVEL0_DIRS_PATH: /dev/shm
  {{- if .Values.logserver.enabled }}
  ALLUXIO_LOGSERVER_HOSTNAME: {{ $fullName }}-logserver
  {{- /* Note: The value must be a string or kubectl will fail to read it */}}
  ALLUXIO_LOGSERVER_PORT: {{ quote .Values.logserver.ports.logging }}
  {{- end }}
