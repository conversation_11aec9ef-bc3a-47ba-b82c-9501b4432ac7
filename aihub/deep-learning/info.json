[{"doc": "http://www.data-master.net:8888/aihub/deoldify", "field": "机器视觉", "scenes": "图像合成", "type": "dateset,notebook,train,inference", "name": "deoldify", "status": "online", "version": "v20221001", "uuid": "deoldify-v20221001", "label": "图片上色", "describe": "图片上色", "pic": "https://picx.zhimg.com/v2-e96dd757c96464427560a9b5e5b07bc3_720w.jpg?source=172ae18b", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {}}, {"doc": "http://www.data-master.net:8888/aihub/paddleocr", "field": "机器视觉", "scenes": "图像识别", "type": "dateset,notebook,train,inference", "name": "paddleocr", "status": "online", "version": "v20221001", "uuid": "paddleocr-v20221001", "label": "ocr识别", "describe": "paddleocr提供的ocr识别", "pic": "https://blog.devzeng.com/images/ios-tesseract-ocr/how-ocr.png", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {}}, {"doc": "http://www.data-master.net:8888/aihub/ddddocr", "field": "机器视觉", "scenes": "图像识别", "type": "dateset,notebook,train,inference", "name": "ddddocr", "status": "online", "version": "v20221001", "uuid": "ddddocr-v20221001", "label": "验证码识别", "describe": "ai识别验证码文字和验证码目标", "pic": "http://www.data-master.net:8888/ddddocr/static/example/ddddocr/example.jpg", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {}}, {"doc": "http://www.data-master.net:8888/aihub/gfpgan", "field": "机器视觉", "scenes": "图像合成", "type": "dateset,notebook,train,inference", "name": "gfpgan", "status": "online", "version": "v20221001", "uuid": "gfpgan-v20221001", "label": "图片修复", "describe": "低分辨率照片修复，清晰度增强", "pic": "http://www.data-master.net:8888/gfpgan/static/example/gfpgan/example.jpg", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {}}, {"doc": "http://www.data-master.net:8888/aihub/stable-diffusion", "field": "神经网络", "scenes": "图像创作", "type": "dateset,notebook,train,inference", "name": "stable-diffusion", "status": "online", "version": "v20221022", "uuid": "stable-diffusion-v20221022", "label": "文字转图像", "describe": "输入一串文字描述，可生成相应的图片", "pic": "https://images.nightcafe.studio//assets/stable-tile.jpg", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {"resource_gpu": "1"}}, {"doc": "http://www.data-master.net:8888/aihub/yolov7", "field": "机器视觉", "scenes": "目标识别", "type": "dateset,notebook,train,inference", "name": "yolov7", "status": "online", "version": "v20221001", "uuid": "yolov7-v20221001", "label": "目标识别", "describe": "yolov7 目标识别", "pic": "http://www.data-master.net:8888/yolov7/static/example/yolov3/example.jpg", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {}}, {"doc": "http://www.data-master.net:8888/aihub/humanseg", "field": "机器视觉", "scenes": "图像合成", "type": "dateset,notebook,train,inference", "name": "humanseg", "status": "online", "version": "v20221001", "uuid": "humanseg-v20221001", "label": "人体分割背景替换", "describe": "人体分割背景替换，视频会议背景替换", "pic": "http://www.data-master.net:8888/humanseg/static/example/humanseg/example.jpg", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {}}, {"doc": "http://www.data-master.net:8888/aihub/panoptic", "field": "机器视觉", "scenes": "目标识别", "type": "dateset,notebook,train,inference", "name": "panoptic", "status": "online", "version": "v20221001", "uuid": "panoptic-v20221001", "label": "图片识别", "describe": "resnet50 图像识别", "pic": "http://www.data-master.net:8888/panoptic/static/example/panoptic/test.jpg", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {}}, {"doc": "http://www.data-master.net:8888/aihub/animegan", "field": "机器视觉", "scenes": "图像合成", "type": "dateset,notebook,train,inference", "name": "animegan", "status": "online", "version": "v20221001", "uuid": "animegan-v20221001", "label": "动漫风格化", "describe": "图片的全新动漫风格化，宫崎骏或新海诚风格的动漫，以及4种关于人脸的风格转换。", "pic": "http://www.data-master.net:8888/animegan/static/example/animegan/example.jpg", "hot": "1", "price": "0", "dataset": {}, "notebook": {"jupyter": [], "appendix": []}, "train": {}, "inference": {"resource_gpu": "1"}}]