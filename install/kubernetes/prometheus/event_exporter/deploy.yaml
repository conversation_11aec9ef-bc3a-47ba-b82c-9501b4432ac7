---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    name: event-exporter
  name: event-exporter
  namespace: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1beta1
kind: ClusterRoleBinding
metadata:
  labels:
    name: event-exporter
  name: event-exporter
  namespace: monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: view
subjects:
- kind: ServiceAccount
  name: event-exporter
  namespace: monitoring
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/scrape: "true"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: https
  labels:
    k8s-app: event-exporter
  name: event-exporter
  namespace: monitoring
spec:
  ports:
    - name: http
      port: 80
      targetPort: 9102
  selector:
    app: event-exporter
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    name: event-exporter
  name: event-exporter
  namespace: monitoring
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: event-exporter
  strategy:
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: "9102"
        prometheus.io/scrape: "true"
      labels:
        app: event-exporter
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: monitoring
                operator: In
                values:
                - "true"
      containers:
        - command:
            - ./event_exporter
          env: []
          image: ccr.ccs.tencentyun.com/cube-studio/event-exporter
          imagePullPolicy: IfNotPresent
          name: event-exporter
          ports:
            - containerPort: 9102
              name: http
          resources:
#            limits:
#              memory: 100Mi
            requests:
              memory: 40Mi
      serviceAccountName: event-exporter
      terminationGracePeriodSeconds: 30
