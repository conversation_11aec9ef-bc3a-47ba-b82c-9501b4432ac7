---
# Source: prometheus-adapter/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: kubeflow-prometheus-adapter
  namespace: kube-system
---

# Source: prometheus-adapter/templates/cluster-role-resource-reader.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: prometheus-adapter-resource-reader
rules:
- apiGroups:
  - ""
  resources:
  - namespaces
  - pods
  - services
  - configmaps
  verbs:
  - get
  - list
  - watch
---
# Source: prometheus-adapter/templates/custom-metrics-cluster-role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: prometheus-adapter-server-resources
rules:
- apiGroups:
  - custom.metrics.k8s.io
  resources: ["*"]
  verbs: ["*"]
---
# Source: prometheus-adapter/templates/cluster-role-binding-auth-delegator.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: prometheus-adapter-system-auth-delegator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:auth-delegator
subjects:
- kind: ServiceAccount
  name: kubeflow-prometheus-adapter
  namespace: "kube-system"
---
# Source: prometheus-adapter/templates/cluster-role-binding-resource-reader.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: prometheus-adapter-resource-reader
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus-adapter-resource-reader
subjects:
- kind: ServiceAccount
  name: kubeflow-prometheus-adapter
  namespace: "kube-system"
---
# Source: prometheus-adapter/templates/custom-metrics-cluster-role-binding-hpa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: prometheus-adapter-hpa-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus-adapter-server-resources
subjects:
- kind: ServiceAccount
  name: kubeflow-prometheus-adapter
  namespace: "kube-system"
---
# Source: prometheus-adapter/templates/role-binding-auth-reader.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: prometheus-adapter-auth-reader
  namespace: kube-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: extension-apiserver-authentication-reader
subjects:
- kind: ServiceAccount
  name: kubeflow-prometheus-adapter
  namespace: "kube-system"
---
# Source: prometheus-adapter/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: kubeflow-prometheus-adapter
  namespace: kube-system
spec:
  ports:
  - port: 443
    protocol: TCP
    targetPort: https
  selector:
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
  type: ClusterIP
---
# Source: prometheus-adapter/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: kubeflow-prometheus-adapter
  namespace: kube-system
spec:
  replicas: 1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  selector:
    matchLabels:
      app.kubernetes.io/name: prometheus-adapter
      app.kubernetes.io/instance: kubeflow
  template:
    metadata:
      labels:
        app.kubernetes.io/component: metrics
        app.kubernetes.io/part-of: prometheus-adapter
        app.kubernetes.io/name: prometheus-adapter
        app.kubernetes.io/instance: kubeflow
        app.kubernetes.io/version: "v0.9.1"
      name: prometheus-adapter
      annotations:
        checksum/config: 484b2ba8875689a707c09b0508be521a111f1f593e2fda532310317f654938f8
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: monitoring
                operator: In
                values:
                - "true"
      serviceAccountName: kubeflow-prometheus-adapter
      containers:
      - name: prometheus-adapter
        image: "ccr.ccs.tencentyun.com/cube-studio/prometheus-adapter:v0.9.1"
        imagePullPolicy: IfNotPresent
        args:
        - /adapter
        - --secure-port=6443
        - --cert-dir=/tmp/cert
        - --prometheus-url=http://prometheus-k8s.monitoring.svc:9090
        - --metrics-relist-interval=1m
        - --v=4
        - --config=/etc/adapter/config.yaml
        ports:
        - containerPort: 6443
          name: https
        livenessProbe:
          httpGet:
            path: /healthz
            port: https
            scheme: HTTPS
          initialDelaySeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /healthz
            port: https
            scheme: HTTPS
          initialDelaySeconds: 30
          timeoutSeconds: 5
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: ["all"]
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 10001
        volumeMounts:
        - mountPath: /etc/adapter/
          name: config
          readOnly: true
        - mountPath: /tmp
          name: tmp
      nodeSelector:
        {}
      securityContext:
        fsGroup: 10001
      tolerations:
        []
      volumes:
      - name: config
        configMap:
          name: kubeflow-prometheus-adapter
      - name: tmp
        emptyDir: {}
---
# Source: prometheus-adapter/templates/custom-metrics-apiservice.yaml
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  labels:
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-adapter
    app.kubernetes.io/name: prometheus-adapter
    app.kubernetes.io/instance: kubeflow
    app.kubernetes.io/version: "v0.9.1"
  name: v1beta1.custom.metrics.k8s.io
spec:
  service:
    name: kubeflow-prometheus-adapter
    namespace: "kube-system"
  group: custom.metrics.k8s.io
  version: v1beta1
  insecureSkipTLSVerify: true
  groupPriorityMinimum: 100
  versionPriority: 100
