---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: istio-validator-istio-system
  labels:
    app: istiod
    release: istio
    istio: istiod
    istio.io/rev: default
webhooks:
  # Webhook handling per-revision validation. Mostly here so we can determine whether webhooks
  # are rejecting invalid configs on a per-revision basis.
  - name: rev.validation.istio.io
    clientConfig:
      # Should change from base but cannot for API compat
      service:
        name: istiod
        namespace: istio-system
        path: "/validate"
    rules:
      - operations:
          - CREATE
          - UPDATE
        apiGroups:
          - security.istio.io
          - networking.istio.io
          - telemetry.istio.io
          - extensions.istio.io
        apiVersions:
          - "*"
        resources:
          - "*"
    # Fail open until the validation webhook is ready. The webhook controller
    # will update this to `Fail` and patch in the `caBundle` when the webhook
    # endpoint is ready.
    failurePolicy: Ignore
    sideEffects: None
    admissionReviewVersions: ["v1beta1", "v1"]
    objectSelector:
      matchExpressions:
        - key: istio.io/rev
          operator: In
          values:
          - "default"
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.11
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.11.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.11.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true,
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.11.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.12
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.12.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.12.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true,
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.12.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.13
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.13.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.13.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true,
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.13.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.14
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.14.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.14.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true,
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.14.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.15
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.15.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.15.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true,
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.15.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.11
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.11.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.11.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.11.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.12
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.12.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.12.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.12.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.13
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.13.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.13.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.13.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.14
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.14.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.14.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.14.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.15
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  priority: -1
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.15.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.15.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.15.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio"
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: istio
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    release: istio
data:

  # Configuration file for the mesh networks to be used by the Split Horizon EDS.
  meshNetworks: |-
    networks: {}

  mesh: |-
    defaultConfig:
      discoveryAddress: istiod.istio-system.svc:15012
      proxyMetadata: {}
      tracing:
        zipkin:
          address: zipkin.istio-system:9411
    enablePrometheusMerge: true
    rootNamespace: istio-system
    trustDomain: cluster.local
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: istio-sidecar-injector
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    release: istio
data:

  values: |-
    {
      "global": {
        "autoscalingv2API": true,
        "caAddress": "",
        "caName": "",
        "configCluster": false,
        "configValidation": true,
        "defaultNodeSelector": {},
        "defaultPodDisruptionBudget": {
          "enabled": true
        },
        "defaultResources": {
          "requests": {
            "cpu": "10m"
          }
        },
        "enabled": true,
        "externalIstiod": false,
        "hub": "docker.io/istio",
        "imagePullPolicy": "",
        "imagePullSecrets": [],
        "istioNamespace": "istio-system",
        "istiod": {
          "enableAnalysis": false
        },
        "jwtPolicy": "first-party-jwt",
        "logAsJson": false,
        "logging": {
          "level": "default:info"
        },
        "meshID": "",
        "meshNetworks": {},
        "mountMtlsCerts": false,
        "multiCluster": {
          "clusterName": "",
          "enabled": false
        },
        "namespace": "istio-system",
        "network": "",
        "omitSidecarInjectorConfigMap": false,
        "oneNamespace": false,
        "operatorManageWebhooks": false,
        "pilotCertProvider": "istiod",
        "priorityClassName": "",
        "proxy": {
          "autoInject": "enabled",
          "clusterDomain": "cluster.local",
          "componentLogLevel": "misc:error",
          "enableCoreDump": false,
          "excludeIPRanges": "",
          "excludeInboundPorts": "",
          "excludeOutboundPorts": "",
          "holdApplicationUntilProxyStarts": false,
          "image": "proxyv2",
          "includeIPRanges": "*",
          "includeInboundPorts": "*",
          "includeOutboundPorts": "",
          "logLevel": "warning",
          "privileged": false,
          "readinessFailureThreshold": 30,
          "readinessInitialDelaySeconds": 1,
          "readinessPeriodSeconds": 2,
          "resources": {
            "limits": {
              "cpu": "2000m",
              "memory": "1024Mi"
            },
            "requests": {
              "cpu": "100m",
              "memory": "128Mi"
            }
          },
          "statusPort": 15020,
          "tracer": "zipkin"
        },
        "proxy_init": {
          "image": "proxyv2",
          "resources": {
            "limits": {
              "cpu": "2000m",
              "memory": "1024Mi"
            },
            "requests": {
              "cpu": "10m",
              "memory": "10Mi"
            }
          }
        },
        "remotePilotAddress": "",
        "sds": {
          "token": {
            "aud": "istio-ca"
          }
        },
        "sts": {
          "servicePort": 0
        },
        "tag": "1.14.1",
        "tracer": {
          "datadog": {
            "address": "$(HOST_IP):8126"
          },
          "lightstep": {
            "accessToken": "",
            "address": ""
          },
          "stackdriver": {
            "debug": false,
            "maxNumberOfAnnotations": 200,
            "maxNumberOfAttributes": 200,
            "maxNumberOfMessageEvents": 200
          },
          "zipkin": {
            "address": ""
          }
        },
        "useMCP": false
      },
      "istio_cni": {
        "enabled": false
      },
      "revision": "",
      "sidecarInjectorWebhook": {
        "alwaysInjectSelector": [],
        "defaultTemplates": [],
        "enableNamespacesByDefault": false,
        "injectedAnnotations": {},
        "neverInjectSelector": [],
        "rewriteAppHTTPProbe": true,
        "templates": {}
      }
    }

  # To disable injection: use omitSidecarInjectorConfigMap, which disables the webhook patching
  # and istiod webhook functionality.
  #
  # New fields should not use Values - it is a 'primary' config object, users should be able
  # to fine tune it or use it with kube-inject.
  config: |-
    # defaultTemplates defines the default template to use for pods that do not explicitly specify a template
    defaultTemplates: [sidecar]
    policy: enabled
    alwaysInjectSelector:
      []
    neverInjectSelector:
      []
    injectedAnnotations:
    template: "{{ Template_Version_And_Istio_Version_Mismatched_Check_Installation }}"
    templates:
      sidecar: |
        {{- define "resources"  }}
          {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) }}
              requests:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory` }}"
                {{ end }}
            {{- end }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
              limits:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit` }}"
                {{ end }}
            {{- end }}
          {{- else }}
            {{- if .Values.global.proxy.resources }}
              {{ toYaml .Values.global.proxy.resources | indent 6 }}
            {{- end }}
          {{- end }}
        {{- end }}
        {{- $containers := list }}
        {{- range $index, $container := .Spec.Containers }}{{ if not (eq $container.Name "istio-proxy") }}{{ $containers = append $containers $container.Name }}{{end}}{{- end}}
        metadata:
          labels:
            security.istio.io/tlsMode: {{ index .ObjectMeta.Labels `security.istio.io/tlsMode` | default "istio"  | quote }}
            service.istio.io/canonical-name: {{ index .ObjectMeta.Labels `service.istio.io/canonical-name` | default (index .ObjectMeta.Labels `app.kubernetes.io/name`) | default (index .ObjectMeta.Labels `app`) | default .DeploymentMeta.Name  | quote }}
            service.istio.io/canonical-revision: {{ index .ObjectMeta.Labels `service.istio.io/canonical-revision` | default (index .ObjectMeta.Labels `app.kubernetes.io/version`) | default (index .ObjectMeta.Labels `version`) | default "latest"  | quote }}
          annotations: {
            {{- if ge (len $containers) 1 }}
            {{- if not (isset .ObjectMeta.Annotations `kubectl.kubernetes.io/default-logs-container`) }}
            kubectl.kubernetes.io/default-logs-container: "{{ index $containers 0 }}",
            {{- end }}
            {{- if not (isset .ObjectMeta.Annotations `kubectl.kubernetes.io/default-container`) }}
            kubectl.kubernetes.io/default-container: "{{ index $containers 0 }}",
            {{- end }}
            {{- end }}
        {{- if .Values.istio_cni.enabled }}
            {{- if not .Values.istio_cni.chained }}
            k8s.v1.cni.cncf.io/networks: '{{ appendMultusNetwork (index .ObjectMeta.Annotations `k8s.v1.cni.cncf.io/networks`) `istio-cni` }}',
            {{- end }}
            sidecar.istio.io/interceptionMode: "{{ annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode }}",
            {{ with annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundIPRanges` .Values.global.proxy.includeIPRanges }}traffic.sidecar.istio.io/includeOutboundIPRanges: "{{.}}",{{ end }}
            {{ with annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundIPRanges` .Values.global.proxy.excludeIPRanges }}traffic.sidecar.istio.io/excludeOutboundIPRanges: "{{.}}",{{ end }}
            {{ with annotation .ObjectMeta `traffic.sidecar.istio.io/includeInboundPorts` .Values.global.proxy.includeInboundPorts }}traffic.sidecar.istio.io/includeInboundPorts: "{{.}}",{{ end }}
            traffic.sidecar.istio.io/excludeInboundPorts: "{{ excludeInboundPort (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) (annotation .ObjectMeta `traffic.sidecar.istio.io/excludeInboundPorts` .Values.global.proxy.excludeInboundPorts) }}",
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/includeOutboundPorts`) (ne (valueOrDefault .Values.global.proxy.includeOutboundPorts "") "") }}
            traffic.sidecar.istio.io/includeOutboundPorts: "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundPorts` .Values.global.proxy.includeOutboundPorts }}",
            {{- end }}
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/excludeOutboundPorts`) (ne .Values.global.proxy.excludeOutboundPorts "") }}
            traffic.sidecar.istio.io/excludeOutboundPorts: "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundPorts` .Values.global.proxy.excludeOutboundPorts }}",
            {{- end }}
            {{ with index .ObjectMeta.Annotations `traffic.sidecar.istio.io/kubevirtInterfaces` }}traffic.sidecar.istio.io/kubevirtInterfaces: "{{.}}",{{ end }}
        {{- end }}
          }
        spec:
          {{- $holdProxy := or .ProxyConfig.HoldApplicationUntilProxyStarts.GetValue .Values.global.proxy.holdApplicationUntilProxyStarts }}
          initContainers:
          {{ if ne (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `NONE` }}
          {{ if .Values.istio_cni.enabled -}}
          - name: istio-validation
          {{ else -}}
          - name: istio-init
          {{ end -}}
          {{- if contains "/" (annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image) }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image }}"
          {{- else }}
            image: "{{ .ProxyImage }}"
          {{- end }}
            args:
            - istio-iptables
            - "-p"
            - {{ .MeshConfig.ProxyListenPort | default "15001" | quote }}
            - "-z"
            - "15006"
            - "-u"
            - "1337"
            - "-m"
            - "{{ annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode }}"
            - "-i"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundIPRanges` .Values.global.proxy.includeIPRanges }}"
            - "-x"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundIPRanges` .Values.global.proxy.excludeIPRanges }}"
            - "-b"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeInboundPorts` .Values.global.proxy.includeInboundPorts }}"
            - "-d"
          {{- if excludeInboundPort (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) (annotation .ObjectMeta `traffic.sidecar.istio.io/excludeInboundPorts` .Values.global.proxy.excludeInboundPorts) }}
            - "15090,15021,{{ excludeInboundPort (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) (annotation .ObjectMeta `traffic.sidecar.istio.io/excludeInboundPorts` .Values.global.proxy.excludeInboundPorts) }}"
          {{- else }}
            - "15090,15021"
          {{- end }}
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/includeOutboundPorts`) (ne (valueOrDefault .Values.global.proxy.includeOutboundPorts "") "") -}}
            - "-q"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundPorts` .Values.global.proxy.includeOutboundPorts }}"
            {{ end -}}
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/excludeOutboundPorts`) (ne (valueOrDefault .Values.global.proxy.excludeOutboundPorts "") "") -}}
            - "-o"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundPorts` .Values.global.proxy.excludeOutboundPorts }}"
            {{ end -}}
            {{ if (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/kubevirtInterfaces`) -}}
            - "-k"
            - "{{ index .ObjectMeta.Annotations `traffic.sidecar.istio.io/kubevirtInterfaces` }}"
            {{ end -}}
            {{ if .Values.istio_cni.enabled -}}
            - "--run-validation"
            - "--skip-rule-apply"
            {{ end -}}
            {{with .Values.global.imagePullPolicy }}imagePullPolicy: "{{.}}"{{end}}
          {{- if .ProxyConfig.ProxyMetadata }}
            env:
            {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
          {{- end }}
            resources:
          {{ template "resources" . }}
            securityContext:
              allowPrivilegeEscalation: {{ .Values.global.proxy.privileged }}
              privileged: {{ .Values.global.proxy.privileged }}
              capabilities:
            {{- if not .Values.istio_cni.enabled }}
                add:
                - NET_ADMIN
                - NET_RAW
            {{- end }}
                drop:
                - ALL
            {{- if not .Values.istio_cni.enabled }}
              readOnlyRootFilesystem: false
              runAsGroup: 0
              runAsNonRoot: false
              runAsUser: 0
            {{- else }}
              readOnlyRootFilesystem: true
              runAsGroup: 1337
              runAsUser: 1337
              runAsNonRoot: true
            {{- end }}
            restartPolicy: Always
          {{ end -}}
          {{- if eq (annotation .ObjectMeta `sidecar.istio.io/enableCoreDump` .Values.global.proxy.enableCoreDump) "true" }}
          - name: enable-core-dump
            args:
            - -c
            - sysctl -w kernel.core_pattern=/var/lib/istio/data/core.proxy && ulimit -c unlimited
            command:
              - /bin/sh
          {{- if contains "/" (annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image) }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image }}"
          {{- else }}
            image: "{{ .ProxyImage }}"
          {{- end }}
            {{with .Values.global.imagePullPolicy }}imagePullPolicy: "{{.}}"{{end}}
            resources:
          {{ template "resources" . }}
            securityContext:
              allowPrivilegeEscalation: true
              capabilities:
                add:
                - SYS_ADMIN
                drop:
                - ALL
              privileged: true
              readOnlyRootFilesystem: false
              runAsGroup: 0
              runAsNonRoot: false
              runAsUser: 0
          {{ end }}
          containers:
          - name: istio-proxy
          {{- if contains "/" (annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image) }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image }}"
          {{- else }}
            image: "{{ .ProxyImage }}"
          {{- end }}
            ports:
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
            args:
            - proxy
            - sidecar
            - --domain
            - $(POD_NAMESPACE).svc.{{ .Values.global.proxy.clusterDomain }}
            - --proxyLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/logLevel` .Values.global.proxy.logLevel }}
            - --proxyComponentLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/componentLogLevel` .Values.global.proxy.componentLogLevel }}
            - --log_output_level={{ annotation .ObjectMeta `sidecar.istio.io/agentLogLevel` .Values.global.logging.level }}
          {{- if .Values.global.sts.servicePort }}
            - --stsPort={{ .Values.global.sts.servicePort }}
          {{- end }}
          {{- if .Values.global.logAsJson }}
            - --log_as_json
          {{- end }}
          {{- if gt .EstimatedConcurrency 0 }}
            - --concurrency
            - "{{ .EstimatedConcurrency }}"
          {{- end -}}
          {{- if .Values.global.proxy.lifecycle }}
            lifecycle:
              {{ toYaml .Values.global.proxy.lifecycle | indent 6 }}
          {{- else if $holdProxy }}
            lifecycle:
              postStart:
                exec:
                  command:
                  - pilot-agent
                  - wait
          {{- end }}
            env:
            {{- if eq (env "PILOT_ENABLE_INBOUND_PASSTHROUGH" "true") "false" }}
            - name: REWRITE_PROBE_LEGACY_LOCALHOST_DESTINATION
              value: "true"
            {{- end }}
            - name: JWT_POLICY
              value: {{ .Values.global.jwtPolicy }}
            - name: PILOT_CERT_PROVIDER
              value: {{ .Values.global.pilotCertProvider }}
            - name: CA_ADDR
            {{- if .Values.global.caAddress }}
              value: {{ .Values.global.caAddress }}
            {{- else }}
              value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.global.istioNamespace }}.svc:15012
            {{- end }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: INSTANCE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: PROXY_CONFIG
              value: |
                     {{ protoToJSON .ProxyConfig }}
            - name: ISTIO_META_POD_PORTS
              value: |-
                [
                {{- $first := true }}
                {{- range $index1, $c := .Spec.Containers }}
                  {{- range $index2, $p := $c.Ports }}
                    {{- if (structToJSON $p) }}
                    {{if not $first}},{{end}}{{ structToJSON $p }}
                    {{- $first = false }}
                    {{- end }}
                  {{- end}}
                {{- end}}
                ]
            - name: ISTIO_META_APP_CONTAINERS
              value: "{{ $containers | join "," }}"
            - name: ISTIO_META_CLUSTER_ID
              value: "{{ valueOrDefault .Values.global.multiCluster.clusterName `Kubernetes` }}"
            - name: ISTIO_META_INTERCEPTION_MODE
              value: "{{ or (index .ObjectMeta.Annotations `sidecar.istio.io/interceptionMode`) .ProxyConfig.InterceptionMode.String }}"
            {{- if .Values.global.network }}
            - name: ISTIO_META_NETWORK
              value: "{{ .Values.global.network }}"
            {{- end }}
            {{- if .DeploymentMeta.Name }}
            - name: ISTIO_META_WORKLOAD_NAME
              value: "{{ .DeploymentMeta.Name }}"
            {{ end }}
            {{- if and .TypeMeta.APIVersion .DeploymentMeta.Name }}
            - name: ISTIO_META_OWNER
              value: kubernetes://apis/{{ .TypeMeta.APIVersion }}/namespaces/{{ valueOrDefault .DeploymentMeta.Namespace `default` }}/{{ toLower .TypeMeta.Kind}}s/{{ .DeploymentMeta.Name }}
            {{- end}}
            {{- if (isset .ObjectMeta.Annotations `sidecar.istio.io/bootstrapOverride`) }}
            - name: ISTIO_BOOTSTRAP_OVERRIDE
              value: "/etc/istio/custom-bootstrap/custom_bootstrap.json"
            {{- end }}
            {{- if .Values.global.meshID }}
            - name: ISTIO_META_MESH_ID
              value: "{{ .Values.global.meshID }}"
            {{- else if (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}
            - name: ISTIO_META_MESH_ID
              value: "{{ (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}"
            {{- end }}
            {{- with (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain)  }}
            - name: TRUST_DOMAIN
              value: "{{ . }}"
            {{- end }}
            {{- if and (eq .Values.global.proxy.tracer "datadog") (isset .ObjectMeta.Annotations `apm.datadoghq.com/env`) }}
            {{- range $key, $value := fromJSON (index .ObjectMeta.Annotations `apm.datadoghq.com/env`) }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
            {{- end }}
            {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
            {{with .Values.global.imagePullPolicy }}imagePullPolicy: "{{.}}"{{end}}
            {{ if ne (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) `0` }}
            readinessProbe:
              httpGet:
                path: /healthz/ready
                port: 15021
              initialDelaySeconds: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/initialDelaySeconds` .Values.global.proxy.readinessInitialDelaySeconds }}
              periodSeconds: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/periodSeconds` .Values.global.proxy.readinessPeriodSeconds }}
              timeoutSeconds: 3
              failureThreshold: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/failureThreshold` .Values.global.proxy.readinessFailureThreshold }}
            {{ end -}}
            securityContext:
              {{- if eq (index .ProxyConfig.ProxyMetadata "IPTABLES_TRACE_LOGGING") "true" }}
              allowPrivilegeEscalation: true
              capabilities:
                add:
                - NET_ADMIN
                drop:
                - ALL
              privileged: true
              readOnlyRootFilesystem: {{ ne (annotation .ObjectMeta `sidecar.istio.io/enableCoreDump` .Values.global.proxy.enableCoreDump) "true" }}
              runAsGroup: 1337
              fsGroup: 1337
              runAsNonRoot: false
              runAsUser: 0
              {{- else }}
              allowPrivilegeEscalation: {{ .Values.global.proxy.privileged }}
              capabilities:
                {{ if or (eq (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `TPROXY`) (eq (annotation .ObjectMeta `sidecar.istio.io/capNetBindService` .Values.global.proxy.capNetBindService) `true`) -}}
                add:
                {{ if eq (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `TPROXY` -}}
                - NET_ADMIN
                {{- end }}
                {{ if eq (annotation .ObjectMeta `sidecar.istio.io/capNetBindService` .Values.global.proxy.capNetBindService) `true` -}}
                - NET_BIND_SERVICE
                {{- end }}
                {{- end }}
                drop:
                - ALL
              privileged: {{ .Values.global.proxy.privileged }}
              readOnlyRootFilesystem: {{ ne (annotation .ObjectMeta `sidecar.istio.io/enableCoreDump` .Values.global.proxy.enableCoreDump) "true" }}
              runAsGroup: 1337
              fsGroup: 1337
              {{ if or (eq (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `TPROXY`) (eq (annotation .ObjectMeta `sidecar.istio.io/capNetBindService` .Values.global.proxy.capNetBindService) `true`) -}}
              runAsNonRoot: false
              runAsUser: 0
              {{- else -}}
              runAsNonRoot: true
              runAsUser: 1337
              {{- end }}
              {{- end }}
            resources:
          {{ template "resources" . }}
            volumeMounts:
            - name: workload-socket
              mountPath: /var/run/secrets/workload-spiffe-uds
            {{- if eq .Values.global.caName "GkeWorkloadCertificate" }}
            - name: gke-workload-certificate
              mountPath: /var/run/secrets/workload-spiffe-credentials
              readOnly: true
            {{- else }}
            - name: workload-certs
              mountPath: /var/run/secrets/workload-spiffe-credentials
            {{- end }}
            {{- if eq .Values.global.pilotCertProvider "istiod" }}
            - mountPath: /var/run/secrets/istio
              name: istiod-ca-cert
            {{- end }}
            - mountPath: /var/lib/istio/data
              name: istio-data
            {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/bootstrapOverride`) }}
            - mountPath: /etc/istio/custom-bootstrap
              name: custom-bootstrap-volume
            {{- end }}
            # SDS channel between istioagent and Envoy
            - mountPath: /etc/istio/proxy
              name: istio-envoy
            {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
            - mountPath: /var/run/secrets/tokens
              name: istio-token
            {{- end }}
            {{- if .Values.global.mountMtlsCerts }}
            # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
            - mountPath: /etc/certs/
              name: istio-certs
              readOnly: true
            {{- end }}
            - name: istio-podinfo
              mountPath: /etc/istio/pod
             {{- if and (eq .Values.global.proxy.tracer "lightstep") .ProxyConfig.GetTracing.GetTlsSettings }}
            - mountPath: {{ directory .ProxyConfig.GetTracing.GetTlsSettings.GetCaCertificates }}
              name: lightstep-certs
              readOnly: true
            {{- end }}
              {{- if isset .ObjectMeta.Annotations `sidecar.istio.io/userVolumeMount` }}
              {{ range $index, $value := fromJSON (index .ObjectMeta.Annotations `sidecar.istio.io/userVolumeMount`) }}
            - name: "{{  $index }}"
              {{ toYaml $value | indent 6 }}
              {{ end }}
              {{- end }}
          volumes:
          - emptyDir:
            name: workload-socket
          {{- if eq .Values.global.caName "GkeWorkloadCertificate" }}
          - name: gke-workload-certificate
            csi:
              driver: workloadcertificates.security.cloud.google.com
          {{- else }}
          - emptyDir:
            name: workload-certs
          {{- end }}
          {{- if (isset .ObjectMeta.Annotations `sidecar.istio.io/bootstrapOverride`) }}
          - name: custom-bootstrap-volume
            configMap:
              name: {{ annotation .ObjectMeta `sidecar.istio.io/bootstrapOverride` "" }}
          {{- end }}
          # SDS channel between istioagent and Envoy
          - emptyDir:
              medium: Memory
            name: istio-envoy
          - name: istio-data
            emptyDir: {}
          - name: istio-podinfo
            downwardAPI:
              items:
                - path: "labels"
                  fieldRef:
                    fieldPath: metadata.labels
                - path: "annotations"
                  fieldRef:
                    fieldPath: metadata.annotations
          {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
          - name: istio-token
            projected:
              sources:
              - serviceAccountToken:
                  path: istio-token
                  expirationSeconds: 43200
                  audience: {{ .Values.global.sds.token.aud }}
          {{- end }}
          {{- if eq .Values.global.pilotCertProvider "istiod" }}
          - name: istiod-ca-cert
            configMap:
              name: istio-ca-root-cert
          {{- end }}
          {{- if .Values.global.mountMtlsCerts }}
          # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
          - name: istio-certs
            secret:
              optional: true
              {{ if eq .Spec.ServiceAccountName "" }}
              secretName: istio.default
              {{ else -}}
              secretName: {{  printf "istio.%s" .Spec.ServiceAccountName }}
              {{  end -}}
          {{- end }}
            {{- if isset .ObjectMeta.Annotations `sidecar.istio.io/userVolume` }}
            {{range $index, $value := fromJSON (index .ObjectMeta.Annotations `sidecar.istio.io/userVolume`) }}
          - name: "{{ $index }}"
            {{ toYaml $value | indent 4 }}
            {{ end }}
            {{ end }}
          {{- if and (eq .Values.global.proxy.tracer "lightstep") .ProxyConfig.GetTracing.GetTlsSettings }}
          - name: lightstep-certs
            secret:
              optional: true
              secretName: lightstep.cacert
          {{- end }}
          {{- if .Values.global.imagePullSecrets }}
          imagePullSecrets:
            {{- range .Values.global.imagePullSecrets }}
            - name: {{ . }}
            {{- end }}
          {{- end }}
          {{- if eq (env "ENABLE_LEGACY_FSGROUP_INJECTION" "true") "true" }}
          securityContext:
            fsGroup: 1337
          {{- end }}
      gateway: |
        {{- $containers := list }}
        {{- range $index, $container := .Spec.Containers }}{{ if not (eq $container.Name "istio-proxy") }}{{ $containers = append $containers $container.Name }}{{end}}{{- end}}
        metadata:
          labels:
            service.istio.io/canonical-name: {{ index .ObjectMeta.Labels `service.istio.io/canonical-name` | default (index .ObjectMeta.Labels `app.kubernetes.io/name`) | default (index .ObjectMeta.Labels `app`) | default .DeploymentMeta.Name  | quote }}
            service.istio.io/canonical-revision: {{ index .ObjectMeta.Labels `service.istio.io/canonical-revision` | default (index .ObjectMeta.Labels `app.kubernetes.io/version`) | default (index .ObjectMeta.Labels `version`) | default "latest"  | quote }}
            istio.io/rev: {{ .Revision | default "default" | quote }}
          annotations: {
            {{- if eq (len $containers) 1 }}
            kubectl.kubernetes.io/default-logs-container: "{{ index $containers 0 }}",
            kubectl.kubernetes.io/default-container: "{{ index $containers 0 }}",
            {{ end }}
          }
        spec:
          containers:
          - name: istio-proxy
          {{- if contains "/" .Values.global.proxy.image }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image }}"
          {{- else }}
            image: "{{ .ProxyImage }}"
          {{- end }}
            ports:
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
            args:
            - proxy
            - router
            - --domain
            - $(POD_NAMESPACE).svc.{{ .Values.global.proxy.clusterDomain }}
            - --proxyLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/logLevel` .Values.global.proxy.logLevel }}
            - --proxyComponentLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/componentLogLevel` .Values.global.proxy.componentLogLevel }}
            - --log_output_level={{ annotation .ObjectMeta `sidecar.istio.io/agentLogLevel` .Values.global.logging.level }}
          {{- if .Values.global.sts.servicePort }}
            - --stsPort={{ .Values.global.sts.servicePort }}
          {{- end }}
          {{- if .Values.global.logAsJson }}
            - --log_as_json
          {{- end }}
          {{- if .Values.global.proxy.lifecycle }}
            lifecycle:
              {{ toYaml .Values.global.proxy.lifecycle | indent 6 }}
          {{- end }}
            env:
            - name: JWT_POLICY
              value: {{ .Values.global.jwtPolicy }}
            - name: PILOT_CERT_PROVIDER
              value: {{ .Values.global.pilotCertProvider }}
            - name: CA_ADDR
            {{- if .Values.global.caAddress }}
              value: {{ .Values.global.caAddress }}
            {{- else }}
              value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.global.istioNamespace }}.svc:15012
            {{- end }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: INSTANCE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: PROXY_CONFIG
              value: |
                     {{ protoToJSON .ProxyConfig }}
            - name: ISTIO_META_POD_PORTS
              value: |-
                [
                {{- $first := true }}
                {{- range $index1, $c := .Spec.Containers }}
                  {{- range $index2, $p := $c.Ports }}
                    {{- if (structToJSON $p) }}
                    {{if not $first}},{{end}}{{ structToJSON $p }}
                    {{- $first = false }}
                    {{- end }}
                  {{- end}}
                {{- end}}
                ]
            - name: ISTIO_META_APP_CONTAINERS
              value: "{{ $containers | join "," }}"
            - name: ISTIO_META_CLUSTER_ID
              value: "{{ valueOrDefault .Values.global.multiCluster.clusterName `Kubernetes` }}"
            - name: ISTIO_META_INTERCEPTION_MODE
              value: "{{ .ProxyConfig.InterceptionMode.String }}"
            {{- if .Values.global.network }}
            - name: ISTIO_META_NETWORK
              value: "{{ .Values.global.network }}"
            {{- end }}
            {{- if .DeploymentMeta.Name }}
            - name: ISTIO_META_WORKLOAD_NAME
              value: "{{ .DeploymentMeta.Name }}"
            {{ end }}
            {{- if and .TypeMeta.APIVersion .DeploymentMeta.Name }}
            - name: ISTIO_META_OWNER
              value: kubernetes://apis/{{ .TypeMeta.APIVersion }}/namespaces/{{ valueOrDefault .DeploymentMeta.Namespace `default` }}/{{ toLower .TypeMeta.Kind}}s/{{ .DeploymentMeta.Name }}
            {{- end}}
            {{- if .Values.global.meshID }}
            - name: ISTIO_META_MESH_ID
              value: "{{ .Values.global.meshID }}"
            {{- else if (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}
            - name: ISTIO_META_MESH_ID
              value: "{{ (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}"
            {{- end }}
            {{- with (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain)  }}
            - name: TRUST_DOMAIN
              value: "{{ . }}"
            {{- end }}
            {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
            {{with .Values.global.imagePullPolicy }}imagePullPolicy: "{{.}}"{{end}}
            readinessProbe:
              httpGet:
                path: /healthz/ready
                port: 15021
              initialDelaySeconds: {{.Values.global.proxy.readinessInitialDelaySeconds }}
              periodSeconds: {{ .Values.global.proxy.readinessPeriodSeconds }}
              timeoutSeconds: 3
              failureThreshold: {{ .Values.global.proxy.readinessFailureThreshold }}
            volumeMounts:
            - name: workload-socket
              mountPath: /var/run/secrets/workload-spiffe-uds
            {{- if eq .Values.global.caName "GkeWorkloadCertificate" }}
            - name: gke-workload-certificate
              mountPath: /var/run/secrets/workload-spiffe-credentials
              readOnly: true
            {{- else }}
            - name: workload-certs
              mountPath: /var/run/secrets/workload-spiffe-credentials
            {{- end }}
            {{- if eq .Values.global.pilotCertProvider "istiod" }}
            - mountPath: /var/run/secrets/istio
              name: istiod-ca-cert
            {{- end }}
            - mountPath: /var/lib/istio/data
              name: istio-data
            # SDS channel between istioagent and Envoy
            - mountPath: /etc/istio/proxy
              name: istio-envoy
            {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
            - mountPath: /var/run/secrets/tokens
              name: istio-token
            {{- end }}
            {{- if .Values.global.mountMtlsCerts }}
            # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
            - mountPath: /etc/certs/
              name: istio-certs
              readOnly: true
            {{- end }}
            - name: istio-podinfo
              mountPath: /etc/istio/pod
          volumes:
          - emptyDir: {}
            name: workload-socket
          {{- if eq .Values.global.caName "GkeWorkloadCertificate" }}
          - name: gke-workload-certificate
            csi:
              driver: workloadcertificates.security.cloud.google.com
          {{- else}}
          - emptyDir: {}
            name: workload-certs
          {{- end }}
          # SDS channel between istioagent and Envoy
          - emptyDir:
              medium: Memory
            name: istio-envoy
          - name: istio-data
            emptyDir: {}
          - name: istio-podinfo
            downwardAPI:
              items:
                - path: "labels"
                  fieldRef:
                    fieldPath: metadata.labels
                - path: "annotations"
                  fieldRef:
                    fieldPath: metadata.annotations
          {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
          - name: istio-token
            projected:
              sources:
              - serviceAccountToken:
                  path: istio-token
                  expirationSeconds: 43200
                  audience: {{ .Values.global.sds.token.aud }}
          {{- end }}
          {{- if eq .Values.global.pilotCertProvider "istiod" }}
          - name: istiod-ca-cert
            configMap:
              name: istio-ca-root-cert
          {{- end }}
          {{- if .Values.global.mountMtlsCerts }}
          # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
          - name: istio-certs
            secret:
              optional: true
              {{ if eq .Spec.ServiceAccountName "" }}
              secretName: istio.default
              {{ else -}}
              secretName: {{  printf "istio.%s" .Spec.ServiceAccountName }}
              {{  end -}}
          {{- end }}
          {{- if .Values.global.imagePullSecrets }}
          imagePullSecrets:
            {{- range .Values.global.imagePullSecrets }}
            - name: {{ . }}
            {{- end }}
          {{- end }}
          {{- if eq (env "ENABLE_LEGACY_FSGROUP_INJECTION" "true") "true" }}
          securityContext:
            fsGroup: 1337
          {{- end }}
      grpc-simple: |
        metadata:
          sidecar.istio.io/rewriteAppHTTPProbers: "false"
        spec:
          initContainers:
            - name: grpc-bootstrap-init
              image: busybox:1.36.0
              imagePullPolicy: IfNotPresent
              volumeMounts:
                - mountPath: /var/lib/grpc/data/
                  name: grpc-io-proxyless-bootstrap
              env:
                - name: INSTANCE_IP
                  valueFrom:
                    fieldRef:
                      fieldPath: status.podIP
                - name: POD_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.name
                - name: POD_NAMESPACE
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.namespace
                - name: ISTIO_NAMESPACE
                  value: |
                     {{ .Values.global.istioNamespace }}
              command:
                - sh
                - "-c"
                - |-
                  NODE_ID="sidecar~${INSTANCE_IP}~${POD_NAME}.${POD_NAMESPACE}~cluster.local"
                  SERVER_URI="dns:///istiod.${ISTIO_NAMESPACE}.svc:15010"
                  echo '
                  {
                    "xds_servers": [
                      {
                        "server_uri": "'${SERVER_URI}'",
                        "channel_creds": [{"type": "insecure"}],
                        "server_features" : ["xds_v3"]
                      }
                    ],
                    "node": {
                      "id": "'${NODE_ID}'",
                      "metadata": {
                        "GENERATOR": "grpc"
                      }
                    }
                  }' > /var/lib/grpc/data/bootstrap.json
          containers:
          {{- range $index, $container := .Spec.Containers }}
          - name: {{ $container.Name }}
            env:
              - name: GRPC_XDS_BOOTSTRAP
                value: /var/lib/grpc/data/bootstrap.json
              - name: GRPC_GO_LOG_VERBOSITY_LEVEL
                value: "99"
              - name: GRPC_GO_LOG_SEVERITY_LEVEL
                value: info
            volumeMounts:
              - mountPath: /var/lib/grpc/data/
                name: grpc-io-proxyless-bootstrap
          {{- end }}
          volumes:
            - name: grpc-io-proxyless-bootstrap
              emptyDir: {}
      grpc-agent: |
        {{- $containers := list }}
        {{- range $index, $container := .Spec.Containers }}{{ if not (eq $container.Name "istio-proxy") }}{{ $containers = append $containers $container.Name }}{{end}}{{- end}}
        metadata:
          labels:
            service.istio.io/canonical-name: {{ index .ObjectMeta.Labels `service.istio.io/canonical-name` | default (index .ObjectMeta.Labels `app.kubernetes.io/name`) | default (index .ObjectMeta.Labels `app`) | default .DeploymentMeta.Name  | quote }}
            service.istio.io/canonical-revision: {{ index .ObjectMeta.Labels `service.istio.io/canonical-revision` | default (index .ObjectMeta.Labels `app.kubernetes.io/version`) | default (index .ObjectMeta.Labels `version`) | default "latest"  | quote }}
          annotations: {
            {{- if eq (len $containers) 1 }}
            kubectl.kubernetes.io/default-logs-container: "{{ index $containers 0 }}",
            kubectl.kubernetes.io/default-container: "{{ index $containers 0 }}",
            {{ end }}
            sidecar.istio.io/rewriteAppHTTPProbers: "false",
          }
        spec:
          containers:
          {{- range $index, $container := .Spec.Containers  }}
          {{ if not (eq $container.Name "istio-proxy") }}
          - name: {{ $container.Name }}
            env:
            - name: "GRPC_XDS_EXPERIMENTAL_SECURITY_SUPPORT"
              value: "true"
            - name: "GRPC_XDS_BOOTSTRAP"
              value: "/etc/istio/proxy/grpc-bootstrap.json"
            volumeMounts:
            - mountPath: /var/lib/istio/data
              name: istio-data
            # UDS channel between istioagent and gRPC client for XDS/SDS
            - mountPath: /etc/istio/proxy
              name: istio-xds
          {{- end }}
          {{- end }}
          - name: istio-proxy
          {{- if contains "/" (annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image) }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image }}"
          {{- else }}
            image: "{{ .ProxyImage }}"
          {{- end }}
            args:
            - proxy
            - sidecar
            - --domain
            - $(POD_NAMESPACE).svc.{{ .Values.global.proxy.clusterDomain }}
            - --log_output_level={{ annotation .ObjectMeta `sidecar.istio.io/agentLogLevel` .Values.global.logging.level }}
          {{- if .Values.global.sts.servicePort }}
            - --stsPort={{ .Values.global.sts.servicePort }}
          {{- end }}
          {{- if .Values.global.logAsJson }}
            - --log_as_json
          {{- end }}
            env:
            - name: ISTIO_META_GENERATOR
              value: grpc
            - name: OUTPUT_CERTS
              value: /var/lib/istio/data
            {{- if eq (env "PILOT_ENABLE_INBOUND_PASSTHROUGH" "true") "false" }}
            - name: REWRITE_PROBE_LEGACY_LOCALHOST_DESTINATION
              value: "true"
            {{- end }}
            - name: JWT_POLICY
              value: {{ .Values.global.jwtPolicy }}
            - name: PILOT_CERT_PROVIDER
              value: {{ .Values.global.pilotCertProvider }}
            - name: CA_ADDR
            {{- if .Values.global.caAddress }}
              value: {{ .Values.global.caAddress }}
            {{- else }}
              value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.global.istioNamespace }}.svc:15012
            {{- end }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: INSTANCE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: PROXY_CONFIG
              value: |
                     {{ protoToJSON .ProxyConfig }}
            - name: ISTIO_META_POD_PORTS
              value: |-
                [
                {{- $first := true }}
                {{- range $index1, $c := .Spec.Containers }}
                  {{- range $index2, $p := $c.Ports }}
                    {{- if (structToJSON $p) }}
                    {{if not $first}},{{end}}{{ structToJSON $p }}
                    {{- $first = false }}
                    {{- end }}
                  {{- end}}
                {{- end}}
                ]
            - name: ISTIO_META_APP_CONTAINERS
              value: "{{ $containers | join "," }}"
            - name: ISTIO_META_CLUSTER_ID
              value: "{{ valueOrDefault .Values.global.multiCluster.clusterName `Kubernetes` }}"
            - name: ISTIO_META_INTERCEPTION_MODE
              value: "{{ or (index .ObjectMeta.Annotations `sidecar.istio.io/interceptionMode`) .ProxyConfig.InterceptionMode.String }}"
            {{- if .Values.global.network }}
            - name: ISTIO_META_NETWORK
              value: "{{ .Values.global.network }}"
            {{- end }}
            {{- if .DeploymentMeta.Name }}
            - name: ISTIO_META_WORKLOAD_NAME
              value: "{{ .DeploymentMeta.Name }}"
            {{ end }}
            {{- if and .TypeMeta.APIVersion .DeploymentMeta.Name }}
            - name: ISTIO_META_OWNER
              value: kubernetes://apis/{{ .TypeMeta.APIVersion }}/namespaces/{{ valueOrDefault .DeploymentMeta.Namespace `default` }}/{{ toLower .TypeMeta.Kind}}s/{{ .DeploymentMeta.Name }}
            {{- end}}
            {{- if .Values.global.meshID }}
            - name: ISTIO_META_MESH_ID
              value: "{{ .Values.global.meshID }}"
            {{- else if (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}
            - name: ISTIO_META_MESH_ID
              value: "{{ (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}"
            {{- end }}
            {{- with (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain)  }}
            - name: TRUST_DOMAIN
              value: "{{ . }}"
            {{- end }}
            {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
            # grpc uses xds:/// to resolve – no need to resolve VIP
            - name: ISTIO_META_DNS_CAPTURE
              value: "false"
            - name: DISABLE_ENVOY
              value: "true"
            {{with .Values.global.imagePullPolicy }}imagePullPolicy: "{{.}}"{{end}}
            {{ if ne (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) `0` }}
            readinessProbe:
              httpGet:
                path: /healthz/ready
                port: {{ .Values.global.proxy.statusPort }}
              initialDelaySeconds: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/initialDelaySeconds` .Values.global.proxy.readinessInitialDelaySeconds }}
              periodSeconds: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/periodSeconds` .Values.global.proxy.readinessPeriodSeconds }}
              timeoutSeconds: 3
              failureThreshold: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/failureThreshold` .Values.global.proxy.readinessFailureThreshold }}
            {{ end -}}
            resources:
          {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) }}
              requests:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory` }}"
                {{ end }}
            {{- end }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
              limits:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit` }}"
                {{ end }}
            {{- end }}
          {{- else }}
            {{- if .Values.global.proxy.resources }}
              {{ toYaml .Values.global.proxy.resources | indent 6 }}
            {{- end }}
          {{- end }}
            volumeMounts:
            - name: workload-socket
              mountPath: /var/run/secrets/workload-spiffe-uds
            - name: workload-certs
              mountPath: /var/run/secrets/workload-spiffe-credentials
            {{- if eq .Values.global.pilotCertProvider "istiod" }}
            - mountPath: /var/run/secrets/istio
              name: istiod-ca-cert
            {{- end }}
            - mountPath: /var/lib/istio/data
              name: istio-data
            # UDS channel between istioagent and gRPC client for XDS/SDS
            - mountPath: /etc/istio/proxy
              name: istio-xds
            {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
            - mountPath: /var/run/secrets/tokens
              name: istio-token
            {{- end }}
            - name: istio-podinfo
              mountPath: /etc/istio/pod
            {{- if isset .ObjectMeta.Annotations `sidecar.istio.io/userVolumeMount` }}
            {{ range $index, $value := fromJSON (index .ObjectMeta.Annotations `sidecar.istio.io/userVolumeMount`) }}
            - name: "{{  $index }}"
            {{ toYaml $value | indent 6 }}
            {{ end }}
            {{- end }}
          volumes:
          - emptyDir: {}
            name: workload-socket
          - emptyDir: {}
            name: workload-certs
          # UDS channel between istioagent and gRPC client for XDS/SDS
          - emptyDir:
              medium: Memory
            name: istio-xds
          - name: istio-data
            emptyDir: {}
          - name: istio-podinfo
            downwardAPI:
              items:
                - path: "labels"
                  fieldRef:
                    fieldPath: metadata.labels
                - path: "annotations"
                  fieldRef:
                    fieldPath: metadata.annotations
        {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
          - name: istio-token
            projected:
              sources:
              - serviceAccountToken:
                  path: istio-token
                  expirationSeconds: 43200
                  audience: {{ .Values.global.sds.token.aud }}
        {{- end }}
          {{- if eq .Values.global.pilotCertProvider "istiod" }}
          - name: istiod-ca-cert
            configMap:
              name: istio-ca-root-cert
          {{- end }}
          {{- if isset .ObjectMeta.Annotations `sidecar.istio.io/userVolume` }}
          {{range $index, $value := fromJSON (index .ObjectMeta.Annotations `sidecar.istio.io/userVolume`) }}
          - name: "{{ $index }}"
          {{ toYaml $value | indent 4 }}
          {{ end }}
          {{ end }}
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: istio-sidecar-injector
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    app: sidecar-injector
    release: istio
webhooks:
- name: rev.namespace.sidecar-injector.istio.io
  clientConfig:
    service:
      name: istiod
      namespace: istio-system
      path: "/inject"
      port: 443
  sideEffects: None
  rules:
  - operations: [ "CREATE" ]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  failurePolicy: Fail
  admissionReviewVersions: ["v1beta1", "v1"]
  namespaceSelector:
    matchExpressions:
    - key: istio.io/rev
      operator: In
      values:
      - "default"
    - key: istio-injection
      operator: DoesNotExist
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: NotIn
      values:
      - "false"
- name: rev.object.sidecar-injector.istio.io
  clientConfig:
    service:
      name: istiod
      namespace: istio-system
      path: "/inject"
      port: 443
  sideEffects: None
  rules:
  - operations: [ "CREATE" ]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  failurePolicy: Fail
  admissionReviewVersions: ["v1beta1", "v1"]
  namespaceSelector:
    matchExpressions:
    - key: istio.io/rev
      operator: DoesNotExist
    - key: istio-injection
      operator: DoesNotExist
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: NotIn
      values:
      - "false"
    - key: istio.io/rev
      operator: In
      values:
      - "default"
- name: namespace.sidecar-injector.istio.io
  clientConfig:
    service:
      name: istiod
      namespace: istio-system
      path: "/inject"
      port: 443
  sideEffects: None
  rules:
  - operations: [ "CREATE" ]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  failurePolicy: Fail
  admissionReviewVersions: ["v1beta1", "v1"]
  namespaceSelector:
    matchExpressions:
    - key: istio-injection
      operator: In
      values:
      - enabled
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: NotIn
      values:
      - "false"
- name: object.sidecar-injector.istio.io
  clientConfig:
    service:
      name: istiod
      namespace: istio-system
      path: "/inject"
      port: 443
  sideEffects: None
  rules:
  - operations: [ "CREATE" ]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  failurePolicy: Fail
  admissionReviewVersions: ["v1beta1", "v1"]
  namespaceSelector:
    matchExpressions:
    - key: istio-injection
      operator: DoesNotExist
    - key: istio.io/rev
      operator: DoesNotExist
  objectSelector:
    matchExpressions:
    - key: sidecar.istio.io/inject
      operator: In
      values:
      - "true"
    - key: istio.io/rev
      operator: DoesNotExist
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: istio-ingressgateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: ingressgateway
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
spec:
  selector:
    matchLabels:
      app: istio-ingressgateway
      istio: ingressgateway
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        app: istio-ingressgateway
        istio: ingressgateway
        heritage: Tiller
        release: istio
        chart: gateways
        service.istio.io/canonical-name: istio-ingressgateway
        service.istio.io/canonical-revision: latest
        istio.io/rev: default
        install.operator.istio.io/owning-resource: unknown
        operator.istio.io/component: "IngressGateways"
        sidecar.istio.io/inject: "false"
      annotations:
        prometheus.io/port: "15020"
        prometheus.io/scrape: "true"
        prometheus.io/path: "/stats/prometheus"
        sidecar.istio.io/inject: "false"
    spec:
      securityContext:
        runAsUser: 1337
        runAsGroup: 1337
        runAsNonRoot: true
        fsGroup: 1337
      serviceAccountName: istio-ingressgateway-service-account
      containers:
        - name: istio-proxy
          image: "docker.io/istio/proxyv2:1.14.1"
          ports:
            - containerPort: 15021
              protocol: TCP
            - containerPort: 8080
              protocol: TCP
            - containerPort: 8443
              protocol: TCP
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
          args:
          - proxy
          - router
          - --domain
          - $(POD_NAMESPACE).svc.cluster.local
          - --proxyLogLevel=warning
          - --proxyComponentLogLevel=misc:error
          - --log_output_level=default:info
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
          readinessProbe:
            failureThreshold: 30
            httpGet:
              path: /healthz/ready
              port: 15021
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 1
          resources:
#            limits:
#              cpu: 2000m
#              memory: 1024Mi
            requests:
              cpu: 100m
              memory: 128Mi
          env:
          - name: JWT_POLICY
            value: first-party-jwt
          - name: PILOT_CERT_PROVIDER
            value: istiod
          - name: CA_ADDR
            value: istiod.istio-system.svc:15012
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: INSTANCE_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.podIP
          - name: HOST_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.hostIP
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                fieldPath: spec.serviceAccountName
          - name: ISTIO_META_WORKLOAD_NAME
            value: istio-ingressgateway
          - name: ISTIO_META_OWNER
            value: kubernetes://apis/apps/v1/namespaces/istio-system/deployments/istio-ingressgateway
          - name: ISTIO_META_MESH_ID
            value: "cluster.local"
          - name: TRUST_DOMAIN
            value: "cluster.local"
          - name: ISTIO_META_UNPRIVILEGED_POD
            value: "true"
          - name: ISTIO_META_CLUSTER_ID
            value: "Kubernetes"
          volumeMounts:
          - name: workload-socket
            mountPath: /var/run/secrets/workload-spiffe-uds
          - name: workload-certs
            mountPath: /var/run/secrets/workload-spiffe-credentials
          - name: istio-envoy
            mountPath: /etc/istio/proxy
          - name: config-volume
            mountPath: /etc/istio/config
          - mountPath: /var/run/secrets/istio
            name: istiod-ca-cert
          - mountPath: /var/lib/istio/data
            name: istio-data
          - name: podinfo
            mountPath: /etc/istio/pod
          - name: ingressgateway-certs
            mountPath: "/etc/istio/ingressgateway-certs"
            readOnly: true
          - name: ingressgateway-ca-certs
            mountPath: "/etc/istio/ingressgateway-ca-certs"
            readOnly: true
      volumes:
      - emptyDir: {}
        name: workload-socket
      - emptyDir: {}
        name: workload-certs
      - name: istiod-ca-cert
        configMap:
          name: istio-ca-root-cert
      - name: podinfo
        downwardAPI:
          items:
            - path: "labels"
              fieldRef:
                fieldPath: metadata.labels
            - path: "annotations"
              fieldRef:
                fieldPath: metadata.annotations
      - name: istio-envoy
        emptyDir: {}
      - name: istio-data
        emptyDir: {}
      - name: config-volume
        configMap:
          name: istio
          optional: true
      - name: ingressgateway-certs
        secret:
          secretName: "istio-ingressgateway-certs"
          optional: true
      - name: ingressgateway-ca-certs
        secret:
          secretName: "istio-ingressgateway-ca-certs"
          optional: true
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: kubernetes.io/arch
                  operator: In
                  values:
                  - "amd64"
                  - "arm64"
                  - "ppc64le"
                  - "s390x"
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 2
              preference:
                matchExpressions:
                - key: kubernetes.io/arch
                  operator: In
                  values:
                  - "amd64"
            - weight: 2
              preference:
                matchExpressions:
                - key: kubernetes.io/arch
                  operator: In
                  values:
                  - "arm64"
            - weight: 2
              preference:
                matchExpressions:
                - key: kubernetes.io/arch
                  operator: In
                  values:
                  - "ppc64le"
            - weight: 2
              preference:
                matchExpressions:
                - key: kubernetes.io/arch
                  operator: In
                  values:
                  - "s390x"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: istiod
  namespace: istio-system
  labels:
    app: istiod
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    istio: pilot
    release: istio
spec:
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 25%
  selector:
    matchLabels:
      istio: pilot
  template:
    metadata:
      labels:
        app: istiod
        istio.io/rev: default
        install.operator.istio.io/owning-resource: unknown
        sidecar.istio.io/inject: "false"
        operator.istio.io/component: "Pilot"
        istio: pilot
      annotations:
        prometheus.io/port: "15014"
        prometheus.io/scrape: "true"
        sidecar.istio.io/inject: "false"
    spec:
      serviceAccountName: istiod
      securityContext:
        fsGroup: 1337
      containers:
        - name: discovery
          image: "docker.io/istio/pilot:1.14.1"
          args:
          - "discovery"
          - --monitoringAddr=:15014
          - --log_output_level=default:info
          - --domain
          - cluster.local
          - --keepaliveMaxServerConnectionAge
          - "30m"
          ports:
          - containerPort: 8080
            protocol: TCP
          - containerPort: 15010
            protocol: TCP
          - containerPort: 15017
            protocol: TCP
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 3
            timeoutSeconds: 5
          env:
          - name: REVISION
            value: "default"
          - name: JWT_POLICY
            value: first-party-jwt
          - name: PILOT_CERT_PROVIDER
            value: istiod
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.serviceAccountName
          - name: KUBECONFIG
            value: /var/run/secrets/remote/config
          - name: ENABLE_LEGACY_FSGROUP_INJECTION
            value: "false"
          - name: PILOT_TRACE_SAMPLING
            value: "1"
          - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_OUTBOUND
            value: "true"
          - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_INBOUND
            value: "true"
          - name: ISTIOD_ADDR
            value: istiod.istio-system.svc:15012
          - name: PILOT_ENABLE_ANALYSIS
            value: "false"
          - name: CLUSTER_ID
            value: "Kubernetes"
          resources:
            requests:
              cpu: 500m
              memory: 2048Mi
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsUser: 1337
            runAsGroup: 1337
            runAsNonRoot: true
            capabilities:
              drop:
              - ALL
          volumeMounts:
          - name: local-certs
            mountPath: /var/run/secrets/istio-dns
          - name: cacerts
            mountPath: /etc/cacerts
            readOnly: true
          - name: istio-kubeconfig
            mountPath: /var/run/secrets/remote
            readOnly: true
      volumes:
      # Technically not needed on this pod - but it helps debugging/testing SDS
      # Should be removed after everything works.
      - emptyDir:
          medium: Memory
        name: local-certs
      # Optional: user-generated root
      - name: cacerts
        secret:
          secretName: cacerts
          optional: true
      - name: istio-kubeconfig
        secret:
          secretName: istio-kubeconfig
          optional: true
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: istio-ingressgateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: ingressgateway
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: istio-ingressgateway
      istio: ingressgateway
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: istiod
  namespace: istio-system
  labels:
    app: istiod
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    release: istio
    istio: pilot
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: istiod
      istio: pilot
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istio-ingressgateway-sds
  namespace: istio-system
  labels:
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod
  namespace: istio-system
  labels:
    app: istiod
    release: istio
rules:
- apiGroups: ["networking.istio.io"]
  verbs: ["create"]
  resources: ["gateways"]

- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod-istio-system
  namespace: istio-system
  labels:
    app: istiod
    release: istio
rules:
- apiGroups: ["networking.istio.io"]
  verbs: ["create"]
  resources: ["gateways"]

- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istio-ingressgateway-sds
  namespace: istio-system
  labels:
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istio-ingressgateway-sds
subjects:
- kind: ServiceAccount
  name: istio-ingressgateway-service-account
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istiod
  namespace: istio-system
  labels:
    app: istiod
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istiod
subjects:
  - kind: ServiceAccount
    name: istiod
    namespace: istio-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istiod-istio-system
  namespace: istio-system
  labels:
    app: istiod
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istiod-istio-system
subjects:
  - kind: ServiceAccount
    name: istiod-service-account
    namespace: istio-system
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: istio-ingressgateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: ingressgateway
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
spec:
  maxReplicas: 5
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istio-ingressgateway
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: istiod
  namespace: istio-system
  labels:
    app: istiod
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
spec:
  maxReplicas: 5
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istiod
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: v1
kind: Service
metadata:
  name: istio-ingressgateway
  namespace: istio-system
  annotations:
  labels:
    app: istio-ingressgateway
    istio: ingressgateway
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
spec:
  selector:
    app: istio-ingressgateway
    istio: ingressgateway
  ports:
    - name: status-port
      port: 15021
      protocol: TCP
      targetPort: 15021
    - name: http2
      port: 80
      protocol: TCP
      targetPort: 8888
    - name: http3
      port: 8080
      protocol: TCP
      targetPort: 8889
#    - name: https
#      port: 443
#      protocol: TCP
#      targetPort: 8443
---
apiVersion: v1
kind: Service
metadata:
  name: istiod
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    app: istiod
    istio: pilot
    release: istio
spec:
  ports:
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
  selector:
    app: istiod
    # Label used by the 'default' service. For versioned deployments we match with app and version.
    # This avoids default deployment picking the canary
    istio: pilot
---
