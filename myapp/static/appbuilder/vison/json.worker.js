/*! For license information please see json.worker.js.LICENSE.txt */
!function(){"use strict";var e={};function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e){var t=function(e,t){if("object"!==n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!==n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===n(t)?t:String(t)}function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,r(i.key),i)}}function o(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}e.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}();var a=new(function(){function e(){t(this,e),this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout((function(){if(e.stack)throw new Error(e.message+"\n\n"+e.stack);throw e}),0)}}return o(e,[{key:"emit",value:function(e){this.listeners.forEach((function(t){t(e)}))}},{key:"onUnexpectedError",value:function(e){this.unexpectedErrorHandler(e),this.emit(e)}},{key:"onUnexpectedExternalError",value:function(e){this.unexpectedErrorHandler(e)}}]),e}());function s(e){l(e)||a.onUnexpectedError(e)}function u(e){return e instanceof Error?{$isError:!0,name:e.name,message:e.message,stack:e.stacktrace||e.stack}:e}var c="Canceled";function l(e){return e instanceof Error&&e.name===c&&e.message===c}var f;function h(e,t){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},h(e,t)}function d(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&h(e,t)}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function g(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function p(e,t){if(t&&("object"===n(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function v(e){var t=g();return function(){var n,r=m(e);if(t){var i=m(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return p(this,n)}}function y(e,t,n){return y=g()?Reflect.construct.bind():function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&h(i,n.prototype),i},y.apply(null,arguments)}function b(e){var t="function"===typeof Map?new Map:void 0;return b=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"===typeof e}}(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return y(e,arguments,m(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),h(n,e)},b(e)}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function C(e,t){if(e){if("string"===typeof e)return _(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_(e,t):void 0}}function S(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=C(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function k(e){var t,n=this,r=!1;return function(){return r?t:(r=!0,t=e.apply(n,arguments))}}function x(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(){A=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var i=t&&t.prototype instanceof y?t:y,a=Object.create(i.prototype),s=new I(r||[]);return o(a,"_invoke",{value:N(e,n,s)}),a}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var d="suspendedStart",m="suspendedYield",g="executing",p="completed",v={};function y(){}function b(){}function _(){}var C={};l(C,s,(function(){return this}));var S=Object.getPrototypeOf,k=S&&S(S(P([])));k&&k!==r&&i.call(k,s)&&(C=k);var x=_.prototype=y.prototype=Object.create(C);function E(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(o,a,s,u){var c=h(e[o],e,a);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==n(f)&&i.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,s,u)}),(function(e){r("throw",e,s,u)})):t.resolve(f).then((function(e){l.value=e,s(l)}),(function(e){return r("throw",e,s,u)}))}u(c.arg)}var a;o(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return a=a?a.then(i,i):i()}})}function N(t,n,r){var i=d;return function(o,a){if(i===g)throw new Error("Generator is already running");if(i===p){if("throw"===o)throw a;return{value:e,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var u=L(s,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var c=h(t,n,r);if("normal"===c.type){if(i=r.done?p:m,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=p,r.method="throw",r.arg=c.arg)}}}function L(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=h(i,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function P(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(i.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(n(t)+" is not iterable")}return b.prototype=_,o(x,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:b,configurable:!0}),b.displayName=l(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,l(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},E(w.prototype),l(w.prototype,u,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new w(f(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(x),l(x,c,"Generator"),l(x,s,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,i){return s.type="throw",s.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var u=i.call(a,"catchLoc"),c=i.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}!function(e){var t=A().mark(s),n=A().mark(u),r=A().mark(c),i=A().mark(l),o=A().mark(f);e.is=function(e){return e&&"object"===typeof e&&"function"===typeof e[Symbol.iterator]};var a=Object.freeze([]);function s(e){return A().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e;case 2:case"end":return t.stop()}}),t)}function u(e,t){var r,i,o;return A().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:r=S(e),n.prev=1,r.s();case 3:if((i=r.n()).done){n.next=10;break}if(o=i.value,!t(o)){n.next=8;break}return n.next=8,o;case 8:n.next=3;break;case 10:n.next=15;break;case 12:n.prev=12,n.t0=n.catch(1),r.e(n.t0);case 15:return n.prev=15,r.f(),n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[1,12,15,18]])}function c(e,t){var n,i,o,a;return A().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=0,i=S(e),r.prev=2,i.s();case 4:if((o=i.n()).done){r.next=10;break}return a=o.value,r.next=8,t(a,n++);case 8:r.next=4;break;case 10:r.next=15;break;case 12:r.prev=12,r.t0=r.catch(2),i.e(r.t0);case 15:return r.prev=15,i.f(),r.finish(15);case 18:case"end":return r.stop()}}),r,null,[[2,12,15,18]])}function l(){var e,t,n,r,o,a,s,u,c,l=arguments;return A().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:for(e=l.length,t=new Array(e),n=0;n<e;n++)t[n]=l[n];r=0,o=t;case 2:if(!(r<o.length)){i.next=24;break}a=o[r],s=S(a),i.prev=5,s.s();case 7:if((u=s.n()).done){i.next=13;break}return c=u.value,i.next=11,c;case 11:i.next=7;break;case 13:i.next=18;break;case 15:i.prev=15,i.t0=i.catch(5),s.e(i.t0);case 18:return i.prev=18,s.f(),i.finish(18);case 21:r++,i.next=2;break;case 24:case"end":return i.stop()}}),i,null,[[5,15,18,21]])}function f(e){var t,n,r,i,a,s;return A().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:t=S(e),o.prev=1,t.s();case 3:if((n=t.n()).done){o.next=24;break}r=n.value,i=S(r),o.prev=6,i.s();case 8:if((a=i.n()).done){o.next=14;break}return s=a.value,o.next=12,s;case 12:o.next=8;break;case 14:o.next=19;break;case 16:o.prev=16,o.t0=o.catch(6),i.e(o.t0);case 19:return o.prev=19,i.f(),o.finish(19);case 22:o.next=3;break;case 24:o.next=29;break;case 26:o.prev=26,o.t1=o.catch(1),t.e(o.t1);case 29:return o.prev=29,t.f(),o.finish(29);case 32:case"end":return o.stop()}}),o,null,[[1,26,29,32],[6,16,19,22]])}e.empty=function(){return a},e.single=s,e.from=function(e){return e||a},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){var n,r=S(e);try{for(r.s();!(n=r.n()).done;){if(t(n.value))return!0}}catch(i){r.e(i)}finally{r.f()}return!1},e.find=function(e,t){var n,r=S(e);try{for(r.s();!(n=r.n()).done;){var i=n.value;if(t(i))return i}}catch(o){r.e(o)}finally{r.f()}},e.filter=u,e.map=c,e.concat=l,e.concatNested=f,e.reduce=function(e,t,n){var r,i=n,o=S(e);try{for(o.s();!(r=o.n()).done;){i=t(i,r.value)}}catch(a){o.e(a)}finally{o.f()}return i},e.slice=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length;return A().mark((function r(){return A().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);case 2:if(!(t<n)){r.next=8;break}return r.next=5,e[t];case 5:t++,r.next=2;break;case 8:case"end":return r.stop()}}),r)}))()},e.consume=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.POSITIVE_INFINITY,r=[];if(0===n)return[r,t];for(var i=t[Symbol.iterator](),o=0;o<n;o++){var a=i.next();if(a.done)return[r,e.empty()];r.push(a.value)}return[r,x({},Symbol.iterator,(function(){return i}))]},e.equals=function(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e,t){return e===t},r=e[Symbol.iterator](),i=t[Symbol.iterator]();;){var o=r.next(),a=i.next();if(o.done!==a.done)return!1;if(o.done)return!0;if(!n(o.value,a.value))return!1}}}(f||(f={}));var E=null;function w(e){return null===E||void 0===E||E.trackDisposable(e),e}function N(e){null===E||void 0===E||E.markAsDisposed(e)}function L(e,t){null===E||void 0===E||E.setParent(e,t)}var T=function(e){d(r,e);var n=v(r);function r(e){var i;return t(this,r),(i=n.call(this,"Encountered errors while disposing of store. Errors: [".concat(e.join(", "),"]"))).errors=e,i}return o(r)}(b(Error));function O(e){if(f.is(e)){var t,n=[],r=S(e);try{for(r.s();!(t=r.n()).done;){var i=t.value;if(i)try{i.dispose()}catch(o){n.push(o)}}}catch(a){r.e(a)}finally{r.f()}if(1===n.length)throw n[0];if(n.length>1)throw new T(n);return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function I(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=P((function(){return O(t)}));return function(e,t){if(E){var n,r=S(e);try{for(r.s();!(n=r.n()).done;){var i=n.value;E.setParent(i,t)}}catch(o){r.e(o)}finally{r.f()}}}(t,r),r}function P(e){var t=w({dispose:k((function(){N(t),e()}))});return t}var M=function(){function e(){t(this,e),this._toDispose=new Set,this._isDisposed=!1,w(this)}return o(e,[{key:"dispose",value:function(){this._isDisposed||(N(this),this._isDisposed=!0,this.clear())}},{key:"clear",value:function(){try{O(this._toDispose.values())}finally{this._toDispose.clear()}}},{key:"add",value:function(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return L(t,this),this._isDisposed?e.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t}}]),e}();M.DISABLE_DISPOSED_WARNING=!1;var R=function(){function e(){t(this,e),this._store=new M,w(this),L(this._store,this)}return o(e,[{key:"dispose",value:function(){N(this),this._store.dispose()}},{key:"_register",value:function(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}]),e}();R.None=Object.freeze({dispose:function(){}});var D,j="en",F=!1,U=!1,V=!1,W=void 0,q="object"===typeof self?self:"object"===typeof e.g?e.g:{},B=void 0;"undefined"!==typeof q.vscode&&"undefined"!==typeof q.vscode.process?B=q.vscode.process:"undefined"!==typeof process&&(B=process);var K="string"===typeof(null===(D=null===B||void 0===B?void 0:B.versions)||void 0===D?void 0:D.electron)&&"renderer"===B.type;if("object"!==typeof navigator||K)if("object"===typeof B){F="win32"===B.platform,U="darwin"===B.platform,(V="linux"===B.platform)&&!!B.env.SNAP&&!!B.env.SNAP_REVISION,j,j;var $=B.env.VSCODE_NLS_CONFIG;if($)try{var H=JSON.parse($),Y=H.availableLanguages["*"];H.locale,Y||j,H._translationsConfigFile}catch($o){}!0}else console.error("Unable to resolve platform.");else F=(W=navigator.userAgent).indexOf("Windows")>=0,U=W.indexOf("Macintosh")>=0,(W.indexOf("Macintosh")>=0||W.indexOf("iPad")>=0||W.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,V=W.indexOf("Linux")>=0,!0,navigator.language;var z=F,G=U,J=function(){if(q.setImmediate)return q.setImmediate.bind(q);if("function"===typeof q.postMessage&&!q.importScripts){var e=[];q.addEventListener("message",(function(t){if(t.data&&t.data.vscodeSetImmediateId)for(var n=0,r=e.length;n<r;n++){var i=e[n];if(i.id===t.data.vscodeSetImmediateId)return e.splice(n,1),void i.callback()}}));var t=0;return function(n){var r=++t;e.push({id:r,callback:n}),q.postMessage({vscodeSetImmediateId:r},"*")}}if("function"===typeof(null===B||void 0===B?void 0:B.nextTick))return B.nextTick.bind(B);var n=Promise.resolve();return function(e){return n.then(e)}}();function Q(e){var t,n=[],r=S(function(e){for(var t=[],n=Object.getPrototypeOf(e);Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}(e));try{for(r.s();!(t=r.n()).done;){var i=t.value;"function"===typeof e[i]&&n.push(i)}}catch(o){r.e(o)}finally{r.f()}return n}function X(e,t){var n,r=function(e){return function(){var n=Array.prototype.slice.call(arguments,0);return t(e,n)}},i={},o=S(e);try{for(o.s();!(n=o.n()).done;){var a=n.value;i[a]=r(a)}}catch(s){o.e(s)}finally{o.f()}return i}var Z="$initialize";var ee=function(){function e(n){t(this,e),this._workerId=-1,this._handler=n,this._lastSentReq=0,this._pendingReplies=Object.create(null)}return o(e,[{key:"setWorkerId",value:function(e){this._workerId=e}},{key:"sendMessage",value:function(e,t){var n=this,r=String(++this._lastSentReq);return new Promise((function(i,o){n._pendingReplies[r]={resolve:i,reject:o},n._send({vsWorker:n._workerId,req:r,method:e,args:t})}))}},{key:"handleMessage",value:function(e){e&&e.vsWorker&&(-1!==this._workerId&&e.vsWorker!==this._workerId||this._handleMessage(e))}},{key:"_handleMessage",value:function(e){var t=this;if(e.seq){var n=e;if(!this._pendingReplies[n.seq])return void console.warn("Got reply to unknown seq");var r=this._pendingReplies[n.seq];if(delete this._pendingReplies[n.seq],n.err){var i=n.err;return n.err.$isError&&((i=new Error).name=n.err.name,i.message=n.err.message,i.stack=n.err.stack),void r.reject(i)}r.resolve(n.res)}else{var o=e,a=o.req;this._handler.handleMessage(o.method,o.args).then((function(e){t._send({vsWorker:t._workerId,seq:a,res:e,err:void 0})}),(function(e){e.detail instanceof Error&&(e.detail=u(e.detail)),t._send({vsWorker:t._workerId,seq:a,res:void 0,err:u(e)})}))}}},{key:"_send",value:function(e){var t=[];if(e.req)for(var n=e,r=0;r<n.args.length;r++)n.args[r]instanceof ArrayBuffer&&t.push(n.args[r]);else{var i=e;i.res instanceof ArrayBuffer&&t.push(i.res)}this._handler.sendMessage(e,t)}}]),e}(),te=function(){function e(n,r){var i=this;t(this,e),this._requestHandlerFactory=r,this._requestHandler=null,this._protocol=new ee({sendMessage:function(e,t){n(e,t)},handleMessage:function(e,t){return i._handleMessage(e,t)}})}return o(e,[{key:"onmessage",value:function(e){this._protocol.handleMessage(e)}},{key:"_handleMessage",value:function(e,t){if(e===Z)return this.initialize(t[0],t[1],t[2],t[3]);if(!this._requestHandler||"function"!==typeof this._requestHandler[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._requestHandler[e].apply(this._requestHandler,t))}catch($o){return Promise.reject($o)}}},{key:"initialize",value:function(e,t,n,r){var i=this;this._protocol.setWorkerId(e);var o=X(r,(function(e,t){return i._protocol.sendMessage(e,t)}));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(o),Promise.resolve(Q(this._requestHandler))):(t&&("undefined"!==typeof t.baseUrl&&delete t.baseUrl,"undefined"!==typeof t.paths&&"undefined"!==typeof t.paths.vs&&delete t.paths.vs,void 0!==typeof t.trustedTypesPolicy&&delete t.trustedTypesPolicy,t.catchError=!0,self.require.config(t)),new Promise((function(e,t){self.require([n],(function(n){i._requestHandler=n.create(o),i._requestHandler?e(Q(i._requestHandler)):t(new Error("No RequestHandler!"))}),t)})))}}]),e}();function ne(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],u=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){c=!0,i=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return s}}(e,t)||C(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var re=function(){function e(n,r,i,o){t(this,e),this.originalStart=n,this.originalLength=r,this.modifiedStart=i,this.modifiedLength=o}return o(e,[{key:"getOriginalEnd",value:function(){return this.originalStart+this.originalLength}},{key:"getModifiedEnd",value:function(){return this.modifiedStart+this.modifiedLength}}]),e}();function ie(e){return 55296<=e&&e<=56319}function oe(e){return 56320<=e&&e<=57343}function ae(e,t){return t-56320+(e-55296<<10)+65536}String.fromCharCode(65279);var se=function(){function e(){t(this,e),this._data=JSON.parse("[0,0,0,51592,51592,11,44424,44424,11,72251,72254,5,7150,7150,7,48008,48008,11,55176,55176,11,128420,128420,14,3276,3277,5,9979,9980,14,46216,46216,11,49800,49800,11,53384,53384,11,70726,70726,5,122915,122916,5,129320,129327,14,2558,2558,5,5906,5908,5,9762,9763,14,43360,43388,8,45320,45320,11,47112,47112,11,48904,48904,11,50696,50696,11,52488,52488,11,54280,54280,11,70082,70083,1,71350,71350,7,73111,73111,5,127892,127893,14,128726,128727,14,129473,129474,14,2027,2035,5,2901,2902,5,3784,3789,5,6754,6754,5,8418,8420,5,9877,9877,14,11088,11088,14,44008,44008,5,44872,44872,11,45768,45768,11,46664,46664,11,47560,47560,11,48456,48456,11,49352,49352,11,50248,50248,11,51144,51144,11,52040,52040,11,52936,52936,11,53832,53832,11,54728,54728,11,69811,69814,5,70459,70460,5,71096,71099,7,71998,71998,5,72874,72880,5,119149,119149,7,127374,127374,14,128335,128335,14,128482,128482,14,128765,128767,14,129399,129400,14,129680,129685,14,1476,1477,5,2377,2380,7,2759,2760,5,3137,3140,7,3458,3459,7,4153,4154,5,6432,6434,5,6978,6978,5,7675,7679,5,9723,9726,14,9823,9823,14,9919,9923,14,10035,10036,14,42736,42737,5,43596,43596,5,44200,44200,11,44648,44648,11,45096,45096,11,45544,45544,11,45992,45992,11,46440,46440,11,46888,46888,11,47336,47336,11,47784,47784,11,48232,48232,11,48680,48680,11,49128,49128,11,49576,49576,11,50024,50024,11,50472,50472,11,50920,50920,11,51368,51368,11,51816,51816,11,52264,52264,11,52712,52712,11,53160,53160,11,53608,53608,11,54056,54056,11,54504,54504,11,54952,54952,11,68108,68111,5,69933,69940,5,70197,70197,7,70498,70499,7,70845,70845,5,71229,71229,5,71727,71735,5,72154,72155,5,72344,72345,5,73023,73029,5,94095,94098,5,121403,121452,5,126981,127182,14,127538,127546,14,127990,127990,14,128391,128391,14,128445,128449,14,128500,128505,14,128752,128752,14,129160,129167,14,129356,129356,14,129432,129442,14,129648,129651,14,129751,131069,14,173,173,4,1757,1757,1,2274,2274,1,2494,2494,5,2641,2641,5,2876,2876,5,3014,3016,7,3262,3262,7,3393,3396,5,3570,3571,7,3968,3972,5,4228,4228,7,6086,6086,5,6679,6680,5,6912,6915,5,7080,7081,5,7380,7392,5,8252,8252,14,9096,9096,14,9748,9749,14,9784,9786,14,9833,9850,14,9890,9894,14,9938,9938,14,9999,9999,14,10085,10087,14,12349,12349,14,43136,43137,7,43454,43456,7,43755,43755,7,44088,44088,11,44312,44312,11,44536,44536,11,44760,44760,11,44984,44984,11,45208,45208,11,45432,45432,11,45656,45656,11,45880,45880,11,46104,46104,11,46328,46328,11,46552,46552,11,46776,46776,11,47000,47000,11,47224,47224,11,47448,47448,11,47672,47672,11,47896,47896,11,48120,48120,11,48344,48344,11,48568,48568,11,48792,48792,11,49016,49016,11,49240,49240,11,49464,49464,11,49688,49688,11,49912,49912,11,50136,50136,11,50360,50360,11,50584,50584,11,50808,50808,11,51032,51032,11,51256,51256,11,51480,51480,11,51704,51704,11,51928,51928,11,52152,52152,11,52376,52376,11,52600,52600,11,52824,52824,11,53048,53048,11,53272,53272,11,53496,53496,11,53720,53720,11,53944,53944,11,54168,54168,11,54392,54392,11,54616,54616,11,54840,54840,11,55064,55064,11,65438,65439,5,69633,69633,5,69837,69837,1,70018,70018,7,70188,70190,7,70368,70370,7,70465,70468,7,70712,70719,5,70835,70840,5,70850,70851,5,71132,71133,5,71340,71340,7,71458,71461,5,71985,71989,7,72002,72002,7,72193,72202,5,72281,72283,5,72766,72766,7,72885,72886,5,73104,73105,5,92912,92916,5,113824,113827,4,119173,119179,5,121505,121519,5,125136,125142,5,127279,127279,14,127489,127490,14,127570,127743,14,127900,127901,14,128254,128254,14,128369,128370,14,128400,128400,14,128425,128432,14,128468,128475,14,128489,128494,14,128715,128720,14,128745,128745,14,128759,128760,14,129004,129023,14,129296,129304,14,129340,129342,14,129388,129392,14,129404,129407,14,129454,129455,14,129485,129487,14,129659,129663,14,129719,129727,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2363,2363,7,2402,2403,5,2507,2508,7,2622,2624,7,2691,2691,7,2786,2787,5,2881,2884,5,3006,3006,5,3072,3072,5,3170,3171,5,3267,3268,7,3330,3331,7,3406,3406,1,3538,3540,5,3655,3662,5,3897,3897,5,4038,4038,5,4184,4185,5,4352,4447,8,6068,6069,5,6155,6157,5,6448,6449,7,6742,6742,5,6783,6783,5,6966,6970,5,7042,7042,7,7143,7143,7,7212,7219,5,7412,7412,5,8206,8207,4,8294,8303,4,8596,8601,14,9410,9410,14,9742,9742,14,9757,9757,14,9770,9770,14,9794,9794,14,9828,9828,14,9855,9855,14,9882,9882,14,9900,9903,14,9929,9933,14,9963,9967,14,9987,9988,14,10006,10006,14,10062,10062,14,10175,10175,14,11744,11775,5,42607,42607,5,43043,43044,7,43263,43263,5,43444,43445,7,43569,43570,5,43698,43700,5,43766,43766,5,44032,44032,11,44144,44144,11,44256,44256,11,44368,44368,11,44480,44480,11,44592,44592,11,44704,44704,11,44816,44816,11,44928,44928,11,45040,45040,11,45152,45152,11,45264,45264,11,45376,45376,11,45488,45488,11,45600,45600,11,45712,45712,11,45824,45824,11,45936,45936,11,46048,46048,11,46160,46160,11,46272,46272,11,46384,46384,11,46496,46496,11,46608,46608,11,46720,46720,11,46832,46832,11,46944,46944,11,47056,47056,11,47168,47168,11,47280,47280,11,47392,47392,11,47504,47504,11,47616,47616,11,47728,47728,11,47840,47840,11,47952,47952,11,48064,48064,11,48176,48176,11,48288,48288,11,48400,48400,11,48512,48512,11,48624,48624,11,48736,48736,11,48848,48848,11,48960,48960,11,49072,49072,11,49184,49184,11,49296,49296,11,49408,49408,11,49520,49520,11,49632,49632,11,49744,49744,11,49856,49856,11,49968,49968,11,50080,50080,11,50192,50192,11,50304,50304,11,50416,50416,11,50528,50528,11,50640,50640,11,50752,50752,11,50864,50864,11,50976,50976,11,51088,51088,11,51200,51200,11,51312,51312,11,51424,51424,11,51536,51536,11,51648,51648,11,51760,51760,11,51872,51872,11,51984,51984,11,52096,52096,11,52208,52208,11,52320,52320,11,52432,52432,11,52544,52544,11,52656,52656,11,52768,52768,11,52880,52880,11,52992,52992,11,53104,53104,11,53216,53216,11,53328,53328,11,53440,53440,11,53552,53552,11,53664,53664,11,53776,53776,11,53888,53888,11,54000,54000,11,54112,54112,11,54224,54224,11,54336,54336,11,54448,54448,11,54560,54560,11,54672,54672,11,54784,54784,11,54896,54896,11,55008,55008,11,55120,55120,11,64286,64286,5,66272,66272,5,68900,68903,5,69762,69762,7,69817,69818,5,69927,69931,5,70003,70003,5,70070,70078,5,70094,70094,7,70194,70195,7,70206,70206,5,70400,70401,5,70463,70463,7,70475,70477,7,70512,70516,5,70722,70724,5,70832,70832,5,70842,70842,5,70847,70848,5,71088,71089,7,71102,71102,7,71219,71226,5,71231,71232,5,71342,71343,7,71453,71455,5,71463,71467,5,71737,71738,5,71995,71996,5,72000,72000,7,72145,72147,7,72160,72160,5,72249,72249,7,72273,72278,5,72330,72342,5,72752,72758,5,72850,72871,5,72882,72883,5,73018,73018,5,73031,73031,5,73109,73109,5,73461,73462,7,94031,94031,5,94192,94193,7,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,126976,126979,14,127184,127231,14,127344,127345,14,127405,127461,14,127514,127514,14,127561,127567,14,127778,127779,14,127896,127896,14,127985,127986,14,127995,127999,5,128326,128328,14,128360,128366,14,128378,128378,14,128394,128397,14,128405,128406,14,128422,128423,14,128435,128443,14,128453,128464,14,128479,128480,14,128484,128487,14,128496,128498,14,128640,128709,14,128723,128724,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129096,129103,14,129292,129292,14,129311,129311,14,129329,129330,14,129344,129349,14,129360,129374,14,129394,129394,14,129402,129402,14,129413,129425,14,129445,129450,14,129466,129471,14,129483,129483,14,129511,129535,14,129653,129655,14,129667,129670,14,129705,129711,14,129731,129743,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2307,2307,7,2366,2368,7,2382,2383,7,2434,2435,7,2497,2500,5,2519,2519,5,2563,2563,7,2631,2632,5,2677,2677,5,2750,2752,7,2763,2764,7,2817,2817,5,2879,2879,5,2891,2892,7,2914,2915,5,3008,3008,5,3021,3021,5,3076,3076,5,3146,3149,5,3202,3203,7,3264,3265,7,3271,3272,7,3298,3299,5,3390,3390,5,3402,3404,7,3426,3427,5,3535,3535,5,3544,3550,7,3635,3635,7,3763,3763,7,3893,3893,5,3953,3966,5,3981,3991,5,4145,4145,7,4157,4158,5,4209,4212,5,4237,4237,5,4520,4607,10,5970,5971,5,6071,6077,5,6089,6099,5,6277,6278,5,6439,6440,5,6451,6456,7,6683,6683,5,6744,6750,5,6765,6770,7,6846,6846,5,6964,6964,5,6972,6972,5,7019,7027,5,7074,7077,5,7083,7085,5,7146,7148,7,7154,7155,7,7222,7223,5,7394,7400,5,7416,7417,5,8204,8204,5,8233,8233,4,8288,8292,4,8413,8416,5,8482,8482,14,8986,8987,14,9193,9203,14,9654,9654,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9775,14,9792,9792,14,9800,9811,14,9825,9826,14,9831,9831,14,9852,9853,14,9872,9873,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9936,9936,14,9941,9960,14,9974,9974,14,9982,9985,14,9992,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10145,10145,14,11013,11015,14,11503,11505,5,12334,12335,5,12951,12951,14,42612,42621,5,43014,43014,5,43047,43047,7,43204,43205,5,43335,43345,5,43395,43395,7,43450,43451,7,43561,43566,5,43573,43574,5,43644,43644,5,43710,43711,5,43758,43759,7,44005,44005,5,44012,44012,7,44060,44060,11,44116,44116,11,44172,44172,11,44228,44228,11,44284,44284,11,44340,44340,11,44396,44396,11,44452,44452,11,44508,44508,11,44564,44564,11,44620,44620,11,44676,44676,11,44732,44732,11,44788,44788,11,44844,44844,11,44900,44900,11,44956,44956,11,45012,45012,11,45068,45068,11,45124,45124,11,45180,45180,11,45236,45236,11,45292,45292,11,45348,45348,11,45404,45404,11,45460,45460,11,45516,45516,11,45572,45572,11,45628,45628,11,45684,45684,11,45740,45740,11,45796,45796,11,45852,45852,11,45908,45908,11,45964,45964,11,46020,46020,11,46076,46076,11,46132,46132,11,46188,46188,11,46244,46244,11,46300,46300,11,46356,46356,11,46412,46412,11,46468,46468,11,46524,46524,11,46580,46580,11,46636,46636,11,46692,46692,11,46748,46748,11,46804,46804,11,46860,46860,11,46916,46916,11,46972,46972,11,47028,47028,11,47084,47084,11,47140,47140,11,47196,47196,11,47252,47252,11,47308,47308,11,47364,47364,11,47420,47420,11,47476,47476,11,47532,47532,11,47588,47588,11,47644,47644,11,47700,47700,11,47756,47756,11,47812,47812,11,47868,47868,11,47924,47924,11,47980,47980,11,48036,48036,11,48092,48092,11,48148,48148,11,48204,48204,11,48260,48260,11,48316,48316,11,48372,48372,11,48428,48428,11,48484,48484,11,48540,48540,11,48596,48596,11,48652,48652,11,48708,48708,11,48764,48764,11,48820,48820,11,48876,48876,11,48932,48932,11,48988,48988,11,49044,49044,11,49100,49100,11,49156,49156,11,49212,49212,11,49268,49268,11,49324,49324,11,49380,49380,11,49436,49436,11,49492,49492,11,49548,49548,11,49604,49604,11,49660,49660,11,49716,49716,11,49772,49772,11,49828,49828,11,49884,49884,11,49940,49940,11,49996,49996,11,50052,50052,11,50108,50108,11,50164,50164,11,50220,50220,11,50276,50276,11,50332,50332,11,50388,50388,11,50444,50444,11,50500,50500,11,50556,50556,11,50612,50612,11,50668,50668,11,50724,50724,11,50780,50780,11,50836,50836,11,50892,50892,11,50948,50948,11,51004,51004,11,51060,51060,11,51116,51116,11,51172,51172,11,51228,51228,11,51284,51284,11,51340,51340,11,51396,51396,11,51452,51452,11,51508,51508,11,51564,51564,11,51620,51620,11,51676,51676,11,51732,51732,11,51788,51788,11,51844,51844,11,51900,51900,11,51956,51956,11,52012,52012,11,52068,52068,11,52124,52124,11,52180,52180,11,52236,52236,11,52292,52292,11,52348,52348,11,52404,52404,11,52460,52460,11,52516,52516,11,52572,52572,11,52628,52628,11,52684,52684,11,52740,52740,11,52796,52796,11,52852,52852,11,52908,52908,11,52964,52964,11,53020,53020,11,53076,53076,11,53132,53132,11,53188,53188,11,53244,53244,11,53300,53300,11,53356,53356,11,53412,53412,11,53468,53468,11,53524,53524,11,53580,53580,11,53636,53636,11,53692,53692,11,53748,53748,11,53804,53804,11,53860,53860,11,53916,53916,11,53972,53972,11,54028,54028,11,54084,54084,11,54140,54140,11,54196,54196,11,54252,54252,11,54308,54308,11,54364,54364,11,54420,54420,11,54476,54476,11,54532,54532,11,54588,54588,11,54644,54644,11,54700,54700,11,54756,54756,11,54812,54812,11,54868,54868,11,54924,54924,11,54980,54980,11,55036,55036,11,55092,55092,11,55148,55148,11,55216,55238,9,65056,65071,5,65529,65531,4,68097,68099,5,68159,68159,5,69446,69456,5,69688,69702,5,69808,69810,7,69815,69816,7,69821,69821,1,69888,69890,5,69932,69932,7,69957,69958,7,70016,70017,5,70067,70069,7,70079,70080,7,70089,70092,5,70095,70095,5,70191,70193,5,70196,70196,5,70198,70199,5,70367,70367,5,70371,70378,5,70402,70403,7,70462,70462,5,70464,70464,5,70471,70472,7,70487,70487,5,70502,70508,5,70709,70711,7,70720,70721,7,70725,70725,7,70750,70750,5,70833,70834,7,70841,70841,7,70843,70844,7,70846,70846,7,70849,70849,7,71087,71087,5,71090,71093,5,71100,71101,5,71103,71104,5,71216,71218,7,71227,71228,7,71230,71230,7,71339,71339,5,71341,71341,5,71344,71349,5,71351,71351,5,71456,71457,7,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123628,123631,5,125252,125258,5,126980,126980,14,127183,127183,14,127245,127247,14,127340,127343,14,127358,127359,14,127377,127386,14,127462,127487,6,127491,127503,14,127535,127535,14,127548,127551,14,127568,127569,14,127744,127777,14,127780,127891,14,127894,127895,14,127897,127899,14,127902,127984,14,127987,127989,14,127991,127994,14,128000,128253,14,128255,128317,14,128329,128334,14,128336,128359,14,128367,128368,14,128371,128377,14,128379,128390,14,128392,128393,14,128398,128399,14,128401,128404,14,128407,128419,14,128421,128421,14,128424,128424,14,128433,128434,14,128444,128444,14,128450,128452,14,128465,128467,14,128476,128478,14,128481,128481,14,128483,128483,14,128488,128488,14,128495,128495,14,128499,128499,14,128506,128591,14,128710,128714,14,128721,128722,14,128725,128725,14,128728,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129664,129666,14,129671,129679,14,129686,129704,14,129712,129718,14,129728,129730,14,129744,129750,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2259,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3134,3136,5,3142,3144,5,3157,3158,5,3201,3201,5,3260,3260,5,3263,3263,5,3266,3266,5,3270,3270,5,3274,3275,7,3285,3286,5,3328,3329,5,3387,3388,5,3391,3392,7,3398,3400,7,3405,3405,5,3415,3415,5,3457,3457,5,3530,3530,5,3536,3537,7,3542,3542,5,3551,3551,5,3633,3633,5,3636,3642,5,3761,3761,5,3764,3772,5,3864,3865,5,3895,3895,5,3902,3903,7,3967,3967,7,3974,3975,5,3993,4028,5,4141,4144,5,4146,4151,5,4155,4156,7,4182,4183,7,4190,4192,5,4226,4226,5,4229,4230,5,4253,4253,5,4448,4519,9,4957,4959,5,5938,5940,5,6002,6003,5,6070,6070,7,6078,6085,7,6087,6088,7,6109,6109,5,6158,6158,4,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6848,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7673,5,8203,8203,4,8205,8205,13,8232,8232,4,8234,8238,4,8265,8265,14,8293,8293,4,8400,8412,5,8417,8417,5,8421,8432,5,8505,8505,14,8617,8618,14,9000,9000,14,9167,9167,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9776,9783,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9935,14,9937,9937,14,9939,9940,14,9961,9962,14,9968,9973,14,9975,9978,14,9981,9981,14,9986,9986,14,9989,9989,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10084,14,10133,10135,14,10160,10160,14,10548,10549,14,11035,11036,14,11093,11093,14,11647,11647,5,12330,12333,5,12336,12336,14,12441,12442,5,12953,12953,14,42608,42610,5,42654,42655,5,43010,43010,5,43019,43019,5,43045,43046,5,43052,43052,5,43188,43203,7,43232,43249,5,43302,43309,5,43346,43347,7,43392,43394,5,43443,43443,5,43446,43449,5,43452,43453,5,43493,43493,5,43567,43568,7,43571,43572,7,43587,43587,5,43597,43597,7,43696,43696,5,43703,43704,5,43713,43713,5,43756,43757,5,43765,43765,7,44003,44004,7,44006,44007,7,44009,44010,7,44013,44013,5,44033,44059,12,44061,44087,12,44089,44115,12,44117,44143,12,44145,44171,12,44173,44199,12,44201,44227,12,44229,44255,12,44257,44283,12,44285,44311,12,44313,44339,12,44341,44367,12,44369,44395,12,44397,44423,12,44425,44451,12,44453,44479,12,44481,44507,12,44509,44535,12,44537,44563,12,44565,44591,12,44593,44619,12,44621,44647,12,44649,44675,12,44677,44703,12,44705,44731,12,44733,44759,12,44761,44787,12,44789,44815,12,44817,44843,12,44845,44871,12,44873,44899,12,44901,44927,12,44929,44955,12,44957,44983,12,44985,45011,12,45013,45039,12,45041,45067,12,45069,45095,12,45097,45123,12,45125,45151,12,45153,45179,12,45181,45207,12,45209,45235,12,45237,45263,12,45265,45291,12,45293,45319,12,45321,45347,12,45349,45375,12,45377,45403,12,45405,45431,12,45433,45459,12,45461,45487,12,45489,45515,12,45517,45543,12,45545,45571,12,45573,45599,12,45601,45627,12,45629,45655,12,45657,45683,12,45685,45711,12,45713,45739,12,45741,45767,12,45769,45795,12,45797,45823,12,45825,45851,12,45853,45879,12,45881,45907,12,45909,45935,12,45937,45963,12,45965,45991,12,45993,46019,12,46021,46047,12,46049,46075,12,46077,46103,12,46105,46131,12,46133,46159,12,46161,46187,12,46189,46215,12,46217,46243,12,46245,46271,12,46273,46299,12,46301,46327,12,46329,46355,12,46357,46383,12,46385,46411,12,46413,46439,12,46441,46467,12,46469,46495,12,46497,46523,12,46525,46551,12,46553,46579,12,46581,46607,12,46609,46635,12,46637,46663,12,46665,46691,12,46693,46719,12,46721,46747,12,46749,46775,12,46777,46803,12,46805,46831,12,46833,46859,12,46861,46887,12,46889,46915,12,46917,46943,12,46945,46971,12,46973,46999,12,47001,47027,12,47029,47055,12,47057,47083,12,47085,47111,12,47113,47139,12,47141,47167,12,47169,47195,12,47197,47223,12,47225,47251,12,47253,47279,12,47281,47307,12,47309,47335,12,47337,47363,12,47365,47391,12,47393,47419,12,47421,47447,12,47449,47475,12,47477,47503,12,47505,47531,12,47533,47559,12,47561,47587,12,47589,47615,12,47617,47643,12,47645,47671,12,47673,47699,12,47701,47727,12,47729,47755,12,47757,47783,12,47785,47811,12,47813,47839,12,47841,47867,12,47869,47895,12,47897,47923,12,47925,47951,12,47953,47979,12,47981,48007,12,48009,48035,12,48037,48063,12,48065,48091,12,48093,48119,12,48121,48147,12,48149,48175,12,48177,48203,12,48205,48231,12,48233,48259,12,48261,48287,12,48289,48315,12,48317,48343,12,48345,48371,12,48373,48399,12,48401,48427,12,48429,48455,12,48457,48483,12,48485,48511,12,48513,48539,12,48541,48567,12,48569,48595,12,48597,48623,12,48625,48651,12,48653,48679,12,48681,48707,12,48709,48735,12,48737,48763,12,48765,48791,12,48793,48819,12,48821,48847,12,48849,48875,12,48877,48903,12,48905,48931,12,48933,48959,12,48961,48987,12,48989,49015,12,49017,49043,12,49045,49071,12,49073,49099,12,49101,49127,12,49129,49155,12,49157,49183,12,49185,49211,12,49213,49239,12,49241,49267,12,49269,49295,12,49297,49323,12,49325,49351,12,49353,49379,12,49381,49407,12,49409,49435,12,49437,49463,12,49465,49491,12,49493,49519,12,49521,49547,12,49549,49575,12,49577,49603,12,49605,49631,12,49633,49659,12,49661,49687,12,49689,49715,12,49717,49743,12,49745,49771,12,49773,49799,12,49801,49827,12,49829,49855,12,49857,49883,12,49885,49911,12,49913,49939,12,49941,49967,12,49969,49995,12,49997,50023,12,50025,50051,12,50053,50079,12,50081,50107,12,50109,50135,12,50137,50163,12,50165,50191,12,50193,50219,12,50221,50247,12,50249,50275,12,50277,50303,12,50305,50331,12,50333,50359,12,50361,50387,12,50389,50415,12,50417,50443,12,50445,50471,12,50473,50499,12,50501,50527,12,50529,50555,12,50557,50583,12,50585,50611,12,50613,50639,12,50641,50667,12,50669,50695,12,50697,50723,12,50725,50751,12,50753,50779,12,50781,50807,12,50809,50835,12,50837,50863,12,50865,50891,12,50893,50919,12,50921,50947,12,50949,50975,12,50977,51003,12,51005,51031,12,51033,51059,12,51061,51087,12,51089,51115,12,51117,51143,12,51145,51171,12,51173,51199,12,51201,51227,12,51229,51255,12,51257,51283,12,51285,51311,12,51313,51339,12,51341,51367,12,51369,51395,12,51397,51423,12,51425,51451,12,51453,51479,12,51481,51507,12,51509,51535,12,51537,51563,12,51565,51591,12,51593,51619,12,51621,51647,12,51649,51675,12,51677,51703,12,51705,51731,12,51733,51759,12,51761,51787,12,51789,51815,12,51817,51843,12,51845,51871,12,51873,51899,12,51901,51927,12,51929,51955,12,51957,51983,12,51985,52011,12,52013,52039,12,52041,52067,12,52069,52095,12,52097,52123,12,52125,52151,12,52153,52179,12,52181,52207,12,52209,52235,12,52237,52263,12,52265,52291,12,52293,52319,12,52321,52347,12,52349,52375,12,52377,52403,12,52405,52431,12,52433,52459,12,52461,52487,12,52489,52515,12,52517,52543,12,52545,52571,12,52573,52599,12,52601,52627,12,52629,52655,12,52657,52683,12,52685,52711,12,52713,52739,12,52741,52767,12,52769,52795,12,52797,52823,12,52825,52851,12,52853,52879,12,52881,52907,12,52909,52935,12,52937,52963,12,52965,52991,12,52993,53019,12,53021,53047,12,53049,53075,12,53077,53103,12,53105,53131,12,53133,53159,12,53161,53187,12,53189,53215,12,53217,53243,12,53245,53271,12,53273,53299,12,53301,53327,12,53329,53355,12,53357,53383,12,53385,53411,12,53413,53439,12,53441,53467,12,53469,53495,12,53497,53523,12,53525,53551,12,53553,53579,12,53581,53607,12,53609,53635,12,53637,53663,12,53665,53691,12,53693,53719,12,53721,53747,12,53749,53775,12,53777,53803,12,53805,53831,12,53833,53859,12,53861,53887,12,53889,53915,12,53917,53943,12,53945,53971,12,53973,53999,12,54001,54027,12,54029,54055,12,54057,54083,12,54085,54111,12,54113,54139,12,54141,54167,12,54169,54195,12,54197,54223,12,54225,54251,12,54253,54279,12,54281,54307,12,54309,54335,12,54337,54363,12,54365,54391,12,54393,54419,12,54421,54447,12,54449,54475,12,54477,54503,12,54505,54531,12,54533,54559,12,54561,54587,12,54589,54615,12,54617,54643,12,54645,54671,12,54673,54699,12,54701,54727,12,54729,54755,12,54757,54783,12,54785,54811,12,54813,54839,12,54841,54867,12,54869,54895,12,54897,54923,12,54925,54951,12,54953,54979,12,54981,55007,12,55009,55035,12,55037,55063,12,55065,55091,12,55093,55119,12,55121,55147,12,55149,55175,12,55177,55203,12,55243,55291,10,65024,65039,5,65279,65279,4,65520,65528,4,66045,66045,5,66422,66426,5,68101,68102,5,68152,68154,5,68325,68326,5,69291,69292,5,69632,69632,7,69634,69634,7,69759,69761,5]")}return o(e,[{key:"getGraphemeBreakType",value:function(e){if(e<32)return 10===e?3:13===e?2:4;if(e<127)return 0;for(var t=this._data,n=t.length/3,r=1;r<=n;)if(e<t[3*r])r*=2;else{if(!(e>t[3*r+1]))return t[3*r+2];r=2*r+1}return 0}}],[{key:"getInstance",value:function(){return e._INSTANCE||(e._INSTANCE=new e),e._INSTANCE}}]),e}();function ue(e,t){return(t<<5)-t+e|0}function ce(e,t){t=ue(149417,t);for(var n=0,r=e.length;n<r;n++)t=ue(e.charCodeAt(n),t);return t}function le(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:32)-t;return(e<<t|(~((1<<n)-1)&e)>>>n)>>>0}function fe(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.byteLength,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=0;i<n;i++)e[t+i]=r}function he(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:32;return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map((function(e){return e.toString(16).padStart(2,"0")})).join(""):function(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"0";e.length<t;)e=n+e;return e}((e>>>0).toString(16),t/4)}se._INSTANCE=null;var de=function(){function e(){t(this,e),this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}return o(e,[{key:"update",value:function(e){var t=e.length;if(0!==t){var n,r,i=this._buff,o=this._buffLen,a=this._leftoverHighSurrogate;for(0!==a?(n=a,r=-1,a=0):(n=e.charCodeAt(0),r=0);;){var s=n;if(ie(n)){if(!(r+1<t)){a=n;break}var u=e.charCodeAt(r+1);oe(u)?(r++,s=ae(n,u)):s=65533}else oe(n)&&(s=65533);if(o=this._push(i,o,s),!(++r<t))break;n=e.charCodeAt(r)}this._buffLen=o,this._leftoverHighSurrogate=a}}},{key:"_push",value:function(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(1984&n)>>>6,e[t++]=128|(63&n)>>>0):n<65536?(e[t++]=224|(61440&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0):(e[t++]=240|(1835008&n)>>>18,e[t++]=128|(258048&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}},{key:"digest",value:function(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),he(this._h0)+he(this._h1)+he(this._h2)+he(this._h3)+he(this._h4)}},{key:"_wrapUp",value:function(){this._buff[this._buffLen++]=128,fe(this._buff,this._buffLen),this._buffLen>56&&(this._step(),fe(this._buff));var e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}},{key:"_step",value:function(){for(var t=e._bigBlock32,n=this._buffDV,r=0;r<64;r+=4)t.setUint32(r,n.getUint32(r,!1),!1);for(var i=64;i<320;i+=4)t.setUint32(i,le(t.getUint32(i-12,!1)^t.getUint32(i-32,!1)^t.getUint32(i-56,!1)^t.getUint32(i-64,!1),1),!1);for(var o,a,s,u=this._h0,c=this._h1,l=this._h2,f=this._h3,h=this._h4,d=0;d<80;d++)d<20?(o=c&l|~c&f,a=1518500249):d<40?(o=c^l^f,a=1859775393):d<60?(o=c&l|c&f|l&f,a=2400959708):(o=c^l^f,a=3395469782),s=le(u,5)+o+h+a+t.getUint32(4*d,!1)&4294967295,h=f,f=l,l=le(c,30),c=u,u=s;this._h0=this._h0+u&4294967295,this._h1=this._h1+c&4294967295,this._h2=this._h2+l&4294967295,this._h3=this._h3+f&4294967295,this._h4=this._h4+h&4294967295}}]),e}();de._bigBlock32=new DataView(new ArrayBuffer(320));var me=function(){function e(n){t(this,e),this.source=n}return o(e,[{key:"getElements",value:function(){for(var e=this.source,t=new Int32Array(e.length),n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}}]),e}();function ge(e,t,n){return new _e(new me(e),new me(t)).ComputeDiff(n).changes}var pe,ve=function(){function e(){t(this,e)}return o(e,null,[{key:"Assert",value:function(e,t){if(!e)throw new Error(t)}}]),e}(),ye=function(){function e(){t(this,e)}return o(e,null,[{key:"Copy",value:function(e,t,n,r,i){for(var o=0;o<i;o++)n[r+o]=e[t+o]}},{key:"Copy2",value:function(e,t,n,r,i){for(var o=0;o<i;o++)n[r+o]=e[t+o]}}]),e}(),be=function(){function e(){t(this,e),this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}return o(e,[{key:"MarkNextChange",value:function(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new re(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}},{key:"AddOriginalElement",value:function(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}},{key:"AddModifiedElement",value:function(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}},{key:"getChanges",value:function(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}},{key:"getReverseChanges",value:function(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}]),e}(),_e=function(){function e(n,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;t(this,e),this.ContinueProcessingPredicate=i,this._originalSequence=n,this._modifiedSequence=r;var o=ne(e._getElements(n),3),a=o[0],s=o[1],u=o[2],c=ne(e._getElements(r),3),l=c[0],f=c[1],h=c[2];this._hasStrings=u&&h,this._originalStringElements=a,this._originalElementsOrHash=s,this._modifiedStringElements=l,this._modifiedElementsOrHash=f,this.m_forwardHistory=[],this.m_reverseHistory=[]}return o(e,[{key:"ElementsAreEqual",value:function(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._modifiedStringElements[t])}},{key:"ElementsAreStrictEqual",value:function(t,n){return!!this.ElementsAreEqual(t,n)&&e._getStrictElement(this._originalSequence,t)===e._getStrictElement(this._modifiedSequence,n)}},{key:"OriginalElementsAreEqual",value:function(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._originalStringElements[t])}},{key:"ModifiedElementsAreEqual",value:function(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._modifiedStringElements[e]===this._modifiedStringElements[t])}},{key:"ComputeDiff",value:function(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}},{key:"_ComputeDiff",value:function(e,t,n,r,i){var o=[!1],a=this.ComputeDiffRecursive(e,t,n,r,o);return i&&(a=this.PrettifyChanges(a)),{quitEarly:o[0],changes:a}}},{key:"ComputeDiffRecursive",value:function(e,t,n,r,i){for(i[0]=!1;e<=t&&n<=r&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&r>=n&&this.ElementsAreEqual(t,r);)t--,r--;var o;if(e>t||n>r)return n<=r?(ve.Assert(e===t+1,"originalStart should only be one more than originalEnd"),o=[new re(e,0,n,r-n+1)]):e<=t?(ve.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),o=[new re(e,t-e+1,n,0)]):(ve.Assert(e===t+1,"originalStart should only be one more than originalEnd"),ve.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),o=[]),o;var a=[0],s=[0],u=this.ComputeRecursionPoint(e,t,n,r,a,s,i),c=a[0],l=s[0];if(null!==u)return u;if(!i[0]){var f=this.ComputeDiffRecursive(e,c,n,l,i),h=[];return h=i[0]?[new re(c+1,t-(c+1)+1,l+1,r-(l+1)+1)]:this.ComputeDiffRecursive(c+1,t,l+1,r,i),this.ConcatenateChanges(f,h)}return[new re(e,t-e+1,n,r-n+1)]}},{key:"WALKTRACE",value:function(e,t,n,r,i,o,a,s,u,c,l,f,h,d,m,g,p,v){var y,b=null,_=new be,C=t,S=n,k=h[0]-g[0]-r,x=-1073741824,A=this.m_forwardHistory.length-1;do{var E=k+e;E===C||E<S&&u[E-1]<u[E+1]?(d=(l=u[E+1])-k-r,l<x&&_.MarkNextChange(),x=l,_.AddModifiedElement(l+1,d),k=E+1-e):(d=(l=u[E-1]+1)-k-r,l<x&&_.MarkNextChange(),x=l-1,_.AddOriginalElement(l,d+1),k=E-1-e),A>=0&&(e=(u=this.m_forwardHistory[A])[0],C=1,S=u.length-1)}while(--A>=-1);if(y=_.getReverseChanges(),v[0]){var w=h[0]+1,N=g[0]+1;if(null!==y&&y.length>0){var L=y[y.length-1];w=Math.max(w,L.getOriginalEnd()),N=Math.max(N,L.getModifiedEnd())}b=[new re(w,f-w+1,N,m-N+1)]}else{_=new be,C=o,S=a,k=h[0]-g[0]-s,x=1073741824,A=p?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{var T=k+i;T===C||T<S&&c[T-1]>=c[T+1]?(d=(l=c[T+1]-1)-k-s,l>x&&_.MarkNextChange(),x=l+1,_.AddOriginalElement(l+1,d+1),k=T+1-i):(d=(l=c[T-1])-k-s,l>x&&_.MarkNextChange(),x=l,_.AddModifiedElement(l+1,d+1),k=T-1-i),A>=0&&(i=(c=this.m_reverseHistory[A])[0],C=1,S=c.length-1)}while(--A>=-1);b=_.getChanges()}return this.ConcatenateChanges(y,b)}},{key:"ComputeRecursionPoint",value:function(e,t,n,r,i,o,a){var s=0,u=0,c=0,l=0,f=0,h=0;e--,n--,i[0]=0,o[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];var d=t-e+(r-n),m=d+1,g=new Int32Array(m),p=new Int32Array(m),v=r-n,y=t-e,b=e-n,_=t-r,C=(y-v)%2===0;g[v]=e,p[y]=t,a[0]=!1;for(var S=1;S<=d/2+1;S++){var k=0,x=0;c=this.ClipDiagonalBound(v-S,S,v,m),l=this.ClipDiagonalBound(v+S,S,v,m);for(var A=c;A<=l;A+=2){u=(s=A===c||A<l&&g[A-1]<g[A+1]?g[A+1]:g[A-1]+1)-(A-v)-b;for(var E=s;s<t&&u<r&&this.ElementsAreEqual(s+1,u+1);)s++,u++;if(g[A]=s,s+u>k+x&&(k=s,x=u),!C&&Math.abs(A-y)<=S-1&&s>=p[A])return i[0]=s,o[0]=u,E<=p[A]&&S<=1448?this.WALKTRACE(v,c,l,b,y,f,h,_,g,p,s,t,i,u,r,o,C,a):null}var w=(k-e+(x-n)-S)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(k,w))return a[0]=!0,i[0]=k,o[0]=x,w>0&&S<=1448?this.WALKTRACE(v,c,l,b,y,f,h,_,g,p,s,t,i,u,r,o,C,a):(e++,n++,[new re(e,t-e+1,n,r-n+1)]);f=this.ClipDiagonalBound(y-S,S,y,m),h=this.ClipDiagonalBound(y+S,S,y,m);for(var N=f;N<=h;N+=2){u=(s=N===f||N<h&&p[N-1]>=p[N+1]?p[N+1]-1:p[N-1])-(N-y)-_;for(var L=s;s>e&&u>n&&this.ElementsAreEqual(s,u);)s--,u--;if(p[N]=s,C&&Math.abs(N-v)<=S&&s<=g[N])return i[0]=s,o[0]=u,L>=g[N]&&S<=1448?this.WALKTRACE(v,c,l,b,y,f,h,_,g,p,s,t,i,u,r,o,C,a):null}if(S<=1447){var T=new Int32Array(l-c+2);T[0]=v-c+1,ye.Copy2(g,c,T,1,l-c+1),this.m_forwardHistory.push(T),(T=new Int32Array(h-f+2))[0]=y-f+1,ye.Copy2(p,f,T,1,h-f+1),this.m_reverseHistory.push(T)}}return this.WALKTRACE(v,c,l,b,y,f,h,_,g,p,s,t,i,u,r,o,C,a)}},{key:"PrettifyChanges",value:function(e){for(var t=0;t<e.length;t++){for(var n=e[t],r=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,i=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,o=n.originalLength>0,a=n.modifiedLength>0;n.originalStart+n.originalLength<r&&n.modifiedStart+n.modifiedLength<i&&(!o||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!a||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){var s=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!s)break;n.originalStart++,n.modifiedStart++}var u=[null];t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],u)&&(e[t]=u[0],e.splice(t+1,1),t--)}for(var c=e.length-1;c>=0;c--){var l=e[c],f=0,h=0;if(c>0){var d=e[c-1];f=d.originalStart+d.originalLength,h=d.modifiedStart+d.modifiedLength}for(var m=l.originalLength>0,g=l.modifiedLength>0,p=0,v=this._boundaryScore(l.originalStart,l.originalLength,l.modifiedStart,l.modifiedLength),y=1;;y++){var b=l.originalStart-y,_=l.modifiedStart-y;if(b<f||_<h)break;if(m&&!this.OriginalElementsAreEqual(b,b+l.originalLength))break;if(g&&!this.ModifiedElementsAreEqual(_,_+l.modifiedLength))break;var C=(b===f&&_===h?5:0)+this._boundaryScore(b,l.originalLength,_,l.modifiedLength);C>v&&(v=C,p=y)}l.originalStart-=p,l.modifiedStart-=p;var S=[null];c>0&&this.ChangesOverlap(e[c-1],e[c],S)&&(e[c-1]=S[0],e.splice(c,1),c++)}if(this._hasStrings)for(var k=1,x=e.length;k<x;k++){var A=e[k-1],E=e[k],w=E.originalStart-A.originalStart-A.originalLength,N=A.originalStart,L=E.originalStart+E.originalLength,T=L-N,O=A.modifiedStart,I=E.modifiedStart+E.modifiedLength,P=I-O;if(w<5&&T<20&&P<20){var M=this._findBetterContiguousSequence(N,T,O,P,w);if(M){var R=ne(M,2),D=R[0],j=R[1];D===A.originalStart+A.originalLength&&j===A.modifiedStart+A.modifiedLength||(A.originalLength=D-A.originalStart,A.modifiedLength=j-A.modifiedStart,E.originalStart=D+w,E.modifiedStart=j+w,E.originalLength=L-E.originalStart,E.modifiedLength=I-E.modifiedStart)}}}return e}},{key:"_findBetterContiguousSequence",value:function(e,t,n,r,i){if(t<i||r<i)return null;for(var o=e+t-i+1,a=n+r-i+1,s=0,u=0,c=0,l=e;l<o;l++)for(var f=n;f<a;f++){var h=this._contiguousSequenceScore(l,f,i);h>0&&h>s&&(s=h,u=l,c=f)}return s>0?[u,c]:null}},{key:"_contiguousSequenceScore",value:function(e,t,n){for(var r=0,i=0;i<n;i++){if(!this.ElementsAreEqual(e+i,t+i))return 0;r+=this._originalStringElements[e+i].length}return r}},{key:"_OriginalIsBoundary",value:function(e){return e<=0||e>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}},{key:"_OriginalRegionIsBoundary",value:function(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){var n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}},{key:"_ModifiedIsBoundary",value:function(e){return e<=0||e>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}},{key:"_ModifiedRegionIsBoundary",value:function(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){var n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}},{key:"_boundaryScore",value:function(e,t,n,r){return(this._OriginalRegionIsBoundary(e,t)?1:0)+(this._ModifiedRegionIsBoundary(n,r)?1:0)}},{key:"ConcatenateChanges",value:function(e,t){var n=[];if(0===e.length||0===t.length)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){var r=new Array(e.length+t.length-1);return ye.Copy(e,0,r,0,e.length-1),r[e.length-1]=n[0],ye.Copy(t,1,r,e.length,t.length-1),r}var i=new Array(e.length+t.length);return ye.Copy(e,0,i,0,e.length),ye.Copy(t,0,i,e.length,t.length),i}},{key:"ChangesOverlap",value:function(e,t,n){if(ve.Assert(e.originalStart<=t.originalStart,"Left change is not less than or equal to right change"),ve.Assert(e.modifiedStart<=t.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){var r=e.originalStart,i=e.originalLength,o=e.modifiedStart,a=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(i=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(a=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new re(r,i,o,a),!0}return n[0]=null,!1}},{key:"ClipDiagonalBound",value:function(e,t,n,r){if(e>=0&&e<r)return e;var i=t%2===0;return e<0?i===(n%2===0)?0:1:i===((r-n-1)%2===0)?r-1:r-2}}],[{key:"_isStringArray",value:function(e){return e.length>0&&"string"===typeof e[0]}},{key:"_getElements",value:function(t){var n=t.getElements();if(e._isStringArray(n)){for(var r=new Int32Array(n.length),i=0,o=n.length;i<o;i++)r[i]=ce(n[i],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}},{key:"_getStrictElement",value:function(e,t){return"function"===typeof e.getStrictElement?e.getStrictElement(t):null}}]),e}();if("undefined"!==typeof q.vscode&&"undefined"!==typeof q.vscode.process){var Ce=q.vscode.process;pe={get platform(){return Ce.platform},get arch(){return Ce.arch},get env(){return Ce.env},cwd:function(){return Ce.cwd()},nextTick:function(e){return J(e)}}}else pe="undefined"!==typeof process?{get platform(){return process.platform},get arch(){return process.arch},get env(){return{NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}},cwd:function(){return{NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.VSCODE_CWD||process.cwd()},nextTick:function(e){return process.nextTick(e)}}:{get platform(){return z?"win32":G?"darwin":"linux"},get arch(){},nextTick:function(e){return J(e)},get env(){return{}},cwd:function(){return"/"}};var Se=pe.cwd,ke=(pe.env,pe.platform),xe=46,Ae=47,Ee=92,we=58,Ne=function(e){d(r,e);var n=v(r);function r(e,i,o){var a,s;t(this,r),"string"===typeof i&&0===i.indexOf("not ")?(s="must not be",i=i.replace(/^not /,"")):s="must be";var u=-1!==e.indexOf(".")?"property":"argument",c='The "'.concat(e,'" ').concat(u," ").concat(s," of type ").concat(i);return c+=". Received type ".concat(typeof o),(a=n.call(this,c)).code="ERR_INVALID_ARG_TYPE",a}return o(r)}(b(Error));function Le(e,t){if("string"!==typeof e)throw new Ne(t,"string",e)}function Te(e){return e===Ae||e===Ee}function Oe(e){return e===Ae}function Ie(e){return e>=65&&e<=90||e>=97&&e<=122}function Pe(e,t,n,r){for(var i="",o=0,a=-1,s=0,u=0,c=0;c<=e.length;++c){if(c<e.length)u=e.charCodeAt(c);else{if(r(u))break;u=Ae}if(r(u)){if(a===c-1||1===s);else if(2===s){if(i.length<2||2!==o||i.charCodeAt(i.length-1)!==xe||i.charCodeAt(i.length-2)!==xe){if(i.length>2){var l=i.lastIndexOf(n);-1===l?(i="",o=0):o=(i=i.slice(0,l)).length-1-i.lastIndexOf(n),a=c,s=0;continue}if(0!==i.length){i="",o=0,a=c,s=0;continue}}t&&(i+=i.length>0?"".concat(n,".."):"..",o=2)}else i.length>0?i+="".concat(n).concat(e.slice(a+1,c)):i=e.slice(a+1,c),o=c-a-1;a=c,s=0}else u===xe&&-1!==s?++s:s=-1}return i}function Me(e,t){if(null===t||"object"!==typeof t)throw new Ne("pathObject","Object",t);var n=t.dir||t.root,r=t.base||"".concat(t.name||"").concat(t.ext||"");return n?n===t.root?"".concat(n).concat(r):"".concat(n).concat(e).concat(r):r}var Re={resolve:function(){for(var e="",t="",n=!1,r=arguments.length-1;r>=-1;r--){var i=void 0;if(r>=0){if(Le(i=r<0||arguments.length<=r?void 0:arguments[r],"path"),0===i.length)continue}else 0===e.length?i=Se():(void 0===(i={NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}["=".concat(e)]||Se())||i.slice(0,2).toLowerCase()!==e.toLowerCase()&&i.charCodeAt(2)===Ee)&&(i="".concat(e,"\\"));var o=i.length,a=0,s="",u=!1,c=i.charCodeAt(0);if(1===o)Te(c)&&(a=1,u=!0);else if(Te(c))if(u=!0,Te(i.charCodeAt(1))){for(var l=2,f=l;l<o&&!Te(i.charCodeAt(l));)l++;if(l<o&&l!==f){var h=i.slice(f,l);for(f=l;l<o&&Te(i.charCodeAt(l));)l++;if(l<o&&l!==f){for(f=l;l<o&&!Te(i.charCodeAt(l));)l++;l!==o&&l===f||(s="\\\\".concat(h,"\\").concat(i.slice(f,l)),a=l)}}}else a=1;else Ie(c)&&i.charCodeAt(1)===we&&(s=i.slice(0,2),a=2,o>2&&Te(i.charCodeAt(2))&&(u=!0,a=3));if(s.length>0)if(e.length>0){if(s.toLowerCase()!==e.toLowerCase())continue}else e=s;if(n){if(e.length>0)break}else if(t="".concat(i.slice(a),"\\").concat(t),n=u,u&&e.length>0)break}return t=Pe(t,!n,"\\",Te),n?"".concat(e,"\\").concat(t):"".concat(e).concat(t)||"."},normalize:function(e){Le(e,"path");var t=e.length;if(0===t)return".";var n,r=0,i=!1,o=e.charCodeAt(0);if(1===t)return Oe(o)?"\\":e;if(Te(o))if(i=!0,Te(e.charCodeAt(1))){for(var a=2,s=a;a<t&&!Te(e.charCodeAt(a));)a++;if(a<t&&a!==s){var u=e.slice(s,a);for(s=a;a<t&&Te(e.charCodeAt(a));)a++;if(a<t&&a!==s){for(s=a;a<t&&!Te(e.charCodeAt(a));)a++;if(a===t)return"\\\\".concat(u,"\\").concat(e.slice(s),"\\");a!==s&&(n="\\\\".concat(u,"\\").concat(e.slice(s,a)),r=a)}}}else r=1;else Ie(o)&&e.charCodeAt(1)===we&&(n=e.slice(0,2),r=2,t>2&&Te(e.charCodeAt(2))&&(i=!0,r=3));var c=r<t?Pe(e.slice(r),!i,"\\",Te):"";return 0!==c.length||i||(c="."),c.length>0&&Te(e.charCodeAt(t-1))&&(c+="\\"),void 0===n?i?"\\".concat(c):c:i?"".concat(n,"\\").concat(c):"".concat(n).concat(c)},isAbsolute:function(e){Le(e,"path");var t=e.length;if(0===t)return!1;var n=e.charCodeAt(0);return Te(n)||t>2&&Ie(n)&&e.charCodeAt(1)===we&&Te(e.charCodeAt(2))},join:function(){if(0===arguments.length)return".";for(var e,t,n=0;n<arguments.length;++n){var r=n<0||arguments.length<=n?void 0:arguments[n];Le(r,"path"),r.length>0&&(void 0===e?e=t=r:e+="\\".concat(r))}if(void 0===e)return".";var i=!0,o=0;if("string"===typeof t&&Te(t.charCodeAt(0))){++o;var a=t.length;a>1&&Te(t.charCodeAt(1))&&(++o,a>2&&(Te(t.charCodeAt(2))?++o:i=!1))}if(i){for(;o<e.length&&Te(e.charCodeAt(o));)o++;o>=2&&(e="\\".concat(e.slice(o)))}return Re.normalize(e)},relative:function(e,t){if(Le(e,"from"),Le(t,"to"),e===t)return"";var n=Re.resolve(e),r=Re.resolve(t);if(n===r)return"";if((e=n.toLowerCase())===(t=r.toLowerCase()))return"";for(var i=0;i<e.length&&e.charCodeAt(i)===Ee;)i++;for(var o=e.length;o-1>i&&e.charCodeAt(o-1)===Ee;)o--;for(var a=o-i,s=0;s<t.length&&t.charCodeAt(s)===Ee;)s++;for(var u=t.length;u-1>s&&t.charCodeAt(u-1)===Ee;)u--;for(var c=u-s,l=a<c?a:c,f=-1,h=0;h<l;h++){var d=e.charCodeAt(i+h);if(d!==t.charCodeAt(s+h))break;d===Ee&&(f=h)}if(h!==l){if(-1===f)return r}else{if(c>l){if(t.charCodeAt(s+h)===Ee)return r.slice(s+h+1);if(2===h)return r.slice(s+h)}a>l&&(e.charCodeAt(i+h)===Ee?f=h:2===h&&(f=3)),-1===f&&(f=0)}var m="";for(h=i+f+1;h<=o;++h)h!==o&&e.charCodeAt(h)!==Ee||(m+=0===m.length?"..":"\\..");return s+=f,m.length>0?"".concat(m).concat(r.slice(s,u)):(r.charCodeAt(s)===Ee&&++s,r.slice(s,u))},toNamespacedPath:function(e){if("string"!==typeof e)return e;if(0===e.length)return"";var t=Re.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===Ee){if(t.charCodeAt(1)===Ee){var n=t.charCodeAt(2);if(63!==n&&n!==xe)return"\\\\?\\UNC\\".concat(t.slice(2))}}else if(Ie(t.charCodeAt(0))&&t.charCodeAt(1)===we&&t.charCodeAt(2)===Ee)return"\\\\?\\".concat(t);return e},dirname:function(e){Le(e,"path");var t=e.length;if(0===t)return".";var n=-1,r=0,i=e.charCodeAt(0);if(1===t)return Te(i)?e:".";if(Te(i)){if(n=r=1,Te(e.charCodeAt(1))){for(var o=2,a=o;o<t&&!Te(e.charCodeAt(o));)o++;if(o<t&&o!==a){for(a=o;o<t&&Te(e.charCodeAt(o));)o++;if(o<t&&o!==a){for(a=o;o<t&&!Te(e.charCodeAt(o));)o++;if(o===t)return e;o!==a&&(n=r=o+1)}}}}else Ie(i)&&e.charCodeAt(1)===we&&(r=n=t>2&&Te(e.charCodeAt(2))?3:2);for(var s=-1,u=!0,c=t-1;c>=r;--c)if(Te(e.charCodeAt(c))){if(!u){s=c;break}}else u=!1;if(-1===s){if(-1===n)return".";s=n}return e.slice(0,s)},basename:function(e,t){void 0!==t&&Le(t,"ext"),Le(e,"path");var n,r=0,i=-1,o=!0;if(e.length>=2&&Ie(e.charCodeAt(0))&&e.charCodeAt(1)===we&&(r=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";var a=t.length-1,s=-1;for(n=e.length-1;n>=r;--n){var u=e.charCodeAt(n);if(Te(u)){if(!o){r=n+1;break}}else-1===s&&(o=!1,s=n+1),a>=0&&(u===t.charCodeAt(a)?-1===--a&&(i=n):(a=-1,i=s))}return r===i?i=s:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=r;--n)if(Te(e.charCodeAt(n))){if(!o){r=n+1;break}}else-1===i&&(o=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname:function(e){Le(e,"path");var t=0,n=-1,r=0,i=-1,o=!0,a=0;e.length>=2&&e.charCodeAt(1)===we&&Ie(e.charCodeAt(0))&&(t=r=2);for(var s=e.length-1;s>=t;--s){var u=e.charCodeAt(s);if(Te(u)){if(!o){r=s+1;break}}else-1===i&&(o=!1,i=s+1),u===xe?-1===n?n=s:1!==a&&(a=1):-1!==n&&(a=-1)}return-1===n||-1===i||0===a||1===a&&n===i-1&&n===r+1?"":e.slice(n,i)},format:Me.bind(null,"\\"),parse:function(e){Le(e,"path");var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var n=e.length,r=0,i=e.charCodeAt(0);if(1===n)return Te(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(Te(i)){if(r=1,Te(e.charCodeAt(1))){for(var o=2,a=o;o<n&&!Te(e.charCodeAt(o));)o++;if(o<n&&o!==a){for(a=o;o<n&&Te(e.charCodeAt(o));)o++;if(o<n&&o!==a){for(a=o;o<n&&!Te(e.charCodeAt(o));)o++;o===n?r=o:o!==a&&(r=o+1)}}}}else if(Ie(i)&&e.charCodeAt(1)===we){if(n<=2)return t.root=t.dir=e,t;if(r=2,Te(e.charCodeAt(2))){if(3===n)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));for(var s=-1,u=r,c=-1,l=!0,f=e.length-1,h=0;f>=r;--f)if(Te(i=e.charCodeAt(f))){if(!l){u=f+1;break}}else-1===c&&(l=!1,c=f+1),i===xe?-1===s?s=f:1!==h&&(h=1):-1!==s&&(h=-1);return-1!==c&&(-1===s||0===h||1===h&&s===c-1&&s===u+1?t.base=t.name=e.slice(u,c):(t.name=e.slice(u,s),t.base=e.slice(u,c),t.ext=e.slice(s,c))),t.dir=u>0&&u!==r?e.slice(0,u-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},De={resolve:function(){for(var e="",t=!1,n=arguments.length-1;n>=-1&&!t;n--){var r=n>=0?n<0||arguments.length<=n?void 0:arguments[n]:Se();Le(r,"path"),0!==r.length&&(e="".concat(r,"/").concat(e),t=r.charCodeAt(0)===Ae)}return e=Pe(e,!t,"/",Oe),t?"/".concat(e):e.length>0?e:"."},normalize:function(e){if(Le(e,"path"),0===e.length)return".";var t=e.charCodeAt(0)===Ae,n=e.charCodeAt(e.length-1)===Ae;return 0===(e=Pe(e,!t,"/",Oe)).length?t?"/":n?"./":".":(n&&(e+="/"),t?"/".concat(e):e)},isAbsolute:function(e){return Le(e,"path"),e.length>0&&e.charCodeAt(0)===Ae},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var n=t<0||arguments.length<=t?void 0:arguments[t];Le(n,"path"),n.length>0&&(void 0===e?e=n:e+="/".concat(n))}return void 0===e?".":De.normalize(e)},relative:function(e,t){if(Le(e,"from"),Le(t,"to"),e===t)return"";if((e=De.resolve(e))===(t=De.resolve(t)))return"";for(var n=e.length,r=n-1,i=t.length-1,o=r<i?r:i,a=-1,s=0;s<o;s++){var u=e.charCodeAt(1+s);if(u!==t.charCodeAt(1+s))break;u===Ae&&(a=s)}if(s===o)if(i>o){if(t.charCodeAt(1+s)===Ae)return t.slice(1+s+1);if(0===s)return t.slice(1+s)}else r>o&&(e.charCodeAt(1+s)===Ae?a=s:0===s&&(a=0));var c="";for(s=1+a+1;s<=n;++s)s!==n&&e.charCodeAt(s)!==Ae||(c+=0===c.length?"..":"/..");return"".concat(c).concat(t.slice(1+a))},toNamespacedPath:function(e){return e},dirname:function(e){if(Le(e,"path"),0===e.length)return".";for(var t=e.charCodeAt(0)===Ae,n=-1,r=!0,i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===Ae){if(!r){n=i;break}}else r=!1;return-1===n?t?"/":".":t&&1===n?"//":e.slice(0,n)},basename:function(e,t){void 0!==t&&Le(t,"ext"),Le(e,"path");var n,r=0,i=-1,o=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";var a=t.length-1,s=-1;for(n=e.length-1;n>=0;--n){var u=e.charCodeAt(n);if(u===Ae){if(!o){r=n+1;break}}else-1===s&&(o=!1,s=n+1),a>=0&&(u===t.charCodeAt(a)?-1===--a&&(i=n):(a=-1,i=s))}return r===i?i=s:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===Ae){if(!o){r=n+1;break}}else-1===i&&(o=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname:function(e){Le(e,"path");for(var t=-1,n=0,r=-1,i=!0,o=0,a=e.length-1;a>=0;--a){var s=e.charCodeAt(a);if(s!==Ae)-1===r&&(i=!1,r=a+1),s===xe?-1===t?t=a:1!==o&&(o=1):-1!==t&&(o=-1);else if(!i){n=a+1;break}}return-1===t||-1===r||0===o||1===o&&t===r-1&&t===n+1?"":e.slice(t,r)},format:Me.bind(null,"/"),parse:function(e){Le(e,"path");var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var n,r=e.charCodeAt(0)===Ae;r?(t.root="/",n=1):n=0;for(var i=-1,o=0,a=-1,s=!0,u=e.length-1,c=0;u>=n;--u){var l=e.charCodeAt(u);if(l!==Ae)-1===a&&(s=!1,a=u+1),l===xe?-1===i?i=u:1!==c&&(c=1):-1!==i&&(c=-1);else if(!s){o=u+1;break}}if(-1!==a){var f=0===o&&r?1:o;-1===i||0===c||1===c&&i===a-1&&i===o+1?t.base=t.name=e.slice(f,a):(t.name=e.slice(f,i),t.base=e.slice(f,a),t.ext=e.slice(i,a))}return o>0?t.dir=e.slice(0,o-1):r&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};De.win32=Re.win32=Re,De.posix=Re.posix=De;"win32"===ke?Re.normalize:De.normalize,"win32"===ke?Re.resolve:De.resolve,"win32"===ke?Re.relative:De.relative,"win32"===ke?Re.dirname:De.dirname,"win32"===ke?Re.basename:De.basename,"win32"===ke?Re.extname:De.extname,"win32"===ke?Re.sep:De.sep;var je,Fe=/^\w[\w\d+.-]*$/,Ue=/^\//,Ve=/^\/\//;function We(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'.concat(e.authority,'", path: "').concat(e.path,'", query: "').concat(e.query,'", fragment: "').concat(e.fragment,'"}'));if(e.scheme&&!Fe.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!Ue.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Ve.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}var qe="",Be="/",Ke=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,$e=function(){function e(n,r,i,o,a){var s=arguments.length>5&&void 0!==arguments[5]&&arguments[5];t(this,e),"object"===typeof n?(this.scheme=n.scheme||qe,this.authority=n.authority||qe,this.path=n.path||qe,this.query=n.query||qe,this.fragment=n.fragment||qe):(this.scheme=function(e,t){return e||t?e:"file"}(n,s),this.authority=r||qe,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Be&&(t=Be+t):t=Be}return t}(this.scheme,i||qe),this.query=o||qe,this.fragment=a||qe,We(this,s))}return o(e,[{key:"fsPath",get:function(){return Qe(this,!1)}},{key:"with",value:function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,i=e.query,o=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=qe),void 0===n?n=this.authority:null===n&&(n=qe),void 0===r?r=this.path:null===r&&(r=qe),void 0===i?i=this.query:null===i&&(i=qe),void 0===o?o=this.fragment:null===o&&(o=qe),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&o===this.fragment?this:new Ye(t,n,r,i,o)}},{key:"toString",value:function(){return Xe(this,arguments.length>0&&void 0!==arguments[0]&&arguments[0])}},{key:"toJSON",value:function(){return this}}],[{key:"isUri",value:function(t){return t instanceof e||!!t&&("string"===typeof t.authority&&"string"===typeof t.fragment&&"string"===typeof t.path&&"string"===typeof t.query&&"string"===typeof t.scheme&&"string"===typeof t.fsPath&&"function"===typeof t.with&&"function"===typeof t.toString)}},{key:"parse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=Ke.exec(e);return n?new Ye(n[2]||qe,tt(n[4]||qe),tt(n[5]||qe),tt(n[7]||qe),tt(n[9]||qe),t):new Ye(qe,qe,qe,qe,qe)}},{key:"file",value:function(e){var t=qe;if(z&&(e=e.replace(/\\/g,Be)),e[0]===Be&&e[1]===Be){var n=e.indexOf(Be,2);-1===n?(t=e.substring(2),e=Be):(t=e.substring(2,n),e=e.substring(n)||Be)}return new Ye("file",t,e,qe,qe)}},{key:"from",value:function(e){var t=new Ye(e.scheme,e.authority,e.path,e.query,e.fragment);return We(t,!0),t}},{key:"joinPath",value:function(t){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");for(var n,r,i,o=arguments.length,a=new Array(o>1?o-1:0),s=1;s<o;s++)a[s-1]=arguments[s];z&&"file"===t.scheme?n=e.file((r=Re).join.apply(r,[Qe(t,!0)].concat(a))).path:n=(i=De).join.apply(i,[t.path].concat(a));return t.with({path:n})}},{key:"revive",value:function(t){if(t){if(t instanceof e)return t;var n=new Ye(t);return n._formatted=t.external,n._fsPath=t._sep===He?t.fsPath:null,n}return t}}]),e}(),He=z?1:void 0,Ye=function(e){d(r,e);var n=v(r);function r(){var e;return t(this,r),(e=n.apply(this,arguments))._formatted=null,e._fsPath=null,e}return o(r,[{key:"fsPath",get:function(){return this._fsPath||(this._fsPath=Qe(this,!1)),this._fsPath}},{key:"toString",value:function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?Xe(this,!0):(this._formatted||(this._formatted=Xe(this,!1)),this._formatted)}},{key:"toJSON",value:function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=He),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}]),r}($e),ze=(x(je={},58,"%3A"),x(je,47,"%2F"),x(je,63,"%3F"),x(je,35,"%23"),x(je,91,"%5B"),x(je,93,"%5D"),x(je,64,"%40"),x(je,33,"%21"),x(je,36,"%24"),x(je,38,"%26"),x(je,39,"%27"),x(je,40,"%28"),x(je,41,"%29"),x(je,42,"%2A"),x(je,43,"%2B"),x(je,44,"%2C"),x(je,59,"%3B"),x(je,61,"%3D"),x(je,32,"%20"),je);function Ge(e,t){for(var n=void 0,r=-1,i=0;i<e.length;i++){var o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));var a=ze[o];void 0!==a?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=a):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function Je(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=ze[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function Qe(e,t){var n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?"//".concat(e.authority).concat(e.path):47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,z&&(n=n.replace(/\//g,"\\")),n}function Xe(e,t){var n=t?Je:Ge,r="",i=e.scheme,o=e.authority,a=e.path,s=e.query,u=e.fragment;if(i&&(r+=i,r+=":"),(o||"file"===i)&&(r+=Be,r+=Be),o){var c=o.indexOf("@");if(-1!==c){var l=o.substr(0,c);o=o.substr(c+1),-1===(c=l.indexOf(":"))?r+=n(l,!1):(r+=n(l.substr(0,c),!1),r+=":",r+=n(l.substr(c+1),!1)),r+="@"}-1===(c=(o=o.toLowerCase()).indexOf(":"))?r+=n(o,!1):(r+=n(o.substr(0,c),!1),r+=o.substr(c))}if(a){if(a.length>=3&&47===a.charCodeAt(0)&&58===a.charCodeAt(2)){var f=a.charCodeAt(1);f>=65&&f<=90&&(a="/".concat(String.fromCharCode(f+32),":").concat(a.substr(3)))}else if(a.length>=2&&58===a.charCodeAt(1)){var h=a.charCodeAt(0);h>=65&&h<=90&&(a="".concat(String.fromCharCode(h+32),":").concat(a.substr(2)))}r+=n(a,!0)}return s&&(r+="?",r+=n(s,!1)),u&&(r+="#",r+=t?u:Ge(u,!1)),r}function Ze(e){try{return decodeURIComponent(e)}catch(D){return e.length>3?e.substr(0,3)+Ze(e.substr(3)):e}}var et=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function tt(e){return e.match(et)?e.replace(et,(function(e){return Ze(e)})):e}var nt=function(){function e(n,r){t(this,e),this.lineNumber=n,this.column=r}return o(e,[{key:"with",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.lineNumber,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.column;return t===this.lineNumber&&n===this.column?this:new e(t,n)}},{key:"delta",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.with(this.lineNumber+e,this.column+t)}},{key:"equals",value:function(t){return e.equals(this,t)}},{key:"isBefore",value:function(t){return e.isBefore(this,t)}},{key:"isBeforeOrEqual",value:function(t){return e.isBeforeOrEqual(this,t)}},{key:"clone",value:function(){return new e(this.lineNumber,this.column)}},{key:"toString",value:function(){return"("+this.lineNumber+","+this.column+")"}}],[{key:"equals",value:function(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}},{key:"isBefore",value:function(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column}},{key:"isBeforeOrEqual",value:function(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column}},{key:"compare",value:function(e,t){var n=0|e.lineNumber,r=0|t.lineNumber;return n===r?(0|e.column)-(0|t.column):n-r}},{key:"lift",value:function(t){return new e(t.lineNumber,t.column)}},{key:"isIPosition",value:function(e){return e&&"number"===typeof e.lineNumber&&"number"===typeof e.column}}]),e}(),rt=function(){function e(n,r,i,o){t(this,e),n>i||n===i&&r>o?(this.startLineNumber=i,this.startColumn=o,this.endLineNumber=n,this.endColumn=r):(this.startLineNumber=n,this.startColumn=r,this.endLineNumber=i,this.endColumn=o)}return o(e,[{key:"isEmpty",value:function(){return e.isEmpty(this)}},{key:"containsPosition",value:function(t){return e.containsPosition(this,t)}},{key:"containsRange",value:function(t){return e.containsRange(this,t)}},{key:"strictContainsRange",value:function(t){return e.strictContainsRange(this,t)}},{key:"plusRange",value:function(t){return e.plusRange(this,t)}},{key:"intersectRanges",value:function(t){return e.intersectRanges(this,t)}},{key:"equalsRange",value:function(t){return e.equalsRange(this,t)}},{key:"getEndPosition",value:function(){return e.getEndPosition(this)}},{key:"getStartPosition",value:function(){return e.getStartPosition(this)}},{key:"toString",value:function(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}},{key:"setEndPosition",value:function(t,n){return new e(this.startLineNumber,this.startColumn,t,n)}},{key:"setStartPosition",value:function(t,n){return new e(t,n,this.endLineNumber,this.endColumn)}},{key:"collapseToStart",value:function(){return e.collapseToStart(this)}}],[{key:"isEmpty",value:function(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}},{key:"containsPosition",value:function(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>e.endColumn))}},{key:"containsRange",value:function(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)))}},{key:"strictContainsRange",value:function(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)))}},{key:"plusRange",value:function(t,n){var r,i,o,a;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(o=n.endLineNumber,a=n.endColumn):n.endLineNumber===t.endLineNumber?(o=n.endLineNumber,a=Math.max(n.endColumn,t.endColumn)):(o=t.endLineNumber,a=t.endColumn),new e(r,i,o,a)}},{key:"intersectRanges",value:function(t,n){var r=t.startLineNumber,i=t.startColumn,o=t.endLineNumber,a=t.endColumn,s=n.startLineNumber,u=n.startColumn,c=n.endLineNumber,l=n.endColumn;return r<s?(r=s,i=u):r===s&&(i=Math.max(i,u)),o>c?(o=c,a=l):o===c&&(a=Math.min(a,l)),r>o||r===o&&i>a?null:new e(r,i,o,a)}},{key:"equalsRange",value:function(e,t){return!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}},{key:"getEndPosition",value:function(e){return new nt(e.endLineNumber,e.endColumn)}},{key:"getStartPosition",value:function(e){return new nt(e.startLineNumber,e.startColumn)}},{key:"collapseToStart",value:function(t){return new e(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}},{key:"fromPositions",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t;return new e(t.lineNumber,t.column,n.lineNumber,n.column)}},{key:"lift",value:function(t){return t?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}},{key:"isIRange",value:function(e){return e&&"number"===typeof e.startLineNumber&&"number"===typeof e.startColumn&&"number"===typeof e.endLineNumber&&"number"===typeof e.endColumn}},{key:"areIntersectingOrTouching",value:function(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}},{key:"areIntersecting",value:function(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}},{key:"compareRangesUsingStarts",value:function(e,t){if(e&&t){var n=0|e.startLineNumber,r=0|t.startLineNumber;if(n===r){var i=0|e.startColumn,o=0|t.startColumn;if(i===o){var a=0|e.endLineNumber,s=0|t.endLineNumber;return a===s?(0|e.endColumn)-(0|t.endColumn):a-s}return i-o}return n-r}return(e?1:0)-(t?1:0)}},{key:"compareRangesUsingEnds",value:function(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}},{key:"spansMultipleLines",value:function(e){return e.endLineNumber>e.startLineNumber}}]),e}();function it(e,t,n,r){return new _e(e,t,n).ComputeDiff(r)}var ot=function(){function e(n){t(this,e);for(var r=[],i=[],o=0,a=n.length;o<a;o++)r[o]=lt(n[o],1),i[o]=ft(n[o],1);this.lines=n,this._startColumns=r,this._endColumns=i}return o(e,[{key:"getElements",value:function(){for(var e=[],t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}},{key:"getStrictElement",value:function(e){return this.lines[e]}},{key:"getStartLineNumber",value:function(e){return e+1}},{key:"getEndLineNumber",value:function(e){return e+1}},{key:"createCharSequence",value:function(e,t,n){for(var r=[],i=[],o=[],a=0,s=t;s<=n;s++)for(var u=this.lines[s],c=e?this._startColumns[s]:1,l=e?this._endColumns[s]:u.length+1,f=c;f<l;f++)r[a]=u.charCodeAt(f-1),i[a]=s+1,o[a]=f,a++;return new at(r,i,o)}}]),e}(),at=function(){function e(n,r,i){t(this,e),this._charCodes=n,this._lineNumbers=r,this._columns=i}return o(e,[{key:"getElements",value:function(){return this._charCodes}},{key:"getStartLineNumber",value:function(e){return this._lineNumbers[e]}},{key:"getStartColumn",value:function(e){return this._columns[e]}},{key:"getEndLineNumber",value:function(e){return this._lineNumbers[e]}},{key:"getEndColumn",value:function(e){return this._columns[e]+1}}]),e}(),st=function(){function e(n,r,i,o,a,s,u,c){t(this,e),this.originalStartLineNumber=n,this.originalStartColumn=r,this.originalEndLineNumber=i,this.originalEndColumn=o,this.modifiedStartLineNumber=a,this.modifiedStartColumn=s,this.modifiedEndLineNumber=u,this.modifiedEndColumn=c}return o(e,null,[{key:"createFromDiffChange",value:function(t,n,r){var i,o,a,s,u,c,l,f;return 0===t.originalLength?(i=0,o=0,a=0,s=0):(i=n.getStartLineNumber(t.originalStart),o=n.getStartColumn(t.originalStart),a=n.getEndLineNumber(t.originalStart+t.originalLength-1),s=n.getEndColumn(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(u=0,c=0,l=0,f=0):(u=r.getStartLineNumber(t.modifiedStart),c=r.getStartColumn(t.modifiedStart),l=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),f=r.getEndColumn(t.modifiedStart+t.modifiedLength-1)),new e(i,o,a,s,u,c,l,f)}}]),e}();var ut=function(){function e(n,r,i,o,a){t(this,e),this.originalStartLineNumber=n,this.originalEndLineNumber=r,this.modifiedStartLineNumber=i,this.modifiedEndLineNumber=o,this.charChanges=a}return o(e,null,[{key:"createFromDiffResult",value:function(t,n,r,i,o,a,s){var u,c,l,f,h=void 0;if(0===n.originalLength?(u=r.getStartLineNumber(n.originalStart)-1,c=0):(u=r.getStartLineNumber(n.originalStart),c=r.getEndLineNumber(n.originalStart+n.originalLength-1)),0===n.modifiedLength?(l=i.getStartLineNumber(n.modifiedStart)-1,f=0):(l=i.getStartLineNumber(n.modifiedStart),f=i.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),a&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&o()){var d=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),m=i.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1),g=it(d,m,o,!0).changes;s&&(g=function(e){if(e.length<=1)return e;for(var t=[e[0]],n=t[0],r=1,i=e.length;r<i;r++){var o=e[r],a=o.originalStart-(n.originalStart+n.originalLength),s=o.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(a,s)<3?(n.originalLength=o.originalStart+o.originalLength-n.originalStart,n.modifiedLength=o.modifiedStart+o.modifiedLength-n.modifiedStart):(t.push(o),n=o)}return t}(g)),h=[];for(var p=0,v=g.length;p<v;p++)h.push(st.createFromDiffChange(g[p],d,m))}return new e(u,c,l,f,h)}}]),e}(),ct=function(){function e(n,r,i){t(this,e),this.shouldComputeCharChanges=i.shouldComputeCharChanges,this.shouldPostProcessCharChanges=i.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=i.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=i.shouldMakePrettyDiff,this.originalLines=n,this.modifiedLines=r,this.original=new ot(n),this.modified=new ot(r),this.continueLineDiff=ht(i.maxComputationTime),this.continueCharDiff=ht(0===i.maxComputationTime?0:Math.min(i.maxComputationTime,5e3))}return o(e,[{key:"computeDiff",value:function(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return 1===this.modified.lines.length&&0===this.modified.lines[0].length?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};var e=it(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){for(var r=[],i=0,o=t.length;i<o;i++)r.push(ut.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[i],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:r}}for(var a=[],s=0,u=0,c=-1,l=t.length;c<l;c++){for(var f=c+1<l?t[c+1]:null,h=f?f.originalStart:this.originalLines.length,d=f?f.modifiedStart:this.modifiedLines.length;s<h&&u<d;){var m=this.originalLines[s],g=this.modifiedLines[u];if(m!==g){for(var p=lt(m,1),v=lt(g,1);p>1&&v>1;){if(m.charCodeAt(p-2)!==g.charCodeAt(v-2))break;p--,v--}(p>1||v>1)&&this._pushTrimWhitespaceCharChange(a,s+1,1,p,u+1,1,v);for(var y=ft(m,1),b=ft(g,1),_=m.length+1,C=g.length+1;y<_&&b<C;){if(m.charCodeAt(y-1)!==m.charCodeAt(b-1))break;y++,b++}(y<_||b<C)&&this._pushTrimWhitespaceCharChange(a,s+1,y,_,u+1,b,C)}s++,u++}f&&(a.push(ut.createFromDiffResult(this.shouldIgnoreTrimWhitespace,f,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),s+=f.originalLength,u+=f.modifiedLength)}return{quitEarly:n,changes:a}}},{key:"_pushTrimWhitespaceCharChange",value:function(e,t,n,r,i,o,a){if(!this._mergeTrimWhitespaceCharChange(e,t,n,r,i,o,a)){var s=void 0;this.shouldComputeCharChanges&&(s=[new st(t,n,t,r,i,o,i,a)]),e.push(new ut(t,t,i,i,s))}}},{key:"_mergeTrimWhitespaceCharChange",value:function(e,t,n,r,i,o,a){var s=e.length;if(0===s)return!1;var u=e[s-1];return 0!==u.originalEndLineNumber&&0!==u.modifiedEndLineNumber&&(u.originalEndLineNumber+1===t&&u.modifiedEndLineNumber+1===i&&(u.originalEndLineNumber=t,u.modifiedEndLineNumber=i,this.shouldComputeCharChanges&&u.charChanges&&u.charChanges.push(new st(t,n,t,r,i,o,i,a)),!0))}}]),e}();function lt(e,t){var n=function(e){for(var t=0,n=e.length;t<n;t++){var r=e.charCodeAt(t);if(32!==r&&9!==r)return t}return-1}(e);return-1===n?t:n+1}function ft(e,t){var n=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.length-1;t>=0;t--){var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}return-1}(e);return-1===n?t:n+2}function ht(e){if(0===e)return function(){return!0};var t=Date.now();return function(){return Date.now()-t<e}}function dt(e){return e<0?0:e>255?255:0|e}function mt(e){return e<0?0:e>4294967295?4294967295:0|e}var gt=o((function e(n,r){t(this,e),this._prefixSumIndexOfResultBrand=void 0,this.index=n,this.remainder=r})),pt=function(){function e(n){t(this,e),this.values=n,this.prefixSum=new Uint32Array(n.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}return o(e,[{key:"insertValues",value:function(e,t){e=mt(e);var n=this.values,r=this.prefixSum,i=t.length;return 0!==i&&(this.values=new Uint32Array(n.length+i),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+i),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}},{key:"changeValue",value:function(e,t){return e=mt(e),t=mt(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}},{key:"removeValues",value:function(e,t){e=mt(e),t=mt(t);var n=this.values,r=this.prefixSum;if(e>=n.length)return!1;var i=n.length-e;return t>=i&&(t=i),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}},{key:"getTotalSum",value:function(){return 0===this.values.length?0:this._getPrefixSum(this.values.length-1)}},{key:"getPrefixSum",value:function(e){return e<0?0:(e=mt(e),this._getPrefixSum(e))}},{key:"_getPrefixSum",value:function(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];var t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(var n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}},{key:"getIndexOf",value:function(e){e=Math.floor(e),this.getTotalSum();for(var t=0,n=this.values.length-1,r=0,i=0,o=0;t<=n;)if(r=t+(n-t)/2|0,e<(o=(i=this.prefixSum[r])-this.values[r]))n=r-1;else{if(!(e>=i))break;t=r+1}return new gt(r,e-o)}}]),e}(),vt=function(){function e(n,r,i,o){t(this,e),this._uri=n,this._lines=r,this._eol=i,this._versionId=o,this._lineStarts=null,this._cachedTextValue=null}return o(e,[{key:"dispose",value:function(){this._lines.length=0}},{key:"version",get:function(){return this._versionId}},{key:"getText",value:function(){return null===this._cachedTextValue&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}},{key:"onEvents",value:function(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);var t,n=S(e.changes);try{for(n.s();!(t=n.n()).done;){var r=t.value;this._acceptDeleteRange(r.range),this._acceptInsertText(new nt(r.range.startLineNumber,r.range.startColumn),r.text)}}catch(i){n.e(i)}finally{n.f()}this._versionId=e.versionId,this._cachedTextValue=null}},{key:"_ensureLineStarts",value:function(){if(!this._lineStarts){for(var e=this._eol.length,t=this._lines.length,n=new Uint32Array(t),r=0;r<t;r++)n[r]=this._lines[r].length+e;this._lineStarts=new pt(n)}}},{key:"_setLineText",value:function(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.changeValue(e,this._lines[e].length+this._eol.length)}},{key:"_acceptDeleteRange",value:function(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}}},{key:"_acceptInsertText",value:function(e,t){if(0!==t.length){var n=t.split(/\r\n|\r|\n/);if(1!==n.length){n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);for(var r=new Uint32Array(n.length-1),i=1;i<n.length;i++)this._lines.splice(e.lineNumber+i-1,0,n[i]),r[i-1]=n[i].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,r)}else this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1))}}}]),e}();var yt=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n="(-?\\d*\\.\\d\\w*)|([^",r=S("`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?");try{for(r.s();!(e=r.n()).done;){var i=e.value;t.indexOf(i)>=0||(n+="\\"+i)}}catch(o){r.e(o)}finally{r.f()}return n+="\\s]+)",new RegExp(n,"g")}();var bt={maxLen:1e3,windowSize:15,timeBudget:150};function _t(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:bt;if(n.length>i.maxLen){var o=e-i.maxLen/2;return o<0?o=0:r+=o,_t(e,t,n=n.substring(o,e+i.maxLen/2),r,i)}for(var a=Date.now(),s=e-1-r,u=-1,c=null,l=1;!(Date.now()-a>=i.timeBudget);l++){var f=s-i.windowSize*l;t.lastIndex=Math.max(0,f);var h=Ct(t,n,s,u);if(!h&&c)break;if(c=h,f<=0)break;u=f}if(c){var d={word:c[0],startColumn:r+1+c.index,endColumn:r+1+c.index+c[0].length};return t.lastIndex=0,d}return null}function Ct(e,t,n,r){for(var i;i=e.exec(t);){var o=i.index||0;if(o<=n&&e.lastIndex>=n)return i;if(r>0&&o>r)return null}return null}var St=function(){function e(n){t(this,e);var r=dt(n);this._defaultValue=r,this._asciiMap=e._createAsciiMap(r),this._map=new Map}return o(e,[{key:"set",value:function(e,t){var n=dt(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}},{key:"get",value:function(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}}],[{key:"_createAsciiMap",value:function(e){for(var t=new Uint8Array(256),n=0;n<256;n++)t[n]=e;return t}}]),e}(),kt=function(){function e(n,r,i){t(this,e);for(var o=new Uint8Array(n*r),a=0,s=n*r;a<s;a++)o[a]=i;this._data=o,this.rows=n,this.cols=r}return o(e,[{key:"get",value:function(e,t){return this._data[e*this.cols+t]}},{key:"set",value:function(e,t,n){this._data[e*this.cols+t]=n}}]),e}(),xt=function(){function e(n){t(this,e);for(var r=0,i=0,o=0,a=n.length;o<a;o++){var s=ne(n[o],3),u=s[0],c=s[1],l=s[2];c>r&&(r=c),u>i&&(i=u),l>i&&(i=l)}r++,i++;for(var f=new kt(i,r,0),h=0,d=n.length;h<d;h++){var m=ne(n[h],3),g=m[0],p=m[1],v=m[2];f.set(g,p,v)}this._states=f,this._maxCharCode=r}return o(e,[{key:"nextState",value:function(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}}]),e}(),At=null;var Et=null;var wt=function(){function e(){t(this,e)}return o(e,null,[{key:"_createLink",value:function(e,t,n,r,i){var o=i-1;do{var a=t.charCodeAt(o);if(2!==e.get(a))break;o--}while(o>r);if(r>0){var s=t.charCodeAt(r-1),u=t.charCodeAt(o);(40===s&&41===u||91===s&&93===u||123===s&&125===u)&&o--}return{range:{startLineNumber:n,startColumn:r+1,endLineNumber:n,endColumn:o+2},url:t.substring(r,o+1)}}},{key:"computeLinks",value:function(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(null===At&&(At=new xt([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),At),r=function(){if(null===Et){Et=new St(0);for(var e=" \t<>'\"\u3001\u3002\uff61\uff64\uff0c\uff0e\uff1a\uff1b\u2018\u3008\u300c\u300e\u3014\uff08\uff3b\uff5b\uff62\uff63\uff5d\uff3d\uff09\u3015\u300f\u300d\u3009\u2019\uff40\uff5e\u2026",t=0;t<35;t++)Et.set(e.charCodeAt(t),1);for(var n=0;n<3;n++)Et.set(".,;".charCodeAt(n),2)}return Et}(),i=[],o=1,a=t.getLineCount();o<=a;o++){for(var s=t.getLineContent(o),u=s.length,c=0,l=0,f=0,h=1,d=!1,m=!1,g=!1,p=!1;c<u;){var v=!1,y=s.charCodeAt(c);if(13===h){var b=void 0;switch(y){case 40:d=!0,b=0;break;case 41:b=d?0:1;break;case 91:g=!0,m=!0,b=0;break;case 93:g=!1,b=m?0:1;break;case 123:p=!0,b=0;break;case 125:b=p?0:1;break;case 39:b=34===f||96===f?0:1;break;case 34:b=39===f||96===f?0:1;break;case 96:b=39===f||34===f?0:1;break;case 42:b=42===f?1:0;break;case 124:b=124===f?1:0;break;case 32:b=g?0:1;break;default:b=r.get(y)}1===b&&(i.push(e._createLink(r,s,o,l,c)),v=!0)}else if(12===h){var _=void 0;91===y?(m=!0,_=0):_=r.get(y),1===_?v=!0:h=13}else 0===(h=n.nextState(h,y))&&(v=!0);v&&(h=1,d=!1,m=!1,p=!1,l=c+1,f=y),c++}13===h&&i.push(e._createLink(r,s,o,l,u))}return i}}]),e}();function Nt(e){return e&&"function"===typeof e.getLineCount&&"function"===typeof e.getLineContent?wt.computeLinks(e):[]}var Lt=function(){function e(){t(this,e),this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}return o(e,[{key:"navigateValueSet",value:function(e,t,n,r,i){if(e&&t){var o=this.doNavigateValueSet(t,i);if(o)return{range:e,value:o}}if(n&&r){var a=this.doNavigateValueSet(r,i);if(a)return{range:n,value:a}}return null}},{key:"doNavigateValueSet",value:function(e,t){var n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)}},{key:"numberReplace",value:function(e,t){var n=Math.pow(10,e.length-(e.lastIndexOf(".")+1)),r=Number(e),i=parseFloat(e);return isNaN(r)||isNaN(i)||r!==i?null:0!==r||t?(r=Math.floor(r*n),r+=t?n:-n,String(r/n)):null}},{key:"textReplace",value:function(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}},{key:"valueSetsReplace",value:function(e,t,n){for(var r=null,i=0,o=e.length;null===r&&i<o;i++)r=this.valueSetReplace(e[i],t,n);return r}},{key:"valueSetReplace",value:function(e,t,n){var r=e.indexOf(t);return r>=0?((r+=n?1:-1)<0?r=e.length-1:r%=e.length,e[r]):null}}]),e}();function Tt(e){return function(e){if(Array.isArray(e))return _(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||C(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}Lt.INSTANCE=new Lt;var Ot=o((function e(n){t(this,e),this.element=n,this.next=e.Undefined,this.prev=e.Undefined}));Ot.Undefined=new Ot(void 0);var It,Pt=function(e){function n(){t(this,n),this._first=Ot.Undefined,this._last=Ot.Undefined,this._size=0}return o(n,[{key:"size",get:function(){return this._size}},{key:"isEmpty",value:function(){return this._first===Ot.Undefined}},{key:"clear",value:function(){for(var e=this._first;e!==Ot.Undefined;){var t=e.next;e.prev=Ot.Undefined,e.next=Ot.Undefined,e=t}this._first=Ot.Undefined,this._last=Ot.Undefined,this._size=0}},{key:"unshift",value:function(e){return this._insert(e,!1)}},{key:"push",value:function(e){return this._insert(e,!0)}},{key:"_insert",value:function(e,t){var n=this,r=new Ot(e);if(this._first===Ot.Undefined)this._first=r,this._last=r;else if(t){var i=this._last;this._last=r,r.prev=i,i.next=r}else{var o=this._first;this._first=r,r.next=o,o.prev=r}this._size+=1;var a=!1;return function(){a||(a=!0,n._remove(r))}}},{key:"shift",value:function(){if(this._first!==Ot.Undefined){var e=this._first.element;return this._remove(this._first),e}}},{key:"pop",value:function(){if(this._last!==Ot.Undefined){var e=this._last.element;return this._remove(this._last),e}}},{key:"_remove",value:function(e){if(e.prev!==Ot.Undefined&&e.next!==Ot.Undefined){var t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===Ot.Undefined&&e.next===Ot.Undefined?(this._first=Ot.Undefined,this._last=Ot.Undefined):e.next===Ot.Undefined?(this._last=this._last.prev,this._last.next=Ot.Undefined):e.prev===Ot.Undefined&&(this._first=this._first.next,this._first.prev=Ot.Undefined);this._size-=1}},{key:Symbol.iterator,value:A().mark((function e(){var t;return A().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this._first;case 1:if(t===Ot.Undefined){e.next=7;break}return e.next=4,t.element;case 4:t=t.next,e.next=1;break;case 7:case"end":return e.stop()}}),e,this)}))}]),n}(),Mt=q.performance&&"function"===typeof q.performance.now,Rt=function(){function e(n){t(this,e),this._highResolution=Mt&&n,this._startTime=this._now(),this._stopTime=-1}return o(e,[{key:"stop",value:function(){this._stopTime=this._now()}},{key:"elapsed",value:function(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime}},{key:"_now",value:function(){return this._highResolution?q.performance.now():Date.now()}}],[{key:"create",value:function(){return new e(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])}}]),e}();!function(e){function n(e){return function(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=!1;return n=e((function(e){if(!i)return n?n.dispose():i=!0,t.call(r,e)}),null,arguments.length>2?arguments[2]:void 0),i&&n.dispose(),n}}function r(e,t){return u((function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e((function(e){return n.call(r,t(e))}),null,arguments.length>2?arguments[2]:void 0)}))}function i(e,t){return u((function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e((function(e){t(e),n.call(r,e)}),null,arguments.length>2?arguments[2]:void 0)}))}function a(e,t){return u((function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e((function(e){return t(e)&&n.call(r,e)}),null,arguments.length>2?arguments[2]:void 0)}))}function s(e,t,n){var i=n;return r(e,(function(e){return i=t(i,e)}))}function u(e){var t,n=new Ft({onFirstListenerAdd:function(){t=e(n.fire,n)},onLastListenerRemove:function(){t.dispose()}});return n.event}function c(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=void 0,a=void 0,s=0,u=new Ft({leakWarningThreshold:arguments.length>4?arguments[4]:void 0,onFirstListenerAdd:function(){n=e((function(e){s++,o=t(o,e),i&&!a&&(u.fire(o),o=void 0),clearTimeout(a),a=setTimeout((function(){var e=o;o=void 0,a=void 0,(!i||s>1)&&u.fire(e),s=0}),r)}))},onLastListenerRemove:function(){n.dispose()}});return u.event}function l(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e,t){return e===t},r=!0;return a(e,(function(e){var i=r||!n(e,t);return r=!1,t=e,i}))}e.None=function(){return R.None},e.once=n,e.map=r,e.forEach=i,e.filter=a,e.signal=function(e){return e},e.any=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2?arguments[2]:void 0;return I.apply(void 0,Tt(t.map((function(t){return t((function(t){return e.call(n,t)}),null,r)}))))}},e.reduce=s,e.debounce=c,e.latch=l,e.split=function(t,n){return[e.filter(t,n),e.filter(t,(function(e){return!n(e)}))]},e.buffer=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:[]).slice(),r=e((function(e){n?n.push(e):o.fire(e)})),i=function(){n&&n.forEach((function(e){return o.fire(e)})),n=null},o=new Ft({onFirstListenerAdd:function(){r||(r=e((function(e){return o.fire(e)})))},onFirstListenerDidAdd:function(){n&&(t?setTimeout(i):i())},onLastListenerRemove:function(){r&&r.dispose(),r=null}});return o.event};var f=function(){function e(n){t(this,e),this.event=n}return o(e,[{key:"map",value:function(t){return new e(r(this.event,t))}},{key:"forEach",value:function(t){return new e(i(this.event,t))}},{key:"filter",value:function(t){return new e(a(this.event,t))}},{key:"reduce",value:function(t,n){return new e(s(this.event,t,n))}},{key:"latch",value:function(){return new e(l(this.event))}},{key:"debounce",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3?arguments[3]:void 0;return new e(c(this.event,t,n,r,i))}},{key:"on",value:function(e,t,n){return this.event(e,t,n)}},{key:"once",value:function(e,t,r){return n(this.event)(e,t,r)}}]),e}();e.chain=function(e){return new f(e)},e.fromNodeEventEmitter=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){return e},r=function(){return i.fire(n.apply(void 0,arguments))},i=new Ft({onFirstListenerAdd:function(){return e.on(t,r)},onLastListenerRemove:function(){return e.removeListener(t,r)}});return i.event},e.fromDOMEventEmitter=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){return e},r=function(){return i.fire(n.apply(void 0,arguments))},i=new Ft({onFirstListenerAdd:function(){return e.addEventListener(t,r)},onLastListenerRemove:function(){return e.removeEventListener(t,r)}});return i.event},e.toPromise=function(e){return new Promise((function(t){return n(e)(t)}))}}(It||(It={}));var Dt=function(){function e(n){t(this,e),this._listenerCount=0,this._invocationCount=0,this._elapsedOverall=0,this._name="".concat(n,"_").concat(e._idPool++)}return o(e,[{key:"start",value:function(e){this._stopWatch=new Rt(!0),this._listenerCount=e}},{key:"stop",value:function(){if(this._stopWatch){var e=this._stopWatch.elapsed();this._elapsedOverall+=e,this._invocationCount+=1,console.info("did FIRE ".concat(this._name,": elapsed_ms: ").concat(e.toFixed(5),", listener: ").concat(this._listenerCount," (elapsed_overall: ").concat(this._elapsedOverall.toFixed(2),", invocations: ").concat(this._invocationCount,")")),this._stopWatch=void 0}}}]),e}();Dt._idPool=0;var jt,Ft=function(){function e(n){var r;t(this,e),this._disposed=!1,this._options=n,this._leakageMon=void 0,this._perfMon=(null===(r=this._options)||void 0===r?void 0:r._profName)?new Dt(this._options._profName):void 0}return o(e,[{key:"event",get:function(){var e=this;return this._event||(this._event=function(t,n,r){var i;e._listeners||(e._listeners=new Pt);var o=e._listeners.isEmpty();o&&e._options&&e._options.onFirstListenerAdd&&e._options.onFirstListenerAdd(e);var a=e._listeners.push(n?[t,n]:t);o&&e._options&&e._options.onFirstListenerDidAdd&&e._options.onFirstListenerDidAdd(e),e._options&&e._options.onListenerDidAdd&&e._options.onListenerDidAdd(e,t,n);var s=null===(i=e._leakageMon)||void 0===i?void 0:i.check(e._listeners.size),u=P((function(){(s&&s(),e._disposed)||(a(),e._options&&e._options.onLastListenerRemove&&(e._listeners&&!e._listeners.isEmpty()||e._options.onLastListenerRemove(e)))}));return r instanceof M?r.add(u):Array.isArray(r)&&r.push(u),u}),this._event}},{key:"fire",value:function(e){var t,n;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new Pt);var r,i=S(this._listeners);try{for(i.s();!(r=i.n()).done;){var o=r.value;this._deliveryQueue.push([o,e])}}catch(l){i.e(l)}finally{i.f()}for(null===(t=this._perfMon)||void 0===t||t.start(this._deliveryQueue.size);this._deliveryQueue.size>0;){var a=ne(this._deliveryQueue.shift(),2),u=a[0],c=a[1];try{"function"===typeof u?u.call(void 0,c):u[0].call(u[1],c)}catch($o){s($o)}}null===(n=this._perfMon)||void 0===n||n.stop()}}},{key:"dispose",value:function(){var e,t,n,r,i;this._disposed||(this._disposed=!0,null===(e=this._listeners)||void 0===e||e.clear(),null===(t=this._deliveryQueue)||void 0===t||t.clear(),null===(r=null===(n=this._options)||void 0===n?void 0:n.onLastListenerRemove)||void 0===r||r.call(n),null===(i=this._leakageMon)||void 0===i||i.dispose())}}]),e}(),Ut=Object.freeze((function(e,t){var n=setTimeout(e.bind(t),0);return{dispose:function(){clearTimeout(n)}}}));!function(e){e.isCancellationToken=function(t){return t===e.None||t===e.Cancelled||(t instanceof Wt||!(!t||"object"!==typeof t)&&("boolean"===typeof t.isCancellationRequested&&"function"===typeof t.onCancellationRequested))},e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:It.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Ut})}(jt||(jt={}));var Vt,Wt=function(){function e(){t(this,e),this._isCancelled=!1,this._emitter=null}return o(e,[{key:"cancel",value:function(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}},{key:"isCancellationRequested",get:function(){return this._isCancelled}},{key:"onCancellationRequested",get:function(){return this._isCancelled?Ut:(this._emitter||(this._emitter=new Ft),this._emitter.event)}},{key:"dispose",value:function(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}]),e}(),qt=function(){function e(n){t(this,e),this._token=void 0,this._parentListener=void 0,this._parentListener=n&&n.onCancellationRequested(this.cancel,this)}return o(e,[{key:"token",get:function(){return this._token||(this._token=new Wt),this._token}},{key:"cancel",value:function(){this._token?this._token instanceof Wt&&this._token.cancel():this._token=jt.Cancelled}},{key:"dispose",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof Wt&&this._token.dispose():this._token=jt.None}}]),e}(),Bt=function(){function e(){t(this,e),this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}return o(e,[{key:"define",value:function(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}},{key:"keyCodeToStr",value:function(e){return this._keyCodeToStr[e]}},{key:"strToKeyCode",value:function(e){return this._strToKeyCode[e.toLowerCase()]||0}}]),e}(),Kt=new Bt,$t=new Bt,Ht=new Bt;!function(){function e(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;Kt.define(e,t),$t.define(e,n),Ht.define(e,r)}e(0,"unknown"),e(1,"Backspace"),e(2,"Tab"),e(3,"Enter"),e(4,"Shift"),e(5,"Ctrl"),e(6,"Alt"),e(7,"PauseBreak"),e(8,"CapsLock"),e(9,"Escape"),e(10,"Space"),e(11,"PageUp"),e(12,"PageDown"),e(13,"End"),e(14,"Home"),e(15,"LeftArrow","Left"),e(16,"UpArrow","Up"),e(17,"RightArrow","Right"),e(18,"DownArrow","Down"),e(19,"Insert"),e(20,"Delete"),e(21,"0"),e(22,"1"),e(23,"2"),e(24,"3"),e(25,"4"),e(26,"5"),e(27,"6"),e(28,"7"),e(29,"8"),e(30,"9"),e(31,"A"),e(32,"B"),e(33,"C"),e(34,"D"),e(35,"E"),e(36,"F"),e(37,"G"),e(38,"H"),e(39,"I"),e(40,"J"),e(41,"K"),e(42,"L"),e(43,"M"),e(44,"N"),e(45,"O"),e(46,"P"),e(47,"Q"),e(48,"R"),e(49,"S"),e(50,"T"),e(51,"U"),e(52,"V"),e(53,"W"),e(54,"X"),e(55,"Y"),e(56,"Z"),e(57,"Meta"),e(58,"ContextMenu"),e(59,"F1"),e(60,"F2"),e(61,"F3"),e(62,"F4"),e(63,"F5"),e(64,"F6"),e(65,"F7"),e(66,"F8"),e(67,"F9"),e(68,"F10"),e(69,"F11"),e(70,"F12"),e(71,"F13"),e(72,"F14"),e(73,"F15"),e(74,"F16"),e(75,"F17"),e(76,"F18"),e(77,"F19"),e(78,"NumLock"),e(79,"ScrollLock"),e(80,";",";","OEM_1"),e(81,"=","=","OEM_PLUS"),e(82,",",",","OEM_COMMA"),e(83,"-","-","OEM_MINUS"),e(84,".",".","OEM_PERIOD"),e(85,"/","/","OEM_2"),e(86,"`","`","OEM_3"),e(110,"ABNT_C1"),e(111,"ABNT_C2"),e(87,"[","[","OEM_4"),e(88,"\\","\\","OEM_5"),e(89,"]","]","OEM_6"),e(90,"'","'","OEM_7"),e(91,"OEM_8"),e(92,"OEM_102"),e(93,"NumPad0"),e(94,"NumPad1"),e(95,"NumPad2"),e(96,"NumPad3"),e(97,"NumPad4"),e(98,"NumPad5"),e(99,"NumPad6"),e(100,"NumPad7"),e(101,"NumPad8"),e(102,"NumPad9"),e(103,"NumPad_Multiply"),e(104,"NumPad_Add"),e(105,"NumPad_Separator"),e(106,"NumPad_Subtract"),e(107,"NumPad_Decimal"),e(108,"NumPad_Divide")}(),function(e){e.toString=function(e){return Kt.keyCodeToStr(e)},e.fromString=function(e){return Kt.strToKeyCode(e)},e.toUserSettingsUS=function(e){return $t.keyCodeToStr(e)},e.toUserSettingsGeneral=function(e){return Ht.keyCodeToStr(e)},e.fromUserSettings=function(e){return $t.strToKeyCode(e)||Ht.strToKeyCode(e)}}(Vt||(Vt={}));var Yt,zt,Gt,Jt,Qt,Xt,Zt,en,tn,nn,rn,on,an,sn,un,cn,ln,fn,hn,dn,mn,gn,pn,vn,yn,bn,_n,Cn,Sn,kn,xn,An,En,wn,Nn,Ln=function(e){d(r,e);var n=v(r);function r(e,i,o,a){var s;return t(this,r),(s=n.call(this,e,i,o,a)).selectionStartLineNumber=e,s.selectionStartColumn=i,s.positionLineNumber=o,s.positionColumn=a,s}return o(r,[{key:"toString",value:function(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}},{key:"equalsSelection",value:function(e){return r.selectionsEqual(this,e)}},{key:"getDirection",value:function(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}},{key:"setEndPosition",value:function(e,t){return 0===this.getDirection()?new r(this.startLineNumber,this.startColumn,e,t):new r(e,t,this.startLineNumber,this.startColumn)}},{key:"getPosition",value:function(){return new nt(this.positionLineNumber,this.positionColumn)}},{key:"setStartPosition",value:function(e,t){return 0===this.getDirection()?new r(e,t,this.endLineNumber,this.endColumn):new r(this.endLineNumber,this.endColumn,e,t)}}],[{key:"selectionsEqual",value:function(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}},{key:"fromPositions",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;return new r(e.lineNumber,e.column,t.lineNumber,t.column)}},{key:"liftSelection",value:function(e){return new r(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}},{key:"selectionsArrEqual",value:function(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(var n=0,r=e.length;n<r;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}},{key:"isISelection",value:function(e){return e&&"number"===typeof e.selectionStartLineNumber&&"number"===typeof e.selectionStartColumn&&"number"===typeof e.positionLineNumber&&"number"===typeof e.positionColumn}},{key:"createWithDirection",value:function(e,t,n,i,o){return 0===o?new r(e,t,n,i):new r(n,i,e,t)}}]),r}(rt),Tn=function(){function e(n,r,i){t(this,e),this._tokenBrand=void 0,this.offset=0|n,this.type=r,this.language=i}return o(e,[{key:"toString",value:function(){return"("+this.offset+", "+this.type+")"}}]),e}();!function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"}(Yt||(Yt={})),function(e){e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"}(zt||(zt={})),function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"}(Gt||(Gt={})),function(e){e[e.Deprecated=1]="Deprecated"}(Jt||(Jt={})),function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"}(Qt||(Qt={})),function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"}(Xt||(Xt={})),function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"}(Zt||(Zt={})),function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(en||(en={})),function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(tn||(tn={})),function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"}(nn||(nn={})),function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.autoClosingDelete=6]="autoClosingDelete",e[e.autoClosingOvertype=7]="autoClosingOvertype",e[e.autoClosingQuotes=8]="autoClosingQuotes",e[e.autoIndent=9]="autoIndent",e[e.automaticLayout=10]="automaticLayout",e[e.autoSurround=11]="autoSurround",e[e.bracketPairColorization=12]="bracketPairColorization",e[e.codeLens=13]="codeLens",e[e.codeLensFontFamily=14]="codeLensFontFamily",e[e.codeLensFontSize=15]="codeLensFontSize",e[e.colorDecorators=16]="colorDecorators",e[e.columnSelection=17]="columnSelection",e[e.comments=18]="comments",e[e.contextmenu=19]="contextmenu",e[e.copyWithSyntaxHighlighting=20]="copyWithSyntaxHighlighting",e[e.cursorBlinking=21]="cursorBlinking",e[e.cursorSmoothCaretAnimation=22]="cursorSmoothCaretAnimation",e[e.cursorStyle=23]="cursorStyle",e[e.cursorSurroundingLines=24]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=25]="cursorSurroundingLinesStyle",e[e.cursorWidth=26]="cursorWidth",e[e.disableLayerHinting=27]="disableLayerHinting",e[e.disableMonospaceOptimizations=28]="disableMonospaceOptimizations",e[e.domReadOnly=29]="domReadOnly",e[e.dragAndDrop=30]="dragAndDrop",e[e.emptySelectionClipboard=31]="emptySelectionClipboard",e[e.extraEditorClassName=32]="extraEditorClassName",e[e.fastScrollSensitivity=33]="fastScrollSensitivity",e[e.find=34]="find",e[e.fixedOverflowWidgets=35]="fixedOverflowWidgets",e[e.folding=36]="folding",e[e.foldingStrategy=37]="foldingStrategy",e[e.foldingHighlight=38]="foldingHighlight",e[e.foldingImportsByDefault=39]="foldingImportsByDefault",e[e.unfoldOnClickAfterEndOfLine=40]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=41]="fontFamily",e[e.fontInfo=42]="fontInfo",e[e.fontLigatures=43]="fontLigatures",e[e.fontSize=44]="fontSize",e[e.fontWeight=45]="fontWeight",e[e.formatOnPaste=46]="formatOnPaste",e[e.formatOnType=47]="formatOnType",e[e.glyphMargin=48]="glyphMargin",e[e.gotoLocation=49]="gotoLocation",e[e.hideCursorInOverviewRuler=50]="hideCursorInOverviewRuler",e[e.highlightActiveIndentGuide=51]="highlightActiveIndentGuide",e[e.hover=52]="hover",e[e.inDiffEditor=53]="inDiffEditor",e[e.inlineSuggest=54]="inlineSuggest",e[e.letterSpacing=55]="letterSpacing",e[e.lightbulb=56]="lightbulb",e[e.lineDecorationsWidth=57]="lineDecorationsWidth",e[e.lineHeight=58]="lineHeight",e[e.lineNumbers=59]="lineNumbers",e[e.lineNumbersMinChars=60]="lineNumbersMinChars",e[e.linkedEditing=61]="linkedEditing",e[e.links=62]="links",e[e.matchBrackets=63]="matchBrackets",e[e.minimap=64]="minimap",e[e.mouseStyle=65]="mouseStyle",e[e.mouseWheelScrollSensitivity=66]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=67]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=68]="multiCursorMergeOverlapping",e[e.multiCursorModifier=69]="multiCursorModifier",e[e.multiCursorPaste=70]="multiCursorPaste",e[e.occurrencesHighlight=71]="occurrencesHighlight",e[e.overviewRulerBorder=72]="overviewRulerBorder",e[e.overviewRulerLanes=73]="overviewRulerLanes",e[e.padding=74]="padding",e[e.parameterHints=75]="parameterHints",e[e.peekWidgetDefaultFocus=76]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=77]="definitionLinkOpensInPeek",e[e.quickSuggestions=78]="quickSuggestions",e[e.quickSuggestionsDelay=79]="quickSuggestionsDelay",e[e.readOnly=80]="readOnly",e[e.renameOnType=81]="renameOnType",e[e.renderControlCharacters=82]="renderControlCharacters",e[e.renderIndentGuides=83]="renderIndentGuides",e[e.renderFinalNewline=84]="renderFinalNewline",e[e.renderLineHighlight=85]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=86]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=87]="renderValidationDecorations",e[e.renderWhitespace=88]="renderWhitespace",e[e.revealHorizontalRightPadding=89]="revealHorizontalRightPadding",e[e.roundedSelection=90]="roundedSelection",e[e.rulers=91]="rulers",e[e.scrollbar=92]="scrollbar",e[e.scrollBeyondLastColumn=93]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=94]="scrollBeyondLastLine",e[e.scrollPredominantAxis=95]="scrollPredominantAxis",e[e.selectionClipboard=96]="selectionClipboard",e[e.selectionHighlight=97]="selectionHighlight",e[e.selectOnLineNumbers=98]="selectOnLineNumbers",e[e.showFoldingControls=99]="showFoldingControls",e[e.showUnused=100]="showUnused",e[e.snippetSuggestions=101]="snippetSuggestions",e[e.smartSelect=102]="smartSelect",e[e.smoothScrolling=103]="smoothScrolling",e[e.stickyTabStops=104]="stickyTabStops",e[e.stopRenderingLineAfter=105]="stopRenderingLineAfter",e[e.suggest=106]="suggest",e[e.suggestFontSize=107]="suggestFontSize",e[e.suggestLineHeight=108]="suggestLineHeight",e[e.suggestOnTriggerCharacters=109]="suggestOnTriggerCharacters",e[e.suggestSelection=110]="suggestSelection",e[e.tabCompletion=111]="tabCompletion",e[e.tabIndex=112]="tabIndex",e[e.unusualLineTerminators=113]="unusualLineTerminators",e[e.useShadowDOM=114]="useShadowDOM",e[e.useTabStops=115]="useTabStops",e[e.wordSeparators=116]="wordSeparators",e[e.wordWrap=117]="wordWrap",e[e.wordWrapBreakAfterCharacters=118]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=119]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=120]="wordWrapColumn",e[e.wordWrapOverride1=121]="wordWrapOverride1",e[e.wordWrapOverride2=122]="wordWrapOverride2",e[e.wrappingIndent=123]="wrappingIndent",e[e.wrappingStrategy=124]="wrappingStrategy",e[e.showDeprecated=125]="showDeprecated",e[e.inlayHints=126]="inlayHints",e[e.editorClassName=127]="editorClassName",e[e.pixelRatio=128]="pixelRatio",e[e.tabFocusMode=129]="tabFocusMode",e[e.layoutInfo=130]="layoutInfo",e[e.wrappingInfo=131]="wrappingInfo"}(rn||(rn={})),function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(on||(on={})),function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"}(an||(an={})),function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"}(sn||(sn={})),function(e){e[e.Other=0]="Other",e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(un||(un={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(cn||(cn={})),function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.KEY_0=21]="KEY_0",e[e.KEY_1=22]="KEY_1",e[e.KEY_2=23]="KEY_2",e[e.KEY_3=24]="KEY_3",e[e.KEY_4=25]="KEY_4",e[e.KEY_5=26]="KEY_5",e[e.KEY_6=27]="KEY_6",e[e.KEY_7=28]="KEY_7",e[e.KEY_8=29]="KEY_8",e[e.KEY_9=30]="KEY_9",e[e.KEY_A=31]="KEY_A",e[e.KEY_B=32]="KEY_B",e[e.KEY_C=33]="KEY_C",e[e.KEY_D=34]="KEY_D",e[e.KEY_E=35]="KEY_E",e[e.KEY_F=36]="KEY_F",e[e.KEY_G=37]="KEY_G",e[e.KEY_H=38]="KEY_H",e[e.KEY_I=39]="KEY_I",e[e.KEY_J=40]="KEY_J",e[e.KEY_K=41]="KEY_K",e[e.KEY_L=42]="KEY_L",e[e.KEY_M=43]="KEY_M",e[e.KEY_N=44]="KEY_N",e[e.KEY_O=45]="KEY_O",e[e.KEY_P=46]="KEY_P",e[e.KEY_Q=47]="KEY_Q",e[e.KEY_R=48]="KEY_R",e[e.KEY_S=49]="KEY_S",e[e.KEY_T=50]="KEY_T",e[e.KEY_U=51]="KEY_U",e[e.KEY_V=52]="KEY_V",e[e.KEY_W=53]="KEY_W",e[e.KEY_X=54]="KEY_X",e[e.KEY_Y=55]="KEY_Y",e[e.KEY_Z=56]="KEY_Z",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.NumLock=78]="NumLock",e[e.ScrollLock=79]="ScrollLock",e[e.US_SEMICOLON=80]="US_SEMICOLON",e[e.US_EQUAL=81]="US_EQUAL",e[e.US_COMMA=82]="US_COMMA",e[e.US_MINUS=83]="US_MINUS",e[e.US_DOT=84]="US_DOT",e[e.US_SLASH=85]="US_SLASH",e[e.US_BACKTICK=86]="US_BACKTICK",e[e.US_OPEN_SQUARE_BRACKET=87]="US_OPEN_SQUARE_BRACKET",e[e.US_BACKSLASH=88]="US_BACKSLASH",e[e.US_CLOSE_SQUARE_BRACKET=89]="US_CLOSE_SQUARE_BRACKET",e[e.US_QUOTE=90]="US_QUOTE",e[e.OEM_8=91]="OEM_8",e[e.OEM_102=92]="OEM_102",e[e.NUMPAD_0=93]="NUMPAD_0",e[e.NUMPAD_1=94]="NUMPAD_1",e[e.NUMPAD_2=95]="NUMPAD_2",e[e.NUMPAD_3=96]="NUMPAD_3",e[e.NUMPAD_4=97]="NUMPAD_4",e[e.NUMPAD_5=98]="NUMPAD_5",e[e.NUMPAD_6=99]="NUMPAD_6",e[e.NUMPAD_7=100]="NUMPAD_7",e[e.NUMPAD_8=101]="NUMPAD_8",e[e.NUMPAD_9=102]="NUMPAD_9",e[e.NUMPAD_MULTIPLY=103]="NUMPAD_MULTIPLY",e[e.NUMPAD_ADD=104]="NUMPAD_ADD",e[e.NUMPAD_SEPARATOR=105]="NUMPAD_SEPARATOR",e[e.NUMPAD_SUBTRACT=106]="NUMPAD_SUBTRACT",e[e.NUMPAD_DECIMAL=107]="NUMPAD_DECIMAL",e[e.NUMPAD_DIVIDE=108]="NUMPAD_DIVIDE",e[e.KEY_IN_COMPOSITION=109]="KEY_IN_COMPOSITION",e[e.ABNT_C1=110]="ABNT_C1",e[e.ABNT_C2=111]="ABNT_C2",e[e.MAX_VALUE=112]="MAX_VALUE"}(ln||(ln={})),function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"}(fn||(fn={})),function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"}(hn||(hn={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(dn||(dn={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"}(mn||(mn={})),function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"}(gn||(gn={})),function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(pn||(pn={})),function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"}(vn||(vn={})),function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"}(yn||(yn={})),function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"}(bn||(bn={})),function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"}(_n||(_n={})),function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"}(Cn||(Cn={})),function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(Sn||(Sn={})),function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"}(kn||(kn={})),function(e){e[e.Deprecated=1]="Deprecated"}(xn||(xn={})),function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"}(An||(An={})),function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"}(En||(En={})),function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"}(wn||(wn={})),function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"}(Nn||(Nn={}));var On=function(){function e(){t(this,e)}return o(e,null,[{key:"chord",value:function(e,t){return function(e,t){return(e|(65535&t)<<16>>>0)>>>0}(e,t)}}]),e}();On.CtrlCmd=2048,On.Shift=1024,On.Alt=512,On.WinCtrl=256;var In=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{u(r.next(e))}catch($o){o($o)}}function s(e){try{u(r.throw(e))}catch($o){o($o)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}u((r=r.apply(e,t||[])).next())}))},Pn=function(e){d(r,e);var n=v(r);function r(){return t(this,r),n.apply(this,arguments)}return o(r,[{key:"uri",get:function(){return this._uri}},{key:"eol",get:function(){return this._eol}},{key:"getValue",value:function(){return this.getText()}},{key:"getLinesContent",value:function(){return this._lines.slice(0)}},{key:"getLineCount",value:function(){return this._lines.length}},{key:"getLineContent",value:function(e){return this._lines[e-1]}},{key:"getWordAtPosition",value:function(e,t){var n=_t(e.column,function(e){var t=yt;if(e&&e instanceof RegExp)if(e.global)t=e;else{var n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}(t),this._lines[e.lineNumber-1],0);return n?new rt(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}},{key:"words",value:function(e){var t=this._lines,n=this._wordenize.bind(this),r=0,i="",o=0,a=[];return x({},Symbol.iterator,A().mark((function s(){var u;return A().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:if(!(o<a.length)){s.next=8;break}return u=i.substring(a[o].start,a[o].end),o+=1,s.next=6,u;case 6:s.next=16;break;case 8:if(!(r<t.length)){s.next=15;break}i=t[r],a=n(i,e),o=0,r+=1,s.next=16;break;case 15:return s.abrupt("break",18);case 16:s.next=0;break;case 18:case"end":return s.stop()}}),s)})))}},{key:"getLineWords",value:function(e,t){var n,r=this._lines[e-1],i=[],o=S(this._wordenize(r,t));try{for(o.s();!(n=o.n()).done;){var a=n.value;i.push({word:r.substring(a.start,a.end),startColumn:a.start+1,endColumn:a.end+1})}}catch(s){o.e(s)}finally{o.f()}return i}},{key:"_wordenize",value:function(e,t){var n,r=[];for(t.lastIndex=0;(n=t.exec(e))&&0!==n[0].length;)r.push({start:n.index,end:n.index+n[0].length});return r}},{key:"getValueInRange",value:function(e){if((e=this._validateRange(e)).startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);var t=this._eol,n=e.startLineNumber-1,r=e.endLineNumber-1,i=[];i.push(this._lines[n].substring(e.startColumn-1));for(var o=n+1;o<r;o++)i.push(this._lines[o]);return i.push(this._lines[r].substring(0,e.endColumn-1)),i.join(t)}},{key:"offsetAt",value:function(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}},{key:"positionAt",value:function(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();var t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}},{key:"_validateRange",value:function(e){var t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}},{key:"_validatePosition",value:function(e){if(!nt.isIPosition(e))throw new Error("bad position");var t=e.lineNumber,n=e.column,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,r=!0;else{var i=this._lines[t-1].length+1;n<1?(n=1,r=!0):n>i&&(n=i,r=!0)}return r?{lineNumber:t,column:n}:e}}]),r}(vt),Mn=function(){function e(n,r){t(this,e),this._host=n,this._models=Object.create(null),this._foreignModuleFactory=r,this._foreignModule=null}return o(e,[{key:"dispose",value:function(){this._models=Object.create(null)}},{key:"_getModel",value:function(e){return this._models[e]}},{key:"_getModels",value:function(){var e=this,t=[];return Object.keys(this._models).forEach((function(n){return t.push(e._models[n])})),t}},{key:"acceptNewModel",value:function(e){this._models[e.url]=new Pn($e.parse(e.url),e.lines,e.EOL,e.versionId)}},{key:"acceptModelChanged",value:function(e,t){this._models[e]&&this._models[e].onEvents(t)}},{key:"acceptRemovedModel",value:function(e){this._models[e]&&delete this._models[e]}},{key:"computeDiff",value:function(e,t,n,r){return In(this,void 0,void 0,A().mark((function i(){var o,a,s,u,c,l,f;return A().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(o=this._getModel(e),a=this._getModel(t),o&&a){i.next=4;break}return i.abrupt("return",null);case 4:return s=o.getLinesContent(),u=a.getLinesContent(),c=new ct(s,u,{shouldComputeCharChanges:!0,shouldPostProcessCharChanges:!0,shouldIgnoreTrimWhitespace:n,shouldMakePrettyDiff:!0,maxComputationTime:r}),l=c.computeDiff(),f=!(l.changes.length>0)&&this._modelsAreIdentical(o,a),i.abrupt("return",{quitEarly:l.quitEarly,identical:f,changes:l.changes});case 10:case"end":return i.stop()}}),i,this)})))}},{key:"_modelsAreIdentical",value:function(e,t){var n=e.getLineCount();if(n!==t.getLineCount())return!1;for(var r=1;r<=n;r++){if(e.getLineContent(r)!==t.getLineContent(r))return!1}return!0}},{key:"computeMoreMinimalEdits",value:function(t,n){return In(this,void 0,void 0,A().mark((function r(){var i,o,a,s,u,c,l,f,h,d,m,g,p,v,y,b,_,C;return A().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(i=this._getModel(t)){r.next=3;break}return r.abrupt("return",n);case 3:o=[],a=void 0,n=n.slice(0).sort((function(e,t){return e.range&&t.range?rt.compareRangesUsingStarts(e.range,t.range):(e.range?0:1)-(t.range?0:1)})),s=S(n),r.prev=7,s.s();case 9:if((u=s.n()).done){r.next=27;break}if(c=u.value,l=c.range,f=c.text,"number"===typeof(h=c.eol)&&(a=h),!rt.isEmpty(l)||f){r.next=14;break}return r.abrupt("continue",25);case 14:if(d=i.getValueInRange(l),f=f.replace(/\r\n|\n|\r/g,i.eol),d!==f){r.next=18;break}return r.abrupt("continue",25);case 18:if(!(Math.max(f.length,d.length)>e._diffLimit)){r.next=21;break}return o.push({range:l,text:f}),r.abrupt("continue",25);case 21:m=ge(d,f,!1),g=i.offsetAt(rt.lift(l).getStartPosition()),p=S(m);try{for(p.s();!(v=p.n()).done;)y=v.value,b=i.positionAt(g+y.originalStart),_=i.positionAt(g+y.originalStart+y.originalLength),C={text:f.substr(y.modifiedStart,y.modifiedLength),range:{startLineNumber:b.lineNumber,startColumn:b.column,endLineNumber:_.lineNumber,endColumn:_.column}},i.getValueInRange(C.range)!==C.text&&o.push(C)}catch(k){p.e(k)}finally{p.f()}case 25:r.next=9;break;case 27:r.next=32;break;case 29:r.prev=29,r.t0=r.catch(7),s.e(r.t0);case 32:return r.prev=32,s.f(),r.finish(32);case 35:return"number"===typeof a&&o.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),r.abrupt("return",o);case 37:case"end":return r.stop()}}),r,this,[[7,29,32,35]])})))}},{key:"computeLinks",value:function(e){return In(this,void 0,void 0,A().mark((function t(){var n;return A().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=this._getModel(e)){t.next=3;break}return t.abrupt("return",null);case 3:return t.abrupt("return",Nt(n));case 4:case"end":return t.stop()}}),t,this)})))}},{key:"textualSuggest",value:function(t,n,r,i){return In(this,void 0,void 0,A().mark((function o(){var a,s,u,c,l,f,h,d,m,g;return A().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:a=new Rt(!0),s=new RegExp(r,i),u=new Set,c=S(t),o.prev=4,c.s();case 6:if((l=c.n()).done){o.next=33;break}if(f=l.value,h=this._getModel(f)){o.next=11;break}return o.abrupt("continue",31);case 11:d=S(h.words(s)),o.prev=12,d.s();case 14:if((m=d.n()).done){o.next=23;break}if((g=m.value)!==n&&isNaN(Number(g))){o.next=18;break}return o.abrupt("continue",21);case 18:if(u.add(g),!(u.size>e._suggestionsLimit)){o.next=21;break}return o.abrupt("break",33);case 21:o.next=14;break;case 23:o.next=28;break;case 25:o.prev=25,o.t0=o.catch(12),d.e(o.t0);case 28:return o.prev=28,d.f(),o.finish(28);case 31:o.next=6;break;case 33:o.next=38;break;case 35:o.prev=35,o.t1=o.catch(4),c.e(o.t1);case 38:return o.prev=38,c.f(),o.finish(38);case 41:return o.abrupt("return",{words:Array.from(u),duration:a.elapsed()});case 42:case"end":return o.stop()}}),o,this,[[4,35,38,41],[12,25,28,31]])})))}},{key:"computeWordRanges",value:function(e,t,n,r){return In(this,void 0,void 0,A().mark((function i(){var o,a,s,u,c,l,f,h,d;return A().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(o=this._getModel(e)){i.next=3;break}return i.abrupt("return",Object.create(null));case 3:a=new RegExp(n,r),s=Object.create(null),u=t.startLineNumber;case 6:if(!(u<t.endLineNumber)){i.next=31;break}c=o.getLineWords(u,a),l=S(c),i.prev=9,l.s();case 11:if((f=l.n()).done){i.next=20;break}if(h=f.value,isNaN(Number(h.word))){i.next=15;break}return i.abrupt("continue",18);case 15:(d=s[h.word])||(d=[],s[h.word]=d),d.push({startLineNumber:u,startColumn:h.startColumn,endLineNumber:u,endColumn:h.endColumn});case 18:i.next=11;break;case 20:i.next=25;break;case 22:i.prev=22,i.t0=i.catch(9),l.e(i.t0);case 25:return i.prev=25,l.f(),i.finish(25);case 28:u++,i.next=6;break;case 31:return i.abrupt("return",s);case 32:case"end":return i.stop()}}),i,this,[[9,22,25,28]])})))}},{key:"navigateValueSet",value:function(e,t,n,r,i){return In(this,void 0,void 0,A().mark((function o(){var a,s,u,c,l,f;return A().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:if(a=this._getModel(e)){o.next=3;break}return o.abrupt("return",null);case 3:if(s=new RegExp(r,i),t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1}),u=a.getValueInRange(t),c=a.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},s)){o.next=9;break}return o.abrupt("return",null);case 9:return l=a.getValueInRange(c),f=Lt.INSTANCE.navigateValueSet(t,u,c,l,n),o.abrupt("return",f);case 12:case"end":return o.stop()}}),o,this)})))}},{key:"loadForeignModule",value:function(e,t,n){var r=this,i={host:X(n,(function(e,t){return r._host.fhr(e,t)})),getMirrorModels:function(){return r._getModels()}};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(i,t),Promise.resolve(Q(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}},{key:"fmr",value:function(e,t){if(!this._foreignModule||"function"!==typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch($o){return Promise.reject($o)}}}]),e}();Mn._diffLimit=1e5,Mn._suggestionsLimit=1e4,"function"===typeof importScripts&&(q.monaco={editor:void 0,languages:void 0,CancellationTokenSource:qt,Emitter:Ft,KeyCode:ln,KeyMod:On,Position:nt,Range:rt,Selection:Ln,SelectionDirection:Cn,MarkerSeverity:fn,MarkerTag:hn,Uri:$e,Token:Tn});var Rn,Dn=!1;function jn(e){if(!Dn){Dn=!0;var t=new te((function(e){self.postMessage(e)}),(function(t){return new Mn(t,e)}));self.onmessage=function(e){t.onmessage(e.data)}}}function Fn(e,t){void 0===t&&(t=!1);var n=e.length,r=0,i="",o=0,a=16,s=0,u=0,c=0,l=0,f=0;function h(t,n){for(var i=0,o=0;i<t||!n;){var a=e.charCodeAt(r);if(a>=48&&a<=57)o=16*o+a-48;else if(a>=65&&a<=70)o=16*o+a-65+10;else{if(!(a>=97&&a<=102))break;o=16*o+a-97+10}r++,i++}return i<t&&(o=-1),o}function d(){if(i="",f=0,o=r,u=s,l=c,r>=n)return o=n,a=17;var t=e.charCodeAt(r);if(Un(t)){do{r++,i+=String.fromCharCode(t),t=e.charCodeAt(r)}while(Un(t));return a=15}if(Vn(t))return r++,i+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,i+="\n"),s++,c=r,a=14;switch(t){case 123:return r++,a=1;case 125:return r++,a=2;case 91:return r++,a=3;case 93:return r++,a=4;case 58:return r++,a=6;case 44:return r++,a=5;case 34:return r++,i=function(){for(var t="",i=r;;){if(r>=n){t+=e.substring(i,r),f=2;break}var o=e.charCodeAt(r);if(34===o){t+=e.substring(i,r),r++;break}if(92!==o){if(o>=0&&o<=31){if(Vn(o)){t+=e.substring(i,r),f=2;break}f=6}r++}else{if(t+=e.substring(i,r),++r>=n){f=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var a=h(4,!0);a>=0?t+=String.fromCharCode(a):f=4;break;default:f=5}i=r}}return t}(),a=10;case 47:var d=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!Vn(e.charCodeAt(r));)r++;return i=e.substring(d,r),a=12}if(42===e.charCodeAt(r+1)){r+=2;for(var g=n-1,p=!1;r<g;){var v=e.charCodeAt(r);if(42===v&&47===e.charCodeAt(r+1)){r+=2,p=!0;break}r++,Vn(v)&&(13===v&&10===e.charCodeAt(r)&&r++,s++,c=r)}return p||(r++,f=1),i=e.substring(d,r),a=13}return i+=String.fromCharCode(t),r++,a=16;case 45:if(i+=String.fromCharCode(t),++r===n||!Wn(e.charCodeAt(r)))return a=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=function(){var t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&Wn(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(!(++r<e.length&&Wn(e.charCodeAt(r))))return f=3,e.substring(t,r);for(r++;r<e.length&&Wn(e.charCodeAt(r));)r++}var n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if((++r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&Wn(e.charCodeAt(r))){for(r++;r<e.length&&Wn(e.charCodeAt(r));)r++;n=r}else f=3;return e.substring(t,n)}(),a=11;default:for(;r<n&&m(t);)r++,t=e.charCodeAt(r);if(o!==r){switch(i=e.substring(o,r)){case"true":return a=8;case"false":return a=9;case"null":return a=7}return a=16}return i+=String.fromCharCode(t),r++,a=16}}function m(e){if(Un(e)||Vn(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,i="",o=0,a=16,f=0},getPosition:function(){return r},scan:t?function(){var e;do{e=d()}while(e>=12&&e<=15);return e}:d,getToken:function(){return a},getTokenValue:function(){return i},getTokenOffset:function(){return o},getTokenLength:function(){return r-o},getTokenStartLine:function(){return u},getTokenStartCharacter:function(){return o-l},getTokenError:function(){return f}}}function Un(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function Vn(e){return 10===e||13===e||8232===e||8233===e}function Wn(e){return e>=48&&e<=57}function qn(e,t,n){var r,i,o,a,s;if(t){for(a=t.offset,s=a+t.length,o=a;o>0&&!Kn(e,o-1);)o--;for(var u=s;u<e.length&&!Kn(e,u);)u++;i=e.substring(o,u),r=function(e,t){var n=0,r=0,i=t.tabSize||4;for(;n<e.length;){var o=e.charAt(n);if(" "===o)r++;else{if("\t"!==o)break;r+=i}n++}return Math.floor(r/i)}(i,n)}else i=e,r=0,o=0,a=0,s=e.length;var c,l=function(e,t){for(var n=0;n<t.length;n++){var r=t.charAt(n);if("\r"===r)return n+1<t.length&&"\n"===t.charAt(n+1)?"\r\n":"\r";if("\n"===r)return"\n"}return e&&e.eol||"\n"}(n,e),f=!1,h=0;c=n.insertSpaces?Bn(" ",n.tabSize||4):"\t";var d=Fn(i,!1),m=!1;function g(){return l+Bn(c,r+h)}function p(){var e=d.scan();for(f=!1;15===e||14===e;)f=f||14===e,e=d.scan();return m=16===e||0!==d.getTokenError(),e}var v=[];function y(n,r,i){m||t&&!(r<s&&i>a)||e.substring(r,i)===n||v.push({offset:r,length:i-r,content:n})}var b=p();if(17!==b){var _=d.getTokenOffset()+o;y(Bn(c,r),o,_)}for(;17!==b;){for(var C=d.getTokenOffset()+d.getTokenLength()+o,S=p(),k="",x=!1;!f&&(12===S||13===S);){y(" ",C,d.getTokenOffset()+o),C=d.getTokenOffset()+d.getTokenLength()+o,k=(x=12===S)?g():"",S=p()}if(2===S)1!==b&&(h--,k=g());else if(4===S)3!==b&&(h--,k=g());else{switch(b){case 3:case 1:h++,k=g();break;case 5:case 12:k=g();break;case 13:f?k=g():x||(k=" ");break;case 6:x||(k=" ");break;case 10:if(6===S){x||(k="");break}case 7:case 8:case 9:case 11:case 2:case 4:12===S||13===S?x||(k=" "):5!==S&&17!==S&&(m=!0);break;case 16:m=!0}!f||12!==S&&13!==S||(k=g())}17===S&&(k=n.insertFinalNewline?l:""),y(k,C,d.getTokenOffset()+o),b=S}return v}function Bn(e,t){for(var n="",r=0;r<t;r++)n+=e;return n}function Kn(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}function $n(e,t,n){void 0===n&&(n=Rn.DEFAULT);var r=Fn(e,!1);function i(e){return e?function(){return e(r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}function o(e){return e?function(t){return e(t,r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}var a=i(t.onObjectBegin),s=o(t.onObjectProperty),u=i(t.onObjectEnd),c=i(t.onArrayBegin),l=i(t.onArrayEnd),f=o(t.onLiteralValue),h=o(t.onSeparator),d=i(t.onComment),m=o(t.onError),g=n&&n.disallowComments,p=n&&n.allowTrailingComma;function v(){for(;;){var e=r.scan();switch(r.getTokenError()){case 4:y(14);break;case 5:y(15);break;case 3:y(13);break;case 1:g||y(11);break;case 2:y(12);break;case 6:y(16)}switch(e){case 12:case 13:g?y(10):d();break;case 16:y(1);break;case 15:case 14:break;default:return e}}}function y(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),m(e),t.length+n.length>0)for(var i=r.getToken();17!==i;){if(-1!==t.indexOf(i)){v();break}if(-1!==n.indexOf(i))break;i=v()}}function b(e){var t=r.getTokenValue();return e?f(t):s(t),v(),!0}function _(){switch(r.getToken()){case 3:return function(){c(),v();for(var e=!1;4!==r.getToken()&&17!==r.getToken();){if(5===r.getToken()){if(e||y(4,[],[]),h(","),v(),4===r.getToken()&&p)break}else e&&y(6,[],[]);_()||y(4,[],[4,5]),e=!0}return l(),4!==r.getToken()?y(8,[4],[]):v(),!0}();case 1:return function(){a(),v();for(var e=!1;2!==r.getToken()&&17!==r.getToken();){if(5===r.getToken()){if(e||y(4,[],[]),h(","),v(),2===r.getToken()&&p)break}else e&&y(6,[],[]);(10!==r.getToken()?(y(3,[],[2,5]),0):(b(!1),6===r.getToken()?(h(":"),v(),_()||y(4,[],[2,5])):y(5,[],[2,5]),1))||y(4,[],[2,5]),e=!0}return u(),2!==r.getToken()?y(7,[2],[]):v(),!0}();case 10:return b(!0);default:return function(){switch(r.getToken()){case 11:var e=r.getTokenValue(),t=Number(e);isNaN(t)&&(y(2),t=0),f(t);break;case 7:f(null);break;case 8:f(!0);break;case 9:f(!1);break;default:return!1}return v(),!0}()}}return v(),17===r.getToken()?!!n.allowEmptyContent||(y(4,[],[]),!1):_()?(17!==r.getToken()&&y(9,[],[]),!0):(y(4,[],[]),!1)}self.onmessage=function(e){Dn||jn(null)},function(e){e.DEFAULT={allowTrailingComma:!1}}(Rn||(Rn={}));var Hn,Yn,zn,Gn,Jn,Qn,Xn,Zn,er,tr,nr,rr,ir,or,ar,sr,ur,cr,lr,fr,hr,dr,mr,gr,pr,vr,yr=Fn,br=function(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=Rn.DEFAULT);var r=null,i=[],o=[];function a(e){Array.isArray(i)?i.push(e):null!==r&&(i[r]=e)}return $n(e,{onObjectBegin:function(){var e={};a(e),o.push(i),i=e,r=null},onObjectProperty:function(e){r=e},onObjectEnd:function(){i=o.pop()},onArrayBegin:function(){var e=[];a(e),o.push(i),i=e,r=null},onArrayEnd:function(){i=o.pop()},onLiteralValue:a,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},n),i[0]},_r=function e(t,n,r){if(void 0===r&&(r=!1),function(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){var i=t.children;if(Array.isArray(i))for(var o=0;o<i.length&&i[o].offset<=n;o++){var a=e(i[o],n,r);if(a)return a}return t}},Cr=function e(t){if(!t.parent||!t.parent.children)return[];var n=e(t.parent);if("property"===t.parent.type){var r=t.parent.children[0].value;n.push(r)}else if("array"===t.parent.type){var i=t.parent.children.indexOf(t);-1!==i&&n.push(i)}return n},Sr=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":for(var n=Object.create(null),r=0,i=t.children;r<i.length;r++){var o=i[r],a=o.children[1];a&&(n[o.children[0].value]=e(a))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}};function kr(e,t){if(e===t)return!0;if(null===e||void 0===e||null===t||void 0===t)return!1;if(typeof e!==typeof t)return!1;if("object"!==typeof e)return!1;if(Array.isArray(e)!==Array.isArray(t))return!1;var n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!kr(e[n],t[n]))return!1}else{var i=[];for(r in e)i.push(r);i.sort();var o=[];for(r in t)o.push(r);if(o.sort(),!kr(i,o))return!1;for(n=0;n<i.length;n++)if(!kr(e[i[n]],t[i[n]]))return!1}return!0}function xr(e){return"number"===typeof e}function Ar(e){return"undefined"!==typeof e}function Er(e){return"boolean"===typeof e}function wr(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0}function Nr(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t}function Lr(e){return wr(e,"(?i)")?new RegExp(e.substring(4),"i"):new RegExp(e)}!function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647}(Hn||(Hn={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647}(Yn||(Yn={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=Yn.MAX_VALUE),t===Number.MAX_VALUE&&(t=Yn.MAX_VALUE),{line:e,character:t}},e.is=function(e){var t=e;return ci.objectLiteral(t)&&ci.uinteger(t.line)&&ci.uinteger(t.character)}}(zn||(zn={})),function(e){e.create=function(e,t,n,r){if(ci.uinteger(e)&&ci.uinteger(t)&&ci.uinteger(n)&&ci.uinteger(r))return{start:zn.create(e,t),end:zn.create(n,r)};if(zn.is(e)&&zn.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+n+", "+r+"]")},e.is=function(e){var t=e;return ci.objectLiteral(t)&&zn.is(t.start)&&zn.is(t.end)}}(Gn||(Gn={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){var t=e;return ci.defined(t)&&Gn.is(t.range)&&(ci.string(t.uri)||ci.undefined(t.uri))}}(Jn||(Jn={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){var t=e;return ci.defined(t)&&Gn.is(t.targetRange)&&ci.string(t.targetUri)&&(Gn.is(t.targetSelectionRange)||ci.undefined(t.targetSelectionRange))&&(Gn.is(t.originSelectionRange)||ci.undefined(t.originSelectionRange))}}(Qn||(Qn={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return ci.numberRange(t.red,0,1)&&ci.numberRange(t.green,0,1)&&ci.numberRange(t.blue,0,1)&&ci.numberRange(t.alpha,0,1)}}(Xn||(Xn={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){var t=e;return Gn.is(t.range)&&Xn.is(t.color)}}(Zn||(Zn={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){var t=e;return ci.string(t.label)&&(ci.undefined(t.textEdit)||cr.is(t))&&(ci.undefined(t.additionalTextEdits)||ci.typedArray(t.additionalTextEdits,cr.is))}}(er||(er={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(tr||(tr={})),function(e){e.create=function(e,t,n,r,i){var o={startLine:e,endLine:t};return ci.defined(n)&&(o.startCharacter=n),ci.defined(r)&&(o.endCharacter=r),ci.defined(i)&&(o.kind=i),o},e.is=function(e){var t=e;return ci.uinteger(t.startLine)&&ci.uinteger(t.startLine)&&(ci.undefined(t.startCharacter)||ci.uinteger(t.startCharacter))&&(ci.undefined(t.endCharacter)||ci.uinteger(t.endCharacter))&&(ci.undefined(t.kind)||ci.string(t.kind))}}(nr||(nr={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){var t=e;return ci.defined(t)&&Jn.is(t.location)&&ci.string(t.message)}}(rr||(rr={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(ir||(ir={})),function(e){e.Unnecessary=1,e.Deprecated=2}(or||(or={})),function(e){e.is=function(e){var t=e;return void 0!==t&&null!==t&&ci.string(t.href)}}(ar||(ar={})),function(e){e.create=function(e,t,n,r,i,o){var a={range:e,message:t};return ci.defined(n)&&(a.severity=n),ci.defined(r)&&(a.code=r),ci.defined(i)&&(a.source=i),ci.defined(o)&&(a.relatedInformation=o),a},e.is=function(e){var t,n=e;return ci.defined(n)&&Gn.is(n.range)&&ci.string(n.message)&&(ci.number(n.severity)||ci.undefined(n.severity))&&(ci.integer(n.code)||ci.string(n.code)||ci.undefined(n.code))&&(ci.undefined(n.codeDescription)||ci.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(ci.string(n.source)||ci.undefined(n.source))&&(ci.undefined(n.relatedInformation)||ci.typedArray(n.relatedInformation,rr.is))}}(sr||(sr={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={title:e,command:t};return ci.defined(n)&&n.length>0&&(i.arguments=n),i},e.is=function(e){var t=e;return ci.defined(t)&&ci.string(t.title)&&ci.string(t.command)}}(ur||(ur={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){var t=e;return ci.objectLiteral(t)&&ci.string(t.newText)&&Gn.is(t.range)}}(cr||(cr={})),function(e){e.create=function(e,t,n){var r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){var t=e;return void 0!==t&&ci.objectLiteral(t)&&ci.string(t.label)&&(ci.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(ci.string(t.description)||void 0===t.description)}}(lr||(lr={})),function(e){e.is=function(e){return"string"===typeof e}}(fr||(fr={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){var t=e;return cr.is(t)&&(lr.is(t.annotationId)||fr.is(t.annotationId))}}(hr||(hr={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){var t=e;return ci.defined(t)&&Ir.is(t.textDocument)&&Array.isArray(t.edits)}}(dr||(dr={})),function(e){e.create=function(e,t,n){var r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"create"===t.kind&&ci.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||ci.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||ci.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||fr.is(t.annotationId))}}(mr||(mr={})),function(e){e.create=function(e,t,n,r){var i={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==r&&(i.annotationId=r),i},e.is=function(e){var t=e;return t&&"rename"===t.kind&&ci.string(t.oldUri)&&ci.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||ci.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||ci.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||fr.is(t.annotationId))}}(gr||(gr={})),function(e){e.create=function(e,t,n){var r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){var t=e;return t&&"delete"===t.kind&&ci.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||ci.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||ci.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||fr.is(t.annotationId))}}(pr||(pr={})),function(e){e.is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return ci.string(e.kind)?mr.is(e)||gr.is(e)||pr.is(e):dr.is(e)})))}}(vr||(vr={}));var Tr,Or,Ir,Pr,Mr,Rr,Dr,jr,Fr,Ur,Vr,Wr,qr,Br,Kr,$r,Hr,Yr,zr,Gr,Jr,Qr,Xr,Zr,ei,ti,ni,ri,ii,oi,ai=function(){function e(e,t){this.edits=e,this.changeAnnotations=t}return e.prototype.insert=function(e,t,n){var r,i;if(void 0===n?r=cr.insert(e,t):fr.is(n)?(i=n,r=hr.insert(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=hr.insert(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.replace=function(e,t,n){var r,i;if(void 0===n?r=cr.replace(e,t):fr.is(n)?(i=n,r=hr.replace(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=hr.replace(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.delete=function(e,t){var n,r;if(void 0===t?n=cr.del(e):fr.is(t)?(r=t,n=hr.del(e,t)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(t),n=hr.del(e,r)),this.edits.push(n),void 0!==r)return r},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e.prototype.assertChangeAnnotations=function(e){if(void 0===e)throw new Error("Text edit change is not configured to manage change annotations.")},e}(),si=function(){function e(e){this._annotations=void 0===e?Object.create(null):e,this._counter=0,this._size=0}return e.prototype.all=function(){return this._annotations},Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.manage=function(e,t){var n;if(fr.is(e)?n=e:(n=this.nextId(),t=e),void 0!==this._annotations[n])throw new Error("Id "+n+" is already in use.");if(void 0===t)throw new Error("No annotation provided for id "+n);return this._annotations[n]=t,this._size++,n},e.prototype.nextId=function(){return this._counter++,this._counter.toString()},e}();!function(){function e(e){var t=this;this._textEditChanges=Object.create(null),void 0!==e?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new si(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach((function(e){if(dr.is(e)){var n=new ai(e.edits,t._changeAnnotations);t._textEditChanges[e.textDocument.uri]=n}}))):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new ai(e.changes[n]);t._textEditChanges[n]=r}))):this._workspaceEdit={}}Object.defineProperty(e.prototype,"edit",{get:function(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(0===this._changeAnnotations.size?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),e.prototype.getTextEditChange=function(e){if(Ir.is(e)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t={uri:e.uri,version:e.version};if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:i=[]};this._workspaceEdit.documentChanges.push(n),r=new ai(i,this._changeAnnotations),this._textEditChanges[t.uri]=r}return r}if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var i=[];this._workspaceEdit.changes[e]=i,r=new ai(i),this._textEditChanges[e]=r}return r},e.prototype.initDocumentChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new si,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},e.prototype.initChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))},e.prototype.createFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,o;if(lr.is(t)||fr.is(t)?r=t:n=t,void 0===r?i=mr.create(e,n):(o=fr.is(r)?r:this._changeAnnotations.manage(r),i=mr.create(e,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o},e.prototype.renameFile=function(e,t,n,r){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var i,o,a;if(lr.is(n)||fr.is(n)?i=n:r=n,void 0===i?o=gr.create(e,t,r):(a=fr.is(i)?i:this._changeAnnotations.manage(i),o=gr.create(e,t,r,a)),this._workspaceEdit.documentChanges.push(o),void 0!==a)return a},e.prototype.deleteFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,o;if(lr.is(t)||fr.is(t)?r=t:n=t,void 0===r?i=pr.create(e,n):(o=fr.is(r)?r:this._changeAnnotations.manage(r),i=pr.create(e,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o}}();!function(e){e.create=function(e){return{uri:e}},e.is=function(e){var t=e;return ci.defined(t)&&ci.string(t.uri)}}(Tr||(Tr={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return ci.defined(t)&&ci.string(t.uri)&&ci.integer(t.version)}}(Or||(Or={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return ci.defined(t)&&ci.string(t.uri)&&(null===t.version||ci.integer(t.version))}}(Ir||(Ir={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){var t=e;return ci.defined(t)&&ci.string(t.uri)&&ci.string(t.languageId)&&ci.integer(t.version)&&ci.string(t.text)}}(Pr||(Pr={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(Mr||(Mr={})),function(e){e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(Mr||(Mr={})),function(e){e.is=function(e){var t=e;return ci.objectLiteral(e)&&Mr.is(t.kind)&&ci.string(t.value)}}(Rr||(Rr={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(Dr||(Dr={})),function(e){e.PlainText=1,e.Snippet=2}(jr||(jr={})),function(e){e.Deprecated=1}(Fr||(Fr={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){var t=e;return t&&ci.string(t.newText)&&Gn.is(t.insert)&&Gn.is(t.replace)}}(Ur||(Ur={})),function(e){e.asIs=1,e.adjustIndentation=2}(Vr||(Vr={})),function(e){e.create=function(e){return{label:e}}}(Wr||(Wr={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(qr||(qr={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){var t=e;return ci.string(t)||ci.objectLiteral(t)&&ci.string(t.language)&&ci.string(t.value)}}(Br||(Br={})),function(e){e.is=function(e){var t=e;return!!t&&ci.objectLiteral(t)&&(Rr.is(t.contents)||Br.is(t.contents)||ci.typedArray(t.contents,Br.is))&&(void 0===e.range||Gn.is(e.range))}}(Kr||(Kr={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}($r||($r={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={label:e};return ci.defined(t)&&(i.documentation=t),ci.defined(n)?i.parameters=n:i.parameters=[],i}}(Hr||(Hr={})),function(e){e.Text=1,e.Read=2,e.Write=3}(Yr||(Yr={})),function(e){e.create=function(e,t){var n={range:e};return ci.number(t)&&(n.kind=t),n}}(zr||(zr={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(Gr||(Gr={})),function(e){e.Deprecated=1}(Jr||(Jr={})),function(e){e.create=function(e,t,n,r,i){var o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o}}(Qr||(Qr={})),function(e){e.create=function(e,t,n,r,i,o){var a={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==o&&(a.children=o),a},e.is=function(e){var t=e;return t&&ci.string(t.name)&&ci.number(t.kind)&&Gn.is(t.range)&&Gn.is(t.selectionRange)&&(void 0===t.detail||ci.string(t.detail))&&(void 0===t.deprecated||ci.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(Xr||(Xr={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(Zr||(Zr={})),function(e){e.create=function(e,t){var n={diagnostics:e};return void 0!==t&&null!==t&&(n.only=t),n},e.is=function(e){var t=e;return ci.defined(t)&&ci.typedArray(t.diagnostics,sr.is)&&(void 0===t.only||ci.typedArray(t.only,ci.string))}}(ei||(ei={})),function(e){e.create=function(e,t,n){var r={title:e},i=!0;return"string"===typeof t?(i=!1,r.kind=t):ur.is(t)?r.command=t:r.edit=t,i&&void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return t&&ci.string(t.title)&&(void 0===t.diagnostics||ci.typedArray(t.diagnostics,sr.is))&&(void 0===t.kind||ci.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||ur.is(t.command))&&(void 0===t.isPreferred||ci.boolean(t.isPreferred))&&(void 0===t.edit||vr.is(t.edit))}}(ti||(ti={})),function(e){e.create=function(e,t){var n={range:e};return ci.defined(t)&&(n.data=t),n},e.is=function(e){var t=e;return ci.defined(t)&&Gn.is(t.range)&&(ci.undefined(t.command)||ur.is(t.command))}}(ni||(ni={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){var t=e;return ci.defined(t)&&ci.uinteger(t.tabSize)&&ci.boolean(t.insertSpaces)}}(ri||(ri={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){var t=e;return ci.defined(t)&&Gn.is(t.range)&&(ci.undefined(t.target)||ci.string(t.target))}}(ii||(ii={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){var n=t;return void 0!==n&&Gn.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(oi||(oi={}));var ui;!function(e){function t(e,n){if(e.length<=1)return e;var r=e.length/2|0,i=e.slice(0,r),o=e.slice(r);t(i,n),t(o,n);for(var a=0,s=0,u=0;a<i.length&&s<o.length;){var c=n(i[a],o[s]);e[u++]=c<=0?i[a++]:o[s++]}for(;a<i.length;)e[u++]=i[a++];for(;s<o.length;)e[u++]=o[s++];return e}e.create=function(e,t,n,r){return new li(e,t,n,r)},e.is=function(e){var t=e;return!!(ci.defined(t)&&ci.string(t.uri)&&(ci.undefined(t.languageId)||ci.string(t.languageId))&&ci.uinteger(t.lineCount)&&ci.func(t.getText)&&ci.func(t.positionAt)&&ci.func(t.offsetAt))},e.applyEdits=function(e,n){for(var r=e.getText(),i=t(n,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=r.length,a=i.length-1;a>=0;a--){var s=i[a],u=e.offsetAt(s.range.start),c=e.offsetAt(s.range.end);if(!(c<=o))throw new Error("Overlapping edit");r=r.substring(0,u)+s.newText+r.substring(c,r.length),o=u}return r}}(ui||(ui={}));var ci,li=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return zn.create(0,e);for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return zn.create(o,e-t[o])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),e}();!function(e){var t=Object.prototype.toString;e.defined=function(e){return"undefined"!==typeof e},e.undefined=function(e){return"undefined"===typeof e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"===typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(ci||(ci={}));var fi,hi,di,mi=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(t,n){for(var r=0,i=t;r<i.length;r++){var o=i[r];if(e.isIncremental(o)){var a=vi(o.range),s=this.offsetAt(a.start),u=this.offsetAt(a.end);this._content=this._content.substring(0,s)+o.text+this._content.substring(u,this._content.length);var c=Math.max(a.start.line,0),l=Math.max(a.end.line,0),f=this._lineOffsets,h=pi(o.text,!1,s);if(l-c===h.length)for(var d=0,m=h.length;d<m;d++)f[d+c+1]=h[d];else h.length<1e4?f.splice.apply(f,[c+1,l-c].concat(h)):this._lineOffsets=f=f.slice(0,c+1).concat(h,f.slice(l+1));var g=o.text.length-(u-s);if(0!==g)for(d=c+1+h.length,m=f.length;d<m;d++)f[d]=f[d]+g}else{if(!e.isFull(o))throw new Error("Unknown change event received");this._content=o.text,this._lineOffsets=void 0}}this._version=n},e.prototype.getLineOffsets=function(){return void 0===this._lineOffsets&&(this._lineOffsets=pi(this._content,!0)),this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return{line:o,character:e-t[o]}},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e.isIncremental=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"===typeof t.rangeLength)},e.isFull=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0===t.range&&void 0===t.rangeLength},e}();function gi(e,t){if(e.length<=1)return e;var n=e.length/2|0,r=e.slice(0,n),i=e.slice(n);gi(r,t),gi(i,t);for(var o=0,a=0,s=0;o<r.length&&a<i.length;){var u=t(r[o],i[a]);e[s++]=u<=0?r[o++]:i[a++]}for(;o<r.length;)e[s++]=r[o++];for(;a<i.length;)e[s++]=i[a++];return e}function pi(e,t,n){void 0===n&&(n=0);for(var r=t?[n]:[],i=0;i<e.length;i++){var o=e.charCodeAt(i);13!==o&&10!==o||(13===o&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,r.push(n+i+1))}return r}function vi(e){var t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function yi(e){var t=vi(e.range);return t!==e.range?{newText:e.newText,range:t}:e}function bi(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return function(e,t){return 0===t.length?e:e.replace(/\{(\d+)\}/g,(function(e,n){var r=n[0];return"undefined"!==typeof t[r]?t[r]:e}))}(t,n)}function _i(e){return bi}!function(e){e.create=function(e,t,n,r){return new mi(e,t,n,r)},e.update=function(e,t,n){if(e instanceof mi)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){for(var n=e.getText(),r=gi(t.map(yi),(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=0,o=[],a=0,s=r;a<s.length;a++){var u=s[a],c=e.offsetAt(u.range.start);if(c<i)throw new Error("Overlapping edit");c>i&&o.push(n.substring(i,c)),u.newText.length&&o.push(u.newText),i=e.offsetAt(u.range.end)}return o.push(n.substr(i)),o.join("")}}(fi||(fi={})),function(e){e[e.Undefined=0]="Undefined",e[e.EnumValueMismatch=1]="EnumValueMismatch",e[e.Deprecated=2]="Deprecated",e[e.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=258]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",e[e.InvalidUnicode=260]="InvalidUnicode",e[e.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",e[e.InvalidCharacter=262]="InvalidCharacter",e[e.PropertyExpected=513]="PropertyExpected",e[e.CommaExpected=514]="CommaExpected",e[e.ColonExpected=515]="ColonExpected",e[e.ValueExpected=516]="ValueExpected",e[e.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",e[e.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",e[e.TrailingComma=519]="TrailingComma",e[e.DuplicateKey=520]="DuplicateKey",e[e.CommentNotPermitted=521]="CommentNotPermitted",e[e.SchemaResolveError=768]="SchemaResolveError"}(hi||(hi={})),function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[Mr.Markdown,Mr.PlainText],commitCharactersSupport:!0}}}}}(di||(di={}));var Ci,Si=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),ki=_i(),xi={"color-hex":{errorMessage:ki("colorHexFormatWarning","Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:ki("dateTimeFormatWarning","String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:ki("dateFormatWarning","String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:ki("timeFormatWarning","String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:ki("emailFormatWarning","String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/}},Ai=function(){function e(e,t,n){void 0===n&&(n=0),this.offset=t,this.length=n,this.parent=e}return Object.defineProperty(e.prototype,"children",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e}(),Ei=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="null",r.value=null,r}return Si(t,e),t}(Ai),wi=function(e){function t(t,n,r){var i=e.call(this,t,r)||this;return i.type="boolean",i.value=n,i}return Si(t,e),t}(Ai),Ni=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="array",r.items=[],r}return Si(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.items},enumerable:!1,configurable:!0}),t}(Ai),Li=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="number",r.isInteger=!0,r.value=Number.NaN,r}return Si(t,e),t}(Ai),Ti=function(e){function t(t,n,r){var i=e.call(this,t,n,r)||this;return i.type="string",i.value="",i}return Si(t,e),t}(Ai),Oi=function(e){function t(t,n,r){var i=e.call(this,t,n)||this;return i.type="property",i.colonOffset=-1,i.keyNode=r,i}return Si(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]},enumerable:!1,configurable:!0}),t}(Ai),Ii=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="object",r.properties=[],r}return Si(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties},enumerable:!1,configurable:!0}),t}(Ai);function Pi(e){return Er(e)?e?{}:{not:{}}:e}!function(e){e[e.Key=0]="Key",e[e.Enum=1]="Enum"}(Ci||(Ci={}));var Mi=function(){function e(e,t){void 0===e&&(e=-1),this.focusOffset=e,this.exclude=t,this.schemas=[]}return e.prototype.add=function(e){this.schemas.push(e)},e.prototype.merge=function(e){Array.prototype.push.apply(this.schemas,e.schemas)},e.prototype.include=function(e){return(-1===this.focusOffset||Ui(e,this.focusOffset))&&e!==this.exclude},e.prototype.newSub=function(){return new e(-1,this.exclude)},e}(),Ri=function(){function e(){}return Object.defineProperty(e.prototype,"schemas",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.add=function(e){},e.prototype.merge=function(e){},e.prototype.include=function(e){return!0},e.prototype.newSub=function(){return this},e.instance=new e,e}(),Di=function(){function e(){this.problems=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}return e.prototype.hasProblems=function(){return!!this.problems.length},e.prototype.mergeAll=function(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];this.merge(r)}},e.prototype.merge=function(e){this.problems=this.problems.concat(e.problems)},e.prototype.mergeEnumValues=function(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(var t=0,n=this.problems;t<n.length;t++){var r=n[t];r.code===hi.EnumValueMismatch&&(r.message=ki("enumWarning","Value is not accepted. Valid values: {0}.",this.enumValues.map((function(e){return JSON.stringify(e)})).join(", ")))}}},e.prototype.mergePropertyMatch=function(e){this.merge(e),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++},e.prototype.compare=function(e){var t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches},e}();function ji(e){return Sr(e)}function Fi(e){return Cr(e)}function Ui(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}var Vi=function(){function e(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]),this.root=e,this.syntaxErrors=t,this.comments=n}return e.prototype.getNodeFromOffset=function(e,t){if(void 0===t&&(t=!1),this.root)return _r(this.root,e,t)},e.prototype.visit=function(e){if(this.root){!function t(n){var r=e(n),i=n.children;if(Array.isArray(i))for(var o=0;o<i.length&&r;o++)r=t(i[o]);return r}(this.root)}},e.prototype.validate=function(e,t,n){if(void 0===n&&(n=ir.Warning),this.root&&t){var r=new Di;return Wi(this.root,t,r,Ri.instance),r.problems.map((function(t){var r,i=Gn.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return sr.create(i,t.message,null!==(r=t.severity)&&void 0!==r?r:n,t.code)}))}},e.prototype.getMatchingSchemas=function(e,t,n){void 0===t&&(t=-1);var r=new Mi(t,n);return this.root&&e&&Wi(this.root,e,new Di,r),r.schemas},e}();function Wi(e,t,n,r){if(e&&r.include(e)){var i=e;switch(i.type){case"object":!function(e,t,n,r){for(var i=Object.create(null),o=[],a=0,s=e.properties;a<s.length;a++){i[U=(p=s[a]).keyNode.value]=p.valueNode,o.push(U)}if(Array.isArray(t.required))for(var u=0,c=t.required;u<c.length;u++){if(!i[k=c[u]]){var l=e.parent&&"property"===e.parent.type&&e.parent.keyNode,f=l?{offset:l.offset,length:l.length}:{offset:e.offset,length:1};n.problems.push({location:f,message:ki("MissingRequiredPropWarning",'Missing property "{0}".',k)})}}var h=function(e){for(var t=o.indexOf(e);t>=0;)o.splice(t,1),t=o.indexOf(e)};if(t.properties)for(var d=0,m=Object.keys(t.properties);d<m.length;d++){h(k=m[d]);var g=t.properties[k];if(L=i[k])if(Er(g))if(g)n.propertiesMatches++,n.propertiesValueMatches++;else{var p=L.parent;n.problems.push({location:{offset:p.keyNode.offset,length:p.keyNode.length},message:t.errorMessage||ki("DisallowedExtraPropWarning","Property {0} is not allowed.",k)})}else Wi(L,g,E=new Di,r),n.mergePropertyMatch(E)}if(t.patternProperties)for(var v=0,y=Object.keys(t.patternProperties);v<y.length;v++)for(var b=y[v],_=Lr(b),C=0,S=o.slice(0);C<S.length;C++){var k=S[C];if(_.test(k))if(h(k),L=i[k])if(Er(g=t.patternProperties[b]))if(g)n.propertiesMatches++,n.propertiesValueMatches++;else{p=L.parent;n.problems.push({location:{offset:p.keyNode.offset,length:p.keyNode.length},message:t.errorMessage||ki("DisallowedExtraPropWarning","Property {0} is not allowed.",k)})}else Wi(L,g,E=new Di,r),n.mergePropertyMatch(E)}if("object"===typeof t.additionalProperties)for(var x=0,A=o;x<A.length;x++){if(L=i[k=A[x]]){var E=new Di;Wi(L,t.additionalProperties,E,r),n.mergePropertyMatch(E)}}else if(!1===t.additionalProperties&&o.length>0)for(var w=0,N=o;w<N.length;w++){var L;if(L=i[k=N[w]]){p=L.parent;n.problems.push({location:{offset:p.keyNode.offset,length:p.keyNode.length},message:t.errorMessage||ki("DisallowedExtraPropWarning","Property {0} is not allowed.",k)})}}xr(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("MaxPropWarning","Object has more properties than limit of {0}.",t.maxProperties)});xr(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("MinPropWarning","Object has fewer properties than the required number of {0}",t.minProperties)});if(t.dependencies)for(var T=0,O=Object.keys(t.dependencies);T<O.length;T++){if(i[U=O[T]]){var I=t.dependencies[U];if(Array.isArray(I))for(var P=0,M=I;P<M.length;P++){var R=M[P];i[R]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},message:ki("RequiredDependentPropWarning","Object is missing property {0} required by property {1}.",R,U)})}else if(g=Pi(I))Wi(e,g,E=new Di,r),n.mergePropertyMatch(E)}}var D=Pi(t.propertyNames);if(D)for(var j=0,F=e.properties;j<F.length;j++){var U;(U=F[j].keyNode)&&Wi(U,D,n,Ri.instance)}}(i,t,n,r);break;case"array":!function(e,t,n,r){if(Array.isArray(t.items)){for(var i=t.items,o=0;o<i.length;o++){var a=Pi(i[o]),s=new Di;(h=e.items[o])?(Wi(h,a,s,r),n.mergePropertyMatch(s)):e.items.length>=i.length&&n.propertiesValueMatches++}if(e.items.length>i.length)if("object"===typeof t.additionalItems)for(var u=i.length;u<e.items.length;u++){s=new Di;Wi(e.items[u],t.additionalItems,s,r),n.mergePropertyMatch(s)}else!1===t.additionalItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer.",i.length)})}else{var c=Pi(t.items);if(c)for(var l=0,f=e.items;l<f.length;l++){var h;Wi(h=f[l],c,s=new Di,r),n.mergePropertyMatch(s)}}var d=Pi(t.contains);if(d){var m=e.items.some((function(e){var t=new Di;return Wi(e,d,t,Ri.instance),!t.hasProblems()}));m||n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||ki("requiredItemMissingWarning","Array does not contain required item.")})}xr(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("minItemsWarning","Array has too few items. Expected {0} or more.",t.minItems)});xr(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("maxItemsWarning","Array has too many items. Expected {0} or fewer.",t.maxItems)});if(!0===t.uniqueItems){var g=ji(e),p=g.some((function(e,t){return t!==g.lastIndexOf(e)}));p&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("uniqueItemsWarning","Array has duplicate items.")})}}(i,t,n,r);break;case"string":!function(e,t,n,r){xr(t.minLength)&&e.value.length<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("minLengthWarning","String is shorter than the minimum length of {0}.",t.minLength)});xr(t.maxLength)&&e.value.length>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("maxLengthWarning","String is longer than the maximum length of {0}.",t.maxLength)});if(i=t.pattern,"string"===typeof i){Lr(t.pattern).test(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||ki("patternWarning",'String does not match the pattern of "{0}".',t.pattern)})}var i;if(t.format)switch(t.format){case"uri":case"uri-reference":var o=void 0;if(e.value){var a=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);a?a[2]||"uri"!==t.format||(o=ki("uriSchemeMissing","URI with a scheme is expected.")):o=ki("uriMissing","URI is expected.")}else o=ki("uriEmpty","URI expected.");o&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||ki("uriFormatWarning","String is not a URI: {0}",o)});break;case"color-hex":case"date-time":case"date":case"time":case"email":var s=xi[t.format];e.value&&s.pattern.exec(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||s.errorMessage})}}(i,t,n);break;case"number":!function(e,t,n,r){var i=e.value;function o(e){var t,n=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(e.toString());return n&&{value:Number(n[1]+(n[2]||"")),multiplier:((null===(t=n[2])||void 0===t?void 0:t.length)||0)-(parseInt(n[3])||0)}}if(xr(t.multipleOf)){var a=-1;if(Number.isInteger(t.multipleOf))a=i%t.multipleOf;else{var s=o(t.multipleOf),u=o(i);if(s&&u){var c=Math.pow(10,Math.abs(u.multiplier-s.multiplier));u.multiplier<s.multiplier?u.value*=c:s.value*=c,a=u.value%s.value}}0!==a&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("multipleOfWarning","Value is not divisible by {0}.",t.multipleOf)})}function l(e,t){return xr(t)?t:Er(t)&&t?e:void 0}function f(e,t){if(!Er(t)||!t)return e}var h=l(t.minimum,t.exclusiveMinimum);xr(h)&&i<=h&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}.",h)});var d=l(t.maximum,t.exclusiveMaximum);xr(d)&&i>=d&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}.",d)});var m=f(t.minimum,t.exclusiveMinimum);xr(m)&&i<m&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("minimumWarning","Value is below the minimum of {0}.",m)});var g=f(t.maximum,t.exclusiveMaximum);xr(g)&&i>g&&n.problems.push({location:{offset:e.offset,length:e.length},message:ki("maximumWarning","Value is above the maximum of {0}.",g)})}(i,t,n);break;case"property":return Wi(i.valueNode,t,n,r)}!function(){function e(e){return i.type===e||"integer"===e&&"number"===i.type&&i.isInteger}Array.isArray(t.type)?t.type.some(e)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||ki("typeArrayMismatchWarning","Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(e(t.type)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||ki("typeMismatchWarning",'Incorrect type. Expected "{0}".',t.type)}));if(Array.isArray(t.allOf))for(var o=0,a=t.allOf;o<a.length;o++){var s=a[o];Wi(i,Pi(s),n,r)}var u=Pi(t.not);if(u){var c=new Di,l=r.newSub();Wi(i,u,c,l),c.hasProblems()||n.problems.push({location:{offset:i.offset,length:i.length},message:ki("notSchemaWarning","Matches a schema that is not allowed.")});for(var f=0,h=l.schemas;f<h.length;f++){var d=h[f];d.inverted=!d.inverted,r.add(d)}}var m=function(e,t){for(var o=[],a=void 0,s=0,u=e;s<u.length;s++){var c=Pi(u[s]),l=new Di,f=r.newSub();if(Wi(i,c,l,f),l.hasProblems()||o.push(c),a)if(t||l.hasProblems()||a.validationResult.hasProblems()){var h=l.compare(a.validationResult);h>0?a={schema:c,validationResult:l,matchingSchemas:f}:0===h&&(a.matchingSchemas.merge(f),a.validationResult.mergeEnumValues(l))}else a.matchingSchemas.merge(f),a.validationResult.propertiesMatches+=l.propertiesMatches,a.validationResult.propertiesValueMatches+=l.propertiesValueMatches;else a={schema:c,validationResult:l,matchingSchemas:f}}return o.length>1&&t&&n.problems.push({location:{offset:i.offset,length:1},message:ki("oneOfWarning","Matches multiple schemas when only one must validate.")}),a&&(n.merge(a.validationResult),n.propertiesMatches+=a.validationResult.propertiesMatches,n.propertiesValueMatches+=a.validationResult.propertiesValueMatches,r.merge(a.matchingSchemas)),o.length};Array.isArray(t.anyOf)&&m(t.anyOf,!1);Array.isArray(t.oneOf)&&m(t.oneOf,!0);var g=function(e){var t=new Di,o=r.newSub();Wi(i,Pi(e),t,o),n.merge(t),n.propertiesMatches+=t.propertiesMatches,n.propertiesValueMatches+=t.propertiesValueMatches,r.merge(o)},p=Pi(t.if);p&&function(e,t,n){var o=Pi(e),a=new Di,s=r.newSub();Wi(i,o,a,s),r.merge(s),a.hasProblems()?n&&g(n):t&&g(t)}(p,Pi(t.then),Pi(t.else));if(Array.isArray(t.enum)){for(var v=ji(i),y=!1,b=0,_=t.enum;b<_.length;b++){if(kr(v,_[b])){y=!0;break}}n.enumValues=t.enum,n.enumValueMatch=y,y||n.problems.push({location:{offset:i.offset,length:i.length},code:hi.EnumValueMismatch,message:t.errorMessage||ki("enumWarning","Value is not accepted. Valid values: {0}.",t.enum.map((function(e){return JSON.stringify(e)})).join(", "))})}if(Ar(t.const)){kr(v=ji(i),t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:i.offset,length:i.length},code:hi.EnumValueMismatch,message:t.errorMessage||ki("constWarning","Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}t.deprecationMessage&&i.parent&&n.problems.push({location:{offset:i.parent.offset,length:i.parent.length},severity:ir.Warning,message:t.deprecationMessage,code:hi.Deprecated})}(),r.add({node:i,schema:t})}}function qi(e,t){var n=[],r=-1,i=e.getText(),o=yr(i,!1),a=t&&t.collectComments?[]:void 0;function s(){for(;;){var t=o.scan();switch(l(),t){case 12:case 13:Array.isArray(a)&&a.push(Gn.create(e.positionAt(o.getTokenOffset()),e.positionAt(o.getTokenOffset()+o.getTokenLength())));break;case 15:case 14:break;default:return t}}}function u(t,i,o,a,s){if(void 0===s&&(s=ir.Error),0===n.length||o!==r){var u=Gn.create(e.positionAt(o),e.positionAt(a));n.push(sr.create(u,t,s,i,e.languageId)),r=o}}function c(e,t,n,r,a){void 0===n&&(n=void 0),void 0===r&&(r=[]),void 0===a&&(a=[]);var c=o.getTokenOffset(),l=o.getTokenOffset()+o.getTokenLength();if(c===l&&c>0){for(c--;c>0&&/\s/.test(i.charAt(c));)c--;l=c+1}if(u(e,t,c,l),n&&f(n,!1),r.length+a.length>0)for(var h=o.getToken();17!==h;){if(-1!==r.indexOf(h)){s();break}if(-1!==a.indexOf(h))break;h=s()}return n}function l(){switch(o.getTokenError()){case 4:return c(ki("InvalidUnicode","Invalid unicode sequence in string."),hi.InvalidUnicode),!0;case 5:return c(ki("InvalidEscapeCharacter","Invalid escape character in string."),hi.InvalidEscapeCharacter),!0;case 3:return c(ki("UnexpectedEndOfNumber","Unexpected end of number."),hi.UnexpectedEndOfNumber),!0;case 1:return c(ki("UnexpectedEndOfComment","Unexpected end of comment."),hi.UnexpectedEndOfComment),!0;case 2:return c(ki("UnexpectedEndOfString","Unexpected end of string."),hi.UnexpectedEndOfString),!0;case 6:return c(ki("InvalidCharacter","Invalid characters in string. Control characters must be escaped."),hi.InvalidCharacter),!0}return!1}function f(e,t){return e.length=o.getTokenOffset()+o.getTokenLength()-e.offset,t&&s(),e}var h=new Ti(void 0,0,0);function d(t,n){var r=new Oi(t,o.getTokenOffset(),h),i=m(r);if(!i){if(16!==o.getToken())return;c(ki("DoubleQuotesExpected","Property keys must be doublequoted"),hi.Undefined);var a=new Ti(r,o.getTokenOffset(),o.getTokenLength());a.value=o.getTokenValue(),i=a,s()}r.keyNode=i;var l=n[i.value];if(l?(u(ki("DuplicateKeyWarning","Duplicate object key"),hi.DuplicateKey,r.keyNode.offset,r.keyNode.offset+r.keyNode.length,ir.Warning),"object"===typeof l&&u(ki("DuplicateKeyWarning","Duplicate object key"),hi.DuplicateKey,l.keyNode.offset,l.keyNode.offset+l.keyNode.length,ir.Warning),n[i.value]=!0):n[i.value]=r,6===o.getToken())r.colonOffset=o.getTokenOffset(),s();else if(c(ki("ColonExpected","Colon expected"),hi.ColonExpected),10===o.getToken()&&e.positionAt(i.offset+i.length).line<e.positionAt(o.getTokenOffset()).line)return r.length=i.length,r;var f=g(r);return f?(r.valueNode=f,r.length=f.offset+f.length-r.offset,r):c(ki("ValueExpected","Value expected"),hi.ValueExpected,r,[],[2,5])}function m(e){if(10===o.getToken()){var t=new Ti(e,o.getTokenOffset());return t.value=o.getTokenValue(),f(t,!0)}}function g(e){return function(e){if(3===o.getToken()){var t=new Ni(e,o.getTokenOffset());s();for(var n=!1;4!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){n||c(ki("ValueExpected","Value expected"),hi.ValueExpected);var r=o.getTokenOffset();if(s(),4===o.getToken()){n&&u(ki("TrailingComma","Trailing comma"),hi.TrailingComma,r,r+1);continue}}else n&&c(ki("ExpectedComma","Expected comma"),hi.CommaExpected);var i=g(t);i?t.items.push(i):c(ki("PropertyExpected","Value expected"),hi.ValueExpected,void 0,[],[4,5]),n=!0}return 4!==o.getToken()?c(ki("ExpectedCloseBracket","Expected comma or closing bracket"),hi.CommaOrCloseBacketExpected,t):f(t,!0)}}(e)||function(e){if(1===o.getToken()){var t=new Ii(e,o.getTokenOffset()),n=Object.create(null);s();for(var r=!1;2!==o.getToken()&&17!==o.getToken();){if(5===o.getToken()){r||c(ki("PropertyExpected","Property expected"),hi.PropertyExpected);var i=o.getTokenOffset();if(s(),2===o.getToken()){r&&u(ki("TrailingComma","Trailing comma"),hi.TrailingComma,i,i+1);continue}}else r&&c(ki("ExpectedComma","Expected comma"),hi.CommaExpected);var a=d(t,n);a?t.properties.push(a):c(ki("PropertyExpected","Property expected"),hi.PropertyExpected,void 0,[],[2,5]),r=!0}return 2!==o.getToken()?c(ki("ExpectedCloseBrace","Expected comma or closing brace"),hi.CommaOrCloseBraceExpected,t):f(t,!0)}}(e)||m(e)||function(e){if(11===o.getToken()){var t=new Li(e,o.getTokenOffset());if(0===o.getTokenError()){var n=o.getTokenValue();try{var r=JSON.parse(n);if(!xr(r))return c(ki("InvalidNumberFormat","Invalid number format."),hi.Undefined,t);t.value=r}catch($o){return c(ki("InvalidNumberFormat","Invalid number format."),hi.Undefined,t)}t.isInteger=-1===n.indexOf(".")}return f(t,!0)}}(e)||function(e){switch(o.getToken()){case 7:return f(new Ei(e,o.getTokenOffset()),!0);case 8:return f(new wi(e,!0,o.getTokenOffset()),!0);case 9:return f(new wi(e,!1,o.getTokenOffset()),!0);default:return}}(e)}var p=void 0;return 17!==s()&&((p=g(p))?17!==o.getToken()&&c(ki("End of file expected","End of file expected."),hi.Undefined):c(ki("Invalid symbol","Expected a JSON object, array or literal."),hi.Undefined)),new Vi(p,n,a)}function Bi(e,t,n){if(null!==e&&"object"===typeof e){var r=t+"\t";if(Array.isArray(e)){if(0===e.length)return"[]";for(var i="[\n",o=0;o<e.length;o++)i+=r+Bi(e[o],r,n),o<e.length-1&&(i+=","),i+="\n";return i+=t+"]"}var a=Object.keys(e);if(0===a.length)return"{}";for(i="{\n",o=0;o<a.length;o++){var s=a[o];i+=r+JSON.stringify(s)+": "+Bi(e[s],r,n),o<a.length-1&&(i+=","),i+="\n"}return i+=t+"}"}return n(e)}var Ki,$i=_i(),Hi=function(){function e(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=Promise),void 0===r&&(r={}),this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r}return e.prototype.doResolve=function(e){for(var t=this.contributions.length-1;t>=0;t--){var n=this.contributions[t].resolveCompletion;if(n){var r=n(e);if(r)return r}}return this.promiseConstructor.resolve(e)},e.prototype.doComplete=function(e,t,n){var r=this,i={items:[],isIncomplete:!1},o=e.getText(),a=e.offsetAt(t),s=n.getNodeFromOffset(a,!0);if(this.isInComment(e,s?s.offset:0,a))return Promise.resolve(i);if(s&&a===s.offset+s.length&&a>0){var u=o[a-1];("object"===s.type&&"}"===u||"array"===s.type&&"]"===u)&&(s=s.parent)}var c,l=this.getCurrentWord(e,a);if(!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type){var f=a-l.length;f>0&&'"'===o[f-1]&&f--,c=Gn.create(e.positionAt(f),t)}else c=Gn.create(e.positionAt(s.offset),e.positionAt(s.offset+s.length));var h={},d={add:function(e){var t=e.label,n=h[t];if(n)n.documentation||(n.documentation=e.documentation),n.detail||(n.detail=e.detail);else{if((t=t.replace(/[\n]/g,"\u21b5")).length>60){var r=t.substr(0,57).trim()+"...";h[r]||(t=r)}c&&void 0!==e.insertText&&(e.textEdit=cr.replace(c,e.insertText)),e.label=t,h[t]=e,i.items.push(e)}},setAsIncomplete:function(){i.isIncomplete=!0},error:function(e){console.error(e)},log:function(e){console.log(e)},getNumberOfProposals:function(){return i.items.length}};return this.schemaService.getSchemaForResource(e.uri,n).then((function(t){var u=[],f=!0,m="",g=void 0;if(s&&"string"===s.type){var p=s.parent;p&&"property"===p.type&&p.keyNode===s&&(f=!p.valueNode,g=p,m=o.substr(s.offset+1,s.length-2),p&&(s=p.parent))}if(s&&"object"===s.type){if(s.offset===a)return i;s.properties.forEach((function(e){g&&g===e||(h[e.keyNode.value]=Wr.create("__"))}));var v="";f&&(v=r.evaluateSeparatorAfter(e,e.offsetAt(c.end))),t?r.getPropertyCompletions(t,n,s,f,v,d):r.getSchemaLessPropertyCompletions(n,s,m,d);var y=Fi(s);r.contributions.forEach((function(t){var n=t.collectPropertyCompletions(e.uri,y,l,f,""===v,d);n&&u.push(n)})),!t&&l.length>0&&'"'!==o.charAt(a-l.length-1)&&(d.add({kind:Dr.Property,label:r.getLabelForValue(l),insertText:r.getInsertTextForProperty(l,void 0,!1,v),insertTextFormat:jr.Snippet,documentation:""}),d.setAsIncomplete())}var b={};return t?r.getValueCompletions(t,n,s,a,e,d,b):r.getSchemaLessValueCompletions(n,s,a,e,d),r.contributions.length>0&&r.getContributedValueCompletions(n,s,a,e,d,u),r.promiseConstructor.all(u).then((function(){if(0===d.getNumberOfProposals()){var t=a;!s||"string"!==s.type&&"number"!==s.type&&"boolean"!==s.type&&"null"!==s.type||(t=s.offset+s.length);var n=r.evaluateSeparatorAfter(e,t);r.addFillerValueCompletions(b,n,d)}return i}))}))},e.prototype.getPropertyCompletions=function(e,t,n,r,i,o){var a=this;t.getMatchingSchemas(e.schema,n.offset).forEach((function(e){if(e.node===n&&!e.inverted){var t=e.schema.properties;t&&Object.keys(t).forEach((function(e){var n=t[e];if("object"===typeof n&&!n.deprecationMessage&&!n.doNotSuggest){var s={kind:Dr.Property,label:e,insertText:a.getInsertTextForProperty(e,n,r,i),insertTextFormat:jr.Snippet,filterText:a.getFilterTextForValue(e),documentation:a.fromMarkup(n.markdownDescription)||n.description||""};void 0!==n.suggestSortText&&(s.sortText=n.suggestSortText),s.insertText&&Nr(s.insertText,"$1"+i)&&(s.command={title:"Suggest",command:"editor.action.triggerSuggest"}),o.add(s)}}));var s=e.schema.propertyNames;if("object"===typeof s&&!s.deprecationMessage&&!s.doNotSuggest){var u=function(e,t){void 0===t&&(t=void 0);var n={kind:Dr.Property,label:e,insertText:a.getInsertTextForProperty(e,void 0,r,i),insertTextFormat:jr.Snippet,filterText:a.getFilterTextForValue(e),documentation:t||a.fromMarkup(s.markdownDescription)||s.description||""};void 0!==s.suggestSortText&&(n.sortText=s.suggestSortText),n.insertText&&Nr(n.insertText,"$1"+i)&&(n.command={title:"Suggest",command:"editor.action.triggerSuggest"}),o.add(n)};if(s.enum)for(var c=0;c<s.enum.length;c++){var l=void 0;s.markdownEnumDescriptions&&c<s.markdownEnumDescriptions.length?l=a.fromMarkup(s.markdownEnumDescriptions[c]):s.enumDescriptions&&c<s.enumDescriptions.length&&(l=s.enumDescriptions[c]),u(s.enum[c],l)}s.const&&u(s.const)}}}))},e.prototype.getSchemaLessPropertyCompletions=function(e,t,n,r){var i=this,o=function(e){e.properties.forEach((function(e){var t=e.keyNode.value;r.add({kind:Dr.Property,label:t,insertText:i.getInsertTextForValue(t,""),insertTextFormat:jr.Snippet,filterText:i.getFilterTextForValue(t),documentation:""})}))};if(t.parent)if("property"===t.parent.type){var a=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e!==t.parent&&e.keyNode.value===a&&e.valueNode&&"object"===e.valueNode.type&&o(e.valueNode),!0}))}else"array"===t.parent.type&&t.parent.items.forEach((function(e){"object"===e.type&&e!==t&&o(e)}));else"object"===t.type&&r.add({kind:Dr.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",void 0,!0,""),insertTextFormat:jr.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(e,t,n,r,i){var o=this,a=n;if(!t||"string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(a=t.offset+t.length,t=t.parent),!t)return i.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:jr.Snippet,documentation:""}),void i.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:jr.Snippet,documentation:""});var s=this.evaluateSeparatorAfter(r,a),u=function(e){e.parent&&!Ui(e.parent,n,!0)&&i.add({kind:o.getSuggestionKind(e.type),label:o.getLabelTextForMatchingNode(e,r),insertText:o.getInsertTextForMatchingNode(e,r,s),insertTextFormat:jr.Snippet,documentation:""}),"boolean"===e.type&&o.addBooleanValueCompletion(!e.value,s,i)};if("property"===t.type&&n>(t.colonOffset||0)){var c=t.valueNode;if(c&&(n>c.offset+c.length||"object"===c.type||"array"===c.type))return;var l=t.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===l&&e.valueNode&&u(e.valueNode),!0})),"$schema"===l&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(s,i)}if("array"===t.type)if(t.parent&&"property"===t.parent.type){var f=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===f&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(u),!0}))}else t.items.forEach(u)},e.prototype.getValueCompletions=function(e,t,n,r,i,o,a){var s=r,u=void 0,c=void 0;if(!n||"string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type||(s=n.offset+n.length,c=n,n=n.parent),n){if("property"===n.type&&r>(n.colonOffset||0)){var l=n.valueNode;if(l&&r>l.offset+l.length)return;u=n.keyNode.value,n=n.parent}if(n&&(void 0!==u||"array"===n.type)){for(var f=this.evaluateSeparatorAfter(i,s),h=0,d=t.getMatchingSchemas(e.schema,n.offset,c);h<d.length;h++){var m=d[h];if(m.node===n&&!m.inverted&&m.schema){if("array"===n.type&&m.schema.items)if(Array.isArray(m.schema.items)){var g=this.findItemAtOffset(n,i,r);g<m.schema.items.length&&this.addSchemaValueCompletions(m.schema.items[g],f,o,a)}else this.addSchemaValueCompletions(m.schema.items,f,o,a);if(void 0!==u){var p=!1;if(m.schema.properties)(_=m.schema.properties[u])&&(p=!0,this.addSchemaValueCompletions(_,f,o,a));if(m.schema.patternProperties&&!p)for(var v=0,y=Object.keys(m.schema.patternProperties);v<y.length;v++){var b=y[v];if(Lr(b).test(u)){p=!0;var _=m.schema.patternProperties[b];this.addSchemaValueCompletions(_,f,o,a)}}if(m.schema.additionalProperties&&!p){_=m.schema.additionalProperties;this.addSchemaValueCompletions(_,f,o,a)}}}}"$schema"!==u||n.parent||this.addDollarSchemaCompletions(f,o),a.boolean&&(this.addBooleanValueCompletion(!0,f,o),this.addBooleanValueCompletion(!1,f,o)),a.null&&this.addNullValueCompletion(f,o)}}else this.addSchemaValueCompletions(e.schema,"",o,a)},e.prototype.getContributedValueCompletions=function(e,t,n,r,i,o){if(t){if("string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(t=t.parent),t&&"property"===t.type&&n>(t.colonOffset||0)){var a=t.keyNode.value,s=t.valueNode;if((!s||n<=s.offset+s.length)&&t.parent){var u=Fi(t.parent);this.contributions.forEach((function(e){var t=e.collectValueCompletions(r.uri,u,a,i);t&&o.push(t)}))}}}else this.contributions.forEach((function(e){var t=e.collectDefaultCompletions(r.uri,i);t&&o.push(t)}))},e.prototype.addSchemaValueCompletions=function(e,t,n,r){var i=this;"object"===typeof e&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.anyOf)&&e.anyOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.oneOf)&&e.oneOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})))},e.prototype.addDefaultValueCompletions=function(e,t,n,r){var i=this;void 0===r&&(r=0);var o=!1;if(Ar(e.default)){for(var a=e.type,s=e.default,u=r;u>0;u--)s=[s],a="array";n.add({kind:this.getSuggestionKind(a),label:this.getLabelForValue(s),insertText:this.getInsertTextForValue(s,t),insertTextFormat:jr.Snippet,detail:$i("json.suggest.default","Default value")}),o=!0}Array.isArray(e.examples)&&e.examples.forEach((function(a){for(var s=e.type,u=a,c=r;c>0;c--)u=[u],s="array";n.add({kind:i.getSuggestionKind(s),label:i.getLabelForValue(u),insertText:i.getInsertTextForValue(u,t),insertTextFormat:jr.Snippet}),o=!0})),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach((function(a){var s,u,c=e.type,l=a.body,f=a.label;if(Ar(l)){e.type;for(var h=r;h>0;h--)l=[l],"array";s=i.getInsertTextForSnippetValue(l,t),u=i.getFilterTextForSnippetValue(l),f=f||i.getLabelForSnippetValue(l)}else{if("string"!==typeof a.bodyText)return;var d="",m="",g="";for(h=r;h>0;h--)d=d+g+"[\n",m=m+"\n"+g+"]",g+="\t",c="array";s=d+g+a.bodyText.split("\n").join("\n"+g)+m+t,f=f||s,u=s.replace(/[\n]/g,"")}n.add({kind:i.getSuggestionKind(c),label:f,documentation:i.fromMarkup(a.markdownDescription)||a.description,insertText:s,insertTextFormat:jr.Snippet,filterText:u}),o=!0})),!o&&"object"===typeof e.items&&!Array.isArray(e.items)&&r<5&&this.addDefaultValueCompletions(e.items,t,n,r+1)},e.prototype.addEnumValueCompletions=function(e,t,n){if(Ar(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:jr.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(var r=0,i=e.enum.length;r<i;r++){var o=e.enum[r],a=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?a=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(a=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(o),insertText:this.getInsertTextForValue(o,t),insertTextFormat:jr.Snippet,documentation:a})}},e.prototype.collectTypes=function(e,t){if(!Array.isArray(e.enum)&&!Ar(e.const)){var n=e.type;Array.isArray(n)?n.forEach((function(e){return t[e]=!0})):n&&(t[n]=!0)}},e.prototype.addFillerValueCompletions=function(e,t,n){e.object&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:jr.Snippet,detail:$i("defaults.object","New object"),documentation:""}),e.array&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:jr.Snippet,detail:$i("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:jr.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:jr.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(e,t){var n=this,r=this.schemaService.getRegisteredSchemaIds((function(e){return"http"===e||"https"===e}));r.forEach((function(r){return t.add({kind:Dr.Module,label:n.getLabelForValue(r),filterText:n.getFilterTextForValue(r),insertText:n.getInsertTextForValue(r,e),insertTextFormat:jr.Snippet,documentation:""})}))},e.prototype.getLabelForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getLabelForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getInsertTextForPlainText=function(e){return e.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(e,t){var n=JSON.stringify(e,null,"\t");return"{}"===n?"{$1}"+t:"[]"===n?"[$1]"+t:this.getInsertTextForPlainText(n+t)},e.prototype.getInsertTextForSnippetValue=function(e,t){return Bi(e,"",(function(e){return"string"===typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)}))+t},e.prototype.getInsertTextForGuessedValue=function(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":var n=JSON.stringify(e);return n=n.substr(1,n.length-2),'"${1:'+(n=this.getInsertTextForPlainText(n))+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)},e.prototype.getSuggestionKind=function(e){if(Array.isArray(e)){var t=e;e=t.length>0?t[0]:void 0}if(!e)return Dr.Value;switch(e){case"string":default:return Dr.Value;case"object":return Dr.Module;case"property":return Dr.Property}},e.prototype.getLabelTextForMatchingNode=function(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:return t.getText().substr(e.offset,e.length)}},e.prototype.getInsertTextForMatchingNode=function(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:var r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}},e.prototype.getInsertTextForProperty=function(e,t,n,r){var i=this.getInsertTextForValue(e,"");if(!n)return i;var o,a=i+": ",s=0;if(t){if(Array.isArray(t.defaultSnippets)){if(1===t.defaultSnippets.length){var u=t.defaultSnippets[0].body;Ar(u)&&(o=this.getInsertTextForSnippetValue(u,""))}s+=t.defaultSnippets.length}if(t.enum&&(o||1!==t.enum.length||(o=this.getInsertTextForGuessedValue(t.enum[0],"")),s+=t.enum.length),Ar(t.default)&&(o||(o=this.getInsertTextForGuessedValue(t.default,"")),s++),Array.isArray(t.examples)&&t.examples.length&&(o||(o=this.getInsertTextForGuessedValue(t.examples[0],"")),s+=t.examples.length),0===s){var c=Array.isArray(t.type)?t.type[0]:t.type;switch(c||(t.properties?c="object":t.items&&(c="array")),c){case"boolean":o="$1";break;case"string":o='"$1"';break;case"object":o="{$1}";break;case"array":o="[$1]";break;case"number":case"integer":o="${1:0}";break;case"null":o="${1:null}";break;default:return i}}}return(!o||s>1)&&(o="$1"),a+o+r},e.prototype.getCurrentWord=function(e,t){for(var n=t-1,r=e.getText();n>=0&&-1===' \t\n\r\v":{[,]}'.indexOf(r.charAt(n));)n--;return r.substring(n+1,t)},e.prototype.evaluateSeparatorAfter=function(e,t){var n=yr(e.getText(),!0);switch(n.setPosition(t),n.scan()){case 5:case 2:case 4:case 17:return"";default:return","}},e.prototype.findItemAtOffset=function(e,t,n){for(var r=yr(t.getText(),!0),i=e.items,o=i.length-1;o>=0;o--){var a=i[o];if(n>a.offset+a.length)return r.setPosition(a.offset+a.length),5===r.scan()&&n>=r.getTokenOffset()+r.getTokenLength()?o+1:o;if(n>=a.offset)return o}return 0},e.prototype.isInComment=function(e,t,n){var r=yr(e.getText(),!1);r.setPosition(t);for(var i=r.scan();17!==i&&r.getTokenOffset()+r.getTokenLength()<n;)i=r.scan();return(12===i||13===i)&&r.getTokenOffset()<=n},e.prototype.fromMarkup=function(e){if(e&&this.doesSupportMarkdown())return{kind:Mr.Markdown,value:e}},e.prototype.doesSupportMarkdown=function(){if(!Ar(this.supportsMarkdown)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=e&&e.completionItem&&Array.isArray(e.completionItem.documentationFormat)&&-1!==e.completionItem.documentationFormat.indexOf(Mr.Markdown)}return this.supportsMarkdown},e.prototype.doesSupportsCommitCharacters=function(){if(!Ar(this.supportsCommitCharacters)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsCommitCharacters=e&&e.completionItem&&!!e.completionItem.commitCharactersSupport}return this.supportsCommitCharacters},e}(),Yi=function(){function e(e,t,n){void 0===t&&(t=[]),this.schemaService=e,this.contributions=t,this.promise=n||Promise}return e.prototype.doHover=function(e,t,n){var r=e.offsetAt(t),i=n.getNodeFromOffset(r);if(!i||("object"===i.type||"array"===i.type)&&r>i.offset+1&&r<i.offset+i.length-1)return this.promise.resolve(null);var o=i;if("string"===i.type){var a=i.parent;if(a&&"property"===a.type&&a.keyNode===i&&!(i=a.valueNode))return this.promise.resolve(null)}for(var s=Gn.create(e.positionAt(o.offset),e.positionAt(o.offset+o.length)),u=function(e){return{contents:e,range:s}},c=Fi(i),l=this.contributions.length-1;l>=0;l--){var f=this.contributions[l].getInfoContribution(e.uri,c);if(f)return f.then((function(e){return u(e)}))}return this.schemaService.getSchemaForResource(e.uri,n).then((function(e){if(e&&i){var t=n.getMatchingSchemas(e.schema,i.offset),r=void 0,o=void 0,a=void 0,s=void 0;t.every((function(e){if(e.node===i&&!e.inverted&&e.schema&&(r=r||e.schema.title,o=o||e.schema.markdownDescription||zi(e.schema.description),e.schema.enum)){var t=e.schema.enum.indexOf(ji(i));e.schema.markdownEnumDescriptions?a=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(a=zi(e.schema.enumDescriptions[t])),a&&"string"!==typeof(s=e.schema.enum[t])&&(s=JSON.stringify(s))}return!0}));var c="";return r&&(c=zi(r)),o&&(c.length>0&&(c+="\n\n"),c+=o),a&&(c.length>0&&(c+="\n\n"),c+="`"+function(e){if(-1!==e.indexOf("`"))return"`` "+e+" ``";return e}(s)+"`: "+a),u([c])}return null}))},e}();function zi(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3").replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}Ki=function(){var e={470:function(e){function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,o=-1,a=0,s=0;s<=e.length;++s){if(s<e.length)n=e.charCodeAt(s);else{if(47===n)break;n=47}if(47===n){if(o===s-1||1===a);else if(o!==s-1&&2===a){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var u=r.lastIndexOf("/");if(u!==r.length-1){-1===u?(r="",i=0):i=(r=r.slice(0,u)).length-1-r.lastIndexOf("/"),o=s,a=0;continue}}else if(2===r.length||1===r.length){r="",i=0,o=s,a=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(o+1,s):r=e.slice(o+1,s),i=s-o-1;o=s,a=0}else 46===n&&-1!==a?++a:a=-1}return r}var r={resolve:function(){for(var e,r="",i=!1,o=arguments.length-1;o>=-1&&!i;o--){var a;o>=0?a=arguments[o]:(void 0===e&&(e=process.cwd()),a=e),t(a),0!==a.length&&(r=a+"/"+r,i=47===a.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var o=e.length,a=o-i,s=1;s<n.length&&47===n.charCodeAt(s);++s);for(var u=n.length-s,c=a<u?a:u,l=-1,f=0;f<=c;++f){if(f===c){if(u>c){if(47===n.charCodeAt(s+f))return n.slice(s+f+1);if(0===f)return n.slice(s+f)}else a>c&&(47===e.charCodeAt(i+f)?l=f:0===f&&(l=0));break}var h=e.charCodeAt(i+f);if(h!==n.charCodeAt(s+f))break;47===h&&(l=f)}var d="";for(f=i+l+1;f<=o;++f)f!==o&&47!==e.charCodeAt(f)||(0===d.length?d+="..":d+="/..");return d.length>0?d+n.slice(s+l):(s+=l,47===n.charCodeAt(s)&&++s,n.slice(s))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,o=!0,a=e.length-1;a>=1;--a)if(47===(n=e.charCodeAt(a))){if(!o){i=a;break}}else o=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,o=-1,a=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var s=n.length-1,u=-1;for(r=e.length-1;r>=0;--r){var c=e.charCodeAt(r);if(47===c){if(!a){i=r+1;break}}else-1===u&&(a=!1,u=r+1),s>=0&&(c===n.charCodeAt(s)?-1==--s&&(o=r):(s=-1,o=u))}return i===o?o=u:-1===o&&(o=e.length),e.slice(i,o)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!a){i=r+1;break}}else-1===o&&(a=!1,o=r+1);return-1===o?"":e.slice(i,o)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,o=!0,a=0,s=e.length-1;s>=0;--s){var u=e.charCodeAt(s);if(47!==u)-1===i&&(o=!1,i=s+1),46===u?-1===n?n=s:1!==a&&(a=1):-1!==n&&(a=-1);else if(!o){r=s+1;break}}return-1===n||-1===i||0===a||1===a&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),o=47===i;o?(n.root="/",r=1):r=0;for(var a=-1,s=0,u=-1,c=!0,l=e.length-1,f=0;l>=r;--l)if(47!==(i=e.charCodeAt(l)))-1===u&&(c=!1,u=l+1),46===i?-1===a?a=l:1!==f&&(f=1):-1!==a&&(f=-1);else if(!c){s=l+1;break}return-1===a||-1===u||0===f||1===f&&a===u-1&&a===s+1?-1!==u&&(n.base=n.name=0===s&&o?e.slice(1,u):e.slice(s,u)):(0===s&&o?(n.name=e.slice(1,a),n.base=e.slice(1,u)):(n.name=e.slice(s,a),n.base=e.slice(s,u)),n.ext=e.slice(a,u)),s>0?n.dir=e.slice(0,s-1):o&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r},447:function(e,t,n){var r;if(n.r(t),n.d(t,{URI:function(){return m},Utils:function(){return A}}),"object"==typeof process)r="win32"===process.platform;else if("object"==typeof navigator){var i=navigator.userAgent;r=i.indexOf("Windows")>=0}var o,a,s=(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),u=/^\w[\w\d+.-]*$/,c=/^\//,l=/^\/\//,f="",h="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,m=function(){function e(e,t,n,r,i,o){void 0===o&&(o=!1),"object"==typeof e?(this.scheme=e.scheme||f,this.authority=e.authority||f,this.path=e.path||f,this.query=e.query||f,this.fragment=e.fragment||f):(this.scheme=function(e,t){return e||t?e:"file"}(e,o),this.authority=t||f,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==h&&(t=h+t):t=h}return t}(this.scheme,n||f),this.query=r||f,this.fragment=i||f,function(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!u.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!c.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,o))}return e.isUri=function(t){return t instanceof e||!!t&&"string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"function"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString},Object.defineProperty(e.prototype,"fsPath",{get:function(){return _(this,!1)},enumerable:!1,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,i=e.query,o=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=f),void 0===n?n=this.authority:null===n&&(n=f),void 0===r?r=this.path:null===r&&(r=f),void 0===i?i=this.query:null===i&&(i=f),void 0===o?o=this.fragment:null===o&&(o=f),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&o===this.fragment?this:new p(t,n,r,i,o)},e.parse=function(e,t){void 0===t&&(t=!1);var n=d.exec(e);return n?new p(n[2]||f,x(n[4]||f),x(n[5]||f),x(n[7]||f),x(n[9]||f),t):new p(f,f,f,f,f)},e.file=function(e){var t=f;if(r&&(e=e.replace(/\\/g,h)),e[0]===h&&e[1]===h){var n=e.indexOf(h,2);-1===n?(t=e.substring(2),e=h):(t=e.substring(2,n),e=e.substring(n)||h)}return new p("file",t,e,f,f)},e.from=function(e){return new p(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),C(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new p(t);return n._formatted=t.external,n._fsPath=t._sep===g?t.fsPath:null,n}return t},e}(),g=r?1:void 0,p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return s(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=_(this,!1)),this._fsPath},enumerable:!1,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?C(this,!0):(this._formatted||(this._formatted=C(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=g),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(m),v=((a={})[58]="%3A",a[47]="%2F",a[63]="%3F",a[35]="%23",a[91]="%5B",a[93]="%5D",a[64]="%40",a[33]="%21",a[36]="%24",a[38]="%26",a[39]="%27",a[40]="%28",a[41]="%29",a[42]="%2A",a[43]="%2B",a[44]="%2C",a[59]="%3B",a[61]="%3D",a[32]="%20",a);function y(e,t){for(var n=void 0,r=-1,i=0;i<e.length;i++){var o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));var a=v[o];void 0!==a?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=a):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function b(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=v[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function _(e,t){var n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,r&&(n=n.replace(/\//g,"\\")),n}function C(e,t){var n=t?b:y,r="",i=e.scheme,o=e.authority,a=e.path,s=e.query,u=e.fragment;if(i&&(r+=i,r+=":"),(o||"file"===i)&&(r+=h,r+=h),o){var c=o.indexOf("@");if(-1!==c){var l=o.substr(0,c);o=o.substr(c+1),-1===(c=l.indexOf(":"))?r+=n(l,!1):(r+=n(l.substr(0,c),!1),r+=":",r+=n(l.substr(c+1),!1)),r+="@"}-1===(c=(o=o.toLowerCase()).indexOf(":"))?r+=n(o,!1):(r+=n(o.substr(0,c),!1),r+=o.substr(c))}if(a){if(a.length>=3&&47===a.charCodeAt(0)&&58===a.charCodeAt(2))(f=a.charCodeAt(1))>=65&&f<=90&&(a="/"+String.fromCharCode(f+32)+":"+a.substr(3));else if(a.length>=2&&58===a.charCodeAt(1)){var f;(f=a.charCodeAt(0))>=65&&f<=90&&(a=String.fromCharCode(f+32)+":"+a.substr(2))}r+=n(a,!0)}return s&&(r+="?",r+=n(s,!1)),u&&(r+="#",r+=t?u:y(u,!1)),r}function S(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+S(e.substr(3)):e}}var k=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function x(e){return e.match(k)?e.replace(k,(function(e){return S(e)})):e}var A,E=n(470),w=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r},N=E.posix||E;!function(e){e.joinPath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return e.with({path:N.join.apply(N,w([e.path],t))})},e.resolvePath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=e.path||"/";return e.with({path:N.resolve.apply(N,w([r],t))})},e.dirname=function(e){var t=N.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)?e:e.with({path:t})},e.basename=function(e){return N.basename(e.path)},e.extname=function(e){return N.extname(e.path)}}(A||(A={}))}},t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}return n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(447)}();var Gi=Ki,Ji=Gi.URI;Gi.Utils;function Qi(e,t){if("string"!==typeof e)throw new TypeError("Expected a string");for(var n,r=String(e),i="",o=!!t&&!!t.extended,a=!!t&&!!t.globstar,s=!1,u=t&&"string"===typeof t.flags?t.flags:"",c=0,l=r.length;c<l;c++)switch(n=r[c]){case"/":case"$":case"^":case"+":case".":case"(":case")":case"=":case"!":case"|":i+="\\"+n;break;case"?":if(o){i+=".";break}case"[":case"]":if(o){i+=n;break}case"{":if(o){s=!0,i+="(";break}case"}":if(o){s=!1,i+=")";break}case",":if(s){i+="|";break}i+="\\"+n;break;case"*":for(var f=r[c-1],h=1;"*"===r[c+1];)h++,c++;var d=r[c+1];if(a)h>1&&("/"===f||void 0===f||"{"===f||","===f)&&("/"===d||void 0===d||","===d||"}"===d)?("/"===d?c++:"/"===f&&i.endsWith("\\/")&&(i=i.substr(0,i.length-2)),i+="((?:[^/]*(?:/|$))*)"):i+="([^/]*)";else i+=".*";break;default:i+=n}return u&&~u.indexOf("g")||(i="^"+i+"$"),new RegExp(i,u)}var Xi=_i(),Zi=function(){function e(e,t){this.globWrappers=[];try{for(var n=0,r=e;n<r.length;n++){var i=r[n],o="!"!==i[0];o||(i=i.substring(1)),i.length>0&&("/"===i[0]&&(i=i.substring(1)),this.globWrappers.push({regexp:Qi("**/"+i,{extended:!0,globstar:!0}),include:o}))}this.uris=t}catch($o){this.globWrappers.length=0,this.uris=[]}}return e.prototype.matchesPattern=function(e){for(var t=!1,n=0,r=this.globWrappers;n<r.length;n++){var i=r[n],o=i.regexp,a=i.include;o.test(e)&&(t=a)}return t},e.prototype.getURIs=function(){return this.uris},e}(),eo=function(){function e(e,t,n){this.service=e,this.url=t,this.dependencies={},n&&(this.unresolvedSchema=this.service.promise.resolve(new to(n)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.url)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var e=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then((function(t){return e.service.resolveSchemaContent(t,e.url,e.dependencies)}))),this.resolvedSchema},e.prototype.clearSchema=function(){this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies={}},e}(),to=function(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t},no=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e.prototype.getSection=function(e){var t=this.getSectionRecursive(e,this.schema);if(t)return Pi(t)},e.prototype.getSectionRecursive=function(e,t){if(!t||"boolean"===typeof t||0===e.length)return t;var n=e.shift();if(t.properties&&(t.properties[n],1))return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties)for(var r=0,i=Object.keys(t.patternProperties);r<i.length;r++){var o=i[r];if(Lr(o).test(n))return this.getSectionRecursive(e,t.patternProperties[o])}else{if("object"===typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(n.match("[0-9]+"))if(Array.isArray(t.items)){var a=parseInt(n,10);if(!isNaN(a)&&t.items[a])return this.getSectionRecursive(e,t.items[a])}else if(t.items)return this.getSectionRecursive(e,t.items)}},e}(),ro=function(){function e(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}return e.prototype.getRegisteredSchemaIds=function(e){return Object.keys(this.registeredSchemasIds).filter((function(t){var n=Ji.parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))}))},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(e){for(var t=this,n=!1,r=[e=oo(e)],i=Object.keys(this.schemasById).map((function(e){return t.schemasById[e]}));r.length;)for(var o=r.pop(),a=0;a<i.length;a++){var s=i[a];s&&(s.url===o||s.dependencies[o])&&(s.url!==o&&r.push(s.url),s.clearSchema(),i[a]=void 0,n=!0)}return n},e.prototype.setSchemaContributions=function(e){if(e.schemas){var t=e.schemas;for(var n in t){var r=oo(n);this.contributionSchemas[r]=this.addSchemaHandle(r,t[n])}}if(Array.isArray(e.schemaAssociations))for(var i=0,o=e.schemaAssociations;i<o.length;i++){var a=o[i],s=a.uris.map(oo),u=this.addFilePatternAssociation(a.pattern,s);this.contributionAssociations.push(u)}},e.prototype.addSchemaHandle=function(e,t){var n=new eo(this,e,t);return this.schemasById[e]=n,n},e.prototype.getOrAddSchemaHandle=function(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)},e.prototype.addFilePatternAssociation=function(e,t){var n=new Zi(e,t);return this.filePatternAssociations.push(n),n},e.prototype.registerExternalSchema=function(e,t,n){var r=oo(e);return this.registeredSchemasIds[r]=!0,this.cachedSchemaForResource=void 0,t&&this.addFilePatternAssociation(t,[e]),n?this.addSchemaHandle(r,n):this.getOrAddSchemaHandle(r)},e.prototype.clearExternalSchemas=function(){for(var e in this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0,this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(var t=0,n=this.contributionAssociations;t<n.length;t++){var r=n[t];this.filePatternAssociations.push(r)}},e.prototype.getResolvedSchema=function(e){var t=oo(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(void 0)},e.prototype.loadSchema=function(e){if(!this.requestService){var t=Xi("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",ao(e));return this.promise.resolve(new to({},[t]))}return this.requestService(e).then((function(t){if(!t){var n=Xi("json.schema.nocontent","Unable to load schema from '{0}': No content.",ao(e));return new to({},[n])}var r,i=[];r=br(t,i);var o=i.length?[Xi("json.schema.invalidFormat","Unable to parse content from '{0}': Parse error at offset {1}.",ao(e),i[0].offset)]:[];return new to(r,o)}),(function(t){var n=t.toString(),r=t.toString().split("Error: ");return r.length>1&&(n=r[1]),Nr(n,".")&&(n=n.substr(0,n.length-1)),new to({},[Xi("json.schema.nocontent","Unable to load schema from '{0}': {1}.",ao(e),n)])}))},e.prototype.resolveSchemaContent=function(e,t,n){var r=this,i=e.errors.slice(0),o=e.schema;if(o.$schema){var a=oo(o.$schema);if("http://json-schema.org/draft-03/schema"===a)return this.promise.resolve(new no({},[Xi("json.schema.draft03.notsupported","Draft-03 schemas are not supported.")]));"https://json-schema.org/draft/2019-09/schema"===a&&i.push(Xi("json.schema.draft201909.notsupported","Draft 2019-09 schemas are not yet fully supported."))}var s=this.contextService,u=function(e,t,n,r){var o=r?decodeURIComponent(r):void 0,a=function(e,t){if(!t)return e;var n=e;return"/"===t[0]&&(t=t.substr(1)),t.split("/").some((function(e){return e=e.replace(/~1/g,"/").replace(/~0/g,"~"),!(n=n[e])})),n}(t,o);if(a)for(var s in a)a.hasOwnProperty(s)&&!e.hasOwnProperty(s)&&(e[s]=a[s]);else i.push(Xi("json.schema.invalidref","$ref '{0}' in '{1}' can not be resolved.",o,n))},c=function(e,t,n,o,a){s&&!/^[A-Za-z][A-Za-z0-9+\-.+]*:\/\/.*/.test(t)&&(t=s.resolveRelativePath(t,o)),t=oo(t);var c=r.getOrAddSchemaHandle(t);return c.getUnresolvedSchema().then((function(r){if(a[t]=!0,r.errors.length){var o=n?t+"#"+n:t;i.push(Xi("json.schema.problemloadingref","Problems loading reference '{0}': {1}",o,r.errors[0]))}return u(e,r.schema,t,n),l(e,r.schema,t,c.dependencies)}))},l=function(e,t,n,i){if(!e||"object"!==typeof e)return Promise.resolve(null);for(var o=[e],a=[],s=[],l=function(e){for(var r=[];e.$ref;){var a=e.$ref,l=a.split("#",2);if(delete e.$ref,l[0].length>0)return void s.push(c(e,l[0],l[1],n,i));-1===r.indexOf(a)&&(u(e,t,n,l[1]),r.push(a))}!function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];"object"===typeof i&&o.push(i)}}(e.items,e.additionalItems,e.additionalProperties,e.not,e.contains,e.propertyNames,e.if,e.then,e.else),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if("object"===typeof i)for(var a in i){var s=i[a];"object"===typeof s&&o.push(s)}}}(e.definitions,e.properties,e.patternProperties,e.dependencies),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if(Array.isArray(i))for(var a=0,s=i;a<s.length;a++){var u=s[a];"object"===typeof u&&o.push(u)}}}(e.anyOf,e.allOf,e.oneOf,e.items)};o.length;){var f=o.pop();a.indexOf(f)>=0||(a.push(f),l(f))}return r.promise.all(s)};return l(o,o,t,n).then((function(e){return new no(o,i)}))},e.prototype.getSchemaForResource=function(e,t){if(t&&t.root&&"object"===t.root.type){var n=t.root.properties.filter((function(e){return"$schema"===e.keyNode.value&&e.valueNode&&"string"===e.valueNode.type}));if(n.length>0){var r=n[0].valueNode;if(r&&"string"===r.type){var i=ji(r);if(i&&wr(i,".")&&this.contextService&&(i=this.contextService.resolveRelativePath(i,e)),i){var o=oo(i);return this.getOrAddSchemaHandle(o).getResolvedSchema()}}}}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===e)return this.cachedSchemaForResource.resolvedSchema;for(var a=Object.create(null),s=[],u=function(e){try{return Ji.parse(e).with({fragment:null,query:null}).toString()}catch($o){return e}}(e),c=0,l=this.filePatternAssociations;c<l.length;c++){var f=l[c];if(f.matchesPattern(u))for(var h=0,d=f.getURIs();h<d.length;h++){var m=d[h];a[m]||(s.push(m),a[m]=!0)}}var g=s.length>0?this.createCombinedSchema(e,s).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:e,resolvedSchema:g},g},e.prototype.createCombinedSchema=function(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);var n="schemaservice://combinedSchema/"+encodeURIComponent(e),r={allOf:t.map((function(e){return{$ref:e}}))};return this.addSchemaHandle(n,r)},e.prototype.getMatchingSchemas=function(e,t,n){if(n){var r=n.id||"schemaservice://untitled/matchingSchemas/"+io++;return this.resolveSchemaContent(new to(n),r,{}).then((function(e){return t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted}))}))}return this.getSchemaForResource(e.uri,t).then((function(e){return e?t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted})):[]}))},e}(),io=0;function oo(e){try{return Ji.parse(e).toString()}catch($o){return e}}function ao(e){try{var t=Ji.parse(e);if("file"===t.scheme)return t.fsPath}catch($o){}return e}var so=_i(),uo=function(){function e(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}return e.prototype.configure=function(e){e&&(this.validationEnabled=!1!==e.validate,this.commentSeverity=e.allowComments?void 0:ir.Error)},e.prototype.doValidation=function(e,t,n,r){var i=this;if(!this.validationEnabled)return this.promise.resolve([]);var o=[],a={},s=function(e){var t=e.range.start.line+" "+e.range.start.character+" "+e.message;a[t]||(a[t]=!0,o.push(e))},u=function(r){var a=(null===n||void 0===n?void 0:n.trailingCommas)?ho(n.trailingCommas):ir.Error,u=(null===n||void 0===n?void 0:n.comments)?ho(n.comments):i.commentSeverity,c=(null===n||void 0===n?void 0:n.schemaValidation)?ho(n.schemaValidation):ir.Warning,l=(null===n||void 0===n?void 0:n.schemaRequest)?ho(n.schemaRequest):ir.Warning;if(r){if(r.errors.length&&t.root&&l){var f=t.root,h="object"===f.type?f.properties[0]:void 0;if(h&&"$schema"===h.keyNode.value){var d=h.valueNode||h,m=Gn.create(e.positionAt(d.offset),e.positionAt(d.offset+d.length));s(sr.create(m,r.errors[0],l,hi.SchemaResolveError))}else{m=Gn.create(e.positionAt(f.offset),e.positionAt(f.offset+1));s(sr.create(m,r.errors[0],l,hi.SchemaResolveError))}}else if(c){var g=t.validate(e,r.schema,c);g&&g.forEach(s)}lo(r.schema)&&(u=void 0),fo(r.schema)&&(a=void 0)}for(var p=0,v=t.syntaxErrors;p<v.length;p++){var y=v[p];if(y.code===hi.TrailingComma){if("number"!==typeof a)continue;y.severity=a}s(y)}if("number"===typeof u){var b=so("InvalidCommentToken","Comments are not permitted in JSON.");t.comments.forEach((function(e){s(sr.create(e,b,u,hi.CommentNotPermitted))}))}return o};if(r){var c=r.id||"schemaservice://untitled/"+co++;return this.jsonSchemaService.resolveSchemaContent(new to(r),c,{}).then((function(e){return u(e)}))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then((function(e){return u(e)}))},e}(),co=0;function lo(e){if(e&&"object"===typeof e){if(Er(e.allowComments))return e.allowComments;if(e.allOf)for(var t=0,n=e.allOf;t<n.length;t++){var r=lo(n[t]);if(Er(r))return r}}}function fo(e){if(e&&"object"===typeof e){if(Er(e.allowTrailingCommas))return e.allowTrailingCommas;var t=e;if(Er(t.allowsTrailingCommas))return t.allowsTrailingCommas;if(e.allOf)for(var n=0,r=e.allOf;n<r.length;n++){var i=fo(r[n]);if(Er(i))return i}}}function ho(e){switch(e){case"error":return ir.Error;case"warning":return ir.Warning;case"ignore":return}}var mo=48,go=57,po=65,vo=97,yo=102;function bo(e){return e<mo?0:e<=go?e-mo:(e<vo&&(e+=vo-po),e>=vo&&e<=yo?e-vo+10:0)}function _o(e){if("#"===e[0])switch(e.length){case 4:return{red:17*bo(e.charCodeAt(1))/255,green:17*bo(e.charCodeAt(2))/255,blue:17*bo(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*bo(e.charCodeAt(1))/255,green:17*bo(e.charCodeAt(2))/255,blue:17*bo(e.charCodeAt(3))/255,alpha:17*bo(e.charCodeAt(4))/255};case 7:return{red:(16*bo(e.charCodeAt(1))+bo(e.charCodeAt(2)))/255,green:(16*bo(e.charCodeAt(3))+bo(e.charCodeAt(4)))/255,blue:(16*bo(e.charCodeAt(5))+bo(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*bo(e.charCodeAt(1))+bo(e.charCodeAt(2)))/255,green:(16*bo(e.charCodeAt(3))+bo(e.charCodeAt(4)))/255,blue:(16*bo(e.charCodeAt(5))+bo(e.charCodeAt(6)))/255,alpha:(16*bo(e.charCodeAt(7))+bo(e.charCodeAt(8)))/255}}}var Co=function(){function e(e){this.schemaService=e}return e.prototype.findDocumentSymbols=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var o=n.resultLimit||Number.MAX_VALUE,a=e.uri;if(("vscode://defaultsettings/keybindings.json"===a||Nr(a.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var s=[],u=0,c=i.items;u<c.length;u++){var l=c[u];if("object"===l.type)for(var f=0,h=l.properties;f<h.length;f++){var d=h[f];if("key"===d.keyNode.value&&d.valueNode){var m=Jn.create(e.uri,So(e,l));if(s.push({name:ji(d.valueNode),kind:Gr.Function,location:m}),--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),s}}}return s}for(var g=[{node:i,containerName:""}],p=0,v=!1,y=[],b=function(t,n){"array"===t.type?t.items.forEach((function(e){e&&g.push({node:e,containerName:n})})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var a=Jn.create(e.uri,So(e,t)),s=n?n+"."+t.keyNode.value:t.keyNode.value;y.push({name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),location:a,containerName:n}),g.push({node:i,containerName:s})}else v=!0}))};p<g.length;){var _=g[p++];b(_.node,_.containerName)}return v&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),y},e.prototype.findDocumentSymbols2=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var o=n.resultLimit||Number.MAX_VALUE,a=e.uri;if(("vscode://defaultsettings/keybindings.json"===a||Nr(a.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var s=[],u=0,c=i.items;u<c.length;u++){var l=c[u];if("object"===l.type)for(var f=0,h=l.properties;f<h.length;f++){var d=h[f];if("key"===d.keyNode.value&&d.valueNode){var m=So(e,l),g=So(e,d.keyNode);if(s.push({name:ji(d.valueNode),kind:Gr.Function,range:m,selectionRange:g}),--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),s}}}return s}for(var p=[],v=[{node:i,result:p}],y=0,b=!1,_=function(t,n){"array"===t.type?t.items.forEach((function(t,i){if(t)if(o>0){o--;var a=So(e,t),s=a,u={name:String(i),kind:r.getSymbolKind(t.type),range:a,selectionRange:s,children:[]};n.push(u),v.push({result:u.children,node:t})}else b=!0})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(o>0){o--;var a=So(e,t),s=So(e,t.keyNode),u=[],c={name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),range:a,selectionRange:s,children:u,detail:r.getDetail(i)};n.push(c),v.push({result:u,node:i})}else b=!0}))};y<v.length;){var C=v[y++];_(C.node,C.result)}return b&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(a),p},e.prototype.getSymbolKind=function(e){switch(e){case"object":return Gr.Module;case"string":return Gr.String;case"number":return Gr.Number;case"array":return Gr.Array;case"boolean":return Gr.Boolean;default:return Gr.Variable}},e.prototype.getKeyLabel=function(e){var t=e.keyNode.value;return t&&(t=t.replace(/[\n]/g,"\u21b5")),t&&t.trim()?t:'"'+t+'"'},e.prototype.getDetail=function(e){if(e)return"boolean"===e.type||"number"===e.type||"null"===e.type||"string"===e.type?String(e.value):"array"===e.type?e.children.length?void 0:"[]":"object"===e.type?e.children.length?void 0:"{}":void 0},e.prototype.findDocumentColors=function(e,t,n){return this.schemaService.getSchemaForResource(e.uri,t).then((function(r){var i=[];if(r)for(var o=n&&"number"===typeof n.resultLimit?n.resultLimit:Number.MAX_VALUE,a={},s=0,u=t.getMatchingSchemas(r.schema);s<u.length;s++){var c=u[s];if(!c.inverted&&c.schema&&("color"===c.schema.format||"color-hex"===c.schema.format)&&c.node&&"string"===c.node.type){var l=String(c.node.offset);if(!a[l]){var f=_o(ji(c.node));if(f){var h=So(e,c.node);i.push({color:f,range:h})}if(a[l]=!0,--o<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(e.uri),i}}}return i}))},e.prototype.getColorPresentations=function(e,t,n,r){var i,o=[],a=Math.round(255*n.red),s=Math.round(255*n.green),u=Math.round(255*n.blue);function c(e){var t=e.toString(16);return 2!==t.length?"0"+t:t}return i=1===n.alpha?"#"+c(a)+c(s)+c(u):"#"+c(a)+c(s)+c(u)+c(Math.round(255*n.alpha)),o.push({label:i,textEdit:cr.replace(r,JSON.stringify(i))}),o},e}();function So(e,t){return Gn.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}var ko=_i(),xo={schemaAssociations:[],schemas:{"http://json-schema.org/schema#":{$ref:"http://json-schema.org/draft-07/schema#"},"http://json-schema.org/draft-04/schema#":{title:ko("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{title:ko("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},Ao={id:ko("schema.json.id","A unique identifier for the schema."),$schema:ko("schema.json.$schema","The schema to verify this document against."),title:ko("schema.json.title","A descriptive title of the element."),description:ko("schema.json.description","A long description of the element. Used in hover menus and suggestions."),default:ko("schema.json.default","A default value. Used by suggestions."),multipleOf:ko("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:ko("schema.json.maximum","The maximum numerical value, inclusive by default."),exclusiveMaximum:ko("schema.json.exclusiveMaximum","Makes the maximum property exclusive."),minimum:ko("schema.json.minimum","The minimum numerical value, inclusive by default."),exclusiveMinimum:ko("schema.json.exclusiveMininum","Makes the minimum property exclusive."),maxLength:ko("schema.json.maxLength","The maximum length of a string."),minLength:ko("schema.json.minLength","The minimum length of a string."),pattern:ko("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored."),additionalItems:ko("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:ko("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:ko("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive."),minItems:ko("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive."),uniqueItems:ko("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false."),maxProperties:ko("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive."),minProperties:ko("schema.json.minProperties","The minimum number of properties an object can have. Inclusive."),required:ko("schema.json.required","An array of strings that lists the names of all properties required on this object."),additionalProperties:ko("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:ko("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:ko("schema.json.properties","A map of property names to schemas for each property."),patternProperties:ko("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties."),dependencies:ko("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:ko("schema.json.enum","The set of literal values that are valid."),type:ko("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:ko("schema.json.format","Describes the format expected for the value."),allOf:ko("schema.json.allOf","An array of schemas, all of which must match."),anyOf:ko("schema.json.anyOf","An array of schemas, where at least one must match."),oneOf:ko("schema.json.oneOf","An array of schemas, exactly one of which must match."),not:ko("schema.json.not","A schema which must not match."),$id:ko("schema.json.$id","A unique identifier for the schema."),$ref:ko("schema.json.$ref","Reference a definition hosted on any location."),$comment:ko("schema.json.$comment","Comments from schema authors to readers or maintainers of the schema."),readOnly:ko("schema.json.readOnly","Indicates that the value of the instance is managed exclusively by the owning authority."),examples:ko("schema.json.examples","Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:ko("schema.json.contains",'An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:ko("schema.json.propertyNames","If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:ko("schema.json.const","An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:ko("schema.json.contentMediaType","Describes the media type of a string property."),contentEncoding:ko("schema.json.contentEncoding","Describes the content encoding of a string property."),if:ko("schema.json.if",'The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:ko("schema.json.then",'The "if" subschema is used for validation when the "if" subschema succeeds.'),else:ko("schema.json.else",'The "else" subschema is used for validation when the "if" subschema fails.')};for(var Eo in xo.schemas){var wo=xo.schemas[Eo];for(var No in wo.properties){var Lo=wo.properties[No];"boolean"===typeof Lo&&(Lo=wo.properties[No]={});var To=Ao[No];To?Lo.description=To:console.log(No+": localize('schema.json."+No+'\', "")')}}function Oo(e,t){var n=[],r=[],i=[],o=-1,a=yr(e.getText(),!1),s=a.scan();function u(e){n.push(e),r.push(i.length)}for(;17!==s;){switch(s){case 1:case 3:var c={startLine:h=e.positionAt(a.getTokenOffset()).line,endLine:h,kind:1===s?"object":"array"};i.push(c);break;case 2:case 4:var l=2===s?"object":"array";if(i.length>0&&i[i.length-1].kind===l){c=i.pop();var f=e.positionAt(a.getTokenOffset()).line;c&&f>c.startLine+1&&o!==c.startLine&&(c.endLine=f-1,u(c),o=c.startLine)}break;case 13:var h=e.positionAt(a.getTokenOffset()).line,d=e.positionAt(a.getTokenOffset()+a.getTokenLength()).line;1===a.getTokenError()&&h+1<e.lineCount?a.setPosition(e.offsetAt(zn.create(h+1,0))):h<d&&(u({startLine:h,endLine:d,kind:tr.Comment}),o=h);break;case 12:var m=e.getText().substr(a.getTokenOffset(),a.getTokenLength()).match(/^\/\/\s*#(region\b)|(endregion\b)/);if(m){f=e.positionAt(a.getTokenOffset()).line;if(m[1]){c={startLine:f,endLine:f,kind:tr.Region};i.push(c)}else{for(var g=i.length-1;g>=0&&i[g].kind!==tr.Region;)g--;if(g>=0){c=i[g];i.length=g,f>c.startLine&&o!==c.startLine&&(c.endLine=f,u(c),o=c.startLine)}}}}s=a.scan()}var p=t&&t.rangeLimit;if("number"!==typeof p||n.length<=p)return n;t&&t.onRangeLimitExceeded&&t.onRangeLimitExceeded(e.uri);for(var v=[],y=0,b=r;y<b.length;y++){(x=b[y])<30&&(v[x]=(v[x]||0)+1)}var _=0,C=0;for(g=0;g<v.length;g++){var S=v[g];if(S){if(S+_>p){C=g;break}_+=S}}var k=[];for(g=0;g<n.length;g++){var x;"number"===typeof(x=r[g])&&(x<C||x===C&&_++<p)&&k.push(n[g])}return k}function Io(e,t,n){function r(t,n){return Gn.create(e.positionAt(t),e.positionAt(n))}var i=yr(e.getText(),!0);function o(e,t){return i.setPosition(e),i.scan()===t?i.getTokenOffset()+i.getTokenLength():-1}return t.map((function(t){for(var i=e.offsetAt(t),a=n.getNodeFromOffset(i,!0),s=[];a;){switch(a.type){case"string":case"object":case"array":var u=a.offset+1,c=a.offset+a.length-1;u<c&&i>=u&&i<=c&&s.push(r(u,c)),s.push(r(a.offset,a.offset+a.length));break;case"number":case"boolean":case"null":case"property":s.push(r(a.offset,a.offset+a.length))}if("property"===a.type||a.parent&&"array"===a.parent.type){var l=o(a.offset+a.length,5);-1!==l&&s.push(r(a.offset,l))}a=a.parent}for(var f=void 0,h=s.length-1;h>=0;h--)f=oi.create(s[h],f);return f||(f=oi.create(Gn.create(t,t))),f}))}function Po(e,t){var n=[];return t.visit((function(r){var i;if("property"===r.type&&"$ref"===r.keyNode.value&&"string"===(null===(i=r.valueNode)||void 0===i?void 0:i.type)){var o=r.valueNode.value,a=function(e,t){var n=function(e){if("#"===e)return[];if("#"!==e[0]||"/"!==e[1])return null;return e.substring(2).split(/\//).map(Do)}(t);if(!n)return null;return Ro(n,e.root)}(t,o);if(a){var s=e.positionAt(a.offset);n.push({target:e.uri+"#"+(s.line+1)+","+(s.character+1),range:Mo(e,r.valueNode)})}}return!0})),Promise.resolve(n)}function Mo(e,t){return Gn.create(e.positionAt(t.offset+1),e.positionAt(t.offset+t.length-1))}function Ro(e,t){if(!t)return null;if(0===e.length)return t;var n=e.shift();if(t&&"object"===t.type){var r=t.properties.find((function(e){return e.keyNode.value===n}));return r?Ro(e,r.valueNode):null}if(t&&"array"===t.type&&n.match(/^(0|[1-9][0-9]*)$/)){var i=Number.parseInt(n),o=t.items[i];return o?Ro(e,o):null}return null}function Do(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function jo(e){var t=e.promiseConstructor||Promise,n=new ro(e.schemaRequestService,e.workspaceContext,t);n.setSchemaContributions(xo);var r=new Hi(n,e.contributions,t,e.clientCapabilities),i=new Yi(n,e.contributions,t),o=new Co(n),a=new uo(n,t);return{configure:function(e){n.clearExternalSchemas(),e.schemas&&e.schemas.forEach((function(e){n.registerExternalSchema(e.uri,e.fileMatch,e.schema)})),a.configure(e)},resetSchema:function(e){return n.onResourceChange(e)},doValidation:a.doValidation.bind(a),parseJSONDocument:function(e){return qi(e,{collectComments:!0})},newJSONDocument:function(e,t){return function(e,t){return void 0===t&&(t=[]),new Vi(e,t,[])}(e,t)},getMatchingSchemas:n.getMatchingSchemas.bind(n),doResolve:r.doResolve.bind(r),doComplete:r.doComplete.bind(r),findDocumentSymbols:o.findDocumentSymbols.bind(o),findDocumentSymbols2:o.findDocumentSymbols2.bind(o),findDocumentColors:o.findDocumentColors.bind(o),getColorPresentations:o.getColorPresentations.bind(o),doHover:i.doHover.bind(i),getFoldingRanges:Oo,getSelectionRanges:Io,findDefinition:function(){return Promise.resolve([])},findLinks:Po,format:function(e,t,n){var r=void 0;if(t){var i=e.offsetAt(t.start);r={offset:i,length:e.offsetAt(t.end)-i}}var o={tabSize:n?n.tabSize:4,insertSpaces:!0===(null===n||void 0===n?void 0:n.insertSpaces),insertFinalNewline:!0===(null===n||void 0===n?void 0:n.insertFinalNewline),eol:"\n"};return function(e,t,n){return qn(e,t,n)}(e.getText(),r,o).map((function(t){return cr.replace(Gn.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length)),t.content)}))}}}var Fo,Uo=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{u(r.next(e))}catch($o){o($o)}}function s(e){try{u(r.throw(e))}catch($o){o($o)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}u((r=r.apply(e,t||[])).next())}))},Vo=function(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch($o){o=[6,$o],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}};"undefined"!==typeof fetch&&(Fo=function(e){return fetch(e).then((function(e){return e.text()}))});var Wo=function(){function e(e,t){this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=jo({workspaceContext:{resolveRelativePath:function(e,t){return function(e,t){if(function(e){return e.charCodeAt(0)===qo}(t)){var n=Ji.parse(e),r=t.split("/");return n.with({path:Ko(r)}).toString()}return function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=Ji.parse(e),i=r.path.split("/"),o=0,a=t;o<a.length;o++){var s=a[o];i.push.apply(i,s.split("/"))}return r.with({path:Ko(i)}).toString()}(e,t)}(t.substr(0,t.lastIndexOf("/")+1),e)}},schemaRequestService:t.enableSchemaRequest&&Fo}),this._languageService.configure(this._languageSettings)}return e.prototype.doValidation=function(e){return Uo(this,void 0,void 0,(function(){var t,n;return Vo(this,(function(r){return(t=this._getTextDocument(e))?(n=this._languageService.parseJSONDocument(t),[2,this._languageService.doValidation(t,n,this._languageSettings)]):[2,Promise.resolve([])]}))}))},e.prototype.doComplete=function(e,t){return Uo(this,void 0,void 0,(function(){var n,r;return Vo(this,(function(i){return n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n),[2,this._languageService.doComplete(n,t,r)]}))}))},e.prototype.doResolve=function(e){return Uo(this,void 0,void 0,(function(){return Vo(this,(function(t){return[2,this._languageService.doResolve(e)]}))}))},e.prototype.doHover=function(e,t){return Uo(this,void 0,void 0,(function(){var n,r;return Vo(this,(function(i){return n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n),[2,this._languageService.doHover(n,t,r)]}))}))},e.prototype.format=function(e,t,n){return Uo(this,void 0,void 0,(function(){var r,i;return Vo(this,(function(o){return r=this._getTextDocument(e),i=this._languageService.format(r,t,n),[2,Promise.resolve(i)]}))}))},e.prototype.resetSchema=function(e){return Uo(this,void 0,void 0,(function(){return Vo(this,(function(t){return[2,Promise.resolve(this._languageService.resetSchema(e))]}))}))},e.prototype.findDocumentSymbols=function(e){return Uo(this,void 0,void 0,(function(){var t,n,r;return Vo(this,(function(i){return t=this._getTextDocument(e),n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentSymbols(t,n),[2,Promise.resolve(r)]}))}))},e.prototype.findDocumentColors=function(e){return Uo(this,void 0,void 0,(function(){var t,n,r;return Vo(this,(function(i){return t=this._getTextDocument(e),n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentColors(t,n),[2,Promise.resolve(r)]}))}))},e.prototype.getColorPresentations=function(e,t,n){return Uo(this,void 0,void 0,(function(){var r,i,o;return Vo(this,(function(a){return r=this._getTextDocument(e),i=this._languageService.parseJSONDocument(r),o=this._languageService.getColorPresentations(r,i,t,n),[2,Promise.resolve(o)]}))}))},e.prototype.getFoldingRanges=function(e,t){return Uo(this,void 0,void 0,(function(){var n,r;return Vo(this,(function(i){return n=this._getTextDocument(e),r=this._languageService.getFoldingRanges(n,t),[2,Promise.resolve(r)]}))}))},e.prototype.getSelectionRanges=function(e,t){return Uo(this,void 0,void 0,(function(){var n,r,i;return Vo(this,(function(o){return n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n),i=this._languageService.getSelectionRanges(n,t,r),[2,Promise.resolve(i)]}))}))},e.prototype._getTextDocument=function(e){for(var t=0,n=this._ctx.getMirrorModels();t<n.length;t++){var r=n[t];if(r.uri.toString()===e)return fi.create(e,this._languageId,r.version,r.getValue())}return null},e}(),qo="/".charCodeAt(0),Bo=".".charCodeAt(0);function Ko(e){for(var t=[],n=0,r=e;n<r.length;n++){var i=r[n];0===i.length||1===i.length&&i.charCodeAt(0)===Bo||(2===i.length&&i.charCodeAt(0)===Bo&&i.charCodeAt(1)===Bo?t.pop():t.push(i))}e.length>1&&0===e[e.length-1].length&&t.push("");var o=t.join("/");return 0===e[0].length&&(o="/"+o),o}self.onmessage=function(){jn((function(e,t){return new Wo(e,t)}))}}();