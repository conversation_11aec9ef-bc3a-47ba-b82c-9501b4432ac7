import React, { useState } from 'react';
// import { Icon } from '@fluentui/react';

interface Props {
  data: any;
}

const ObjectType: React.FC<Props> = () => {
  // const [expanded, setExpanded] = useState(true);

  return (
    <table>
      <thead>
        <tr>
          <td></td>
          <th></th>
          <th></th>
        </tr>
      </thead>
    </table>
  );
};

export default ObjectType;
