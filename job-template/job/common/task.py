import os
import requests

clearml_api_host = os.environ.get("CLEARML_API_HOST", "")

def create_project(project_name:str)->str:
    try:
        project_payload = {"name": project_name}
        get_all_uri = f"{clearml_api_host}/projects.get_all"
        resp = requests.post(get_all_uri, json = project_payload).json()
        if resp.get("data").get("projects"):
            return resp.get("data").get("projects")[0].get("id")
        
        create_uri = f"{clearml_api_host}/projects.create"
        resp = requests.post(create_uri, json = project_payload).json()
        return resp.get("data").get("id")
    except Exception as ex:
        print(ex)
        return None

def create_task(project_id:str, task_name:str, cmd:str, params:dict, algo_id:str, algo_version:str, dataset_id:str, dataset_version:str, image:str):
    try:
        task_payload = {
            "project": project_id,
            "name": task_name,
            "cmd": cmd,
            "type":"training",
            "hyperparams":{
            "Args":{"cmd":{"section": "Args","name": "cmd","value":cmd}},
            "Params":params,
            "Mounts":{
                "algo_id":{"section": "Mounts","name":"algo_id", "value":algo_id},
                "algo_version":{"section": "Mounts","name":"algo_version", "value":algo_version},
                "dataset_id":{"section": "Mounts","name":"dataset_id", "value":dataset_id},
                "dataset_version":{"section": "Mounts","name":"dataset_version", "value":dataset_version},
                }
        },
        "container":{
            "image":image,
        }
        }
        create_uri = f"{clearml_api_host}/tasks.create"
        create_resp = requests.post(create_uri, json=task_payload).json()
        print(f'create task -------------- {create_resp.get("data")}')
        return create_resp.get("data").get("id")
    except Exception as ex:
        print(ex)
        return None
    return 

def start_task(task_id:str):
    
    enqueue_task_payload = {
        "task": task_id,
        "queue_name" : os.environ.get("CLEARML_QUEUE", "default")
    }

    try:
        uri = f"{clearml_api_host}/tasks.enqueue"
        resp = requests.post(uri, json = enqueue_task_payload).json()
        print(resp)
    except Exception as ex:
        print(ex)
        return None