apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: event-exporter   # 这里必须是k8s-app  不然prometheus采集不了该资源
  name: event-exporter
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 60s
    port: http    # 端口号只能是字符串, 在pod中定义的端口name
  jobLabel: k8s-app
  namespaceSelector:
    matchNames:
    - monitoring
  selector:
    matchLabels:
      k8s-app: event-exporter   # 匹配service
