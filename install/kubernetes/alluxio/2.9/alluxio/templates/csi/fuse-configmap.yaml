#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ if .Values.csi.enabled -}}
{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}

---
kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ $fullName }}-csi-fuse-config
  labels:
    name: {{ $fullName }}-csi-fuse-config
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  alluxio-csi-fuse-config: |
    kind: Pod
    apiVersion: v1
    metadata:
      name: {{ $fullName }}-fuse
      labels:
        name: {{ $fullName }}-fuse
        app: {{ $name }}
        role: alluxio-fuse
    spec:
      nodeName:
      hostNetwork: {{ .Values.fuse.hostNetwork }}
      hostPID: {{ .Values.fuse.hostID }}
      dnsPolicy: {{ .Values.fuse.dnsPolicy }}
      securityContext:
        runAsUser: 0
        runAsGroup: 0
        fsGroup: 0
      containers:
        - name: alluxio-fuse
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          {{- if .Values.fuse.resources }}
          resources:
            {{- if .Values.fuse.resources.limits }}
            limits:
              cpu: {{ .Values.fuse.resources.limits.cpu }}
              memory: {{ .Values.fuse.resources.limits.memory }}
            {{- end }}
            {{- if .Values.fuse.resources.requests }}
              cpu: {{ .Values.fuse.resources.requests.cpu }}
              memory: {{ .Values.fuse.resources.requests.memory }}
            {{- end }}
          {{- end }}
          command: [ "/entrypoint.sh" ]
          args:
            - fuse
          env:
          {{- range $key, $value := .Values.fuse.env }}
          - name: "{{ $key }}"
            value: "{{ $value }}"
          {{- end }}
          securityContext:
            privileged: true
            capabilities:
              add:
                # SYS_ADMIN is needed for run `mount` command in the container
                - SYS_ADMIN
          envFrom:
          - configMapRef:
              name: {{ $fullName }}-config
          volumeMounts:
            - name: pods-mount-dir
              mountPath: /var/lib/kubelet
              mountPropagation: "Bidirectional"
      restartPolicy: Always
      volumes:
        - name: pods-mount-dir
          hostPath:
            path: /var/lib/kubelet
            type: Directory
{{- end }}
