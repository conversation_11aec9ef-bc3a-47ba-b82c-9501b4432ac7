# 直接获取到参数组创建训练

import argparse
import sys
import os
import json
import time
from common import info
from common import algo
from common import task


def create_tasks(hyperParams, project_id, image,  algo_id, algo_version, dataset_id, dataset_version):

    index = 0
    for comb in hyperParams.get("comboData", []):
        index += 1
        params_dict = {}
        for k,v in comb.items():
            print(f"param  k {k}, v {v}")
            if k  == "train_cmd":
                cmd = v
            else:
                params_dict.update({k:{"section":"Params","name":k, "value":str(v)}})
        
        # cmd = hyperParams.get("configData", "python keras_simple.py")
        # hyperParams.get("configData",{}).get()
        # TODO: 修改此处的cmd获取逻辑
        cmd = "python code/train.py"

        print(f"dataset_id {dataset_id}, dataset_version {dataset_version}")
        task_id = task.create_task(project_id=project_id, 
                            task_name=f'{project_id}-{index}', 
                            cmd=cmd, 
                            params=params_dict,
                            algo_id=algo_id, 
                            algo_version=algo_version, 
                            dataset_id=dataset_id,
                            dataset_version=dataset_version, 
                            image=image)
        task.start_task(task_id)
    return


# 等待所有task执行完成
def wait_for_finished():

    project_name = f'project-{os.environ.get("KFJ_RUN_ID")}'
    status = task.get_project_status(project_name)
    while status and status != "finished":
        time.sleep(20)
        print(f"project {project_name} status: {status}")
        status = task.get_project_status(project_name)
    return

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="算法训练配置")
    parser.add_argument('--algo_id', type=str, required=True, help="算法ID")
    parser.add_argument('--algo_version', type=str, required=True, help="算法版本")
    parser.add_argument('--hyperParams', type=str, required=True, help="超参")
    args = parser.parse_args()


    # 1、拿到算法参数、运行命令
    algo_version = algo.get_algo_version_detail(algo_id=args.algo_id, algo_version=args.algo_version)
    if not algo_version:
        print(f'Error: algo {args.algo_id} version{args.algo_version} not found...')

        
    image = "hub.tiduyun.com:5000/clearml/executor:1.4.1-keras"
    if  algo_version.get("exec_config",{}).get("image") and algo_version.get("exec_config",{}).get("tag"):
        image = algo_version.get("exec_config",{}).get("image") + ":" +algo_version.get("exec_config",{}).get("version")
        # image = algo_version.get("exec_config", "")

    cmd = algo_version.get("args",{}).get("input_train",{}).get("train_cmd")
    original_params = algo_version.get("args",{}).get("input_train",{})
    hyperParams = json.loads(args.hyperParams)
    if not hyperParams.get("comboData",{}):
        print(f'Error: comboData not found')

    for param in original_params:
        print("param")
    
    # 2、批量创建训练任务
    project_name = f'project-{os.environ.get("KFJ_RUN_ID")}'
    project_id = task.create_project(project_name)

    dataset_id = info.get_from_json("dataset.id")
    dataset_version = info.get_from_json("dataset.version")

    create_tasks(hyperParams, project_id, image, args.algo_id, args.algo_version, dataset_id, dataset_version)
    wait_for_finished()

    info.set_json_value("algo.id",  args.algo_id)
    info.set_json_value("algo.version", args.algo_version)