# cat sample-app-hpa.yaml
kind: HorizontalPodAutoscaler
apiVersion: autoscaling/v2beta1
metadata:
  name: tfserving-live-deepfm-low-202112230946
  namespace: kfservice
spec:
  scaleTargetRef:
    # point the HPA at the sample application
    # you created above
    apiVersion: apps/v1
    kind: Deployment
    name: resnet50-tf-tfserving
  # autoscale between 1 and 10 replicas
  minReplicas: 1
  maxReplicas: 5
  metrics:
  # use a "Pods" metric, which takes the average of the
  # given metric across all pods controlled by the autoscaling target
  - type: Pods
    pods:
      # use the metric that you used above: pods/http_requests
      metricName: container_gpu_usage
      targetAverageValue: 0.5 # 就是所有pod 该指标的平均值大于这个就扩一个出来



