# docker build -t ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard:2025.03.01 -f install/docker/Dockerfile .

FROM ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard:base-python3.9-20250101

RUN wget https://cube-studio.oss-cn-hangzhou.aliyuncs.com/aihub/deeplearning/aihub.zip && mkdir -p /cube-studio/aihub && unzip aihub.zip -d /cube-studio/aihub/ && rm aihub.zip

COPY install/docker/docker-add-file/configuration.py /usr/local/lib/python3.9/dist-packages/kubernetes/client/configuration.py

COPY myapp /home/<USER>/myapp
COPY myapp/static/appbuilder/frontend /data/web/frontend
COPY aihub /cube-studio/aihub
ENV PATH=/home/<USER>/myapp/bin:$PATH
ENV PYTHONPATH=/home/<USER>

COPY install/docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /home/<USER>/myapp/bin/myapp /entrypoint.sh

# ENTRYPOINT ["bash","/entrypoint.sh"]

# HEALTHCHECK CMD ["curl", "-f", "http://localhost:80/health"]





