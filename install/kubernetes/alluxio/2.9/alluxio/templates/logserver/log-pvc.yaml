#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ $enabled := .Values.logserver.enabled -}}
{{ $needPVC := eq .Values.logserver.volumeType "persistentVolumeClaim" -}}
{{ if and .Values.logserver.enabled $needPVC -}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.logserver.pvcName }}
  labels:
    app: {{ template "alluxio.name" . }}
    chart: {{ template "alluxio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-logserver
spec:
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{ .Values.logserver.size }}
  storageClassName: {{ .Values.logserver.storageClass }}
  accessModes:
{{ toYaml .Values.logserver.accessModes | trim | indent 4 }}
  {{- with .Values.logserver.selector }}
  selector:
{{ toYaml . | nindent 4 }}
  {{- end }}
{{- end -}}
