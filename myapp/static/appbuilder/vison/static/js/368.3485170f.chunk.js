"use strict";(self.webpackChunkvite_ml_platform=self.webpackChunkvite_ml_platform||[]).push([[368],{84688:function(e,t,n){n.d(t,{t:function(){return r}});var o=n(65141);function r(e,t,n){void 0===n&&(n=!0);var r=!1;if(e&&t)if(n)if(e===t)r=!0;else for(r=!1;t;){var i=(0,o.G)(t);if(i===e){r=!0;break}t=i}else e.contains&&(r=e.contains(t));return r}},30810:function(e,t,n){n.d(t,{X:function(){return r}});var o=n(65141);function r(e,t){return e&&e!==document.body?t(e)?e:r((0,o.G)(e),t):null}},65141:function(e,t,n){function o(e,t){return void 0===t&&(t=!0),e&&(t&&function(e){var t,n;return e&&(n=e)&&n._virtual&&(t=e._virtual.parent),t}(e)||e.parentNode&&e.parentNode)}n.d(t,{G:function(){return o}})},1509:function(e,t,n){n.d(t,{w:function(){return i}});var o=n(30810),r=n(96758);function i(e,t){var n=(0,o.X)(e,(function(e){return t===e||e.hasAttribute(r.Y)}));return null!==n&&n.hasAttribute(r.Y)}},96758:function(e,t,n){n.d(t,{U:function(){return r},Y:function(){return o}});var o="data-portal-element";function r(e){e.setAttribute(o,"true")}},81080:function(e,t,n){n.d(t,{b:function(){return o}});var o={topLeftEdge:0,topCenter:1,topRightEdge:2,topAutoEdge:3,bottomLeftEdge:4,bottomCenter:5,bottomRightEdge:6,bottomAutoEdge:7,leftTopEdge:8,leftCenter:9,leftBottomEdge:10,rightTopEdge:11,rightCenter:12,rightBottomEdge:13}},54794:function(e,t,n){n.d(t,{K:function(){return p}});var o=n(75971),r=n(72791),i=n(62020),a=n(50907),s=n(91605),l=n(40528),u=n(42792),c=n(63997),d=(0,u.NF)((function(e,t){var n,o,r,i=(0,c.W)(e),a={root:{padding:"0 4px",height:"40px",color:e.palette.neutralPrimary,backgroundColor:"transparent",border:"1px solid transparent",selectors:(n={},n[l.qJ]={borderColor:"Window"},n)},rootHovered:{color:e.palette.themePrimary,selectors:(o={},o[l.qJ]={color:"Highlight"},o)},iconHovered:{color:e.palette.themePrimary},rootPressed:{color:e.palette.black},rootExpanded:{color:e.palette.themePrimary},iconPressed:{color:e.palette.themeDarker},rootDisabled:{color:e.palette.neutralTertiary,backgroundColor:"transparent",borderColor:"transparent",selectors:(r={},r[l.qJ]={color:"GrayText"},r)},rootChecked:{color:e.palette.black},iconChecked:{color:e.palette.themeDarker},flexContainer:{justifyContent:"flex-start"},icon:{color:e.palette.themeDarkAlt},iconDisabled:{color:"inherit"},menuIcon:{color:e.palette.neutralSecondary},textContainer:{flexGrow:0}};return(0,l.E$)(i,a,t)})),p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.styles,n=e.theme;return r.createElement(i.Y,(0,o.pi)({},this.props,{variantClassName:"ms-Button--action ms-Button--command",styles:d(n,t),onRenderDescription:a.S}))},t=(0,o.gn)([(0,s.a)("ActionButton",["theme","styles"],!0)],t)}(r.Component)},71885:function(e,t,n){n.d(t,{f:function(){return a},n:function(){return i}});var o=n(42792),r=n(40528),i={msButton:"ms-Button",msButtonHasMenu:"ms-Button--hasMenu",msButtonIcon:"ms-Button-icon",msButtonMenuIcon:"ms-Button-menuIcon",msButtonLabel:"ms-Button-label",msButtonDescription:"ms-Button-description",msButtonScreenReaderText:"ms-Button-screenReaderText",msButtonFlexContainer:"ms-Button-flexContainer",msButtonTextContainer:"ms-Button-textContainer"},a=(0,o.NF)((function(e,t,n,o,a,s,l,u,c,d,p){var m,f,h=(0,r.Cn)(i,e||{}),v=d&&!p;return(0,r.ZC)({root:[h.msButton,t.root,o,c&&["is-checked",t.rootChecked],v&&["is-expanded",t.rootExpanded,{selectors:(m={},m[":hover .".concat(h.msButtonIcon)]=t.iconExpandedHovered,m[":hover .".concat(h.msButtonMenuIcon)]=t.menuIconExpandedHovered||t.rootExpandedHovered,m[":hover"]=t.rootExpandedHovered,m)}],u&&[i.msButtonHasMenu,t.rootHasMenu],l&&["is-disabled",t.rootDisabled],!l&&!v&&!c&&{selectors:(f={":hover":t.rootHovered},f[":hover .".concat(h.msButtonLabel)]=t.labelHovered,f[":hover .".concat(h.msButtonIcon)]=t.iconHovered,f[":hover .".concat(h.msButtonDescription)]=t.descriptionHovered,f[":hover .".concat(h.msButtonMenuIcon)]=t.menuIconHovered,f[":focus"]=t.rootFocused,f[":active"]=t.rootPressed,f[":active .".concat(h.msButtonIcon)]=t.iconPressed,f[":active .".concat(h.msButtonDescription)]=t.descriptionPressed,f[":active .".concat(h.msButtonMenuIcon)]=t.menuIconPressed,f)},l&&c&&[t.rootCheckedDisabled],!l&&c&&{selectors:{":hover":t.rootCheckedHovered,":active":t.rootCheckedPressed}},n],flexContainer:[h.msButtonFlexContainer,t.flexContainer],textContainer:[h.msButtonTextContainer,t.textContainer],icon:[h.msButtonIcon,a,t.icon,v&&t.iconExpanded,c&&t.iconChecked,l&&t.iconDisabled],label:[h.msButtonLabel,t.label,c&&t.labelChecked,l&&t.labelDisabled],menuIcon:[h.msButtonMenuIcon,s,t.menuIcon,c&&t.menuIconChecked,l&&!p&&t.menuIconDisabled,!l&&!v&&!c&&{selectors:{":hover":t.menuIconHovered,":active":t.menuIconPressed}},v&&["is-expanded",t.menuIconExpanded]],description:[h.msButtonDescription,t.description,c&&t.descriptionChecked,l&&t.descriptionDisabled],screenReaderText:[h.msButtonScreenReaderText,t.screenReaderText]})}))},62020:function(e,t,n){n.d(t,{Y:function(){return nt}});var o,r=n(75971),i=n(72791),a=n(6475),s=n(42792),l=n(29723),u=n(1509),c=n(9691),d=n(61910),p=n(8877),m=n(68959),f=n(92853),h=n(18597),v=n(23983),g=n(91106),b=n(15863),y=n(35726),_=n(50907),C=n(96971),k=n(29109),x=n(18511),E=n(90511),I=n(89085),w=n(93474),T=function(e){var t=e.className,n=e.imageProps,o=(0,b.pq)(e,b.iY,["aria-label","aria-labelledby","title","aria-describedby"]),a=n.alt||e["aria-label"],s=a||e["aria-labelledby"]||e.title||n["aria-label"]||n["aria-labelledby"]||n.title,u={"aria-labelledby":e["aria-labelledby"],"aria-describedby":e["aria-describedby"],title:e.title},c=s?{}:{"aria-hidden":!0};return i.createElement("div",(0,r.pi)({},c,o,{className:(0,l.i)(w.Sk,w.AK.root,w.AK.image,t)}),i.createElement(I.E,(0,r.pi)({},u,n,{alt:s?a:""})))},M=n(81080),D=n(40433),S=n(45651);!function(e){e[e.Normal=0]="Normal",e[e.Divider=1]="Divider",e[e.Header=2]="Header",e[e.Section=3]="Section"}(o||(o={}));var P=n(78498),N=n(65260),R=n(79292),B=n(87376),F=n(16455),O=n(56500),L=n(90101),A=n(86749),H=n(97471),z=n(20777);function W(e){return e.canCheck?!(!e.isChecked&&!e.checked):"boolean"===typeof e.isChecked?e.isChecked:"boolean"===typeof e.checked?e.checked:null}function K(e){return!(!e.subMenuProps&&!e.items)}function q(e){return!(!e.isDisabled&&!e.disabled)}function U(e){return null!==W(e)?"menuitemcheckbox":"menuitem"}var G=n(87023),J=function(e){var t=e.item,n=e.classNames,o=t.iconProps;return i.createElement(x.J,(0,r.pi)({},o,{className:n.icon}))},Z=function(e){var t=e.item;return e.hasIcons?t.onRenderIcon?t.onRenderIcon(e,J):J(e):null},j=function(e){var t=e.onCheckmarkClick,n=e.item,o=e.classNames,r=W(n);if(t){return i.createElement(x.J,{iconName:!1!==n.canCheck&&r?"CheckMark":"",className:o.checkmarkIcon,onClick:function(e){return t(n,e)}})}return null},Y=function(e){var t=e.item,n=e.classNames;return t.text||t.name?i.createElement("span",{className:n.label},t.text||t.name):null},V=function(e){var t=e.item,n=e.classNames;return t.secondaryText?i.createElement("span",{className:n.secondaryText},t.secondaryText):null},$=function(e){var t=e.item,n=e.classNames,o=e.theme;return K(t)?i.createElement(x.J,(0,r.pi)({iconName:(0,B.zg)(o)?"ChevronLeft":"ChevronRight"},t.submenuIconProps,{className:n.subMenuIcon})):null},Q=function(e){function t(t){var n=e.call(this,t)||this;return n.openSubMenu=function(){var e=n.props,t=e.item,o=e.openSubMenu,r=e.getSubmenuTarget;if(r){var i=r();K(t)&&o&&i&&o(t,i)}},n.dismissSubMenu=function(){var e=n.props,t=e.item,o=e.dismissSubMenu;K(t)&&o&&o()},n.dismissMenu=function(e){var t=n.props.dismissMenu;t&&t(void 0,e)},(0,p.l)(n),n}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.item,n=e.classNames,o=t.onRenderContent||this._renderLayout;return i.createElement("div",{className:t.split?n.linkContentMenu:n.linkContent},o(this.props,{renderCheckMarkIcon:j,renderItemIcon:Z,renderItemName:Y,renderSecondaryText:V,renderSubMenuIcon:$}))},t.prototype._renderLayout=function(e,t){return i.createElement(i.Fragment,null,t.renderCheckMarkIcon(e),t.renderItemIcon(e),t.renderItemName(e),t.renderSecondaryText(e),t.renderSubMenuIcon(e))},t}(i.Component),X=n(40528),ee=(0,s.NF)((function(e){return(0,X.ZC)({wrapper:{display:"inline-flex",height:"100%",alignItems:"center"},divider:{width:1,height:"100%",backgroundColor:e.palette.neutralTertiaryAlt}})})),te=36,ne=(0,X.sK)(0,X.yp),oe=(0,s.NF)((function(e){var t,n,o,i,a,s=e.semanticColors,l=e.fonts,u=e.palette,c=s.menuItemBackgroundHovered,d=s.menuItemTextHovered,p=s.menuItemBackgroundPressed,m=s.bodyDivider,f={item:[l.medium,{color:s.bodyText,position:"relative",boxSizing:"border-box"}],divider:{display:"block",height:"1px",backgroundColor:m,position:"relative"},root:[(0,X.GL)(e),l.medium,{color:s.bodyText,backgroundColor:"transparent",border:"none",width:"100%",height:te,lineHeight:te,display:"block",cursor:"pointer",padding:"0px 8px 0 4px",textAlign:"left"}],rootDisabled:{color:s.disabledBodyText,cursor:"default",pointerEvents:"none",selectors:(t={},t[X.qJ]={color:"GrayText",opacity:1},t)},rootHovered:{backgroundColor:c,color:d,selectors:{".ms-ContextualMenu-icon":{color:u.themeDarkAlt},".ms-ContextualMenu-submenuIcon":{color:u.neutralPrimary}}},rootFocused:{backgroundColor:u.white},rootChecked:{selectors:{".ms-ContextualMenu-checkmarkIcon":{color:u.neutralPrimary}}},rootPressed:{backgroundColor:p,selectors:{".ms-ContextualMenu-icon":{color:u.themeDark},".ms-ContextualMenu-submenuIcon":{color:u.neutralPrimary}}},rootExpanded:{backgroundColor:p,color:s.bodyTextChecked,selectors:(n={".ms-ContextualMenu-submenuIcon":(o={},o[X.qJ]={color:"inherit"},o)},n[X.qJ]=(0,r.pi)({},(0,X.xM)()),n)},linkContent:{whiteSpace:"nowrap",height:"inherit",display:"flex",alignItems:"center",maxWidth:"100%"},anchorLink:{padding:"0px 8px 0 4px",textRendering:"auto",color:"inherit",letterSpacing:"normal",wordSpacing:"normal",textTransform:"none",textIndent:"0px",textShadow:"none",textDecoration:"none",boxSizing:"border-box"},label:{margin:"0 4px",verticalAlign:"middle",display:"inline-block",flexGrow:"1",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"},secondaryText:{color:e.palette.neutralSecondary,paddingLeft:"20px",textAlign:"right"},icon:{display:"inline-block",minHeight:"1px",maxHeight:te,fontSize:X.ld.medium,width:X.ld.medium,margin:"0 4px",verticalAlign:"middle",flexShrink:"0",selectors:(i={},i[ne]={fontSize:X.ld.large,width:X.ld.large},i)},iconColor:{color:s.menuIcon},iconDisabled:{color:s.disabledBodyText},checkmarkIcon:{color:s.bodySubtext},subMenuIcon:{height:te,lineHeight:te,color:u.neutralSecondary,textAlign:"center",display:"inline-block",verticalAlign:"middle",flexShrink:"0",fontSize:X.ld.small,selectors:(a={":hover":{color:u.neutralPrimary},":active":{color:u.neutralPrimary}},a[ne]={fontSize:X.ld.medium},a)},splitButtonFlexContainer:[(0,X.GL)(e),{display:"flex",height:te,flexWrap:"nowrap",justifyContent:"center",alignItems:"flex-start"}]};return(0,X.E$)(f)})),re="28px",ie=(0,X.sK)(0,X.yp),ae=(0,s.NF)((function(e){var t;return(0,X.ZC)(ee(e),{wrapper:{position:"absolute",right:28,selectors:(t={},t[ie]={right:32},t)},divider:{height:16,width:1}})})),se={item:"ms-ContextualMenu-item",divider:"ms-ContextualMenu-divider",root:"ms-ContextualMenu-link",isChecked:"is-checked",isExpanded:"is-expanded",isDisabled:"is-disabled",linkContent:"ms-ContextualMenu-linkContent",linkContentMenu:"ms-ContextualMenu-linkContent",icon:"ms-ContextualMenu-icon",iconColor:"ms-ContextualMenu-iconColor",checkmarkIcon:"ms-ContextualMenu-checkmarkIcon",subMenuIcon:"ms-ContextualMenu-submenuIcon",label:"ms-ContextualMenu-itemText",secondaryText:"ms-ContextualMenu-secondaryText",splitMenu:"ms-ContextualMenu-splitMenu",screenReaderText:"ms-ContextualMenu-screenReaderText"},le=(0,s.NF)((function(e,t,n,o,r,i,a,s,l,u,c,p){var m,f,h,v,g=oe(e),b=(0,X.Cn)(se,e);return(0,X.ZC)({item:[b.item,g.item,a],divider:[b.divider,g.divider,s],root:[b.root,g.root,o&&[b.isChecked,g.rootChecked],r&&g.anchorLink,n&&[b.isExpanded,g.rootExpanded],t&&[b.isDisabled,g.rootDisabled],!t&&!n&&[{selectors:(m={":hover":g.rootHovered,":active":g.rootPressed},m[".".concat(d.G$," &:focus, .").concat(d.G$," &:focus:hover")]=g.rootFocused,m[".".concat(d.G$," &:hover")]={background:"inherit;"},m)}],p],splitPrimary:[g.root,{width:"calc(100% - ".concat(re,")")},o&&["is-checked",g.rootChecked],(t||c)&&["is-disabled",g.rootDisabled],!(t||c)&&!o&&[{selectors:(f={":hover":g.rootHovered},f[":hover ~ .".concat(b.splitMenu)]=g.rootHovered,f[":active"]=g.rootPressed,f[".".concat(d.G$," &:focus, .").concat(d.G$," &:focus:hover")]=g.rootFocused,f[".".concat(d.G$," &:hover")]={background:"inherit;"},f)}]],splitMenu:[b.splitMenu,g.root,{flexBasis:"0",padding:"0 8px",minWidth:re},n&&["is-expanded",g.rootExpanded],t&&["is-disabled",g.rootDisabled],!t&&!n&&[{selectors:(h={":hover":g.rootHovered,":active":g.rootPressed},h[".".concat(d.G$," &:focus, .").concat(d.G$," &:focus:hover")]=g.rootFocused,h[".".concat(d.G$," &:hover")]={background:"inherit;"},h)}]],anchorLink:g.anchorLink,linkContent:[b.linkContent,g.linkContent],linkContentMenu:[b.linkContentMenu,g.linkContent,{justifyContent:"center"}],icon:[b.icon,i&&g.iconColor,g.icon,l,t&&[b.isDisabled,g.iconDisabled]],iconColor:g.iconColor,checkmarkIcon:[b.checkmarkIcon,i&&g.checkmarkIcon,g.icon,l],subMenuIcon:[b.subMenuIcon,g.subMenuIcon,u,n&&{color:e.palette.neutralPrimary},t&&[g.iconDisabled]],label:[b.label,g.label],secondaryText:[b.secondaryText,g.secondaryText],splitContainer:[g.splitButtonFlexContainer,!t&&!o&&[{selectors:(v={},v[".".concat(d.G$," &:focus, .").concat(d.G$," &:focus:hover")]=g.rootFocused,v)}]],screenReaderText:[b.screenReaderText,g.screenReaderText,X.ul,{visibility:"hidden"}]})})),ue=function(e){var t=e.theme,n=e.disabled,o=e.expanded,r=e.checked,i=e.isAnchorLink,a=e.knownIcon,s=e.itemClassName,l=e.dividerClassName,u=e.iconClassName,c=e.subMenuClassName,d=e.primaryDisabled,p=e.className;return le(t,n,o,r,i,a,s,l,u,c,d,p)},ce=(0,D.z)(Q,ue,void 0,{scope:"ContextualMenuItem"}),de=function(e){function t(t){var n=e.call(this,t)||this;return n._onItemMouseEnter=function(e){var t=n.props,o=t.item,r=t.onItemMouseEnter;r&&r(o,e,e.currentTarget)},n._onItemClick=function(e){var t=n.props,o=t.item,r=t.onItemClickBase;r&&r(o,e,e.currentTarget)},n._onItemMouseLeave=function(e){var t=n.props,o=t.item,r=t.onItemMouseLeave;r&&r(o,e)},n._onItemKeyDown=function(e){var t=n.props,o=t.item,r=t.onItemKeyDown;r&&r(o,e)},n._onItemMouseMove=function(e){var t=n.props,o=t.item,r=t.onItemMouseMove;r&&r(o,e,e.currentTarget)},n._getSubmenuTarget=function(){},(0,p.l)(n),n}return(0,r.ZT)(t,e),t.prototype.shouldComponentUpdate=function(e){return!(0,y.Vv)(e,this.props)},t}(i.Component),pe=n(54911),me=n(32561),fe=n(1604),he=n(7211),ve=n(7796),ge=n(63039);function be(e){return e.reduce((function(e,t){return e+pe.by+t.split("").join(pe.by)}),pe.ww)}function ye(e){var t=i.useRef(),n=e.keytipProps?(0,r.pi)({disabled:e.disabled},e.keytipProps):void 0,o=(0,me.B)(ve.K.getInstance()),a=(0,fe.D)(e);(0,he.L)((function(){t.current&&n&&((null===a||void 0===a?void 0:a.keytipProps)!==e.keytipProps||(null===a||void 0===a?void 0:a.disabled)!==e.disabled)&&o.update(n,t.current)})),(0,he.L)((function(){return n&&(t.current=o.register(n)),function(){n&&o.unregister(n,t.current)}}),[]);var s={ariaDescribedBy:void 0,keytipId:void 0};return n&&(s=function(e,t,n){var o=e.addParentOverflow(t),i=(0,k.I)(n,function(e){var t=" "+pe.nK;return e.length?t+" "+be(e):t}(o.keySequences)),a=(0,r.ev)([],o.keySequences,!0);o.overflowSetSequence&&(a=function(e,t){var n=t.length,o=(0,r.ev)([],t,!0).pop(),i=(0,r.ev)([],e,!0);return(0,ge.OA)(i,n-1,o)}(a,o.overflowSetSequence));var s=be(a);return{ariaDescribedBy:i,keytipId:s}}(o,n,e.ariaDescribedBy)),s}var _e=function(e){var t,n=e.children,o=ye((0,r._T)(e,["children"])),i=o.keytipId,a=o.ariaDescribedBy;return n(((t={})[pe.fV]=i,t[pe.ms]=i,t["aria-describedby"]=a,t))},Ce=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._anchor=i.createRef(),t._getMemoizedMenuButtonKeytipProps=(0,s.NF)((function(e){return(0,r.pi)((0,r.pi)({},e),{hasMenu:!0})})),t._getSubmenuTarget=function(){return t._anchor.current?t._anchor.current:void 0},t._onItemClick=function(e){var n=t.props,o=n.item,r=n.onItemClick;r&&r(o,e)},t._renderAriaDescription=function(e,n){return e?i.createElement("span",{id:t._ariaDescriptionId,className:n},e):null},t}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.item,o=t.classNames,a=t.index,s=t.focusableElementIndex,l=t.totalItemCount,u=t.hasCheckmarks,c=t.hasIcons,d=t.expandedMenuItemKey,p=t.onItemClick,m=t.openSubMenu,f=t.dismissSubMenu,h=t.dismissMenu,v=ce;this.props.item.contextualMenuItemAs&&(v=(0,z.Z)(this.props.item.contextualMenuItemAs,v)),this.props.contextualMenuItemAs&&(v=(0,z.Z)(this.props.contextualMenuItemAs,v));var y=n.rel;n.target&&"_blank"===n.target.toLowerCase()&&(y=y||"nofollow noopener noreferrer");var _=K(n),C=(0,b.pq)(n,b.h2),x=q(n),E=n.itemProps,I=n.ariaDescription,w=n.keytipProps;w&&_&&(w=this._getMemoizedMenuButtonKeytipProps(w)),I&&(this._ariaDescriptionId=(0,g.z)());var T=(0,k.I)(n.ariaDescribedBy,I?this._ariaDescriptionId:void 0,C["aria-describedby"]),M={"aria-describedby":T};return i.createElement("div",null,i.createElement(_e,{keytipProps:n.keytipProps,ariaDescribedBy:T,disabled:x},(function(t){return i.createElement("a",(0,r.pi)({},M,C,t,{ref:e._anchor,href:n.href,target:n.target,rel:y,className:o.root,role:"menuitem","aria-haspopup":_||void 0,"aria-expanded":_?n.key===d:void 0,"aria-posinset":s+1,"aria-setsize":l,"aria-disabled":q(n),style:n.style,onClick:e._onItemClick,onMouseEnter:e._onItemMouseEnter,onMouseLeave:e._onItemMouseLeave,onMouseMove:e._onItemMouseMove,onKeyDown:_?e._onItemKeyDown:void 0}),i.createElement(v,(0,r.pi)({componentRef:n.componentRef,item:n,classNames:o,index:a,onCheckmarkClick:u&&p?p:void 0,hasIcons:c,openSubMenu:m,dismissSubMenu:f,dismissMenu:h,getSubmenuTarget:e._getSubmenuTarget},E)),e._renderAriaDescription(I,o.screenReaderText))})))},t}(de),ke=(0,R.y)(),xe=i.forwardRef((function(e,t){var n=e.styles,o=e.theme,r=e.getClassNames,a=e.className,s=ke(n,{theme:o,getClassNames:r,className:a});return i.createElement("span",{className:s.wrapper,ref:t},i.createElement("span",{className:s.divider}))}));xe.displayName="VerticalDividerBase";var Ee=(0,D.z)(xe,(function(e){var t=e.theme,n=e.getClassNames,o=e.className;if(!t)throw new Error("Theme is undefined or null.");if(n){var r=n(t);return{wrapper:[r.wrapper],divider:[r.divider]}}return{wrapper:[{display:"inline-flex",height:"100%",alignItems:"center"},o],divider:[{width:1,height:"100%",backgroundColor:t.palette.neutralTertiaryAlt}]}}),void 0,{scope:"VerticalDivider"}),Ie=function(e){function t(t){var n=e.call(this,t)||this;return n._getMemoizedMenuButtonKeytipProps=(0,s.NF)((function(e){return(0,r.pi)((0,r.pi)({},e),{hasMenu:!0})})),n._onItemKeyDown=function(e){var t=n.props,o=t.item,r=t.onItemKeyDown;e.which===c.m.enter?(n._executeItemClick(e),e.preventDefault(),e.stopPropagation()):r&&r(o,e)},n._getSubmenuTarget=function(){return n._splitButton},n._renderAriaDescription=function(e,t){return e?i.createElement("span",{id:n._ariaDescriptionId,className:t},e):null},n._onItemMouseEnterPrimary=function(e){var t=n.props,o=t.item,i=t.onItemMouseEnter;i&&i((0,r.pi)((0,r.pi)({},o),{subMenuProps:void 0,items:void 0}),e,n._splitButton)},n._onItemMouseEnterIcon=function(e){var t=n.props,o=t.item,r=t.onItemMouseEnter;r&&r(o,e,n._splitButton)},n._onItemMouseMovePrimary=function(e){var t=n.props,o=t.item,i=t.onItemMouseMove;i&&i((0,r.pi)((0,r.pi)({},o),{subMenuProps:void 0,items:void 0}),e,n._splitButton)},n._onItemMouseMoveIcon=function(e){var t=n.props,o=t.item,r=t.onItemMouseMove;r&&r(o,e,n._splitButton)},n._onIconItemClick=function(e){var t=n.props,o=t.item,r=t.onItemClickBase;r&&r(o,e,n._splitButton?n._splitButton:e.currentTarget)},n._executeItemClick=function(e){var t=n.props,o=t.item,r=t.executeItemClick,i=t.onItemClick;if(!o.disabled&&!o.isDisabled)return n._processingTouch&&!o.canCheck&&i?i(o,e):void(r&&r(o,e))},n._onTouchStart=function(e){n._splitButton&&!("onpointerdown"in n._splitButton)&&n._handleTouchAndPointerEvent(e)},n._onPointerDown=function(e){"touch"===e.pointerType&&(n._handleTouchAndPointerEvent(e),e.preventDefault(),e.stopImmediatePropagation())},n._async=new m.e(n),n._events=new f.r(n),n._dismissLabelId=(0,g.z)(),n}return(0,r.ZT)(t,e),t.prototype.componentDidMount=function(){this._splitButton&&"onpointerdown"in this._splitButton&&this._events.on(this._splitButton,"pointerdown",this._onPointerDown,!0)},t.prototype.componentWillUnmount=function(){this._async.dispose(),this._events.dispose()},t.prototype.render=function(){var e,t=this,n=this.props,o=n.item,a=n.classNames,s=n.index,l=n.focusableElementIndex,u=n.totalItemCount,c=n.hasCheckmarks,d=n.hasIcons,p=n.onItemMouseLeave,m=n.expandedMenuItemKey,f=K(o),h=o.keytipProps;h&&(h=this._getMemoizedMenuButtonKeytipProps(h));var v=o.ariaDescription;v&&(this._ariaDescriptionId=(0,g.z)());var b=null!==(e=W(o))&&void 0!==e?e:void 0;return i.createElement(_e,{keytipProps:h,disabled:q(o)},(function(e){return i.createElement("div",{"data-ktp-target":e["data-ktp-target"],ref:function(e){return t._splitButton=e},role:U(o),"aria-label":o.ariaLabel,className:a.splitContainer,"aria-disabled":q(o),"aria-expanded":f?o.key===m:void 0,"aria-haspopup":!0,"aria-describedby":(0,k.I)(o.ariaDescribedBy,v?t._ariaDescriptionId:void 0,e["aria-describedby"]),"aria-checked":b,"aria-posinset":l+1,"aria-setsize":u,onMouseEnter:t._onItemMouseEnterPrimary,onMouseLeave:p?p.bind(t,(0,r.pi)((0,r.pi)({},o),{subMenuProps:null,items:null})):void 0,onMouseMove:t._onItemMouseMovePrimary,onKeyDown:t._onItemKeyDown,onClick:t._executeItemClick,onTouchStart:t._onTouchStart,tabIndex:0,"data-is-focusable":!0,"aria-roledescription":o["aria-roledescription"]},t._renderSplitPrimaryButton(o,a,s,c,d),t._renderSplitDivider(o),t._renderSplitIconButton(o,a,s,e),t._renderAriaDescription(v,a.screenReaderText))}))},t.prototype._renderSplitPrimaryButton=function(e,t,n,o,a){var s=this.props,l=s.contextualMenuItemAs,u=void 0===l?ce:l,c=s.onItemClick,d={key:e.key,disabled:q(e)||e.primaryDisabled,name:e.name,text:e.text||e.name,secondaryText:e.secondaryText,className:t.splitPrimary,canCheck:e.canCheck,isChecked:e.isChecked,checked:e.checked,iconProps:e.iconProps,id:this._dismissLabelId,onRenderIcon:e.onRenderIcon,data:e.data,"data-is-focusable":!1},p=e.itemProps;return i.createElement("button",(0,r.pi)({},(0,b.pq)(d,b.Yq)),i.createElement(u,(0,r.pi)({"data-is-focusable":!1,item:d,classNames:t,index:n,onCheckmarkClick:o&&c?c:void 0,hasIcons:a},p)))},t.prototype._renderSplitDivider=function(e){var t=e.getSplitButtonVerticalDividerClassNames||ae;return i.createElement(Ee,{getClassNames:t})},t.prototype._renderSplitIconButton=function(e,t,n,o){var a=this.props,s=a.onItemMouseLeave,l=a.onItemMouseDown,u=a.openSubMenu,c=a.dismissSubMenu,d=a.dismissMenu,p=ce;this.props.item.contextualMenuItemAs&&(p=(0,z.Z)(this.props.item.contextualMenuItemAs,p)),this.props.contextualMenuItemAs&&(p=(0,z.Z)(this.props.contextualMenuItemAs,p));var m={onClick:this._onIconItemClick,disabled:q(e),className:t.splitMenu,subMenuProps:e.subMenuProps,submenuIconProps:e.submenuIconProps,split:!0,key:e.key,"aria-labelledby":this._dismissLabelId},f=(0,r.pi)((0,r.pi)({},(0,b.pq)(m,b.Yq)),{onMouseEnter:this._onItemMouseEnterIcon,onMouseLeave:s?s.bind(this,e):void 0,onMouseDown:function(t){return l?l(e,t):void 0},onMouseMove:this._onItemMouseMoveIcon,"data-is-focusable":!1,"data-ktp-execute-target":o["data-ktp-execute-target"],"aria-haspopup":!0}),h=e.itemProps;return i.createElement("button",(0,r.pi)({},f),i.createElement(p,(0,r.pi)({componentRef:e.componentRef,item:m,classNames:t,index:n,hasIcons:!1,openSubMenu:u,dismissSubMenu:c,dismissMenu:d,getSubmenuTarget:this._getSubmenuTarget},h)))},t.prototype._handleTouchAndPointerEvent=function(e){var t=this,n=this.props.onTap;n&&n(e),this._lastTouchTimeoutId&&(this._async.clearTimeout(this._lastTouchTimeoutId),this._lastTouchTimeoutId=void 0),this._processingTouch=!0,this._lastTouchTimeoutId=this._async.setTimeout((function(){t._processingTouch=!1,t._lastTouchTimeoutId=void 0}),500)},t}(de),we=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._btn=i.createRef(),t._getMemoizedMenuButtonKeytipProps=(0,s.NF)((function(e){return(0,r.pi)((0,r.pi)({},e),{hasMenu:!0})})),t._renderAriaDescription=function(e,n){return e?i.createElement("span",{id:t._ariaDescriptionId,className:n},e):null},t._getSubmenuTarget=function(){return t._btn.current?t._btn.current:void 0},t}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.item,o=t.classNames,a=t.index,s=t.focusableElementIndex,l=t.totalItemCount,u=t.hasCheckmarks,c=t.hasIcons,d=t.contextualMenuItemAs,p=t.expandedMenuItemKey,m=t.onItemMouseDown,f=t.onItemClick,h=t.openSubMenu,v=t.dismissSubMenu,y=t.dismissMenu,_=ce;n.contextualMenuItemAs&&(_=(0,z.Z)(n.contextualMenuItemAs,_)),d&&(_=(0,z.Z)(d,_));var C=W(n),x=null!==C,E=U(n),I=K(n),w=n.itemProps,T=n.ariaLabel,M=n.ariaDescription,D=(0,b.pq)(n,b.Yq);delete D.disabled;var S=n.role||E;M&&(this._ariaDescriptionId=(0,g.z)());var P=(0,k.I)(n.ariaDescribedBy,M?this._ariaDescriptionId:void 0,D["aria-describedby"]),N={className:o.root,onClick:this._onItemClick,onKeyDown:I?this._onItemKeyDown:void 0,onMouseEnter:this._onItemMouseEnter,onMouseLeave:this._onItemMouseLeave,onMouseDown:function(e){return m?m(n,e):void 0},onMouseMove:this._onItemMouseMove,href:n.href,title:n.title,"aria-label":T,"aria-describedby":P,"aria-haspopup":I||void 0,"aria-expanded":I?n.key===p:void 0,"aria-posinset":s+1,"aria-setsize":l,"aria-disabled":q(n),"aria-checked":"menuitemcheckbox"!==S&&"menuitemradio"!==S||!x?void 0:!!C,"aria-selected":"menuitem"===S&&x?!!C:void 0,role:S,style:n.style},R=n.keytipProps;return R&&I&&(R=this._getMemoizedMenuButtonKeytipProps(R)),i.createElement(_e,{keytipProps:R,ariaDescribedBy:P,disabled:q(n)},(function(t){return i.createElement("button",(0,r.pi)({ref:e._btn},D,N,t),i.createElement(_,(0,r.pi)({componentRef:n.componentRef,item:n,classNames:o,index:a,onCheckmarkClick:u&&f?f:void 0,hasIcons:c,openSubMenu:h,dismissSubMenu:v,dismissMenu:y,getSubmenuTarget:e._getSubmenuTarget},w)),e._renderAriaDescription(M,o.screenReaderText))}))},t}(de),Te=n(24994),Me=n(32092);var De=n(42086),Se=n(98687),Pe=n(91381),Ne=i.createContext({}),Re=(0,R.y)(),Be=(0,R.y)(),Fe={items:[],shouldFocusOnMount:!0,gapSpace:0,directionalHint:M.b.bottomAutoEdge,beakWidth:16};function Oe(e){for(var t=0,n=0,r=e;n<r.length;n++){var i=r[n];if(i.itemType!==o.Divider&&i.itemType!==o.Header)t+=i.customOnRenderListLength?i.customOnRenderListLength:1}return t}function Le(e,t){var n=null===t||void 0===t?void 0:t.target,o=e.subMenuProps?e.subMenuProps.items:e.items;if(o){for(var i=[],a=0,s=o;a<s.length;a++){var l=s[a];if(l.preferMenuTargetAsEventTarget){var u=l.onClick,c=(0,r._T)(l,["onClick"]);i.push((0,r.pi)((0,r.pi)({},c),{onClick:Ze(u,n)}))}else i.push(l)}return i}}var Ae="ContextualMenu",He=(0,s.NF)((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){return X.l7.apply(void 0,(0,r.ev)([t,ue],e,!1))}}));function ze(e,t){var n=e.hidden,o=e.items,r=e.theme,a=e.className,s=e.id,l=e.target,u=i.useState(),c=u[0],d=u[1],p=i.useState(),m=p[0],f=p[1],h=i.useState(),v=h[0],g=h[1],b=(0,Te.M)(Ae,s),_=i.useCallback((function(){g(void 0),d(void 0),f(void 0)}),[]),C=i.useCallback((function(e,t,n){var o=e.key;c!==o&&(t.focus(),g(n),d(o),f(t))}),[c]);i.useEffect((function(){n&&_()}),[n,_]);var k=function(e,t){var n=i.useRef(!1);i.useEffect((function(){return n.current=!0,function(){n.current=!1}}),[]);var o=function(o,r){r?e(o,r):n.current&&t()};return o}(t,_);return[c,C,function(){var e=Je(c,o),t=null;if(e&&(t={items:Le(e,{target:l}),target:m,onDismiss:k,isSubMenu:!0,id:b,shouldFocusOnMount:!0,shouldFocusOnContainer:v,directionalHint:(0,B.zg)(r)?M.b.leftTopEdge:M.b.rightTopEdge,className:a,gapSpace:0,isBeakVisible:!1},e.subMenuProps&&(0,y.f0)(t,e.subMenuProps),e.preferMenuTargetAsEventTarget)){var n=e.onItemClick;t.onItemClick=Ze(n,l)}return t},k]}function We(e,t,n,o){var r=e.theme,a=e.isSubMenu,s=e.focusZoneProps,l=void 0===s?{}:s,u=l.checkForNoWrap,d=l.direction,p=void 0===d?P.U.vertical:d,m=i.useRef(),f=function(e,n,o){var r=!1;return n(e)&&(t(e,o),e.preventDefault(),e.stopPropagation(),r=!0),r},h=function(e){return e.which===c.m.escape||function(e){var t=(0,B.zg)(r)?c.m.right:c.m.left;return!(e.which!==t||!a)&&!(p!==P.U.vertical&&(!u||(0,O.mM)(e.target,"data-no-horizontal-wrap")))}(e)||e.which===c.m.up&&(e.altKey||e.metaKey)},v=function(e){m.current=qe(e);var t=e.which===c.m.escape&&((0,L.V)()||(0,A.g)());return f(e,h,t)},g=function(e){var t=m.current&&qe(e);return m.current=!1,!!t&&!((0,A.g)()||(0,L.V)())};return[v,function(e){return f(e,g,!0)},function(e){if(!v(e)&&n.current){var t=!(!e.altKey&&!e.metaKey),o=e.which===c.m.up,r=e.which===c.m.down;if(!t&&(o||r)){var i=o?(0,O.TE)(n.current,n.current.lastChild,!0):(0,O.ft)(n.current,n.current.firstChild,!0);i&&(i.focus(),e.preventDefault(),e.stopPropagation())}}},function(e,t){var n=(0,B.zg)(r)?c.m.left:c.m.right;e.disabled||t.which!==n&&t.which!==c.m.enter&&(t.which!==c.m.down||!t.altKey&&!t.metaKey)||(o(e,t.currentTarget),t.preventDefault())}]}var Ke=i.memo(i.forwardRef((function(e,t){var n,a=(0,H.j)(Fe,e),s=(a.ref,(0,r._T)(a,["ref"])),u=i.useRef(null),c=(0,Me.r)(),d=(0,Te.M)(Ae,s.id),p=function(e,t){var n;return null===(n=s.onDismiss)||void 0===n?void 0:n.call(s,e,t)},m=(0,De.e)(s.target,u),f=m[0],h=m[1],v=function(e,t,n){var o=e.hidden,r=e.onRestoreFocus,a=i.useRef(),s=i.useCallback((function(e){var t,n;r?r(e):(null===e||void 0===e?void 0:e.documentContainsFocus)&&(null===(n=null===(t=a.current)||void 0===t?void 0:t.focus)||void 0===n||n.call(t))}),[r]);return(0,he.L)((function(){var e,r;if(o)a.current&&(s({originalElement:a.current,containsFocus:!0,documentContainsFocus:(null===(r=(0,F.M)())||void 0===r?void 0:r.hasFocus())||!1}),a.current=void 0);else{var i=null===t||void 0===t?void 0:t.document.activeElement;(null===(e=n.current)||void 0===e?void 0:e.contains(i))||"BODY"===i.tagName||(a.current=i)}}),[o,null===t||void 0===t?void 0:t.document.activeElement,s,n]),[s]}(s,h,u)[0],g=ze(s,p),y=g[0],_=g[1],k=g[2],x=g[3],E=function(e){var t=e.delayUpdateFocusOnHover,n=e.hidden,o=i.useRef(!t),r=i.useRef(!1);i.useEffect((function(){o.current=!t,r.current=!n&&!t&&r.current}),[t,n]);var a=i.useCallback((function(){t&&(o.current=!1)}),[t]);return[o,r,a]}(s),I=E[0],w=E[1],T=E[2],M=function(e){var t=i.useRef(!0),n=i.useRef();return[function(){t.current||void 0===n.current?t.current=!1:(e.clearTimeout(n.current),n.current=void 0),n.current=e.setTimeout((function(){t.current=!0}),250)},t]}(c),D=M[0],S=M[1],R=function(e,t){var n=e.subMenuHoverDelay,o=void 0===n?250:n,r=i.useRef(void 0),a=function(){void 0!==r.current&&(t.clearTimeout(r.current),r.current=void 0)};return[a,function(e){r.current=t.setTimeout((function(){e(),a()}),o)},r]}(s,c),B=R[0],O=R[1],L=R[2],A=(0,Se.q)(u,s.responsiveMode);!function(e,t){var n=e.hidden,o=void 0!==n&&n,r=e.onMenuDismissed,a=e.onMenuOpened,s=(0,fe.D)(o),l=i.useRef(a),u=i.useRef(r),c=i.useRef(e);l.current=a,u.current=r,c.current=e,i.useEffect((function(){var e,t;o&&!1===s?null===(e=u.current)||void 0===e||e.call(u,c.current):o||!1===s||null===(t=l.current)||void 0===t||t.call(l,c.current)}),[o,s]),i.useEffect((function(){return function(){var e;return null===(e=u.current)||void 0===e?void 0:e.call(u,c.current)}}),[])}(s);var U=We(s,p,u,_),J=U[0],Z=U[1],j=U[2],Y=U[3],V=function(e,t,n,o,r,i,a,s,l,u,c,d,p){var m=e.target,f=function(){return!t.current||!i.current},h=function(e,t,n){var o=n||t.currentTarget;e.key!==a&&(u(),void 0===a&&o.focus(),K(e)?(t.stopPropagation(),l((function(){o.focus(),c(e,o,!0)}))):l((function(){d(t),o.focus()})))},v=function(t,n,o){var r=Le(t,{target:m});if(u(),K(t)||r&&r.length){if(t.key!==a){var i="boolean"===typeof e.shouldFocusOnContainer?e.shouldFocusOnContainer:"mouse"===n.nativeEvent.pointerType;c(t,o,i)}}else g(t,n);n.stopPropagation(),n.preventDefault()},g=function(t,n){if(!t.disabled&&!t.isDisabled){t.preferMenuTargetAsEventTarget&&je(n,m);var o=!1;t.onClick?o=!!t.onClick(n,t):e.onItemClick&&(o=!!e.onItemClick(n,t)),!o&&n.defaultPrevented||p(n,!0)}};return[function(e,t,n){r.current&&(i.current=!0),f()||h(e,t,n)},function(e,a,s){var l=a.currentTarget;r.current&&(i.current=!0,t.current&&void 0===n.current&&l!==(null===o||void 0===o?void 0:o.document.activeElement)&&h(e,a,s))},function(e,t){var n;if(!f()&&(u(),void 0===a))if(s.current.setActive)try{s.current.setActive()}catch(o){}else null===(n=s.current)||void 0===n||n.focus()},function(e,t){v(e,t,t.currentTarget)},function(e,t){g(e,t),t.stopPropagation()},g,v]}(s,S,L,h,I,w,y,u,O,B,_,x,p),$=V[0],Q=V[1],X=V[2],ee=V[3],te=V[4],ne=V[5],oe=V[6],re=function(e,t,n){var r=0,a=e.items,s=e.totalItemCount,l=e.hasCheckmarks,u=e.hasIcons;return i.createElement("ul",{className:t.list,onKeyDown:J,onKeyUp:Z,role:"presentation"},a.map((function(e,n){var i=ie(e,n,r,s,l,u,t);if(e.itemType!==o.Divider&&e.itemType!==o.Header){var a=e.customOnRenderListLength?e.customOnRenderListLength:1;r+=a}return i})))},ie=function(e,t,n,r,a,l,u){var c,d,p=[],m=e.iconProps||{iconName:"None"},f=e.getItemClassNames,h=e.itemProps,v=h?h.styles:void 0,g=e.itemType===o.Divider?e.className:void 0,b=e.submenuIconProps?e.submenuIconProps.className:"";if(f)d=f(s.theme,q(e),y===e.key,!!W(e),!!e.href,"None"!==m.iconName,e.className,g,m.className,b,e.primaryDisabled);else{var _={theme:s.theme,disabled:q(e),expanded:y===e.key,checked:!!W(e),isAnchorLink:!!e.href,knownIcon:"None"!==m.iconName,itemClassName:e.className,dividerClassName:g,iconClassName:m.className,subMenuClassName:b,primaryDisabled:e.primaryDisabled};d=Be(He(null===(c=u.subComponentStyles)||void 0===c?void 0:c.menuItem,v),_)}switch("-"!==e.text&&"-"!==e.name||(e.itemType=o.Divider),e.itemType){case o.Divider:p.push(le(t,d));break;case o.Header:p.push(le(t,d));var C=de(e,d,u,t,a,l);p.push(se(C,e.key||t,d,e.title));break;case o.Section:p.push(ae(e,d,u,t,a,l));break;default:var k=function(){return ue(e,d,t,n,r,a,l)},x=s.onRenderContextualMenuItem?s.onRenderContextualMenuItem(e,k):k();p.push(se(x,e.key||t,d,e.title))}return i.createElement(i.Fragment,{key:e.key},p)},ae=function(e,t,n,a,s,l){var u=e.sectionProps;if(u){var c,p;if(u.title){var m=void 0,f="";if("string"===typeof u.title){var h=d+u.title.replace(/\s/g,"");m={key:"section-".concat(u.title,"-title"),itemType:o.Header,text:u.title,id:h},f=h}else{var v=u.title.id||d+u.title.key.replace(/\s/g,"");m=(0,r.pi)((0,r.pi)({},u.title),{id:v}),f=v}m&&(p={role:"group","aria-labelledby":f},c=de(m,t,n,a,s,l))}if(u.items&&u.items.length>0){var g=0;return i.createElement("li",{role:"presentation",key:u.key||e.key||"section-".concat(a)},i.createElement("div",(0,r.pi)({},p),i.createElement("ul",{className:n.list,role:"presentation"},u.topDivider&&le(a,t,!0,!0),c&&se(c,e.key||a,t,e.title),u.items.map((function(e,t){var r=ie(e,t,g,Oe(u.items),s,l,n);if(e.itemType!==o.Divider&&e.itemType!==o.Header){var i=e.customOnRenderListLength?e.customOnRenderListLength:1;g+=i}return r})),u.bottomDivider&&le(a,t,!1,!0))))}}},se=function(e,t,n,o){return i.createElement("li",{role:"presentation",title:o,key:t,className:n.item},e)},le=function(e,t,n,o){return o||e>0?i.createElement("li",{role:"separator",key:"separator-"+e+(void 0===n?"":n?"-top":"-bottom"),className:t.divider,"aria-hidden":"true"}):null},ue=function(e,t,n,o,a,l,u){if(e.onRender)return e.onRender((0,r.pi)({"aria-posinset":o+1,"aria-setsize":a},e),p);var c={item:e,classNames:t,index:n,focusableElementIndex:o,totalItemCount:a,hasCheckmarks:l,hasIcons:u,contextualMenuItemAs:s.contextualMenuItemAs,onItemMouseEnter:$,onItemMouseLeave:X,onItemMouseMove:Q,onItemMouseDown:Ue,executeItemClick:ne,onItemKeyDown:Y,expandedMenuItemKey:y,openSubMenu:_,dismissSubMenu:x,dismissMenu:p};if(e.href){var d=Ce;return e.contextualMenuItemWrapperAs&&(d=(0,z.Z)(e.contextualMenuItemWrapperAs,d)),i.createElement(d,(0,r.pi)({},c,{onItemClick:te}))}if(e.split&&K(e)){var m=Ie;return e.contextualMenuItemWrapperAs&&(m=(0,z.Z)(e.contextualMenuItemWrapperAs,m)),i.createElement(m,(0,r.pi)({},c,{onItemClick:ee,onItemClickBase:oe,onTap:B}))}var f=we;return e.contextualMenuItemWrapperAs&&(f=(0,z.Z)(e.contextualMenuItemWrapperAs,f)),i.createElement(f,(0,r.pi)({},c,{onItemClick:ee,onItemClickBase:oe}))},de=function(e,t,n,o,a,l){var u=ce;e.contextualMenuItemAs&&(u=(0,z.Z)(e.contextualMenuItemAs,u)),s.contextualMenuItemAs&&(u=(0,z.Z)(s.contextualMenuItemAs,u));var c=e.itemProps,d=e.id,p=c&&(0,b.pq)(c,b.n7);return i.createElement("div",(0,r.pi)({id:d,className:n.header},p,{style:e.style}),i.createElement(u,(0,r.pi)({item:e,classNames:t,index:o,onCheckmarkClick:a?ee:void 0,hasIcons:l},c)))},pe=s.isBeakVisible,me=s.items,ve=s.labelElementId,ge=s.id,be=s.className,ye=s.beakWidth,_e=s.directionalHint,ke=s.directionalHintForRTL,xe=s.alignTargetEdge,Ee=s.gapSpace,Ke=s.coverTarget,qe=s.ariaLabel,Je=s.doNotLayer,Ze=s.target,Ye=s.bounds,Ve=s.useTargetWidth,$e=s.useTargetAsMinWidth,Qe=s.directionalHintFixed,Xe=s.shouldFocusOnMount,et=s.shouldFocusOnContainer,tt=s.title,nt=s.styles,ot=s.theme,rt=s.calloutProps,it=s.onRenderSubMenu,at=void 0===it?Ge:it,st=s.onRenderMenuList,lt=void 0===st?function(e,t){return re(e,dt)}:st,ut=s.focusZoneProps,ct=s.getMenuClassNames,dt=ct?ct(ot,be):Re(nt,{theme:ot,className:be}),pt=function e(t){for(var n=0,r=t;n<r.length;n++){var i=r[n];if(i.iconProps)return!0;if(i.itemType===o.Section&&i.sectionProps&&e(i.sectionProps.items))return!0}return!1}(me);var mt,ft=(0,r.pi)((0,r.pi)({direction:P.U.vertical,handleTabKey:P.J.all,isCircularNavigation:!0},ut),{className:(0,l.i)(dt.root,null===(n=s.focusZoneProps)||void 0===n?void 0:n.className)}),ht=function(e){return e.some((function(e){return!!e.canCheck||!(!e.sectionProps||!e.sectionProps.items.some((function(e){return!0===e.canCheck})))}))}(me),vt=y&&!0!==s.hidden?k():null;pe=void 0===pe?A<=Pe.eD.medium:pe;var gt=f.current;if((Ve||$e)&&gt&&gt.offsetWidth){var bt=gt.getBoundingClientRect().width-2;Ve?mt={width:bt}:$e&&(mt={minWidth:bt})}if(me&&me.length>0){var yt=Oe(me),_t=dt.subComponentStyles?dt.subComponentStyles.callout:void 0;return i.createElement(Ne.Consumer,null,(function(e){return i.createElement(G.U,(0,r.pi)({styles:_t,onRestoreFocus:v},rt,{target:Ze||e.target,isBeakVisible:pe,beakWidth:ye,directionalHint:_e,directionalHintForRTL:ke,gapSpace:Ee,coverTarget:Ke,doNotLayer:Je,className:(0,l.i)("ms-ContextualMenu-Callout",rt&&rt.className),setInitialFocus:Xe,onDismiss:s.onDismiss||e.onDismiss,onScroll:D,bounds:Ye,directionalHintFixed:Qe,alignTargetEdge:xe,hidden:s.hidden||e.hidden,ref:t}),i.createElement("div",{style:mt,ref:u,id:ge,className:dt.container,tabIndex:et?0:-1,onKeyDown:j,onKeyUp:Z,onFocusCapture:T,"aria-label":qe,"aria-labelledby":ve,role:"menu"},tt&&i.createElement("div",{className:dt.title}," ",tt," "),me&&me.length?function(e,t){var n=s.focusZoneAs,o=void 0===n?N.k:n;return i.createElement(o,(0,r.pi)({},t),e)}(lt({ariaLabel:qe,items:me,totalItemCount:yt,hasCheckmarks:ht,hasIcons:pt,defaultMenuItemRenderer:function(e){return function(e,t){var n=e.index,o=e.focusableElementIndex,r=e.totalItemCount,i=e.hasCheckmarks,a=e.hasIcons;return ie(e,n,o,r,i,a,t)}(e,dt)},labelElementId:ve},(function(e,t){return re(e,dt)})),ft):null,vt&&at(vt,Ge)),i.createElement(C.u,null))}))}return null})),(function(e,t){return!(t.shouldUpdateWhenHidden||!e.hidden||!t.hidden)||(0,y.Vv)(e,t)}));function qe(e){return e.which===c.m.alt||"Meta"===e.key}function Ue(e,t){var n;null===(n=e.onMouseDown)||void 0===n||n.call(e,e,t)}function Ge(e,t){throw Error("ContextualMenuBase: onRenderSubMenu callback is null or undefined. Please ensure to set `onRenderSubMenu` property either manually or with `styled` helper.")}function Je(e,t){for(var n=0,r=t;n<r.length;n++){var i=r[n];if(i.itemType===o.Section&&i.sectionProps){var a=Je(e,i.sectionProps.items);if(a)return a}else if(i.key&&i.key===e)return i}}function Ze(e,t){return e?function(n,o){return je(n,t),e(n,o)}:e}function je(e,t){e&&t&&(e.persist(),t instanceof Event?e.target=t.target:t instanceof Element&&(e.target=t))}Ke.displayName="ContextualMenuBase";var Ye={root:"ms-ContextualMenu",container:"ms-ContextualMenu-container",list:"ms-ContextualMenu-list",header:"ms-ContextualMenu-header",title:"ms-ContextualMenu-title",isopen:"is-open"};function Ve(e){return i.createElement($e,(0,r.pi)({},e))}var $e=(0,D.z)(Ke,(function(e){var t=e.className,n=e.theme,o=(0,X.Cn)(Ye,n),r=n.fonts,i=n.semanticColors,a=n.effects;return{root:[n.fonts.medium,o.root,o.isopen,{backgroundColor:i.menuBackground,minWidth:"180px"},t],container:[o.container,{selectors:{":focus":{outline:0}}}],list:[o.list,o.isopen,{listStyleType:"none",margin:"0",padding:"0"}],header:[o.header,r.small,{fontWeight:X.lq.semibold,color:i.menuHeader,background:"none",backgroundColor:"transparent",border:"none",height:te,lineHeight:te,cursor:"default",padding:"0px 6px",userSelect:"none",textAlign:"left"}],title:[o.title,{fontSize:r.mediumPlus.fontSize,paddingRight:"14px",paddingLeft:"14px",paddingBottom:"5px",paddingTop:"5px",backgroundColor:i.menuItemBackgroundPressed}],subComponentStyles:{callout:{root:{boxShadow:a.elevation8}},menuItem:{}}}}),(function(e){return{onRenderSubMenu:e.onRenderSubMenu?(0,S.k)(e.onRenderSubMenu,Ve):Ve}}),{scope:"ContextualMenu"}),Qe=$e;Qe.displayName="ContextualMenu";var Xe=n(71885),et=(0,s.NF)((function(e,t,n,o,r){return{root:(0,X.y0)(e.splitButtonMenuButton,n&&[e.splitButtonMenuButtonExpanded],t&&[e.splitButtonMenuButtonDisabled],o&&!t&&[e.splitButtonMenuButtonChecked],r&&!t&&[{selectors:{":focus":e.splitButtonMenuFocused}}]),splitButtonContainer:(0,X.y0)(e.splitButtonContainer,!t&&o&&[e.splitButtonContainerChecked,{selectors:{":hover":e.splitButtonContainerCheckedHovered}}],!t&&!o&&[{selectors:{":hover":e.splitButtonContainerHovered,":focus":e.splitButtonContainerFocused}}],t&&e.splitButtonContainerDisabled),icon:(0,X.y0)(e.splitButtonMenuIcon,t&&e.splitButtonMenuIconDisabled,!t&&r&&e.splitButtonMenuIcon),flexContainer:(0,X.y0)(e.splitButtonFlexContainer),divider:(0,X.y0)(e.splitButtonDivider,(r||t)&&e.splitButtonDividerDisabled)}})),tt="BaseButton",nt=function(e){function t(t){var n=e.call(this,t)||this;return n._buttonElement=i.createRef(),n._splitButtonContainer=i.createRef(),n._mergedRef=(0,a.S)(),n._renderedVisibleMenu=!1,n._getMemoizedMenuButtonKeytipProps=(0,s.NF)((function(e){return(0,r.pi)((0,r.pi)({},e),{hasMenu:!0})})),n._onRenderIcon=function(e,t){var o=n.props.iconProps;if(o&&(void 0!==o.iconName||o.imageProps)){var a=o.className,s=o.imageProps,u=(0,r._T)(o,["className","imageProps"]);if(o.styles)return i.createElement(x.J,(0,r.pi)({className:(0,l.i)(n._classNames.icon,a),imageProps:s},u));if(o.iconName)return i.createElement(E.xu,(0,r.pi)({className:(0,l.i)(n._classNames.icon,a)},u));if(s)return i.createElement(T,(0,r.pi)({className:(0,l.i)(n._classNames.icon,a),imageProps:s},u))}return null},n._onRenderTextContents=function(){var e=n.props,t=e.text,o=e.children,r=e.secondaryText,a=void 0===r?n.props.description:r,s=e.onRenderText,l=void 0===s?n._onRenderText:s,u=e.onRenderDescription,c=void 0===u?n._onRenderDescription:u;return t||"string"===typeof o||a?i.createElement("span",{className:n._classNames.textContainer},l(n.props,n._onRenderText),c(n.props,n._onRenderDescription)):[l(n.props,n._onRenderText),c(n.props,n._onRenderDescription)]},n._onRenderText=function(){var e=n.props.text,t=n.props.children;return void 0===e&&"string"===typeof t&&(e=t),n._hasText()?i.createElement("span",{key:n._labelId,className:n._classNames.label,id:n._labelId},e):null},n._onRenderChildren=function(){var e=n.props.children;return"string"===typeof e?null:e},n._onRenderDescription=function(e){var t=e.secondaryText,o=void 0===t?n.props.description:t;return o?i.createElement("span",{key:n._descriptionId,className:n._classNames.description,id:n._descriptionId},o):null},n._onRenderAriaDescription=function(){var e=n.props.ariaDescription;return e?i.createElement("span",{className:n._classNames.screenReaderText,id:n._ariaDescriptionId},e):null},n._onRenderMenuIcon=function(e){var t=n.props.menuIconProps;return i.createElement(E.xu,(0,r.pi)({iconName:"ChevronDown"},t,{className:n._classNames.menuIcon}))},n._onRenderMenu=function(e){var t=n.props.menuAs?(0,z.Z)(n.props.menuAs,Qe):Qe;return i.createElement(t,(0,r.pi)({},e))},n._onDismissMenu=function(e){var t=n.props.menuProps;t&&t.onDismiss&&t.onDismiss(e),e&&e.defaultPrevented||n._dismissMenu()},n._dismissMenu=function(){n._menuShouldFocusOnMount=void 0,n._menuShouldFocusOnContainer=void 0,n.setState({menuHidden:!0})},n._openMenu=function(e,t){void 0===t&&(t=!0),n.props.menuProps&&(n._menuShouldFocusOnContainer=e,n._menuShouldFocusOnMount=t,n._renderedVisibleMenu=!0,n.setState({menuHidden:!1}))},n._onToggleMenu=function(e){var t=!0;n.props.menuProps&&!1===n.props.menuProps.shouldFocusOnMount&&(t=!1),n.state.menuHidden?n._openMenu(e,t):n._dismissMenu()},n._onSplitContainerFocusCapture=function(e){var t=n._splitButtonContainer.current;!t||e.target&&(0,u.w)(e.target,t)||t.focus()},n._onSplitButtonPrimaryClick=function(e){n.state.menuHidden||n._dismissMenu();var t=n._processingTouch&&!n.props.toggle;!t&&n.props.onClick?n.props.onClick(e):t&&n._onMenuClick(e)},n._onKeyDown=function(e){!n.props.disabled||e.which!==c.m.enter&&e.which!==c.m.space?n.props.disabled||(n.props.menuProps?n._onMenuKeyDown(e):void 0!==n.props.onKeyDown&&n.props.onKeyDown(e)):(e.preventDefault(),e.stopPropagation())},n._onKeyUp=function(e){n.props.disabled||void 0===n.props.onKeyUp||n.props.onKeyUp(e)},n._onKeyPress=function(e){n.props.disabled||void 0===n.props.onKeyPress||n.props.onKeyPress(e)},n._onMouseUp=function(e){n.props.disabled||void 0===n.props.onMouseUp||n.props.onMouseUp(e)},n._onMouseDown=function(e){n.props.disabled||void 0===n.props.onMouseDown||n.props.onMouseDown(e)},n._onClick=function(e){n.props.disabled||(n.props.menuProps?n._onMenuClick(e):void 0!==n.props.onClick&&n.props.onClick(e))},n._onSplitButtonContainerKeyDown=function(e){e.which===c.m.enter||e.which===c.m.space?n._buttonElement.current&&(n._buttonElement.current.click(),e.preventDefault(),e.stopPropagation()):n._onMenuKeyDown(e)},n._onMenuKeyDown=function(e){var t;if(!n.props.disabled){n.props.onKeyDown&&n.props.onKeyDown(e);var o=e.which===c.m.up,r=e.which===c.m.down;if(!e.defaultPrevented&&n._isValidMenuOpenKey(e)){var i=n.props.onMenuClick;i&&i(e,n.props),n._onToggleMenu(!1),e.preventDefault(),e.stopPropagation()}if(e.which!==c.m.enter&&e.which!==c.m.space||(0,d.MU)(!0,e.target,null===(t=n.context)||void 0===t?void 0:t.registeredProviders),!e.altKey&&!e.metaKey&&(o||r))if(!n.state.menuHidden&&n.props.menuProps)(void 0!==n._menuShouldFocusOnMount?n._menuShouldFocusOnMount:n.props.menuProps.shouldFocusOnMount)||(e.preventDefault(),e.stopPropagation(),n._menuShouldFocusOnMount=!0,n.forceUpdate())}},n._onTouchStart=function(){n._isSplitButton&&n._splitButtonContainer.current&&!("onpointerdown"in n._splitButtonContainer.current)&&n._handleTouchAndPointerEvent()},n._onMenuClick=function(e){var t=n.props,o=t.onMenuClick,r=t.menuProps;o&&o(e,n.props);var i="boolean"===typeof(null===r||void 0===r?void 0:r.shouldFocusOnContainer)?r.shouldFocusOnContainer:"mouse"===e.nativeEvent.pointerType;e.defaultPrevented||(n._onToggleMenu(i),e.preventDefault(),e.stopPropagation())},(0,p.l)(n),n._async=new m.e(n),n._events=new f.r(n),(0,h.w)(tt,t,["menuProps","onClick"],"split",n.props.split),(0,v.b)(tt,t,{rootProps:void 0,description:"secondaryText",toggled:"checked"}),n._labelId=(0,g.z)(),n._descriptionId=(0,g.z)(),n._ariaDescriptionId=(0,g.z)(),n.state={menuHidden:!0},n}return(0,r.ZT)(t,e),Object.defineProperty(t.prototype,"_isSplitButton",{get:function(){return!!this.props.menuProps&&!!this.props.onClick&&!0===this.props.split},enumerable:!1,configurable:!0}),t.prototype.render=function(){var e,t=this.props,n=t.ariaDescription,o=t.ariaLabel,r=t.ariaHidden,i=t.className,a=t.disabled,s=t.allowDisabledFocus,l=t.primaryDisabled,u=t.secondaryText,c=void 0===u?this.props.description:u,d=t.href,p=t.iconProps,m=t.menuIconProps,f=t.styles,h=t.checked,v=t.variantClassName,g=t.theme,C=t.toggle,k=t.getClassNames,x=t.role,E=this.state.menuHidden,I=a||l;this._classNames=k?k(g,i,v,p&&p.className,m&&m.className,I,h,!E,!!this.props.menuProps,this.props.split,!!s):(0,Xe.f)(g,f,i,v,p&&p.className,m&&m.className,I,!!this.props.menuProps,h,!E,this.props.split);var w=this,T=w._ariaDescriptionId,M=w._labelId,D=w._descriptionId,S=!I&&!!d,P=S?"a":"button",N=(0,b.pq)((0,y.f0)(S?{}:{type:"button"},this.props.rootProps,this.props),S?b.h2:b.Yq,["disabled"]),R=o||N["aria-label"],B=void 0;n?B=T:c&&this.props.onRenderDescription!==_.S?B=D:N["aria-describedby"]&&(B=N["aria-describedby"]);var F=void 0;N["aria-labelledby"]?F=N["aria-labelledby"]:B&&!R&&(F=this._hasText()?M:void 0);var O=!(!1===this.props["data-is-focusable"]||a&&!s||this._isSplitButton),L="menuitemcheckbox"===x||"checkbox"===x,A=L||!0===C?!!h:void 0,H=(0,y.f0)(N,((e={className:this._classNames.root,ref:this._mergedRef(this.props.elementRef,this._buttonElement),disabled:I&&!s,onKeyDown:this._onKeyDown,onKeyPress:this._onKeyPress,onKeyUp:this._onKeyUp,onMouseDown:this._onMouseDown,onMouseUp:this._onMouseUp,onClick:this._onClick,"aria-label":R,"aria-labelledby":F,"aria-describedby":B,"aria-disabled":I,"data-is-focusable":O})[L?"aria-checked":"aria-pressed"]=A,e));if(r&&(H["aria-hidden"]=!0),this._isSplitButton)return this._onRenderSplitButtonContent(P,H);if(this.props.menuProps){var z=this.props.menuProps.id,W=void 0===z?"".concat(this._labelId,"-menu"):z;(0,y.f0)(H,{"aria-expanded":!E,"aria-controls":E?null:W,"aria-haspopup":!0})}return this._onRenderContent(P,H)},t.prototype.componentDidMount=function(){this._isSplitButton&&this._splitButtonContainer.current&&("onpointerdown"in this._splitButtonContainer.current&&this._events.on(this._splitButtonContainer.current,"pointerdown",this._onPointerDown,!0),"onpointerup"in this._splitButtonContainer.current&&this.props.onPointerUp&&this._events.on(this._splitButtonContainer.current,"pointerup",this.props.onPointerUp,!0))},t.prototype.componentDidUpdate=function(e,t){this.props.onAfterMenuDismiss&&!t.menuHidden&&this.state.menuHidden&&this.props.onAfterMenuDismiss()},t.prototype.componentWillUnmount=function(){this._async.dispose(),this._events.dispose()},t.prototype.focus=function(){var e,t;this._isSplitButton&&this._splitButtonContainer.current?((0,d.MU)(!0,void 0,null===(e=this.context)||void 0===e?void 0:e.registeredProviders),this._splitButtonContainer.current.focus()):this._buttonElement.current&&((0,d.MU)(!0,void 0,null===(t=this.context)||void 0===t?void 0:t.registeredProviders),this._buttonElement.current.focus())},t.prototype.dismissMenu=function(){this._dismissMenu()},t.prototype.openMenu=function(e,t){this._openMenu(e,t)},t.prototype._onRenderContent=function(e,t){var n=this,o=this.props,a=e,s=o.menuIconProps,l=o.menuProps,u=o.onRenderIcon,c=void 0===u?this._onRenderIcon:u,d=o.onRenderAriaDescription,p=void 0===d?this._onRenderAriaDescription:d,m=o.onRenderChildren,f=void 0===m?this._onRenderChildren:m,h=o.onRenderMenu,v=void 0===h?this._onRenderMenu:h,g=o.onRenderMenuIcon,b=void 0===g?this._onRenderMenuIcon:g,y=o.disabled,_=o.keytipProps;_&&l&&(_=this._getMemoizedMenuButtonKeytipProps(_));var k=function(e){return i.createElement(a,(0,r.pi)({},t,e),i.createElement("span",{className:n._classNames.flexContainer,"data-automationid":"splitbuttonprimary"},c(o,n._onRenderIcon),n._onRenderTextContents(),p(o,n._onRenderAriaDescription),f(o,n._onRenderChildren),!n._isSplitButton&&(l||s||n.props.onRenderMenuIcon)&&b(n.props,n._onRenderMenuIcon),l&&!l.doNotLayer&&n._shouldRenderMenu()&&v(n._getMenuProps(l),n._onRenderMenu)))},x=_?i.createElement(_e,{keytipProps:this._isSplitButton?void 0:_,ariaDescribedBy:t["aria-describedby"],disabled:y},(function(e){return k(e)})):k();return l&&l.doNotLayer?i.createElement(i.Fragment,null,x,this._shouldRenderMenu()&&v(this._getMenuProps(l),this._onRenderMenu)):i.createElement(i.Fragment,null,x,i.createElement(C.u,null))},t.prototype._shouldRenderMenu=function(){var e=this.state.menuHidden,t=this.props,n=t.persistMenu,o=t.renderPersistedMenuHiddenOnMount;return!e||!(!n||!this._renderedVisibleMenu&&!o)},t.prototype._hasText=function(){return null!==this.props.text&&(void 0!==this.props.text||"string"===typeof this.props.children)},t.prototype._getMenuProps=function(e){var t=this.props.persistMenu,n=this.state.menuHidden;return e.ariaLabel||e.labelElementId||!this._hasText()||(e=(0,r.pi)((0,r.pi)({},e),{labelElementId:this._labelId})),(0,r.pi)((0,r.pi)({id:this._labelId+"-menu",directionalHint:M.b.bottomLeftEdge},e),{shouldFocusOnContainer:this._menuShouldFocusOnContainer,shouldFocusOnMount:this._menuShouldFocusOnMount,hidden:t?n:void 0,className:(0,l.i)("ms-BaseButton-menuhost",e.className),target:this._isSplitButton?this._splitButtonContainer.current:this._buttonElement.current,onDismiss:this._onDismissMenu})},t.prototype._onRenderSplitButtonContent=function(e,t){var n=this,o=this.props,a=o.styles,s=void 0===a?{}:a,l=o.disabled,u=o.allowDisabledFocus,c=o.checked,d=o.getSplitButtonClassNames,p=o.primaryDisabled,m=o.menuProps,f=o.toggle,h=o.role,v=o.primaryActionButtonProps,g=this.props.keytipProps,_=this.state.menuHidden,C=d?d(!!l,!_,!!c,!!u):s&&et(s,!!l,!_,!!c,!!p);(0,y.f0)(t,{onClick:void 0,onPointerDown:void 0,onPointerUp:void 0,tabIndex:-1,"data-is-focusable":!1}),g&&m&&(g=this._getMemoizedMenuButtonKeytipProps(g));var x=(0,b.pq)(t,[],["disabled"]);v&&(0,y.f0)(t,v);var E=function(o){return i.createElement("div",(0,r.pi)({},x,{"data-ktp-target":o?o["data-ktp-target"]:void 0,role:h||"button","aria-disabled":l,"aria-haspopup":!0,"aria-expanded":!_,"aria-pressed":f?!!c:void 0,"aria-describedby":(0,k.I)(t["aria-describedby"],o?o["aria-describedby"]:void 0),className:C&&C.splitButtonContainer,onKeyDown:n._onSplitButtonContainerKeyDown,onTouchStart:n._onTouchStart,ref:n._splitButtonContainer,"data-is-focusable":!0,onClick:l||p?void 0:n._onSplitButtonPrimaryClick,tabIndex:!l&&!p||u?0:void 0,"aria-roledescription":t["aria-roledescription"],onFocusCapture:n._onSplitContainerFocusCapture}),i.createElement("span",{style:{display:"flex",width:"100%"}},n._onRenderContent(e,t),n._onRenderSplitButtonMenuButton(C,o),n._onRenderSplitButtonDivider(C)))};return g?i.createElement(_e,{keytipProps:g,disabled:l},(function(e){return E(e)})):E()},t.prototype._onRenderSplitButtonDivider=function(e){if(e&&e.divider){return i.createElement("span",{className:e.divider,"aria-hidden":!0,onClick:function(e){e.stopPropagation()}})}return null},t.prototype._onRenderSplitButtonMenuButton=function(e,n){var o=this.props,a=o.allowDisabledFocus,s=o.checked,l=o.disabled,u=o.splitButtonMenuProps,c=o.splitButtonAriaLabel,d=o.primaryDisabled,p=this.state.menuHidden,m=this.props.menuIconProps;void 0===m&&(m={iconName:"ChevronDown"});var f=(0,r.pi)((0,r.pi)({},u),{styles:e,checked:s,disabled:l,allowDisabledFocus:a,onClick:this._onMenuClick,menuProps:void 0,iconProps:(0,r.pi)((0,r.pi)({},m),{className:this._classNames.menuIcon}),ariaLabel:c,"aria-haspopup":!0,"aria-expanded":!p,"data-is-focusable":!1});return i.createElement(t,(0,r.pi)({},f,{"data-ktp-execute-target":n?n["data-ktp-execute-target"]:n,onMouseDown:this._onMouseDown,tabIndex:d&&!a?0:-1}))},t.prototype._onPointerDown=function(e){var t=this.props.onPointerDown;t&&t(e),"touch"===e.pointerType&&(this._handleTouchAndPointerEvent(),e.preventDefault(),e.stopImmediatePropagation())},t.prototype._handleTouchAndPointerEvent=function(){var e=this;void 0!==this._lastTouchTimeoutId&&(this._async.clearTimeout(this._lastTouchTimeoutId),this._lastTouchTimeoutId=void 0),this._processingTouch=!0,this._lastTouchTimeoutId=this._async.setTimeout((function(){e._processingTouch=!1,e._lastTouchTimeoutId=void 0,e.state.menuHidden&&e.focus()}),500)},t.prototype._isValidMenuOpenKey=function(e){return this.props.menuTriggerKeyCode?e.which===this.props.menuTriggerKeyCode:!!this.props.menuProps&&(e.which===c.m.down&&(e.altKey||e.metaKey))},t.defaultProps={baseClassName:"ms-Button",styles:{},split:!1},t.contextType=C.uK,t}(i.Component)},63997:function(e,t,n){n.d(t,{W:function(){return s}});var o=n(42792),r=n(40528),i={outline:0},a=function(e){return{fontSize:e,margin:"0 4px",height:"16px",lineHeight:"16px",textAlign:"center",flexShrink:0}},s=(0,o.NF)((function(e){var t,n,o=e.semanticColors,s=e.effects,l=e.fonts,u=o.buttonBorder,c=o.disabledBackground,d=o.disabledText,p={left:-2,top:-2,bottom:-2,right:-2,outlineColor:"ButtonText"};return{root:[(0,r.GL)(e,{inset:1,highContrastStyle:p,borderColor:"transparent"}),e.fonts.medium,{border:"1px solid "+u,borderRadius:s.roundedCorner2,boxSizing:"border-box",cursor:"pointer",display:"inline-block",padding:"0 16px",textDecoration:"none",textAlign:"center",userSelect:"none",selectors:{":active > span":{position:"relative",left:0,top:0}}}],rootDisabled:[(0,r.GL)(e,{inset:1,highContrastStyle:p,borderColor:"transparent"}),{backgroundColor:c,borderColor:c,color:d,cursor:"default",selectors:{":hover":i,":focus":i}}],iconDisabled:{color:d,selectors:(t={},t[r.qJ]={color:"GrayText"},t)},menuIconDisabled:{color:d,selectors:(n={},n[r.qJ]={color:"GrayText"},n)},flexContainer:{display:"flex",height:"100%",flexWrap:"nowrap",justifyContent:"center",alignItems:"center"},description:{display:"block"},textContainer:{flexGrow:1,display:"block"},icon:a(l.mediumPlus.fontSize),menuIcon:a(l.small.fontSize),label:{margin:"0 4px",lineHeight:"100%",display:"block"},screenReaderText:r.ul}}))},36190:function(e,t,n){n.d(t,{M:function(){return o}});var o=n(54794).K},81318:function(e,t,n){n.d(t,{a:function(){return f}});var o=n(75971),r=n(72791),i=n(62020),a=n(50907),s=n(91605),l=n(40528),u=n(42792),c=n(63997),d=n(30116),p=n(61910);var m=(0,u.NF)((function(e,t,n){var r=(0,c.W)(e),i=(0,d.W)(e),a={root:{minWidth:"80px",height:"32px"},label:{fontWeight:l.lq.semibold}};return(0,l.E$)(r,a,n?function(e){var t,n,r,i,a,s,u,c,d,m=e.palette,f=e.semanticColors;return{root:{backgroundColor:f.primaryButtonBackground,border:"1px solid ".concat(f.primaryButtonBackground),color:f.primaryButtonText,selectors:(t={},t[l.qJ]=(0,o.pi)({color:"Window",backgroundColor:"WindowText",borderColor:"WindowText"},(0,l.xM)()),t[".".concat(p.G$," &:focus")]={selectors:{":after":{border:"none",outlineColor:m.white}}},t)},rootHovered:{backgroundColor:f.primaryButtonBackgroundHovered,border:"1px solid ".concat(f.primaryButtonBackgroundHovered),color:f.primaryButtonTextHovered,selectors:(n={},n[l.qJ]={color:"Window",backgroundColor:"Highlight",borderColor:"Highlight"},n)},rootPressed:{backgroundColor:f.primaryButtonBackgroundPressed,border:"1px solid ".concat(f.primaryButtonBackgroundPressed),color:f.primaryButtonTextPressed,selectors:(r={},r[l.qJ]=(0,o.pi)({color:"Window",backgroundColor:"WindowText",borderColor:"WindowText"},(0,l.xM)()),r)},rootExpanded:{backgroundColor:f.primaryButtonBackgroundPressed,color:f.primaryButtonTextPressed},rootChecked:{backgroundColor:f.primaryButtonBackgroundPressed,color:f.primaryButtonTextPressed},rootCheckedHovered:{backgroundColor:f.primaryButtonBackgroundPressed,color:f.primaryButtonTextPressed},rootDisabled:{color:f.primaryButtonTextDisabled,backgroundColor:f.primaryButtonBackgroundDisabled,selectors:(i={},i[l.qJ]={color:"GrayText",borderColor:"GrayText",backgroundColor:"Window"},i)},splitButtonContainer:{selectors:(a={},a[l.qJ]={border:"none"},a)},splitButtonDivider:(0,o.pi)((0,o.pi)({},{position:"absolute",width:1,right:31,top:8,bottom:8}),{backgroundColor:m.white,selectors:(s={},s[l.qJ]={backgroundColor:"Window"},s)}),splitButtonMenuButton:{backgroundColor:f.primaryButtonBackground,color:f.primaryButtonText,selectors:(u={},u[l.qJ]={backgroundColor:"Canvas"},u[":hover"]={backgroundColor:f.primaryButtonBackgroundHovered,selectors:(c={},c[l.qJ]={color:"Highlight"},c)},u)},splitButtonMenuButtonDisabled:{backgroundColor:f.primaryButtonBackgroundDisabled,selectors:{":hover":{backgroundColor:f.primaryButtonBackgroundDisabled}}},splitButtonMenuButtonChecked:{backgroundColor:f.primaryButtonBackgroundPressed,selectors:{":hover":{backgroundColor:f.primaryButtonBackgroundPressed}}},splitButtonMenuButtonExpanded:{backgroundColor:f.primaryButtonBackgroundPressed,selectors:{":hover":{backgroundColor:f.primaryButtonBackgroundPressed}}},splitButtonMenuIcon:{color:f.primaryButtonText},splitButtonMenuIconDisabled:{color:m.neutralTertiary,selectors:(d={},d[l.qJ]={color:"GrayText"},d)}}}(e):function(e){var t,n,r,i,a,s=e.semanticColors,u=e.palette,c=s.buttonBackground,d=s.buttonBackgroundPressed,p=s.buttonBackgroundHovered,m=s.buttonBackgroundDisabled,f=s.buttonText,h=s.buttonTextHovered,v=s.buttonTextDisabled,g=s.buttonTextChecked,b=s.buttonTextCheckedHovered;return{root:{backgroundColor:c,color:f},rootHovered:{backgroundColor:p,color:h,selectors:(t={},t[l.qJ]={borderColor:"Highlight",color:"Highlight"},t)},rootPressed:{backgroundColor:d,color:g},rootExpanded:{backgroundColor:d,color:g},rootChecked:{backgroundColor:d,color:g},rootCheckedHovered:{backgroundColor:d,color:b},rootDisabled:{color:v,backgroundColor:m,selectors:(n={},n[l.qJ]={color:"GrayText",borderColor:"GrayText",backgroundColor:"Window"},n)},splitButtonContainer:{selectors:(r={},r[l.qJ]={border:"none"},r)},splitButtonMenuButton:{color:u.white,backgroundColor:"transparent",selectors:{":hover":{backgroundColor:u.neutralLight,selectors:(i={},i[l.qJ]={color:"Highlight"},i)}}},splitButtonMenuButtonDisabled:{backgroundColor:s.buttonBackgroundDisabled,selectors:{":hover":{backgroundColor:s.buttonBackgroundDisabled}}},splitButtonDivider:(0,o.pi)((0,o.pi)({},{position:"absolute",width:1,right:31,top:8,bottom:8}),{backgroundColor:u.neutralTertiaryAlt,selectors:(a={},a[l.qJ]={backgroundColor:"WindowText"},a)}),splitButtonDividerDisabled:{backgroundColor:e.palette.neutralTertiaryAlt},splitButtonMenuButtonChecked:{backgroundColor:u.neutralQuaternaryAlt,selectors:{":hover":{backgroundColor:u.neutralQuaternaryAlt}}},splitButtonMenuButtonExpanded:{backgroundColor:u.neutralQuaternaryAlt,selectors:{":hover":{backgroundColor:u.neutralQuaternaryAlt}}},splitButtonMenuIcon:{color:s.buttonText},splitButtonMenuIconDisabled:{color:s.buttonTextDisabled}}}(e),i,t)})),f=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.primary,n=void 0!==t&&t,s=e.styles,l=e.theme;return r.createElement(i.Y,(0,o.pi)({},this.props,{variantClassName:n?"ms-Button--primary":"ms-Button--default",styles:m(l,s,n),onRenderDescription:a.S}))},t=(0,o.gn)([(0,s.a)("DefaultButton",["theme","styles"],!0)],t)}(r.Component)},29093:function(e,t,n){n.d(t,{h:function(){return m}});var o=n(75971),r=n(72791),i=n(62020),a=n(50907),s=n(91605),l=n(40528),u=n(42792),c=n(63997),d=n(30116),p=(0,u.NF)((function(e,t){var n,o=(0,c.W)(e),r=(0,d.W)(e),i=e.palette,a={root:{padding:"0 4px",width:"32px",height:"32px",backgroundColor:"transparent",border:"none",color:e.semanticColors.link},rootHovered:{color:i.themeDarkAlt,backgroundColor:i.neutralLighter,selectors:(n={},n[l.qJ]={borderColor:"Highlight",color:"Highlight"},n)},rootHasMenu:{width:"auto"},rootPressed:{color:i.themeDark,backgroundColor:i.neutralLight},rootExpanded:{color:i.themeDark,backgroundColor:i.neutralLight},rootChecked:{color:i.themeDark,backgroundColor:i.neutralLight},rootCheckedHovered:{color:i.themeDark,backgroundColor:i.neutralQuaternaryAlt},rootDisabled:{color:i.neutralTertiaryAlt}};return(0,l.E$)(o,a,r,t)})),m=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.styles,n=e.theme;return r.createElement(i.Y,(0,o.pi)({},this.props,{variantClassName:"ms-Button--icon",styles:p(n,t),onRenderText:a.S,onRenderDescription:a.S}))},t=(0,o.gn)([(0,s.a)("IconButton",["theme","styles"],!0)],t)}(r.Component)},70927:function(e,t,n){n.d(t,{K:function(){return l}});var o=n(75971),r=n(72791),i=n(50907),a=n(91605),s=n(81318),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.ZT)(t,e),t.prototype.render=function(){return r.createElement(s.a,(0,o.pi)({},this.props,{primary:!0,onRenderDescription:i.S}))},t=(0,o.gn)([(0,a.a)("PrimaryButton",["theme","styles"],!0)],t)}(r.Component)},30116:function(e,t,n){n.d(t,{W:function(){return i}});var o=n(75971),r=n(40528),i=(0,n(42792).NF)((function(e,t){var n,i,a,s,l,u,c,d,p,m,f,h,v,g=e.effects,b=e.palette,y=e.semanticColors,_={left:-2,top:-2,bottom:-2,right:-2,border:"none"},C={position:"absolute",width:1,right:31,top:8,bottom:8},k={splitButtonContainer:[(0,r.GL)(e,{highContrastStyle:_,inset:2,pointerEvents:"none"}),{display:"inline-flex",selectors:{".ms-Button--default":{borderTopRightRadius:"0",borderBottomRightRadius:"0",borderRight:"none",flexGrow:"1"},".ms-Button--primary":{borderTopRightRadius:"0",borderBottomRightRadius:"0",border:"none",flexGrow:"1",selectors:(n={},n[r.qJ]=(0,o.pi)({color:"WindowText",backgroundColor:"Window",border:"1px solid WindowText",borderRightWidth:"0"},(0,r.xM)()),n[":hover"]={border:"none"},n[":active"]={border:"none"},n)},".ms-Button--primary + .ms-Button":{border:"none",selectors:(i={},i[r.qJ]={border:"1px solid WindowText",borderLeftWidth:"0"},i)}}}],splitButtonContainerHovered:{selectors:{".ms-Button--primary":{selectors:(a={},a[r.qJ]={color:"Window",backgroundColor:"Highlight"},a)},".ms-Button.is-disabled":{color:y.buttonTextDisabled,selectors:(s={},s[r.qJ]={color:"GrayText",borderColor:"GrayText",backgroundColor:"Window"},s)}}},splitButtonContainerChecked:{selectors:{".ms-Button--primary":{selectors:(l={},l[r.qJ]=(0,o.pi)({color:"Window",backgroundColor:"WindowText"},(0,r.xM)()),l)}}},splitButtonContainerCheckedHovered:{selectors:{".ms-Button--primary":{selectors:(u={},u[r.qJ]=(0,o.pi)({color:"Window",backgroundColor:"WindowText"},(0,r.xM)()),u)}}},splitButtonContainerFocused:{outline:"none!important"},splitButtonMenuButton:(c={padding:6,height:"auto",boxSizing:"border-box",borderRadius:0,borderTopRightRadius:g.roundedCorner2,borderBottomRightRadius:g.roundedCorner2,border:"1px solid ".concat(b.neutralSecondaryAlt),borderLeft:"none",outline:"transparent",userSelect:"none",display:"inline-block",textDecoration:"none",textAlign:"center",cursor:"pointer",verticalAlign:"top",width:32,marginLeft:-1,marginTop:0,marginRight:0,marginBottom:0},c[r.qJ]={".ms-Button-menuIcon":{color:"WindowText"}},c),splitButtonDivider:(0,o.pi)((0,o.pi)({},C),{selectors:(d={},d[r.qJ]={backgroundColor:"WindowText"},d)}),splitButtonDividerDisabled:(0,o.pi)((0,o.pi)({},C),{selectors:(p={},p[r.qJ]={backgroundColor:"GrayText"},p)}),splitButtonMenuButtonDisabled:{pointerEvents:"none",border:"none",selectors:(m={":hover":{cursor:"default"},".ms-Button--primary":{selectors:(f={},f[r.qJ]={color:"GrayText",borderColor:"GrayText",backgroundColor:"Window"},f)},".ms-Button-menuIcon":{selectors:(h={},h[r.qJ]={color:"GrayText"},h)}},m[r.qJ]={color:"GrayText",border:"1px solid GrayText",backgroundColor:"Window"},m)},splitButtonFlexContainer:{display:"flex",height:"100%",flexWrap:"nowrap",justifyContent:"center",alignItems:"center"},splitButtonContainerDisabled:{outline:"none",border:"none",selectors:(v={},v[r.qJ]=(0,o.pi)({color:"GrayText",borderColor:"GrayText",backgroundColor:"Window"},(0,r.xM)()),v)},splitButtonMenuFocused:(0,o.pi)({},(0,r.GL)(e,{highContrastStyle:_,inset:2}))};return(0,r.E$)(k,t)}))},87023:function(e,t,n){n.d(t,{U:function(){return ke}});var o,r=n(75971),i=n(72791),a=n(40433),s=n(81080),l=n(56500),u=n(84688),c=n(64862),d=n(97471),p=n(15863),m=n(29723),f=n(35726),h=n(50899),v=n(87376),g=n(71675),b=function(){function e(e,t,n,o){void 0===e&&(e=0),void 0===t&&(t=0),void 0===n&&(n=0),void 0===o&&(o=0),this.top=n,this.bottom=o,this.left=e,this.right=t}return Object.defineProperty(e.prototype,"width",{get:function(){return this.right-this.left},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this.bottom-this.top},enumerable:!1,configurable:!0}),e.prototype.equals=function(e){return parseFloat(this.top.toFixed(4))===parseFloat(e.top.toFixed(4))&&parseFloat(this.bottom.toFixed(4))===parseFloat(e.bottom.toFixed(4))&&parseFloat(this.left.toFixed(4))===parseFloat(e.left.toFixed(4))&&parseFloat(this.right.toFixed(4))===parseFloat(e.right.toFixed(4))},e}();function y(e,t,n){return{targetEdge:e,alignmentEdge:t,isAuto:n}}var _=((o={})[s.b.topLeftEdge]=y(h.z.top,h.z.left),o[s.b.topCenter]=y(h.z.top),o[s.b.topRightEdge]=y(h.z.top,h.z.right),o[s.b.topAutoEdge]=y(h.z.top,void 0,!0),o[s.b.bottomLeftEdge]=y(h.z.bottom,h.z.left),o[s.b.bottomCenter]=y(h.z.bottom),o[s.b.bottomRightEdge]=y(h.z.bottom,h.z.right),o[s.b.bottomAutoEdge]=y(h.z.bottom,void 0,!0),o[s.b.leftTopEdge]=y(h.z.left,h.z.top),o[s.b.leftCenter]=y(h.z.left),o[s.b.leftBottomEdge]=y(h.z.left,h.z.bottom),o[s.b.rightTopEdge]=y(h.z.right,h.z.top),o[s.b.rightCenter]=y(h.z.right),o[s.b.rightBottomEdge]=y(h.z.right,h.z.bottom),o);function C(e,t){return!(e.top<t.top)&&(!(e.bottom>t.bottom)&&(!(e.left<t.left)&&!(e.right>t.right)))}function k(e,t){var n=[];return e.top<t.top&&n.push(h.z.top),e.bottom>t.bottom&&n.push(h.z.bottom),e.left<t.left&&n.push(h.z.left),e.right>t.right&&n.push(h.z.right),n}function x(e,t){return e[h.z[t]]}function E(e,t,n){return e[h.z[t]]=n,e}function I(e,t){var n=L(t);return(x(e,n.positiveEdge)+x(e,n.negativeEdge))/2}function w(e,t){return e>0?t:-1*t}function T(e,t){return w(e,x(t,e))}function M(e,t,n){return w(n,x(e,n)-x(t,n))}function D(e,t,n,o){void 0===o&&(o=!0);var r=x(e,t)-n,i=E(e,t,n);return o&&(i=E(e,-1*t,x(e,-1*t)-r)),i}function S(e,t,n,o){return void 0===o&&(o=0),D(e,n,x(t,n)+w(n,o))}function P(e,t,n){return T(n,e)>T(n,t)}function N(e,t){for(var n=0,o=0,r=k(e,t);o<r.length;o++){var i=r[o];n+=Math.pow(M(e,t,i),2)}return n}function R(e,t,n,o,r,i,a){void 0===r&&(r=0);var s=o.alignmentEdge,l=o.alignTargetEdge,u={elementRectangle:e,targetEdge:o.targetEdge,alignmentEdge:s};i||a||(u=function(e,t,n,o,r){void 0===r&&(r=0);var i=[h.z.left,h.z.right,h.z.bottom,h.z.top];(0,v.zg)()&&(i[0]*=-1,i[1]*=-1);for(var a,s=e,l=o.targetEdge,u=o.alignmentEdge,c=l,d=u,p=0;p<4;p++){if(P(s,n,l))return{elementRectangle:s,targetEdge:l,alignmentEdge:u};var m=N(s,n);(!a||m<a)&&(a=m,c=l,d=u),i.splice(i.indexOf(l),1),i.length>0&&(i.indexOf(-1*l)>-1?l*=-1:(u=l,l=i.slice(-1)[0]),s=O(e,t,{targetEdge:l,alignmentEdge:u},r))}return{elementRectangle:s=O(e,t,{targetEdge:c,alignmentEdge:d},r),targetEdge:c,alignmentEdge:d}}(e,t,n,o,r));var c=k(u.elementRectangle,n),d=i?-u.targetEdge:void 0;if(c.length>0)if(l)if(u.alignmentEdge&&c.indexOf(-1*u.alignmentEdge)>-1){var p=function(e,t,n,o){var r=e.alignmentEdge,i=e.targetEdge,a=-1*r;return{elementRectangle:O(e.elementRectangle,t,{targetEdge:i,alignmentEdge:a},n,o),targetEdge:i,alignmentEdge:a}}(u,t,r,a);if(C(p.elementRectangle,n))return p;u=B(k(p.elementRectangle,n),u,n,d)}else u=B(c,u,n,d);else u=B(c,u,n,d);return u}function B(e,t,n,o){for(var r=0,i=e;r<i.length;r++){var a=i[r],s=void 0;if(o&&o===-1*a)s=D(t.elementRectangle,a,x(n,a),!1),t.forcedInBounds=!0;else P(s=S(t.elementRectangle,n,a),n,-1*a)||(s=D(s,-1*a,x(n,-1*a),!1),t.forcedInBounds=!0);t.elementRectangle=s}return t}function F(e,t,n){var o=L(t).positiveEdge;return D(e,o,n-(I(e,t)-x(e,o)))}function O(e,t,n,o,r){void 0===o&&(o=0);var i=new b(e.left,e.right,e.top,e.bottom),a=n.alignmentEdge,s=n.targetEdge,l=r?s:-1*s;(i=r?S(i,t,s,o):function(e,t,n,o){void 0===o&&(o=0);var r=w(-1*n,o);return D(e,-1*n,x(t,n)+r)}(i,t,s,o),a)?i=S(i,t,a):i=F(i,l,I(t,s));return i}function L(e){return e===h.z.top||e===h.z.bottom?{positiveEdge:h.z.left,negativeEdge:h.z.right}:{positiveEdge:h.z.top,negativeEdge:h.z.bottom}}function A(e,t,n){return n&&Math.abs(M(e,n,t))>Math.abs(M(e,n,-1*t))?-1*t:t}function H(e,t,n,o,r,i,a,s){var l,u={},c=J(t),d=i?n:-1*n,p=r||L(n).positiveEdge;return a&&!function(e,t,n){return void 0!==n&&x(e,t)===x(n,t)}(e,(l=p,-1*l),o)||(p=A(e,p,o)),u[h.z[d]]=M(e,c,d),u[h.z[p]]=M(e,c,p),s&&(u[h.z[-1*d]]=M(e,c,-1*d),u[h.z[-1*p]]=M(e,c,-1*p)),u}function z(e){return Math.sqrt(e*e*2)}function W(e,t,n){if(void 0===e&&(e=s.b.bottomAutoEdge),n)return{alignmentEdge:n.alignmentEdge,isAuto:n.isAuto,targetEdge:n.targetEdge};var o=(0,r.pi)({},_[e]);return(0,v.zg)()?(o.alignmentEdge&&o.alignmentEdge%2===0&&(o.alignmentEdge=-1*o.alignmentEdge),void 0!==t?_[t]:o):o}function K(e,t,n){var o=I(t,e),r=I(n,e),i=L(e),a=i.positiveEdge,s=i.negativeEdge;return o<=r?a:s}function q(e,t,n,o,r,i,a){var s=O(e,t,o,r,a);return C(s,n)?{elementRectangle:s,targetEdge:o.targetEdge,alignmentEdge:o.alignmentEdge}:R(s,t,n,o,r,i,a)}function U(e,t,n){var o=-1*e.targetEdge,i=new b(0,e.elementRectangle.width,0,e.elementRectangle.height),a={},s=A(e.elementRectangle,e.alignmentEdge?e.alignmentEdge:L(o).positiveEdge,n),l=M(e.elementRectangle,e.targetRectangle,o)>Math.abs(x(t,o));return a[h.z[o]]=x(t,o),a[h.z[s]]=M(t,i,s),{elementPosition:(0,r.pi)({},a),closestEdge:K(e.targetEdge,t,i),targetEdge:o,hideBeak:!l}}function G(e,t){var n=t.targetRectangle,o=L(t.targetEdge),r=o.positiveEdge,i=o.negativeEdge,a=I(n,t.targetEdge),s=new b(e/2,t.elementRectangle.width-e/2,e/2,t.elementRectangle.height-e/2),l=new b(0,e,0,e);return P(l=F(l=D(l,-1*t.targetEdge,-e/2),-1*t.targetEdge,a-T(r,t.elementRectangle)),s,r)?P(l,s,i)||(l=S(l,s,i)):l=S(l,s,r),l}function J(e){var t=e.getBoundingClientRect();return new b(t.left,t.right,t.top,t.bottom)}function Z(e){return new b(e.left,e.right,e.top,e.bottom)}function j(e,t,n,o){var i=e.gapSpace?e.gapSpace:0,a=function(e,t){var n;if(t){if(t.preventDefault){var o=t;n=new b(o.clientX,o.clientX,o.clientY,o.clientY)}else if(t.getBoundingClientRect)n=J(t);else{var r=t,i=r.left||r.x,a=r.top||r.y,s=r.right||i,l=r.bottom||a;n=new b(i,s,a,l)}if(!C(n,e))for(var u=0,c=k(n,e);u<c.length;u++){var d=c[u];n[h.z[d]]=e[h.z[d]]}}else n=new b(0,0,0,0);return n}(n,e.target),s=function(e,t,n,o,r){return e.isAuto&&(e.alignmentEdge=K(e.targetEdge,t,n)),e.alignTargetEdge=r,e}(W(e.directionalHint,e.directionalHintForRTL,o),a,n,e.coverTarget,e.alignTargetEdge),l=q(J(t),a,n,s,i,e.directionalHintFixed,e.coverTarget);return(0,r.pi)((0,r.pi)({},l),{targetRectangle:a})}function Y(e,t,n,o,r){return{elementPosition:H(e.elementRectangle,t,e.targetEdge,n,e.alignmentEdge,o,r,e.forcedInBounds),targetEdge:e.targetEdge,alignmentEdge:e.alignmentEdge}}function V(e,t,n,o,i){var a=e.isBeakVisible&&e.beakWidth||0,s=z(a)/2+(e.gapSpace?e.gapSpace:0),l=e;l.gapSpace=s;var u=e.bounds?Z(e.bounds):new b(0,window.innerWidth-(0,g.np)(),0,window.innerHeight),c=j(l,n,u,o),d=U(c,G(a,c),u);return(0,r.pi)((0,r.pi)({},Y(c,t,u,e.coverTarget,i)),{beakPosition:d})}function $(e,t,n,o){return function(e,t,n,o){return V(e,t,n,o,!0)}(e,t,n,o)}var Q,X=n(68416),ee=n(79292),te=n(40528),ne=n(32092),oe=n(77769),re=n(32561),ie=n(66587),ae=n(42086),se=((Q={})[h.z.top]=te.k4.slideUpIn10,Q[h.z.bottom]=te.k4.slideDownIn10,Q[h.z.left]=te.k4.slideLeftIn10,Q[h.z.right]=te.k4.slideRightIn10,Q),le=0,ue=0,ce={opacity:0,filter:"opacity(0)",pointerEvents:"none"},de=["role","aria-roledescription"],pe={preventDismissOnLostFocus:!1,preventDismissOnScroll:!1,preventDismissOnResize:!1,isBeakVisible:!0,beakWidth:16,gapSpace:0,minPagePadding:8,directionalHint:s.b.bottomAutoEdge},me=(0,ee.y)({disableCaching:!0});function fe(e,t,n,o,a){var s=i.useState(),l=s[0],u=s[1],c=i.useRef(0),d=i.useRef(),p=(0,ne.r)(),m=e.hidden,f=e.target,h=e.finalHeight,v=e.calloutMaxHeight,g=e.onPositioned,b=e.directionalHint;return i.useEffect((function(){if(!m){var i=p.requestAnimationFrame((function(){var i,s,p,m;if(t.current&&n){var b=(0,r.pi)((0,r.pi)({},e),{target:o.current,bounds:a()}),y=n.cloneNode(!0);y.style.maxHeight=v?"".concat(v):"",y.style.visibility="hidden",null===(i=n.parentElement)||void 0===i||i.appendChild(y);var _=d.current===f?l:void 0,C=h?$(b,t.current,y,_):function(e,t,n,o){return V(e,t,n,o)}(b,t.current,y,_);null===(s=n.parentElement)||void 0===s||s.removeChild(y),!l&&C||l&&C&&(m=C,!ge((p=l).elementPosition,m.elementPosition)||!ge(p.beakPosition.elementPosition,m.beakPosition.elementPosition))&&c.current<5?(c.current++,u(C)):c.current>0&&(c.current=0,null===g||void 0===g||g(l))}}),n);return d.current=f,function(){p.cancelAnimationFrame(i),d.current=void 0}}u(void 0),c.current=0}),[m,b,p,n,v,t,o,h,a,g,l,e,f]),l}var he=i.memo(i.forwardRef((function(e,t){var n=(0,d.j)(pe,e),o=n.styles,a=n.style,s=n.ariaLabel,f=n.ariaDescribedBy,h=n.ariaLabelledBy,v=n.className,g=n.isBeakVisible,b=n.children,y=n.beakWidth,_=n.calloutWidth,C=n.calloutMaxWidth,k=n.calloutMinWidth,x=n.doNotLayer,E=n.finalHeight,I=n.hideOverflow,w=void 0===I?!!E:I,T=n.backgroundColor,M=n.calloutMaxHeight,D=n.onScroll,S=n.shouldRestoreFocus,P=void 0===S||S,N=n.target,R=n.hidden,B=n.onLayerMounted,F=n.popupProps,O=i.useRef(null),L=i.useState(null),A=L[0],H=L[1],z=i.useCallback((function(e){H(e)}),[]),W=(0,ie.r)(O,t),K=(0,ae.e)(n.target,{current:A}),q=K[0],U=K[1],G=function(e,t,n){var o=e.bounds,r=e.minPagePadding,a=void 0===r?pe.minPagePadding:r,s=e.target,l=i.useState(!1),u=l[0],c=l[1],d=i.useRef(),p=i.useCallback((function(){if(!d.current||u){var e="function"===typeof o?n?o(s,n):void 0:o;!e&&n&&(e=function(e,t){return function(e,t){var n=void 0;if(t.getWindowSegments&&(n=t.getWindowSegments()),void 0===n||n.length<=1)return{top:0,left:0,right:t.innerWidth,bottom:t.innerHeight,width:t.innerWidth,height:t.innerHeight};var o=0,r=0;if(null!==e&&e.getBoundingClientRect){var i=e.getBoundingClientRect();o=(i.left+i.right)/2,r=(i.top+i.bottom)/2}else null!==e&&(o=e.left||e.x,r=e.top||e.y);for(var a={top:0,left:0,right:0,bottom:0,width:0,height:0},s=0,l=n;s<l.length;s++){var u=l[s];o&&u.left<=o&&u.right>=o&&r&&u.top<=r&&u.bottom>=r&&(a={top:u.top,left:u.left,right:u.right,bottom:u.bottom,width:u.width,height:u.height})}return a}(e,t)}(t.current,n),e={top:e.top+a,left:e.left+a,right:e.right-a,bottom:e.bottom-a,width:e.width-2*a,height:e.height-2*a}),d.current=e,u&&c(!1)}return d.current}),[o,a,s,t,n,u]),m=(0,ne.r)();return(0,oe.d)(n,"resize",m.debounce((function(){c(!0)}),500,{leading:!0})),p}(n,q,U),J=fe(n,O,A,q,G),Z=function(e,t,n){var o,r=e.calloutMaxHeight,a=e.finalHeight,s=e.directionalHint,l=e.directionalHintFixed,u=e.hidden,c=i.useState(),d=c[0],p=c[1],m=null!==(o=null===n||void 0===n?void 0:n.elementPosition)&&void 0!==o?o:{},f=m.top,h=m.bottom;return i.useEffect((function(){var e,n,o=null!==(e=t())&&void 0!==e?e:{},i=o.top,a=o.bottom;"number"===typeof f&&a?n=a-f:"number"===typeof h&&"number"===typeof i&&a&&(n=a-i-h),p(!r&&!u||r&&n&&r>n?n:r||void 0)}),[h,r,a,s,l,t,u,n,f]),d}(n,G,J),j=function(e,t,n,o,r){var a=e.hidden,s=e.onDismiss,l=e.preventDismissOnScroll,d=e.preventDismissOnResize,p=e.preventDismissOnLostFocus,m=e.dismissOnTargetClick,f=e.shouldDismissOnWindowFocus,h=e.preventDismissOnEvent,v=i.useRef(!1),g=(0,ne.r)(),b=(0,re.B)([function(){v.current=!0},function(){v.current=!1}]),y=!!t;return i.useEffect((function(){var e=function(e){y&&!l&&b(e)},t=function(e){d||h&&h(e)||null===s||void 0===s||s(e)},i=function(e){p||b(e)},b=function(e){var t=e.composedPath?e.composedPath():[],i=t.length>0?t[0]:e.target,a=n.current&&!(0,u.t)(n.current,i);if(a&&v.current)v.current=!1;else if(!o.current&&a||e.target!==r&&a&&(!o.current||"stopPropagation"in o.current||m||i!==o.current&&!(0,u.t)(o.current,i))){if(h&&h(e))return;null===s||void 0===s||s(e)}},_=function(e){f&&((!h||h(e))&&(h||p)||(null===r||void 0===r?void 0:r.document.hasFocus())||null!==e.relatedTarget||null===s||void 0===s||s(e))},C=new Promise((function(n){g.setTimeout((function(){if(!a&&r){var o=[(0,c.on)(r,"scroll",e,!0),(0,c.on)(r,"resize",t,!0),(0,c.on)(r.document.documentElement,"focus",i,!0),(0,c.on)(r.document.documentElement,"click",i,!0),(0,c.on)(r,"blur",_,!0)];n((function(){o.forEach((function(e){return e()}))}))}}),0)}));return function(){C.then((function(e){return e()}))}}),[a,g,n,o,r,s,f,m,p,d,l,y,h]),b}(n,J,O,q,U),Y=j[0],V=j[1],$=(null===J||void 0===J?void 0:J.elementPosition.top)&&(null===J||void 0===J?void 0:J.elementPosition.bottom),Q=(0,r.pi)((0,r.pi)({},null===J||void 0===J?void 0:J.elementPosition),{maxHeight:Z});if($&&(Q.bottom=void 0),function(e,t,n){var o=e.hidden,r=e.setInitialFocus,a=(0,ne.r)(),s=!!t;i.useEffect((function(){if(!o&&r&&s&&n){var e=a.requestAnimationFrame((function(){return(0,l.uo)(n)}),n);return function(){return a.cancelAnimationFrame(e)}}}),[o,s,a,n,r])}(n,J,A),i.useEffect((function(){R||null===B||void 0===B||B()}),[R]),!U)return null;var ee=w,te=g&&!!N,le=me(o,{theme:n.theme,className:v,overflowYHidden:ee,calloutWidth:_,positions:J,beakWidth:y,backgroundColor:T,calloutMaxWidth:C,calloutMinWidth:k,doNotLayer:x}),ue=(0,r.pi)((0,r.pi)({maxHeight:M||"100%"},a),ee&&{overflowY:"hidden"}),he=n.hidden?{visibility:"hidden"}:void 0;return i.createElement("div",{ref:W,className:le.container,style:he},i.createElement("div",(0,r.pi)({},(0,p.pq)(n,p.n7,de),{className:(0,m.i)(le.root,J&&J.targetEdge&&se[J.targetEdge]),style:J?(0,r.pi)({},Q):ce,tabIndex:-1,ref:z}),te&&i.createElement("div",{className:le.beak,style:ve(J)}),te&&i.createElement("div",{className:le.beakCurtain}),i.createElement(X.G,(0,r.pi)({role:n.role,"aria-roledescription":n["aria-roledescription"],ariaDescribedBy:f,ariaLabel:s,ariaLabelledBy:h,className:le.calloutMain,onDismiss:n.onDismiss,onMouseDown:Y,onMouseUp:V,onRestoreFocus:n.onRestoreFocus,onScroll:D,shouldRestoreFocus:P,style:ue},F),b)))})),(function(e,t){return!(t.shouldUpdateWhenHidden||!e.hidden||!t.hidden)||(0,f.Vv)(e,t)}));function ve(e){var t,n,o=(0,r.pi)((0,r.pi)({},null===(t=null===e||void 0===e?void 0:e.beakPosition)||void 0===t?void 0:t.elementPosition),{display:(null===(n=null===e||void 0===e?void 0:e.beakPosition)||void 0===n?void 0:n.hideBeak)?"none":void 0});return o.top||o.bottom||o.left||o.right||(o.left=ue,o.top=le),o}function ge(e,t){for(var n in t)if(t.hasOwnProperty(n)){var o=e[n],r=t[n];if(void 0===o||void 0===r)return!1;if(o.toFixed(2)!==r.toFixed(2))return!1}return!0}function be(e){return{height:e,width:e}}he.displayName="CalloutContentBase";var ye={container:"ms-Callout-container",root:"ms-Callout",beak:"ms-Callout-beak",beakCurtain:"ms-Callout-beakCurtain",calloutMain:"ms-Callout-main"},_e=(0,a.z)(he,(function(e){var t,n=e.theme,o=e.className,r=e.overflowYHidden,i=e.calloutWidth,a=e.beakWidth,s=e.backgroundColor,l=e.calloutMaxWidth,u=e.calloutMinWidth,c=e.doNotLayer,d=(0,te.Cn)(ye,n),p=n.semanticColors,m=n.effects;return{container:[d.container,{position:"relative"}],root:[d.root,n.fonts.medium,{position:"absolute",display:"flex",zIndex:c?te.bR.Layer:void 0,boxSizing:"border-box",borderRadius:m.roundedCorner2,boxShadow:m.elevation16,selectors:(t={},t[te.qJ]={borderWidth:1,borderStyle:"solid",borderColor:"WindowText"},t)},(0,te.e2)(),o,!!i&&{width:i},!!l&&{maxWidth:l},!!u&&{minWidth:u}],beak:[d.beak,{position:"absolute",backgroundColor:p.menuBackground,boxShadow:"inherit",border:"inherit",boxSizing:"border-box",transform:"rotate(45deg)"},be(a),s&&{backgroundColor:s}],beakCurtain:[d.beakCurtain,{position:"absolute",top:0,right:0,bottom:0,left:0,backgroundColor:p.menuBackground,borderRadius:m.roundedCorner2}],calloutMain:[d.calloutMain,{backgroundColor:p.menuBackground,overflowX:"hidden",overflowY:"auto",position:"relative",width:"100%",borderRadius:m.roundedCorner2},r&&{overflowY:"hidden"},s&&{backgroundColor:s}]}}),void 0,{scope:"CalloutContent"}),Ce=n(25443),ke=i.forwardRef((function(e,t){var n=e.layerProps,o=e.doNotLayer,a=(0,r._T)(e,["layerProps","doNotLayer"]),s=i.createElement(_e,(0,r.pi)({},a,{doNotLayer:o,ref:t}));return o?s:i.createElement(Ce.m,(0,r.pi)({},n),s)}));ke.displayName="Callout"},56756:function(e,t,n){n.d(t,{L:function(){return Ze}});var o,r=n(40433),i=n(75971),a=n(72791),s=n(79292),l=n(63039),u=n(13223),c=n(29723),d=n(16455),p=n(9691),m=n(56500),f=n(8877),h=n(91106),v=n(15863),g=n(29109),b=n(90101),y=n(86749),_=n(87023),C=n(81080),k=n(36190);!function(e){e[e.Normal=0]="Normal",e[e.Divider=1]="Divider",e[e.Header=2]="Header",e[e.SelectAll=3]="SelectAll"}(o||(o={}));var x,E=function(){function e(){this._size=0}return e.prototype.updateOptions=function(e){for(var t=[],n=[],r=0,a=0;a<e.length;a++){var s=e[a],l=s.itemType,u=s.hidden;l===o.Divider||l===o.Header?(t.push(a),n.push(a)):u?n.push(a):r++}this._size=r,this._displayOnlyOptionsCache=t,this._notSelectableOptionsCache=n,this._cachedOptions=(0,i.ev)([],e,!0)},Object.defineProperty(e.prototype,"optionSetSize",{get:function(){return this._size},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cachedOptions",{get:function(){return this._cachedOptions},enumerable:!1,configurable:!0}),e.prototype.positionInSet=function(e){if(void 0!==e){for(var t=0;e>this._notSelectableOptionsCache[t];)t++;if(this._displayOnlyOptionsCache[t]===e)throw new Error("Unexpected: Option at index ".concat(e," is not a selectable element."));if(this._notSelectableOptionsCache[t]!==e)return e-t+1}},e}(),I=n(65260),w=n(78498),T=n(18511),M=n(63934),D=n(29093),S=n(25443),P=n(69894),N=n(68416),R=n(71675),B=n(23983),F=n(68959),O=n(92853),L=n(87376),A=n(84688),H=n(10892);!function(e){e[e.smallFluid=0]="smallFluid",e[e.smallFixedFar=1]="smallFixedFar",e[e.smallFixedNear=2]="smallFixedNear",e[e.medium=3]="medium",e[e.large=4]="large",e[e.largeFixed=5]="largeFixed",e[e.extraLarge=6]="extraLarge",e[e.custom=7]="custom",e[e.customNear=8]="customNear"}(x||(x={}));var z,W=(0,s.y)();!function(e){e[e.closed=0]="closed",e[e.animatingOpen=1]="animatingOpen",e[e.open=2]="open",e[e.animatingClosed=3]="animatingClosed"}(z||(z={}));var K,q,U,G,J,Z=function(e){function t(t){var n=e.call(this,t)||this;n._panel=a.createRef(),n._animationCallback=null,n._hasCustomNavigation=!(!n.props.onRenderNavigation&&!n.props.onRenderNavigationContent),n.dismiss=function(e){n.props.onDismiss&&n.isActive&&n.props.onDismiss(e),(!e||e&&!e.defaultPrevented)&&n.close()},n._allowScrollOnPanel=function(e){e?n._allowTouchBodyScroll?(0,R.eC)(e,n._events):(0,R.C7)(e,n._events):n._events.off(n._scrollableContent),n._scrollableContent=e},n._onRenderNavigation=function(e){if(!n.props.onRenderNavigationContent&&!n.props.onRenderNavigation&&!n.props.hasCloseButton)return null;var t=n.props.onRenderNavigationContent,o=void 0===t?n._onRenderNavigationContent:t;return a.createElement("div",{className:n._classNames.navigation},o(e,n._onRenderNavigationContent))},n._onRenderNavigationContent=function(e){var t,o=e.closeButtonAriaLabel,r=e.hasCloseButton,i=e.onRenderHeader,s=void 0===i?n._onRenderHeader:i;if(r){var l=null===(t=n._classNames.subComponentStyles)||void 0===t?void 0:t.closeButton();return a.createElement(a.Fragment,null,!n._hasCustomNavigation&&s(n.props,n._onRenderHeader,n._headerTextId),a.createElement(D.h,{styles:l,className:n._classNames.closeButton,onClick:n._onPanelClick,ariaLabel:o,title:o,"data-is-visible":!0,iconProps:{iconName:"Cancel"}}))}return null},n._onRenderHeader=function(e,t,o){var r=e.headerText,s=e.headerTextProps,l=void 0===s?{}:s;return r?a.createElement("div",{className:n._classNames.header},a.createElement("div",(0,i.pi)({id:o,role:"heading","aria-level":1},l,{className:(0,c.i)(n._classNames.headerText,l.className)}),r)):null},n._onRenderBody=function(e){return a.createElement("div",{className:n._classNames.content},e.children)},n._onRenderFooter=function(e){var t=n.props.onRenderFooterContent,o=void 0===t?null:t;return o?a.createElement("div",{className:n._classNames.footer},a.createElement("div",{className:n._classNames.footerInner},o())):null},n._animateTo=function(e){e===z.open&&n.props.onOpen&&n.props.onOpen(),n._animationCallback=n._async.setTimeout((function(){n.setState({visibility:e}),n._onTransitionComplete(e)}),200)},n._clearExistingAnimationTimer=function(){null!==n._animationCallback&&n._async.clearTimeout(n._animationCallback)},n._onPanelClick=function(e){n.dismiss(e)},n._onTransitionComplete=function(e){n._updateFooterPosition(),e===z.open&&n.props.onOpened&&n.props.onOpened(),e===z.closed&&n.props.onDismissed&&n.props.onDismissed()};var o=n.props.allowTouchBodyScroll,r=void 0!==o&&o;return n._allowTouchBodyScroll=r,(0,f.l)(n),(0,B.b)("Panel",t,{ignoreExternalFocusing:"focusTrapZoneProps",forceFocusInsideTrap:"focusTrapZoneProps",firstFocusableSelector:"focusTrapZoneProps"}),n.state={isFooterSticky:!1,visibility:z.closed,id:(0,h.z)("Panel")},n}return(0,i.ZT)(t,e),t.getDerivedStateFromProps=function(e,t){return void 0===e.isOpen?null:!e.isOpen||t.visibility!==z.closed&&t.visibility!==z.animatingClosed?e.isOpen||t.visibility!==z.open&&t.visibility!==z.animatingOpen?null:{visibility:z.animatingClosed}:{visibility:z.animatingOpen}},t.prototype.componentDidMount=function(){this._async=new F.e(this),this._events=new O.r(this),this._events.on(window,"resize",this._updateFooterPosition),this._shouldListenForOuterClick(this.props)&&this._events.on(document.body,"mousedown",this._dismissOnOuterClick,!0),this.props.isOpen&&this.setState({visibility:z.animatingOpen})},t.prototype.componentDidUpdate=function(e,t){var n=this._shouldListenForOuterClick(this.props),o=this._shouldListenForOuterClick(e);this.state.visibility!==t.visibility&&(this._clearExistingAnimationTimer(),this.state.visibility===z.animatingOpen?this._animateTo(z.open):this.state.visibility===z.animatingClosed&&this._animateTo(z.closed)),n&&!o?this._events.on(document.body,"mousedown",this._dismissOnOuterClick,!0):!n&&o&&this._events.off(document.body,"mousedown",this._dismissOnOuterClick,!0)},t.prototype.componentWillUnmount=function(){this._async.dispose(),this._events.dispose()},t.prototype.render=function(){var e=this.props,t=e.className,n=void 0===t?"":t,o=e.elementToFocusOnDismiss,r=e.firstFocusableSelector,s=e.focusTrapZoneProps,l=e.forceFocusInsideTrap,u=e.hasCloseButton,c=e.headerText,d=e.headerClassName,p=void 0===d?"":d,m=e.ignoreExternalFocusing,f=e.isBlocking,h=e.isFooterAtBottom,g=e.isLightDismiss,b=e.isHiddenOnDismiss,y=e.layerProps,_=e.overlayProps,C=e.popupProps,k=e.type,E=e.styles,I=e.theme,w=e.customWidth,T=e.onLightDismissClick,M=void 0===T?this._onPanelClick:T,D=e.onRenderNavigation,R=void 0===D?this._onRenderNavigation:D,B=e.onRenderHeader,F=void 0===B?this._onRenderHeader:B,O=e.onRenderBody,A=void 0===O?this._onRenderBody:O,K=e.onRenderFooter,q=void 0===K?this._onRenderFooter:K,U=this.state,G=U.isFooterSticky,J=U.visibility,Z=U.id,j=k===x.smallFixedNear||k===x.customNear,Y=(0,L.zg)(I)?j:!j,V=k===x.custom||k===x.customNear?{width:w}:{},$=(0,v.pq)(this.props,v.n7),Q=this.isActive,X=J===z.animatingClosed||J===z.animatingOpen;if(this._headerTextId=c&&Z+"-headerText",!Q&&!X&&!b)return null;this._classNames=W(E,{theme:I,className:n,focusTrapZoneClassName:s?s.className:void 0,hasCloseButton:u,headerClassName:p,isAnimating:X,isFooterSticky:G,isFooterAtBottom:h,isOnRightSide:Y,isOpen:Q,isHiddenOnDismiss:b,type:k,hasCustomNavigation:this._hasCustomNavigation});var ee,te=this._classNames,ne=this._allowTouchBodyScroll;return f&&Q&&(ee=a.createElement(P.a,(0,i.pi)({className:te.overlay,isDarkThemed:!1,onClick:g?M:void 0,allowTouchBodyScroll:ne},_))),a.createElement(S.m,(0,i.pi)({},y),a.createElement(N.G,(0,i.pi)({role:"dialog","aria-modal":f?"true":void 0,ariaLabelledBy:this._headerTextId?this._headerTextId:void 0,onDismiss:this.dismiss,className:te.hiddenPanel,enableAriaHiddenSiblings:!!Q},C),a.createElement("div",(0,i.pi)({"aria-hidden":!Q&&X},$,{ref:this._panel,className:te.root}),ee,a.createElement(H.P,(0,i.pi)({ignoreExternalFocusing:m,forceFocusInsideTrap:!(!f||b&&!Q)&&l,firstFocusableSelector:r,isClickableOutsideFocusTrap:!0},s,{className:te.main,style:V,elementToFocusOnDismiss:o}),a.createElement("div",{className:te.contentInner},a.createElement("div",{ref:this._allowScrollOnPanel,className:te.scrollableContent,"data-is-scrollable":!0},a.createElement("div",{className:te.commands,"data-is-visible":!0},R(this.props,this._onRenderNavigation)),(this._hasCustomNavigation||!u)&&F(this.props,this._onRenderHeader,this._headerTextId),A(this.props,this._onRenderBody),q(this.props,this._onRenderFooter)))))))},t.prototype.open=function(){void 0===this.props.isOpen&&(this.isActive||this.setState({visibility:z.animatingOpen}))},t.prototype.close=function(){void 0===this.props.isOpen&&this.isActive&&this.setState({visibility:z.animatingClosed})},Object.defineProperty(t.prototype,"isActive",{get:function(){return this.state.visibility===z.open||this.state.visibility===z.animatingOpen},enumerable:!1,configurable:!0}),t.prototype._shouldListenForOuterClick=function(e){return!!e.isBlocking&&!!e.isOpen},t.prototype._updateFooterPosition=function(){var e=this._scrollableContent;if(e){var t=e.clientHeight,n=e.scrollHeight;this.setState({isFooterSticky:t<n})}},t.prototype._dismissOnOuterClick=function(e){var t=this._panel.current;this.isActive&&t&&!e.defaultPrevented&&((0,A.t)(t,e.target)||(this.props.onOuterClick?this.props.onOuterClick(e):this.dismiss(e)))},t.defaultProps={isHiddenOnDismiss:!1,isOpen:void 0,isBlocking:!0,hasCloseButton:!0,type:x.smallFixedFar},t}(a.Component),j=n(40528),Y={root:"ms-Panel",main:"ms-Panel-main",commands:"ms-Panel-commands",contentInner:"ms-Panel-contentInner",scrollableContent:"ms-Panel-scrollableContent",navigation:"ms-Panel-navigation",closeButton:"ms-Panel-closeButton ms-PanelAction-close",header:"ms-Panel-header",headerText:"ms-Panel-headerText",content:"ms-Panel-content",footer:"ms-Panel-footer",footerInner:"ms-Panel-footerInner",isOpen:"is-open",hasCloseButton:"ms-Panel--hasCloseButton",smallFluid:"ms-Panel--smFluid",smallFixedNear:"ms-Panel--smLeft",smallFixedFar:"ms-Panel--sm",medium:"ms-Panel--md",large:"ms-Panel--lg",largeFixed:"ms-Panel--fixed",extraLarge:"ms-Panel--xl",custom:"ms-Panel--custom",customNear:"ms-Panel--customLeft"},V="100%",$="auto",Q=272,X=340,ee=592,te=644,ne=940,oe="auto",re=0,ie=48,ae=428,se=176,le=((K={})["@media (min-width: ".concat(j.dd,"px)")]={width:X},K),ue=((q={})["@media (min-width: ".concat(j.AV,"px)")]={width:ee},q["@media (min-width: ".concat(j.qv,"px)")]={width:te},q),ce=((U={})["@media (min-width: ".concat(j.bE,"px)")]={left:ie,width:$},U["@media (min-width: ".concat(j.B,"px)")]={left:ae},U),de=((G={})["@media (min-width: ".concat(j.B,"px)")]={left:oe,width:ne},G),pe=((J={})["@media (min-width: ".concat(j.B,"px)")]={left:se},J),me=function(e){var t;switch(e){case x.smallFixedFar:t=(0,i.pi)({},le);break;case x.medium:t=(0,i.pi)((0,i.pi)({},le),ue);break;case x.large:t=(0,i.pi)((0,i.pi)((0,i.pi)({},le),ue),ce);break;case x.largeFixed:t=(0,i.pi)((0,i.pi)((0,i.pi)((0,i.pi)({},le),ue),ce),de);break;case x.extraLarge:t=(0,i.pi)((0,i.pi)((0,i.pi)((0,i.pi)({},le),ue),ce),pe)}return t},fe={paddingLeft:"24px",paddingRight:"24px"},he=(0,r.z)(Z,(function(e){var t,n,o,r,a=e.className,s=e.focusTrapZoneClassName,l=e.hasCloseButton,u=e.headerClassName,c=e.isAnimating,d=e.isFooterSticky,p=e.isFooterAtBottom,m=e.isOnRightSide,f=e.isOpen,h=e.isHiddenOnDismiss,v=e.hasCustomNavigation,g=e.theme,b=e.type,y=void 0===b?x.smallFixedFar:b,_=g.effects,C=g.fonts,k=g.semanticColors,E=(0,j.Cn)(Y,g),I=y===x.custom||y===x.customNear;return{root:[E.root,g.fonts.medium,f&&E.isOpen,l&&E.hasCloseButton,{pointerEvents:"none",position:"absolute",top:0,left:0,right:0,bottom:0},I&&m&&E.custom,I&&!m&&E.customNear,a],overlay:[{pointerEvents:"auto",cursor:"pointer"},f&&c&&j.k4.fadeIn100,!f&&c&&j.k4.fadeOut100],hiddenPanel:[!f&&!c&&h&&{visibility:"hidden"}],main:[E.main,{backgroundColor:k.bodyBackground,boxShadow:_.elevation64,pointerEvents:"auto",position:"absolute",display:"flex",flexDirection:"column",overflowX:"hidden",overflowY:"auto",WebkitOverflowScrolling:"touch",bottom:0,top:0,left:oe,right:re,width:V,selectors:(0,i.pi)((t={},t[j.qJ]={borderLeft:"3px solid ".concat(k.variantBorder),borderRight:"3px solid ".concat(k.variantBorder)},t),me(y))},y===x.smallFluid&&{left:re},y===x.smallFixedNear&&{left:re,right:oe,width:Q},y===x.customNear&&{right:"auto",left:0},I&&{maxWidth:"100vw"},f&&c&&!m&&j.k4.slideRightIn40,f&&c&&m&&j.k4.slideLeftIn40,!f&&c&&!m&&j.k4.slideLeftOut40,!f&&c&&m&&j.k4.slideRightOut40,s],commands:[E.commands,{backgroundColor:k.bodyBackground,paddingTop:18,selectors:(n={},n["@media (min-height: ".concat(j.dd,"px)")]={position:"sticky",top:0,zIndex:1},n)},v&&{paddingTop:"inherit"}],navigation:[E.navigation,{display:"flex",justifyContent:"flex-end"},v&&{height:"44px"}],contentInner:[E.contentInner,{display:"flex",flexDirection:"column",flexGrow:1,overflowY:"hidden"}],header:[E.header,fe,{alignSelf:"flex-start"},l&&!v&&{flexGrow:1},v&&{flexShrink:0}],headerText:[E.headerText,C.xLarge,{color:k.bodyText,lineHeight:"27px",overflowWrap:"break-word",wordWrap:"break-word",wordBreak:"break-word",hyphens:"auto"},u],scrollableContent:[E.scrollableContent,{overflowY:"auto"},p&&{flexGrow:1,display:"inherit",flexDirection:"inherit"}],content:[E.content,fe,{paddingBottom:20},p&&{selectors:(o={},o["@media (min-height: ".concat(j.dd,"px)")]={flexGrow:1},o)}],footer:[E.footer,{flexShrink:0,borderTop:"1px solid transparent",transition:"opacity ".concat(j.D1.durationValue3," ").concat(j.D1.easeFunction2),selectors:(r={},r["@media (min-height: ".concat(j.dd,"px)")]={position:"sticky",bottom:0},r)},d&&{backgroundColor:k.bodyBackground,borderTopColor:k.variantBorder}],footerInner:[E.footerInner,fe,{paddingBottom:16,paddingTop:16}],subComponentStyles:{closeButton:{root:[E.closeButton,{marginRight:14,color:g.palette.neutralSecondary,fontSize:j.ld.large},v&&{marginRight:0,height:"auto",width:"44px"}],rootHovered:{color:g.palette.neutralPrimary}}}}}),void 0,{scope:"Panel"}),ve=n(98687),ge=n(91381);function be(e,t){for(var n=[],o=0,r=t;o<r.length;o++){var i=e[r[o]];i&&n.push(i)}return n}var ye=n(24994),_e=n(66587),Ce=n(53193),ke=n(96971),xe=(0,s.y)(),Ee=a.forwardRef((function(e,t){var n=e.disabled,o=e.required,r=e.inputProps,s=e.name,l=e.ariaLabel,u=e.ariaLabelledBy,c=e.ariaDescribedBy,d=e.ariaPositionInSet,p=e.ariaSetSize,m=e.title,f=e.checkmarkIconProps,h=e.styles,v=e.theme,g=e.className,b=e.boxSide,y=void 0===b?"start":b,_=(0,ye.M)("checkbox-",e.id),C=a.useRef(null),k=(0,_e.r)(C,t),x=a.useRef(null),E=(0,Ce.G)(e.checked,e.defaultChecked,e.onChange),I=E[0],w=E[1],M=(0,Ce.G)(e.indeterminate,e.defaultIndeterminate),D=M[0],S=M[1];(0,ke.Pr)(C);var P=xe(h,{theme:v,className:g,disabled:n,indeterminate:D,checked:I,reversed:"start"!==y,isUsingCustomLabelRender:!!e.onRenderLabel}),N=a.useCallback((function(e){D?(w(!!I,e),S(!1)):w(!I,e)}),[w,S,D,I]),R=a.useCallback((function(e){return e&&e.label?a.createElement("span",{className:P.text,title:e.title},e.label):null}),[P.text]),B=a.useCallback((function(e){if(x.current){var t=!!e;x.current.indeterminate=t,S(t)}}),[S]);!function(e,t,n,o,r){a.useImperativeHandle(e.componentRef,(function(){return{get checked(){return!!t},get indeterminate(){return!!n},set indeterminate(e){o(e)},focus:function(){r.current&&r.current.focus()}}}),[r,t,n,o])}(e,I,D,B,x),a.useEffect((function(){return B(D)}),[B,D]);var F=e.onRenderLabel||R,O=D?"mixed":void 0,L=(0,i.pi)((0,i.pi)({className:P.input,type:"checkbox"},r),{checked:!!I,disabled:n,required:o,name:s,id:_,title:m,onChange:N,"aria-disabled":n,"aria-label":l,"aria-labelledby":u,"aria-describedby":c,"aria-posinset":d,"aria-setsize":p,"aria-checked":O});return a.createElement("div",{className:P.root,title:m,ref:k},a.createElement("input",(0,i.pi)({},L,{ref:x,title:m,"data-ktp-execute-target":!0})),a.createElement("label",{className:P.label,htmlFor:_},a.createElement("div",{className:P.checkbox,"data-ktp-target":!0},a.createElement(T.J,(0,i.pi)({iconName:"CheckMark"},f,{className:P.checkmark}))),F(e,R)))}));Ee.displayName="CheckboxBase";var Ie=n(61910),we={root:"ms-Checkbox",label:"ms-Checkbox-label",checkbox:"ms-Checkbox-checkbox",checkmark:"ms-Checkbox-checkmark",text:"ms-Checkbox-text"},Te="20px",Me="200ms",De="cubic-bezier(.4, 0, .23, 1)",Se=(0,r.z)(Ee,(function(e){var t,n,o,r,a,s,l,u,c,d,p,m,f,h,v,g,b,y,_=e.className,C=e.theme,k=e.reversed,x=e.checked,E=e.disabled,I=e.isUsingCustomLabelRender,w=e.indeterminate,T=C.semanticColors,M=C.effects,D=C.palette,S=C.fonts,P=(0,j.Cn)(we,C),N=T.inputForegroundChecked,R=D.neutralSecondary,B=D.neutralPrimary,F=T.inputBackgroundChecked,O=T.inputBackgroundChecked,L=T.disabledBodySubtext,A=T.inputBorderHovered,H=T.inputBackgroundCheckedHovered,z=T.inputBackgroundChecked,W=T.inputBackgroundCheckedHovered,K=T.inputBackgroundCheckedHovered,q=T.inputTextHovered,U=T.disabledBodySubtext,G=T.bodyText,J=T.disabledText,Z=[(t={content:'""',borderRadius:M.roundedCorner2,position:"absolute",width:10,height:10,top:4,left:4,boxSizing:"border-box",borderWidth:5,borderStyle:"solid",borderColor:E?L:F,transitionProperty:"border-width, border, border-color",transitionDuration:Me,transitionTimingFunction:De},t[j.qJ]={borderColor:"WindowText"},t)];return{root:[P.root,{position:"relative",display:"flex"},k&&"reversed",x&&"is-checked",!E&&"is-enabled",E&&"is-disabled",!E&&[!x&&(n={},n[":hover .".concat(P.checkbox)]=(o={borderColor:A},o[j.qJ]={borderColor:"Highlight"},o),n[":focus .".concat(P.checkbox)]={borderColor:A},n[":hover .".concat(P.checkmark)]=(r={color:R,opacity:"1"},r[j.qJ]={color:"Highlight"},r),n),x&&!w&&(a={},a[":hover .".concat(P.checkbox)]={background:W,borderColor:K},a[":focus .".concat(P.checkbox)]={background:W,borderColor:K},a[j.qJ]=(s={},s[":hover .".concat(P.checkbox)]={background:"Highlight",borderColor:"Highlight"},s[":focus .".concat(P.checkbox)]={background:"Highlight"},s[":focus:hover .".concat(P.checkbox)]={background:"Highlight"},s[":focus:hover .".concat(P.checkmark)]={color:"Window"},s[":hover .".concat(P.checkmark)]={color:"Window"},s),a),w&&(l={},l[":hover .".concat(P.checkbox,", :hover .").concat(P.checkbox,":after")]=(u={borderColor:H},u[j.qJ]={borderColor:"WindowText"},u),l[":focus .".concat(P.checkbox)]={borderColor:H},l[":hover .".concat(P.checkmark)]={opacity:"0"},l),(c={},c[":hover .".concat(P.text,", :focus .").concat(P.text)]=(d={color:q},d[j.qJ]={color:E?"GrayText":"WindowText"},d),c)],_],input:(p={position:"absolute",background:"none",opacity:0},p[".".concat(Ie.G$," &:focus + label::before")]=(m={outline:"1px solid "+C.palette.neutralSecondary,outlineOffset:"2px"},m[j.qJ]={outline:"1px solid WindowText"},m),p),label:[P.label,C.fonts.medium,{display:"flex",alignItems:I?"center":"flex-start",cursor:E?"default":"pointer",position:"relative",userSelect:"none"},k&&{flexDirection:"row-reverse",justifyContent:"flex-end"},{"&::before":{position:"absolute",left:0,right:0,top:0,bottom:0,content:'""',pointerEvents:"none"}}],checkbox:[P.checkbox,(f={position:"relative",display:"flex",flexShrink:0,alignItems:"center",justifyContent:"center",height:Te,width:Te,border:"1px solid ".concat(B),borderRadius:M.roundedCorner2,boxSizing:"border-box",transitionProperty:"background, border, border-color",transitionDuration:Me,transitionTimingFunction:De,overflow:"hidden",":after":w?Z:null},f[j.qJ]=(0,i.pi)({borderColor:"WindowText"},(0,j.xM)()),f),w&&{borderColor:F},k?{marginLeft:4}:{marginRight:4},!E&&!w&&x&&(h={background:z,borderColor:O},h[j.qJ]={background:"Highlight",borderColor:"Highlight"},h),E&&(v={borderColor:L},v[j.qJ]={borderColor:"GrayText"},v),x&&E&&(g={background:U,borderColor:L},g[j.qJ]={background:"Window"},g)],checkmark:[P.checkmark,(b={opacity:x&&!w?"1":"0",color:N},b[j.qJ]=(0,i.pi)({color:E?"GrayText":"Window"},(0,j.xM)()),b)],text:[P.text,(y={color:E?J:G,fontSize:S.medium.fontSize,lineHeight:"20px"},y[j.qJ]=(0,i.pi)({color:E?"GrayText":"WindowText"},(0,j.xM)()),y),k?{marginRight:4}:{marginLeft:4}]}}),void 0,{scope:"Checkbox"}),Pe=n(97471),Ne=n(1604),Re=(0,s.y)(),Be={options:[]};var Fe=a.forwardRef((function(e,t){var n=(0,Pe.j)(Be,e),o=a.useRef(null),r=(0,_e.r)(t,o),s=(0,ve.q)(o,n.responsiveMode),u=function(e){var t,n=e.defaultSelectedKeys,o=e.selectedKeys,r=e.defaultSelectedKey,i=e.selectedKey,s=e.options,u=e.multiSelect,c=(0,Ne.D)(s),d=a.useState([]),p=d[0],m=d[1],f=s!==c;t=u?f&&void 0!==n?n:o:f&&void 0!==r?r:i;var h=(0,Ne.D)(t);return a.useEffect((function(){var e=function(){return s.map((function(e,t){return e.selected?t:-1})).filter((function(e){return-1!==e}))},n=function(e){return(0,l.cx)(s,(function(t){return null!=e?t.key===e:!!t.selected||!!t.isSelected}))};void 0===t&&c||t===h&&!f||m(function(){if(void 0===t)return u?e():-1!==(a=n(null))?[a]:[];if(!Array.isArray(t))return-1!==(a=n(t))?[a]:[];for(var o=[],r=0,i=t;r<i.length;r++){var a,s=i[r];-1!==(a=n(s))&&o.push(a)}return o}())}),[f,u,c,h,s,t]),[p,m]}(n),c=u[0],d=u[1];return a.createElement(ze,(0,i.pi)({},n,{responsiveMode:s,hoisted:{rootRef:r,selectedIndices:c,setSelectedIndices:d}}))}));Fe.displayName="DropdownBase";var Oe,Le,Ae,He,ze=function(e){function t(t){var n=e.call(this,t)||this;n._host=a.createRef(),n._focusZone=a.createRef(),n._dropDown=a.createRef(),n._scrollIdleDelay=250,n._sizePosCache=new E,n._requestAnimationFrame=function(e){var t;return function(n){t||(t=new Set,(0,u.c)(e,{componentWillUnmount:function(){t.forEach((function(e){return cancelAnimationFrame(e)}))}}));var o=requestAnimationFrame((function(){t.delete(o),n()}));t.add(o)}}(n),n.dismissMenu=function(){n.state.isOpen&&n.setState({isOpen:!1})},n._onChange=function(e,t,o,r,a){var s=n.props,l=s.onChange,u=s.onChanged;if(l||u){var c=a?(0,i.pi)((0,i.pi)({},t[o]),{selected:!r}):t[o];l&&l((0,i.pi)((0,i.pi)({},e),{target:n._dropDown.current}),c,o),u&&u(c,o)}},n._getPlaceholder=function(){return n.props.placeholder||n.props.placeHolder},n._getTitle=function(e,t){var o=n.props.multiSelectDelimiter,r=void 0===o?", ":o;return e.map((function(e){return e.text})).join(r)},n._onRenderTitle=function(e){return a.createElement(a.Fragment,null,n._getTitle(e))},n._onRenderPlaceholder=function(e){return n._getPlaceholder()?a.createElement(a.Fragment,null,n._getPlaceholder()):null},n._onRenderContainer=function(e){var t=e.calloutProps,o=e.panelProps,r=n.props,s=r.responsiveMode,l=r.dropdownWidth,u=s<=ge.eD.medium,c={firstFocusableTarget:"#".concat(n._listId,"1")},d=n._classNames.subComponentStyles?n._classNames.subComponentStyles.panel:void 0,p=void 0,m=void 0;return"auto"===l?m=n._dropDown.current?n._dropDown.current.clientWidth:0:p=l||(n._dropDown.current?n._dropDown.current.clientWidth:0),u?a.createElement(he,(0,i.pi)({closeButtonAriaLabel:"Close",focusTrapZoneProps:c,hasCloseButton:!0,isOpen:!0,isLightDismiss:!0,onDismiss:n._onDismiss,styles:d},o),n._renderFocusableList(e)):a.createElement(_.U,(0,i.pi)({isBeakVisible:!1,gapSpace:0,doNotLayer:!1,directionalHintFixed:!1,directionalHint:C.b.bottomLeftEdge,calloutWidth:p,calloutMinWidth:m},t,{className:n._classNames.callout,target:n._dropDown.current,onDismiss:n._onDismiss,onScroll:n._onScroll,onPositioned:n._onPositioned}),n._renderFocusableList(e))},n._onRenderCaretDown=function(e){return a.createElement(T.J,{className:n._classNames.caretDown,iconName:"ChevronDown","aria-hidden":!0})},n._onRenderList=function(e){var t=e.onRenderItem,r=void 0===t?n._onRenderItem:t,s={items:[]},l=[],u=function(){var e=s.id?[a.createElement("div",{role:"group",key:s.id,"aria-labelledby":s.id},s.items)]:s.items;l=(0,i.ev)((0,i.ev)([],l,!0),e,!0),s={items:[]}};return e.options.forEach((function(e,t){!function(e,t){switch(e.itemType){case o.Header:s.items.length>0&&u();var a=n._id+e.key;s.items.push(r((0,i.pi)((0,i.pi)({id:a},e),{index:t}),n._onRenderItem)),s.id=a;break;case o.Divider:t>0&&s.items.push(r((0,i.pi)((0,i.pi)({},e),{index:t}),n._onRenderItem)),s.items.length>0&&u();break;default:s.items.push(r((0,i.pi)((0,i.pi)({},e),{index:t}),n._onRenderItem))}}(e,t)})),s.items.length>0&&u(),a.createElement(a.Fragment,null,l)},n._onRenderItem=function(e){switch(e.itemType){case o.Divider:return n._renderSeparator(e);case o.Header:return n._renderHeader(e);default:return n._renderOption(e)}},n._renderOption=function(e){var t,o=n.props,r=o.onRenderOption,s=void 0===r?n._onRenderOption:r,l=o.hoisted.selectedIndices,u=void 0===l?[]:l,d=!(void 0===e.index||!u)&&u.indexOf(e.index)>-1,p=e.hidden?n._classNames.dropdownItemHidden:d&&!0===e.disabled?n._classNames.dropdownItemSelectedAndDisabled:d?n._classNames.dropdownItemSelected:!0===e.disabled?n._classNames.dropdownItemDisabled:n._classNames.dropdownItem,m=e.title,f=n._listId+e.index,h=null!==(t=e.id)&&void 0!==t?t:f+"-label",v=n._classNames.subComponentStyles?n._classNames.subComponentStyles.multiSelectItem:void 0;return n.props.multiSelect?a.createElement(Se,{id:f,key:e.key,disabled:e.disabled,onChange:n._onItemClick(e),inputProps:(0,i.pi)({"aria-selected":d,onMouseEnter:n._onItemMouseEnter.bind(n,e),onMouseLeave:n._onMouseItemLeave.bind(n,e),onMouseMove:n._onItemMouseMove.bind(n,e),role:"option"},{"data-index":e.index,"data-is-focusable":!(e.disabled||e.hidden)}),label:e.text,title:m,onRenderLabel:n._onRenderItemLabel.bind(n,(0,i.pi)((0,i.pi)({},e),{id:h})),className:(0,c.i)(p,"is-multi-select"),checked:d,styles:v,ariaPositionInSet:e.hidden?void 0:n._sizePosCache.positionInSet(e.index),ariaSetSize:e.hidden?void 0:n._sizePosCache.optionSetSize,ariaLabel:e.ariaLabel,ariaLabelledBy:e.ariaLabel?void 0:h}):a.createElement(k.M,{id:f,key:e.key,"data-index":e.index,"data-is-focusable":!e.disabled,disabled:e.disabled,className:p,onClick:n._onItemClick(e),onMouseEnter:n._onItemMouseEnter.bind(n,e),onMouseLeave:n._onMouseItemLeave.bind(n,e),onMouseMove:n._onItemMouseMove.bind(n,e),role:"option","aria-selected":d?"true":"false",ariaLabel:e.ariaLabel,title:m,"aria-posinset":n._sizePosCache.positionInSet(e.index),"aria-setsize":n._sizePosCache.optionSetSize},s(e,n._onRenderOption))},n._onRenderOption=function(e){return a.createElement("span",{className:n._classNames.dropdownOptionText},e.text)},n._onRenderMultiselectOption=function(e){return a.createElement("span",{id:e.id,"aria-hidden":"true",className:n._classNames.dropdownOptionText},e.text)},n._onRenderItemLabel=function(e){var t=n.props.onRenderOption;return(void 0===t?n._onRenderMultiselectOption:t)(e,n._onRenderMultiselectOption)},n._onPositioned=function(e){n._focusZone.current&&n._requestAnimationFrame((function(){var e=n.props.hoisted.selectedIndices;if(n._focusZone.current)if(!n._hasBeenPositioned&&e&&e[0]&&!n.props.options[e[0]].disabled){var t=(0,d.M)().getElementById("".concat(n._id,"-list").concat(e[0]));t&&n._focusZone.current.focusElement(t),n._hasBeenPositioned=!0}else n._focusZone.current.focus()})),n.state.calloutRenderEdge&&n.state.calloutRenderEdge===e.targetEdge||n.setState({calloutRenderEdge:e.targetEdge})},n._onItemClick=function(e){return function(t){e.disabled||(n.setSelectedIndex(t,e.index),n.props.multiSelect||n.setState({isOpen:!1}))}},n._onScroll=function(){n._isScrollIdle||void 0===n._scrollIdleTimeoutId?n._isScrollIdle=!1:(clearTimeout(n._scrollIdleTimeoutId),n._scrollIdleTimeoutId=void 0),n._scrollIdleTimeoutId=window.setTimeout((function(){n._isScrollIdle=!0}),n._scrollIdleDelay)},n._onMouseItemLeave=function(e,t){if(!n._shouldIgnoreMouseEvent()&&n._host.current)if(n._host.current.setActive)try{n._host.current.setActive()}catch(o){}else n._host.current.focus()},n._onDismiss=function(){n.setState({isOpen:!1})},n._onDropdownBlur=function(e){n._isDisabled()||n.state.isOpen||(n.setState({hasFocus:!1}),n.props.onBlur&&n.props.onBlur(e))},n._onDropdownKeyDown=function(e){if(!n._isDisabled()&&(n._lastKeyDownWasAltOrMeta=n._isAltOrMeta(e),!n.props.onKeyDown||(n.props.onKeyDown(e),!e.defaultPrevented))){var t,o=n.props.hoisted.selectedIndices.length?n.props.hoisted.selectedIndices[0]:-1,r=e.altKey||e.metaKey,i=n.state.isOpen;switch(e.which){case p.m.enter:n.setState({isOpen:!i});break;case p.m.escape:if(!i)return;n.setState({isOpen:!1});break;case p.m.up:if(r){if(i){n.setState({isOpen:!1});break}return}n.props.multiSelect?n.setState({isOpen:!0}):n._isDisabled()||(t=n._moveIndex(e,-1,o-1,o));break;case p.m.down:r&&(e.stopPropagation(),e.preventDefault()),r&&!i||n.props.multiSelect?n.setState({isOpen:!0}):n._isDisabled()||(t=n._moveIndex(e,1,o+1,o));break;case p.m.home:n.props.multiSelect||(t=n._moveIndex(e,1,0,o));break;case p.m.end:n.props.multiSelect||(t=n._moveIndex(e,-1,n.props.options.length-1,o));break;case p.m.space:break;default:return}t!==o&&(e.stopPropagation(),e.preventDefault())}},n._onDropdownKeyUp=function(e){if(!n._isDisabled()){var t=n._shouldHandleKeyUp(e),o=n.state.isOpen;n.props.onKeyUp&&(n.props.onKeyUp(e),e.defaultPrevented)||(e.which===p.m.space?(n.setState({isOpen:!o}),e.stopPropagation(),e.preventDefault()):t&&o&&n.setState({isOpen:!1}))}},n._onZoneKeyDown=function(e){var t,o,r;n._lastKeyDownWasAltOrMeta=n._isAltOrMeta(e);var i=e.altKey||e.metaKey;switch(e.which){case p.m.up:i?n.setState({isOpen:!1}):n._host.current&&(r=(0,m.TE)(n._host.current,n._host.current.lastChild,!0));break;case p.m.home:case p.m.end:case p.m.pageUp:case p.m.pageDown:break;case p.m.down:!i&&n._host.current&&(r=(0,m.ft)(n._host.current,n._host.current.firstChild,!0));break;case p.m.escape:n.setState({isOpen:!1});break;case p.m.tab:n.setState({isOpen:!1});var a=(0,d.M)();a&&(e.shiftKey?null===(t=(0,m.TD)(a.body,n._dropDown.current,!1,!1,!0,!0))||void 0===t||t.focus():null===(o=(0,m.dc)(a.body,n._dropDown.current,!1,!1,!0,!0))||void 0===o||o.focus());break;default:return}r&&r.focus(),e.stopPropagation(),e.preventDefault()},n._onZoneKeyUp=function(e){n._shouldHandleKeyUp(e)&&n.state.isOpen&&(n.setState({isOpen:!1}),e.preventDefault())},n._onDropdownClick=function(e){if(!n.props.onClick||(n.props.onClick(e),!e.defaultPrevented)){var t=n.state.isOpen;n._isDisabled()||n._shouldOpenOnFocus()||n.setState({isOpen:!t}),n._isFocusedByClick=!1}},n._onDropdownMouseDown=function(){n._isFocusedByClick=!0},n._onFocus=function(e){if(!n._isDisabled()){n.props.onFocus&&n.props.onFocus(e);var t={hasFocus:!0};n._shouldOpenOnFocus()&&(t.isOpen=!0),n.setState(t)}},n._isDisabled=function(){var e=n.props.disabled,t=n.props.isDisabled;return void 0===e&&(e=t),e},n._onRenderLabel=function(e){var t=e.label,o=e.required,r=e.disabled,i=n._classNames.subComponentStyles?n._classNames.subComponentStyles.label:void 0;return t?a.createElement(M._,{className:n._classNames.label,id:n._labelId,required:o,styles:i,disabled:r},t):null},(0,f.l)(n);t.multiSelect,t.selectedKey,t.selectedKeys,t.defaultSelectedKey,t.defaultSelectedKeys;var r=t.options;return n._id=t.id||(0,h.z)("Dropdown"),n._labelId=n._id+"-label",n._listId=n._id+"-list",n._optionId=n._id+"-option",n._isScrollIdle=!0,n._hasBeenPositioned=!1,n._sizePosCache.updateOptions(r),n.state={isOpen:!1,hasFocus:!1,calloutRenderEdge:void 0},n}return(0,i.ZT)(t,e),Object.defineProperty(t.prototype,"selectedOptions",{get:function(){var e=this.props;return be(e.options,e.hoisted.selectedIndices)},enumerable:!1,configurable:!0}),t.prototype.componentWillUnmount=function(){clearTimeout(this._scrollIdleTimeoutId)},t.prototype.componentDidUpdate=function(e,t){!0===t.isOpen&&!1===this.state.isOpen&&(this._gotMouseMove=!1,this._hasBeenPositioned=!1,this.props.onDismiss&&this.props.onDismiss())},t.prototype.render=function(){var e=this._id,t=this.props,n=t.className,o=t.label,r=t.options,s=t.ariaLabel,l=t.required,u=t.errorMessage,c=t.styles,d=t.theme,p=t.panelProps,m=t.calloutProps,f=t.onRenderTitle,h=void 0===f?this._getTitle:f,b=t.onRenderContainer,y=void 0===b?this._onRenderContainer:b,_=t.onRenderCaretDown,C=void 0===_?this._onRenderCaretDown:_,k=t.onRenderLabel,x=void 0===k?this._onRenderLabel:k,E=t.onRenderItem,I=void 0===E?this._onRenderItem:E,w=t.hoisted.selectedIndices,T=this.state,M=T.isOpen,D=T.calloutRenderEdge,S=T.hasFocus,P=t.onRenderPlaceholder||t.onRenderPlaceHolder||this._getPlaceholder;r!==this._sizePosCache.cachedOptions&&this._sizePosCache.updateOptions(r);var N=be(r,w),R=(0,v.pq)(t,v.n7),B=this._isDisabled(),F=e+"-errorMessage";this._classNames=Re(c,{theme:d,className:n,hasError:!!(u&&u.length>0),hasLabel:!!o,isOpen:M,required:l,disabled:B,isRenderingPlaceholder:!N.length,panelClassName:p?p.className:void 0,calloutClassName:m?m.className:void 0,calloutRenderEdge:D});var O=!!u&&u.length>0;return a.createElement("div",{className:this._classNames.root,ref:this.props.hoisted.rootRef,"aria-owns":M?this._listId:void 0},x(this.props,this._onRenderLabel),a.createElement("div",(0,i.pi)({"data-is-focusable":!B,"data-ktp-target":!0,ref:this._dropDown,id:e,tabIndex:B?-1:0,role:"combobox","aria-haspopup":"listbox","aria-expanded":M?"true":"false","aria-label":s,"aria-labelledby":o&&!s?(0,g.I)(this._labelId,this._optionId):void 0,"aria-describedby":O?this._id+"-errorMessage":void 0,"aria-required":l,"aria-disabled":B,"aria-controls":M?this._listId:void 0},R,{className:this._classNames.dropdown,onBlur:this._onDropdownBlur,onKeyDown:this._onDropdownKeyDown,onKeyUp:this._onDropdownKeyUp,onClick:this._onDropdownClick,onMouseDown:this._onDropdownMouseDown,onFocus:this._onFocus}),a.createElement("span",{id:this._optionId,className:this._classNames.title,"aria-live":S?"polite":void 0,"aria-atomic":!!S||void 0,"aria-invalid":O},N.length?h(N,this._onRenderTitle):P(t,this._onRenderPlaceholder)),a.createElement("span",{className:this._classNames.caretDownWrapper},C(t,this._onRenderCaretDown))),M&&y((0,i.pi)((0,i.pi)({},t),{onDismiss:this._onDismiss,onRenderItem:I}),this._onRenderContainer),O&&a.createElement("div",{role:"alert",id:F,className:this._classNames.errorMessage},u))},t.prototype.focus=function(e){this._dropDown.current&&(this._dropDown.current.focus(),e&&this.setState({isOpen:!0}))},t.prototype.setSelectedIndex=function(e,t){var n=this.props,o=n.options,r=n.selectedKey,i=n.selectedKeys,a=n.multiSelect,s=n.notifyOnReselect,l=n.hoisted.selectedIndices,u=void 0===l?[]:l,c=!!u&&u.indexOf(t)>-1,d=[];if(t=Math.max(0,Math.min(o.length-1,t)),void 0===r&&void 0===i){if(a||s||t!==u[0]){if(a)if(d=u?this._copyArray(u):[],c){var p=d.indexOf(t);p>-1&&d.splice(p,1)}else d.push(t);else d=[t];e.persist(),this.props.hoisted.setSelectedIndices(d),this._onChange(e,o,t,c,a)}}else this._onChange(e,o,t,c,a)},t.prototype._copyArray=function(e){for(var t=[],n=0,o=e;n<o.length;n++){var r=o[n];t.push(r)}return t},t.prototype._moveIndex=function(e,t,n,r){var i=this.props.options;if(r===n||0===i.length)return r;n>=i.length?n=0:n<0&&(n=i.length-1);for(var a=0;i[n].itemType===o.Header||i[n].itemType===o.Divider||i[n].disabled;){if(a>=i.length)return r;n+t<0?n=i.length:n+t>=i.length&&(n=-1),n+=t,a++}return this.setSelectedIndex(e,n),n},t.prototype._renderFocusableList=function(e){var t=e.onRenderList,n=void 0===t?this._onRenderList:t,o=e.label,r=e.ariaLabel,i=e.multiSelect;return a.createElement("div",{className:this._classNames.dropdownItemsWrapper,onKeyDown:this._onZoneKeyDown,onKeyUp:this._onZoneKeyUp,ref:this._host,tabIndex:0},a.createElement(I.k,{ref:this._focusZone,direction:w.U.vertical,id:this._listId,className:this._classNames.dropdownItems,role:"listbox","aria-label":r,"aria-labelledby":o&&!r?this._labelId:void 0,"aria-multiselectable":i},n(e,this._onRenderList)))},t.prototype._renderSeparator=function(e){var t=e.index,n=e.key,o=e.hidden?this._classNames.dropdownDividerHidden:this._classNames.dropdownDivider;return t>0?a.createElement("div",{role:"presentation",key:n,className:o}):null},t.prototype._renderHeader=function(e){var t=this.props.onRenderOption,n=void 0===t?this._onRenderOption:t,o=e.key,r=e.id,i=e.hidden?this._classNames.dropdownItemHeaderHidden:this._classNames.dropdownItemHeader;return a.createElement("div",{id:r,key:o,className:i},n(e,this._onRenderOption))},t.prototype._onItemMouseEnter=function(e,t){this._shouldIgnoreMouseEvent()||t.currentTarget.focus()},t.prototype._onItemMouseMove=function(e,t){var n=t.currentTarget;this._gotMouseMove=!0,this._isScrollIdle&&document.activeElement!==n&&n.focus()},t.prototype._shouldIgnoreMouseEvent=function(){return!this._isScrollIdle||!this._gotMouseMove},t.prototype._isAltOrMeta=function(e){return e.which===p.m.alt||"Meta"===e.key},t.prototype._shouldHandleKeyUp=function(e){var t=this._lastKeyDownWasAltOrMeta&&this._isAltOrMeta(e);return this._lastKeyDownWasAltOrMeta=!1,!!t&&!((0,b.V)()||(0,y.g)())},t.prototype._shouldOpenOnFocus=function(){var e=this.state.hasFocus,t=this.props.openOnKeyboardFocus;return!this._isFocusedByClick&&!0===t&&!e},t.defaultProps={options:[]},t}(a.Component),We=n(50899),Ke={root:"ms-Dropdown-container",label:"ms-Dropdown-label",dropdown:"ms-Dropdown",title:"ms-Dropdown-title",caretDownWrapper:"ms-Dropdown-caretDownWrapper",caretDown:"ms-Dropdown-caretDown",callout:"ms-Dropdown-callout",panel:"ms-Dropdown-panel",dropdownItems:"ms-Dropdown-items",dropdownItem:"ms-Dropdown-item",dropdownDivider:"ms-Dropdown-divider",dropdownOptionText:"ms-Dropdown-optionText",dropdownItemHeader:"ms-Dropdown-header",titleIsPlaceHolder:"ms-Dropdown-titleIsPlaceHolder",titleHasError:"ms-Dropdown-title--hasError"},qe=((Oe={})["".concat(j.qJ,", ").concat(j.bO.replace("@media ",""))]=(0,i.pi)({},(0,j.xM)()),Oe),Ue={selectors:(0,i.pi)((Le={},Le[j.qJ]={backgroundColor:"Highlight",borderColor:"Highlight",color:"HighlightText"},Le[".ms-Checkbox-checkbox"]=(Ae={},Ae[j.qJ]={borderColor:"HighlightText"},Ae),Le),qe)},Ge={selectors:(He={},He[j.qJ]={borderColor:"Highlight"},He)},Je=(0,j.sK)(0,j.dd),Ze=(0,r.z)(Fe,(function(e){var t,n,o,r,a,s,l,u,c,d,p,m,f=e.theme,h=e.hasError,v=e.hasLabel,g=e.className,b=e.isOpen,y=e.disabled,_=e.required,C=e.isRenderingPlaceholder,k=e.panelClassName,x=e.calloutClassName,E=e.calloutRenderEdge;if(!f)throw new Error("theme is undefined or null in base Dropdown getStyles function.");var I=(0,j.Cn)(Ke,f),w=f.palette,T=f.semanticColors,M=f.effects,D=f.fonts,S={color:T.menuItemTextHovered},P={color:T.menuItemText},N={borderColor:T.errorText},R=[I.dropdownItem,{backgroundColor:"transparent",boxSizing:"border-box",cursor:"pointer",display:"flex",alignItems:"center",padding:"0 8px",width:"100%",minHeight:36,lineHeight:20,height:0,position:"relative",border:"1px solid transparent",borderRadius:0,wordWrap:"break-word",overflowWrap:"break-word",textAlign:"left",".ms-Button-flexContainer":{width:"100%"}}],B=[I.dropdownItemHeader,(0,i.pi)((0,i.pi)({},D.medium),{fontWeight:j.lq.semibold,color:T.menuHeader,background:"none",backgroundColor:"transparent",border:"none",height:36,lineHeight:36,cursor:"default",padding:"0 8px",userSelect:"none",textAlign:"left",selectors:(t={},t[j.qJ]=(0,i.pi)({color:"GrayText"},(0,j.xM)()),t)})],F=T.menuItemBackgroundPressed,O=function(e){var t,n;return void 0===e&&(e=!1),{selectors:(t={"&:hover":[{color:T.menuItemTextHovered,backgroundColor:e?F:T.menuItemBackgroundHovered},Ue],"&.is-multi-select:hover":[{backgroundColor:e?F:"transparent"},Ue],"&:active:hover":[{color:T.menuItemTextHovered,backgroundColor:e?T.menuItemBackgroundHovered:T.menuItemBackgroundPressed},Ue]},t[".".concat(Ie.G$," &:focus:after")]=(n={left:0,top:0,bottom:0,right:0},n[j.qJ]={inset:"2px"},n),t[j.qJ]={border:"none"},t)}},L=(0,i.ev)((0,i.ev)([],R,!0),[{backgroundColor:F,color:T.menuItemTextHovered},O(!0),Ue],!1),A=(0,i.ev)((0,i.ev)([],R,!0),[{color:T.disabledText,cursor:"default",selectors:(n={},n[j.qJ]={color:"GrayText",border:"none"},n)}],!1),H=E===We.z.bottom?"".concat(M.roundedCorner2," ").concat(M.roundedCorner2," 0 0"):"0 0 ".concat(M.roundedCorner2," ").concat(M.roundedCorner2),z=E===We.z.bottom?"0 0 ".concat(M.roundedCorner2," ").concat(M.roundedCorner2):"".concat(M.roundedCorner2," ").concat(M.roundedCorner2," 0 0");return{root:[I.root,g],label:I.label,dropdown:[I.dropdown,j.Fv,D.medium,{color:T.menuItemText,borderColor:T.focusBorder,position:"relative",outline:0,userSelect:"none",selectors:(o={},o["&:hover ."+I.title]=[!y&&S,{borderColor:b?w.neutralSecondary:w.neutralPrimary},Ge],o["&:focus ."+I.title]=[!y&&S,{selectors:(r={},r[j.qJ]={color:"Highlight"},r)}],o["&:focus:after"]=[{pointerEvents:"none",content:"''",position:"absolute",boxSizing:"border-box",top:"0px",left:"0px",width:"100%",height:"100%",border:y?"none":"2px solid ".concat(w.themePrimary),borderRadius:"2px",selectors:(a={},a[j.qJ]={color:"Highlight"},a)}],o["&:active ."+I.title]=[!y&&S,{borderColor:w.themePrimary},Ge],o["&:hover ."+I.caretDown]=!y&&P,o["&:focus ."+I.caretDown]=[!y&&P,{selectors:(s={},s[j.qJ]={color:"Highlight"},s)}],o["&:active ."+I.caretDown]=!y&&P,o["&:hover ."+I.titleIsPlaceHolder]=!y&&P,o["&:focus ."+I.titleIsPlaceHolder]=!y&&P,o["&:active ."+I.titleIsPlaceHolder]=!y&&P,o["&:hover ."+I.titleHasError]=N,o["&:active ."+I.titleHasError]=N,o)},b&&"is-open",y&&"is-disabled",_&&"is-required",_&&!v&&{selectors:(l={":before":{content:"'*'",color:T.errorText,position:"absolute",top:-5,right:-10}},l[j.qJ]={selectors:{":after":{right:-14}}},l)}],title:[I.title,j.Fv,{backgroundColor:T.inputBackground,borderWidth:1,borderStyle:"solid",borderColor:T.inputBorder,borderRadius:b?H:M.roundedCorner2,cursor:"pointer",display:"block",height:32,lineHeight:30,padding:"0 28px 0 8px",position:"relative",overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"},C&&[I.titleIsPlaceHolder,{color:T.inputPlaceholderText}],h&&[I.titleHasError,N],y&&{backgroundColor:T.disabledBackground,border:"none",color:T.disabledText,cursor:"default",selectors:(u={},u[j.qJ]=(0,i.pi)({border:"1px solid GrayText",color:"GrayText",backgroundColor:"Window"},(0,j.xM)()),u)}],caretDownWrapper:[I.caretDownWrapper,{height:32,lineHeight:30,paddingTop:1,position:"absolute",right:8,top:0},!y&&{cursor:"pointer"}],caretDown:[I.caretDown,{color:w.neutralSecondary,fontSize:D.small.fontSize,pointerEvents:"none"},y&&{color:T.disabledText,selectors:(c={},c[j.qJ]=(0,i.pi)({color:"GrayText"},(0,j.xM)()),c)}],errorMessage:(0,i.pi)((0,i.pi)({color:T.errorText},f.fonts.small),{paddingTop:5}),callout:[I.callout,{boxShadow:M.elevation8,borderRadius:z,selectors:(d={},d[".ms-Callout-main"]={borderRadius:z},d)},x],dropdownItemsWrapper:{selectors:{"&:focus":{outline:0}}},dropdownItems:[I.dropdownItems,{display:"block"}],dropdownItem:(0,i.ev)((0,i.ev)([],R,!0),[O()],!1),dropdownItemSelected:L,dropdownItemDisabled:A,dropdownItemSelectedAndDisabled:[L,A,{backgroundColor:"transparent"}],dropdownItemHidden:(0,i.ev)((0,i.ev)([],R,!0),[{display:"none"}],!1),dropdownDivider:[I.dropdownDivider,{height:1,backgroundColor:T.bodyDivider}],dropdownDividerHidden:[I.dropdownDivider,{display:"none"}],dropdownOptionText:[I.dropdownOptionText,{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",minWidth:0,maxWidth:"100%",wordWrap:"break-word",overflowWrap:"break-word",margin:"1px"}],dropdownItemHeader:B,dropdownItemHeaderHidden:(0,i.ev)((0,i.ev)([],B,!0),[{display:"none"}],!1),subComponentStyles:{label:{root:{display:"inline-block"}},multiSelectItem:{root:{padding:0},label:{alignSelf:"stretch",padding:"0 8px",width:"100%"},input:{selectors:(p={},p[".".concat(Ie.G$," &:focus + label::before")]={outlineOffset:"0px"},p)}},panel:{root:[k],main:{selectors:(m={},m[Je]={width:272},m)},contentInner:{padding:"0 0 20px"}}}}}),void 0,{scope:"Dropdown"});Ze.displayName="Dropdown"},10892:function(e,t,n){n.d(t,{P:function(){return _}});var o=n(75971),r=n(72791),i=n(97471),a=n(56500),s=n(84688),l=n(64862),u=n(38994),c=n(15863),d=n(66587),p=n(1604),m=n(32561),f=n(24994),h=n(7211);function v(e){var t=r.useRef((function(){throw new Error("Cannot call an event handler while rendering")}));return(0,h.L)((function(){t.current=e}),[e]),(0,m.B)((function(){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t.current.apply(void 0,e)}}))}var g=n(38289),b=n(3141),y={disabled:!1,disableFirstFocus:!1,forceFocusInsideTrap:!0,isClickableOutsideFocusTrap:!1},_=r.forwardRef((function(e,t){var n,h=r.useRef(null),C=r.useRef(null),k=r.useRef(null),x=(0,d.r)(h,t),E=(0,b.ky)(),I=null===(n=(0,p.D)(!1))||void 0===n||n,w=(0,i.j)(y,e),T=(0,m.B)({hasFocus:!1,focusStackId:(0,f.M)("ftz-",w.id)}),M=w.children,D=w.componentRef,S=w.disabled,P=w.disableFirstFocus,N=w.forceFocusInsideTrap,R=w.focusPreviouslyFocusedInnerElement,B=w.firstFocusableSelector,F=w.firstFocusableTarget,O=w.disableRestoreFocus,L=void 0===O?w.ignoreExternalFocusing:O,A=w.isClickableOutsideFocusTrap,H=w.enableAriaHiddenSiblings,z={"aria-hidden":!0,style:{pointerEvents:"none",position:"fixed"},tabIndex:S?-1:0,"data-is-visible":!0,"data-is-focus-trap-zone-bumper":!0},W=r.useCallback((function(e){e!==C.current&&e!==k.current&&(0,a.um)(e)}),[]),K=v((function(){if(h.current){var e=T.previouslyFocusedElementInTrapZone;if(R&&e&&(0,s.t)(h.current,e))W(e);else{var t=null;if("string"===typeof F)t=h.current.querySelector(F);else if(F)t=F(h.current);else if(B){var n="string"===typeof B?B:B();t=h.current.querySelector("."+n)}t||(t=(0,a.dc)(h.current,h.current.firstChild,!1,!1,!1,!0)),t&&W(t)}}})),q=function(e){if(!S&&h.current){var t=e===T.hasFocus?(0,a.xY)(h.current,k.current,!0,!1):(0,a.RK)(h.current,C.current,!0,!1);t&&(t===C.current||t===k.current?K():t.focus())}},U=v((function(e){if(_.focusStack=_.focusStack.filter((function(e){return T.focusStackId!==e})),E){var t=E.activeElement;L||"function"!==typeof(null===e||void 0===e?void 0:e.focus)||!(0,s.t)(h.current,t)&&t!==E.body||W(e)}})),G=v((function(e){if(!S&&T.focusStackId===_.focusStack.slice(-1)[0]){var t=e.target;t&&!(0,s.t)(h.current,t)&&(E&&E.activeElement===E.body?setTimeout((function(){E&&E.activeElement===E.body&&(K(),T.hasFocus=!0)}),0):(K(),T.hasFocus=!0),e.preventDefault(),e.stopPropagation())}}));return r.useEffect((function(){var e=[];return N&&e.push((0,l.on)(window,"focus",G,!0)),A||e.push((0,l.on)(window,"click",G,!0)),function(){e.forEach((function(e){return e()}))}}),[N,A]),r.useEffect((function(){if(!S&&(I||N)&&h.current){_.focusStack.push(T.focusStackId);var e=w.elementToFocusOnDismiss||E.activeElement;return P||(0,s.t)(h.current,e)||K(),function(){return U(e)}}}),[N,S]),r.useEffect((function(){if(!S&&H)return(0,u.O)(h.current)}),[S,H,h]),(0,g.k)((function(){delete T.previouslyFocusedElementInTrapZone})),function(e,t,n){r.useImperativeHandle(e,(function(){return{get previouslyFocusedElement(){return t},focus:n}}),[n,t])}(D,T.previouslyFocusedElementInTrapZone,K),r.createElement("div",(0,o.pi)({"aria-labelledby":w.ariaLabelledBy},(0,c.pq)(w,c.n7),{ref:x,onFocusCapture:function(e){var t;null===(t=w.onFocusCapture)||void 0===t||t.call(w,e),e.target===C.current?q(!0):e.target===k.current&&q(!1),T.hasFocus=!0,e.target!==e.currentTarget&&e.target!==C.current&&e.target!==k.current&&(T.previouslyFocusedElementInTrapZone=e.target)},onBlurCapture:function(e){var t;null===(t=w.onBlurCapture)||void 0===t||t.call(w,e);var n=e.relatedTarget;null===e.relatedTarget&&(n=E.activeElement),(0,s.t)(h.current,n)||(T.hasFocus=!1)}}),r.createElement("div",(0,o.pi)({},z,{ref:C})),M,r.createElement("div",(0,o.pi)({},z,{ref:k})))}));_.displayName="FocusTrapZone",_.focusStack=[]},90511:function(e,t,n){n.d(t,{xu:function(){return d},z1:function(){return c}});var o=n(75971),r=n(72791),i=n(93474),a=n(42792),s=n(15863),l=n(29723),u=n(40528),c=(0,a.NF)((function(e){var t=(0,u.q7)(e)||{subset:{},code:void 0},n=t.code,o=t.subset;return n?{children:n,iconClassName:o.className,fontFamily:o.fontFace&&o.fontFace.fontFamily,mergeImageProps:o.mergeImageProps}:null}),void 0,!0),d=function(e){var t=e.iconName,n=e.className,a=e.style,u=void 0===a?{}:a,d=c(t)||{},p=d.iconClassName,m=d.children,f=d.fontFamily,h=d.mergeImageProps,v=(0,s.pq)(e,s.iY),g=e["aria-label"]||e.title,b=e["aria-label"]||e["aria-labelledby"]||e.title?{role:h?void 0:"img"}:{"aria-hidden":!0},y=m;return h&&"object"===typeof m&&"object"===typeof m.props&&g&&(y=r.cloneElement(m,{alt:g})),r.createElement("i",(0,o.pi)({"data-icon-name":t},b,v,h?{title:void 0,"aria-label":void 0}:{},{className:(0,l.i)(i.Sk,i.AK.root,p,!t&&i.AK.placeholder,n),style:(0,o.pi)({fontFamily:f},u)}),y)};(0,a.NF)((function(e,t,n){return d({iconName:e,className:t,"aria-label":n})}))},18511:function(e,t,n){n.d(t,{J:function(){return h}});var o,r=n(40433),i=n(75971),a=n(72791);!function(e){e[e.default=0]="default",e[e.image=1]="image",e[e.Default=1e5]="Default",e[e.Image=100001]="Image"}(o||(o={}));var s=n(89085),l=n(72405),u=n(79292),c=n(15863),d=n(90511),p=(0,u.y)({cacheSize:100}),m=function(e){function t(t){var n=e.call(this,t)||this;return n._onImageLoadingStateChange=function(e){n.props.imageProps&&n.props.imageProps.onLoadingStateChange&&n.props.imageProps.onLoadingStateChange(e),e===l.U9.error&&n.setState({imageLoadError:!0})},n.state={imageLoadError:!1},n}return(0,i.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.children,n=e.className,r=e.styles,l=e.iconName,u=e.imageErrorAs,m=e.theme,f="string"===typeof l&&0===l.length,h=!!this.props.imageProps||this.props.iconType===o.image||this.props.iconType===o.Image,v=(0,d.z1)(l)||{},g=v.iconClassName,b=v.children,y=v.mergeImageProps,_=p(r,{theme:m,className:n,iconClassName:g,isImage:h,isPlaceholder:f}),C=h?"span":"i",k=(0,c.pq)(this.props,c.iY,["aria-label"]),x=this.state.imageLoadError,E=(0,i.pi)((0,i.pi)({},this.props.imageProps),{onLoadingStateChange:this._onImageLoadingStateChange}),I=x&&u||s.E,w=this.props["aria-label"]||this.props.ariaLabel,T=E.alt||w||this.props.title,M=!!(T||this.props["aria-labelledby"]||E["aria-label"]||E["aria-labelledby"])?{role:h||y?void 0:"img","aria-label":h||y?void 0:T}:{"aria-hidden":!0},D=b;return y&&b&&"object"===typeof b&&T&&(D=a.cloneElement(b,{alt:T})),a.createElement(C,(0,i.pi)({"data-icon-name":l},M,k,y?{title:void 0,"aria-label":void 0}:{},{className:_.root}),h?a.createElement(I,(0,i.pi)({},E)):t||D)},t}(a.Component),f=n(93474),h=(0,r.z)(m,f.Wi,void 0,{scope:"Icon"},!0);h.displayName="Icon"},93474:function(e,t,n){n.d(t,{AK:function(){return o},Sk:function(){return r},Wi:function(){return i}});var o=(0,n(40528).ZC)({root:{display:"inline-block"},placeholder:["ms-Icon-placeHolder",{width:"1em"}],image:["ms-Icon-imageContainer",{overflow:"hidden"}]}),r="ms-Icon",i=function(e){var t=e.className,n=e.iconClassName,r=e.isPlaceholder,i=e.isImage,a=e.styles;return{root:[r&&o.placeholder,o.root,i&&o.image,n,t,a&&a.root,a&&a.imageContainer]}}},89085:function(e,t,n){n.d(t,{E:function(){return g}});var o=n(40433),r=n(75971),i=n(72791),a=n(79292),s=n(15863),l=n(72405),u=n(7211),c=n(66587),d=(0,a.y)(),p=/\.svg$/i;var m=i.forwardRef((function(e,t){var n=i.useRef(),o=i.useRef(),a=function(e,t){var n=e.onLoadingStateChange,o=e.onLoad,r=e.onError,a=e.src,s=i.useState(l.U9.notLoaded),c=s[0],d=s[1];(0,u.L)((function(){d(l.U9.notLoaded)}),[a]),i.useEffect((function(){c===l.U9.notLoaded&&t.current&&(a&&t.current.naturalWidth>0&&t.current.naturalHeight>0||t.current.complete&&p.test(a))&&d(l.U9.loaded)})),i.useEffect((function(){null===n||void 0===n||n(c)}),[c]);var m=i.useCallback((function(e){null===o||void 0===o||o(e),a&&d(l.U9.loaded)}),[a,o]),f=i.useCallback((function(e){null===r||void 0===r||r(e),d(l.U9.error)}),[r]);return[c,m,f]}(e,o),m=a[0],f=a[1],h=a[2],v=(0,s.pq)(e,s.it,["width","height"]),g=e.src,b=e.alt,y=e.width,_=e.height,C=e.shouldFadeIn,k=void 0===C||C,x=e.shouldStartVisible,E=e.className,I=e.imageFit,w=e.role,T=e.maximizeFrame,M=e.styles,D=e.theme,S=e.loading,P=function(e,t,n,o){var r=i.useRef(t),a=i.useRef();(void 0===a||r.current===l.U9.notLoaded&&t===l.U9.loaded)&&(a.current=function(e,t,n,o){var r=e.imageFit,i=e.width,a=e.height;if(void 0!==e.coverStyle)return e.coverStyle;if(t===l.U9.loaded&&(r===l.kQ.cover||r===l.kQ.contain||r===l.kQ.centerContain||r===l.kQ.centerCover)&&n.current&&o.current){var s=void 0;if(s="number"===typeof i&&"number"===typeof a&&r!==l.kQ.centerContain&&r!==l.kQ.centerCover?i/a:o.current.clientWidth/o.current.clientHeight,n.current.naturalWidth/n.current.naturalHeight>s)return l.yZ.landscape}return l.yZ.portrait}(e,t,n,o));return r.current=t,a.current}(e,m,o,n),N=d(M,{theme:D,className:E,width:y,height:_,maximizeFrame:T,shouldFadeIn:k,shouldStartVisible:x,isLoaded:m===l.U9.loaded||m===l.U9.notLoaded&&e.shouldStartVisible,isLandscape:P===l.yZ.landscape,isCenter:I===l.kQ.center,isCenterContain:I===l.kQ.centerContain,isCenterCover:I===l.kQ.centerCover,isContain:I===l.kQ.contain,isCover:I===l.kQ.cover,isNone:I===l.kQ.none,isError:m===l.U9.error,isNotImageFit:void 0===I});return i.createElement("div",{className:N.root,style:{width:y,height:_},ref:n},i.createElement("img",(0,r.pi)({},v,{onLoad:f,onError:h,key:"fabricImage"+e.src||"",className:N.image,ref:(0,c.r)(o,t),src:g,alt:b,role:w,loading:S})))}));m.displayName="ImageBase";var f=n(40528),h=n(12513),v={root:"ms-Image",rootMaximizeFrame:"ms-Image--maximizeFrame",image:"ms-Image-image",imageCenter:"ms-Image-image--center",imageContain:"ms-Image-image--contain",imageCover:"ms-Image-image--cover",imageCenterContain:"ms-Image-image--centerContain",imageCenterCover:"ms-Image-image--centerCover",imageNone:"ms-Image-image--none",imageLandscape:"ms-Image-image--landscape",imagePortrait:"ms-Image-image--portrait"},g=(0,o.z)(m,(function(e){var t=e.className,n=e.width,o=e.height,r=e.maximizeFrame,i=e.isLoaded,a=e.shouldFadeIn,s=e.shouldStartVisible,l=e.isLandscape,u=e.isCenter,c=e.isContain,d=e.isCover,p=e.isCenterContain,m=e.isCenterCover,g=e.isNone,b=e.isError,y=e.isNotImageFit,_=e.theme,C=(0,f.Cn)(v,_),k={position:"absolute",left:"50% /* @noflip */",top:"50%",transform:"translate(-50%,-50%)"},x=(0,h.J)(),E=void 0!==x&&void 0===x.navigator.msMaxTouchPoints,I=c&&l||d&&!l?{width:"100%",height:"auto"}:{width:"auto",height:"100%"};return{root:[C.root,_.fonts.medium,{overflow:"hidden"},r&&[C.rootMaximizeFrame,{height:"100%",width:"100%"}],i&&a&&!s&&f.k4.fadeIn400,(u||c||d||p||m)&&{position:"relative"},t],image:[C.image,{display:"block",opacity:0},i&&["is-loaded",{opacity:1}],u&&[C.imageCenter,k],c&&[C.imageContain,E&&{width:"100%",height:"100%",objectFit:"contain"},!E&&I,!E&&k],d&&[C.imageCover,E&&{width:"100%",height:"100%",objectFit:"cover"},!E&&I,!E&&k],p&&[C.imageCenterContain,l&&{maxWidth:"100%"},!l&&{maxHeight:"100%"},k],m&&[C.imageCenterCover,l&&{maxHeight:"100%"},!l&&{maxWidth:"100%"},k],g&&[C.imageNone,{width:"auto",height:"auto"}],y&&[!!n&&!o&&{height:"auto",width:"100%"},!n&&!!o&&{height:"100%",width:"auto"},!!n&&!!o&&{height:"100%",width:"100%"}],l&&C.imageLandscape,!l&&C.imagePortrait,!i&&"is-notLoaded",a&&"is-fadeIn",b&&"is-error"]}}),void 0,{scope:"Image"},!0);g.displayName="Image"},72405:function(e,t,n){var o,r,i;n.d(t,{U9:function(){return i},kQ:function(){return o},yZ:function(){return r}}),function(e){e[e.center=0]="center",e[e.contain=1]="contain",e[e.cover=2]="cover",e[e.none=3]="none",e[e.centerCover=4]="centerCover",e[e.centerContain=5]="centerContain"}(o||(o={})),function(e){e[e.landscape=0]="landscape",e[e.portrait=1]="portrait"}(r||(r={})),function(e){e[e.notLoaded=0]="notLoaded",e[e.loaded=1]="loaded",e[e.error=2]="error",e[e.errorLoaded=3]="errorLoaded"}(i||(i={}))},63934:function(e,t,n){n.d(t,{_:function(){return c}});var o=n(40433),r=n(75971),i=n(72791),a=n(15863),s=(0,n(79292).y)({cacheSize:100}),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.as,n=void 0===t?"label":t,o=e.children,l=e.className,u=e.disabled,c=e.styles,d=e.required,p=e.theme,m=s(c,{className:l,disabled:u,required:d,theme:p});return i.createElement(n,(0,r.pi)({},(0,a.pq)(this.props,a.n7),{className:m.root}),o)},t}(i.Component),u=n(40528),c=(0,o.z)(l,(function(e){var t,n=e.theme,o=e.className,i=e.disabled,a=e.required,s=n.semanticColors,l=u.lq.semibold,c=s.bodyText,d=s.disabledBodyText,p=s.errorText;return{root:["ms-Label",n.fonts.medium,{fontWeight:l,color:c,boxSizing:"border-box",boxShadow:"none",margin:0,display:"block",padding:"5px 0",wordWrap:"break-word",overflowWrap:"break-word"},i&&{color:d,selectors:(t={},t[u.qJ]=(0,r.pi)({color:"GrayText"},(0,u.xM)()),t)},a&&{selectors:{"::after":{content:"' *'",color:p,paddingRight:12}}},o]}}),void 0,{scope:"Label"})},25443:function(e,t,n){n.d(t,{m:function(){return W}});var o=n(40433),r=n(75971),i=n(72791),a=i.createContext(void 0),s=function(){return function(){}};a.Provider;var l=n(54164),u=n(79292),c=n(42792),d=n(87376),p=n(15863),m=n(6003),f=n(19101),h=n(72557);function v(e,t){void 0===e&&(e={});var n=b(t)?t:function(e){return function(t){return e?(0,r.pi)((0,r.pi)({},t),e):t}}(t);return n(e)}function g(e,t){return void 0===e&&(e={}),(b(t)?t:function(e){void 0===e&&(e={});return function(t){var n=(0,r.pi)({},t);for(var o in e)e.hasOwnProperty(o)&&(n[o]=(0,r.pi)((0,r.pi)({},t[o]),e[o]));return n}}(t))(e)}function b(e){return"function"===typeof e}var y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onCustomizationChange=function(){return t.forceUpdate()},t}return(0,r.ZT)(t,e),t.prototype.componentDidMount=function(){f.X.observe(this._onCustomizationChange)},t.prototype.componentWillUnmount=function(){f.X.unobserve(this._onCustomizationChange)},t.prototype.render=function(){var e=this,t=this.props.contextTransform;return i.createElement(h.i.Consumer,null,(function(n){var o=function(e,t){var n=(t||{}).customizations,o=void 0===n?{settings:{},scopedSettings:{}}:n;return{customizations:{settings:v(o.settings,e.settings),scopedSettings:g(o.scopedSettings,e.scopedSettings),inCustomizerContext:!0}}}(e.props,n);return t&&(o=t(o)),i.createElement(h.i.Provider,{value:o},e.props.children)}))},t}(i.Component),_=n(16455),C=n(17645),k=n(66587),x=(0,u.y)(),E=(0,c.NF)((function(e,t){return(0,C.j)((0,r.pi)((0,r.pi)({},e),{rtl:t}))})),I=i.forwardRef((function(e,t){var n=e.className,o=e.theme,a=e.applyTheme,s=e.applyThemeToBody,l=e.styles,u=x(l,{theme:o,applyTheme:a,className:n}),c=i.useRef(null);return function(e,t,n){var o=t.bodyThemed;i.useEffect((function(){if(e){var t=(0,_.M)(n.current);if(t)return t.body.classList.add(o),function(){t.body.classList.remove(o)}}}),[o,e,n])}(s,u,c),i.createElement(i.Fragment,null,function(e,t,n,o){var a=t.root,s=e.as,l=void 0===s?"div":s,u=e.dir,c=e.theme,f=(0,p.pq)(e,p.n7,["dir"]),h=function(e){var t=e.theme,n=e.dir,o=(0,d.zg)(t)?"rtl":"ltr",r=(0,d.zg)()?"rtl":"ltr",i=n||o;return{rootDir:i!==o||i!==r?i:n,needsTheme:i!==o}}(e),v=h.rootDir,g=h.needsTheme,b=i.createElement(m.Y,{providerRef:n},i.createElement(l,(0,r.pi)({dir:v},f,{className:a,ref:(0,k.r)(n,o)})));g&&(b=i.createElement(y,{settings:{theme:E(c,"rtl"===u)}},b));return b}(e,u,c,t))}));I.displayName="FabricBase";var w=n(40528),T={fontFamily:"inherit"},M={root:"ms-Fabric",bodyThemed:"ms-Fabric-bodyThemed"},D=(0,o.z)(I,(function(e){var t=e.applyTheme,n=e.className,o=e.preventBlanketFontInheritance,r=e.theme;return{root:[(0,w.Cn)(M,r).root,r.fonts.medium,{color:r.palette.neutralPrimary},!o&&{"& button":T,"& input":T,"& textarea":T},t&&{color:r.semanticColors.bodyText,backgroundColor:r.semanticColors.bodyBackground},n],bodyThemed:[{backgroundColor:r.semanticColors.bodyBackground}]}}),void 0,{scope:"Fabric"}),S=n(61910),P=n(96971),N=n(96758);var R,B=n(29723),F=n(48794),O=n(7211),L=(0,u.y)(),A=i.forwardRef((function(e,t){var n=function(){var e;return null!==(e=i.useContext(a))&&void 0!==e?e:s}(),o=i.useRef(null),u=(0,k.r)(o,t),c=i.useRef(),d=i.useRef(null),p=i.useContext(P.uK),f=i.useState(!1),h=f[0],v=f[1],g=i.useCallback((function(e){var t,n=!!(null===(t=null===p||void 0===p?void 0:p.providerRef)||void 0===t?void 0:t.current)&&t.current.classList.contains(S.G$);e&&n&&e.classList.add(S.G$)}),[p]),b=e.children,y=e.className,C=e.eventBubblingEnabled,x=e.fabricProps,E=e.hostId,I=e.insertFirst,w=e.onLayerDidMount,T=void 0===w?function(){}:w,M=e.onLayerMounted,A=void 0===M?function(){}:M,z=e.onLayerWillUnmount,W=e.styles,K=e.theme,q=(0,k.r)(d,null===x||void 0===x?void 0:x.ref,g),U=L(W,{theme:K,className:y,isNotHost:!E}),G=function(){null===z||void 0===z||z();var e=c.current;c.current=void 0,e&&e.parentNode&&e.parentNode.removeChild(e)},J=function(){var e,t=(0,_.M)(o.current);if(t){var n=function(e){var t,n;if(E){var o=(0,F.Sx)(E);return o?null!==(t=o.rootRef.current)&&void 0!==t?t:null:null!==(n=e.getElementById(E))&&void 0!==n?n:null}var r=(0,F.OJ)(),i=r?e.querySelector(r):null;return i||(i=(0,F.l_)(e)),i}(t);if(n){G();var r=(null!==(e=n.ownerDocument)&&void 0!==e?e:t).createElement("div");r.className=U.root,(0,N.U)(r),function(e,t){var n=e,o=t;n._virtual||(n._virtual={children:[]});var r=n._virtual.parent;if(r&&r!==t){var i=r._virtual.children.indexOf(n);i>-1&&r._virtual.children.splice(i,1)}n._virtual.parent=o||void 0,o&&(o._virtual||(o._virtual={children:[]}),o._virtual.children.push(n))}(r,o.current),I?n.insertBefore(r,n.firstChild):n.appendChild(r),c.current=r,v(!0)}}};return(0,O.L)((function(){J(),E&&(0,F.Pc)(E,J);var e=c.current?n(c.current):void 0;return function(){e&&e(),G(),E&&(0,F.tq)(E,J)}}),[E]),i.useEffect((function(){c.current&&h&&(null===A||void 0===A||A(),null===T||void 0===T||T(),v(!1))}),[h,A,T]),i.createElement("span",{className:"ms-layer",ref:u},c.current&&l.createPortal(i.createElement(m.Y,{layerRoot:!0,providerRef:q},i.createElement(D,(0,r.pi)({},!C&&function(){R||(R={},["onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOver","onMouseOut","onMouseUp","onTouchMove","onTouchStart","onTouchCancel","onTouchEnd","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onInvalid","onSubmit"].forEach((function(e){return R[e]=H})));return R}(),x,{className:(0,B.i)(U.content,null===x||void 0===x?void 0:x.className),ref:q}),b)),c.current))}));A.displayName="LayerBase";var H=function(e){e.eventPhase===Event.BUBBLING_PHASE&&"mouseenter"!==e.type&&"mouseleave"!==e.type&&"touchstart"!==e.type&&"touchend"!==e.type&&e.stopPropagation()};var z={root:"ms-Layer",rootNoHost:"ms-Layer--fixed",content:"ms-Layer-content"},W=(0,o.z)(A,(function(e){var t=e.className,n=e.isNotHost,o=e.theme,r=(0,w.Cn)(z,o);return{root:[r.root,o.fonts.medium,n&&[r.rootNoHost,{position:"fixed",zIndex:w.bR.Layer,top:0,left:0,bottom:0,right:0,visibility:"hidden"}],t],content:[r.content,{visibility:"visible"}]}}),void 0,{scope:"Layer",fields:["hostId","theme","styles"]})},48794:function(e,t,n){n.d(t,{EQ:function(){return m},OJ:function(){return f},Pc:function(){return s},Sx:function(){return u},_Y:function(){return c},l_:function(){return p},nw:function(){return d},tq:function(){return l}});var o={},r={},i="fluent-default-layer-host",a="#".concat(i);function s(e,t){o[e]||(o[e]=[]),o[e].push(t);var n=r[e];if(n)for(var i=0,a=n;i<a.length;i++){a[i].notifyLayersChanged()}}function l(e,t){var n=o[e];if(n){var i=n.indexOf(t);i>=0&&(n.splice(i,1),0===n.length&&delete o[e])}var a=r[e];if(a)for(var s=0,l=a;s<l.length;s++){l[s].notifyLayersChanged()}}function u(e){var t=r[e];return t&&t[0]||void 0}function c(e,t){(r[e]||(r[e]=[])).unshift(t)}function d(e,t){var n=r[e];if(n){var o=n.indexOf(t);o>=0&&n.splice(o,1),0===n.length&&delete r[e]}}function p(e){var t=e.createElement("div");return t.setAttribute("id",i),t.style.cssText="position:fixed;z-index:1000000",null===e||void 0===e||e.body.appendChild(t),t}function m(e){o[e]&&o[e].forEach((function(e){return e()}))}function f(){return a}},69894:function(e,t,n){n.d(t,{a:function(){return f}});var o=n(40433),r=n(75971),i=n(72791),a=n(79292),s=n(8877),l=n(71675),u=n(15863),c=(0,a.y)(),d=function(e){function t(t){var n=e.call(this,t)||this;(0,s.l)(n);var o=n.props.allowTouchBodyScroll,r=void 0!==o&&o;return n._allowTouchBodyScroll=r,n}return(0,r.ZT)(t,e),t.prototype.componentDidMount=function(){!this._allowTouchBodyScroll&&(0,l.Qp)()},t.prototype.componentWillUnmount=function(){!this._allowTouchBodyScroll&&(0,l.tG)()},t.prototype.render=function(){var e=this.props,t=e.isDarkThemed,n=e.className,o=e.theme,a=e.styles,s=(0,u.pq)(this.props,u.n7),l=c(a,{theme:o,className:n,isDark:t});return i.createElement("div",(0,r.pi)({},s,{className:l.root}))},t}(i.Component),p=n(40528),m={root:"ms-Overlay",rootDark:"ms-Overlay--dark"},f=(0,o.z)(d,(function(e){var t,n=e.className,o=e.theme,r=e.isNone,i=e.isDark,a=o.palette,s=(0,p.Cn)(m,o);return{root:[s.root,o.fonts.medium,{backgroundColor:a.whiteTranslucent40,top:0,right:0,bottom:0,left:0,position:"absolute",selectors:(t={},t[p.qJ]={border:"1px solid WindowText",opacity:0},t)},r&&{visibility:"hidden"},i&&[s.rootDark,{backgroundColor:a.blackTranslucent40}],n]}}),void 0,{scope:"Overlay"})},68416:function(e,t,n){n.d(t,{G:function(){return g}});var o=n(75971),r=n(72791),i=n(12513),a=n(16455),s=n(56500),l=n(38994),u=n(97471),c=n(9691),d=n(15863),p=n(32092),m=n(77769),f=n(66587),h=n(3141);function v(e){var t=e.originalElement,n=e.containsFocus;t&&n&&t!==(0,i.J)()&&setTimeout((function(){var e;null===(e=t.focus)||void 0===e||e.call(t)}),0)}var g=r.forwardRef((function(e,t){var n=(0,u.j)({shouldRestoreFocus:!0,enableAriaHiddenSiblings:!0},e),i=r.useRef(),g=(0,f.r)(i,t);!function(e,t){var n="true"===String(e["aria-modal"]).toLowerCase()&&e.enableAriaHiddenSiblings;r.useEffect((function(){if(n&&t.current)return(0,l.O)(t.current)}),[t,n])}(n,i),function(e,t){var n=e.onRestoreFocus,o=void 0===n?v:n,i=r.useRef(),l=r.useRef(!1);r.useEffect((function(){return i.current=(0,a.M)().activeElement,(0,s.WU)(t.current)&&(l.current=!0),function(){var e;null===o||void 0===o||o({originalElement:i.current,containsFocus:l.current,documentContainsFocus:(null===(e=(0,a.M)())||void 0===e?void 0:e.hasFocus())||!1}),i.current=void 0}}),[]),(0,m.d)(t,"focus",r.useCallback((function(){l.current=!0}),[]),!0),(0,m.d)(t,"blur",r.useCallback((function(e){t.current&&e.relatedTarget&&!t.current.contains(e.relatedTarget)&&(l.current=!1)}),[]),!0)}(n,i);var b=n.role,y=n.className,_=n.ariaLabel,C=n.ariaLabelledBy,k=n.ariaDescribedBy,x=n.style,E=n.children,I=n.onDismiss,w=function(e,t){var n=(0,p.r)(),o=r.useState(!1),i=o[0],a=o[1];return r.useEffect((function(){return n.requestAnimationFrame((function(){var n;if(!e.style||!e.style.overflowY){var o=!1;if(t&&t.current&&(null===(n=t.current)||void 0===n?void 0:n.firstElementChild)){var r=t.current.clientHeight,s=t.current.firstElementChild.clientHeight;r>0&&s>r&&(o=s-r>1)}i!==o&&a(o)}})),function(){return n.dispose()}})),i}(n,i),T=r.useCallback((function(e){if(e.which===c.m.escape)I&&(I(e),e.preventDefault(),e.stopPropagation())}),[I]),M=(0,h.zY)();return(0,m.d)(M,"keydown",T),r.createElement("div",(0,o.pi)({ref:g},(0,d.pq)(n,d.n7),{className:y,role:b,"aria-label":_,"aria-labelledby":C,"aria-describedby":k,onKeyDown:T,style:(0,o.pi)({overflowY:w?"scroll":void 0,outline:"none"},x)}),E)}));g.displayName="Popup"},97708:function(e,t,n){n.d(t,{$:function(){return v}});var o=n(40433),r=n(75971),i=n(72791),a=n(90110),s=n(79292),l=n(15863),u=n(12103),c=(0,s.y)(),d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.type,n=e.size,o=e.ariaLabel,s=e.ariaLive,d=e.styles,p=e.label,m=e.theme,f=e.className,h=e.labelPosition,v=o,g=(0,l.pq)(this.props,l.n7,["size"]),b=n;void 0===b&&void 0!==t&&(b=t===a.d.large?a.E.large:a.E.medium);var y=c(d,{theme:m,size:b,className:f,labelPosition:h});return i.createElement("div",(0,r.pi)({},g,{className:y.root}),i.createElement("div",{className:y.circle}),p&&i.createElement("div",{className:y.label},p),v&&i.createElement("div",{role:"status","aria-live":s},i.createElement(u.U,null,i.createElement("div",{className:y.screenReaderText},v))))},t.defaultProps={size:a.E.medium,ariaLive:"polite",labelPosition:"bottom"},t}(i.Component),p=n(40528),m=n(42792),f={root:"ms-Spinner",circle:"ms-Spinner-circle",label:"ms-Spinner-label"},h=(0,m.NF)((function(){return(0,p.F4)({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}})})),v=(0,o.z)(d,(function(e){var t,n=e.theme,o=e.size,i=e.className,s=e.labelPosition,l=n.palette,u=(0,p.Cn)(f,n);return{root:[u.root,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},"top"===s&&{flexDirection:"column-reverse"},"right"===s&&{flexDirection:"row"},"left"===s&&{flexDirection:"row-reverse"},i],circle:[u.circle,{boxSizing:"border-box",borderRadius:"50%",border:"1.5px solid "+l.themeLight,borderTopColor:l.themePrimary,animationName:h(),animationDuration:"1.3s",animationIterationCount:"infinite",animationTimingFunction:"cubic-bezier(.53,.21,.29,.67)",selectors:(t={},t[p.qJ]=(0,r.pi)({borderTopColor:"Highlight"},(0,p.xM)()),t)},o===a.E.xSmall&&["ms-Spinner--xSmall",{width:12,height:12}],o===a.E.small&&["ms-Spinner--small",{width:16,height:16}],o===a.E.medium&&["ms-Spinner--medium",{width:20,height:20}],o===a.E.large&&["ms-Spinner--large",{width:28,height:28}]],label:[u.label,n.fonts.small,{color:l.themePrimary,margin:"8px 0 0",textAlign:"center"},"top"===s&&{margin:"0 0 8px"},"right"===s&&{margin:"0 0 0 8px"},"left"===s&&{margin:"0 8px 0 0"}],screenReaderText:p.ul}}),void 0,{scope:"Spinner"})},90110:function(e,t,n){var o,r;n.d(t,{E:function(){return o},d:function(){return r}}),function(e){e[e.xSmall=0]="xSmall",e[e.small=1]="small",e[e.medium=2]="medium",e[e.large=3]="large"}(o||(o={})),function(e){e[e.normal=0]="normal",e[e.large=1]="large"}(r||(r={}))},59332:function(e,t,n){n.d(t,{K:function(){return C}});var o=n(75971),r=n(72791),i=n(14988),a=n(89e3),s=n(23983),l=n(15863),u=n(29723),c=n(40528),d={root:"ms-StackItem"},p={start:"flex-start",end:"flex-end"},m=function(e,t){return t.spacing.hasOwnProperty(e)?t.spacing[e]:e},f=function(e){var t=parseFloat(e),n=isNaN(t)?0:t,o=isNaN(t)?"":t.toString();return{value:n,unit:e.substring(o.toString().length)||"px"}},h=function(e,t){if(void 0===e||"number"===typeof e||""===e)return e;var n=e.split(" ");return n.length<2?m(e,t):n.reduce((function(e,n){return m(e,t)+" "+m(n,t)}))},v={start:"flex-start",end:"flex-end"},g={root:"ms-Stack",inner:"ms-Stack-inner",child:"ms-Stack-child"},b=(0,a.L)((function(e){var t=e.children,n=(0,l.pq)(e,l.iY);if(null==t)return null;var r=(0,i.FJ)(e,{root:"div"});return(0,i.Yb)(r.root,(0,o.pi)({},n),t)}),{displayName:"StackItem",styles:function(e,t,n){var o=e.grow,r=e.shrink,i=e.disableShrink,a=e.align,s=e.verticalFill,l=e.order,u=e.className,m=e.basis,f=void 0===m?"auto":m,h=(0,c.Cn)(d,t);return{root:[t.fonts.medium,h.root,{flexBasis:f,margin:n.margin,padding:n.padding,height:s?"100%":"auto",width:"auto"},o&&{flexGrow:!0===o?1:o},(i||!o&&!r)&&{flexShrink:0},r&&!i&&{flexShrink:1},a&&{alignSelf:p[a]||a},l&&{order:l},u]}}});function y(e,t){var n=t.disableShrink,i=t.enableScopedSelectors,a=t.doNotRenderFalsyValues,s=r.Children.toArray(e);return s=r.Children.map(s,(function(e){if(!e)return a?null:e;if(!r.isValidElement(e))return e;if(e.type===r.Fragment)return e.props.children?y(e.props.children,{disableShrink:n,enableScopedSelectors:i,doNotRenderFalsyValues:a}):null;var t,s=e,l={};(t=e)&&"object"===typeof t&&t.type&&t.type.displayName===b.displayName&&(l={shrink:!n});var c=s.props.className;return r.cloneElement(s,(0,o.pi)((0,o.pi)((0,o.pi)((0,o.pi)({},l),s.props),c&&{className:c}),i&&{className:(0,u.i)(g.child,c)}))}))}var _={Item:b},C=(0,a.L)((function(e){var t=e.as,n=void 0===t?"div":t,r=e.disableShrink,a=void 0!==r&&r,u=e.doNotRenderFalsyValues,c=void 0!==u&&u,d=e.enableScopedSelectors,p=void 0!==d&&d,m=e.wrap,f=(0,o._T)(e,["as","disableShrink","doNotRenderFalsyValues","enableScopedSelectors","wrap"]);(0,s.b)("Stack",e,{gap:"tokens.childrenGap",maxHeight:"tokens.maxHeight",maxWidth:"tokens.maxWidth",padding:"tokens.padding"});var h=y(e.children,{disableShrink:a,enableScopedSelectors:p,doNotRenderFalsyValues:c}),v=(0,l.pq)(f,l.iY),g=(0,i.FJ)(e,{root:n,inner:"div"});return m?(0,i.Yb)(g.root,(0,o.pi)({},v),(0,i.Yb)(g.inner,null,h)):(0,i.Yb)(g.root,(0,o.pi)({},v),h)}),{displayName:"Stack",styles:function(e,t,n){var r,i,a,s,l,u,p,b,y,_,C,k,x,E=e.className,I=e.disableShrink,w=e.enableScopedSelectors,T=e.grow,M=e.horizontal,D=e.horizontalAlign,S=e.reversed,P=e.verticalAlign,N=e.verticalFill,R=e.wrap,B=(0,c.Cn)(g,t),F=n&&n.childrenGap?n.childrenGap:e.gap,O=n&&n.maxHeight?n.maxHeight:e.maxHeight,L=n&&n.maxWidth?n.maxWidth:e.maxWidth,A=n&&n.padding?n.padding:e.padding,H=function(e,t){if(void 0===e||""===e)return{rowGap:{value:0,unit:"px"},columnGap:{value:0,unit:"px"}};if("number"===typeof e)return{rowGap:{value:e,unit:"px"},columnGap:{value:e,unit:"px"}};var n=e.split(" ");if(n.length>2)return{rowGap:{value:0,unit:"px"},columnGap:{value:0,unit:"px"}};if(2===n.length)return{rowGap:f(m(n[0],t)),columnGap:f(m(n[1],t))};var o=f(m(e,t));return{rowGap:o,columnGap:o}}(F,t),z=H.rowGap,W=H.columnGap,K="".concat(-.5*W.value).concat(W.unit),q="".concat(-.5*z.value).concat(z.unit),U={textOverflow:"ellipsis"},G="> "+(w?"."+g.child:"*"),J=((r={})["".concat(G,":not(.").concat(d.root,")")]={flexShrink:0},r);return R?{root:[B.root,{flexWrap:"wrap",maxWidth:L,maxHeight:O,width:"auto",overflow:"visible",height:"100%"},D&&(i={},i[M?"justifyContent":"alignItems"]=v[D]||D,i),P&&(a={},a[M?"alignItems":"justifyContent"]=v[P]||P,a),E,{display:"flex"},M&&{height:N?"100%":"auto"}],inner:[B.inner,(s={display:"flex",flexWrap:"wrap",marginLeft:K,marginRight:K,marginTop:q,marginBottom:q,overflow:"visible",boxSizing:"border-box",padding:h(A,t),width:0===W.value?"100%":"calc(100% + ".concat(W.value).concat(W.unit,")"),maxWidth:"100vw"},s[G]=(0,o.pi)({margin:"".concat(.5*z.value).concat(z.unit," ").concat(.5*W.value).concat(W.unit)},U),s),I&&J,D&&(l={},l[M?"justifyContent":"alignItems"]=v[D]||D,l),P&&(u={},u[M?"alignItems":"justifyContent"]=v[P]||P,u),M&&(p={flexDirection:S?"row-reverse":"row",height:0===z.value?"100%":"calc(100% + ".concat(z.value).concat(z.unit,")")},p[G]={maxWidth:0===W.value?"100%":"calc(100% - ".concat(W.value).concat(W.unit,")")},p),!M&&(b={flexDirection:S?"column-reverse":"column",height:"calc(100% + ".concat(z.value).concat(z.unit,")")},b[G]={maxHeight:0===z.value?"100%":"calc(100% - ".concat(z.value).concat(z.unit,")")},b)]}:{root:[B.root,(y={display:"flex",flexDirection:M?S?"row-reverse":"row":S?"column-reverse":"column",flexWrap:"nowrap",width:"auto",height:N?"100%":"auto",maxWidth:L,maxHeight:O,padding:h(A,t),boxSizing:"border-box"},y[G]=U,y),I&&J,T&&{flexGrow:!0===T?1:T},D&&(_={},_[M?"justifyContent":"alignItems"]=v[D]||D,_),P&&(C={},C[M?"alignItems":"justifyContent"]=v[P]||P,C),M&&W.value>0&&(k={},k["".concat(G,S?":not(:last-child)":":not(:first-child)")]={marginLeft:"".concat(W.value).concat(W.unit)},k),!M&&z.value>0&&(x={},x["".concat(G,S?":not(:last-child)":":not(:first-child)")]={marginTop:"".concat(z.value).concat(z.unit)},x),E]}},statics:_})},4026:function(e,t,n){n.d(t,{x:function(){return s}});var o=n(89e3),r=n(75971),i=n(14988),a=n(15863),s=(0,o.L)((function(e){if(null==e.children)return null;e.block,e.className;var t=e.as,n=void 0===t?"span":t,o=(e.variant,e.nowrap,(0,r._T)(e,["block","className","as","variant","nowrap"])),s=(0,i.FJ)(e,{root:n});return(0,i.Yb)(s.root,(0,r.pi)({},(0,a.pq)(o,a.iY)))}),{displayName:"Text",styles:function(e,t){var n=e.as,o=e.className,r=e.block,i=e.nowrap,a=e.variant,s=t.fonts,l=t.semanticColors,u=s[a||"medium"];return{root:[u,{color:u.color||l.bodyText,display:r?"td"===n?"table-cell":"block":"inline",mozOsxFontSmoothing:u.MozOsxFontSmoothing,webkitFontSmoothing:u.WebkitFontSmoothing},i&&{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},o]}}})},93657:function(e,t,n){n.d(t,{G:function(){return I}});var o=n(40433),r=n(75971),i=n(72791),a=n(40528),s=n(79292),l=n(91106);var u,c=n(1509),d=n(9691),p=n(8877),m=n(68959),f=n(35726),h=n(15863);!function(e){e[e.Parent=0]="Parent",e[e.Self=1]="Self"}(u||(u={}));var v,g=n(87023),b=n(81080),y=(0,s.y)(),_=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onRenderContent=function(e){return"string"===typeof e.content?i.createElement("p",{className:t._classNames.subText},e.content):i.createElement("div",{className:t._classNames.subText},e.content)},t}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.className,n=e.calloutProps,o=e.directionalHint,a=e.directionalHintForRTL,s=e.styles,l=e.id,u=e.maxWidth,c=e.onRenderContent,d=void 0===c?this._onRenderContent:c,p=e.targetElement,m=e.theme;return this._classNames=y(s,{theme:m,className:t||n&&n.className,beakWidth:n&&n.isBeakVisible?n.beakWidth:0,gapSpace:n&&n.gapSpace,maxWidth:u}),i.createElement(g.U,(0,r.pi)({target:p,directionalHint:o,directionalHintForRTL:a},n,(0,h.pq)(this.props,h.n7,["id"]),{className:this._classNames.root}),i.createElement("div",{className:this._classNames.content,id:l,onFocus:this.props.onFocus,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},d(this.props,this._onRenderContent)))},t.defaultProps={directionalHint:b.b.topCenter,maxWidth:"364px",calloutProps:{isBeakVisible:!0,beakWidth:16,gapSpace:0,setInitialFocus:!0,doNotLayer:!1}},t}(i.Component),C=(0,o.z)(_,(function(e){var t=e.className,n=e.beakWidth,o=void 0===n?16:n,r=e.gapSpace,i=void 0===r?0:r,s=e.maxWidth,l=e.theme,u=l.semanticColors,c=l.fonts,d=l.effects,p=-(Math.sqrt(o*o/2)+i)+1/window.devicePixelRatio;return{root:["ms-Tooltip",l.fonts.medium,a.k4.fadeIn200,{background:u.menuBackground,boxShadow:d.elevation8,padding:"8px",maxWidth:s,selectors:{":after":{content:"''",position:"absolute",bottom:p,left:p,right:p,top:p,zIndex:0}}},t],content:["ms-Tooltip-content",c.small,{position:"relative",zIndex:1,color:u.menuItemText,wordWrap:"break-word",overflowWrap:"break-word",overflow:"hidden"}],subText:["ms-Tooltip-subtext",{fontSize:"inherit",fontWeight:"inherit",color:"inherit",margin:0}]}}),void 0,{scope:"Tooltip"});!function(e){e[e.zero=0]="zero",e[e.medium=1]="medium",e[e.long=2]="long"}(v||(v={}));var k=(0,s.y)(),x=function(e){function t(n){var o=e.call(this,n)||this;return o._tooltipHost=i.createRef(),o._defaultTooltipId=(0,l.z)("tooltip"),o.show=function(){o._toggleTooltip(!0)},o.dismiss=function(){o._hideTooltip()},o._getTargetElement=function(){if(o._tooltipHost.current){var e=o.props.overflowMode;if(void 0!==e)switch(e){case u.Parent:return o._tooltipHost.current.parentElement;case u.Self:return o._tooltipHost.current}return o._tooltipHost.current}},o._onTooltipFocus=function(e){o._ignoreNextFocusEvent?o._ignoreNextFocusEvent=!1:o._onTooltipMouseEnter(e)},o._onTooltipContentFocus=function(e){t._currentVisibleTooltip&&t._currentVisibleTooltip!==o&&t._currentVisibleTooltip.dismiss(),t._currentVisibleTooltip=o,o._clearDismissTimer(),o._clearOpenTimer()},o._onTooltipBlur=function(e){o._ignoreNextFocusEvent=(null===document||void 0===document?void 0:document.activeElement)===e.target,o._dismissTimerId=o._async.setTimeout((function(){o._hideTooltip()}),0)},o._onTooltipMouseEnter=function(e){var n,r=o.props,i=r.overflowMode,a=r.delay;if(t._currentVisibleTooltip&&t._currentVisibleTooltip!==o&&t._currentVisibleTooltip.dismiss(),t._currentVisibleTooltip=o,void 0!==i){var s=o._getTargetElement();if(s&&(!function(e){return e.clientWidth<e.scrollWidth}(n=s)&&!function(e){return e.clientHeight<e.scrollHeight}(n)))return}if(!e.target||!(0,c.w)(e.target,o._getTargetElement()))if(o._clearDismissTimer(),o._clearOpenTimer(),a!==v.zero){var l=o._getDelayTime(a);o._openTimerId=o._async.setTimeout((function(){o._toggleTooltip(!0)}),l)}else o._toggleTooltip(!0)},o._onTooltipMouseLeave=function(e){var n=o.props.closeDelay;o._clearDismissTimer(),o._clearOpenTimer(),n?o._dismissTimerId=o._async.setTimeout((function(){o._toggleTooltip(!1)}),n):o._toggleTooltip(!1),t._currentVisibleTooltip===o&&(t._currentVisibleTooltip=void 0)},o._onTooltipKeyDown=function(e){(e.which===d.m.escape||e.ctrlKey)&&o.state.isTooltipVisible&&(o._hideTooltip(),e.stopPropagation())},o._clearDismissTimer=function(){o._async.clearTimeout(o._dismissTimerId)},o._clearOpenTimer=function(){o._async.clearTimeout(o._openTimerId)},o._hideTooltip=function(){o._clearOpenTimer(),o._clearDismissTimer(),o._toggleTooltip(!1)},o._toggleTooltip=function(e){o.state.isTooltipVisible!==e&&o.setState({isTooltipVisible:e},(function(){return o.props.onTooltipToggle&&o.props.onTooltipToggle(e)}))},o._getDelayTime=function(e){switch(e){case v.medium:return 300;case v.long:return 500;default:return 0}},(0,p.l)(o),o.state={isAriaPlaceholderRendered:!1,isTooltipVisible:!1},o._async=new m.e(o),o}return(0,r.ZT)(t,e),t.prototype.render=function(){var e=this.props,t=e.calloutProps,n=e.children,o=e.content,s=e.directionalHint,l=e.directionalHintForRTL,u=e.hostClassName,c=e.id,d=e.setAriaDescribedBy,p=void 0===d||d,m=e.tooltipProps,v=e.styles,g=e.theme;this._classNames=k(v,{theme:g,className:u});var b=this.state.isTooltipVisible,y=c||this._defaultTooltipId,_=(0,r.pi)((0,r.pi)({id:"".concat(y,"--tooltip"),content:o,targetElement:this._getTargetElement(),directionalHint:s,directionalHintForRTL:l,calloutProps:(0,f.f0)({},t,{onDismiss:this._hideTooltip,onFocus:this._onTooltipContentFocus,onMouseEnter:this._onTooltipMouseEnter,onMouseLeave:this._onTooltipMouseLeave}),onMouseEnter:this._onTooltipMouseEnter,onMouseLeave:this._onTooltipMouseLeave},(0,h.pq)(this.props,h.n7,["id"])),m),x=(null===m||void 0===m?void 0:m.onRenderContent)?m.onRenderContent(_,(function(e){return(null===e||void 0===e?void 0:e.content)?i.createElement(i.Fragment,null,e.content):null})):o,E=b&&!!x,I=p&&b&&x?y:void 0;return i.createElement("div",(0,r.pi)({className:this._classNames.root,ref:this._tooltipHost},{onFocusCapture:this._onTooltipFocus},{onBlurCapture:this._onTooltipBlur},{onMouseEnter:this._onTooltipMouseEnter,onMouseLeave:this._onTooltipMouseLeave,onKeyDown:this._onTooltipKeyDown,role:"none","aria-describedby":I}),n,E&&i.createElement(C,(0,r.pi)({},_)),i.createElement("div",{hidden:!0,id:y,style:a.ul},x))},t.prototype.componentWillUnmount=function(){t._currentVisibleTooltip&&t._currentVisibleTooltip===this&&(t._currentVisibleTooltip=void 0),this._async.dispose()},t.defaultProps={delay:v.medium},t}(i.Component),E={root:"ms-TooltipHost",ariaPlaceholder:"ms-TooltipHost-aria-placeholder"},I=(0,o.z)(x,(function(e){var t=e.className,n=e.theme;return{root:[(0,a.Cn)(E,n).root,{display:"inline"},t]}}),void 0,{scope:"TooltipHost"})},91381:function(e,t,n){n.d(t,{K7:function(){return s},eD:function(){return o},tc:function(){return u}});var o;n(72791);!function(e){e[e.small=0]="small",e[e.medium=1]="medium",e[e.large=2]="large",e[e.xLarge=3]="xLarge",e[e.xxLarge=4]="xxLarge",e[e.xxxLarge=5]="xxxLarge",e[e.unknown=999]="unknown"}(o||(o={}));var r,i,a=[479,639,1023,1365,1919,99999999];function s(){var e;return null!==(e=null!==r&&void 0!==r?r:i)&&void 0!==e?e:o.large}function l(e){try{return e.document.documentElement.clientWidth}catch(t){return e.innerWidth}}function u(e){var t=o.small;if(e){try{for(;l(e)>a[t];)t++}catch(n){t=s()}i=t}else{if(void 0===r)throw new Error("Content was rendered in a server environment without providing a default responsive mode. Call setResponsiveMode to define what the responsive mode is.");t=r}return t}},98687:function(e,t,n){n.d(t,{q:function(){return l}});var o=n(72791),r=n(12513),i=n(77769),a=n(91381),s=n(3141),l=function(e,t){var n=o.useState((0,a.K7)()),l=n[0],u=n[1],c=o.useCallback((function(){var t=(0,a.tc)((0,r.J)(e.current));l!==t&&u(t)}),[e,l]),d=(0,s.zY)();return(0,i.d)(d,"resize",c),o.useEffect((function(){void 0===t&&c()}),[t]),null!==t&&void 0!==t?t:l}},54911:function(e,t,n){n.d(t,{Tj:function(){return o},by:function(){return i},fV:function(){return a},ms:function(){return s},nK:function(){return l},ww:function(){return r}});var o,r="ktp",i="-",a="data-ktp-target",s="data-ktp-execute-target",l="ktp-layer-id";!function(e){e.KEYTIP_ADDED="keytipAdded",e.KEYTIP_REMOVED="keytipRemoved",e.KEYTIP_UPDATED="keytipUpdated",e.PERSISTED_KEYTIP_ADDED="persistedKeytipAdded",e.PERSISTED_KEYTIP_REMOVED="persistedKeytipRemoved",e.PERSISTED_KEYTIP_EXECUTE="persistedKeytipExecute",e.ENTER_KEYTIP_MODE="enterKeytipMode",e.EXIT_KEYTIP_MODE="exitKeytipMode"}(o||(o={}))},7796:function(e,t,n){n.d(t,{K:function(){return s}});var o=n(75971),r=n(92853),i=n(91106),a=n(54911),s=function(){function e(){this.keytips={},this.persistedKeytips={},this.sequenceMapping={},this.inKeytipMode=!1,this.shouldEnterKeytipMode=!0,this.delayUpdatingKeytipChange=!1}return e.getInstance=function(){return this._instance},e.prototype.init=function(e){this.delayUpdatingKeytipChange=e},e.prototype.register=function(e,t){void 0===t&&(t=!1);var n=e;t||(n=this.addParentOverflow(e),this.sequenceMapping[n.keySequences.toString()]=n);var o=this._getUniqueKtp(n);if(t?this.persistedKeytips[o.uniqueID]=o:this.keytips[o.uniqueID]=o,this.inKeytipMode||!this.delayUpdatingKeytipChange){var i=t?a.Tj.PERSISTED_KEYTIP_ADDED:a.Tj.KEYTIP_ADDED;r.r.raise(this,i,{keytip:n,uniqueID:o.uniqueID})}return o.uniqueID},e.prototype.update=function(e,t){var n=this.addParentOverflow(e),o=this._getUniqueKtp(n,t),i=this.keytips[t];i&&(o.keytip.visible=i.keytip.visible,this.keytips[t]=o,delete this.sequenceMapping[i.keytip.keySequences.toString()],this.sequenceMapping[o.keytip.keySequences.toString()]=o.keytip,!this.inKeytipMode&&this.delayUpdatingKeytipChange||r.r.raise(this,a.Tj.KEYTIP_UPDATED,{keytip:o.keytip,uniqueID:o.uniqueID}))},e.prototype.unregister=function(e,t,n){void 0===n&&(n=!1),n?delete this.persistedKeytips[t]:delete this.keytips[t],!n&&delete this.sequenceMapping[e.keySequences.toString()];var o=n?a.Tj.PERSISTED_KEYTIP_REMOVED:a.Tj.KEYTIP_REMOVED;!this.inKeytipMode&&this.delayUpdatingKeytipChange||r.r.raise(this,o,{keytip:e,uniqueID:t})},e.prototype.enterKeytipMode=function(){r.r.raise(this,a.Tj.ENTER_KEYTIP_MODE)},e.prototype.exitKeytipMode=function(){r.r.raise(this,a.Tj.EXIT_KEYTIP_MODE)},e.prototype.getKeytips=function(){var e=this;return Object.keys(this.keytips).map((function(t){return e.keytips[t].keytip}))},e.prototype.addParentOverflow=function(e){var t=(0,o.ev)([],e.keySequences,!0);if(t.pop(),0!==t.length){var n=this.sequenceMapping[t.toString()];if(n&&n.overflowSetSequence)return(0,o.pi)((0,o.pi)({},e),{overflowSetSequence:n.overflowSetSequence})}return e},e.prototype.menuExecute=function(e,t){r.r.raise(this,a.Tj.PERSISTED_KEYTIP_EXECUTE,{overflowButtonSequences:e,keytipSequences:t})},e.prototype._getUniqueKtp=function(e,t){return void 0===t&&(t=(0,i.z)()),{keytip:(0,o.pi)({},e),uniqueID:t}},e._instance=new e,e}()},50899:function(e,t,n){var o,r;n.d(t,{L:function(){return r},z:function(){return o}}),function(e){e[e.top=1]="top",e[e.bottom=-1]="bottom",e[e.left=2]="left",e[e.right=-2]="right"}(o||(o={})),function(e){e[e.top=0]="top",e[e.bottom=1]="bottom",e[e.start=2]="start",e[e.end=3]="end"}(r||(r={}))},89e3:function(e,t,n){n.d(t,{L:function(){return c}});var o=n(75971),r=n(72791),i=n(40528),a=n(72557),s=n(19101),l=n(14988),u=n(87865);function c(e,t){void 0===t&&(t={});var n=t.factoryOptions,c=(void 0===n?{}:n).defaultProp,p=function(n){var l=function(e,t,n){var o=["theme","styles","tokens"];return s.X.getSettings(n||o,e,t.customizations)}(t.displayName,r.useContext(a.i),t.fields),u=t.state;u&&(n=(0,o.pi)((0,o.pi)({},n),u(n)));var c=n.theme||l.theme,p=d(n,c,t.tokens,l.tokens,n.tokens),m=function(e,t,n){for(var o=[],r=3;r<arguments.length;r++)o[r-3]=arguments[r];return i.E$.apply(void 0,o.map((function(o){return"function"===typeof o?o(e,t,n):o})))}(n,c,p,t.styles,l.styles,n.styles),f=(0,o.pi)((0,o.pi)({},n),{styles:m,tokens:p,_defaultStyles:m,theme:c});return e(f)};return p.displayName=t.displayName||e.name,c&&(p.create=(0,l.sw)(p,{defaultProp:c})),(0,u.f)(p,t.statics),p}function d(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];for(var i={},a=0,s=n;a<s.length;a++){var l=s[a];l&&(l="function"===typeof l?l(e,t):l,Array.isArray(l)&&(l=d.apply(void 0,(0,o.ev)([e,t],l,!1))),(0,u.f)(i,l))}return i}},14988:function(e,t,n){var o;n.d(t,{FJ:function(){return m},Yb:function(){return c},sw:function(){return d}});var r=n(75971),i=n(72791),a=n(18323),s=n(42792),l=n(87376),u=n(87865);function c(e,t){for(var a=[],s=2;s<arguments.length;s++)a[s-2]=arguments[s];var l=e;return l.isSlot?0===(a=i.Children.toArray(a)).length?l(t):l((0,r.pi)((0,r.pi)({},t),{children:a})):i.createElement.apply(o||(o=n.t(i,2)),(0,r.ev)([e,t],a,!1))}function d(e,t){void 0===t&&(t={});var n=t.defaultProp,o=void 0===n?"children":n;return function(t,n,s,c,d){if(i.isValidElement(n))return n;var p=function(e,t){var n,o;"string"===typeof t||"number"===typeof t||"boolean"===typeof t?((n={})[e]=t,o=n):o=t;return o}(o,n),m=function(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];for(var r={},i=[],s=0,c=n;s<c.length;s++){var d=c[s];i.push(d&&d.className),(0,u.f)(r,d)}return r.className=(0,a.R)([e,i],{rtl:(0,l.zg)(t)}),r}(c,d,t,p);if(s){if(s.component){var f=s.component;return i.createElement(f,(0,r.pi)({},m))}if(s.render)return s.render(m,e)}return i.createElement(e,(0,r.pi)({},m))}}var p=(0,s.NF)((function(e){return d(e)}));function m(e,t){var n={},o=e,r=function(e){if(t.hasOwnProperty(e)){var r=function(n){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];if(r.length>0)throw new Error("Any module using getSlots must use withSlots. Please see withSlots javadoc for more info.");return function(e,t,n,o,r,i){return void 0!==e.create?e.create(t,n,o,r):p(e)(t,n,o,r,i)}(t[e],n,o[e],o.slots&&o.slots[e],o._defaultStyles&&o._defaultStyles[e],o.theme)};r.isSlot=!0,n[e]=r}};for(var i in t)r(i);return n}},87865:function(e,t,n){n.d(t,{f:function(){return o}});var o=n(75971).pi},65260:function(e,t,n){n.d(t,{k:function(){return B}});var o,r=n(75971),i=n(72791),a=n(78498),s=n(6475),l=n(56500),u=n(65141),c=n(9691),d=n(87376),p=n(8877),m=n(91106),f=n(84688),h=n(15863),v=n(29723),g=n(71675),b=n(1509),y=n(16455),_=n(18323),C=n(40528),k="data-is-focusable",x="data-focuszone-id",E="tabindex",I="data-no-vertical-wrap",w="data-no-horizontal-wrap",T=999999999,M=-999999999;function D(e,t){var n;"function"===typeof MouseEvent?n=new MouseEvent("click",{ctrlKey:null===t||void 0===t?void 0:t.ctrlKey,metaKey:null===t||void 0===t?void 0:t.metaKey,shiftKey:null===t||void 0===t?void 0:t.shiftKey,altKey:null===t||void 0===t?void 0:t.altKey,bubbles:null===t||void 0===t?void 0:t.bubbles,cancelable:null===t||void 0===t?void 0:t.cancelable}):(n=document.createEvent("MouseEvents")).initMouseEvent("click",!!t&&t.bubbles,!!t&&t.cancelable,window,0,0,0,0,0,!!t&&t.ctrlKey,!!t&&t.altKey,!!t&&t.shiftKey,!!t&&t.metaKey,0,null),e.dispatchEvent(n)}var S={},P=new Set,N=["text","number","password","email","tel","url","search","textarea"],R=!1,B=function(e){function t(n){var o,r,f,h,v=this;(v=e.call(this,n)||this)._root=i.createRef(),v._mergedRef=(0,s.S)(),v._onFocus=function(e){if(!v._portalContainsElement(e.target)){var t,n=v.props,o=n.onActiveElementChanged,r=n.doNotAllowFocusEventToPropagate,i=n.stopFocusPropagation,a=n.onFocusNotification,s=n.onFocus,c=n.shouldFocusInnerElementWhenReceivedFocus,d=n.defaultTabbableElement,p=v._isImmediateDescendantOfZone(e.target);if(p)t=e.target;else for(var m=e.target;m&&m!==v._root.current;){if((0,l.MW)(m)&&v._isImmediateDescendantOfZone(m)){t=m;break}m=(0,u.G)(m,R)}if(c&&e.target===v._root.current){var f=d&&"function"===typeof d&&v._root.current&&d(v._root.current);f&&(0,l.MW)(f)?(t=f,f.focus()):(v.focus(!0),v._activeElement&&(t=null))}var h=!v._activeElement;t&&t!==v._activeElement&&((p||h)&&v._setFocusAlignment(t,!0,!0),v._activeElement=t,h&&v._updateTabIndexes()),o&&o(v._activeElement,e),(i||r)&&e.stopPropagation(),s?s(e):a&&a()}},v._onBlur=function(){v._setParkedFocus(!1)},v._onMouseDown=function(e){if(!v._portalContainsElement(e.target)&&!v.props.disabled){for(var t=e.target,n=[];t&&t!==v._root.current;)n.push(t),t=(0,u.G)(t,R);for(;n.length&&((t=n.pop())&&(0,l.MW)(t)&&v._setActiveElement(t,!0),!(0,l.jz)(t)););}},v._onKeyDown=function(e,t){if(!v._portalContainsElement(e.target)){var n=v.props,o=n.direction,r=n.disabled,i=n.isInnerZoneKeystroke,s=n.pagingSupportDisabled,u=n.shouldEnterInnerZone;if(!r&&(v.props.onKeyDown&&v.props.onKeyDown(e),!e.isDefaultPrevented()&&(v._getDocument().activeElement!==v._root.current||!v._isInnerZone))){if((u&&u(e)||i&&i(e))&&v._isImmediateDescendantOfZone(e.target)){var p=v._getFirstInnerZone();if(p){if(!p.focus(!0))return}else{if(!(0,l.gc)(e.target))return;if(!v.focusElement((0,l.dc)(e.target,e.target.firstChild,!0)))return}}else{if(e.altKey)return;switch(e.which){case c.m.space:if(v._shouldRaiseClicksOnSpace&&v._tryInvokeClickForFocusable(e.target,e))break;return;case c.m.left:if(o!==a.U.vertical&&(v._preventDefaultWhenHandled(e),v._moveFocusLeft(t)))break;return;case c.m.right:if(o!==a.U.vertical&&(v._preventDefaultWhenHandled(e),v._moveFocusRight(t)))break;return;case c.m.up:if(o!==a.U.horizontal&&(v._preventDefaultWhenHandled(e),v._moveFocusUp()))break;return;case c.m.down:if(o!==a.U.horizontal&&(v._preventDefaultWhenHandled(e),v._moveFocusDown()))break;return;case c.m.pageDown:if(!s&&v._moveFocusPaging(!0))break;return;case c.m.pageUp:if(!s&&v._moveFocusPaging(!1))break;return;case c.m.tab:if(v.props.allowTabKey||v.props.handleTabKey===a.J.all||v.props.handleTabKey===a.J.inputOnly&&v._isElementInput(e.target)){var m=!1;if(v._processingTabKey=!0,o!==a.U.vertical&&v._shouldWrapFocus(v._activeElement,w))m=((0,d.zg)(t)?!e.shiftKey:e.shiftKey)?v._moveFocusLeft(t):v._moveFocusRight(t);else m=e.shiftKey?v._moveFocusUp():v._moveFocusDown();if(v._processingTabKey=!1,m)break;v.props.shouldResetActiveElementWhenTabFromZone&&(v._activeElement=null)}return;case c.m.home:if(v._isContentEditableElement(e.target)||v._isElementInput(e.target)&&!v._shouldInputLoseFocus(e.target,!1))return!1;var f=v._root.current&&v._root.current.firstChild;if(v._root.current&&f&&v.focusElement((0,l.dc)(v._root.current,f,!0)))break;return;case c.m.end:if(v._isContentEditableElement(e.target)||v._isElementInput(e.target)&&!v._shouldInputLoseFocus(e.target,!0))return!1;var h=v._root.current&&v._root.current.lastChild;if(v._root.current&&v.focusElement((0,l.TD)(v._root.current,h,!0,!0,!0)))break;return;case c.m.enter:if(v._shouldRaiseClicksOnEnter&&v._tryInvokeClickForFocusable(e.target,e))break;return;default:return}}e.preventDefault(),e.stopPropagation()}}},v._getHorizontalDistanceFromCenter=function(e,t,n){var o=v._focusAlignment.left||v._focusAlignment.x||0,r=Math.floor(n.top),i=Math.floor(t.bottom),a=Math.floor(n.bottom),s=Math.floor(t.top);return e&&r>i||!e&&a<s?o>=n.left&&o<=n.left+n.width?0:Math.abs(n.left+n.width/2-o):v._shouldWrapFocus(v._activeElement,I)?T:M},(0,p.l)(v),v._id=(0,m.z)("FocusZone"),v._focusAlignment={left:0,top:0},v._processingTabKey=!1;var g=null===(r=null!==(o=n.shouldRaiseClicks)&&void 0!==o?o:t.defaultProps.shouldRaiseClicks)||void 0===r||r;return v._shouldRaiseClicksOnEnter=null!==(f=n.shouldRaiseClicksOnEnter)&&void 0!==f?f:g,v._shouldRaiseClicksOnSpace=null!==(h=n.shouldRaiseClicksOnSpace)&&void 0!==h?h:g,v}return(0,r.ZT)(t,e),t.getOuterZones=function(){return P.size},t._onKeyDownCapture=function(e){e.which===c.m.tab&&P.forEach((function(e){return e._updateTabIndexes()}))},t.prototype.componentDidMount=function(){var e=this._root.current;if(S[this._id]=this,e){for(var n=(0,u.G)(e,R);n&&n!==this._getDocument().body&&1===n.nodeType;){if((0,l.jz)(n)){this._isInnerZone=!0;break}n=(0,u.G)(n,R)}this._isInnerZone||(P.add(this),this._root.current&&this._root.current.addEventListener("keydown",t._onKeyDownCapture,!0)),this._root.current&&this._root.current.addEventListener("blur",this._onBlur,!0),this._updateTabIndexes(),this.props.defaultTabbableElement&&"string"===typeof this.props.defaultTabbableElement?this._activeElement=this._getDocument().querySelector(this.props.defaultTabbableElement):this.props.defaultActiveElement&&(this._activeElement=this._getDocument().querySelector(this.props.defaultActiveElement)),this.props.shouldFocusOnMount&&this.focus()}},t.prototype.componentDidUpdate=function(){var e=this._root.current,t=this._getDocument();if((this._activeElement&&!(0,f.t)(this._root.current,this._activeElement,R)||this._defaultFocusElement&&!(0,f.t)(this._root.current,this._defaultFocusElement,R))&&(this._activeElement=null,this._defaultFocusElement=null,this._updateTabIndexes()),!this.props.preventFocusRestoration&&t&&this._lastIndexPath&&(t.activeElement===t.body||null===t.activeElement||t.activeElement===e)){var n=(0,l.bF)(e,this._lastIndexPath);n?(this._setActiveElement(n,!0),n.focus(),this._setParkedFocus(!1)):this._setParkedFocus(!0)}},t.prototype.componentWillUnmount=function(){delete S[this._id],this._isInnerZone||(P.delete(this),this._root.current&&this._root.current.removeEventListener("keydown",t._onKeyDownCapture,!0)),this._root.current&&this._root.current.removeEventListener("blur",this._onBlur,!0),this._activeElement=null,this._defaultFocusElement=null},t.prototype.render=function(){var e=this,t=this.props,n=t.as,a=t.elementType,s=t.rootProps,l=t.ariaDescribedBy,u=t.ariaLabelledBy,c=t.className,d=(0,h.pq)(this.props,h.iY),p=n||a||"div";this._evaluateFocusBeforeRender();var m=(0,C.gh)();return i.createElement(p,(0,r.pi)({"aria-labelledby":u,"aria-describedby":l},d,s,{className:(0,v.i)((o||(o=(0,_.y)({selectors:{":focus":{outline:"none"}}},"ms-FocusZone")),o),c),ref:this._mergedRef(this.props.elementRef,this._root),"data-focuszone-id":this._id,onKeyDown:function(t){return e._onKeyDown(t,m)},onFocus:this._onFocus,onMouseDownCapture:this._onMouseDown}),this.props.children)},t.prototype.focus=function(e,t){if(void 0===e&&(e=!1),void 0===t&&(t=!1),this._root.current){if(!e&&"true"===this._root.current.getAttribute(k)&&this._isInnerZone){var n=this._getOwnerZone(this._root.current);if(n!==this._root.current){var o=S[n.getAttribute(x)];return!!o&&o.focusElement(this._root.current)}return!1}if(!e&&this._activeElement&&(0,f.t)(this._root.current,this._activeElement)&&(0,l.MW)(this._activeElement)&&(!t||(0,l.BS)(this._activeElement)))return this._activeElement.focus(),!0;var r=this._root.current.firstChild;return this.focusElement((0,l.dc)(this._root.current,r,!0,void 0,void 0,void 0,void 0,void 0,t))}return!1},t.prototype.focusLast=function(){if(this._root.current){var e=this._root.current&&this._root.current.lastChild;return this.focusElement((0,l.TD)(this._root.current,e,!0,!0,!0))}return!1},t.prototype.focusElement=function(e,t){var n=this.props,o=n.onBeforeFocus,r=n.shouldReceiveFocus;return!(r&&!r(e)||o&&!o(e))&&(!!e&&(this._setActiveElement(e,t),this._activeElement&&this._activeElement.focus(),!0))},t.prototype.setFocusAlignment=function(e){this._focusAlignment=e},Object.defineProperty(t.prototype,"defaultFocusElement",{get:function(){return this._defaultFocusElement},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"activeElement",{get:function(){return this._activeElement},enumerable:!1,configurable:!0}),t.prototype._evaluateFocusBeforeRender=function(){var e=this._root.current,t=this._getDocument();if(t){var n=t.activeElement;if(n!==e){var o=(0,f.t)(e,n,!1);this._lastIndexPath=o?(0,l.xu)(e,n):void 0}}},t.prototype._setParkedFocus=function(e){var t=this._root.current;t&&this._isParked!==e&&(this._isParked=e,e?(this.props.allowFocusRoot||(this._parkedTabIndex=t.getAttribute("tabindex"),t.setAttribute("tabindex","-1")),t.focus()):this.props.allowFocusRoot||(this._parkedTabIndex?(t.setAttribute("tabindex",this._parkedTabIndex),this._parkedTabIndex=void 0):t.removeAttribute("tabindex")))},t.prototype._setActiveElement=function(e,t){var n=this._activeElement;this._activeElement=e,n&&((0,l.jz)(n)&&this._updateTabIndexes(n),n.tabIndex=-1),this._activeElement&&(this._focusAlignment&&!t||this._setFocusAlignment(e,!0,!0),this._activeElement.tabIndex=0)},t.prototype._preventDefaultWhenHandled=function(e){this.props.preventDefaultWhenHandled&&e.preventDefault()},t.prototype._tryInvokeClickForFocusable=function(e,t){var n=e;if(n===this._root.current)return!1;do{if("BUTTON"===n.tagName||"A"===n.tagName||"INPUT"===n.tagName||"TEXTAREA"===n.tagName||"SUMMARY"===n.tagName)return!1;if(this._isImmediateDescendantOfZone(n)&&"true"===n.getAttribute(k)&&"true"!==n.getAttribute("data-disable-click-on-enter"))return D(n,t),!0;n=(0,u.G)(n,R)}while(n!==this._root.current);return!1},t.prototype._getFirstInnerZone=function(e){if(!(e=e||this._activeElement||this._root.current))return null;if((0,l.jz)(e))return S[e.getAttribute(x)];for(var t=e.firstElementChild;t;){if((0,l.jz)(t))return S[t.getAttribute(x)];var n=this._getFirstInnerZone(t);if(n)return n;t=t.nextElementSibling}return null},t.prototype._moveFocus=function(e,t,n,o){void 0===o&&(o=!0);var r=this._activeElement,i=-1,s=void 0,u=!1,c=this.props.direction===a.U.bidirectional;if(!r||!this._root.current)return!1;if(this._isElementInput(r)&&!this._shouldInputLoseFocus(r,e))return!1;var d=c?r.getBoundingClientRect():null;do{if(r=e?(0,l.dc)(this._root.current,r):(0,l.TD)(this._root.current,r),!c){s=r;break}if(r){var p=t(d,r.getBoundingClientRect());if(-1===p&&-1===i){s=r;break}if(p>-1&&(-1===i||p<i)&&(i=p,s=r),i>=0&&p<0)break}}while(r);if(s&&s!==this._activeElement)u=!0,this.focusElement(s);else if(this.props.isCircularNavigation&&o)return e?this.focusElement((0,l.dc)(this._root.current,this._root.current.firstElementChild,!0)):this.focusElement((0,l.TD)(this._root.current,this._root.current.lastElementChild,!0,!0,!0));return u},t.prototype._moveFocusDown=function(){var e=this,t=-1,n=this._focusAlignment.left||this._focusAlignment.x||0;return!!this._moveFocus(!0,(function(o,r){var i=-1,a=Math.floor(r.top),s=Math.floor(o.bottom);return a<s?e._shouldWrapFocus(e._activeElement,I)?T:M:((-1===t&&a>=s||a===t)&&(t=a,i=n>=r.left&&n<=r.left+r.width?0:Math.abs(r.left+r.width/2-n)),i)}))&&(this._setFocusAlignment(this._activeElement,!1,!0),!0)},t.prototype._moveFocusUp=function(){var e=this,t=-1,n=this._focusAlignment.left||this._focusAlignment.x||0;return!!this._moveFocus(!1,(function(o,r){var i=-1,a=Math.floor(r.bottom),s=Math.floor(r.top),l=Math.floor(o.top);return a>l?e._shouldWrapFocus(e._activeElement,I)?T:M:((-1===t&&a<=l||s===t)&&(t=s,i=n>=r.left&&n<=r.left+r.width?0:Math.abs(r.left+r.width/2-n)),i)}))&&(this._setFocusAlignment(this._activeElement,!1,!0),!0)},t.prototype._moveFocusLeft=function(e){var t=this,n=this._shouldWrapFocus(this._activeElement,w);return!!this._moveFocus((0,d.zg)(e),(function(o,r){var i=-1;return((0,d.zg)(e)?parseFloat(r.top.toFixed(3))<parseFloat(o.bottom.toFixed(3)):parseFloat(r.bottom.toFixed(3))>parseFloat(o.top.toFixed(3)))&&r.right<=o.right&&t.props.direction!==a.U.vertical?i=o.right-r.right:n||(i=M),i}),void 0,n)&&(this._setFocusAlignment(this._activeElement,!0,!1),!0)},t.prototype._moveFocusRight=function(e){var t=this,n=this._shouldWrapFocus(this._activeElement,w);return!!this._moveFocus(!(0,d.zg)(e),(function(o,r){var i=-1;return((0,d.zg)(e)?parseFloat(r.bottom.toFixed(3))>parseFloat(o.top.toFixed(3)):parseFloat(r.top.toFixed(3))<parseFloat(o.bottom.toFixed(3)))&&r.left>=o.left&&t.props.direction!==a.U.vertical?i=r.left-o.left:n||(i=M),i}),void 0,n)&&(this._setFocusAlignment(this._activeElement,!0,!1),!0)},t.prototype._moveFocusPaging=function(e,t){void 0===t&&(t=!0);var n=this._activeElement;if(!n||!this._root.current)return!1;if(this._isElementInput(n)&&!this._shouldInputLoseFocus(n,e))return!1;var o=(0,g.zj)(n);if(!o)return!1;var r=-1,i=void 0,a=-1,s=-1,u=o.clientHeight,c=n.getBoundingClientRect();do{if(n=e?(0,l.dc)(this._root.current,n):(0,l.TD)(this._root.current,n)){var d=n.getBoundingClientRect(),p=Math.floor(d.top),m=Math.floor(c.bottom),f=Math.floor(d.bottom),h=Math.floor(c.top),v=this._getHorizontalDistanceFromCenter(e,c,d);if(e&&p>m+u||!e&&f<h-u)break;v>-1&&(e&&p>a?(a=p,r=v,i=n):!e&&f<s?(s=f,r=v,i=n):(-1===r||v<=r)&&(r=v,i=n))}}while(n);var b=!1;if(i&&i!==this._activeElement)b=!0,this.focusElement(i),this._setFocusAlignment(i,!1,!0);else if(this.props.isCircularNavigation&&t)return e?this.focusElement((0,l.dc)(this._root.current,this._root.current.firstElementChild,!0)):this.focusElement((0,l.TD)(this._root.current,this._root.current.lastElementChild,!0,!0,!0));return b},t.prototype._setFocusAlignment=function(e,t,n){if(this.props.direction===a.U.bidirectional&&(!this._focusAlignment||t||n)){var o=e.getBoundingClientRect(),r=o.left+o.width/2,i=o.top+o.height/2;this._focusAlignment||(this._focusAlignment={left:r,top:i}),t&&(this._focusAlignment.left=r),n&&(this._focusAlignment.top=i)}},t.prototype._isImmediateDescendantOfZone=function(e){return this._getOwnerZone(e)===this._root.current},t.prototype._getOwnerZone=function(e){for(var t=(0,u.G)(e,R);t&&t!==this._root.current&&t!==this._getDocument().body;){if((0,l.jz)(t))return t;t=(0,u.G)(t,R)}return t},t.prototype._updateTabIndexes=function(e){!this._activeElement&&this.props.defaultTabbableElement&&"function"===typeof this.props.defaultTabbableElement&&(this._activeElement=this.props.defaultTabbableElement(this._root.current)),!e&&this._root.current&&(this._defaultFocusElement=null,e=this._root.current,this._activeElement&&!(0,f.t)(e,this._activeElement)&&(this._activeElement=null)),this._activeElement&&!(0,l.MW)(this._activeElement)&&(this._activeElement=null);for(var t=e&&e.children,n=0;t&&n<t.length;n++){var o=t[n];(0,l.jz)(o)?"true"===o.getAttribute(k)&&(this._isInnerZone||(this._activeElement||this._defaultFocusElement)&&this._activeElement!==o?"-1"!==o.getAttribute(E)&&o.setAttribute(E,"-1"):(this._defaultFocusElement=o,"0"!==o.getAttribute(E)&&o.setAttribute(E,"0"))):(o.getAttribute&&"false"===o.getAttribute(k)&&o.setAttribute(E,"-1"),(0,l.MW)(o)?this.props.disabled?o.setAttribute(E,"-1"):this._isInnerZone||(this._activeElement||this._defaultFocusElement)&&this._activeElement!==o?"-1"!==o.getAttribute(E)&&o.setAttribute(E,"-1"):(this._defaultFocusElement=o,"0"!==o.getAttribute(E)&&o.setAttribute(E,"0")):"svg"===o.tagName&&"false"!==o.getAttribute("focusable")&&o.setAttribute("focusable","false")),this._updateTabIndexes(o)}},t.prototype._isContentEditableElement=function(e){return e&&"true"===e.getAttribute("contenteditable")},t.prototype._isElementInput=function(e){return!(!e||!e.tagName||"input"!==e.tagName.toLowerCase()&&"textarea"!==e.tagName.toLowerCase())},t.prototype._shouldInputLoseFocus=function(e,t){if(!this._processingTabKey&&e&&e.type&&N.indexOf(e.type.toLowerCase())>-1){var n=e.selectionStart,o=n!==e.selectionEnd,r=e.value,i=e.readOnly;if(o||n>0&&!t&&!i||n!==r.length&&t&&!i||this.props.handleTabKey&&(!this.props.shouldInputLoseFocusOnArrowKey||!this.props.shouldInputLoseFocusOnArrowKey(e)))return!1}return!0},t.prototype._shouldWrapFocus=function(e,t){return!this.props.checkForNoWrap||(0,l.mM)(e,t)},t.prototype._portalContainsElement=function(e){return e&&!!this._root.current&&(0,b.w)(e,this._root.current)},t.prototype._getDocument=function(){return(0,y.M)(this._root.current)},t.defaultProps={isCircularNavigation:!1,direction:a.U.bidirectional,shouldRaiseClicks:!0},t}(i.Component)},78498:function(e,t,n){n.d(t,{J:function(){return r},U:function(){return o}});var o,r={none:0,all:1,inputOnly:2};!function(e){e[e.vertical=0]="vertical",e[e.horizontal=1]="horizontal",e[e.bidirectional=2]="bidirectional",e[e.domOrder=3]="domOrder"}(o||(o={}))},32092:function(e,t,n){n.d(t,{r:function(){return i}});var o=n(68959),r=n(72791);function i(){var e=r.useRef();return e.current||(e.current=new o.e),r.useEffect((function(){return function(){var t;null===(t=e.current)||void 0===t||t.dispose(),e.current=void 0}}),[]),e.current}},32594:function(e,t,n){n.d(t,{k:function(){return i}});var o=n(72791),r=n(32561);function i(e){var t=o.useState(e),n=t[0],i=t[1];return[n,{setTrue:(0,r.B)((function(){return function(){i(!0)}})),setFalse:(0,r.B)((function(){return function(){i(!1)}})),toggle:(0,r.B)((function(){return function(){i((function(e){return!e}))}}))}]}},32561:function(e,t,n){n.d(t,{B:function(){return r}});var o=n(72791);function r(e){var t=o.useRef();return void 0===t.current&&(t.current={value:"function"===typeof e?e():e}),t.current.value}},53193:function(e,t,n){n.d(t,{G:function(){return i}});var o=n(72791),r=n(32561);function i(e,t,n){var i=o.useState(t),a=i[0],s=i[1],l=(0,r.B)(void 0!==e),u=l?e:a,c=o.useRef(u),d=o.useRef(n);o.useEffect((function(){c.current=u,d.current=n}));var p=(0,r.B)((function(){return function(e,t){var n="function"===typeof e?e(c.current):e;d.current&&d.current(t,n),l||s(n)}}));return[u,p]}},24994:function(e,t,n){n.d(t,{M:function(){return i}});var o=n(72791),r=n(91106);function i(e,t){var n=o.useRef(t);return n.current||(n.current=(0,r.z)(e)),n.current}},77769:function(e,t,n){n.d(t,{d:function(){return i}});var o=n(64862),r=n(72791);function i(e,t,n,i){var a=r.useRef(n);a.current=n,r.useEffect((function(){var n=e&&"current"in e?e.current:e;if(n)return(0,o.on)(n,t,(function(e){return a.current(e)}),i)}),[e,t,i])}},1604:function(e,t,n){n.d(t,{D:function(){return r}});var o=n(72791);function r(e){var t=(0,o.useRef)();return(0,o.useEffect)((function(){t.current=e})),t.current}},60684:function(e,t,n){n.d(t,{L:function(){return i}});var o=n(72791),r=n(32561),i=function(){var e=(0,r.B)({});return o.useEffect((function(){return function(){for(var t=0,n=Object.keys(e);t<n.length;t++){var o=n[t];clearTimeout(o)}}}),[e]),(0,r.B)({setTimeout:function(e){function t(t,n){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(t,n){var o=setTimeout(t,n);return e[o]=1,o})),clearTimeout:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(t){delete e[t],clearTimeout(t)}))})}},42086:function(e,t,n){n.d(t,{e:function(){return a}});var o=n(16455),r=n(72791),i=n(3141);function a(e,t){var n=r.useRef(),a=r.useRef(null),s=(0,i.zY)();if(!e||e!==n.current||"string"===typeof e){var l=null===t||void 0===t?void 0:t.current;if(e)if("string"===typeof e){var u=(0,o.M)(l);a.current=u?u.querySelector(e):null}else a.current="stopPropagation"in e||"getBoundingClientRect"in e?e:"current"in e?e.current:e;n.current=e}return[a,s]}},38289:function(e,t,n){n.d(t,{k:function(){return r}});var o=n(72791),r=function(e){var t=o.useRef(e);t.current=e,o.useEffect((function(){return function(){var e;null===(e=t.current)||void 0===e||e.call(t)}}),[])}},68959:function(e,t,n){n.d(t,{e:function(){return r}});var o=n(12513),r=function(){function e(e,t){this._timeoutIds=null,this._immediateIds=null,this._intervalIds=null,this._animationFrameIds=null,this._isDisposed=!1,this._parent=e||null,this._onErrorHandler=t,this._noop=function(){}}return e.prototype.dispose=function(){var e;if(this._isDisposed=!0,this._parent=null,this._timeoutIds){for(e in this._timeoutIds)this._timeoutIds.hasOwnProperty(e)&&this.clearTimeout(parseInt(e,10));this._timeoutIds=null}if(this._immediateIds){for(e in this._immediateIds)this._immediateIds.hasOwnProperty(e)&&this.clearImmediate(parseInt(e,10));this._immediateIds=null}if(this._intervalIds){for(e in this._intervalIds)this._intervalIds.hasOwnProperty(e)&&this.clearInterval(parseInt(e,10));this._intervalIds=null}if(this._animationFrameIds){for(e in this._animationFrameIds)this._animationFrameIds.hasOwnProperty(e)&&this.cancelAnimationFrame(parseInt(e,10));this._animationFrameIds=null}},e.prototype.setTimeout=function(e,t){var n=this,o=0;return this._isDisposed||(this._timeoutIds||(this._timeoutIds={}),o=setTimeout((function(){try{n._timeoutIds&&delete n._timeoutIds[o],e.apply(n._parent)}catch(t){n._logError(t)}}),t),this._timeoutIds[o]=!0),o},e.prototype.clearTimeout=function(e){this._timeoutIds&&this._timeoutIds[e]&&(clearTimeout(e),delete this._timeoutIds[e])},e.prototype.setImmediate=function(e,t){var n=this,r=0,i=(0,o.J)(t);if(!this._isDisposed){this._immediateIds||(this._immediateIds={});r=i.setTimeout((function(){try{n._immediateIds&&delete n._immediateIds[r],e.apply(n._parent)}catch(t){n._logError(t)}}),0),this._immediateIds[r]=!0}return r},e.prototype.clearImmediate=function(e,t){var n=(0,o.J)(t);this._immediateIds&&this._immediateIds[e]&&(n.clearTimeout(e),delete this._immediateIds[e])},e.prototype.setInterval=function(e,t){var n=this,o=0;return this._isDisposed||(this._intervalIds||(this._intervalIds={}),o=setInterval((function(){try{e.apply(n._parent)}catch(t){n._logError(t)}}),t),this._intervalIds[o]=!0),o},e.prototype.clearInterval=function(e){this._intervalIds&&this._intervalIds[e]&&(clearInterval(e),delete this._intervalIds[e])},e.prototype.throttle=function(e,t,n){var o=this;if(this._isDisposed)return this._noop;var r,i,a=t||0,s=!0,l=!0,u=0,c=null;n&&"boolean"===typeof n.leading&&(s=n.leading),n&&"boolean"===typeof n.trailing&&(l=n.trailing);var d=function t(n){var d=Date.now(),p=d-u,m=s?a-p:a;return p>=a&&(!n||s)?(u=d,c&&(o.clearTimeout(c),c=null),r=e.apply(o._parent,i)):null===c&&l&&(c=o.setTimeout(t,m)),r};return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i=e,d(!0)}},e.prototype.debounce=function(e,t,n){var o=this;if(this._isDisposed){var r=function(){};return r.cancel=function(){},r.flush=function(){return null},r.pending=function(){return!1},r}var i,a,s=t||0,l=!1,u=!0,c=null,d=0,p=Date.now(),m=null;n&&"boolean"===typeof n.leading&&(l=n.leading),n&&"boolean"===typeof n.trailing&&(u=n.trailing),n&&"number"===typeof n.maxWait&&!isNaN(n.maxWait)&&(c=n.maxWait);var f=function(e){m&&(o.clearTimeout(m),m=null),p=e},h=function(t){f(t),i=e.apply(o._parent,a)},v=function e(t){var n=Date.now(),r=!1;t&&(l&&n-d>=s&&(r=!0),d=n);var a=n-d,f=s-a,v=n-p,g=!1;return null!==c&&(v>=c&&m?g=!0:f=Math.min(f,c-v)),a>=s||g||r?h(n):null!==m&&t||!u||(m=o.setTimeout(e,f)),i},g=function(){return!!m},b=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return a=e,v(!0)};return b.cancel=function(){g()&&f(Date.now())},b.flush=function(){return g()&&h(Date.now()),i},b.pending=g,b},e.prototype.requestAnimationFrame=function(e,t){var n=this,r=0,i=(0,o.J)(t);if(!this._isDisposed){this._animationFrameIds||(this._animationFrameIds={});var a=function(){try{n._animationFrameIds&&delete n._animationFrameIds[r],e.apply(n._parent)}catch(t){n._logError(t)}};r=i.requestAnimationFrame?i.requestAnimationFrame(a):i.setTimeout(a,0),this._animationFrameIds[r]=!0}return r},e.prototype.cancelAnimationFrame=function(e,t){var n=(0,o.J)(t);this._animationFrameIds&&this._animationFrameIds[e]&&(n.cancelAnimationFrame?n.cancelAnimationFrame(e):n.clearTimeout(e),delete this._animationFrameIds[e])},e.prototype._logError=function(e){this._onErrorHandler&&this._onErrorHandler(e)},e}()},50907:function(e,t,n){n.d(t,{S:function(){return d}});var o=n(75971),r=n(72791),i=n(68959),a=n(92853),s=n(18597),l=n(40382),u=n(23983);!function(e){function t(n,o){var r=e.call(this,n,o)||this;return function(e,t,n){for(var o=0,r=n.length;o<r;o++)c(e,t,n[o])}(r,t.prototype,["componentDidMount","shouldComponentUpdate","getSnapshotBeforeUpdate","render","componentDidUpdate","componentWillUnmount"]),r}(0,o.ZT)(t,e),t.prototype.componentDidUpdate=function(e,t){this._updateComponentRef(e,this.props)},t.prototype.componentDidMount=function(){this._setComponentRef(this.props.componentRef,this)},t.prototype.componentWillUnmount=function(){if(this._setComponentRef(this.props.componentRef,null),this.__disposables){for(var e=0,t=this._disposables.length;e<t;e++){var n=this.__disposables[e];n.dispose&&n.dispose()}this.__disposables=null}},Object.defineProperty(t.prototype,"className",{get:function(){if(!this.__className){var e=/function (.{1,})\(/.exec(this.constructor.toString());this.__className=e&&e.length>1?e[1]:""}return this.__className},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_disposables",{get:function(){return this.__disposables||(this.__disposables=[]),this.__disposables},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_async",{get:function(){return this.__async||(this.__async=new i.e(this),this._disposables.push(this.__async)),this.__async},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_events",{get:function(){return this.__events||(this.__events=new a.r(this),this._disposables.push(this.__events)),this.__events},enumerable:!1,configurable:!0}),t.prototype._resolveRef=function(e){var t=this;return this.__resolves||(this.__resolves={}),this.__resolves[e]||(this.__resolves[e]=function(n){return t[e]=n}),this.__resolves[e]},t.prototype._updateComponentRef=function(e,t){void 0===t&&(t={}),e&&t&&e.componentRef!==t.componentRef&&(this._setComponentRef(e.componentRef,null),this._setComponentRef(t.componentRef,this))},t.prototype._warnDeprecations=function(e){(0,u.b)(this.className,this.props,e)},t.prototype._warnMutuallyExclusive=function(e){(0,l.L)(this.className,this.props,e)},t.prototype._warnConditionallyRequiredProps=function(e,t,n){(0,s.w)(this.className,this.props,e,t,n)},t.prototype._setComponentRef=function(e,t){!this._skipComponentRefResolution&&e&&("function"===typeof e&&e(t),"object"===typeof e&&(e.current=t))}}(r.Component);function c(e,t,n){var o=e[n],r=t[n];(o||r)&&(e[n]=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r&&(e=r.apply(this,t)),o!==r&&(e=o.apply(this,t)),e})}function d(){return null}},12103:function(e,t,n){n.d(t,{U:function(){return a}});var o=n(75971),r=n(72791),i=n(12513),a=function(e){function t(t){var n=e.call(this,t)||this;return n.state={isRendered:void 0===(0,i.J)()},n}return(0,o.ZT)(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props.delay;this._timeoutId=window.setTimeout((function(){e.setState({isRendered:!0})}),t)},t.prototype.componentWillUnmount=function(){this._timeoutId&&clearTimeout(this._timeoutId)},t.prototype.render=function(){return this.state.isRendered?r.Children.only(this.props.children):null},t.defaultProps={delay:0},t}(r.Component)},92853:function(e,t,n){n.d(t,{r:function(){return r}});var o=n(35726),r=function(){function e(t){this._id=e._uniqueId++,this._parent=t,this._eventRecords=[]}return e.raise=function(t,n,r,i){var a;if(e._isElement(t)){if("undefined"!==typeof document&&document.createEvent){var s=document.createEvent("HTMLEvents");s.initEvent(n,i||!1,!0),(0,o.f0)(s,r),a=t.dispatchEvent(s)}else if("undefined"!==typeof document&&document.createEventObject){var l=document.createEventObject(r);t.fireEvent("on"+n,l)}}else for(;t&&!1!==a;){var u=t.__events__,c=u?u[n]:null;if(c)for(var d in c)if(c.hasOwnProperty(d))for(var p=c[d],m=0;!1!==a&&m<p.length;m++){var f=p[m];f.objectCallback&&(a=f.objectCallback.call(f.parent,r))}t=i?t.parent:null}return a},e.isObserved=function(e,t){var n=e&&e.__events__;return!!n&&!!n[t]},e.isDeclared=function(e,t){var n=e&&e.__declaredEvents;return!!n&&!!n[t]},e.stopPropagation=function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0},e._isElement=function(e){return!!e&&(!!e.addEventListener||"undefined"!==typeof HTMLElement&&e instanceof HTMLElement)},e.prototype.dispose=function(){this._isDisposed||(this._isDisposed=!0,this.off(),this._parent=null)},e.prototype.onAll=function(e,t,n){for(var o in t)t.hasOwnProperty(o)&&this.on(e,o,t[o],n)},e.prototype.on=function(t,n,o,r){var i=this;if(n.indexOf(",")>-1)for(var a=n.split(/[ ,]+/),s=0;s<a.length;s++)this.on(t,a[s],o,r);else{var l=this._parent,u={target:t,eventName:n,parent:l,callback:o,options:r};if((a=t.__events__=t.__events__||{})[n]=a[n]||{count:0},a[n][this._id]=a[n][this._id]||[],a[n][this._id].push(u),a[n].count++,e._isElement(t)){var c=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(!i._isDisposed){var n;try{if(!1===(n=o.apply(l,e))&&e[0]){var r=e[0];r.preventDefault&&r.preventDefault(),r.stopPropagation&&r.stopPropagation(),r.cancelBubble=!0}}catch(r){}return n}};u.elementCallback=c,t.addEventListener?t.addEventListener(n,c,r):t.attachEvent&&t.attachEvent("on"+n,c)}else{u.objectCallback=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(!i._isDisposed)return o.apply(l,e)}}this._eventRecords.push(u)}},e.prototype.off=function(e,t,n,o){for(var r=0;r<this._eventRecords.length;r++){var i=this._eventRecords[r];if((!e||e===i.target)&&(!t||t===i.eventName)&&(!n||n===i.callback)&&("boolean"!==typeof o||o===i.options)){var a=i.target.__events__,s=a[i.eventName],l=s?s[this._id]:null;l&&(1!==l.length&&n?(s.count--,l.splice(l.indexOf(i),1)):(s.count-=l.length,delete a[i.eventName][this._id]),s.count||delete a[i.eventName]),i.elementCallback&&(i.target.removeEventListener?i.target.removeEventListener(i.eventName,i.elementCallback,i.options):i.target.detachEvent&&i.target.detachEvent("on"+i.eventName,i.elementCallback)),this._eventRecords.splice(r--,1)}}},e.prototype.raise=function(t,n,o){return e.raise(this._parent,t,n,o)},e.prototype.declare=function(e){var t=this._parent.__declaredEvents=this._parent.__declaredEvents||{};if("string"===typeof e)t[e]=!0;else for(var n=0;n<e.length;n++)t[e[n]]=!0},e._uniqueId=0,e}()},29109:function(e,t,n){function o(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.filter((function(e){return e})).join(" ").trim();return""===n?void 0:n}n.d(t,{I:function(){return o}})},63039:function(e,t,n){function o(e,t,n){void 0===n&&(n=0);for(var o=-1,r=n;e&&r<e.length;r++)if(t(e[r],r)){o=r;break}return o}function r(e,t,n){var o=e.slice();return o.splice(t,0,n),o}function i(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}n.d(t,{OA:function(){return r},cO:function(){return i},cx:function(){return o}})},79292:function(e,t,n){n.d(t,{y:function(){return p}});var o=n(79248),r=n(96701),i=n(87376),a=n(12513),s=50,l=5,u=0,c=o.Y.getInstance();c&&c.onReset&&c.onReset((function(){return u++}));var d="__retval__";function p(e){void 0===e&&(e={});var t=new Map,n=0,o=0,c=u;return function(p,m){var h;if(void 0===m&&(m={}),e.useStaticStyles&&"function"===typeof p&&p.__noStyleOverride__)return p(m);o++;var v=t,g=m.theme,b=g&&void 0!==g.rtl?g.rtl:(0,i.zg)(),y=e.disableCaching;if(c!==u&&(c=u,t=new Map,n=0),e.disableCaching||(v=f(t,p),v=f(v,m)),!y&&v[d]||(v[d]=void 0===p?{}:(0,r.I)(["function"===typeof p?p(m):p],{rtl:!!b,specificityMultiplier:e.useStaticStyles?l:void 0}),y||n++),n>(e.cacheSize||s)){var _=(0,a.J)();(null===(h=null===_||void 0===_?void 0:_.FabricConfig)||void 0===h?void 0:h.enableClassNameCacheFullWarning)&&(console.warn("Styles are being recalculated too frequently. Cache miss rate is ".concat(n,"/").concat(o,".")),console.trace()),t.clear(),n=0,e.disableCaching=!0}return v[d]}}function m(e,t){return t=function(e){switch(e){case void 0:return"__undefined__";case null:return"__null__";default:return e}}(t),e.has(t)||e.set(t,new Map),e.get(t)}function f(e,t){if("function"===typeof t)if(t.__cachedInputs__)for(var n=0,o=t.__cachedInputs__;n<o.length;n++){e=m(e,o[n])}else e=m(e,t);else if("object"===typeof t)for(var r in t)t.hasOwnProperty(r)&&(e=m(e,t[r]));return e}},20777:function(e,t,n){n.d(t,{Z:function(){return s}});var o=n(75971),r=n(72791),i=n(42792);var a=(0,i.Ct)((function(e){var t=e;return(0,i.Ct)((function(n){if(e===n)throw new Error("Attempted to compose a component with itself.");var a=n,s=(0,i.Ct)((function(e){return function(t){return r.createElement(a,(0,o.pi)({},t,{defaultRender:e}))}}));return function(e){var n=e.defaultRender;return r.createElement(t,(0,o.pi)({},e,{defaultRender:n?s(n):a}))}}))}));function s(e,t){return a(e)(t)}},6475:function(e,t,n){n.d(t,{S:function(){return r}});var o=n(63039),r=function(e){var t={refs:[]};return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t.resolver&&(0,o.cO)(t.refs,e)||(t.resolver=function(e){return function(t){for(var n=0,o=e.refs;n<o.length;n++){var r=o[n];"function"===typeof r?r(t):r&&(r.current=t)}}}(t)),t.refs=e,t.resolver}}},91605:function(e,t,n){n.d(t,{a:function(){return l}});var o=n(75971),r=n(72791),i=n(19101);var a=n(72557),s=n(74104);function l(e,t,n){return function(l){var u,c=((u=function(u){function c(e){var t=u.call(this,e)||this;return t._styleCache={},t._onSettingChanged=t._onSettingChanged.bind(t),t}return(0,o.ZT)(c,u),c.prototype.componentDidMount=function(){i.X.observe(this._onSettingChanged)},c.prototype.componentWillUnmount=function(){i.X.unobserve(this._onSettingChanged)},c.prototype.render=function(){var u=this;return r.createElement(a.i.Consumer,null,(function(a){var c=i.X.getSettings(t,e,a.customizations),d=u.props;if(c.styles&&"function"===typeof c.styles&&(c.styles=c.styles((0,o.pi)((0,o.pi)({},c),d))),n&&c.styles){if(u._styleCache.default!==c.styles||u._styleCache.component!==d.styles){var p=(0,s.m)(c.styles,d.styles);u._styleCache.default=c.styles,u._styleCache.component=d.styles,u._styleCache.merged=p}return r.createElement(l,(0,o.pi)({},c,d,{styles:u._styleCache.merged}))}return r.createElement(l,(0,o.pi)({},c,d))}))},c.prototype._onSettingChanged=function(){this.forceUpdate()},c}(r.Component)).displayName="Customized"+e,u);return function(e,t){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}(l,c)}}},64862:function(e,t,n){function o(e,t,n,o){return e.addEventListener(t,n,o),function(){return e.removeEventListener(t,n,o)}}n.d(t,{on:function(){return o}})},13223:function(e,t,n){function o(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.length<2?t[0]:function(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];t.forEach((function(t){return t&&t.apply(e,n)}))}}function r(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=o(e,e[n],t[n]))}n.d(t,{c:function(){return r}})},56500:function(e,t,n){n.d(t,{WU:function(){return E},um:function(){return T},uo:function(){return v},xu:function(){return D},ft:function(){return p},RK:function(){return f},bF:function(){return M},TE:function(){return m},xY:function(){return h},dc:function(){return b},TD:function(){return g},gc:function(){return x},jz:function(){return k},MW:function(){return C},BS:function(){return _},mM:function(){return I}});var o=n(30810);var r=n(84688),i=n(65141),a=n(12513),s=n(16455),l="data-is-focusable",u="data-is-visible",c="data-focuszone-id",d="data-is-sub-focuszone";function p(e,t,n){return b(e,t,!0,!1,!1,n)}function m(e,t,n){return g(e,t,!0,!1,!0,n)}function f(e,t,n,o){return void 0===o&&(o=!0),b(e,t,o,!1,!1,n,!1,!0)}function h(e,t,n,o){return void 0===o&&(o=!0),g(e,t,o,!1,!0,n,!1,!0)}function v(e,t){var n=b(e,e,!0,!1,!1,!0,void 0,void 0,t);return!!n&&(T(n),!0)}function g(e,t,n,o,r,i,a,s){if(!t||!a&&t===e)return null;var l=y(t);if(r&&l&&(i||!k(t)&&!x(t))){var u=g(e,t.lastElementChild,!0,!0,!0,i,a,s);if(u){if(s&&C(u,!0)||!s)return u;var c=g(e,u.previousElementSibling,!0,!0,!0,i,a,s);if(c)return c;for(var d=u.parentElement;d&&d!==t;){var p=g(e,d.previousElementSibling,!0,!0,!0,i,a,s);if(p)return p;d=d.parentElement}}}if(n&&l&&C(t,s))return t;var m=g(e,t.previousElementSibling,!0,!0,!0,i,a,s);return m||(o?null:g(e,t.parentElement,!0,!1,!1,i,a,s))}function b(e,t,n,o,r,i,a,s,l){if(!t||t===e&&r&&!a)return null;var u=(l?_:y)(t);if(n&&u&&C(t,s))return t;if(!r&&u&&(i||!k(t)&&!x(t))){var c=b(e,t.firstElementChild,!0,!0,!1,i,a,s,l);if(c)return c}if(t===e)return null;var d=b(e,t.nextElementSibling,!0,!0,!1,i,a,s,l);return d||(o?null:b(e,t.parentElement,!1,!1,!0,i,a,s,l))}function y(e){if(!e||!e.getAttribute)return!1;var t=e.getAttribute(u);return null!==t&&void 0!==t?"true"===t:0!==e.offsetHeight||null!==e.offsetParent||!0===e.isVisible}function _(e){return!!e&&y(e)&&!e.hidden&&"hidden"!==window.getComputedStyle(e).visibility}function C(e,t){if(!e||e.disabled)return!1;var n=0,o=null;e&&e.getAttribute&&(o=e.getAttribute("tabIndex"))&&(n=parseInt(o,10));var r=e.getAttribute?e.getAttribute(l):null,i=null!==o&&n>=0,a=!!e&&"false"!==r&&("A"===e.tagName||"BUTTON"===e.tagName||"INPUT"===e.tagName||"TEXTAREA"===e.tagName||"SELECT"===e.tagName||"true"===r||i);return t?-1!==n&&a:a}function k(e){return!!(e&&e.getAttribute&&e.getAttribute(c))}function x(e){return!(!e||!e.getAttribute||"true"!==e.getAttribute(d))}function E(e){var t=(0,s.M)(e),n=t&&t.activeElement;return!(!n||!(0,r.t)(e,n))}function I(e,t){return"true"!==function(e,t){var n=(0,o.X)(e,(function(e){return e.hasAttribute(t)}));return n&&n.getAttribute(t)}(e,t)}var w=void 0;function T(e){if(e){var t=(0,a.J)(e);t&&(void 0!==w&&t.cancelAnimationFrame(w),w=t.requestAnimationFrame((function(){e&&e.focus(),w=void 0})))}}function M(e,t){for(var n=e,o=0,r=t;o<r.length;o++){var i=r[o],a=n.children[Math.min(i,n.children.length-1)];if(!a)break;n=a}return n=C(n)&&y(n)?n:b(e,n,!0)||g(e,n)}function D(e,t){for(var n=[];t&&e&&t!==e;){var o=(0,i.G)(t,!0);if(null===o)return[];n.unshift(Array.prototype.indexOf.call(o.children,t)),t=o}return n}},8877:function(e,t,n){n.d(t,{l:function(){return r}});var o=n(13223);function r(e){(0,o.c)(e,{componentDidMount:i,componentDidUpdate:a,componentWillUnmount:s})}function i(){l(this.props.componentRef,this)}function a(e){e.componentRef!==this.props.componentRef&&(l(e.componentRef,null),l(this.props.componentRef,this))}function s(){l(this.props.componentRef,null)}function l(e,t){e&&("object"===typeof e?e.current=t:"function"===typeof e&&e(t))}},42792:function(e,t,n){n.d(t,{Ct:function(){return d},NF:function(){return c}});var o=n(79248),r=!1,i=0,a={empty:!0},s={},l="undefined"===typeof WeakMap?null:WeakMap;function u(){i++}function c(e,t,n){if(void 0===t&&(t=100),void 0===n&&(n=!1),!l)return e;if(!r){var a=o.Y.getInstance();a&&a.onReset&&o.Y.getInstance().onReset(u),r=!0}var s,c=0,d=i;return function(){for(var o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];var a=s;(void 0===s||d!==i||t>0&&c>t)&&(s=m(),c=0,d=i),a=s;for(var l=0;l<o.length;l++){var u=p(o[l]);a.map.has(u)||a.map.set(u,m()),a=a.map.get(u)}return a.hasOwnProperty("value")||(a.value=e.apply(void 0,o),c++),!n||null!==a.value&&void 0!==a.value||(a.value=e.apply(void 0,o)),a.value}}function d(e){if(!l)return e;var t=new l;return function(n){if(!n||"function"!==typeof n&&"object"!==typeof n)return e(n);if(t.has(n))return t.get(n);var o=e(n);return t.set(n,o),o}}function p(e){return e?"object"===typeof e||"function"===typeof e?e:(s[e]||(s[e]={val:e}),s[e]):a}function m(){return{map:l?new l:null}}},86749:function(e,t,n){n.d(t,{g:function(){return o}});var o=function(){return!!(window&&window.navigator&&window.navigator.userAgent)&&/iPad|iPhone|iPod/i.test(window.navigator.userAgent)}},38994:function(e,t,n){n.d(t,{O:function(){return i}});var o=n(16455),r=["TEMPLATE","STYLE","SCRIPT"];function i(e){var t=(0,o.M)(e);if(!t)return function(){};for(var n=[];e!==t.body&&e.parentElement;){for(var i=0,a=e.parentElement.children;i<a.length;i++){var s=a[i],l=s.getAttribute("aria-hidden");s!==e&&"true"!==(null===l||void 0===l?void 0:l.toLowerCase())&&-1===r.indexOf(s.tagName)&&n.push([s,l])}e=e.parentElement}return n.forEach((function(e){e[0].setAttribute("aria-hidden","true")})),function(){!function(e){e.forEach((function(e){var t=e[0],n=e[1];n?t.setAttribute("aria-hidden",n):t.removeAttribute("aria-hidden")}))}(n),n=[]}}},90101:function(e,t,n){n.d(t,{V:function(){return i}});var o,r=n(12513);function i(e){var t;if("undefined"===typeof o||e){var n=(0,r.J)(),i=null===(t=null===n||void 0===n?void 0:n.navigator)||void 0===t?void 0:t.userAgent;o=!!i&&-1!==i.indexOf("Macintosh")}return!!o}},45651:function(e,t,n){n.d(t,{k:function(){return i}});var o=n(42792);var r=(0,o.Ct)((function(e){return(0,o.Ct)((function(t){var n=(0,o.Ct)((function(e){return function(n){return t(n,e)}}));return function(o,r){return e(o,r?n(r):t)}}))}));function i(e,t){return r(e)(t)}},87376:function(e,t,n){n.d(t,{dP:function(){return c},zg:function(){return u}});var o,r=n(9691),i=n(16455),a=n(86864),s=n(46765),l="isRTL";function u(e){if(void 0===e&&(e={}),void 0!==e.rtl)return e.rtl;if(void 0===o){var t=(0,a.r)(l);null!==t&&function(e,t){void 0===t&&(t=!1);var n=(0,i.M)();n&&n.documentElement.setAttribute("dir",e?"rtl":"ltr");t&&(0,a.L)(l,e?"1":"0");o=e,(0,s.ok)(o)}(o="1"===t);var n=(0,i.M)();void 0===o&&n&&(o="rtl"===(n.body&&n.body.getAttribute("dir")||n.documentElement.getAttribute("dir")),(0,s.ok)(o))}return!!o}function c(e,t){return void 0===t&&(t={}),u(t)&&(e===r.m.left?e=r.m.right:e===r.m.right&&(e=r.m.left)),e}},71675:function(e,t,n){n.d(t,{C7:function(){return c},Qp:function(){return m},eC:function(){return d},np:function(){return h},tG:function(){return f},zj:function(){return v}});var o,r=n(16455),i=n(18323),a=n(12513),s=0,l=(0,i.y)({overflow:"hidden !important"}),u="data-is-scrollable",c=function(e,t){if(e){var n=0,o=null;t.on(e,"touchstart",(function(e){1===e.targetTouches.length&&(n=e.targetTouches[0].clientY)}),{passive:!1}),t.on(e,"touchmove",(function(e){if(1===e.targetTouches.length&&(e.stopPropagation(),o)){var t=e.targetTouches[0].clientY-n,r=v(e.target);r&&(o=r),0===o.scrollTop&&t>0&&e.preventDefault(),o.scrollHeight-Math.ceil(o.scrollTop)<=o.clientHeight&&t<0&&e.preventDefault()}}),{passive:!1}),o=e}},d=function(e,t){if(e){t.on(e,"touchmove",(function(e){e.stopPropagation()}),{passive:!1})}},p=function(e){e.preventDefault()};function m(){var e=(0,r.M)();e&&e.body&&!s&&(e.body.classList.add(l),e.body.addEventListener("touchmove",p,{passive:!1,capture:!1})),s++}function f(){if(s>0){var e=(0,r.M)();e&&e.body&&1===s&&(e.body.classList.remove(l),e.body.removeEventListener("touchmove",p)),s--}}function h(){if(void 0===o){var e=document.createElement("div");e.style.setProperty("width","100px"),e.style.setProperty("height","100px"),e.style.setProperty("overflow","scroll"),e.style.setProperty("position","absolute"),e.style.setProperty("top","-9999px"),document.body.appendChild(e),o=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return o}function v(e){for(var t=e,n=(0,r.M)(e);t&&t!==n.body;){if("true"===t.getAttribute(u))return t;t=t.parentElement}for(t=e;t&&t!==n.body;){if("false"!==t.getAttribute(u)){var o=getComputedStyle(t),i=o?o.getPropertyValue("overflow-y"):"";if(i&&("scroll"===i||"auto"===i))return t}t=t.parentElement}return t&&t!==n.body||(t=(0,a.J)(e)),t}},40433:function(e,t,n){n.d(t,{z:function(){return l}});var o=n(75971),r=n(72791),i=n(54925),a=n(67312),s=["theme","styles"];function l(e,t,n,l,u){var c=(l=l||{scope:"",fields:void 0}).scope,d=l.fields,p=void 0===d?s:d,m=r.forwardRef((function(s,l){var u=r.useRef(),d=(0,a.D)(p,c),m=d.styles,f=(d.dir,(0,o._T)(d,["styles","dir"])),h=n?n(s):void 0,v=u.current&&u.current.__cachedInputs__||[],g=s.styles;if(!u.current||m!==v[1]||g!==v[2]){var b=function(e){return(0,i.l)(e,t,m,g)};b.__cachedInputs__=[t,m,g],b.__noStyleOverride__=!m&&!g,u.current=b}return r.createElement(e,(0,o.pi)({ref:l},f,h,s,{styles:u.current}))}));m.displayName="Styled".concat(e.displayName||e.name);var f=u?r.memo(m):m;return m.displayName&&(f.displayName=m.displayName),f}},7211:function(e,t,n){n.d(t,{L:function(){return r}});var o=n(72791),r=(0,n(57780).N)()?o.useLayoutEffect:o.useEffect},13202:function(e,t,n){n.d(t,{Z:function(){return o}});function o(e){console&&console.warn&&console.warn(e)}},18597:function(e,t,n){function o(e,t,n,o,r){}n.d(t,{w:function(){return o}})},23983:function(e,t,n){function o(e,t,n){}n.d(t,{b:function(){return o}})},40382:function(e,t,n){function o(e,t,n){}n.d(t,{L:function(){return o}})}}]);