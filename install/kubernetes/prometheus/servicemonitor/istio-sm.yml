apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: istio-mesh
  name: istio-mesh
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
    port: prometheus    # 端口号只能是字符串    这个服务网格的收集流量，下面的是组件本身的监控
  jobLabel: istio-mesh   # prometheus里面显示的名字
  namespaceSelector:
    matchNames:
    - istio-system
  selector:
    name: istio-telemetry    # 直接使用名称匹配
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: istio-policy
  name: istio-policy
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
    port: http-monitoring    # 端口号只能是字符串
  jobLabel: istio-policy   # prometheus里面显示的名字
  namespaceSelector:
    matchNames:
    - istio-system
  selector:
    name: istio-policy    # 直接使用名称匹配
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: istio-telemetry
  name: istio-telemetry
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
    port: http-monitoring    # 端口号只能是字符串
  jobLabel: istio-telemetry   # prometheus里面显示的名字
  namespaceSelector:
    matchNames:
    - istio-system
  selector:
    name: istio-telemetry    # 直接使用名称匹配
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: istio-pilot
  name: istio-pilot
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
    port: http-monitoring    # 端口号只能是字符串
  jobLabel: istio-pilot   # prometheus里面显示的名字
  namespaceSelector:
    matchNames:
    - istio-system
  selector:
    name: istio-pilot    # 直接使用名称匹配
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: istio-galley
  name: istio-galley
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
    port: http-monitoring    # 端口号只能是字符串
  jobLabel: istio-galley   # prometheus里面显示的名字
  namespaceSelector:
    matchNames:
    - istio-system
  selector:
    name: istio-galley    # 直接使用名称匹配
