"""empty message

Revision ID: 8feab47444dc
Revises: c347941bd412
Create Date: 2024-12-31 11:17:54.561364

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '8feab47444dc'
down_revision = 'c347941bd412'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('pytorchjob')
    op.drop_table('tfjob')
    op.drop_table('xgbjob')

    with op.batch_alter_table('dataset', schema=None) as batch_op:
        batch_op.alter_column('icon',
               existing_type=mysql.VARCHAR(length=2000),
               type_=sa.Text(),
               comment='数据集预览图片',
               existing_nullable=True)

    with op.batch_alter_table('dimension', schema=None) as batch_op:
        batch_op.add_column(sa.Column('expand', sa.Text(length=65536), nullable=True, comment='扩展参数'))

    with op.batch_alter_table('inferenceservice', schema=None) as batch_op:
        batch_op.add_column(sa.Column('cronhpa', sa.String(length=400), nullable=True, comment='定时伸缩'))

    with op.batch_alter_table('model', schema=None) as batch_op:
        batch_op.add_column(sa.Column('expand', sa.Text(length=65536), nullable=True, comment='扩展参数'))

    with op.batch_alter_table('nni', schema=None) as batch_op:
        batch_op.add_column(sa.Column('parallel_trial_type', sa.String(length=100), nullable=True, comment='并行方式'))
        batch_op.add_column(sa.Column('expand', sa.Text(length=65536), nullable=True, comment='扩展参数'))

    with op.batch_alter_table('pipeline', schema=None) as batch_op:
        batch_op.add_column(sa.Column('priority', sa.String(length=100), nullable=True, comment='优先级'))

    try:
        with op.batch_alter_table('ab_user', schema=None) as batch_op:
            batch_op.add_column(sa.Column('wechat', sa.String(length=200), nullable=True, comment='微信'))
            batch_op.add_column(sa.Column('phone', sa.String(length=200), nullable=True, comment='电话'))
            batch_op.add_column(sa.Column('balance', sa.Text(length=65536), nullable=True, comment='余额'))
            batch_op.add_column(sa.Column('coupon', sa.Text(length=65536), nullable=True, comment='优惠券'))
            batch_op.add_column(sa.Column('voucher', sa.Text(length=65536), nullable=True, comment='代金券'))
            batch_op.add_column(sa.Column('bill', sa.Text(length=65536), nullable=True, comment='发票'))
            batch_op.add_column(sa.Column('real_name_authentication', sa.Text(length=65536), nullable=True, comment='实名认证'))
            batch_op.add_column(sa.Column('subaccount', sa.Text(length=65536), nullable=True, comment='子用户'))
    except Exception as e:
        pass

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('pipeline', schema=None) as batch_op:
        batch_op.drop_column('priority')

    with op.batch_alter_table('nni', schema=None) as batch_op:

        batch_op.drop_column('expand')
        batch_op.drop_column('parallel_trial_type')

    with op.batch_alter_table('model', schema=None) as batch_op:
        batch_op.drop_column('expand')

    with op.batch_alter_table('inferenceservice', schema=None) as batch_op:
        batch_op.drop_column('cronhpa')

    with op.batch_alter_table('dimension', schema=None) as batch_op:

        batch_op.drop_column('expand')

    with op.batch_alter_table('dataset', schema=None) as batch_op:

        batch_op.alter_column('icon',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=2000),
               comment=None,
               existing_comment='数据集预览图片',
               existing_nullable=True)

    op.create_table('xgbjob',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('namespace', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('create_time', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('status', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('annotations', mysql.TEXT(), nullable=True),
    sa.Column('labels', mysql.TEXT(), nullable=True),
    sa.Column('spec', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('status_more', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('username', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('info_json', mysql.TEXT(), nullable=True),
    sa.Column('add_row_time', mysql.DATETIME(), nullable=True),
    sa.Column('foreign_key', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('change_time', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('cluster', mysql.VARCHAR(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8mb3',
    mysql_engine='InnoDB'
    )
    op.create_table('tfjob',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('namespace', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('create_time', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('status', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('annotations', mysql.TEXT(), nullable=True),
    sa.Column('labels', mysql.TEXT(), nullable=True),
    sa.Column('spec', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('status_more', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('username', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('info_json', mysql.TEXT(), nullable=True),
    sa.Column('add_row_time', mysql.DATETIME(), nullable=True),
    sa.Column('foreign_key', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('change_time', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('cluster', mysql.VARCHAR(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8mb3',
    mysql_engine='InnoDB'
    )
    op.create_table('pytorchjob',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('namespace', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('create_time', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('status', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('annotations', mysql.TEXT(), nullable=True),
    sa.Column('labels', mysql.TEXT(), nullable=True),
    sa.Column('spec', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('status_more', mysql.MEDIUMTEXT(), nullable=True),
    sa.Column('username', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('info_json', mysql.TEXT(), nullable=True),
    sa.Column('add_row_time', mysql.DATETIME(), nullable=True),
    sa.Column('foreign_key', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('change_time', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('cluster', mysql.VARCHAR(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8mb3',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
