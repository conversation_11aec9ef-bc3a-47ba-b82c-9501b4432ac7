{"version": 3, "file": "static/js/397.01f03a1b.chunk.js", "mappings": "yOA0FMA,EAAyC,CAC3CC,OAAQ,KAIG,SAASC,EAAWC,GAC/B,OAA0CC,EAAAA,EAAAA,YAA2B,eAA9DC,EAAa,KAAEC,EAAgB,KAChCC,EAAKC,KAAKC,SAASC,SAAS,IAAIC,UAAU,GAChD,GAAoBC,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAEFC,GAFS,EAAJC,KAEI,CAAC,GAchB,OAZAC,EAAAA,EAAAA,YAAU,WACN,IAAMC,EAAWC,SAASC,eAAeZ,GACzC,GAAIU,EAAU,CACV,IAAMG,EAAQC,EAAAA,GAAaJ,GAC3BG,EAAME,WAAU,kBAAKR,GAAWX,EAAMW,SAEjCT,GACDC,EAAiBc,EAEzB,CACJ,GAAG,CAACjB,EAAMW,OAAQX,EAAMoB,QAGpB,SAAC,IAAI,CAACC,SAAUrB,EAAMsB,QAAQ,UAC1B,iBAAKC,UAAU,kBAAiB,WAC5B,gBAAKnB,GAAIA,EAAIoB,OAAK,kBAAO3B,GAAsBG,EAAMwB,SAEjDxB,EAAMyB,UAAW,gBAAKF,UAAU,eAAc,UAC1C,yBAAMb,EAAE,gCACH,SAK7B,C,+GCjGe,SAASgB,EAAY1B,GAChC,IACI2B,EADEvB,EAAKC,KAAKC,SAASC,SAAS,IAAIC,UAAU,GAGhD,GAAkCP,EAAAA,EAAAA,UAAmBD,EAAM4B,SAAW,IAAG,eAAlEC,EAAS,KAAEC,EAAY,KAC9B,GAA0B7B,EAAAA,EAAAA,UAASD,EAAM+B,OAAS,IAAG,eAA9CA,EAAK,KAAEC,EAAQ,MAEtBnB,EAAAA,EAAAA,YAAU,WACN,IAAMoB,EAAajC,EAAMkC,mBAAqBlC,EAAM4B,SAAW,IAAIO,QAAO,SAAAC,GACtE,OAAgC,IAAzBA,EAAKC,QAAQN,EACxB,IAAM/B,EAAM4B,SAAW,GACvBE,EAAaG,EACjB,GAAG,CAACjC,EAAM4B,WAEVf,EAAAA,EAAAA,YAAU,WACNmB,EAAShC,EAAM+B,OAAS,GAE5B,GAAG,CAAC/B,EAAM+B,QAEV,IAAMO,EAAe,SAACP,GAClBC,EAASD,GACT/B,EAAMuC,UAAYvC,EAAMuC,SAASR,EACrC,EAeMS,EAAmB,SAACJ,GACtB,IAAMK,EAAUV,EACVW,EAAQN,EAAKC,QAAQN,GAC3B,IAAe,IAAXW,EACA,OAAO,0BAAON,IAElB,IAAMO,EAASP,EAAK5B,UAAU,EAAGkC,GAC3BE,EAAUR,EAAK5B,UAAUkC,EAAQX,EAAMc,QAC7C,OAAO,4BAAOF,GAAO,iBAAMpB,UAAU,YAAW,SAAEkB,IAAgBG,IACtE,EAYME,EAVW,SAACC,GAA+B,IACzCC,EADoBC,EAAI,uDAAG,IAE/B,OAAO,WAA+B,IAAD,uBAAjBC,EAAI,yBAAJA,EAAI,gBACpBC,aAAaH,GACbA,EAAQI,YAAW,WACfL,GAAOA,EAAIM,MAAM,KAAK,GAAD,OAAMH,GAC/B,GAAGD,EACP,CACJ,CAEuBK,CAAStD,EAAMuD,gBAoBtC,OACI,iBAAKhC,UAAU,qBAAoB,UAE3BvB,EAAMwD,WAAY,kBAAOC,QAASrD,EAAImB,UAAU,mBAAkB,SAAEvB,EAAMwD,YAAqB,MAEnG,iBAAKjC,UAAU,aAAaC,MAAO,CAAEkC,MAAO1D,EAAM0D,OAAS,QAAY,WACnE,SAAC,IAAK,CACFlC,MAAO,CAAEkC,MAAO,QAChBC,SAAU3D,EAAM2D,SAChBvD,GAAIA,EACJwD,YAAa5D,EAAM4D,aAAe,GAClCC,UAAW7D,EAAM6D,WAAa,IAC9BtB,SAAU,SAACuB,GAAgC,OAAWxB,EAAawB,EAAEC,OAAOhC,MAAM,EAClFiC,WA9DO,SAACF,GAEU,KAA1BA,EAAEG,YAAYC,UACdvC,EAASwC,MAAQxC,EAASwC,OAC1BnE,EAAMoE,UAAYpE,EAAMoE,SAASN,EAAEO,cAActC,OAEzD,EAyDgBA,MAAOA,EACPuC,IAAK,SAAAC,GAAO,OAAI5C,EAAW4C,CAAO,KAEtC,SAACC,EAAA,EAAc,CAACjD,UAAU,kBAK1BM,EAAUgB,QAAS,gBAAItB,UAAU,uBAAuBkD,SAxC/C,SAACX,GAClBA,EAAEY,kBASF,IAAQL,EAAkBP,EAAlBO,cACAM,EAA0CN,EAA1CM,UAAWC,EAA+BP,EAA/BO,aAA+BP,EAAjBQ,aACCD,EAAeD,EAChC,IACb3E,EAAMuD,gBAAkBT,GAEhC,EAwB4FtB,MAAO,CAAE,UAAY,GAAD,OAAKxB,EAAM8E,UAAS,OAAO,UAEvH9E,EAAMsB,SAAU,gBAAKC,UAAU,cAAcC,MAAO,CAAEuD,MAAM,GAAD,OAAK,EAAC,MAAMC,IAAI,GAAD,OAAK,EAAC,OAAO,UACnF,gBAAKzD,UAAU,WAAWC,MAAO,CAAEuD,MAAM,GAAD,OAAK,EAAC,MAAMC,IAAI,GAAD,OAAK,EAAC,WACxD,KAGTnD,EAAUoD,KAAI,SAAC7C,EAAMM,GACjB,OAAO,eAAInB,UAAU,SAAS2D,YAAa,kBApF/C,SAACnD,GACjBO,EAAaP,GACb/B,EAAMmF,SAAWnF,EAAMmF,QAAQpD,EACnC,CAiF+EqD,CAAYhD,EAAK,EAAC,SAAcI,EAAiBJ,IAAzBM,EACnF,OAEA,OAIxB,C,0UCtIM2C,EAAkBC,EAAQ,OA6B1BC,EAAiB,SAAH,GAAgD,IAA1CC,EAAQ,EAARA,SAAU9B,EAAK,EAALA,MAAU+B,GAAS,YACtD,OAAK/B,GAKJ,SAAC,EAAAgC,UAAS,CACThC,MAAOA,EACP5D,OAAQ,EACR6F,QACC,iBACCpE,UAAU,yBACV4D,QAAS,SAACrB,GACTA,EAAEY,iBACH,IAGFc,SAAUA,EACVI,cAAe,CAAEC,sBAAsB,GAAQ,UAE/C,iCAAQJ,GAAS,IAAEjE,OAAK,kBAAgB,OAATiE,QAAS,IAATA,OAAS,EAATA,EAAWjE,OAAK,IAAEsE,WAAY,eAlBvD,yBAAQL,GAqBjB,EAiRA,EA/QiB,SAACzF,GACjB,OAAkDC,EAAAA,EAAAA,WAAS,GAAM,eAA1D8F,EAAiB,KAAEC,EAAoB,KAC9C,GAAoC/F,EAAAA,EAAAA,UAAyC,CAC5EgG,OAAQ,GACR7E,KAAM,KACL,eAHK8E,EAAU,KAAEC,EAAa,KAIhC,GAAsClG,EAAAA,EAAAA,UAAgB,IAAG,eAAlDmG,EAAW,KAAEC,EAAc,KAGlC,GAAwBpG,EAAAA,EAAAA,UAASD,EAAMsG,SAAQ,eAAxCC,EAAI,KAAEC,EAAO,KACdC,EAAe,SAAC/D,GACrB,OAAO,SAACgE,EAAO,GAAoB,IAAjBC,EAAI,EAAJA,KACjB,KAAIA,EAAKjD,MAAQ,KAAjB,CACA,IAAMkD,GAAI,OAAOL,GACjBK,EAAKlE,IAAM,kBAAQkE,EAAKlE,IAAM,IAAEgB,MAAOiD,EAAKjD,QAC5C,IAAMmD,EAAaD,EAAKE,QAAO,SAACC,EAAUC,GAAS,OAAKD,EAAMC,EAAKtD,OAAS,GAAG,GAAE,GAAK,IACtFuD,aAAaC,QAAQlH,EAAMmH,UAAY,GAAIC,KAAKC,UAAUT,IAE1DU,GAAsB,kBAAKC,GAAkB,IAAEC,EAAGX,KAClDL,EAAQI,EAPoB,CAQ7B,CACD,EACMa,EAAgBlB,EAAKtB,KAAI,SAACyC,EAAUhF,GACzC,OAAO,kBACHgF,GAAG,IACNhE,MAAOgE,EAAIhE,OAAS,IACpBiE,aAAc,SAACC,GACd,MAAO,CACNlE,MAAOkE,EAAOlE,MACd8B,SAAUiB,EAAa/D,GAEzB,GAEF,IACA,GAAoDzC,EAAAA,EAAAA,UAASD,EAAM6H,QAAO,eAAnEN,EAAkB,KAAED,EAAqB,KAChD,GAAoB7G,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAAO,EAAJE,MAEXC,EAAAA,EAAAA,YAAU,WACT2F,EAAQxG,EAAMsG,QACf,GAAG,CAACtG,EAAMsG,WAEVzF,EAAAA,EAAAA,YAAU,WACTyG,EAAsBtH,EAAM6H,OAC7B,GAAG,CAAC7H,EAAM6H,UAEVhH,EAAAA,EAAAA,YAAU,WACT,GAAIb,EAAM8H,WAAY,CACrB,IAAMxB,EAAUtG,EAAMsG,QAAQnE,QAAO,SAACC,GAAS,OAAMgE,EAAY/D,QAAQD,EAAK2F,UAAU,IACxFC,EAAoB1B,EAAStG,EAAM8H,WACpC,CACD,GAAG,CAAC9H,EAAM8H,WAAY9H,EAAMsG,UAE5B,IAOM0B,EAAsB,WAAoC,IAAnCC,EAAW,uDAAG,GAAI7G,EAAW,uCACnDkF,EAAU2B,EAAYhD,KAAI,SAAC7C,GAAS,OAAKA,EAAK2F,SAAS,IAAE5F,QAAO,SAACC,GAAY,MAAc,WAATA,CAAiB,IACnG8F,EAAcD,EAAYhD,KAAI,SAAC7C,GAAS,OAAKA,EAAK+F,KAAK,IAAEhG,QAAO,SAACC,GAAY,OAAKA,IAAS1B,EAAE,eAAK,IAClG0H,EAAe,GAErBhH,EAAKiH,SAAQ,SAACC,GACb,IAAMC,EAAW,CAAC,EAClBjC,EAAQrB,KAAI,SAACuD,GACZ,IAAMC,EAAMH,EAAQE,GACpBD,EAAIC,GAAWC,GAAO,EACvB,IACAL,EAAQM,KAAKH,EACd,IAEApC,EAAc,CACbF,OAAQiC,EACR9G,KAAMgH,GAER,EAgBMO,EAAmB,WACxB,IAAM1C,EAASC,EAAWD,OACpB7E,EAAO8E,EAAW9E,KACpBwH,EAAM,GA2BV,OA1BI3C,EAAOpD,QAAUzB,EAAKyB,QACzB+F,EACC,IACA3C,EAAO4C,KAAK,KADZ,MAKDzH,EAAKiH,SAAQ,SAACE,GACb,IAAMO,EAASC,OAAOC,OAAOT,GAAKtD,KAAI,SAAC7C,GACtC,MAAa,KAATA,EACI,IAEDA,CACR,IACAwG,EACCA,EACA,IACAE,EAAOD,KAAK,KAFZD,KAMF,KAEAA,EAAM,GAGAA,CACR,EAEMK,EAAmB,WACxB,IAAMhD,EAASC,EAAWD,OACpB7E,EAAO8E,EAAW9E,KACpBwH,EAAM,GAsBV,OArBI3C,EAAOpD,QAAUzB,EAAKyB,QACzB+F,EACC3C,EAAO4C,KAAK,MAAI,KAGjBzH,EAAKiH,SAAQ,SAACE,GACb,IAAMO,EAASC,OAAOC,OAAOT,GAAKtD,KAAI,SAAC7C,GACtC,MAAa,KAATA,EACI,IAEDA,CACR,IACAwG,EACCA,EACAE,EAAOD,KAAK,MAAI,IAGlB,KAEAD,EAAM,GAEAA,CACR,EAEA,OACC,UAAC,IAAK,CAACrH,UAAU,WAAW2H,UAAU,WAAWvC,KAAK,SAAQ,WAC7D,UAAC,IAAK,CACLjD,MAAO,IACPyF,cAAc,EACdC,UAAU,EACVC,UAAW,CAAEvE,UAAW,IAAKwE,SAAU,QACvCC,QAASxD,EACToC,MAAOzH,EAAE,4BACT8I,SAAU,WACTxD,GAAqB,EACtB,EACAyD,OAAQ,KAAK,WAEb,iBAAKjI,MAAO,CAAEkI,SAAU,YAAa,WACpC,iBAAKnI,UAAU,OAAM,WAAC,kBAAMA,UAAU,MAAK,UAAEb,EAAE,oDAAY,aAAQ,SAAC,UAAc,CACjFkB,QAAS5B,EAAMsG,QACbrB,KAAI,SAAC7C,GAAS,MAAM,CAAEuH,MAAOvH,EAAK+F,MAAOpG,MAAOK,EAAK2F,UAAW,IAChE5F,QAAO,SAACC,GAAS,MAAoB,WAAfA,EAAKL,KAAkB,IAC/C6H,aAAc,GACd7H,MAAOqE,EACP7D,SAAU,SAACyG,GACV3C,EAAe2C,GACf,IAAM1C,EAAUtG,EAAMsG,QAAQnE,QAAO,SAACC,GAAS,OAAM4G,EAAO3G,QAAQD,EAAK2F,UAAU,IACnFC,EAAoB1B,EAAStG,EAAM8H,WACpC,QAED,iBAAKtG,MAAO,CAAEkI,SAAU,WAAY3E,MAAO,EAAG8E,OAAQ,GAAI,WACzD,SAAC,IAAM,CACNlD,KAAK,QACLmD,KAAK,OACL3E,QAAS,WACRkB,EACCrG,EAAMsG,QACJrB,KAAI,SAAC7C,GAAS,OAAKA,EAAK2F,SAAS,IACjC5F,QAAO,SAACC,GAAS,MAAc,WAATA,CAAiB,KAE1C4F,EAAoBhI,EAAMsG,QAAStG,EAAM8H,WAC1C,EAAE,SAEDpH,EAAE,mBAEJ,SAAC,IAAM,CACNiG,KAAK,QACLmD,KAAK,OACL3E,QAAS,WACRkB,EAAe,IACf2B,EAAoB,GAAIhI,EAAM8H,WAC/B,EAAE,SAEDpH,EAAE,yBAKN,UAAC,IAAI,YACJ,SAAC,YAAY,CAACqJ,IAAI,mBAAQ,UACzB,SAAC1E,EAAe,CAAC2E,KAAMrB,IAAoBsB,OAAQ,kBAAMC,EAAAA,GAAAA,QAAgBxJ,EAAE,8CAAW,EAAC,UACtF,gBAAKc,MAAO,CAAE2I,OAAQ,UAAWC,UAAW,KAAM,UACjD,0BAAOzB,WAHqB,SAO/B,SAAC,YAAY,CAACoB,IAAI,mBAAQ,UACzB,SAAC1E,EAAe,CAAC2E,KAAMf,IAAoBgB,OAAQ,kBAAMC,EAAAA,GAAAA,QAAgBxJ,EAAE,8CAAW,EAAC,UACtF,gBAAKc,MAAO,CAAE2I,OAAQ,UAAWC,UAAW,KAAM,UACjD,0BAAOnB,WAHqB,cAmBhCjJ,EAAMqK,WAAarK,EAAMsK,aAAetK,EAAMuK,kBAAmB,UAAC,IAAG,CAACC,QAAQ,gBAAgBC,MAAM,SAAQ,WAC3G,SAAC,IAAG,WACH,SAAC,IAAK,CAACA,MAAM,SAAQ,SAAEzK,EAAMqK,eAE9B,SAAC,IAAG,WACH,UAAC,IAAK,CAACI,MAAM,SAAQ,UACnBzK,EAAMsK,WACNtK,EAAMuK,iBAAmB,MACzB,SAAC,IAAM,CAAC/I,MAAO,CAAEkJ,WAAY,GAAKvF,QAAS,kBAAMa,GAAqB,EAAK,EAAC,SAC1EtF,EAAE,sCAKC,MAEV,SAAC,KAAc,CAACiK,YAxMW,WAAH,OACzB,UAAC,IAAG,CAACH,QAAQ,SAASC,MAAM,SAASjJ,MAAO,CAAE1B,OAAQ,IAAK8K,cAAe,UAAW,WACpF,gBAAKC,IAAKC,EAAUtJ,MAAO,CAAEkC,MAAO,KAAOqH,IAAI,MAC/C,yBAAMrK,EAAE,gCACH,EAoM6C,UACjD,SAAC,IAAK,CACLiG,KAAM3G,EAAM2G,MAAQ,SACpBmC,OAAQ9I,EAAM8I,OAAS9I,EAAM8I,OAAS,KACtChB,WAAY9H,EAAM8H,WAElBkD,WAAY,CAAE/E,OAAQ,CAAEgF,KAAM1F,IAC9Be,QAASmB,EACTyD,YAAiC,IAArBlL,EAAMkL,aAAoB,UAAQlL,EAAMkL,YACpDrD,OAAQN,EACRjG,QAAStB,EAAMsB,QACfiB,SAAUvC,EAAMuC,SAChB4I,aAAcnL,EAAMmL,mBAKzB,C,qkDCtTe,SAASC,WAAWpL,OAE/B,eAAsCC,EAAAA,mCAAAA,UAAiD,CAAC,GAAE,8IAAnFoL,YAAW,cAAEC,eAAc,eAKlCzK,EAAAA,mCAAAA,YAAU,WAEN,IAAM0K,EAAc,6QAAG,WAAOxB,EAAayB,EAAezJ,GAAa,gNAEhD0J,EAAAA,oCAAAA,IAAO1J,GAAO,KAAD,EAA1B2J,EAAI,OAEVJ,gBAAe,SAACK,GAAI,qPAEbA,GAAI,6HAEN5B,GAAG,8OAEI4B,EAAK5B,IAAQ,CAAC,GAAG,CAAF,4HAElByB,EAAQE,KAAI,IAIjB,2CAEP,gBAlBmB,0CAoBpB1L,MAAMoB,KAAKiH,SAAQ,SAAC0B,GAEhBA,EAAI6B,QAAQvD,SAAQ,SAACmD,GAEe,aAA5BA,EAAMK,aAAa/B,MAEnByB,EAAexB,EAAI+B,QAASN,EAAMO,UAAWP,EAAMK,aAAa9J,MAIxE,GAEJ,GAEJ,GAAG,CAAC/B,MAAMoB,OAQV,IAAM4K,mBAAqB,SAAClC,EAAyB8B,EAAcE,EAAiBC,GAEhF,OAAQjC,GAEJ,IAAK,MAED,OAAOmC,mBAAmBL,GAE9B,IAAK,SAED,OAAOM,aAAaN,GAExB,IAAK,OAED,OAAOO,cAAcP,GAEzB,IAAK,SAED,OAAOQ,gBAAgBR,GAE3B,IAAK,OAED,OAAOS,WAAWT,GAEtB,IAAK,WAED,OAAOL,eAAeO,EAASC,GAEnC,QAEI,OAAO,8DAAMvK,MAAO,CAAE8K,UAAW,aAAcC,WAAY,YAAa,SAAEX,IAItF,EAKMS,WAAa,SAACjL,GAEhB,OAAO,6DAAKoL,wBAAyB,CAAEC,OAAQrL,IAEnD,EAEM6K,mBAAqB,SAAC7K,GAExB,IAAMsL,EAAoD3D,OAAO4D,QAAQvL,GAAM0F,QAAO,SAACC,EAAS,GAAD,iIAAG6F,EAAG,KAAEC,EAAG,gJAAW9F,GAAG,CAAE,CAAE4C,MAAOiD,EAAK7K,MAAO8K,IAAK,GAAI,IAExJ,OAAO,6DAAKtL,UAAU,eAAc,SAI5BmL,EAASzH,KAAI,SAAC7C,EAAMM,GAEhB,OAAO,uDAAC,qCAAG,CAACnB,UAAU,WAAU,WAE5B,sDAAC,qCAAG,CAACuL,KAAM,EAAE,UAAC,6DAAKvL,UAAU,OAAM,UAAC,2EAASa,EAAKuH,MAAM,iBAExD,sDAAC,qCAAG,CAACmD,KAAM,GAAG,UAAC,8DAAMtL,MAAO,CAAE8K,UAAW,aAAcC,WAAY,YAAa,SAAEnK,EAAKL,YAAmB,yBAJtDW,GAQ5D,KAMZ,EAEMwJ,aAAe,SAAfA,aAAgB9K,MAElB,IAAI2L,WAAkB,CAAC,EAIvB,OAFAC,KAAK,cAAD,OAAe5L,QAEZ,6DAAKG,UAAU,eAAc,UAEhC,sDAAC,sDAAU,CAACZ,OAAQoM,WAAYzL,SAAS,KAIjD,EAEM6K,cAAgB,SAAC/K,GAEnB,OAAO,6DAAKG,UAAU,eAAeC,MAAO,CAAE8K,UAAW,aAAcC,WAAY,YAAa,SAAEnL,GAEtG,EAEMgL,gBAAkB,SAAChL,GAErB,OAAO,gEAEHyJ,IAAKzJ,EAAK6L,IAEVC,iBAAe,EAEfC,MAAM,0CAEN5L,UAAU,eAEVC,MAAO,CAAE4L,OAAQ,EAAGtN,OAAQ,MAIpC,EAEMyL,eAAiB,SAACO,EAAiBC,GAAuB,IAAD,EAErDsB,EAAmC,QAAvB,EAAGhC,YAAYS,UAAQ,aAApB,EAAuBC,GAE5C,OAAOsB,GAAe,6DAAKb,wBAAyB,CAAEC,OAAQY,KAAqB,IAEvF,EAKA,OAEI,yHAEI,sDAAC,qCAAI,CAAC9L,UAAU,iBAAgB,SAIxBvB,MAAMoB,KAAK6D,KAAI,SAAC8E,EAAKuD,GAAc,IAAD,EAE9B,OAAO,sDAAC,6CAAY,CAACvD,IAAKA,EAAI+B,QAAQ,UAElC,8DAAKvK,UAAU,qBAAoB,WAE/B,6DAAKA,UAAU,QAAO,SAIdwI,EAAI6B,QAAQ3G,KAAI,SAACuG,EAAO+B,GAEpB,OAAO,8DAAKhM,UAAU,OAAM,WAExB,6DAAKA,UAAU,8BAA8BC,MAAO,CAAEgM,gBAAiB,GAAKhB,wBAAyB,CAAEC,OAAQjB,EAAMO,cAErH,sEAEKC,mBAAmBR,EAAMK,aAAa/B,KAAM0B,EAAMK,aAAa9J,MAAOgI,EAAI+B,QAASN,EAAMO,eAExF,mBARoCwB,GAYlD,OAMR,6DAAKhM,UAAU,kBAAiB,SAIR,QAJQ,EAIxBwI,EAAI0D,oBAAY,aAAhB,EAAkBxI,KAAI,SAAAyI,GAElB,OAAO,uDAAC,qCAAM,CAACnM,UAAU,yBAAyB4D,QAAS,WAEvDwI,OAAOC,KAAKF,EAAOT,IAAK,QAE5B,EAAE,WAEE,8DAAM1L,UAAU,YAAYiL,wBAAyB,CAAEC,OAAQiB,EAAOG,SAEtE,8DAAMtM,UAAU,MAAK,SAAEmM,EAAO1D,SAItC,UAMN,uBApDkDsD,GAwDhE,OAUpB,C,qTC1QQQ,EAAUC,EAAAA,EAAAA,MAiClB,EAxBoB,SAAC/N,GACjB,IAAMgO,EAA8B,CAAEtE,SAAU,SAAU1E,IAAK,GAC/D,OACI,UAAC,IAAG,CACAzD,UAAU,eACViJ,QAAQ,gBACRC,MAAM,SACNjJ,MAAOxB,EAAMiO,gBAAc,QAAKC,aAAc,QAAWF,GAAWA,EAAO,WAC3E,4BACI,SAACF,EAAK,CAACvM,UAAU,YAAY4M,MAAO,EAAG3M,MAAO,CAAE4M,aAAc,IAAK,SAC9DpO,EAAMmI,SAEX,gBAAK5G,UAAU,OAAM,SAChBvB,EAAMqO,kBAIf,SAAC,IAAG,WACA,SAAC,IAAK,UAAErO,EAAMsO,SAAWtO,EAAMsO,SAAW,WAI1D,E,iKC6CA,EAhEkB,SAACtO,GAClB,OAAoBS,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EACR,GADe,EAAJE,KACI2N,EAAAA,EAAAA,WAARC,GAAsB,YAAlB,GACX,GAAwBvO,EAAAA,EAAAA,YAAfwO,GAA8B,YAAnB,GACAC,EAAAA,aAAkB,kBAAMD,EAAY,CAAC,EAAE,GAAE,KAE7D5N,EAAAA,EAAAA,YAAU,WACLb,EAAM2O,UACTH,EAAKI,eAAe5O,EAAM2O,SAE5B,GAAG,CAAC3O,IAEJ,OAA0CC,EAAAA,EAAAA,UAGvC,CACF4O,cAAe,CAAC,EAChBC,UAAW,CAAC,IACX,eANKC,EAAa,KAAEC,EAAgB,KAUtC,OACC,SAAC,IACA,CACAC,gBAAgB,EAChB9F,cAAc,EACdzF,MAAO1D,EAAM0D,OAAS,IACtB6F,QAASvJ,EAAMuJ,QACfpB,MAAOnI,EAAMmI,MACb+G,OAAQxO,EAAE,gBACVyO,WAAYzO,EAAE,gBACd8I,SAAU,WACTgF,EAAKY,cACLpP,EAAMwJ,UACP,EACA6F,KAAM,WACLC,QAAQC,IAAIf,EAAKgB,kBACjBhB,EAAKiB,iBACHC,MAAK,SAAC1G,GACNhJ,EAAM2P,SAAS3G,EAAQwF,EAExB,IACCoB,OAAM,SAACC,GACP,GAEH,EAAE,UAEF,SAAC,IAAI,CAACxO,SAAUrB,EAAMsB,QAAQ,UAC7B,SAAC,IAAI,CAACwO,eAAgB,SAAC/N,EAAO+M,GAC7BE,EAAiB,CAChBH,cAAe9M,EACf+M,UAAAA,GAEF,EAAGiB,SAAU,CAAEjD,KAAM,GAAKkD,WAAY,CAAElD,KAAM,IAAM0B,KAAMA,EAAMyB,OAAO,aAAaC,KAAK,gBAAe,SACtGlQ,EAAMsO,UAA+D,sBAAnDvF,OAAOoH,UAAU5P,SAAS6P,KAAKpQ,EAAMsO,UACrDtO,EAAMsO,SAASE,EAAMO,GACrB/O,EAAMsO,cAKd,E,6ECvEQ+B,EAAWC,EAAAA,EAAAA,OA8RnB,EAzQkB,SAACtQ,GAClB,MAAeuO,EAAAA,EAAAA,UAARC,GAAsB,YAAlB,GACX,GAAkDvO,EAAAA,EAAAA,WAAS,GAAM,eAA1DsQ,EAAS,KAAEC,EAAY,KAC9B,GAA6DvQ,EAAAA,EAAAA,UAAS,IAAG,eAGnEwQ,GAHQ,KAAY,KAGD,SAACrP,GACzB,OAAQA,GAAQ,IAAI6D,KAAI,SAAC7C,EAAMsO,GAAQ,yBAAWtO,GAAI,IAAEsO,SAAAA,GAAQ,GACjE,GACA,GAAoCzQ,EAAAA,EAAAA,UAAgCwQ,EAAiBzQ,EAAM2Q,SAAQ,eAA5FC,EAAU,KAAEC,EAAa,KAChC,GAAkD5Q,EAAAA,EAAAA,UAAgCwQ,EAAiBzQ,EAAM2Q,SAAQ,eAA1GG,EAAiB,KAAEC,EAAoB,KAC9C,GAA0C9Q,EAAAA,EAAAA,UAA2C,IAAI+Q,KAAM,eAAxFC,EAAa,KAAEC,EAAgB,KAEtC,GAAoBzQ,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAAO,EAAJE,MAEXC,EAAAA,EAAAA,YAAU,WACT,GAAIb,EAAMgJ,OAAQ,CACjB,IAAMwC,EAAQxL,EAAMgJ,OAAOnG,OAAS7C,EAAMgJ,OAAS,CAAC,CACnD4D,SAAKuE,EACLpP,WAAOoP,IAER3C,EAAKI,eAAe,CACnBpD,MAAAA,IAID,IADA,IAAMpD,GAAO,OAAO0I,GACXM,EAAI,EAAGA,EAAIhJ,EAAQvF,OAAQuO,IACnC,IAAK,IAAIC,EAAI,EAAGA,EAAI7F,EAAM3I,OAAQwO,IAAK,MAExBF,IADA3F,EAAM6F,IACO7F,EAAM6F,GAAGzE,MAAQxE,EAAQgJ,GAAGlB,OACtD9H,EAAQgJ,GAAGE,MAAO,EAEpB,CAEDP,EAAqB3I,EACtB,CACD,GAAG,CAACpI,EAAMgJ,UAEVnI,EAAAA,EAAAA,YAAU,WACT,GAAIb,EAAM2Q,QAAU3Q,EAAM2Q,OAAO9N,OAAQ,CACxC,IAAM0O,EAAad,EAAiBzQ,EAAM2Q,QAC1CE,EAAcU,GAEd,IADA,IAAMC,EAAUP,EACPG,EAAI,EAAGA,EAAIG,EAAW1O,OAAQuO,IAAK,CAC3C,IAAMK,EAAQF,EAAWH,GACzBI,EAAQE,IAAID,EAAMvB,KAAMuB,EACzB,CACAP,EAAiBM,EAClB,CACD,GAAG,CAACxR,EAAM2Q,SAGV,IAAMgB,EAAgB,yCAAG,WAAO3I,GAAW,8EAC1CsG,QAAQC,IAAIvG,GACN4I,EAAS5I,EAAc,MAAE7G,QAAQ,SAACC,GAAS,QAAOA,EAAKwK,GAAG,IAC1DiF,EAASD,EAAO3M,KAAI,SAAC7C,GAAS,MAAM,CAAEwK,IAAKxK,EAAKwK,IAAK7K,MAAOK,EAAKL,MAAO,IAC9E/B,EAAMuC,SAASsP,GAAQ,2CACvB,gBALqB,sCAqBhBC,EAAsB,SAACpP,GAC5B,IAAIkK,EAAM4B,EAAKuD,cAAc,CAAC,QAASrP,EAAO,QAC9C,IAAIkK,EAqBH,OAAO,SAAC,IAAK,CAACpL,MAAO,CAAEkC,MAAO,OAASsO,aAAc,kBAAMC,GAAkB,IApB7E,IAAMC,EAAcjB,EAAckB,IAAIvF,GACtC,GAA0B,WAAX,OAAXsF,QAAW,IAAXA,OAAW,EAAXA,EAAapI,MAChB,OAAO,SAAC,IAAK,CACZtI,MAAO,CAAEkC,MAAO,OAChBkG,aAAcsI,EAAYE,aAC1BxO,YAAasO,EAAYG,YACzBL,aAAc,kBAAMC,GAAkB,IACjC,GAA0B,YAAX,OAAXC,QAAW,IAAXA,OAAW,EAAXA,EAAapI,MAAmB,CAC1C,IAAMwI,GAA4B,OAAXJ,QAAW,IAAXA,OAAW,EAAXA,EAAavR,SAAU,GAC9C,OAAO,SAAC,IAAM,CACba,MAAO,CAAEkC,MAAO,OAChB6O,yBAA0B,IAC1BC,YAAU,EACVC,KAAc,UAAR7F,EAAkB,gBAAauE,EACrCuB,iBAAiB,QACjB9Q,QAAS0Q,EAAerN,KAAI,SAAA7C,GAAI,MAAK,CAAEuH,MAAOvH,EAAKuH,MAAO5H,MAAOK,EAAKL,MAAO,KAG/E,CAIF,EAkBMkQ,EAAmB,WACxBzD,EAAKiB,gBACN,EAEA,OACC,UAAC,IACA,CACAlO,UAAU,0BACViN,KAAMA,EACNmE,SAAUhB,EACViB,cAAe,CACdpH,MAAO,CACN,CACCoB,SAAKuE,EACLpP,WAAOoP,KAGR,WAEF,UAAC,IAAG,CAAC5P,UAAU,yBAAyBsR,OAAQ,GAAIrR,OAAK,QAAIkJ,WAAY,EAAGoI,YAAa,GAAMvC,EAAY,CAAEzQ,OAAQ,IAAO,CAAEA,OAAQ,SAAW,WAChJ,SAAC,SAAS,CAACoQ,KAAI,QAAU,SACvB,SAAC6C,EAAO,GAAsB,IAAnBC,EAAG,EAAHA,IAAKC,EAAM,EAANA,OAChB,OAAO,gCAELF,EAAO9N,KAAI,SAACiO,EAAOxQ,GAClB,OACC,SAAC,IAAG,CAACoK,KAAM,EAAE,UACZ,UAAC,IAAG,CAACrC,MAAM,SAASoI,OAAQ,EAAE,WAE7B,SAAC,IAAG,CAACtR,UAAU,uBAAsB,UACpC,UAAC,UAAW,CAAC4R,SAAO,aACnB,SAAC,SAAS,CACTC,SAAO,EACPlD,KAAM,CAACgD,EAAMhD,KAAM,OACnBmD,MAAO,CAAC,CAAEC,UAAU,EAAOpJ,QAASxJ,EAAE,2BACvC,UAEC,SAAC,IAAM,CACNc,MAAO,CAAEkC,MAAO,OAChBE,YAAalD,EAAE,sBACf6B,SAAU,SAACR,IAEiB,SAACA,GAG3B,IAFA,IAAMqG,GAAO,OAAO0I,GACdyC,GAAW/E,EAAKuD,cAAc,UAAY,IAAI5P,QAAO,SAACC,GAAS,QAAOA,CAAI,IAAE6C,KAAI,SAAC7C,GAAS,OAAKA,EAAKwK,GAAG,IACpGwE,EAAI,EAAGA,EAAIhJ,EAAQvF,OAAQuO,IAAK,CACxC,IAAMhP,EAAOgG,EAAQgJ,GACjBhP,EAAK8N,OAASnO,EACjBqG,EAAQgJ,GAAGE,MAAO,EACPiC,EAAQC,SAASpR,EAAK8N,QACjC9H,EAAQgJ,GAAGE,MAAO,EAEpB,CACAP,EAAqB3I,EACtB,CACAqL,CAAmB1R,EACpB,EAAE,SAED+O,EAAkB7L,KAAI,SAAC7C,EAAMM,GAC7B,OACC,SAAC2N,EAAM,CAAC7O,MAAO,CAAEkS,QAAStR,EAAKkP,KAAO,OAAS,WAAqDvP,MAAOK,EAAK8N,KAAK,SACnH9N,EAAK+F,OAAS/F,EAAK8N,MAAI,oBADqD9N,EAAK8N,KAAI,YAAIxN,GAI7F,SAGF,SAAC,SAAS,CACT0Q,SAAO,EACPO,cAAY,EACZzD,KAAM,CAACgD,EAAMhD,KAAM,SACnBmD,MAAO,CAAC,CAAEC,UAAU,EAAOpJ,QAASxJ,EAAE,6BAAe,SAEpDoR,EAAoBpP,WAKvB,SAAC,IAAG,CAACnB,UAAU,wBAAwB4D,QAAS,WAC/C,IAAMoO,GAAW/E,EAAKuD,cAAc,UAAY,IAAI9M,KAAI,SAAC7C,GAAS,OAAKA,EAAOA,EAAKwK,SAAMuE,CAAS,IAC5F/I,GAAO,OAAO0I,GACpB,GAAIyC,EAAQ7Q,GACX,IAAK,IAAI0O,EAAI,EAAGA,EAAIhJ,EAAQvF,OAAQuO,IAAK,CAC3BhJ,EAAQgJ,GACZlB,OAASqD,EAAQ7Q,KACzB0F,EAAQgJ,GAAGE,MAAO,EAEpB,CAEDP,EAAqB3I,GACrB6K,EAAOvQ,EACR,EAAE,UACD,SAACkR,EAAA,EAAc,UAUZ,oBA5EyBV,EAAMtG,IAAG,YAAIlK,GA+E/C,IAEAkO,EAAW/N,SAAWkQ,EAAOlQ,SAC7B,SAAC,IAAG,CAACtB,UAAU,0BAA0B4D,QAAS,WACjD6N,GACD,EAAE,UACD,SAACa,EAAA,EAAY,QAIjB,KAGD,SAAC,IAAG,CAACC,KAAM,EAAE,UACZ,SAAC,IAAG,CAACtJ,QAAQ,MAAK,UACjB,SAAC,IAAM,CAACV,KAAK,UAAUiK,SAAS,SAAQ,SACtCrT,EAAE,0BAKP,SAAC,IAAG,CAACa,UAAU,2BAA0B,UACxC,SAAC,IAAG,CAAC4D,QAAS,WAtLhBqL,GAAcD,EAsLyB,EAAE/F,QAAQ,SAASC,MAAM,SAAQ,SACpE8F,GACA,iCACC,SAAC,IAAG,UAAE7P,EAAE,mBACR,SAAC,IAAG,WACH,SAACsT,EAAA,EAAY,UAId,iCACC,SAAC,IAAG,UAAEtT,EAAE,mBACR,SAAC,IAAG,WACH,SAACuT,EAAA,EAAU,eAQpB,E,+KCjRe,SAASC,GAAelU,GACnC,OAAoDC,EAAAA,EAAAA,WAAS,GAAM,eACnE,GADyB,KAAuB,MACVA,EAAAA,EAAAA,WAAS,IAAM,eAAjCkU,GAAF,KAAgB,MAClC,GAA4BlU,EAAAA,EAAAA,UAAS,IAAG,eACxC,GADa,KAAW,MACUA,EAAAA,EAAAA,UAAmB,KAAG,eACxD,GADgB,KAAc,MACAA,EAAAA,EAAAA,WAAS,IAAK,eAC5C,GADc,KAAY,MACMA,EAAAA,EAAAA,UAAuB,KAAG,eAAnDmU,EAAQ,KAAEC,EAAW,KAiF5B,IAAMC,EAAqB,SAACC,EAAuBC,EAAmBC,GAClE,IAAMxH,EAbV,SAAsBsH,GAClB,IAAItH,EAAM,KAQV,YAPuCkE,GAAlCxD,OAAe+G,gBAChBzH,EAAOU,OAAe+G,gBAAgBH,QACjBpD,GAAdxD,OAAOgH,IACd1H,EAAMU,OAAOgH,IAAID,gBAAgBH,QACNpD,GAApBxD,OAAOiH,YACd3H,EAAMU,OAAOiH,UAAUF,gBAAgBH,IAEpCtH,CACX,CAGgB4H,CAAaN,GACnB3H,EAAMvM,KAAKC,SAASC,SAAS,IAAIC,UAAU,GACjD,MAAiB,UAAbiU,GACO,iBAAKlT,UAAU,MAAK,WACvB,iBACI4D,QAAS,WACL,IAAM2P,GAAe,OAAOV,GAC5BU,EAAgBC,OAAOP,EAAW,GAClCH,EAAYS,GACZ9U,EAAMuC,UAAYvC,EAAMuC,SAASuS,EACrC,EACAvT,UAAU,6BACVC,MAAO,CAAEwD,IAAK,EAAGD,MAAO,EAAGiQ,uBAAwB,EAAGC,OAAQ,GAAI,UAClE,SAACrB,EAAA,EAAc,CAACpS,MAAO,CAAE0T,MAAO,aAEpC,kBAAO3T,UAAU,WAAWsJ,IAAKoC,EAAKkI,UAAQ,MAZjBvI,GAcb,UAAb6H,GACA,iBAAKlT,UAAU,aAAY,WAC9B,kBAAOA,UAAU,aAAasJ,IAAKoC,EAAKkI,UAAQ,KAChD,iBACIhQ,QAAS,WACL,IAAM2P,GAAe,OAAOV,GAC5BU,EAAgBC,OAAOP,EAAW,GAClCH,EAAYS,GACZ9U,EAAMuC,UAAYvC,EAAMuC,SAASuS,EACrC,EACAvT,UAAU,yBACVC,MAAO,CAAE4T,aAAc,GAAI,UAC3B,SAACxB,EAAA,EAAc,CAACpS,MAAO,CAAE0T,MAAO,cAXAtI,GAerC2H,CACX,EAEA,OACI,iCACI,yBAEQH,EAASnP,KAAI,SAACsP,EAAMC,GAChB,OAAOF,EAAmBC,EAAMC,EAAWxU,EAAMyU,UAAY,OACjE,OAGR,UAAC,aAAc,CAEXL,SAAUA,EACViB,gBAAgB,EAChBC,cAAe,SAAC1T,GACZ0N,QAAQC,IAAI,eAAe3N,EAAQ2S,MACnC,IAAMgB,EAAO,kBAAOnB,GAAQ,CAAExS,EAAQ2S,OACtCjF,QAAQC,IAAI,UAAUgG,GACtBlB,EAAYkB,GAEZC,QAAQC,IAAIF,EAAQtQ,KAAI,SAAC7C,GAAS,OAtFhCmS,EAsF8CnS,EArF5DkN,QAAQC,IAAI,WAAYgF,GACjB,IAAIiB,SAAQ,SAACE,EAASC,GACzB,GAAIpB,EAAM,CACKA,EAAKrE,KAAK0F,QAAQ,OAAQ,IACtBrB,EAAKrE,KADpB,IAEI2F,EAAS,IAAIC,WACjBD,EAAOE,cAAcxB,GACrBsB,EAAOG,OAAS,WACZN,EAAQG,EAAOI,OACnB,CACJ,MACIN,OAAOxE,EAEf,IAda,IAACoD,CAsFmD,KAAG7E,MAAK,SAAAjH,GACzD6G,QAAQC,IAAI9G,GACZzI,EAAMuC,UAAYvC,EAAMuC,SAASkG,EACrC,GASJ,EACAyN,aA1IZ,SAAsB3B,GAAe,IAAD,EAG1B4B,EAAWnW,EAAMmW,UAAY,EACnC,GAAI/B,EAASvR,QAAUsT,EAEnB,OADAjM,EAAAA,GAAAA,MAAc,qDACP,EAGX,IAAMkM,EAAyB,QAAf,EAAGpW,EAAMqW,cAAM,aAAZ,EAAc7C,SAASe,EAAKzK,MAC1CsM,GACDlM,EAAAA,GAAAA,MAAc,wCAElB,IAAMoM,EAAS/B,EAAK5N,MAAQ3G,EAAMuW,SAAW,SAI7C,OAHKD,GACDpM,EAAAA,GAAAA,MAAc,wCAEXkM,GAAcE,CACzB,EAyHY/T,SAvHS,SAACsN,GAGlB,GAFAP,QAAQC,IAAIM,GAEa,cAArBA,EAAK0E,KAAKiC,OASd,MALyB,SAArB3G,EAAK0E,KAAKiC,SACVrC,GAAe,GACfE,EAAYxE,EAAKuE,UACjBpU,EAAMuC,UAAYvC,EAAMuC,SAASsN,EAAKuE,WAEjB,YAArBvE,EAAK0E,KAAKiC,QACVnC,EAAYxE,EAAKuE,eACjBpU,EAAMuC,UAAYvC,EAAMuC,SAASsN,EAAKuE,iBAF1C,EARID,GAAe,EAavB,EAsGmC,WAEvB,eAAG5S,UAAU,uBAAsB,UAEP,SAAnBvB,EAAMyU,UAAwBzU,EAAMyU,SAAgC,MAApB,SAACgC,GAAA,EAAa,IAG5C,UAAnBzW,EAAMyU,UAAuB,SAACiC,GAAA,EAAsB,IAAM,KAGvC,UAAnB1W,EAAMyU,UAAuB,SAACkC,GAAA,EAAa,IAAM,SAGzD,cAAGpV,UAAU,kBAAiB,SAAC,gEAI/C,C,wCCtLe,SAASqV,GAAW5W,GACjC,OAAoBS,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EACR,GADe,EAAJE,MAC2BX,EAAAA,EAAAA,WAAS,IAAK,eAA7C4W,EAAW,KAAEC,EAAc,KA4BlC,OACE,4BACE,SAAC,MAAU,CACT/U,MA5Ba,SAACgV,GAClB,IACE,IAAMC,EAAS5P,KAAK6P,MAAMF,GAE1B,OADuB3P,KAAKC,UAAU2P,EAAQ,KAAM,EAEtD,CAAE,MAAOlT,GACP,OAAOiT,CACT,CACF,CAoBaG,CAAWlX,EAAM+B,OAAS,MACjCQ,SAAU,SAACR,GAAK,OApBK,SAACA,GAC1B,IACE,IAAMiV,EAAS5P,KAAK6P,MAAMlV,GACpBoV,EAAiB/P,KAAKC,UAAU2P,EAAQ,KAAM,GAChDhX,EAAMuC,UACRvC,EAAMuC,SAAS4U,GAEjBL,GAAe,EACjB,CAAE,MAAOhT,GACPgT,GAAe,GACX9W,EAAMuC,UACRvC,EAAMuC,SAASR,EAEnB,CACF,CAM2BqV,CAAmBrV,EAAM,EAC9CsV,SAAUrX,EAAMqX,SAChBvS,UAAU,QACVwS,WAAY,EACVC,EAAAA,GAAAA,MACAC,GAAAA,GAAAA,cAEF5T,YAAa5D,EAAM4D,YACnB6T,WAAY,CAAEC,aAAa,MAG3Bb,IACA,gBAAKrV,MAAO,CAAE0T,MAAO,MAAOyC,UAAW,QAAS,SAC7CjX,EAAE,oCAKb,C,sBCGO,SAASkX,GAAYC,GAMxB,OADYA,EAAQ/Q,QAAO,SAACC,EAAKC,GAAI,OAAKD,GAAcC,GAHhC,IAAI8Q,MAAM,IAAIhR,QAAO,SAACC,EAAKC,GAAI,OAAKD,EAAMC,EAAK+Q,WAAW,EAAE,GAAE,EAGzB,GAAE,EAEnE,CAEe,SAASC,GAAYhY,GAChC,IAAQiY,EAAgBjY,EAAhBiY,YACR,GAAoBxX,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EACR,GADe,EAAJE,MACmBX,EAAAA,EAAAA,UAAS,IAAE,eAAlCiY,EAAO,KAAEC,EAAU,KAC1B,GAA2ClY,EAAAA,EAAAA,UAASD,EAAMoY,QAAO,eAA1DC,EAAa,KAAEC,EAAiB,KACjCC,GAAmBC,EAAAA,EAAAA,QAAOxY,EAAMoY,QAChCK,EAAmB,SAACrX,GACtBmX,EAAiBL,QAAU9W,EAC3BkX,EAAkBlX,EACtB,EAEA,GAAqDnB,EAAAA,EAAAA,UAASD,EAAM0Y,aAAY,eAAzEC,EAAkB,KAAEC,EAAsB,KAC3CC,GAAwBL,EAAAA,EAAAA,QAAOxY,EAAM0Y,aACrCI,EAAwB,SAAC1X,GAC3ByX,EAAsBX,QAAU9W,EAChCwX,EAAuBxX,EAC3B,EAsDM2X,EAAkB,SAAC7F,EAAe8F,GACpC,IAAMC,EArDkB,SAAC/F,EAAekF,GAYxC,OARYA,EAAOjW,QAAO,SAAA+W,GAAU,OAAIA,EAAWC,IAAI3F,SAASN,EAAM,IAAEjO,KAAI,SAAA7C,GACxE,IACMgX,EAAUxB,GADDxV,EAAK+W,IAAIlU,KAAI,SAAA7C,GAAI,aAAc,QAAd,EAAIpC,EAAMwO,YAAI,aAAV,EAAYuD,cAAc3P,EAAK,IAAED,QAAO,SAAAC,GAAI,aAAe+O,IAAT/O,GAA+B,OAATA,EAAc,KAE1H,MAAO,CACHiX,OAAQjX,EAAKiX,OACb1Y,OAAQyB,EAAKkX,aAAaF,IAAY,GAE9C,GAEJ,CAwC4BG,CAAoBrG,EAAO8F,GACnDC,EAAgB5Q,SAAQ,SAAAjG,GAAS,IAAD,EAClB,QAAV,EAAApC,EAAMwO,YAAI,OAAV,EAAYI,gBAAe,UAAGxM,EAAKiX,YAASlI,IAxC3B,SAAC+B,EAAelT,GACrC,IAAMwZ,EAAYjB,EAAiBL,SAAO,OAAOK,EAAiBL,SAAW,GAC7E,GAAIsB,EACA,IAAK,IAAIpI,EAAI,EAAGA,EAAIoI,EAAU3W,OAAQuO,IAAK,CACvC,IAAMhP,EAAOoX,EAAUpI,GACnBhP,EAAK8N,OAASgD,IACdsG,EAAUpI,IAAE,kBACLhP,GACApC,GAGf,CAEJyY,EAAiBe,EACrB,CA2BQC,CAAiBrX,EAAKiX,OAAQ,CAAEzX,QAASQ,EAAKzB,SAzBxB,SAACuS,EAAelT,GAE1C,IADA,IAAM0Z,EAAiBb,EAAsBX,SAAO,OAAOW,EAAsBX,SAAW,GACnF9G,EAAI,EAAGA,EAAIsI,EAAe7W,OAAQuO,IAAK,CAE5C,IADA,IAAMuI,GAAU,OAAOD,EAAetI,GAAGgH,QAChC/G,EAAI,EAAGA,EAAIsI,EAAW9W,OAAQwO,IAAK,CACxC,IAAMjP,EAAOuX,EAAWtI,GACpBjP,EAAK8N,OAASgD,IACdyG,EAAWtI,IAAE,kBACNjP,GACApC,GAGf,CACA0Z,EAAetI,IAAE,kBACVsI,EAAetI,IAAE,IACpBgH,OAAQuB,GAEhB,CACAb,EAAsBY,EAC1B,CAOQE,CAAsBxX,EAAKiX,OAAQ,CAAEzX,QAASQ,EAAKzB,QACvD,GACJ,GAEAE,EAAAA,EAAAA,YAAU,WACN,GAAIb,EAAM+O,eAAiB/O,EAAMgZ,cAAe,CAC5C,IAAQnK,EAAkB7O,EAAM+O,cAAxBF,cACRkK,EAAgBhQ,OAAO8Q,KAAKhL,GAAe,GAAI7O,EAAMgZ,cACzD,CACJ,GAAG,CAAChZ,EAAM+O,iBAGVlO,EAAAA,EAAAA,YAAU,WAAO,IAAD,EACZ4X,EAAiBzY,EAAMoY,QACvBU,EAAsB9Y,EAAM0Y,aAC5B,IAAMoB,GAAuB,QAAV,EAAA9Z,EAAMwO,YAAI,aAAV,EAAYgB,mBAAoB,CAAC,EACpDzG,OAAO4D,QAAQmN,GAAYzR,SAAQ,YAAmB,IAAD,eAAhBuE,EAAG,UACtBuE,IAD6B,MAEvC4H,EAAgBnM,EAAK5M,EAAMgZ,eAAiB,GAEpD,GACJ,GAAG,CAAChZ,EAAM0Y,YAAa1Y,EAAMoY,OAAQH,IAErC,IAAMjR,EAAO,WACTmR,EAAWD,EAAU,EACzB,EAwBM6B,EAAc,SAAC3B,EAAgC4B,GAQjD,OAAO,SAAC,UAAS,gBAEbrQ,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAEb,SAAC,IAAK,CAACrW,SAAUyU,EAAOmC,QAAS3W,YAAawU,EAAO/F,aAAW,UAAO3R,EAAE,uBAAM,OAAG0X,EAAOzO,WAAW,sBApBhFyO,EAAOlI,MAsBnC,EA2SMsK,EAAyB,SAACpY,GAA2F,IAA7D4X,EAA8B,uDAAG,CAAC,EAC5F,OAAQ5X,EAAK0H,MACT,IAAK,QAEL,IAAK,cACD,OAAOiQ,EAAY3X,EAAM4X,GAE7B,IAAK,eACD,OA5Pc,SAAC5B,EAAgC4B,GAIvD,IAAMpY,GAAqBwW,EAAOxW,SAAW,IAAIqD,KAAI,SAAA7C,GAAI,OAAIA,EAAKuH,KAAK,IACvE,OAAO,SAAC,UAAS,gBAEbA,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAEb,SAACtY,GAAA,EAAW,CACRyD,QAAS,SAACpD,GACJqW,EAAOhX,KAAKqZ,YAAcza,EAAM0a,mBAAqB1a,EAAM0a,kBAAkB3Y,EACnF,EACAG,mBAAmB,EACnByB,SAAUyU,EAAOmC,QACjB3W,YAAW,UAAKlD,EAAE,uBAAM,OAAG0X,EAAOzO,OAClC/H,QAASA,MAAW,sBA3BJwW,EAAOlI,MA6BnC,CAyNmByK,CAAkBvY,EAAM4X,GACnC,IAAK,WACD,OAtCW,SAAC5B,EAAgC4B,GACpD,IAAMpY,EAAUwW,EAAOxW,SAAW,GAElC,OADA0N,QAAQC,IAAI3N,IACL,SAAC,UAAS,gBAEb+H,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAGb,SAAC,KAAQ,CAACpW,YAAY,SAAShC,QAASA,MAAW,sBArB/BwW,EAAOlI,MAuBnC,CAWmB0K,CAAexY,EAAM4X,GAChC,IAAK,WACD,OA3NW,SAAC5B,EAAgC4B,GACpD,OAAO,SAAC,UAAS,gBAEbrQ,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAEb,SAAC,aAAc,CAACa,SAAU,CAAEC,QAAS,GAAKnX,SAAUyU,EAAOmC,QAAS3W,YAAawU,EAAO/F,aAAW,UAAO3R,EAAE,uBAAM,OAAG0X,EAAOzO,WAAW,sBApBnHyO,EAAOlI,MAsBnC,CAmMmB6K,CAAe3Y,EAAM4X,GAChC,IAAK,OACD,OApMa,SAAC5B,EAAgC4B,GACtD,OAAO,SAAC,UAAS,gBAEbrQ,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAEb,SAACpD,GAAU,CAACS,SAAUe,EAAOmC,QAAS3W,YAAawU,EAAO/F,aAAW,UAAO3R,EAAE,uBAAM,OAAG0X,EAAOzO,WAAW,sBApBrFyO,EAAOlI,MAsBnC,CA4KmB8K,CAAiB5Y,EAAM4X,GAClC,IAAK,SACD,OA7KS,SAAC5B,EAAgC4B,GAIlD,IAAMpY,EAA0BwW,EAAOxW,SAAW,GAClD,OAAO,SAAC,UAAS,gBAEb+H,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM/Y,UAAU,MAAMiL,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,KAE3GlC,EAAOhX,KAAK6Z,WAAY,iBAAK1Z,UAAU,UAAU4D,QAAS,WACtDnF,EAAM0a,mBAAqB1a,EAAM0a,mBACrC,EAAE,WACE,SAACQ,GAAA,EAAY,CAAC1Z,MAAO,CAAE0T,MAAO,cAC9B,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,iCAC5B,SAGbsZ,GAAS,cAEb,SAAC,KAAM,CACHxY,MAAO,CAAEkC,MAAO,QAChB+O,KAAM2F,EAAO+C,SAAW,gBAAahK,EACrC5O,SAAU,SAACR,GACLqW,EAAOhX,KAAKqZ,YAAcza,EAAM0a,mBAAqB1a,EAAM0a,kBAAkB3Y,EACnF,EACAyQ,YAAU,EACV7O,SAAUyU,EAAOmC,QACjB7H,iBAAiB,QACjB9O,YAAawU,EAAO/F,aAAW,UAAO3R,EAAE,uBAAM,OAAG0X,EAAOzO,OACxD/H,QAASA,MAAW,sBAtCJwW,EAAOlI,MAwCnC,CA+HmBkL,CAAahZ,EAAM4X,GAC9B,IAAK,aACD,OAnGa,SAAC5B,EAAgC4B,GACtD,OAAO,SAAC,UAAS,gBAEbrQ,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO,CAAC,CAAEC,UAAU,EAAMpJ,QAASxJ,EAAE,oCACrCwZ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAEb,SAAC,KAAU,CAACxY,MAAO,CAAEkC,MAAO,QAAU2X,OAAQA,GAAAA,EAAQC,WAAYlD,EAAOhX,KAAKka,SAAUC,aAAc,SAACrD,GACnG,OAAOA,GAAWA,EAAUsD,MAASC,MAAM,MAC/C,MAAK,sBArBerD,EAAOlI,MAuBnC,CA0EmBwL,CAAiBtZ,EAAM4X,GAClC,IAAK,cACD,OA3Ec,SAAC5B,EAAgC4B,GACvD,OAAO,SAAC,UAAS,gBAEbrQ,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO,CAAC,CAAEC,UAAU,EAAMpJ,QAASxJ,EAAE,gDACrCwZ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAEb,SAAC,KAAU,CAACxY,MAAO,CAAEkC,MAAO,QAAU2X,OAAQA,GAAAA,EAAQC,WAAYlD,EAAOhX,KAAKka,SAAUC,aAAc,SAACrD,GACnG,OAAOA,GAAWA,EAAUsD,MAASC,MAAM,MAC/C,MAAK,sBArBerD,EAAOlI,MAuBnC,CAkDmByL,CAAkBvZ,EAAM4X,GACnC,IAAK,QACD,OApIQ,SAAC5B,EAAgC4B,GAIjD,IAAMpY,EAAUwW,EAAOxW,SAAW,GAClC,OAAO,SAAC,UAAS,gBAEb+H,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,OAAO,gCACF9B,EAAOhX,KAAK+Y,MAAO,SAAC,IAAO,CACxB5Y,UAAU,MACV6Y,UAAU,SACVjS,OAAO,iBAAMqE,wBAAyB,CAAEC,OAAQ2L,EAAOhX,KAAK+Y,QAAgB,UAE5E,iBAAK5Y,UAAU,UAAS,WACpB,SAAC8Y,EAAA,EAAsB,CAAC7Y,MAAO,CAAE0T,MAAO,cACxC,iBAAM3T,UAAU,cAAa,SAAEb,EAAE,uBAE5B,KACZ0X,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,SAE/FN,GAAS,cAEb,SAAC,WAAW,CAACpY,QAASA,MAAW,sBApBbwW,EAAOlI,MAsBnC,CAwGmB0L,CAAYxZ,EAAM4X,GAC7B,IAAK,aACD,OApXa,SAAC5B,EAAgC4B,GACtD,OAAO,SAAC,UAAS,gBAEbrQ,MAAOyO,EAAOzO,MACduG,KAAMkI,EAAOlI,KACbmD,MAAO+E,EAAO/E,MACd4G,aAAc7B,EAAOxO,aACrBsQ,MAAO9B,EAAOkC,aAAc,iBAAM9N,wBAAyB,CAAEC,OAAQ2L,EAAOkC,eAAyB,MACjGN,GAAS,cAEb,SAAC9F,GAAc,CACXO,SAAU2D,EAAOhX,KAAK0I,KACtBuM,OAAQ+B,EAAOhX,KAAKiV,OACpBF,SAAUiC,EAAOhX,KAAK+U,UAAY,MACpC,sBAZkBiC,EAAOlI,MAcnC,CAoWmB2L,CAAiBzZ,EAAM4X,GAClC,QACI,OAAO,KAEnB,EAEM8B,EAAiB,SAAC1D,GACpB,OAAQA,GAAU,IAAInT,KAAI,SAAA7C,GACtB,OAAIA,EAAK2Z,MAAQ3Z,EAAK2Z,KAAKlZ,QACN,SAAC,SAAS,CAAkCqN,KAAM9N,EAAK8N,KAAK,SACxE,SAAC6C,EAAO,GAAD,IAAIC,EAAG,EAAHA,IAAKC,EAAM,EAANA,OAAM,OACnB,gCACKF,EAAO9N,KAAI,gBAAG2H,EAAG,EAAHA,IAAKsD,EAAI,EAAJA,KAAS8L,GAAS,oBAoBlC,iBAAeza,UAAU,+BAA+BC,MAAO,CAAEya,WAAY,QAASC,SAAU,MAAO,UAE/F9Z,EAAK2Z,MAAQ3Z,EAAK2Z,KAAK9W,KAAI,SAAAkX,GACvB,OAAO3B,EAAuB2B,GAAS,kBAChCH,GAAS,IACZ9L,KAAM,CAACA,EAAMiM,EAASjM,MACtBkM,WAAY,OACZrM,SAAU,GACVvO,MAAO,CAAEoJ,cAAe,SAAUkJ,KAAM,EAAG1F,aAAc,KAEjE,KAGJ,SAAC,SAAS,WACN,SAAC,IAAM,CAACiO,QAAM,EAAClX,QAAS,kBAAM8N,EAAO/C,EAAK,EAAEoM,OAAK,EAACzO,MAAM,SAAC0O,GAAA,EAAmB,IAAK/a,MAAO,CAAEkC,MAAO,KAAM,SAClGhD,EAAE,kCAfLkM,EAkBJ,KAEV,SAAC,SAAS,CAACwG,SAAO,EAAC7R,UAAU,OAAOoI,MAAM,GAAE,UACxC,SAAC,IAAM,CAACG,KAAK,SAASvI,UAAU,OAAO4D,QAAS,kBAAM6N,GAAK,EAAEsJ,OAAK,EAACzO,MAAM,SAACgG,EAAA,EAAY,IAAI,SACrFnT,EAAE,kCAGZ,GACN,sBAjD2C0B,EAAK8N,QAqD9C,gBAAK1O,MAAO,CAAEkC,MAAO,KAAM,SAC7B8W,EAAuBpY,IAGpC,GACJ,EAEA,OACI,iCACI,SAAC,SAAS,CAEN8N,KAAMlQ,EAAMwc,YAAc,KAC1BpJ,SAAO,EACPqJ,QAAM,YAEN,SAAC,IAAK,KAAG,kBAIT9D,GAAsBA,EAAmB9V,QAAS,iCAC9C,SAAC,KAAK,CAACqV,QAASA,EAAQ,UAEfS,GAAsB,IAAI1T,KAAI,SAAC7C,EAAMM,GAClC,OAAO,SAAC,KAAAga,KAAU,CAAavU,MAAO/F,EAAKoJ,OAAnB9I,EAC5B,OAGR,gBAAKnB,UAAU,OAAM,UAEZoX,GAAsB,IAAI1T,KAAI,SAAC7C,EAAMM,GAClC,OAAO,gBAAiBnB,UAAW,CAAC2W,IAAYxV,EAAQ,SAAW,wBAAwBmG,KAAK,KAAK,SAChGiT,EAAe1Z,EAAKgW,SADR1V,EAGrB,OAGR,iBAAKnB,UAAU,YAAW,UACrB2W,EAAU,IACP,SAAC,IAAM,CAAC/S,QAAS,WA3drCgT,EAAWD,EAAU,EA2d4B,EAAC,SACzBxX,EAAE,wBAGVwX,GAAWS,GAAsB,IAAI9V,OAAS,IAC3C,SAAC,IAAM,CAACiH,KAAK,UAAUvI,UAAU,OAAO4D,QAAS,WAC7C,GAAInF,EAAMwO,KAAM,CACZ,IAAMmO,EAA6BhE,EAAmBT,GAASE,OAAOnT,KAAI,SAAA7C,GAAI,OAAIA,EAAK8N,IAAI,IAC3FlQ,EAAMwO,KAAKiB,eAAekN,GAA4BjN,MAAK,WACvD1I,GACJ,IAAG4I,OAAM,SAAAgN,GACLtN,QAAQC,IAAIqN,EAChB,GACJ,MACI5V,GAER,EAAE,SACGtG,EAAE,yBAGX,yBACKwX,KAAaS,GAAsB,IAAI9V,OAAS,IAC7C,gBAAKtB,UAAU,eAAc,SAAEb,EAAE,+DAI3C,gBAAKc,MAAO,CAAEkC,MAAO,KAAM,SAEzBoY,EAAezD,GAAiB,QAMxD,C,uCClqBe,SAASwE,GAAU7c,GAAgB,IAAD,EAChD,GAA8BC,EAAAA,EAAAA,WAAS,GAAM,eAAtCqB,EAAO,KAAEwb,EAAU,KACvB,GAAwB7c,EAAAA,EAAAA,YAA0B,eAA3CmB,EAAI,KAAE2b,EAAO,MAEpBlc,EAAAA,EAAAA,YAAU,WACNmc,EAAUhd,EAAMiN,IACvB,GAAG,CAACjN,EAAMiN,MACP,IAAM+P,EAAY,SAAC/P,GACTA,IACF6P,GAAW,IACXG,EAAAA,EAAAA,IAAoBhQ,GACfyC,MAAK,SAACjH,GACCA,EAAIrH,KACJ2b,EAAQtU,EAAIrH,KAAK6U,SAEjB3G,QAAQ4N,MAAM,uBAAwBzU,EAAIrH,MAC1C2b,OAAQ5L,GAEhB,IACCvB,OAAM,SAACgN,GACJtN,QAAQ4N,MAAM,uBAAwBN,EAC1C,IACCO,SAAQ,WACLL,GAAW,EACf,IAEZ,EACA,OACI,SAAC,IAAK,CACF7N,gBAAgB,EAChB9F,cAAc,EACdzF,MAAO,IACPkK,KAAM5N,EAAMuJ,QACZpB,MAAW,OAAJ/G,QAAI,IAAJA,OAAI,EAAJA,EAAM+G,MACbqB,SAAU,kBAAMxJ,EAAMod,oBAAmB,EAAM,EAC/C/N,KAAM,kBAAMrP,EAAMod,oBAAmB,EAAM,EAC3C3T,OACQ,OAAJrI,QAAI,IAAJA,GAAkB,QAAd,EAAJA,EAAMqM,oBAAY,OAAlB,EAAoB5K,QAChB,gBAAKtB,UAAU,mBAAkB,SAC5BH,EAAKqM,aAAaxI,KAAI,SAACyI,EAAQhL,GAAK,OACjC,UAAC,IAAM,CAEHoH,KAAK,UACL3E,QAAS,kBAAMkY,EAAAA,EAAAA,IAAgB3P,EAAO4P,OAAQ5P,EAAOT,IAAKS,EAAO6P,IAAI,EACrEhc,UAAU,OAAM,UAEfmM,EAAOG,OACJ,iBACItM,UAAU,OACViL,wBAAyB,CAAEC,OAAQiB,EAAOG,QAGjDH,EAAO1D,OAAI,uBAXStH,GAYhB,MAGjB,KACP,UAED,SAAC,IAAI,CAACrB,SAAUC,EAAQ,SACnBF,GAAO,SAACgK,GAAA,EAAU,CAAChK,KAAMA,EAAKwK,SAAW,MAAS,0BAAMlL,EAAAA,GAAAA,GAAE,0BAK3E,CC3Ce,SAAS8c,GAAgBxd,GACpC,IACMyd,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,MACjB,GAAgC3d,EAAAA,EAAAA,UAAgB,IAAG,eAA5CyM,EAAQ,KAAEmR,EAAW,KAC5B,GAA8B5d,EAAAA,EAAAA,WAAS,GAAK,eAArCqB,EAAO,KAAEwb,EAAU,KAC1B,GAAoC7c,EAAAA,EAAAA,WAAS,GAAM,eAA5C6d,EAAU,KAAEC,EAAa,KAChC,GAAoC9d,EAAAA,EAAAA,UAAuC,UAA7B+d,EAAAA,EAAAA,IAAS,kBAA+B,GAAM,eAArFC,GAAU,KAAEC,GAAa,KAChC,IAA0Cje,EAAAA,EAAAA,WAAS,GAAM,iBAAlDke,GAAa,MAAEC,GAAgB,MACtC,IAA0Cne,EAAAA,EAAAA,WAAS,GAAM,iBAAlDoe,GAAa,MAAEC,GAAgB,MACtC,IAAgDre,EAAAA,EAAAA,WAAS,GAAM,iBAAxDse,GAAgB,MAAEC,GAAmB,MAC5C,IAAoDve,EAAAA,EAAAA,UAAS,IAAG,iBAAzDwe,GAAkB,MAAEC,GAAqB,MAChD,IAA0Cze,EAAAA,EAAAA,WAAS,GAAM,iBAAlD0e,GAAa,MAAEC,GAAgB,MACtC,IAA0C3e,EAAAA,EAAAA,WAAS,GAAM,iBAAlD4e,GAAa,MAAEC,GAAgB,MACtC,IAA8C7e,EAAAA,EAAAA,UAAsB,IAAG,iBAAhE8e,GAAe,MAAEC,GAAkB,MACpCC,GAAsC,CACxC/G,QAAS,EACTgH,SAhBc,GAiBdC,MAAO,EACPC,iBAAiB,EACjBC,iBAAiB,EACjBC,gBAAiB,CAAC,GAAI,GAAI,IAAK,KAC/BC,UAAW,SAACJ,GAAK,gBAAQze,GAAE,WAAI,OAAGye,GAAK,OAAGze,GAAE,UAAI,GAEpD,IAAgCT,EAAAA,EAAAA,UAAgCgf,IAAa,iBAAtEO,GAAQ,MAAEC,GAAW,MAC5B,IAA4Cxf,EAAAA,EAAAA,UAA2B,IAAG,iBAAnEyf,GAAc,MAAEC,GAAiB,MACxC,IAAwC1f,EAAAA,EAAAA,UAAgC,IAAG,iBAApE2f,GAAY,MAAEC,GAAe,MACpC,IAAyC5f,EAAAA,EAAAA,UAA8E,IAAG,iBAAnH6f,GAAY,MAAEC,GAAgB,MAC/BC,IAAkBxH,EAAAA,EAAAA,QAAOsH,IACzBG,GAAkB,SAAC7e,GACrB4e,GAAgB9H,QAAU9W,EAC1B2e,GAAiB3e,EACrB,EACA,IAAwDnB,EAAAA,EAAAA,UAAmC,IAAG,iBAAvFigB,GAAoB,MAAEC,GAAuB,MACpD,IAA8DlgB,EAAAA,EAAAA,UAAmC,IAAG,iBAA7FmgB,GAAuB,MAAEC,GAA0B,MAC1D,IAAkEpgB,EAAAA,EAAAA,UAAwC,IAAG,iBAAtGqgB,GAAyB,MAAEC,GAA4B,MAC9D,IAAwEtgB,EAAAA,EAAAA,UAAwC,IAAG,iBAA5GugB,GAA4B,MAAEC,GAA+B,MAEhEC,GAAyC,CAAC,EAC9C,IACIA,GAAiBtZ,KAAK6P,OAAM+G,EAAAA,EAAAA,IAAS,aAAe,KACxD,CAAE,MAAOpB,IAAO,CAChB,QAAoD3c,EAAAA,EAAAA,UAASygB,IAAe,iBAArEC,GAAkB,MAAEC,GAAqB,MAChD,IAAgD3gB,EAAAA,EAAAA,UAA8B,CAAC,GAAE,iBAA1E4gB,GAAgB,MAAEC,GAAmB,MAC5C,IAAiC7gB,EAAAA,EAAAA,UAAiC,CAAC,GAAE,iBAA9D8gB,GAAQ,MAAEC,GAAY,MACvBC,IAAczI,EAAAA,EAAAA,QAAOuI,IAK3B,IAAoC9gB,EAAAA,EAAAA,UAA4D,IAAG,iBAA5FihB,GAAU,MAAEC,GAAa,MAChC,IAAoClhB,EAAAA,EAAAA,UAAS,KAAK,iBAA3C4G,GAAU,MAAEua,GAAa,MAChC,IAAsCnhB,EAAAA,EAAAA,UAAmB,IAAG,iBAArDohB,GAAW,MAAEC,GAAc,MAElC,IAA8CrhB,EAAAA,EAAAA,UAA8B,CAAC,GAAE,iBAAxEshB,GAAe,MAAEC,GAAkB,MAC1C,IAA8BvhB,EAAAA,EAAAA,YAAyB,iBAAhDwhB,GAAO,MAAEC,GAAU,MAE1B,IAA+BzhB,EAAAA,EAAAA,YAAkB,iBAA1C0hB,GAAO,MAAEC,GAAW,MACrBC,IAAarJ,EAAAA,EAAAA,QAAOmJ,IAK1B,IAAwC1hB,EAAAA,EAAAA,WAAS,GAAM,iBAAhD6hB,GAAY,MAAEC,GAAe,MACpC,IAA4C9hB,EAAAA,EAAAA,WAAS,GAAM,iBAApD+hB,GAAc,MAAEC,GAAiB,MACxC,IAAoDhiB,EAAAA,EAAAA,UAA2B,IAAG,iBAA3EiiB,GAAkB,MAAEC,GAAqB,MAChD,IAA4CliB,EAAAA,EAAAA,UAAoC,IAAG,iBAA5EmiB,GAAc,MAAEC,GAAiB,MACxC,IAAsCpiB,EAAAA,EAAAA,YAGlC,iBAHGqiB,GAAW,MAAEC,GAAc,MAIlC,IAAoCtiB,EAAAA,EAAAA,UAAS,IAAG,iBAAzCuc,GAAU,MAAEgG,GAAa,MAChC,IAAoCviB,EAAAA,EAAAA,UAAS,IAAG,iBAAzCwiB,GAAU,MAAEC,GAAa,MAChC,IAAwCziB,EAAAA,EAAAA,YAA4B,iBAA7D0iB,GAAY,MAAEC,GAAe,MACpC,IAAwC3iB,EAAAA,EAAAA,YAGpC,iBAHG4iB,GAAY,MAAEC,GAAe,MAIpC,IAA8B7iB,EAAAA,EAAAA,UAG1B,IAAG,iBAHA8iB,GAAO,MAAEC,GAAU,MAI1B,IAAsC/iB,EAAAA,EAAAA,UAAmB,IAAG,iBAArDgjB,GAAW,MAAEC,GAAc,MAClC,IAA2CjjB,EAAAA,EAAAA,WAAS,GAAK,iBAAlDkjB,GAAa,MAAEC,GAAiB,MACjCC,IAAmB7K,EAAAA,EAAAA,QAAO2K,IAKhC,IAA2CljB,EAAAA,EAAAA,WAAS,GAAM,iBAAnDqjB,GAAa,MAAEC,GAAiB,MACjCC,IAAmBhL,EAAAA,EAAAA,QAAO8K,IAKhC,IAAwCrjB,EAAAA,EAAAA,WAAS,GAAM,iBAAhDwjB,GAAY,MAAEC,GAAe,MACpC,IAAgCzjB,EAAAA,EAAAA,UA/Fd,IA+FiC,iBAA5Cif,GAAQ,MAAEyE,GAAW,MAC5B,IAAkC1jB,EAAAA,EAAAA,YAAkB,iBAA7C2jB,GAAS,MAAEC,GAAY,MAE9B,IAAoBpjB,EAAAA,EAAAA,MAAZC,GAAC,GAADA,EAER,IAFe,GAAJE,MAEmBX,EAAAA,EAAAA,UAAS,KAAG,iBAAnC6jB,GAAO,MAAEC,GAAU,MAEpBC,GAAkB,CACpBC,SAAUhF,GACVtO,OAAQ,GACRuT,UAAW3C,GACX4C,YAAQhT,IAGZtQ,EAAAA,EAAAA,YAAU,WAEV,GAAG,CAACqe,MAEJre,EAAAA,EAAAA,YAAU,WACNkjB,IAAWK,EAAAA,EAAAA,MACf,GAAG,KAEHvjB,EAAAA,EAAAA,YAAU,WACFb,GAASA,EAAMua,SACfkD,EAAS,OAEjB,GAAG,IAEH,IAAM4G,GAAqB,SAArBA,EAAsBjjB,EAA6BkjB,EAAoCC,GACzF,OAAOnjB,EAAK6D,KAAI,SAAC7C,EAAMM,GACnB,IAAIoH,EAAO1H,EAAK,YAAc,QACjB,YAAT0H,IACAA,EAAO,UAEE,SAATA,IACAA,EAAO,cAEX,IAAMH,EAAQvH,EAAKuH,OAAS2a,EAAcliB,EAAK8N,MAGzCmD,GAASjR,EAAKoiB,YAAc,IAAIvf,KAAI,SAAC7C,GACvC,GAAa,WAAT0H,EACA,MAAqB,iBAAd1H,EAAK0H,KAA0B,CAAEwJ,UAAU,EAAMpJ,QAAQ,GAAD,OAAKxJ,GAAE,sBAAM,YAAIiJ,SAAYwH,EAGhG,OAAQ/O,EAAK0H,MACT,IAAK,eACD,MAAO,CAAEwJ,UAAU,EAAMpJ,QAAQ,GAAD,OAAKxJ,GAAE,sBAAM,YAAIiJ,IACrD,IAAK,SACD,MAAO,CAAE8a,QAAS,IAAIC,OAAO,GAAD,OAAItiB,EAAKuiB,QAAUza,QAAQ,GAAD,OAAKxJ,GAAE,4DACjE,IAAK,SACD,MAAO,CAAEkkB,IAAKxiB,EAAKwiB,KAAO,EAAGC,IAAKziB,EAAKyiB,IAAK3a,QAAQ,GAAD,OAAKxJ,GAAE,sDAC9D,QACI,OAEZ,IAAGyB,QAAO,SAACC,GAAS,QAAOA,CAAI,IAEzB2Z,EAAOsI,EAAoBjiB,EAAKyN,MAAQ,GAAKyU,EAAeC,GAgBlE,MAdoC,CAChC5a,MAAAA,EACAG,KAAAA,EACAuJ,MAAAA,EACA0I,KAAAA,EACA7L,KAAM9N,EAAK8N,KACXqK,QAASnY,EAAKmY,QACdD,YAAalY,EAAKkY,aAAeiK,EAAoBniB,EAAK8N,YAASiB,EACnEmC,SAAUlR,EAAKkR,SACf1J,aAA+B,KAAjBxH,EAAK0iB,aAAiB3T,EAAY/O,EAAK0iB,QACrD3J,SAAU/Y,EAAK,YAAkC,YAApBA,EAAK,WAClCR,SAAUQ,EAAK4G,QAAU,IAAI/D,KAAI,SAAC7C,GAAS,MAAM,CAAEuH,MAAOvH,EAAKL,MAAOA,MAAOK,EAAKhC,GAAIkO,SAAUlM,EAAKkM,SAAU,IAC/GlN,MAAK,UAAMgB,GAGnB,GACJ,GAEAvB,EAAAA,EAAAA,YAAU,WACN,IAAMkkB,GAAW/G,EAAAA,EAAAA,IAAS,YACpB/Q,EAAM8X,EAAQ,4CAAwCA,EAAQ,SAAe,OAAL/kB,QAAK,IAALA,OAAK,EAALA,EAAOiN,IACrF8Q,GAAc,IAEdiH,EAAAA,EAAAA,IAAuB/X,GAAKyC,MAAK,SAAAjH,GAC7B,MA2BIA,EAAIrH,KA1BJ6jB,EAAY,EAAZA,aACAX,EAAa,EAAbA,cACAY,EAAO,EAAPA,QACAC,EAAW,EAAXA,YACAC,EAAY,EAAZA,aACA/D,EAAW,EAAXA,YACAkD,EAAmB,EAAnBA,oBACAc,EAAa,EAAbA,cAEAC,GADc,EAAdC,eACQ,EAARD,UACAE,EAAa,EAAbA,cACAC,EAAM,EAANA,OACAC,EAAU,EAAVA,WACAC,EAAc,EAAdA,eACAC,EAAW,EAAXA,YACAC,EAAW,EAAXA,YACAC,EAAU,EAAVA,WACAC,EAAW,EAAXA,YACAC,EAAa,EAAbA,cACArD,EAAY,EAAZA,aACAE,EAAY,EAAZA,aACAoD,EAAQ,EAARA,SACAC,EAAe,EAAfA,gBACAC,EAAM,EAANA,OACAC,EAAS,EAATA,UACAC,EAAU,EAAVA,WAGEC,GAAiB,OAALtmB,QAAK,IAALA,OAAK,EAALA,EAAOumB,UAAWlF,EAAY7N,SAAS,aAAe6N,EAAY7N,SAAS,aAAe6N,EAAY7N,SAAS,cAC3HgT,EAAevf,aAAawf,QAAQ,YAAD,OAAa9I,EAAS+I,WACzDC,EAAwBvf,KAAK6P,MAAMuP,GAAgB,MAAO1f,QAAO,SAACC,EAAUC,GAAS,yBAAWD,GAAG,cAAGC,EAAKe,UAAYf,EAAKtD,OAAK,GAAK,CAAC,GAEvIkjB,EAAwC7d,OAAO4D,QAAQgZ,GAAkB,CAAC,GAC3E7e,QAAO,SAACC,EAAW,GAAD,mBAAQhF,GAAF,KAAO,8BAAWgF,GAAG,CAAE,CAC5CoS,IAAKpX,EAAM8kB,YACXxN,OAAQtX,EAAM+kB,YAAYje,KAAK,IAC/ByQ,aAAcvX,EAAMwkB,QAAQzf,QAAO,SAACigB,EAAWC,GAAK,yBAAWD,GAAI,cAAGnP,GAAYoP,EAAMC,WAAaD,EAAME,UAAUjiB,KAAI,SAAA7C,GAAI,MAAK,CAAEuH,MAAOvH,EAAML,MAAOK,EAAM,KAAE,GAAK,CAAC,KACzK,GAAI,IAEH6gB,EAAcgC,EAAahgB,KAAI,SAAA2C,GACjC,IACA,GADoB0c,EAAc1c,IAAWA,GACTkQ,MAAM,KAAI,eAAvCqP,EAAM,KAAEC,EAAK,KAEpB,MAAO,CACHjf,MAAOif,GAAM,UAAC,IAAO,CAAChN,UAAU,MAAMjS,MAAOif,EAAM,UAAED,GAAO,SAACE,EAAA,EAAkB,OAAcF,EAC7Fpf,UAAWH,EACXgF,IAAKhF,EACLuc,OAAQqB,EAAchS,SAAS5L,GAAU,SAAC0f,EAAQC,GAAM,OAAKD,EAAE1f,GAAU2f,EAAE3f,EAAO,OAAGuJ,EACrFqW,OAAQ,SAACxd,EAAWyd,GAAiB,IAAD,EAChC,QAAatW,IAATnH,GAA+B,KAATA,EACtB,MAAO,IAEX,IAAI0d,EAAAA,EAAAA,IAAY1d,GAAM,CAClB,IAAM2d,EAAU5mB,SAAS6mB,cAAc,OACvCD,EAAQE,UAAY7d,EACpB,IAAM8d,EAAaH,EAAQI,kBACrBC,EAAsB,OAAVF,QAAU,IAAVA,OAAU,EAAVA,EAAYG,aAAa,QACrCC,EAAuB,OAAVJ,QAAU,IAAVA,OAAU,EAAVA,EAAYG,aAAa,cAI5C,GAFA3Y,QAAQC,IAAI,QAASyY,GACrB1Y,QAAQC,IAAI,cAAe2Y,GACvBF,GAAaE,EAAW,CACxB,GAAkB,SAAdF,EACA,OAAO,SAAC,IAAO,CAAC7f,OAAO,iBAAM5G,UAAU,eAAeiL,wBAAyB,CAAEC,OAAQyb,KAAsB9N,UAAU,UAAS,UAChI,gBAAK7Y,UAAWukB,EAAWle,GAAQkC,MAAQ,SAAU0C,wBAAyB,CAAEC,OAAQzC,OAI9F,GAAkB,oBAAdge,EACA,OAAO,gBAAK7iB,QAAS,WAAKqZ,IAAoB,GAAME,GAAsBwJ,EAAW,EAAG1b,wBAAyB,CAAEC,OAAQzC,IAEnI,CACJ,CAEA,GAAI8b,EAAWle,KAA0D,KAAxB,QAAvB,EAAAke,EAAWle,GAAQkC,YAAI,aAAvB,EAAyBzH,QAAQ,UACvD,OAAO,SAAC,IAAO,CAAC8F,OAAO,iBAAM5G,UAAU,eAAeiL,wBAAyB,CAAEC,OAAQzC,KAAiBoQ,UAAU,UAAS,UACzH,gBAAK7Y,UAAWukB,EAAWle,GAAQkC,KAAM0C,wBAAyB,CAAEC,OAAQzC,OAIpF,GAA6C,oBAAzCjB,OAAOoH,UAAU5P,SAAS6P,KAAKpG,GAA6B,CAC5D,IAAMme,EAASpf,OAAO4D,QAAQ3C,GAAMlD,QAAO,SAACC,EAAS,GAAD,mBAAG4C,EAAK,KAAE5H,EAAK,6BAAUgF,GAAG,CAAE,CAAE4C,MAAAA,EAAO5H,MAAAA,IAAO,GAAG,IACrG,OAAKomB,EAAOtlB,QAGL,gBAAKrB,MAAO,CAAE8H,SAAU,OAAQxE,UAAW,KAAM,SAEhDqjB,EAAOljB,KAAI,SAAC7C,EAAWM,GACnB,OAAO,2BAAuC4hB,EAAcliB,EAAKuH,QAAUvH,EAAKuH,MAAM,IAAEvH,EAAKL,QAAK,0BAA9DW,GACxC,MANG,GASf,CACA,OAAO,gBAAKlB,MAAO,CAAE8H,SAAU,OAAQxE,UAAW,KAAO0H,wBAAyB,CAAEC,OAAQzC,IAChG,EACAtG,MAAOijB,EAAqB/e,IAAYke,EAAWle,IAAWke,EAAWle,GAAQlE,OAAU,IAEnG,IAEM0kB,EAAarf,OAAO4D,QAAQ8Y,GAAU,CAAC,GAAG3e,QAAO,SAACC,EAAS,GAAD,mBAAShF,GAAF,KAAO,8BAAWgF,GAAG,YAAOhF,IAAK,GAAM,IACxGqgB,EAA4CgG,EAAWjmB,QAAO,SAACC,GAAS,QAAOA,EAAK+Y,QAAQ,IAC5FkN,GAA0CD,EAAWjmB,QAAO,SAACC,GAAS,QAAOA,EAAKkmB,MAAM,IAExFC,GAAmB,CACrBpgB,MAAOzH,GAAE,gBACTgD,MA9EgB,GA+EhBqE,UAAW,SACX6E,IAAK,SACLnC,MAAO,QACP+d,MAAO,QACPhB,OAAQ,SAACxd,EAAWyd,GAAiB,IAAD,EAChC,OACI,SAAC,IAAK,CAAC9gB,KAAK,SAAQ,SAEZ2f,GAAY,SAAC,IAAQ,CAACmC,SAAS,UAAC,IAAI,WAE5BjF,GAAiBtL,SAAWmL,GAAiBnL,SAAU,SAAC,SAAS,WAAC,gBAAK3W,UAAU,OAAO4D,QAAS,WAC7FujB,EAAAA,EAAAA,QAAc,CACVvgB,MAAOzH,GAAE,gBACTmN,MAAM,SAAC8a,EAAA,EAAyB,IAChC/c,QAAQ,GAAD,OAAKlL,GAAE,4BAAO,KACrBwO,OAAQxO,GAAE,4BACVyO,WAAYzO,GAAE,gBACd2O,KAAI,WACA,OAAO,IAAImG,SAAQ,SAACE,EAASC,IACzBiT,EAAAA,EAAAA,IAA2B,GAAD,OAAIlD,EAAU,oBAAY+B,EAAO7B,KACtDlW,MAAK,SAACjH,GACHiN,EAAQ,GACZ,IACC9F,OAAM,SAACgN,GACJjH,GACJ,GACR,IACKjG,MAAK,SAACjH,GACHyB,EAAAA,GAAAA,QAAgBxJ,GAAE,6BAClBmoB,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,GACV7O,OAAQqP,GAAgB9H,QACxBgM,UAAWgB,IAEnB,IACCtV,OAAM,WACH1F,EAAAA,GAAAA,MAAcxJ,GAAE,4BACpB,GACR,EACA8I,SAAQ,WAAK,GAErB,EAAE,SAAE9I,GAAE,oBAA2B,KAGjC8iB,GAAiBtL,UAAYmL,GAAiBnL,SAAU,SAAC,SAAS,WAAC,gBAAK3W,UAAU,OAAO4D,QAAS,WAC9FujB,EAAAA,EAAAA,QAAc,CACVvgB,MAAOzH,GAAE,4BACTmN,MAAM,SAAC8a,EAAA,EAAyB,IAChC/c,QAAQ,GAAD,OAAKlL,GAAE,wCAAS,KACvBwO,OAAQxO,GAAE,wCACVyO,WAAYzO,GAAE,gBACd2O,KAAI,WACA,OAAO,IAAImG,SAAQ,SAACE,EAASC,IACzBmT,EAAAA,EAAAA,IAAiC,GAAD,OAAIpD,EAAU,oBAAY+B,EAAO7B,KAC5DlW,MAAK,SAACjH,GACHiN,EAAQ,GACZ,IACC9F,OAAM,SAACgN,GACJjH,GACJ,GACR,IACKjG,MAAK,SAACjH,GACHyB,EAAAA,GAAAA,QAAgBxJ,GAAE,6BAClBmoB,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,GACV7O,OAAQqP,GAAgB9H,QACxBgM,UAAWgB,IAEnB,IACCtV,OAAM,WACH1F,EAAAA,GAAAA,MAAcxJ,GAAE,4BACpB,GACR,EACA8I,SAAQ,WAAK,GAErB,EAAE,SAAE9I,GAAE,gCAA6B,KAGnC2gB,EAAY7N,SAAS,aAAc,SAAC,SAAS,WAAC,gBAAKjS,UAAU,OAAO4D,QAAS,WACzE2Z,IAAiB,GACjBiK,GAAgBtB,EAAO7B,GAC3B,EAAE,SACGllB,GAAE,oBACc,KAGrB2gB,EAAY7N,SAAS,aAAc,SAAC,SAAS,WAAC,gBAAKjS,UAAU,OAAO4D,QAAS,WACzEmZ,IAAiB,IACjB0G,EAAAA,EAAAA,IAAuBU,EAAY+B,EAAO7B,IAAclW,MAAK,SAAAjH,GACzD,MAA4EA,EAAIrH,KAAxEgkB,EAAY,EAAZA,aAAcd,EAAa,EAAbA,cAAeC,EAAmB,EAAnBA,oBAAoBgB,EAAc,EAAdA,eACnDyD,EAA6C3E,GAAmBe,EAAcd,EAAeC,GAC7F1D,EAAmBuE,EAAate,QAAO,SAACC,EAAUC,GAAS,yBAAWD,GAAG,cAAGC,EAAKkJ,KAAOlJ,GAAI,GAAK,CAAC,GACxGoe,EAAa/c,SAAQ,SAACjG,GACM,SAApBA,EAAK,YACLA,EAAKyN,KAAKxH,SAAQ,SAAC4gB,GACfpI,EAAiBoI,EAAS/Y,MAAQ+Y,CACtC,GAER,IACAnI,GAAoBD,GACpB,IAAMqI,EAAuD3D,EAAetgB,KAAI,SAAAuG,GAC5E,IAAM2d,EAAc3d,EAAMuH,OAAO9N,KAAI,SAAAiO,GAAK,OAAI2N,EAAiB3N,EAAM,IAAE/Q,QAAO,SAAAC,GAAI,QAAMA,CAAI,IAC5F,MAAO,CACHoJ,MAAOA,EAAMA,MACb4d,SAAU5d,EAAM4d,SAChBhR,OAAQiM,GAAmB8E,EAAa7E,EAAeC,GAE/D,IAEAlE,GAA2B2I,GAC3BvI,GAAgCyI,GAEhCH,GAAgBtB,EAAO7B,GAC3B,IAAGhW,OAAM,WACL1F,EAAAA,GAAAA,KAAaxJ,GAAE,oDACnB,GACJ,EAAE,SACGA,GAAE,oBACc,KAGrB2gB,EAAY7N,SAAS,eAAgB,SAAC,SAAS,WAAC,gBAAKjS,UAAU,YAAY4D,QAAS,WAChFujB,EAAAA,EAAAA,QAAc,CACVvgB,MAAOzH,GAAE,gBACTmN,MAAM,SAAC8a,EAAA,EAAyB,IAChC/c,QAAQ,GAAD,OAAKlL,GAAE,4BAAO,KACrBwO,OAAQxO,GAAE,4BACVyO,WAAYzO,GAAE,gBACd2oB,cAAe,CAAEhN,QAAQ,GACzBhN,KAAI,WACA,OAAO,IAAImG,SAAQ,SAACE,EAASC,IACzB2T,EAAAA,EAAAA,IAAyB,GAAD,OAAI5D,GAAU,OAAG+B,EAAO7B,KAC3ClW,MAAK,SAACjH,GACHiN,EAAQ,GACZ,IACC9F,OAAM,SAACgN,GACJjH,GACJ,GACR,IACKjG,MAAK,SAACjH,GACHyB,EAAAA,GAAAA,QAAgBxJ,GAAE,6BAClBmoB,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,GACV7O,OAAQqP,GAAgB9H,QACxBgM,UAAWgB,IAEnB,IACCtV,OAAM,WACH1F,EAAAA,GAAAA,MAAcxJ,GAAE,4BACpB,GACR,EACA8I,SAAQ,WAAK,GAErB,EAAE,SACG9I,GAAE,oBACc,KAGhB,OAALV,QAAK,IAALA,GAAc,QAAT,EAALA,EAAOumB,eAAO,WAAT,EAAL,EAAgBthB,KAAI,SAAC7C,EAAMM,GACvB,OAAO,SAAC,SAAS,WACb,gBAAKnB,UAAU,OAAO4D,QAAS,WAC3BsY,EAAS,GAAD,OAAIE,EAAS+I,SAAQ,YAAItkB,EAAK8N,KAAI,eAAOuX,EAAO7B,IAC5D,EAAE,SACGxjB,EAAK+F,SACJ,qBAL2BzF,GAOzC,MAGE2lB,GAAaxlB,QAAUwlB,GAAapjB,KAAI,SAACwgB,EAAQ/iB,GAC/C,OAAO,SAAC,SAAS,WAA+B,gBAAKnB,UAAU,OAAO4D,QAAS,WAC3EujB,EAAAA,EAAAA,QAAc,CACVvgB,MAAOsd,EAAO8D,aACd1b,MAAM,SAAC8a,EAAA,EAAyB,IAChC/c,QAAS,GACTsD,OAAQxO,GAAE,gBACVyO,WAAYzO,GAAE,gBACd2O,KAAI,WACA,OAAO,IAAImG,SAAQ,SAACE,EAASC,IACzB6T,EAAAA,EAAAA,IAAyB,GAAD,OAAI9D,EAAU,kBAAUD,EAAOvV,KAAI,YAAIuX,EAAO7B,KACjElW,MAAK,SAACjH,GACHiN,EAAQjN,EACZ,IACCmH,OAAM,SAACgN,GACJjH,EAAOiH,EACX,GACR,IACKlN,MAAK,SAACjH,GACHyB,EAAAA,GAAAA,QAAgBxJ,GAAE,6BAEd+H,EAAIrH,KAAK6U,OAAOwT,MAChB9b,OAAOC,KAAKnF,EAAIrH,KAAK6U,OAAOwT,KAAM,QAEtCZ,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,GACV7O,OAAQqP,GAAgB9H,QACxBgM,UAAWgB,IAEnB,IACCtV,OAAM,WACH1F,EAAAA,GAAAA,MAAcxJ,GAAE,4BACpB,GACR,EACA8I,SAAQ,WAAK,GAErB,EAAE,SACG9I,GAAE,GAAD,OAAI+kB,EAAOzb,UACX,uBAtCiCtH,GAuC3C,OAEA,UACJ,iBAAKnB,UAAU,OAAM,UAAEb,GAAE,iBAAM,SAACsT,EAAA,EAAY,SAClC,MAI9B,GAEE0V,IAAkD,OAAOzG,GAC3DqD,GACAoD,GAAWhhB,KAAK6f,IAGpB,IAAMoB,GAAgBxE,EAAYre,QAAO,SAACC,EAAUC,GAAS,yBAAWD,GAAG,cAAGC,EAAKkJ,KAAOlJ,GAAI,GAAK,CAAC,GACpG,GAAI0Z,IAAkB3X,OAAO8Q,KAAK6G,IAAgB7d,OAAQ,CACtD,IAAM+mB,IAAkB7gB,OAAO8Q,KAAK6G,KAAmB,IAAIve,QAAO,SAAAyK,GAAG,OAAI8T,GAAe9T,IAAQ+c,GAAc/c,IAAQ+c,GAAc/c,GAAK6N,UAAU,IAC/IoP,GAAgBD,GAAe/mB,OAkCnC,GAAI+mB,GAAe/mB,OAAQ,CACvB,IAAMinB,GAAUpJ,GAChB,IAAK,IAAM9T,MAAOkd,GAAS,CACvB,GAAI/gB,OAAOoH,UAAU4Z,eAAe3Z,KAAK0Z,GAASld,SAEhCuE,IADA2Y,GAAQld,YAEXkd,GAAQld,GAG3B,EAzCoB,SAAlBod,EAAmBC,GACrBJ,IAAgC,GAEhCK,EAAAA,EAAAA,IAA4B,GAAD,OAAIxE,EAAU,SAAS,CAAEyE,eAAgBF,IAAOva,MAAK,SAAAjH,GAC5E,MAA2EA,EAAIrH,KAAvE+jB,EAAW,EAAXA,YAAab,EAAa,EAAbA,cAAeC,EAAmB,EAAnBA,oBAAqBc,EAAa,EAAbA,cACnDsE,EAAgBxE,EAAYre,QAAO,SAACC,EAAUC,GAAS,yBAAWD,GAAG,cAAGC,EAAKkJ,KAAOlJ,GAAI,GAAK,CAAC,GAC9FojB,EAA0C/F,GAAmBc,EAAab,EAAeC,GACzF8F,EAAoDhF,EAAcpgB,KAAI,SAAAuG,GACxE,IAAM2d,EAAc3d,EAAMuH,OAAO9N,KAAI,SAAAiO,GAAK,OAAIyW,EAAczW,EAAM,IAAE/Q,QAAO,SAAAC,GAAI,QAAMA,CAAI,IACzF,MAAO,CACHoJ,MAAOA,EAAMA,MACb4d,SAAU5d,EAAM4d,SAChBhR,OAAQiM,GAAmB8E,EAAa7E,EAAeC,GAE/D,IACM+F,EAAYnF,EAAYhjB,QAAO,SAACC,GAAI,MAAsB,KAAjBA,EAAK0iB,OAAc,IAAE7f,KAAI,SAAA2C,GAAM,iBAAQA,EAAOsI,KAAOtI,EAAOkd,QAAO,IAAKhe,QAAO,SAACC,EAAKC,GAAI,yBAAWD,GAAQC,EAAI,GAAK,CAAC,GAMrK,GAJA4Z,GAAsB0J,GACtBnK,GAAwBiK,GACxB7J,GAA6B8J,GAEzBR,GAAe,CACf,IAAMU,EAASnjB,KAAKC,UAAUijB,GAC9BN,EAAgBO,EACpB,CACJ,IAAG3a,OAAM,SAAAgN,GACL1S,EAAAA,GAAAA,MAAcxJ,GAAE,wCACpB,IAAGyc,SAAQ,WACPY,GAAc,EAClB,GACJ,CAcIiM,CAFY5iB,KAAKC,UAAUyiB,IAG/B,CACJ,CAEA,IAAMjJ,GAAmBuE,EAAate,QAAO,SAACC,EAAUC,GAAS,yBAAWD,GAAG,cAAGC,EAAKkJ,KAAOlJ,GAAI,GAAK,CAAC,GACxGoe,EAAa/c,SAAQ,SAACjG,GACM,SAApBA,EAAK,YACLA,EAAKyN,KAAKxH,SAAQ,SAAC4gB,GACfpI,GAAiBoI,EAAS/Y,MAAQ+Y,CACtC,GAER,IACA,IAAMmB,GAA0C/F,GAAmBc,EAAab,EAAeC,GACzF8F,GAAoDhF,EAAcpgB,KAAI,SAAAuG,GACxE,IAAM2d,EAAc3d,EAAMuH,OAAO9N,KAAI,SAAAiO,GAAK,OAAIyW,GAAczW,EAAM,IAAE/Q,QAAO,SAAAC,GAAI,QAAMA,CAAI,IACzF,MAAO,CACHoJ,MAAOA,EAAMA,MACb4d,SAAU5d,EAAM4d,SAChBhR,OAAQiM,GAAmB8E,EAAa7E,EAAeC,GAE/D,IAEMiG,GAAmCzhB,OAAO4D,QAAQuY,GACnDpe,QAAO,SAACC,EAAS,GAAoB,IAAD,eAAjBmJ,EAAI,KAAEnO,EAAK,KAC3B,MAAM,GAAN,eAAWgF,GAAG,CAAE,CACZmJ,KAAAA,EACApG,KAAM/H,EAAM,YAAc,QAC1BoG,MAAOmc,EAAcpU,GACrBua,QAAS1oB,EAAMI,OAAO8C,KAAI,SAAA7C,GAAI,OAAIA,EAAKsoB,QAAQ,IAC/CtY,aAAgC,KAAlBrQ,EAAM+iB,aAAiB3T,EAAYpP,EAAM+iB,QACvDnkB,OAAQoB,EAAMiH,OAASjH,EAAMiH,OAAO/D,KAAI,SAAA7C,GAAI,MAAK,CAAEuH,MAAOvH,EAAKL,MAAOA,MAAOK,EAAKhC,GAAI,SAAK+Q,IAEnG,GAAG,IAEHwZ,GAAsB5hB,OAAO4D,QAAQuY,GACpCpe,QAAO,SAACC,EAAS,GAAmB,IAAD,eAAhB6F,EAAG,KAAE7K,EAAK,KAC1B,MAAM,GAAN,eAAWgF,GAAG,CAAE,CACZ6F,IAAAA,EACA7K,MAAOA,EAAM+iB,UAErB,GAAG,IAAI3iB,QAAO,SAACC,GAAS,OAAKA,EAAKL,KAAK,IAErC6oB,GAAmBxjB,KAAK6P,MAAMhQ,aAAawf,QAAQ,UAAD,OAAW9I,EAAS+I,UAAQ,OAAG/I,EAASkN,UAAa,MACzGC,QAAY3Z,EAChB,IAAI6M,EAAAA,EAAAA,IAAS,UACT,IACI8M,GAAY1jB,KAAK6P,OAAM+G,EAAAA,EAAAA,IAAS,WAAa,KACjD,CAAE,MAAOd,IACLhT,EAAAA,GAAAA,MAAcxJ,GAAE,kCACpB,CAEJ,IA1gBkBU,GA0gBZ2pB,GAAcD,IAAaF,GAC7BG,IAAeA,GAAYloB,SAC3B8nB,GAAsBI,IAG1BlH,GAAawC,GACb1C,GAAYyC,GACZ1C,GAAgByC,GAjhBE/kB,GAkhBD8kB,EAjhBrB1C,GAAiBtL,QAAU9W,GAC3BmiB,GAAkBniB,IAihBd4hB,GAAWiD,GACX/C,GAAe+B,GACfrC,GAAgBD,GAChBG,GAAgBD,GAChBZ,GAAkB+D,GAClBjE,GAAgBgE,GAChBrD,GAAcmD,GACdrD,GAAcoD,GACdzD,GAAsByE,GACtBvE,GAAkBD,GA5jBP,SAAChhB,GAChBygB,GAAW3J,QAAU9W,EACrBwgB,GAAYxgB,EAChB,CA0jBQ4pB,CAAWtF,GACX5E,GAAoBD,IACpBW,GAAmB0D,GACnBvF,GAAkB+J,IAClB7J,GAAgB2K,IAChBrK,GAAwBiK,IACxB7J,GAA6B8J,IAhlBjB,SAACjpB,GACjB6f,GAAY/I,QAAU9W,EACtB4f,GAAa5f,EACjB,CA+kBQ6pB,CAAY3G,GACZhD,GAAeD,GACf,IAAM6J,GAAoB1E,EAAekD,GAAW5iB,QAAO,SAACC,EAAUC,GAAS,OAAKD,EAAMC,EAAKtD,OAAS,GAAG,GAAE,GAAK,GAAyB,IAApBgmB,GAAW7mB,OAAe,GAnb7H,GAobpBue,GAAc8J,IACdxJ,GAAW4D,GACXrF,GAAgB0K,IAEhB9B,GAAU,CACN5E,UAAS,kBACFhF,IAAY,IACfC,SAAUkH,IAEdzV,OAAQga,GACRzG,UAAWgB,EACXf,YAAQhT,GAGhB,IAAGvB,OAAM,SAAAgN,GACLtN,QAAQC,IAAIqN,EAChB,IAAGO,SAAQ,WACPL,GAAW,GACXiB,GAAc,EAClB,GACJ,GAAG,IAEH,IAAMoN,GAAqB,SAACxa,EAAeuT,GACvC,IACMkH,GAAYpN,EAAAA,EAAAA,IAAS,MA8B3B,MA5Ba,CACTkH,QAAS,CACLkG,EAAY,CACR1jB,IAAU,OAAL1H,QAAK,IAALA,OAAK,EAALA,EAAOqrB,WACZC,IAAK,UACLvpB,OAAQqpB,QACRja,GAAS,eACVR,EAAOxO,QAAO,SAAAsP,GAAK,YAAoBN,IAAhBM,EAAM1P,KAAmB,IAAEkD,KAAI,SAACwM,GAKtD,IAJA,IAAI6Z,EAAM,GACJb,EAAU,CAAC,UAAW,KAAM,MAC5Bc,EAA0BrH,EAAUzS,EAAM7E,KAAKzK,OAAO8C,KAAI,SAAC7C,GAAS,OAAKA,EAAKsoB,QAAQ,KAAK,GAExFtZ,EAAI,EAAGA,EAAIqZ,EAAQ5nB,OAAQuO,IAAK,CACrC,IAAMoa,EAAaf,EAAQrZ,GAC3B,GAAIma,EAAc/X,SAASgY,GAAa,CACpCF,EAAME,EACN,KACJ,CACJ,CAEA,MAAO,CACH9jB,IAAK+J,EAAM7E,IACX0e,IAAKA,EACLvpB,MAAO0P,EAAM1P,MAErB,MACFI,QAAO,SAAAC,GAAI,QAAMA,CAAI,IAG/B,EAEMymB,GAAY,WAYP,IAAD,yDANW,CACb5E,SAAUhF,GACVtO,OAAQmP,GACRoE,UAAW3C,GACX4C,YAAQhT,EACRsa,eAAe,GAVnBxH,EAAQ,EAARA,SACAtT,EAAM,EAANA,OACAuT,EAAS,EAATA,UACAC,EAAM,EAANA,OACAsH,EAAa,EAAbA,cAQA3O,GAAW,GAEX,IAAM4O,EAAYtkB,KAAKC,WAAU,kBAC1B8jB,GAAmBxa,EAAQuT,IAAU,IACxCuH,cAAAA,EACAE,YAAa,EACbC,MAAO3H,EAAS/L,SAAW,GAAK,EAChCkO,UAAWnC,EAAS/E,UAAY,IAC7BiF,KAGP0H,EAAAA,EAAAA,IAAoBhK,GAAW3J,QAAS,CACpCwT,UAAAA,IAEChc,MAAK,SAACjH,GACH,MAAwBA,EAAIrH,KAAK6U,OAAzB6V,EAAK,EAALA,MAAO1qB,EAAI,EAAJA,KACfyc,EAAYzc,GACZ4d,GAAmB,IACnBS,IAAY,0BAAKR,IAAiBgF,GAAQ,IAAE9E,MAAO2M,KACnDvJ,GAAe4B,EACnB,IACCvU,OAAM,SAACsN,GACJ5N,QAAQC,IAAI2N,EAChB,IACCC,SAAQ,kBAAML,GAAW,EAAM,GACxC,EAEMiM,GAAkB,SAAC3oB,GACrBwe,IAAiB,GACjBuC,GAAc,KACd4K,EAAAA,EAAAA,IAAsB,GAAD,OAAIlK,GAAW3J,SAAO,OAAG9X,IACzCsP,MAAK,SAAAjH,GACF,IAAMrH,EAAOqH,EAAIrH,KAAK6U,OAChB+V,EAAgB,GAChBC,EAAc,SAAC7qB,GACjB,MAA6C,oBAAzC2H,OAAOoH,UAAU5P,SAAS6P,KAAKhP,GACxBA,EAAK8qB,UAET9qB,CACX,EACA2H,OAAO8Q,KAAKzY,GAAMiH,SAAQ,SAAAuE,GACtBof,EAAOtjB,KAAK,CACRiB,MAAOsX,GAAY/I,QAAQtL,IAAQA,EACnC7K,MAAOkqB,EAAY7qB,EAAKwL,IACxBA,IAAAA,GAER,IACAuU,GAAc6K,EAClB,IACCpc,OAAM,SAAAgN,GAAS,IACfO,SAAQ,WAAQyB,IAAiB,EAAO,GACjD,EA2CMuN,GAA4B,CAC9Bjc,KAAM,WACNiG,SAAU,EACVsP,OAAO,GAAD,OAAK9D,GAAO,WAClByK,QAAS,CACLC,cAAe,sBAEnBnW,aAAc,SAAA3B,GACV,IAAM+X,GAAuC,IAA/B/X,EAAKrE,KAAK7N,QAAQ,QAC1BkqB,GAAuC,IAA/BhY,EAAKrE,KAAK7N,QAAQ,QAC1BmqB,GAAyC,IAAhCjY,EAAKrE,KAAK7N,QAAQ,SAC3BoqB,GAAyC,IAAhClY,EAAKrE,KAAK7N,QAAQ,SACjC,GAAIiqB,GAASE,GAAUD,GAASE,EAC5B,OAAO,EAEPviB,EAAAA,GAAAA,MAAc,wDAEtB,EACA3H,SAAQ,SAACsN,GACoB,SAArBA,EAAK0E,KAAKiC,OACVkW,EAAAA,EAAAA,QAAwB,CACpBxiB,QAASxJ,GAAE,4BACX4Z,YAAalT,KAAKC,UAAUwI,EAAK0E,KAAKoY,YAEd,UAArB9c,EAAK0E,KAAKiC,QACjBkW,EAAAA,EAAAA,MAAsB,CAClBxiB,QAASxJ,GAAE,4BACX4Z,YAAalT,KAAKC,UAAUwI,EAAK0E,KAAKoY,WAGlD,GAGJ,OACI,iBAAKprB,UAAU,wBAAuB,WAElC,SAAC,EAAS,CACN4G,MAAK,UAAKzH,GAAE,gBAAK,YAAI+hB,IAErB9T,SAAUgS,GACVrf,QAASwc,EACTvU,QAAS0U,GACTzU,SAAU,WAAQ0U,IAAc,EAAO,EACvCvO,SAAU,SAAC3G,EAAQwF,GAEf,IAAK,IAAM5B,KADXmR,GAAc,GACI/U,EACd,GAAID,OAAOoH,UAAU4Z,eAAe3Z,KAAKpH,EAAQ4D,GAAM,CACnD,IAAM7K,EAAQiH,EAAO4D,GACrB,GAAIggB,MAAMC,QAAQ9qB,GAAQ,CACtB,GAAIA,EAAM,IAAmD,oBAA7CgH,OAAOoH,UAAU5P,SAAS6P,KAAKrO,EAAM,IACjD,SAEJiH,EAAO4D,GAAO5D,EAAO4D,GAAK/D,KAAK,IACnC,CACJ,EAEJikB,EAAAA,EAAAA,IAAsBjL,GAAW3J,QAASlP,GAAQ0G,MAAK,SAACjH,GACpDyB,EAAAA,GAAAA,QAAgB,GAAD,OAAIxJ,GAAE,gBAAK,YAAI+hB,GAAU,YAAI/hB,GAAE,kBAC9C8N,EAAKY,cACL8O,IAAc,GACd2K,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,GACV7O,OAAQmP,GACRqE,OAAQ7B,GACR4B,UAAW3C,KAEnB,IAAG3R,OAAM,SAAAgN,GACL1S,EAAAA,GAAAA,MAAc,GAAD,OAAIxJ,GAAE,gBAAK,YAAI+hB,GAAU,YAAI/hB,GAAE,iBAChD,IAAGyc,SAAQ,WACPY,GAAc,EAClB,GACJ,EAAE,SAGE,SAACvP,EAAoBO,GAAkB,OAAK,SAACiJ,GAAW,CAACxJ,KAAMA,EAAMkM,kBAAmB,SAAC3Y,GACrFgc,GAAc,GAEd,IAAM+L,EAAUtb,EAAKgB,iBACrB,IAAK,IAAM5C,KAAOkd,EAAS,CACvB,GAAI/gB,OAAOoH,UAAU4Z,eAAe3Z,KAAK0Z,EAASld,QAEhCuE,IADA2Y,EAAQld,WAEXkd,EAAQld,EAG3B,CACA,IAAMqd,EAAM7iB,KAAKC,UAAUyiB,GAC3Btb,EAAKY,eAEL8a,EAAAA,EAAAA,IAA4B,GAAD,OAAIrI,GAAW3J,QAAO,SAAS,CAAEiS,eAAgBF,IAAOva,MAAK,SAAAjH,GACpF,MAA2EA,EAAIrH,KAAvE+jB,EAAW,EAAXA,YAAab,EAAa,EAAbA,cAAeC,EAAmB,EAAnBA,oBAAqBc,EAAa,EAAbA,cACnDsE,EAAgBxE,EAAYre,QAAO,SAACC,EAAUC,GAAS,yBAAWD,GAAG,cAAGC,EAAKkJ,KAAOlJ,GAAI,GAAK,CAAC,GAC9FojB,EAA0C/F,GAAmBc,EAAab,EAAeC,GACzF8F,EAAoDhF,EAAcpgB,KAAI,SAAAuG,GACxE,IAAM2d,EAAc3d,EAAMuH,OAAO9N,KAAI,SAAAiO,GAAK,OAAIyW,EAAczW,EAAM,IAAE/Q,QAAO,SAAAC,GAAI,QAAMA,CAAI,IACzF,MAAO,CACHoJ,MAAOA,EAAMA,MACb4d,SAAU5d,EAAM4d,SAChBhR,OAAQiM,GAAmB8E,EAAa7E,EAAeC,GAE/D,IACM+F,EAAYnF,EAAYhjB,QAAO,SAACC,GAAI,MAAsB,KAAjBA,EAAK0iB,OAAc,IAAE7f,KAAI,SAAA2C,GAAM,iBAAQA,EAAOsI,KAAOtI,EAAOkd,QAAO,IAAKhe,QAAO,SAACC,EAAKC,GAAI,yBAAWD,GAAQC,EAAI,GAAK,CAAC,GAErKwH,EAAKI,eAAe0b,GACpBnK,GAAwBiK,GACxB7J,GAA6B8J,EACjC,IAAGza,OAAM,SAAAgN,GACL1S,EAAAA,GAAAA,MAAcxJ,GAAE,wCACpB,IAAGyc,SAAQ,WACPY,GAAc,EAClB,GAEJ,EAAGhP,cAAeA,EAAeiK,cAAekJ,GAAoB9J,OAAQ8H,GAAsBxH,YAAa4H,IAA6B,KAIpJ,SAAC,EAAS,CACNnY,MAAK,UAAKzH,GAAE,gBAAK,YAAI+hB,IAErB9T,SAAUuS,GAAWpa,QAAO,SAACC,EAAKC,GAC9B,GAAsD,YAAjD6Z,GAAiB7Z,EAAK4F,MAAQ,CAAC,GAAG,WAAyB,CAC5D,IAAI7K,EAAQiF,EAAKjF,MACXH,GAAWif,GAAiB7Z,EAAK4F,MAAQ,CAAC,GAAG5D,QAAU,GACvD+jB,EAAWnrB,EAAQqD,KAAI,SAAC7C,GAAS,OAAKA,EAAKL,KAAK,IAAEM,QAAQ2E,EAAKjF,OAIrE,OAHKgrB,IACDhrB,EAAQH,EAAQmrB,GAAU3sB,KAEvB,kBAAK2G,GAAG,cAAGC,EAAK4F,IAAM7K,GACjC,CAEI,IAAIirB,EADR,GAAsD,aAAjDnM,GAAiB7Z,EAAK4F,MAAQ,CAAC,GAAG,WAcnC,OAXIogB,EADDJ,MAAMC,QAAQ7lB,EAAKjF,OACDiF,EAAKjF,MAAMkD,KAAI,SAAC7C,GACzB,MAAoB,kBAATA,GAA8B,OAATA,GAAiB,OAAQA,EAC9CA,EAAKhC,GAELgC,CAEf,KAGc4E,EAAKjF,OAAS,IAAI+V,MAAM,MAEvC,kBAAK/Q,GAAG,cAAGC,EAAK4F,IAAMogB,IAGjC,GAAsD,gBAAjDnM,GAAiB7Z,EAAK4F,MAAQ,CAAC,GAAG,WAA6B,CAChE,IAAI7K,EAAQiF,EAAKjF,MAEjB,OADAA,EAAQyZ,IAAOzZ,IACR,kBAAKgF,GAAG,cAAGC,EAAK4F,IAAM7K,GACjC,CAEA,GAAsD,UAAjD8e,GAAiB7Z,EAAK4F,MAAQ,CAAC,GAAG,WAAuB,CAC1D,IAAM7K,GAASiF,EAAKjF,OAAS,IAAIkD,KAAI,SAAC7C,GAClC,IAAK,IAAM6qB,KAAe7qB,EACtB,GAAI2G,OAAOoH,UAAU4Z,eAAe3Z,KAAKhO,EAAM6qB,GAAc,CACzD,IAAMC,EAAgB9qB,EAAK6qB,GAC8B,gBAApDpM,GAAiBoM,IAAgB,CAAC,GAAG,aACtC7qB,EAAK6qB,GAAezR,IAAO0R,GAEnC,CAEJ,OAAO9qB,CACX,IACA,OAAO,kBAAK2E,GAAG,cAAGC,EAAK4F,IAAM7K,GACjC,CAEA,OAAO,kBAAKgF,GAAG,cAAGC,EAAK4F,IAAM5F,EAAKjF,OACtC,GAAG,CAAC,GACJT,QAAS6c,IAAiBQ,GAC1BpV,QAAS8U,GACT7U,SAAU,WAAQ8U,IAAiB,EAAO,EAC1C3O,SAAU,SAAC3G,GAGP,IAAK,IAAM4D,KAFXwR,IAAiB,GACjB+C,GAAc,IACInY,EACd,GAAID,OAAOoH,UAAU4Z,eAAe3Z,KAAKpH,EAAQ4D,GAAM,CACnD,IAAM7K,EAAQiH,EAAO4D,GACrB,GAAIggB,MAAMC,QAAQ9qB,GAAQ,CACtB,GAAIA,EAAM,IAAmD,oBAA7CgH,OAAOoH,UAAU5P,SAAS6P,KAAKrO,EAAM,IACjD,SAEJiH,EAAO4D,GAAO5D,EAAO4D,GAAK/D,KAAK,IACnC,CACJ,EAEJskB,EAAAA,EAAAA,IAAyB,GAAD,OAAItL,GAAW3J,SAAO,OAAGlP,EAAOwT,KAAexT,GAClE0G,MAAK,SAAAjH,GACFyB,EAAAA,GAAAA,QAAgB,GAAD,OAAIxJ,GAAE,gBAAK,YAAI+hB,GAAU,YAAI/hB,GAAE,kBAC9C4d,IAAiB,GACjBuK,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,GACV7O,OAAQmP,GACRqE,OAAQ7B,GACR4B,UAAW3C,KAEnB,IACC3R,OAAM,SAAAgN,GACH1S,EAAAA,GAAAA,MAAc,GAAD,OAAIxJ,GAAE,gBAAK,YAAI+hB,GAAU,YAAI/hB,GAAE,iBAChD,IACCyc,SAAQ,WAAQiB,IAAiB,EAAO,GACjD,EAAE,SAGE,SAAC5P,GAAkB,OAAK,SAACwJ,GAAW,CAACxJ,KAAMA,EAAMgO,WAAYA,GAAYpE,OAAQgI,GAAyBpH,cAAekJ,GAAoBxJ,YAAa8H,IAAgC,KAIlM,SAAC,IAAK,CACFrY,MAAK,UAAKsa,GAAU,YAAI/hB,GAAE,iBAC1B6I,QAASsV,GACTpV,OAAQ,KACR/F,MAAO,IACPuL,gBAAc,EACdzF,SAAU,WAAQsV,IAAiB,EAAO,EAAE,UAC5C,SAAC,IAAI,CAACzd,SAAUsd,GAAc,UAC1B,gBAAKpd,UAAU,MACXC,MAAO,CACH4rB,cAAe,MACfhjB,UAAW,QACXijB,SAAU,OACVC,OAAQ,SACRC,UAAW,QACb,SAELrM,GAAWjc,KAAI,SAAC7C,EAAMM,GAAK,OACxB,iBAEAlB,MAAO,CACHkS,QAAS,OACT8Z,IAAK,OACLpf,aAAc,QAChB,WAGF,gBACI5M,MAAO,CACPkS,QAAS,OACT+Z,eAAgB,QAChB/pB,MAAM,SACJ,UAEF,8BAAStB,EAAKuH,MAAM,eAIxB,gBACInI,MAAO,CACPsS,KAAM,IACNJ,QAAS,OACT+Z,eAAgB,cACd,UAEF,gBACAjsB,MAAO,CACH+K,WAAY,WACZD,UAAW,aACXihB,UAAW,OACXD,OAAQ,GAEZ9gB,wBAAyB,CACrBC,OAAS,WACT,IAAIb,EAAUxJ,EAAKL,MACnB,GACmD,oBAA/CgH,OAAOoH,UAAU5P,SAAS6P,KAAKhO,EAAKL,QACW,mBAA/CgH,OAAOoH,UAAU5P,SAAS6P,KAAKhO,EAAKL,OAEpC,IACA6J,EAAUxE,KAAKC,UAAUjF,EAAKL,MAAO,KAAM,EAC3C,CAAE,MAAOmb,GAAQ,CAErB,OAAOtR,CACP,CAXS,UAcX,qBAhDalJ,GAiDb,WAMlB,SAACma,GAAS,CAACtT,QAASgV,GAAkBtR,IAAKwR,GAAoBrB,mBAAoBoB,MACnF,SAAC,EAAW,CAACrW,OAAO,gCAEN,OAALnI,QAAK,IAALA,GAAAA,EAAO0tB,YAAc,UAAC,IAAM,CAACnsB,UAAU,OAAO4D,QAAS,WACpDwI,OAAOggB,QAAQC,MACnB,EAAE,WAAC,SAACC,EAAA,EAAgB,IAAIntB,GAAE,mBAAkB,MAEhD,0BAAO+hB,QACNpU,cAAmB,OAALrO,QAAK,IAALA,OAAK,EAALA,EAAOqO,cAAe,IAAIpJ,KAAI,SAAC6oB,EAAQC,GACtD,OAAO,kBAAkDxsB,UAAU,gBAAe,UAAC,KAAC,iBAAMA,UAAU,OAAM,SAAEusB,MAAc,wBAAlF,OAAL9tB,QAAK,IAALA,OAAK,EAALA,EAAOkQ,KAAI,YAAI6d,GACtD,IAAG,SAEKtM,IAAU,iBAAKlgB,UAAU,OAAM,WAAC,iBAAMA,UAAU,MAAM4D,QAAS,WAC3DwI,OAAOC,KAAK6T,GAAS,QACzB,EAAE,SAAE/gB,GAAE,+BAAe,SAAC2Z,EAAA,EAAsB,OAAY,QAGhE,SAAC,KAAO,CAAC9Y,UAAU,yCAAwC,UACvD,iBAAKA,UAAU,wBAAuB,YAE5Bqe,GAAa/c,SAAU,SAAC,EAAS,CAACmG,OAAQ8W,GAAcnP,OAAQiP,GAAcrd,SAAU,SAACyG,GACvF/B,aAAaC,QAAQ,UAAD,OAAWyW,EAAS+I,UAAQ,OAAG/I,EAASkN,QAAUzjB,KAAKC,UAAU2B,IACrFiX,GAAgBjX,GAChB6f,IAAU,kBACH7E,IAAe,IAClBC,SAAUhF,GACVtO,OAAQ3H,EACRmb,OAAQ7B,GACR4B,UAAW3C,KAEnB,IAIAkC,IAAe,SAACuK,GAAA,EAAmB,CAAC/gB,IAAK0U,KAAc,KAItC,SAAjBgB,IAA0B,SAACsL,EAAA,EAAQ,CAC/B1jB,kBAAkB,EAClBpD,SAAQ,mBAAcwW,EAAS+I,UAC/Brc,WAAW,SAAC,IAAG,CAAC9I,UAAU,iBAAgB,UACtC,iBAAKA,UAAU,SAAQ,WACnB,gBAAKA,UAAU,MAAK,SAAEqiB,IAAa,KAE/BN,IAAgB,gBAAK/hB,UAAU,MAAK,UAChC,SAAC,IAAM,CAAC2sB,QAAS/K,GAAegL,gBAAiBztB,GAAE,gBAAO0tB,kBAAmB1tB,GAAE,4BAAS2tB,gBAAc,EAAC9rB,SAAU,SAAC2rB,GA5hCjI,IAAC9sB,IA6hCmC8sB,EA5hCzD7K,GAAiBnL,QAAU9W,EAC3BgiB,GAAkBhiB,GA4hCsBynB,IAAU,kBACH7E,IAAe,IAClBC,SAAUhF,GACVtO,OAAQmP,GACRqE,OAAQ7B,GACR4B,UAAW3C,GACXkK,eAAgByC,IAExB,MACK,UAIrB5jB,YAAY,iBAAK/I,UAAU,SAAQ,UAE3BwhB,IAAWA,GAAQlgB,OAASkgB,GAAQ9d,KAAI,SAAAmT,GACpC,OAAO,SAAC,IAAM,CAACtO,KAAK,UAAUvI,UAAU,OAAO4D,QAAS,WACpDwI,OAAOC,KAAKwK,EAAOnL,IAAK,OAC5B,EAAE,SAAEmL,EAAOpO,MACf,IAAK,KAILqX,GAAY7N,SAAS,YAAa,UAAC,IAAM,CAACjS,UAAU,OAAOuI,KAAK,UAAU3E,QAAS,kBAAM+Y,IAAc,EAAK,EAAC,UAAExd,GAAE,gBAAO+hB,IAAW,SAAC5O,EAAA,EAAY,OAAe,KAG/JuO,IAAkBA,GAAevf,QAAS,0BACtC,SAAC,IAAQ,CAAC4lB,SAAS,SAAC,IAAI,UAEhBrG,GAAend,KAAI,SAACwgB,EAAQ/iB,GACxB,OAAO,SAAC,SAAS,WACb,iBAAMnB,UAAU,OAAO4D,QAAS,kBApZ1D,SAACsgB,GACnB1G,GAAgBlc,OAChB6lB,EAAAA,EAAAA,QAAc,CACVvgB,MAAOsd,EAAO8D,aACd1b,MAAM,SAAC8a,EAAA,EAAyB,IAChC/c,QAAS,GACTsD,OAAQxO,GAAE,gBACVyO,WAAYzO,GAAE,gBACd2O,KAAI,WACA,OAAO,IAAImG,SAAQ,SAACE,EAASC,IACzB2Y,EAAAA,EAAAA,IAA0B,GAAD,OAAIzM,GAAW3J,QAAO,wBAAgBuN,EAAOvV,MAAQ,CAC1Eqe,IAAKxP,GAAgB9Z,KAAI,SAAC7C,GAAS,OAAKgF,KAAK6P,MAAM7U,GAAQ,MAAMoa,GAAW,MAE3E9M,MAAK,SAACjH,GACHiN,EAAQ,GACZ,IACC9F,OAAM,SAACgN,GACJjH,GACJ,GACR,IACKjG,MAAK,SAACjH,GACHyB,EAAAA,GAAAA,QAAgBxJ,GAAE,6BAClBmoB,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,GACV7O,OAAQmP,GACRqE,OAAQ7B,GACR4B,UAAW3C,KAEnB,IACC3R,OAAM,WACH1F,EAAAA,GAAAA,MAAcxJ,GAAE,4BACpB,GACR,EACA8I,SAAQ,WAAK,IAGjBU,EAAAA,GAAAA,KAAaxJ,GAAE,4BAEvB,CA6W0F8tB,CAAkB/I,EAAO,EAAC,mBACxD/kB,GAAE,gBAAK,YAAI+kB,EAAOzb,SACnB,wBAH6BtH,GAK5C,MAGA,UACJ,UAAC,IAAM,WAAEhC,GAAE,4BAAQ,KAAC,SAACsT,EAAA,EAAY,WAEhC,KAGT8N,IAAe,gBAAKvgB,UAAU,WAAU,UACpC,SAAC,IAAO,CAAC2T,MAAM,OAAO/M,OAAO,kBAAM5G,UAAU,iBAAgB,WAAC,2BAAMb,GAAE,iDAAc,aAAO,yBAAMA,GAAE,4DAAmB,KAAC,gBAAKa,UAAU,OAAO4D,QAAS,WAClJwI,OAAOC,KAAK,GAAD,OAAID,OAAOgQ,SAAS8Q,QAAM,OAAG5M,GAAW3J,QAAO,qBAC9D,EAAE,SAAGxX,GAAE,6CAA0B0Z,UAAU,UAAS,UAChD,SAAC,KAAM,kBAAK+R,IAAY,cACpB,SAAC,IAAM,CAAC5qB,UAAU,GAAGsM,MAAM,SAAC6gB,EAAA,EAAc,IAAI,SAAEhuB,GAAE,iDAGrD,KAGTshB,IAAiB,UAAC,IAAM,CAACzgB,UAAU,OAAO4D,QAAS,WAC/CujB,EAAAA,EAAAA,QAAc,CACVvgB,MAAOzH,GAAE,4BACTmN,MAAM,SAAC8a,EAAA,EAAyB,IAChC/c,QAAS,GACTsD,OAAQxO,GAAE,wCACVyO,WAAYzO,GAAE,gBACd2O,KAAI,WACA,IAAMkC,EAAa4Z,GAAmBrL,GAAcyB,IAC9CmK,EAAYtkB,KAAKC,UAAUkK,GACjC5D,OAAOC,KAAK,GAAD,OAAID,OAAOgQ,SAAS8Q,QAAM,OAAG5M,GAAW3J,QAAO,8BAAsBwT,IAChFxhB,EAAAA,GAAAA,QAAgBxJ,GAAE,4BACtB,EACA8I,SAAQ,WAAK,GAErB,EAAE,UAAE9I,GAAE,4BAAQ,MAAE,SAACiuB,EAAA,EAAc,OAAe,QAItD7lB,OAAQ,SAAC2e,GACL,OAAOrgB,KAAKC,UAAUogB,EAC1B,EACAnhB,QAASoZ,GACTpe,QAASA,EACT4J,WAAYsU,GACZ1X,WAAY4E,EACZnK,SAAU,SAACid,EAAe0F,EAASf,GAC/B,IAAMyK,EAAYzK,EAAO0K,MAAQ,CAC7BC,aAAc3K,EAAO4K,UACrBC,gBAAkC,WAAjB7K,EAAO0K,MAAqB,MAAQ,aACrC1d,EAEpB0X,IAAU,kBACH7E,IAAe,IAClBC,SAAUzE,EACV7O,OAAQmP,GACRoE,UAAW3C,GACX4C,OAAQyK,IAEhB,EACAzjB,aAAc,CACVrB,KAAM,WACN0e,MAAO,OACPyG,YAAa,GACblQ,gBAAAA,GACAxc,SAAU,SAACwc,GACPC,GAAmBD,EACvB,GAEJlX,OAAQ,CAAEL,EAAGX,GAAYqoB,EAAGpL,OAC3B,iBAAKviB,UAAU,WAAU,WAC1B,gBAAKA,UAAU,SAAQ,SAEfmL,EAASzH,KAAI,SAACsD,EAAK4mB,GACf,OAAO,gBAAK3tB,MAAO,CAAE4tB,UAAW,OAAQ1rB,MAAmB,OAAZmf,SAAY,IAAZA,QAAY,EAAZA,GAAcwM,WAAYvvB,OAAoB,OAAZ+iB,SAAY,IAAZA,QAAY,EAAZA,GAAcyM,aAAuC/tB,UAAU,qBAAoB,SAE5JwH,OAAO8Q,KAAKtR,GAAKtD,KAAI,SAAC2H,EAAK2iB,GACvB,OAAItM,GAAYzP,SAAS5G,IACd,gBAAKpL,MAAO,CAAE8K,UAAW,aAAkDE,wBAAyB,CAAEC,OAAQlE,EAAIqE,KAAO,aAAtEuiB,GAAQ,OAAGI,IAElE,IACX,KAAE,cAPgHJ,GAU9H,OAGR,gBAAK5tB,UAAU,OAAM,UACjB,SAAC,KAAU,kBAAKie,IAAQ,IAAEjd,SAAU,SAACqpB,EAAM1M,GACvC2J,IAAU,kBACH7E,IAAe,IAClBC,UAAS,kBACFzE,IAAQ,IACXtH,QAAS0T,EACT1M,SAAAA,IAEJvO,OAAQmP,GACRoE,UAAW3C,KAEnB,iBAQhC,C,+lBC3xCe,SAASyM,oBAAoBhuB,OACxC,eAA4BC,EAAAA,mCAAAA,UAAmB,CAAC,GAAE,8IAA3CU,OAAM,cAAEQ,UAAS,cACxB,YAA8BlB,EAAAA,mCAAAA,WAAS,GAAK,+IAArCqB,QAAO,cAAEwb,WAAU,cAe1B,OAbAjc,EAAAA,mCAAAA,YAAU,WACFb,MAAMiN,MACNuiB,EAAAA,8CAAAA,IAA8B,GAAD,OAAIxvB,MAAMiN,IAAG,UAAU,CAAC,GAAGyC,MAAK,SAAAjH,KACzD,IAAM9H,OAAS8H,IAAIrH,KAAK6U,OACpBlJ,WAAkB,CAAC,EACvBC,KAAK,cAAD,OAAerM,SACnBQ,UAAU4L,WACd,IAAG6C,OAAM,SAAAgN,GAAS,IAAGO,SAAQ,WACzBL,YAAW,EACf,GAER,GAAG,CAAC9c,MAAMiN,OAGN,sDAAC,iEAAU,CAACtM,OAAQA,OAAQW,QAASA,SAE7C,C", "sources": ["components/EchartCore/EchartCore.tsx", "components/InputSearch/InputSearch.tsx", "components/TableBox/TableBox.tsx", "components/TabsModal/TabsDetail.tsx", "components/TitleHeader/TitleHeader.tsx", "components/ModalForm/ModalForm.tsx", "components/MixSearch/MixSearch.tsx", "components/FileUploadPlus/FileUploadPlus.tsx", "components/JsonEditor/JsonEditor.tsx", "components/DynamicForm/DynamicForm.tsx", "components/TabsModal/TabsModal.tsx", "pages/ADUGTemplate.tsx", "pages/ChartOptionTempalte.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\nimport * as echarts from 'echarts';\n// import * as echarts from 'echarts/core';\n// import {\n//     BarChart,\n//     // 系列类型的定义后缀都为 SeriesOption\n//     BarSeriesOption,\n//     PieChart,\n//     PieSeriesOption,\n//     LineChart,\n//     LineSeriesOption,\n//     HeatmapChart,\n//     HeatmapSeriesOption\n// } from 'echarts/charts';\n// import {\n//     TitleComponent,\n//     // 组件类型的定义后缀都为 ComponentOption\n//     TitleComponentOption,\n//     TooltipComponent,\n//     TooltipComponentOption,\n//     GridComponent,\n//     GridComponentOption,\n//     // 数据集组件\n//     DatasetComponent,\n//     DatasetComponentOption,\n//     LegendComponent,\n//     // 内置数据转换器组件 (filter, sort)\n//     TransformComponent,\n//     CalendarComponentOption,\n//     CalendarComponent,\n//     VisualMapComponent,\n//     VisualMapComponentOption,\n//     ToolboxComponent\n// } from 'echarts/components';\nimport { LabelLayout, UniversalTransition } from 'echarts/features';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport './EchartCore.less';\nimport { Spin } from 'antd';\nimport { FieldNumberOutlined } from '@ant-design/icons';\nimport { useTranslation } from 'react-i18next';\n\nexport type ECOption = echarts.EChartsOption\n// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型\n// export type ECOption = echarts.ComposeOption<\n//     | BarSeriesOption\n//     | LineSeriesOption\n//     | TitleComponentOption\n//     | TooltipComponentOption\n//     | GridComponentOption\n//     | DatasetComponentOption\n//     | CalendarComponentOption\n//     | HeatmapSeriesOption\n//     | VisualMapComponentOption\n//     | PieSeriesOption\n// >;\n\n// // 注册必须的组件\n// echarts.use([\n//     LegendComponent,\n//     TitleComponent,\n//     TooltipComponent,\n//     GridComponent,\n//     DatasetComponent,\n//     TransformComponent,\n//     CalendarComponent,\n//     VisualMapComponent,\n//     ToolboxComponent,\n//     BarChart,\n//     LineChart,\n//     PieChart,\n//     LabelLayout,\n//     HeatmapChart,\n//     UniversalTransition,\n//     CanvasRenderer\n// ]);\n\ninterface IProps {\n    // option: ECOption\n    option: echarts.EChartsOption\n    loading?: boolean\n    title?: string\n    style?: React.CSSProperties\n    unit?: string\n    data?: {\n        xData: any[]\n        yData: any[]\n    }\n    isNoData?: boolean\n}\n\nconst defaultChartStyle: React.CSSProperties = {\n    height: 300\n}\n\n// https://echarts.apache.org/handbook/zh/how-to/data/dynamic-data\nexport default function EchartCore(props: IProps) {\n    const [chartInstance, setChartInstance] = useState<echarts.ECharts>()\n    const id = Math.random().toString(36).substring(2);\n    const { t, i18n } = useTranslation();\n\n    const option = {}\n\n    useEffect(() => {\n        const chartDom = document.getElementById(id)\n        if (chartDom) {\n            const chart = echarts.init(chartDom);\n            chart.setOption({ ...option, ...props.option })\n\n            if (!chartInstance) {\n                setChartInstance(chart)\n            }\n        }\n    }, [props.option, props.data])\n\n    return (\n        <Spin spinning={props.loading}>\n            <div className=\"chart-container\">\n                <div id={id} style={{ ...defaultChartStyle, ...props.style }}></div>\n                {\n                    props.isNoData ? <div className=\"chart-nodata\">\n                        <div>{t('暂无数据')}</div>\n                    </div> : null\n                }\n            </div>\n        </Spin>\n    )\n}\n", "import { SearchOutlined } from '@ant-design/icons';\nimport { Input } from 'antd';\nimport React, { useState, ChangeEvent, useEffect } from 'react';\nimport './InputSearch.less';\n\ninterface IProps {\n    labelName?: string,\n    width?: string,\n    placeholder?: string,\n    maxLength?: number,\n    maxHeight?: number,\n    // 是否开启前端搜索匹配\n    isOpenSearchMatch?: boolean,\n    loading?: boolean | JSX.Element,\n    // 配置提示列表\n    options?: string[],\n    // 当配置value时，即为可控组件\n    value?: string,\n    disabled?: boolean\n    // 按回车时回调\n    onSearch?: (value: string) => void,\n    // 输入字符、按下回车时回调\n    onChange?: (value: string) => void,\n    // 点击option中的item\n    onClick?: (value: string) => void,\n    // 滚动条到底时触发\n    onScrollButtom?: () => void\n}\n\nexport default function InputSearch(props: IProps): JSX.Element {\n    const id = Math.random().toString(36).substring(2);\n    let inputRef: any;\n\n    const [dataCache, setDataCache] = useState<string[]>(props.options || []);\n    const [value, setValue] = useState(props.value || '');\n\n    useEffect(() => {\n        const dataFilter = props.isOpenSearchMatch ? (props.options || []).filter(item => {\n            return item.indexOf(value) !== -1;\n        }) : (props.options || []);\n        setDataCache(dataFilter);\n    }, [props.options]);\n\n    useEffect(() => {\n        setValue(props.value || '');\n        // props.onChange && props.onChange(props.value);\n    }, [props.value]);\n\n    const handleChange = (value: string): void => {\n        setValue(value);\n        props.onChange && props.onChange(value);\n    };\n\n    const handleClick = (value: string): void => {\n        handleChange(value);\n        props.onClick && props.onClick(value);\n    };\n\n    const handleEnterKey = (e: React.KeyboardEvent<HTMLInputElement>): void => {\n        // 回车\n        if (e.nativeEvent.keyCode === 13) {\n            inputRef.blur && inputRef.blur();\n            props.onSearch && props.onSearch(e.currentTarget.value);\n        }\n    };\n\n    const highlightKeyWord = (item: string): JSX.Element => {\n        const keyWord = value;\n        const index = item.indexOf(value);\n        if (index === -1) {\n            return <span>{item}</span>;\n        }\n        const preStr = item.substring(0, index);\n        const nextStr = item.substring(index + value.length);\n        return <span>{preStr}<span className=\"highlight\">{keyWord}</span>{nextStr}</span>;\n    };\n\n    const debounce = (fun: any, time = 500): any => {\n        let timer: ReturnType<typeof setTimeout>;\n        return function (...args: any): void {\n            clearTimeout(timer);\n            timer = setTimeout(() => {\n                fun && fun.apply(null, [...args]);\n            }, time);\n        };\n    };\n\n    const debounceScroll = debounce(props.onScrollButtom);\n\n    const handleScroll = (e: React.UIEvent<HTMLElement>): void => {\n        e.stopPropagation();\n        // console.log({\n        //     event: e,\n        //     target: e.target, // Note 1* scrollTop is undefined on e.target\n        //     currentTarget: e.currentTarget,\n        //     scrollTop: e.currentTarget.scrollTop,\n        //     scrollHeight: e.currentTarget.scrollHeight,\n        //     clientHeight: e.currentTarget.clientHeight\n        // });\n        const { currentTarget } = e;\n        const { scrollTop, clientHeight, scrollHeight } = currentTarget;\n        const difference = scrollHeight - clientHeight - scrollTop;\n        if (difference < 20) {\n            props.onScrollButtom && debounceScroll();\n        }\n    };\n\n    return (\n        <div className=\"select-down-modern\">\n            {\n                props.labelName ? <label htmlFor={id} className=\"pb4 mb0 fs12 d-b\">{props.labelName}</label> : null\n            }\n            <div className=\"p-r d-f ac\" style={{ width: props.width || 100 + '%' }}>\n                <Input\n                    style={{ width: '100%' }}\n                    disabled={props.disabled}\n                    id={id}\n                    placeholder={props.placeholder || ''}\n                    maxLength={props.maxLength || 200}\n                    onChange={(e: ChangeEvent<HTMLInputElement>): void => handleChange(e.target.value)}\n                    onKeyPress={handleEnterKey}\n                    value={value}\n                    ref={element => inputRef = element}\n                />\n                <SearchOutlined className=\"p-a r0 mr8\" />\n            </div>\n\n            {\n                // 输入提示\n                dataCache.length ? <ul className=\"select-option shadow\" onScroll={handleScroll} style={{ 'maxHeight': `${props.maxHeight}px` }}>\n                    {\n                        props.loading ? <div className=\"p-s z9 ta-r\" style={{ right: `${0}px`, top: `${0}px` }}>\n                            <div className=\"d-il p-a\" style={{ right: `${8}px`, top: `${0}px` }}></div>\n                        </div> : null\n                    }\n                    {\n                        dataCache.map((item, index) => {\n                            return <li className=\"ellip1\" onMouseDown={(): void => handleClick(item)} key={index}>{highlightKeyWord(item)}</li>;\n                        })\n                    }\n                </ul> : null\n            }\n        </div>\n    );\n}", "import React, { ReactNode, useEffect, useState } from 'react';\nimport { Row, Col, Space, Table, ConfigProvider, Button, Modal, Tabs, message, Checkbox } from 'antd';\nimport './TableBox.less';\nimport { TablePaginationConfig } from 'antd/lib/table/Table';\nimport emptyImg from '../../images/emptyBg.png';\nimport { GetRowKey, SorterResult, TableRowSelection } from 'antd/lib/table/interface';\n// import ExportJsonExcel from 'js-export-excel';\nimport { Resizable } from 'react-resizable';\nimport { useTranslation } from 'react-i18next';\n\nconst CopyToClipboard = require('react-copy-to-clipboard');\n\ninterface IProps {\n\tsize?: 'large' | 'middle' | 'small'\n\ttableKey?: string\n\trowKey?: string | GetRowKey<any>;\n\ttitleNode?: string | ReactNode;\n\tbuttonNode?: ReactNode;\n\tdataSource: any;\n\tcolumns: any;\n\tpagination?: false | TablePaginationConfig;\n\tscroll?:\n\t| ({\n\t\tx?: string | number | true | undefined;\n\t\ty?: string | number | undefined;\n\t} & {\n\t\tscrollToFirstRowOnChange?: boolean | undefined;\n\t})\n\t| undefined;\n\tloading?: boolean;\n\trowSelection?: TableRowSelection<any>;\n\tcancelExportData?: boolean;\n\tonChange?: (\n\t\tpagination: TablePaginationConfig,\n\t\tfilters: Record<string, (string | number | boolean)[] | null>,\n\t\tsorter: SorterResult<any> | SorterResult<any>[],\n\t) => void;\n}\n\nconst ResizableTitle = ({ onResize, width, ...restProps }: any) => {\n\tif (!width) {\n\t\treturn <th {...restProps} />;\n\t}\n\n\treturn (\n\t\t<Resizable\n\t\t\twidth={width}\n\t\t\theight={0}\n\t\t\thandle={\n\t\t\t\t<span\n\t\t\t\t\tclassName=\"react-resizable-handle\"\n\t\t\t\t\tonClick={(e) => {\n\t\t\t\t\t\te.stopPropagation();\n\t\t\t\t\t}}\n\t\t\t\t/>\n\t\t\t}\n\t\t\tonResize={onResize}\n\t\t\tdraggableOpts={{ enableUserSelectHack: false }}\n\t\t>\n\t\t\t<th {...restProps} style={{ ...restProps?.style, userSelect: 'none' }} />\n\t\t</Resizable>\n\t);\n};\n\nconst TableBox = (props: IProps) => {\n\tconst [exportDataVisible, setExportDataVisible] = useState(false);\n\tconst [dataFormat, setDataFormat] = useState<{ header: any[]; data: any[] }>({\n\t\theader: [],\n\t\tdata: [],\n\t});\n\tconst [filterValue, setFilterValue] = useState<any[]>([]);\n\n\t// 可伸缩列设置\n\tconst [cols, setCols] = useState(props.columns);\n\tconst handleResize = (index: any) => {\n\t\treturn (_: any, { size }: any) => {\n\t\t\tif (size.width < 100) return\n\t\t\tconst temp = [...cols];\n\t\t\ttemp[index] = { ...temp[index], width: size.width };\n\t\t\tconst tableWidth = temp.reduce((pre: any, next: any) => pre + next.width || 100, 0) + 200\n\t\t\tlocalStorage.setItem(props.tableKey || '', JSON.stringify(temp))\n\t\t\t// console.log(currentTableScroll, temp);\n\t\t\tsetCurrentTableScroll({ ...currentTableScroll, x: tableWidth })\n\t\t\tsetCols(temp);\n\t\t};\n\t};\n\tconst customColumns = cols.map((col: any, index: any) => {\n\t\treturn {\n\t\t\t...col,\n\t\t\twidth: col.width || 200,\n\t\t\tonHeaderCell: (column: any) => {\n\t\t\t\treturn {\n\t\t\t\t\twidth: column.width,\n\t\t\t\t\tonResize: handleResize(index),\n\t\t\t\t};\n\t\t\t},\n\t\t};\n\t});\n\tconst [currentTableScroll, setCurrentTableScroll] = useState(props.scroll)\n\tconst { t, i18n } = useTranslation();\n\n\tuseEffect(() => {\n\t\tsetCols(props.columns);\n\t}, [props.columns]);\n\n\tuseEffect(() => {\n\t\tsetCurrentTableScroll(props.scroll);\n\t}, [props.scroll]);\n\n\tuseEffect(() => {\n\t\tif (props.dataSource) {\n\t\t\tconst columns = props.columns.filter((item: any) => ~filterValue.indexOf(item.dataIndex));\n\t\t\thanddleFilterHeader(columns, props.dataSource);\n\t\t}\n\t}, [props.dataSource, props.columns]);\n\n\tconst customizeRenderEmpty = () => (\n\t\t<Row justify=\"center\" align=\"middle\" style={{ height: 360, flexDirection: 'column' }}>\n\t\t\t<img src={emptyImg} style={{ width: 266 }} alt=\"\" />\n\t\t\t<div>{t('暂无数据')}</div>\n\t\t</Row>\n\t);\n\n\tconst handdleFilterHeader = (dataColumns = [], data: any[]) => {\n\t\tconst columns = dataColumns.map((item: any) => item.dataIndex).filter((item: string) => item !== 'handle');\n\t\tconst sheetHeader = dataColumns.map((item: any) => item.title).filter((item: string) => item !== t('操作'));\n\t\tconst tarData: any = [];\n\n\t\tdata.forEach((dataRow: any) => {\n\t\t\tconst row: any = {};\n\t\t\tcolumns.map((colName: string) => {\n\t\t\t\tconst res = dataRow[colName];\n\t\t\t\trow[colName] = res || '';\n\t\t\t});\n\t\t\ttarData.push(row);\n\t\t});\n\n\t\tsetDataFormat({\n\t\t\theader: sheetHeader,\n\t\t\tdata: tarData,\n\t\t});\n\t};\n\n\t// const handleClickOutputExcel = () => {\n\t// \tconst option: any = {};\n\t// \toption.fileName = 'result';\n\t// \toption.datas = [\n\t// \t\t{\n\t// \t\t\tsheetData: dataFormat.data,\n\t// \t\t\tsheetName: 'sheet',\n\t// \t\t\tsheetHeader: dataFormat.header,\n\t// \t\t},\n\t// \t];\n\t// \tconst toExcel = new ExportJsonExcel(option);\n\t// \ttoExcel.saveExcel();\n\t// };\n\n\tconst handleExportJira = () => {\n\t\tconst header = dataFormat.header;\n\t\tconst data = dataFormat.data;\n\t\tlet str = '';\n\t\tif (header.length && data.length) {\n\t\t\tstr =\n\t\t\t\t'|' +\n\t\t\t\theader.join('|') +\n\t\t\t\t'|' +\n\t\t\t\t`\n`;\n\t\t\tdata.forEach((row: any) => {\n\t\t\t\tconst rowKey = Object.values(row).map((item) => {\n\t\t\t\t\tif (item === '') {\n\t\t\t\t\t\treturn ' ';\n\t\t\t\t\t}\n\t\t\t\t\treturn item;\n\t\t\t\t});\n\t\t\t\tstr =\n\t\t\t\t\tstr +\n\t\t\t\t\t'|' +\n\t\t\t\t\trowKey.join('|') +\n\t\t\t\t\t'|' +\n\t\t\t\t\t`\n`;\n\t\t\t});\n\t\t} else {\n\t\t\tstr = '';\n\t\t}\n\n\t\treturn str;\n\t};\n\n\tconst handleExportText = () => {\n\t\tconst header = dataFormat.header;\n\t\tconst data = dataFormat.data;\n\t\tlet str = '';\n\t\tif (header.length && data.length) {\n\t\t\tstr =\n\t\t\t\theader.join('\t') +\n\t\t\t\t`\n`;\n\t\t\tdata.forEach((row: any) => {\n\t\t\t\tconst rowKey = Object.values(row).map((item) => {\n\t\t\t\t\tif (item === '') {\n\t\t\t\t\t\treturn ' ';\n\t\t\t\t\t}\n\t\t\t\t\treturn item;\n\t\t\t\t});\n\t\t\t\tstr =\n\t\t\t\t\tstr +\n\t\t\t\t\trowKey.join('\t') +\n\t\t\t\t\t`\n`;\n\t\t\t});\n\t\t} else {\n\t\t\tstr = '';\n\t\t}\n\t\treturn str;\n\t};\n\n\treturn (\n\t\t<Space className=\"tablebox\" direction=\"vertical\" size=\"middle\">\n\t\t\t<Modal\n\t\t\t\twidth={1000}\n\t\t\t\tmaskClosable={false}\n\t\t\t\tcentered={true}\n\t\t\t\tbodyStyle={{ maxHeight: 500, overflow: 'auto' }}\n\t\t\t\tvisible={exportDataVisible}\n\t\t\t\ttitle={t('导出数据')}\n\t\t\t\tonCancel={() => {\n\t\t\t\t\tsetExportDataVisible(false);\n\t\t\t\t}}\n\t\t\t\tfooter={null}\n\t\t\t>\n\t\t\t\t<div style={{ position: 'relative' }}>\n\t\t\t\t\t<div className=\"mb16\"><span className=\"pr8\">{t('选择需要导出的列')}：</span><Checkbox.Group\n\t\t\t\t\t\toptions={props.columns\n\t\t\t\t\t\t\t.map((item: any) => ({ label: item.title, value: item.dataIndex }))\n\t\t\t\t\t\t\t.filter((item: any) => item.value !== 'handle')}\n\t\t\t\t\t\tdefaultValue={[]}\n\t\t\t\t\t\tvalue={filterValue}\n\t\t\t\t\t\tonChange={(values: any) => {\n\t\t\t\t\t\t\tsetFilterValue(values);\n\t\t\t\t\t\t\tconst columns = props.columns.filter((item: any) => ~values.indexOf(item.dataIndex));\n\t\t\t\t\t\t\thanddleFilterHeader(columns, props.dataSource);\n\t\t\t\t\t\t}}\n\t\t\t\t\t/></div>\n\t\t\t\t\t<div style={{ position: 'absolute', right: 0, bottom: 0 }}>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\ttype=\"link\"\n\t\t\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\t\t\tsetFilterValue(\n\t\t\t\t\t\t\t\t\tprops.columns\n\t\t\t\t\t\t\t\t\t\t.map((item: any) => item.dataIndex)\n\t\t\t\t\t\t\t\t\t\t.filter((item: any) => item !== 'handle'),\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\thanddleFilterHeader(props.columns, props.dataSource);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{t('全选')}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\ttype=\"link\"\n\t\t\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\t\t\tsetFilterValue([]);\n\t\t\t\t\t\t\t\thanddleFilterHeader([], props.dataSource);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{t('反选')}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Tabs>\n\t\t\t\t\t<Tabs.TabPane tab=\"Wiki格式\" key=\"jira\">\n\t\t\t\t\t\t<CopyToClipboard text={handleExportJira()} onCopy={() => message.success(t('已复制到粘贴板'))}>\n\t\t\t\t\t\t\t<pre style={{ cursor: 'pointer', minHeight: 100 }}>\n\t\t\t\t\t\t\t\t<code>{handleExportJira()}</code>\n\t\t\t\t\t\t\t</pre>\n\t\t\t\t\t\t</CopyToClipboard>\n\t\t\t\t\t</Tabs.TabPane>\n\t\t\t\t\t<Tabs.TabPane tab=\"Text格式\" key=\"test\">\n\t\t\t\t\t\t<CopyToClipboard text={handleExportText()} onCopy={() => message.success(t('已复制到粘贴板'))}>\n\t\t\t\t\t\t\t<pre style={{ cursor: 'pointer', minHeight: 100 }}>\n\t\t\t\t\t\t\t\t<code>{handleExportText()}</code>\n\t\t\t\t\t\t\t</pre>\n\t\t\t\t\t\t</CopyToClipboard>\n\t\t\t\t\t</Tabs.TabPane>\n\t\t\t\t\t{/* <Tabs.TabPane tab=\"Excel格式\" key=\"excel\">\n\t\t\t\t\t\t<Row justify=\"center\" align=\"middle\" style={{ minHeight: 100 }}>\n\t\t\t\t\t\t\t<Col>\n\t\t\t\t\t\t\t\t<Button type=\"primary\" onClick={handleClickOutputExcel}>\n\t\t\t\t\t\t\t\t\t导出Excel\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t</Row>\n\t\t\t\t\t</Tabs.TabPane> */}\n\t\t\t\t</Tabs>\n\t\t\t</Modal>\n\t\t\t{\n\t\t\t\tprops.titleNode || props.buttonNode || !props.cancelExportData ? <Row justify=\"space-between\" align=\"middle\">\n\t\t\t\t\t<Col>\n\t\t\t\t\t\t<Space align=\"center\">{props.titleNode}</Space>\n\t\t\t\t\t</Col>\n\t\t\t\t\t<Col>\n\t\t\t\t\t\t<Space align=\"center\">\n\t\t\t\t\t\t\t{props.buttonNode}\n\t\t\t\t\t\t\t{props.cancelExportData ? null : (\n\t\t\t\t\t\t\t\t<Button style={{ marginLeft: 6 }} onClick={() => setExportDataVisible(true)}>\n\t\t\t\t\t\t\t\t\t{t('导出数据')}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t</Space>\n\t\t\t\t\t</Col>\n\t\t\t\t</Row> : null\n\t\t\t}\n\t\t\t<ConfigProvider renderEmpty={customizeRenderEmpty}>\n\t\t\t\t<Table\n\t\t\t\t\tsize={props.size || 'middle'}\n\t\t\t\t\trowKey={props.rowKey ? props.rowKey : 'id'}\n\t\t\t\t\tdataSource={props.dataSource}\n\t\t\t\t\t// columns={props.columns}\n\t\t\t\t\tcomponents={{ header: { cell: ResizableTitle } }}\n\t\t\t\t\tcolumns={customColumns}\n\t\t\t\t\tpagination={props.pagination !== false ? { ...props.pagination } : false}\n\t\t\t\t\tscroll={currentTableScroll}\n\t\t\t\t\tloading={props.loading}\n\t\t\t\t\tonChange={props.onChange}\n\t\t\t\t\trowSelection={props.rowSelection}\n\t\t\t\t/>\n\t\t\t</ConfigProvider>\n\t\t</Space>\n\t);\n};\n\nexport default TableBox;\n", "\nimport { <PERSON>ton, Col, Row, Tabs } from 'antd'\n\nimport React, { useEffect, useState } from 'react'\n\nimport { ITabDetailItem, TGroupContentType } from '../../api/interface/tabsModalInterface';\n\nimport EchartCore from '../EchartCore/EchartCore';\n\nimport './TabsDetail.less';\n\nimport {marked} from 'marked'\n\n\n\n\ninterface IProps {\n\n    data: ITabDetailItem[]\n\n}\n\n\nexport default function TabsDetail(props: IProps) {\n\n    const [markdownMap, setMarkdownMap] = useState<Record<string, Record<string, string>>>({});\n\n\n\n\n    useEffect(() => {\n\n        const renderMarkdown = async (tab: string, group: string, value: string) => {\n\n            const html = await marked(value);\n\n            setMarkdownMap((prev) => ({\n\n                ...prev,\n\n                [tab]: {\n\n                    ...(prev[tab] || {}),\n\n                    [group]: html,\n\n                },\n\n            }));\n\n        };\n\n        props.data.forEach((tab) => {\n\n            tab.content.forEach((group) => {\n\n                if (group.groupContent.type === 'markdown') {\n\n                    renderMarkdown(tab.tabName, group.groupName, group.groupContent.value);\n\n                }\n\n            });\n\n        });\n\n    }, [props.data]);\n\n\n\n\n\n\n\n    const handleGroupContent = (type: TGroupContentType, content: any, tabName: string, groupName: string) => {\n\n        switch (type) {\n\n            case 'map':\n\n                return renderMapComponent(content)\n\n            case 'echart':\n\n                return renderEchart(content)\n\n            case 'text':\n\n                return renderMapText(content)\n\n            case 'iframe':\n\n                return renderMapIframe(content)\n\n            case 'html':\n\n                return renderHtml(content)\n\n            case 'markdown':\n\n                return renderMarkdown(tabName, groupName)\n\n            default:\n\n                return <span style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>{content}</span>\n\n        }\n\n    }\n\n\n\n\n    const renderHtml = (data: string) => {\n\n        return <div dangerouslySetInnerHTML={{ __html: data }}></div>\n\n    }\n\n    const renderMapComponent = (data: Record<string, string>) => {\n\n        const dataList: Array<{ label: string, value: string }> = Object.entries(data).reduce((pre: any, [key, val]) => ([...pre, { label: key, value: val }]), [])\n\n        return <div className=\"bg-title p16\">\n\n            {\n\n                dataList.map((item, index) => {\n\n                    return <Row className=\"mb8 w100\" key={`tabsDetailItem_${index}`}>\n\n                        <Col span={8}><div className=\"ta-l\"><strong>{item.label}：</strong></div></Col>\n\n                        <Col span={16}><span style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>{item.value}</span></Col>\n\n                    </Row>\n\n                })\n\n            }\n\n        </div>\n\n    }\n\n    const renderEchart = (data: any) => {\n\n        var currentOps: any = {}\n\n        eval(`currentOps=${data}`)\n\n        return <div className=\"bg-title p16\">\n\n            <EchartCore option={currentOps} loading={false} />\n\n        </div>\n\n    }\n\n    const renderMapText = (data: string) => {\n\n        return <div className=\"p16 bg-title\" style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>{data}</div>\n\n    }\n\n    const renderMapIframe = (data: any) => {\n\n        return <iframe\n\n            src={data.url}\n\n            allowFullScreen\n\n            allow=\"microphone;camera;midi;encrypted-media;\"\n\n            className=\"w100 fade-in\"\n\n            style={{ border: 0, height: 500 }}>\n\n        </iframe>\n\n    }\n\n    const renderMarkdown = (tabName: string, groupName: string) => {\n\n        const markdownHtml = markdownMap[tabName]?.[groupName];\n\n        return markdownHtml ? <div dangerouslySetInnerHTML={{ __html: markdownHtml }} /> : null;\n\n    };\n\n\n\n\n    return (\n\n        <>\n\n            <Tabs className=\"tabsdetail-tab\">\n\n                {\n\n                    props.data.map((tab, tabIndex) => {\n\n                        return <Tabs.TabPane tab={tab.tabName} key={`tabsDetailTab${tabIndex}`}>\n\n                            <div className=\"d-f fd-c jc-b h100\">\n\n                                <div className=\"flex1\">\n\n                                    {\n\n                                        tab.content.map((group, groupIndex) => {\n\n                                            return <div className=\"mb32\" key={`tabsGroup${groupIndex}`}>\n\n                                                <div className=\"fs16 mb16 bor-l b-theme pl4\" style={{ borderLeftWidth: 2 }} dangerouslySetInnerHTML={{ __html: group.groupName }}></div>\n\n                                                <div>\n\n                                                    {handleGroupContent(group.groupContent.type, group.groupContent.value, tab.tabName, group.groupName)}\n\n                                                </div>\n\n                                            </div>\n\n                                        })\n\n                                    }\n\n                                </div>\n\n                                <div className=\"tabsdetail-tool\">\n\n                                    {\n\n                                        tab.bottomButton?.map(button => {\n\n                                            return <Button className=\"mr12 icon-tool-wrapper\" onClick={() => {\n\n                                                window.open(button.url, 'blank')\n\n                                            }}>\n\n                                                <span className=\"icon-tool\" dangerouslySetInnerHTML={{ __html: button.icon }}></span>\n\n                                                <span className=\"ml6\">{button.text}</span>\n\n                                            </Button>\n\n                                        })\n\n                                    }\n\n                                </div>\n\n                            </div>\n\n                        </Tabs.TabPane>\n\n                    })\n\n                }\n\n            </Tabs>\n\n        </>\n\n    )\n\n}", "import React, { ReactNode } from 'react';\nimport { Row, Typography, Col, Space } from 'antd';\nimport './TitleHeader.less';\n\nconst { Title } = Typography;\n\ninterface IProps {\n    title: ReactNode | string;\n    children?: ReactNode;\n    noBorderBottom?: boolean;\n    breadcrumbs?: ReactNode | string;\n}\n\nconst TitleHeader = (props: IProps) => {\n    const styles: React.CSSProperties = { position: 'sticky', top: 0 }\n    return (\n        <Row\n            className=\"title-header\"\n            justify=\"space-between\"\n            align=\"middle\"\n            style={props.noBorderBottom ? { borderBottom: 'none', ...styles } : styles}>\n            <div>\n                <Title className=\"d-il mr12\" level={5} style={{ marginBottom: 10 }}>\n                    {props.title}\n                </Title>\n                <div className=\"d-il\">\n                    {props.breadcrumbs}\n                </div>\n            </div>\n\n            <Col>\n                <Space>{props.children ? props.children : null}</Space>\n            </Col>\n        </Row>\n    );\n};\n\nexport default TitleHeader;\n", "import React, { ReactNode, useEffect, useMemo, useState } from 'react';\nimport { Modal, Form, Spin, Input, FormInstance } from 'antd';\nimport { useTranslation } from 'react-i18next';\n\ninterface ModalFormProps {\n\tvisible: boolean;\n\tonCreate: (values: any, form: FormInstance<any>) => void;\n\tonCancel: () => void;\n\tloading?: boolean;\n\tchildren?: any;\n\ttitle?: string;\n\tformData?: Record<string, any>;\n\twidth?: number;\n\tonValuesChange?: () => {}\n}\n\nconst ModalForm = (props: ModalFormProps): JSX.Element => {\n\tconst { t, i18n } = useTranslation();\n\tconst [form] = Form.useForm();\n\tconst [, updateState] = useState<any>();\n\tconst forceUpdate = React.useCallback(() => updateState({}), []);\n\n\tuseEffect(() => {\n\t\tif (props.formData) {\n\t\t\tform.setFieldsValue(props.formData);\n\t\t}\n\t}, [props]);\n\n\tconst [formChangeRes, setFormChangeRes] = useState<{\n\t\tcurrentChange: Record<string, any>\n\t\tallValues: Record<string, any>\n\t}>({\n\t\tcurrentChange: {},\n\t\tallValues: {}\n\t})\n\n\t// const propsChildrenMemo = useMemo(() => props.children(form), [])\n\n\treturn (\n\t\t<Modal\n\t\t\t// confirmLoading={props.loading}\n\t\t\tdestroyOnClose={true}\n\t\t\tmaskClosable={false}\n\t\t\twidth={props.width || 680}\n\t\t\tvisible={props.visible}\n\t\t\ttitle={props.title}\n\t\t\tokText={t('确定')}\n\t\t\tcancelText={t('取消')}\n\t\t\tonCancel={() => {\n\t\t\t\tform.resetFields();\n\t\t\t\tprops.onCancel();\n\t\t\t}}\n\t\t\tonOk={() => {\n\t\t\t\tconsole.log(form.getFieldsValue())\n\t\t\t\tform.validateFields()\n\t\t\t\t\t.then((values) => {\n\t\t\t\t\t\tprops.onCreate(values, form);\n\t\t\t\t\t\t// form.resetFields();\n\t\t\t\t\t})\n\t\t\t\t\t.catch((info) => {\n\t\t\t\t\t\t// console.log('Validate Failed:', info);\n\t\t\t\t\t});\n\t\t\t}}\n\t\t>\n\t\t\t<Spin spinning={props.loading}>\n\t\t\t\t<Form onValuesChange={(value, allValues) => {\n\t\t\t\t\tsetFormChangeRes({\n\t\t\t\t\t\tcurrentChange: value,\n\t\t\t\t\t\tallValues\n\t\t\t\t\t})\n\t\t\t\t}} labelCol={{ span: 5 }} wrapperCol={{ span: 19 }} form={form} layout=\"horizontal\" name=\"form_in_modal\">\n\t\t\t\t\t{props.children && Object.prototype.toString.call(props.children) === '[object Function]'\n\t\t\t\t\t\t? props.children(form, formChangeRes)\n\t\t\t\t\t\t: props.children}\n\t\t\t\t</Form>\n\t\t\t</Spin>\n\t\t</Modal>\n\t);\n};\n\nexport default ModalForm;\n", "import React, { ReactNode, useState, useEffect, ReactText } from 'react';\nimport { Form, Row, Col, Input, Select, Button } from 'antd';\nimport { DeleteOutlined, PlusOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';\nimport './MixSearch.less';\nimport { LabeledValue } from 'antd/lib/select';\nimport { useTranslation } from 'react-i18next';\n\nconst { Option } = Select;\nexport interface IMixSearchParamItem {\n\tname: string\n\ttype: TMixSearchType\n\ttitle?: string\n\tdefalutValue?: any\n\tplaceHolder?: string\n\toption?: LabeledValue[]\n\tmultiple?: boolean\n\tindexKey?: number\n\tused?: boolean\n}\n\nexport type TMixSearchType = 'input' | 'select' | 'datePicker' | 'rangePicker'\n\ninterface IProps {\n\tparams?: IMixSearchParamItem[]\n\tvalues?: Array<{ key: ReactText | undefined, value: ReactText | undefined }>\n\tonChange: (values: Array<{ key: ReactText | undefined, value: ReactText | undefined }>) => void;\n}\n\nconst MixSearch = (props: IProps) => {\n\tconst [form] = Form.useForm();\n\tconst [collapsed, setCollapsed]: [boolean, any] = useState(false);\n\tconst [typeArr, setTypeArr]: [(string | undefined)[], any] = useState([]);\n\n\t// 序列化数据\n\tconst formatParamsData = (data?: IMixSearchParamItem[]) => {\n\t\treturn (data || []).map((item, indexKey) => ({ ...item, indexKey }))\n\t}\n\tconst [paramsData, setParamsData] = useState<IMixSearchParamItem[]>(formatParamsData(props.params))\n\tconst [currentParamsData, setCurrentParamsData] = useState<IMixSearchParamItem[]>(formatParamsData(props.params))\n\tconst [paramsDataMap, setParamsDataMap] = useState<Map<string, IMixSearchParamItem>>(new Map())\n\n\tconst { t, i18n } = useTranslation();\n\n\tuseEffect(() => {\n\t\tif (props.values) {\n\t\t\tconst group = props.values.length ? props.values : [{\n\t\t\t\tkey: undefined,\n\t\t\t\tvalue: undefined\n\t\t\t}]\n\t\t\tform.setFieldsValue({\n\t\t\t\tgroup\n\t\t\t})\n\n\t\t\tconst tarData = [...currentParamsData]\n\t\t\tfor (let i = 0; i < tarData.length; i++) {\n\t\t\t\tfor (let j = 0; j < group.length; j++) {\n\t\t\t\t\tconst value = group[j];\n\t\t\t\t\tif (value !== undefined && group[j].key === tarData[i].name) {\n\t\t\t\t\t\ttarData[i].used = true\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tsetCurrentParamsData(tarData)\n\t\t}\n\t}, [props.values])\n\n\tuseEffect(() => {\n\t\tif (props.params && props.params.length) {\n\t\t\tconst formatData = formatParamsData(props.params)\n\t\t\tsetParamsData(formatData)\n\t\t\tconst dataMap = paramsDataMap\n\t\t\tfor (let i = 0; i < formatData.length; i++) {\n\t\t\t\tconst param = formatData[i];\n\t\t\t\tdataMap.set(param.name, param)\n\t\t\t}\n\t\t\tsetParamsDataMap(dataMap)\n\t\t}\n\t}, [props.params])\n\n\t/**利用表单获取查询字段 */\n\tconst handleFinishForm = async (values: any): Promise<void> => {\n\t\tconsole.log(values);\n\t\tconst preVal = values['group'].filter(((item: any) => !!item.key))\n\t\tconst tarVal = preVal.map((item: any) => ({ key: item.key, value: item.value }))\n\t\tprops.onChange(tarVal);\n\t};\n\n\t/**展开收起 */\n\tconst handleCollapsed = (): void => {\n\t\tsetCollapsed(!collapsed);\n\t};\n\n\t/**选择筛选类型 */\n\tconst handleSelectType = (name: string, index: number): void => {\n\t\tform.resetFields([['group', index, 'value']]);\n\t\tlet arr = [...typeArr];\n\t\tarr[index] = name;\n\t\tsetTypeArr(arr);\n\t};\n\n\t/**根据选择的类型，渲染input或select */\n\tconst handleRenderValueEl = (index: number): ReactNode => {\n\t\tlet key = form.getFieldValue(['group', index, 'key']);\n\t\tif (key) {\n\t\t\tconst currentItem = paramsDataMap.get(key)\n\t\t\tif (currentItem?.type === 'input') {\n\t\t\t\treturn <Input\n\t\t\t\t\tstyle={{ width: '65%' }}\n\t\t\t\t\tdefaultValue={currentItem.defalutValue}\n\t\t\t\t\tplaceholder={currentItem.placeHolder}\n\t\t\t\t\tonPressEnter={() => handlePressEnter()} />\n\t\t\t} else if (currentItem?.type === 'select') {\n\t\t\t\tconst currentOptions = currentItem?.option || []\n\t\t\t\treturn <Select\n\t\t\t\t\tstyle={{ width: '65%' }}\n\t\t\t\t\tdropdownMatchSelectWidth={500}\n\t\t\t\t\tshowSearch\n\t\t\t\t\tmode={key === 'label' ? 'multiple' : undefined}\n\t\t\t\t\toptionFilterProp=\"label\"\n\t\t\t\t\toptions={currentOptions.map(item => ({ label: item.label, value: item.value }))}\n\t\t\t\t// onDropdownVisibleChange={(open) => handleDropdown(open, key)}\n\t\t\t\t/>\n\t\t\t}\n\t\t} else {\n\t\t\treturn <Input style={{ width: '65%' }} onPressEnter={() => handlePressEnter()} />;\n\t\t}\n\t};\n\n\t/**下拉获取对应的数据，并判断是否存在下拉数据，没有则请求，有则不请求 */\n\t// const handleDropdown = async (open: boolean, key: string): Promise<void> => {\n\t// \tif (open) {\n\t// \t\tif (selectionData[key]) {\n\t// \t\t\treturn;\n\t// \t\t}\n\t// \t\ttry {\n\t// \t\t\tlet res = await getSelections(key);\n\t// \t\t\tlet data = { ...selectionData };\n\t// \t\t\tdata[key] = res.data.data;\n\t// \t\t\tsetSelectionData(data);\n\t// \t\t} catch (error) { }\n\t// \t}\n\t// };\n\n\t/**输入框回车操作 */\n\tconst handlePressEnter = (): void => {\n\t\tform.validateFields();\n\t};\n\n\treturn (\n\t\t<Form\n\t\t\t// {...formConfig}\n\t\t\tclassName=\"cmdb-mixsearch bg-title\"\n\t\t\tform={form}\n\t\t\tonFinish={handleFinishForm}\n\t\t\tinitialValues={{\n\t\t\t\tgroup: [\n\t\t\t\t\t{\n\t\t\t\t\t\tkey: undefined,\n\t\t\t\t\t\tvalue: undefined,\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t}}\n\t\t>\n\t\t\t<Row className=\"cmdb-mixsearch-content\" gutter={16} style={{ marginLeft: 0, marginRight: 0, ...collapsed ? { height: 70 } : { height: 'auto' } }}>\n\t\t\t\t<Form.List name={`group`}>\n\t\t\t\t\t{(fields, { add, remove }) => {\n\t\t\t\t\t\treturn <>\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tfields.map((field, index) => {\n\t\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\t\t<Col span={8} key={`mixSearch_${field.key}_${index}`}>\n\t\t\t\t\t\t\t\t\t\t\t<Row align=\"middle\" gutter={8}>\n\t\t\t\t\t\t\t\t\t\t\t\t{/* <Col className=\"cmdb-mixsearch-name\">名称</Col> */}\n\t\t\t\t\t\t\t\t\t\t\t\t<Col className=\"cmdb-mixsearch-group\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Input.Group compact>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Item\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnoStyle\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname={[field.name, 'key']}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trules={[{ required: false, message: t('请选择key') }]}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// initialValue={'testParams'}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: '35%' }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={t('请选择')}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(value: string) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// handleSelectType(value, index)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst selectActionRemove = (value: string) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst tarData = [...currentParamsData]\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst usedKey = (form.getFieldValue('group') || []).filter((item: any) => !!item).map((item: any) => item.key)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfor (let i = 0; i < tarData.length; i++) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst item = tarData[i];\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (item.name === value) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttarData[i].used = true\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} else if (!usedKey.includes(item.name)) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttarData[i].used = false\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetCurrentParamsData(tarData)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectActionRemove(value)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{currentParamsData.map((item, index) => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Option style={{ display: item.used ? 'none' : 'inherit' }} key={`mixSearch_${item.name}_${index}`} value={item.name}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.title || item.name}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Item>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Form.Item\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tnoStyle\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tshouldUpdate\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname={[field.name, 'value']}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trules={[{ required: false, message: t('请填写value') }]}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{handleRenderValueEl(index)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Form.Item>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Input.Group>\n\t\t\t\t\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t\t\t\t\t\t{(\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Col className=\"cmdb-mixsearch-delete\" onClick={() => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst usedKey = (form.getFieldValue('group') || []).map((item: any) => item ? item.key : undefined)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst tarData = [...currentParamsData]\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (usedKey[index]) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfor (let i = 0; i < tarData.length; i++) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst item = tarData[i];\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (item.name === usedKey[index]) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttarData[i].used = false\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetCurrentParamsData(tarData)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tremove(index)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DeleteOutlined />\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t\t\t{/* {index === fields.length - 1 && index < (paramsData.length - 1) && (\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Col className=\"cmdb-mixsearch-add\" onClick={() => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tadd()\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<PlusOutlined />\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t\t\t\t\t\t)} */}\n\t\t\t\t\t\t\t\t\t\t\t</Row>\n\t\t\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t{paramsData.length !== fields.length && (\n\t\t\t\t\t\t\t\t<Col className=\"cmdb-mixsearch-add d-il\" onClick={() => {\n\t\t\t\t\t\t\t\t\tadd()\n\t\t\t\t\t\t\t\t}}>\n\t\t\t\t\t\t\t\t\t<PlusOutlined />\n\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t</>\n\t\t\t\t\t}}\n\t\t\t\t</Form.List>\n\n\t\t\t\t<Col flex={1}>\n\t\t\t\t\t<Row justify=\"end\">\n\t\t\t\t\t\t<Button type=\"primary\" htmlType=\"submit\">\n\t\t\t\t\t\t\t{t('查询')}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Row>\n\t\t\t\t</Col>\n\t\t\t</Row>\n\t\t\t<Row className=\"cmdb-mixsearch-collapsed\">\n\t\t\t\t<Row onClick={() => handleCollapsed()} justify=\"center\" align=\"middle\">\n\t\t\t\t\t{collapsed ? (\n\t\t\t\t\t\t<>\n\t\t\t\t\t\t\t<Col>{t('展开')}</Col>\n\t\t\t\t\t\t\t<Col>\n\t\t\t\t\t\t\t\t<DownOutlined />\n\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t</>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t\t<>\n\t\t\t\t\t\t\t\t<Col>{t('收起')}</Col>\n\t\t\t\t\t\t\t\t<Col>\n\t\t\t\t\t\t\t\t\t<UpOutlined />\n\t\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t\t</>\n\t\t\t\t\t\t)}\n\t\t\t\t</Row>\n\t\t\t</Row>\n\t\t</Form>\n\t);\n};\n\nexport default MixSearch;\n", "import { AudioOutlined, CloseOutlined, DeleteColumnOutlined, DeleteFilled, DeleteOutlined, InboxOutlined, LoadingOutlined, PlusOutlined, VideoCameraAddOutlined } from '@ant-design/icons';\nimport { message } from 'antd';\nimport Upload, { RcFile, UploadChangeParam } from 'antd/lib/upload';\nimport { UploadFile } from 'antd/lib/upload/interface';\nimport React, { useEffect, useState } from 'react'\nimport './FileUploadPlus.less';\n\ninterface Iprops {\n    filetype?: TFileType\n    onChange?: (value: any) => void\n    value?: string[]\n    maxCount?: number\n    maxSize?: number\n    format?: string[]\n}\n\ntype TFileType = 'file' | 'video' | 'audio'\n\nexport default function FileUploadPlus(props: Iprops) {\n    const [visableChangePhone, setVisableChangePhone] = useState(false);\n    const [fileLoading, setFileLoading] = useState(false);\n    const [imgUrl, setImgUrl] = useState('');\n    const [imageList, setImageList] = useState<string[]>([])\n    const [loading, setLoading] = useState(true);\n    const [fileList, setFileList] = useState<UploadFile[]>([])\n\n    // useEffect(() => {\n    //     setFileList(props.value || [])\n    // }, [props.value])\n\n    function getBase64(img: any, callback: any) {\n        const reader = new FileReader();\n        reader.addEventListener('load', () => callback(reader.result));\n        reader.readAsDataURL(img);\n    }\n    // console.log('props', props);\n    function beforeUpload(file: RcFile) {\n        // console.log('file', file);\n\n        const maxCount = props.maxCount || 1\n        if (fileList.length >= maxCount) {\n            message.error('超出文件数量限制');\n            return false\n        }\n        // 'image/jpeg' || 'video/mp4' || 'audio/mpeg'\n        const isFormatOk = props.format?.includes(file.type);\n        if (!isFormatOk) {\n            message.error('文件格式错误');\n        }\n        const isLt2M = file.size < (props.maxSize || 2 * 1024 * 1024);\n        if (!isLt2M) {\n            message.error('文件大小限制');\n        }\n        return isFormatOk && isLt2M;\n    }\n\n    const handleChange = (info: UploadChangeParam) => {\n        console.log(info);\n\n        if (info.file.status === 'uploading') {\n            setFileLoading(true);\n            return;\n        }\n        if (info.file.status === 'done') {\n            setFileLoading(false);\n            setFileList(info.fileList)\n            props.onChange && props.onChange(info.fileList)\n        }\n        if (info.file.status === \"removed\") {\n            setFileList(info.fileList)\n            props.onChange && props.onChange(info.fileList)\n            return;\n        }\n    };\n\n    const file2Bin = (file?: RcFile) => {\n        console.log('file2Bin', file);\n        return new Promise((resolve, reject) => {\n            if (file) {\n                let name = file.name.replace(/.+\\./, '');\n                let filename = file.name;\n                let reader = new FileReader();\n                reader.readAsDataURL(file);\n                reader.onload = () => {\n                    resolve(reader.result)\n                }\n            } else {\n                reject(undefined)\n            }\n        })\n    }\n\n    //建立一个可存取到该file的url\n    function getObjectURL(file: any) {\n        var url = null;\n        if ((window as any).createObjectURL != undefined) { // basic\n            url = (window as any).createObjectURL(file);\n        } else if (window.URL != undefined) { // mozilla(firefox)\n            url = window.URL.createObjectURL(file);\n        } else if (window.webkitURL != undefined) { // webkit or chrome\n            url = window.webkitURL.createObjectURL(file);\n        }\n        return url;\n    }\n\n    const createMediaPreview = (file: UploadFile<any>, fileIndex: number, filetype: TFileType) => {\n        const url = getObjectURL(file)\n        const key = Math.random().toString(36).substring(2);\n        if (filetype === 'video') {\n            return <div className=\"p-r\" key={key}>\n                <span\n                    onClick={() => {\n                        const currentFileList = [...fileList]\n                        currentFileList.splice(fileIndex, 1)\n                        setFileList(currentFileList)\n                        props.onChange && props.onChange(currentFileList)\n                    }}\n                    className=\"d-il p-a plr8 ptb2 bg-fail\"\n                    style={{ top: 0, right: 0, borderBottomLeftRadius: 6, zIndex: 9 }}>\n                    <DeleteOutlined style={{ color: '#fff' }} />\n                </span>\n                <video className=\"w100 mb8\" src={url} controls></video>\n            </div>\n        } else if (filetype === 'audio') {\n            return <div className=\"d-f ac mb8\" key={key}>\n                <audio className=\"w100 flex1\" src={url} controls></audio>\n                <span\n                    onClick={() => {\n                        const currentFileList = [...fileList]\n                        currentFileList.splice(fileIndex, 1)\n                        setFileList(currentFileList)\n                        props.onChange && props.onChange(currentFileList)\n                    }}\n                    className=\"d-il plr8 ptb2 bg-fail\"\n                    style={{ borderRadius: 6 }}>\n                    <DeleteOutlined style={{ color: '#fff' }} />\n                </span>\n            </div>\n        }\n        return file\n    }\n\n    return (\n        <>\n            <div>\n                {\n                    fileList.map((file, fileIndex) => {\n                        return createMediaPreview(file, fileIndex, props.filetype || 'file')\n                    })\n                }\n            </div>\n            <Upload.Dragger\n                // name=\"file\"\n                fileList={fileList}\n                showUploadList={false}\n                customRequest={(options) => {\n                    console.log(\"options.file\",options.file);\n                    const tarList = [...fileList, options.file as RcFile]\n                    console.log(\"tarList\",tarList);\n                    setFileList(tarList)\n\n                    Promise.all(tarList.map((item: any) => file2Bin(item))).then(res => {\n                        console.log(res)\n                        props.onChange && props.onChange(res)\n                    })\n\n                    // getBase64(options.file, (imageUrl: string) => {\n                    //     // setImgUrl(imageUrl);\n                    //     const tarList = [...imageList, imageUrl]\n                    //     setImageList(tarList)\n                    //     setFileLoading(false);\n                    //     props.onChange && props.onChange(tarList)\n                    // });\n                }}\n                beforeUpload={beforeUpload}\n                onChange={handleChange}\n            >\n                <p className=\"ant-upload-drag-icon\">\n                    {\n                        (props.filetype === 'file' || !props.filetype) ? <InboxOutlined /> : null\n                    }\n                    {\n                        props.filetype === 'video' ? <VideoCameraAddOutlined /> : null\n                    }\n                    {\n                        props.filetype === 'audio' ? <AudioOutlined /> : null\n                    }\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件上传</p>\n            </Upload.Dragger>\n        </>\n    )\n}\n", "import React, { useState } from 'react';\nimport CodeMirror, { ReactCodeMirrorProps } from '@uiw/react-codemirror';\nimport { json } from '@codemirror/lang-json';\nimport { EditorView } from '@codemirror/view';\nimport { useTranslation } from 'react-i18next';\n\ninterface IProps extends ReactCodeMirrorProps {\n  onChange?: (value: string) => void;\n}\n\nexport default function JsonEditor(props: IProps) {\n  const { t, i18n } = useTranslation();\n  const [isValidJson, setIsValidJson] = useState(true);\n\n  // 格式化 JSON 内容\n  const formatJson = (code: string) => {\n    try {\n      const parsed = JSON.parse(code);\n      const formattedJson =  JSON.stringify(parsed, null, 2);\n      return formattedJson;\n    } catch (e) {\n      return code;\n    }\n  };\n  const handleEditorChange = (value: string) => {\n    try {\n      const parsed = JSON.parse(value);\n      const formattedJson =  JSON.stringify(parsed, null, 2);\n      if (props.onChange) {\n        props.onChange(formattedJson);\n      }\n      setIsValidJson(true);\n    } catch (e) {\n      setIsValidJson(false);\n      if (props.onChange) {\n        props.onChange(value);\n      }\n    }\n  };\n\n  return (\n    <div>\n      <CodeMirror\n        value={formatJson(props.value || '{}')}\n        onChange={(value) => handleEditorChange(value)}  // 处理内容变化\n        readOnly={props.readOnly}  // 控制只读模式\n        maxHeight=\"300px\"\n        extensions={[\n          json(),               // 启用 JSON 语法高亮\n          EditorView.lineWrapping, // 启用自动换行\n        ]}\n        placeholder={props.placeholder}  // 设置占位符\n        basicSetup={{ lineNumbers: false }}  // 启用行号\n      />\n      {/* 显示无效 JSON 提示 */}\n      {!isValidJson && (\n        <div style={{ color: 'red', marginTop: '10px' }}>\n          {t(\"json格式错误\")}\n        </div>\n      )}\n    </div>\n  );\n}\n", "import { <PERSON><PERSON>, <PERSON>, Collapse, DatePicker, Form, FormInstance, Input, message, Radio, Row, Space, Cascader, Steps, Tooltip } from 'antd'\nimport { Rule, RuleObject } from 'antd/lib/form'\nimport Select from 'antd/lib/select'\nimport React, { useEffect, useLayoutEffect, useRef, useState } from 'react'\nimport moment from \"moment\";\nimport { MinusCircleOutlined, PlusOutlined, QuestionCircleOutlined, SyncOutlined } from '@ant-design/icons';\nimport InputSearch from '../InputSearch/InputSearch';\nimport 'moment/locale/zh-cn';\nimport locale from 'antd/es/date-picker/locale/zh_CN';\nimport { useTranslation } from 'react-i18next';\nimport FileUploadPlus from '../FileUploadPlus/FileUploadPlus';\nimport JsonEditor from '../JsonEditor/JsonEditor';\n\ninterface IProps {\n    primaryKey?: string\n    form?: FormInstance\n    config?: IDynamicFormConfigItem[]\n    configGroup?: IDynamicFormGroupConfigItem[]\n    formChangeRes?: IFormChangeRes\n    linkageConfig?: ILinkageConfig[]\n    dataOptions?: any\n    onRetryInfoChange?: (value?: string) => void\n}\n\nexport interface ILinkageConfig {\n    dep: string[]\n    effect: string\n    effectOption: Record<string | number, LabeledValue[]>\n}\n\ninterface IFormChangeRes {\n    currentChange: Record<string, any>\n    allValues: Record<string, any>\n}\nexport interface IDynamicFormGroupConfigItem {\n    expanded: boolean\n    group: string\n    config: IDynamicFormConfigItem[]\n}\n\nexport interface LabeledValue {\n    key?: string;\n    value: string | number;\n    label: React.ReactNode;\n    children?: LabeledValue[]\n}\n\nexport interface IDynamicFormConfigItem {\n    name: string\n    label: string\n    type: TDynamicFormType\n    defaultValue?: number | string\n    required?: boolean\n    placeHolder?: string\n    options?: LabeledValue[]\n    rules?: Rule[]\n    disable?: boolean\n    description?: any\n    multiple?: boolean,\n    list?: IDynamicFormConfigItem[]\n    data: Record<string, any>\n}\n\nexport type TDynamicFormType = 'input' | 'textArea' | 'select' | 'datePicker' | 'rangePicker' | 'radio' | 'checkout' | 'match-input' | 'input-select' | 'fileUpload' | 'cascader' | 'json'\n\nexport function calculateId(strList: string[]): number {\n    const str2Num = (str: string) => {\n        const res = (str || '').split('').reduce((pre, next) => pre + next.charCodeAt(0), 0)\n        return res\n    }\n    const sum = strList.reduce((pre, next) => pre + str2Num(next), 0)\n    return sum\n}\n\nexport default function DynamicForm(props: IProps) {\n    const { dataOptions } = props;\n    const { t, i18n } = useTranslation();\n    const [current, setCurrent] = useState(0);\n    const [currentConfig, _setCurrentConfig] = useState(props.config)\n    const currentConfigRef = useRef(props.config);\n    const setCurrentConfig = (data: IDynamicFormConfigItem[] | undefined): void => {\n        currentConfigRef.current = data;\n        _setCurrentConfig(data);\n    };\n\n    const [currentConfigGroup, _setCurrentConfigGroup] = useState(props.configGroup)\n    const currentConfigGroupRef = useRef(props.configGroup);\n    const setCurrentConfigGroup = (data: IDynamicFormGroupConfigItem[] | undefined): void => {\n        currentConfigGroupRef.current = data;\n        _setCurrentConfigGroup(data);\n    };\n\n    const findOptionInLinkAge = (field: string, config: ILinkageConfig[]): Array<{\n        effect: string\n        option: LabeledValue[]\n    }> => {\n        const res = config.filter(configItem => configItem.dep.includes(field)).map(item => {\n            const values = item.dep.map(item => props.form?.getFieldValue(item)).filter(item => !(item === undefined || item === null))\n            const valueId = calculateId(values)\n            return {\n                effect: item.effect,\n                option: item.effectOption[valueId] || []\n            }\n        })\n        return res\n    }\n\n    const setValueInConfig = (field: string, props: Record<string, any>) => {\n        const tarConfig = currentConfigRef.current ? [...currentConfigRef.current] : []\n        if (tarConfig) {\n            for (let i = 0; i < tarConfig.length; i++) {\n                const item = tarConfig[i];\n                if (item.name === field) {\n                    tarConfig[i] = {\n                        ...item,\n                        ...props\n                    }\n                }\n            }\n        }\n        setCurrentConfig(tarConfig)\n    }\n\n    const setValueInConfigGroup = (field: string, props: Record<string, any>) => {\n        const tarConfigGroup = currentConfigGroupRef.current ? [...currentConfigGroupRef.current] : []\n        for (let i = 0; i < tarConfigGroup.length; i++) {\n            const configList = [...tarConfigGroup[i].config];\n            for (let j = 0; j < configList.length; j++) {\n                const item = configList[j];\n                if (item.name === field) {\n                    configList[j] = {\n                        ...item,\n                        ...props\n                    }\n                }\n            }\n            tarConfigGroup[i] = {\n                ...tarConfigGroup[i],\n                config: configList\n            }\n        }\n        setCurrentConfigGroup(tarConfigGroup)\n    }\n\n    const resetFieldProps = (field: string, linkageConfig: ILinkageConfig[]) => {\n        const optionInlinkAge = findOptionInLinkAge(field, linkageConfig)\n        optionInlinkAge.forEach(item => {\n            props.form?.setFieldsValue({ [item.effect]: undefined })\n            setValueInConfig(item.effect, { options: item.option })\n            setValueInConfigGroup(item.effect, { options: item.option })\n        })\n    }\n\n    useEffect(() => {\n        if (props.formChangeRes && props.linkageConfig) {\n            const { currentChange } = props.formChangeRes\n            resetFieldProps(Object.keys(currentChange)[0], props.linkageConfig)\n        }\n    }, [props.formChangeRes])\n\n    // 表单联动初始化\n    useEffect(() => {\n        setCurrentConfig(props.config)\n        setCurrentConfigGroup(props.configGroup)\n        const formValues = props.form?.getFieldsValue() || {}\n        Object.entries(formValues).forEach(([key, value]) => {\n            if (value !== undefined) {\n                resetFieldProps(key, props.linkageConfig || [])\n            }\n        })\n    }, [props.configGroup, props.config, dataOptions])\n\n    const next = () => {\n        setCurrent(current + 1);\n    };\n\n    const prev = () => {\n        setCurrent(current - 1);\n    };\n\n    const renderFileUpload = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            {...itemProps}\n        >\n            <FileUploadPlus\n                filetype={config.data.type}\n                format={config.data.format}\n                maxCount={config.data.maxCount || 1}\n            />\n        </Form.Item>\n    }\n\n    const renderInput = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        // const rules: Rule[] = [\n        //     { required: config.required, message: `请输入${config.label}` },\n        //     config.rule ? { pattern: new RegExp(`/^${config.rule}$/`), message: '请按正确的规则输入' } : undefined,\n        // ].filter(item => !!item) as Rule[]\n\n        let extraContent: any = null\n\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n            <Input disabled={config.disable} placeholder={config.placeHolder || `${t('请选择')}${config.label}`} />\n        </Form.Item>\n    }\n\n    const renderMatchInput = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n\n        return <Form.Item key={`dynamicForm_${config.name}_noStyle`} noStyle shouldUpdate={(pre, next) => {\n            // todo:更新有点问题\n            // return pre[config.name] != pre[config.name]\n            return JSON.stringify(pre) != JSON.stringify(next)\n        }}>\n            {\n                ({ getFieldValue, setFieldsValue }) => {\n                    const templateText = `${config.defaultValue}`\n                    const matchList = templateText.match(/\\$\\{\\w*}/gi) || []\n                    let value = templateText\n                    matchList.forEach(item => {\n                        const itemKey = item.replace(/^\\$\\{/, '').replace(/\\}$/, '')\n                        const itemValue = getFieldValue(itemKey)\n                        if (itemValue !== undefined) {\n                            value = value.replace(item, itemValue)\n                        }\n                    })\n\n                    if (getFieldValue(config.name) !== value) {\n                        setFieldsValue({\n                            [config.name]: value\n                        })\n                    }\n\n                    return <Form.Item\n                        key={`dynamicForm_${config.name}`}\n                        label={config.label}\n                        name={config.name}\n                        rules={config.rules}\n                        extra={<>\n                            {config.data.tips ? <Tooltip\n                                className=\"mr8\"\n                                placement=\"bottom\"\n                                title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                            >\n                                <div className=\"cp d-il\">\n                                    <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                                    <span className=\"pl4 c-theme\">{t('详情')}</span>\n                                </div>\n                            </Tooltip> : null}\n                            {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n                        </>}\n                        {...itemProps}\n                    >\n                        <Input disabled={true} />\n                    </Form.Item>\n                }\n            }\n        </Form.Item>\n    }\n\n    const renderInputSelect = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        // const rules = [\n        //     { required: config.required, message: `${t('请选择')}${config.label}` },\n        // ]\n        const options: string[] = (config.options || []).map(item => item.label as string)\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n            <InputSearch\n                onClick={(value) => {\n                    !!config.data.retry_info && props.onRetryInfoChange && props.onRetryInfoChange(value)\n                }}\n                isOpenSearchMatch={true}\n                disabled={config.disable}\n                placeholder={`${t('请选择')}${config.label}`}\n                options={options} />\n        </Form.Item>\n    }\n\n    const renderTextArea = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n            <Input.TextArea autoSize={{ minRows: 4 }} disabled={config.disable} placeholder={config.placeHolder || `${t('请选择')}${config.label}`} />\n        </Form.Item>\n    }\n    const renderJsonEditor = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n            <JsonEditor readOnly={config.disable} placeholder={config.placeHolder || `${t('请选择')}${config.label}`} />\n        </Form.Item>\n    }\n    const renderSelect = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        // const rules = [\n        //     { required: config.required, message: `${t('请选择')}${config.label}` },\n        // ]\n        const options: LabeledValue[] = config.options || []\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span className=\"pr4\" dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n                {\n                    config.data.isRefresh ? <div className=\"cp d-il\" onClick={() => {\n                        props.onRetryInfoChange && props.onRetryInfoChange()\n                    }}>\n                        <SyncOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('刷新列表')}</span>\n                    </div> : null\n                }\n            </>}\n            {...itemProps}\n        >\n            <Select\n                style={{ width: '100%' }}\n                mode={config.multiple ? 'multiple' : undefined}\n                onChange={(value) => {\n                    !!config.data.retry_info && props.onRetryInfoChange && props.onRetryInfoChange(value)\n                }}\n                showSearch\n                disabled={config.disable}\n                optionFilterProp=\"label\"\n                placeholder={config.placeHolder || `${t('请选择')}${config.label}`}\n                options={options} />\n        </Form.Item>\n    }\n    const renderRadio = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        // const rules = [\n        //     { required: config.required, message: `${t('请选择')}${config.label}` },\n        // ]\n        const options = config.options || []\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n            <Radio.Group options={options} />\n        </Form.Item>\n    }\n    const renderDatePicker = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={[{ required: true, message: t('请选择时间') }]}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n            <DatePicker style={{ width: '100%' }} locale={locale} showTime={!!config.data.showTime} disabledDate={(current) => {\n                return current && current > moment().endOf('day');\n            }} />\n        </Form.Item>\n    }\n    const renderRangePicker = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={[{ required: true, message: t('请选择时间范围') }]}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n            <DatePicker style={{ width: '100%' }} locale={locale} showTime={!!config.data.showTime} disabledDate={(current) => {\n                return current && current > moment().endOf('day');\n            }} />\n        </Form.Item>\n    }\n    // 多级选择器\n    const renderCascader = (config: IDynamicFormConfigItem, itemProps: Record<string, any>) => {\n        const options = config.options || []\n        console.log(options)\n        return <Form.Item\n            key={`dynamicForm_${config.name}`}\n            label={config.label}\n            name={config.name}\n            rules={config.rules}\n            initialValue={config.defaultValue}\n            extra={<>\n                {config.data.tips ? <Tooltip\n                    className=\"mr8\"\n                    placement=\"bottom\"\n                    title={<span dangerouslySetInnerHTML={{ __html: config.data.tips }}></span>}\n                >\n                    <div className=\"cp d-il\">\n                        <QuestionCircleOutlined style={{ color: '#1672fa' }} />\n                        <span className=\"pl4 c-theme\">{t('详情')}</span>\n                    </div>\n                </Tooltip> : null}\n                {config.description ? <span dangerouslySetInnerHTML={{ __html: config.description }}></span> : null}\n            </>}\n            {...itemProps}\n        >\n\n            <Cascader placeholder=\"select\" options={options} />\n        </Form.Item>\n    }\n    const dispatchRenderFormItem = (item: IDynamicFormConfigItem, itemProps: Record<string, any> = {}): JSX.Element | null => {\n        switch (item.type) {\n            case 'input':\n                return renderInput(item, itemProps)\n            case 'match-input':\n                return renderInput(item, itemProps)\n            // return renderMatchInput(item, itemProps)\n            case 'input-select':\n                return renderInputSelect(item, itemProps)\n            case 'cascader':\n                return renderCascader(item, itemProps)\n            case 'textArea':\n                return renderTextArea(item, itemProps)\n            case 'json':\n                return renderJsonEditor(item, itemProps)\n            case 'select':\n                return renderSelect(item, itemProps)\n            case 'datePicker':\n                return renderDatePicker(item, itemProps)\n            case 'rangePicker':\n                return renderRangePicker(item, itemProps)\n            case 'radio':\n                return renderRadio(item, itemProps)\n            case 'fileUpload':\n                return renderFileUpload(item, itemProps)\n            default:\n                return null\n        }\n    }\n\n    const renderFormItem = (config: IDynamicFormConfigItem[]): Array<any | null> => {\n        return (config || []).map(item => {\n            if (item.list && item.list.length) {\n                const formList = <Form.List key={`dynamicForm_${item.name}`} name={item.name}>\n                    {(fields, { add, remove }) => (\n                        <>\n                            {fields.map(({ key, name, ...restField }) => (\n                                // <Space key={key} style={{ display: 'flex', marginBottom: 8 }}\n                                //     align='baseline'\n                                // >\n                                //     {\n                                //         item.list && item.list.map(listItem => {\n                                //             return dispatchRenderFormItem(listItem, {\n                                //                 ...restField,\n                                //                 name: [name, listItem.name],\n                                //                 // style: { flexDirection: 'column' }\n                                //             })\n                                //         })\n                                //     }\n                                //     {/* <MinusCircleOutlined onClick={() => remove(name)} /> */}\n                                //     <Form.Item wrapperCol={{ offset: 5 }}>\n                                //         <Button danger onClick={() => remove(name)} block icon={<MinusCircleOutlined />}>\n                                //             删除该项\n                                //         </Button>\n                                //     </Form.Item>\n                                // </Space>\n                                <div key={key} className=\"bor b-side pt8 plr16 mb8 d-f\" style={{ alignItems: 'start', minWidth: 1600 }}>\n                                    {\n                                        item.list && item.list.map(listItem => {\n                                            return dispatchRenderFormItem(listItem, {\n                                                ...restField,\n                                                name: [name, listItem.name],\n                                                labelAlign: 'left',\n                                                labelCol: 24,\n                                                style: { flexDirection: 'column', flex: 1, marginBottom: 8 },\n                                            })\n                                        })\n                                    }\n                                    {/* <MinusCircleOutlined onClick={() => remove(name)} /> */}\n                                    <Form.Item >\n                                        <Button danger onClick={() => remove(name)} block icon={<MinusCircleOutlined />} style={{ width: 120 }}>\n                                            {t('删除该项')}\n                                        </Button>\n                                    </Form.Item>\n                                </div>\n                            ))}\n                            <Form.Item noStyle className=\"w100\" label=\"\">\n                                <Button type=\"dashed\" className=\"w100\" onClick={() => add()} block icon={<PlusOutlined />}>\n                                    {t('增加一项')}\n                                </Button>\n                            </Form.Item>\n                        </>\n                    )}\n                </Form.List>\n                return formList\n            } else {\n                return <div style={{ width: 680 }}>\n                    {dispatchRenderFormItem(item)}\n                </div>\n            }\n        })\n    }\n\n    return (\n        <>\n            <Form.Item\n                key={`dynamicForm_id`}\n                name={props.primaryKey || 'id'}\n                noStyle\n                hidden\n            >\n                <Input />\n            </Form.Item>\n\n            {\n                currentConfigGroup && currentConfigGroup.length ? <>\n                    <Steps current={current}>\n                        {\n                            (currentConfigGroup || []).map((item, index) => {\n                                return <Steps.Step key={index} title={item.group} />\n                            })\n                        }\n                    </Steps>\n                    <div className=\"pt32\">\n                        {\n                            (currentConfigGroup || []).map((item, index) => {\n                                return <div key={index} className={[current === index ? 'p-r z9' : 'p-a z-99 v-h l-10000'].join(' ')}>\n                                    {renderFormItem(item.config)}\n                                </div>\n                            })\n                        }\n                    </div>\n                    <div className=\"ta-c pt32\">\n                        {current > 0 && (\n                            <Button onClick={() => prev()}>\n                                {t('上一步')}\n                            </Button>\n                        )}\n                        {current < (currentConfigGroup || []).length - 1 && (\n                            <Button type=\"primary\" className=\"ml16\" onClick={() => {\n                                if (props.form) {\n                                    const currentConfigGroupNameList = currentConfigGroup[current].config.map(item => item.name)\n                                    props.form.validateFields(currentConfigGroupNameList).then(() => {\n                                        next()\n                                    }).catch(err => {\n                                        console.log(err)\n                                    })\n                                } else {\n                                    next()\n                                }\n                            }}>\n                                {t('下一步')}\n                            </Button>\n                        )}\n                        <div>\n                            {current === (currentConfigGroup || []).length - 1 && (\n                                <div className=\"pt8 c-hint-b\">{t('点击确定完成提交')}</div>\n                            )}\n                        </div>\n                    </div>\n                </> : <div style={{ width: 680 }}>\n                    {\n                        renderFormItem(currentConfig || [])\n                    }\n                </div>\n            }\n        </>\n    )\n}\n", "\nimport { useEffect, useState } from \"react\";\nimport { ITabDetailItem, ITabsModalData} from '../../api/interface/tabsModalInterface';\nimport TabsDetail from './TabsDetail';\nimport { Button, Modal, Spin } from \"antd\";\nimport { t } from \"i18next\";\nimport { actionTabsModal, actionTabsModalInfo } from \"../../api/kubeflowApi\";\ninterface IProps {\n\tvisible: boolean;\n\turl: string;\n    onVisibilityChange: (visible: boolean) => void;\n}\nexport default function TabsModal(props: IProps) {\n\tconst [loading, setLoading] = useState(false);\n    const [data, setData] = useState<ITabsModalData>()\n\n    useEffect(() => {\n        fatchData(props.url)\n\t}, [props.url]);\n    const fatchData = (url: string) => {\n        if (!!url) {\n            setLoading(true);\n            actionTabsModalInfo(url)\n                .then((res) => {\n                    if (res.data) {\n                        setData(res.data.result);\n                    } else {\n                        console.error('Invalid data format:', res.data);\n                        setData(undefined);\n                    }\n                })\n                .catch((err) => {\n                    console.error('Error fetching data:', err);\n                })\n                .finally(() => {\n                    setLoading(false);\n                });\n        }\n    };\n    return (\n        <Modal\n            destroyOnClose={true}\n            maskClosable={false}\n            width={680}\n            open={props.visible}\n            title={data?.title}\n            onCancel={() => props.onVisibilityChange(false)}\n            onOk={() => props.onVisibilityChange(false)}\n            footer={\n                data?.bottomButton?.length ? (\n                    <div className=\"flex justify-end\">\n                        {data.bottomButton.map((button, index) => (\n                            <Button\n                                key={`footerButton_${index}`}\n                                type=\"primary\"\n                                onClick={() => actionTabsModal(button.method, button.url, button.arg)}\n                                className=\"ml-2\"\n                            >\n                                {button.icon && (\n                                    <span\n                                        className=\"mr-2\"\n                                        dangerouslySetInnerHTML={{ __html: button.icon }}\n                                    ></span>\n                                )}\n                                {button.text}\n                            </Button>\n                        ))}\n                    </div>\n                ) : null\n            }\n        >\n            <Spin spinning={loading}>\n                {data ? <TabsDetail data={data.content || []} /> : <div>{t('No data available')}</div>}\n            </Spin>\n        </Modal>\n    );\n\n}", "import React, { ReactText, useEffect, useRef, useState } from 'react';\nimport { Button, Col, Input, DatePicker, TablePaginationConfig, Row, message, Space, Menu, Dropdown, Modal, Spin, Form, Tag, Popover, Tooltip, Select, FormInstance, Upload, UploadProps, Drawer, notification, Pagination, Switch } from 'antd';\nimport { Content } from 'antd/lib/layout/layout';\nimport TitleHeader from '../components/TitleHeader/TitleHeader';\nimport TableBox from '../components/TableBox/TableBox';\nimport moment from \"moment\";\nimport { InfoCircleOutlined, CopyOutlined, DownOutlined, ExclamationCircleOutlined, ExportOutlined, PlusOutlined, QuestionCircleOutlined, RollbackOutlined, UploadOutlined } from '@ant-design/icons'\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport {getParam, getTableScroll, isDomString, isJsonString} from '../util';\nimport ModalForm from '../components/ModalForm/ModalForm';\nimport cookies from 'js-cookie';\nimport { IADUGTemplateActionItem, IAppMenuItem } from '../api/interface/kubeflowInterface';\nimport { getADUGTemplateList, getADUGTemplateApiInfo, actionADUGTemplateDelete, getADUGTemplateDetail, actionADUGTemplateAdd, actionADUGTemplateUpdate, actionADUGTemplateSingle, actionADUGTemplateMuliple, actionADUGTemplateRetryInfo, actionADUGTemplateFavorite, actionADUGTemplateCancelFavorite, actionADUGTemplateChartOption } from '../api/kubeflowApi';\nimport { ColumnsType } from 'antd/lib/table';\nimport MixSearch, { IMixSearchParamItem } from '../components/MixSearch/MixSearch';\nimport DynamicForm, { calculateId, IDynamicFormConfigItem, IDynamicFormGroupConfigItem, ILinkageConfig } from '../components/DynamicForm/DynamicForm';\nimport ChartOptionTempalte from './ChartOptionTempalte';\nimport { useTranslation } from 'react-i18next';\nimport './ADUGTemplate.less';\nimport TabsModal from '../components/TabsModal/TabsModal';\n\ninterface fatchDataParams {\n    pageConf: TablePaginationConfig\n    params: any[]\n    paramsMap: Record<string, any>\n    sorter?: ISorterParam\n    only_favorite?: boolean\n}\n\ninterface ISorterParam {\n    order_column: string\n    order_direction: 'desc' | 'asc'\n}\n\nexport default function TaskListManager(props?: IAppMenuItem) {\n    const PAGE_SIZE = 20;\n    const navigate = useNavigate();\n    const location = useLocation()\n    const [dataList, setDataList] = useState<any[]>([]);\n    const [loading, setLoading] = useState(true);\n    const [loadingAdd, setLoadingAdd] = useState(false)\n    const [visableAdd, setVisableAdd] = useState((getParam('isVisableAdd') === 'true') || false)\n    const [loadingUpdate, setLoadingUpdate] = useState(false)\n    const [visableUpdate, setVisableUpdate] = useState(false)\n    const [visibleTabsModal, setVisibleTabsModal] = useState(false)\n    const [enhancedDetailsUrl, setEnhancedDetailsUrl] = useState('')\n    const [loadingDetail, setLoadingDetail] = useState(false)\n    const [visableDetail, setVisableDetail] = useState(false)\n    const [selectedRowKeys, setSelectedRowKeys] = useState<ReactText[]>([])\n    const pageInfoInit: TablePaginationConfig = {\n        current: 1,\n        pageSize: PAGE_SIZE,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        pageSizeOptions: [20, 50, 100, 500],\n        showTotal: (total) => `${t('共')}${total}${t('条')}`,\n    };\n    const [pageInfo, setPageInfo] = useState<TablePaginationConfig>(pageInfoInit);\n    const [currentColumns, setCurrentColumns] = useState<ColumnsType<any>>([])\n    const [filterParams, setFilterParams] = useState<IMixSearchParamItem[]>([])\n    const [filterValues, _setFilterValues] = useState<Array<{ key: ReactText | undefined, value: ReactText | undefined }>>([])\n    const filterValuesRef = useRef(filterValues);\n    const setFilterValues = (data: Array<{ key: ReactText | undefined, value: ReactText | undefined }>): void => {\n        filterValuesRef.current = data;\n        _setFilterValues(data);\n    };\n    const [dynamicFormConfigAdd, setDynamicFormConfigAdd] = useState<IDynamicFormConfigItem[]>([])\n    const [dynamicFormConfigUpdate, setDynamicFormConfigUpdate] = useState<IDynamicFormConfigItem[]>([])\n    const [dynamicFormGroupConfigAdd, setDynamicFormGroupConfigAdd] = useState<IDynamicFormGroupConfigItem[]>([])\n    const [dynamicFormGroupConfigUpdate, setDynamicFormGroupConfigUpdate] = useState<IDynamicFormGroupConfigItem[]>([])\n\n    let customFormData: Record<string, string> = {}\n    try {\n        customFormData = JSON.parse(getParam('formData') || \"{}\")\n    } catch (err) { }\n    const [dynamicFormDataAdd, setDynamicFormDataAdd] = useState(customFormData)\n    const [updateColumnsMap, setUpdateColumnsMap] = useState<Record<string, any>>({})\n    const [labelMap, _setLabelMap] = useState<Record<string, string>>({})\n    const labelMapRef = useRef(labelMap);\n    const setLabelMap = (data: Record<string, string>): void => {\n        labelMapRef.current = data;\n        _setLabelMap(data);\n    };\n    const [dataDetail, setDataDetail] = useState<Array<{ label: string, value: any, key: string }>>([])\n    const [tableWidth, setTableWidth] = useState(1000)\n    const [permissions, setPermissions] = useState<string[]>([])\n    // const [tips, setTips] = useState<Array<{ label: string, value: any }>>([])\n    const [filterParamsMap, setFilterParamsMap] = useState<Record<string, any>>({})\n    const [helpUrl, setHelpUrl] = useState<string | null>()\n\n    const [baseUrl, _setBaseUrl] = useState<string>()\n    const baseUrlRef = useRef(baseUrl);\n    const setBaseUrl = (data: string): void => {\n        baseUrlRef.current = data;\n        _setBaseUrl(data);\n    };\n    const [isImportData, setIsImportData] = useState(false)\n    const [isDownLoadData, setIsDownLoadData] = useState(false)\n    const [columnRelateFormat, setColumnRelateFormat] = useState<ILinkageConfig[]>([])\n    const [multipleAction, setMultipleAction] = useState<IADUGTemplateActionItem[]>([])\n    const [sorterParam, setSorterParam] = useState<{\n        order_column: string\n        order_direction: 'desc' | 'asc'\n    }>()\n    const [primaryKey, setPrimaryKey] = useState('')\n    const [labelTitle, setLabelTitle] = useState('')\n    const [list_ui_type, setList_ui_type] = useState<'card' | 'table'>()\n    const [list_ui_args, setList_ui_args] = useState<{\n        card_width: string\n        card_height: string\n    }>()\n    const [opsLink, setOpsLink] = useState<Array<{\n        text: string\n        url: string\n    }>>([])\n    const [listColumns, setListColumns] = useState<string[]>([])\n    const [isAllDataList, _setIsAllDataList] = useState(true)\n    const isAllDataListRef = useRef(isAllDataList);\n    const setIsAllDataList = (data: boolean): void => {\n        isAllDataListRef.current = data;\n        _setIsAllDataList(data);\n    };\n    const [isShowCollect, _setIsShowCollect] = useState(false)\n    const isShowCollectRef = useRef(isShowCollect);\n    const setIsShowCollect = (data: boolean): void => {\n        isShowCollectRef.current = data;\n        _setIsShowCollect(data);\n    };\n    const [isEchartShow, setIsEchartShow] = useState(false)\n    const [pageSize, setPageSize] = useState(PAGE_SIZE)\n    const [listTitle, setListTitle] = useState<string>()\n\n    const { t, i18n } = useTranslation();\n\n    const [scrollY, setScrollY] = useState(\"\")\n\n    const fetchDataParams = {\n        pageConf: pageInfoInit,\n        params: [],\n        paramsMap: filterParamsMap,\n        sorter: undefined\n    }\n\n    useEffect(() => {\n\n    }, [pageSize])\n\n    useEffect(() => {\n        setScrollY(getTableScroll())\n    }, [])\n\n    useEffect(() => {\n        if (props && props.disable) {\n            navigate('/404')\n        }\n    }, [])\n\n    const createDyFormConfig = (data: Record<string, any>[], label_columns: Record<string, any>, description_columns: Record<string, any>): IDynamicFormConfigItem[] => {\n        return data.map((item, index) => {\n            let type = item['ui-type'] || 'input'\n            if (type === 'select2') {\n                type = 'select'\n            }\n            if (type === 'file') {\n                type = 'fileUpload'\n            }\n            const label = item.label || label_columns[item.name]\n\n            // 校验规则\n            const rules = (item.validators || []).map((item: any) => {\n                if (type === 'select') {\n                    return item.type === 'DataRequired' ? { required: true, message: `${t('请选择')} ${label}` } : undefined\n                }\n\n                switch (item.type) {\n                    case 'DataRequired':\n                        return { required: true, message: `${t('请输入')} ${label}` }\n                    case 'Regexp':\n                        return { pattern: new RegExp(`${item.regex}`), message: `${t('请按正确的规则输入')}` }\n                    case 'Length':\n                        return { min: item.min || 0, max: item.max, message: `${t('请输入正确的长度')}` }\n                    default:\n                        return undefined\n                }\n            }).filter((item: any) => !!item)\n\n            const list = createDyFormConfig((item.info || []), label_columns, description_columns)\n\n            const res: IDynamicFormConfigItem = {\n                label,\n                type,\n                rules,\n                list,\n                name: item.name,\n                disable: item.disable,\n                description: item.description || description_columns[item.name] || undefined,\n                required: item.required,\n                defaultValue: item.default === '' ? undefined : item.default,\n                multiple: item['ui-type'] && item['ui-type'] === 'select2',\n                options: (item.values || []).map((item: any) => ({ label: item.value, value: item.id, children: item.children })),\n                data: { ...item }\n            }\n            return res\n        })\n    }\n\n    useEffect(() => {\n        const targetId = getParam('targetId')\n        const url = targetId ? `/dimension_remote_table_modelview/${targetId}/api/` : props?.url\n        setLoadingAdd(true)\n\n        getADUGTemplateApiInfo(url).then(res => {\n            const {\n                list_columns,\n                label_columns,\n                filters,\n                add_columns,\n                edit_columns,\n                permissions,\n                description_columns,\n                add_fieldsets,\n                edit_fieldsets,\n                help_url,\n                order_columns,\n                action,\n                route_base,\n                column_related,\n                primary_key,\n                label_title,\n                cols_width,\n                import_data,\n                download_data,\n                list_ui_type,\n                list_ui_args,\n                ops_link,\n                enable_favorite,\n                echart,\n                page_size,\n                list_title\n            } = res.data\n            const actionwidth = 80 || [props?.related, permissions.includes('can_show'), permissions.includes('can_edit'), permissions.includes('can_delete')].filter(item => !!item).length * 60\n            const hasAction = props?.related || permissions.includes('can_show') || permissions.includes('can_edit') || permissions.includes('can_delete')\n            const cacheColumns = localStorage.getItem(`tablebox_${location.pathname}`)\n            const cacheColumnsWidthMap = (JSON.parse(cacheColumns || '[]')).reduce((pre: any, next: any) => ({ ...pre, [next.dataIndex]: next.width }), {});\n\n            const columnRelatedFormat: ILinkageConfig[] = Object.entries(column_related || {})\n                .reduce((pre: any[], [key, value]) => ([...pre, {\n                    dep: value.src_columns,\n                    effect: value.des_columns.join(''),\n                    effectOption: value.related.reduce((ePre: any, eNext) => ({ ...ePre, [calculateId(eNext.src_value)]: eNext.des_value.map(item => ({ label: item, value: item })) }), {})\n                }]), [])\n\n            const listColumns = list_columns.map(column => {\n                const columnTitle = label_columns[column] || column;\n                const [before, after] = columnTitle.split(\":\");\n\n                return {\n                    title: after?<Tooltip placement=\"top\" title={after}>{before}<InfoCircleOutlined /></Tooltip>:before,\n                    dataIndex: column,\n                    key: column,\n                    sorter: order_columns.includes(column) ? (a: any, b: any) => a[column] - b[column] : undefined,\n                    render: (text: any, record: any) => {\n                        if (text === undefined || text === '') {\n                            return '-'\n                        }\n                        if (isDomString(text)){\n                            const tempDiv = document.createElement('div');\n                            tempDiv.innerHTML = text;\n                            const topElement = tempDiv.firstElementChild;\n                            const typeValue = topElement?.getAttribute('type');\n                            const addedValue = topElement?.getAttribute('addedValue');\n\n                            console.log('type:', typeValue);\n                            console.log('addedValue:', addedValue);\n                            if (typeValue && addedValue){\n                                if (typeValue === 'tips'){\n                                    return <Tooltip title={<span className=\"tips-content\" dangerouslySetInnerHTML={{ __html: addedValue}}></span>} placement=\"topLeft\">\n                                      <div className={cols_width[column].type || 'ellip2'} dangerouslySetInnerHTML={{ __html: text }}>\n                                       </div>\n                                    </Tooltip>\n                                }\n                                if (typeValue === 'enhancedDetails'){\n                                    return <div onClick={()=>{setVisibleTabsModal(true);setEnhancedDetailsUrl(addedValue)}} dangerouslySetInnerHTML={{ __html: text }}></div>\n                                }\n                            }\n                        }\n\n                        if (cols_width[column] && cols_width[column].type?.indexOf('ellip') !== -1) {\n                            return <Tooltip title={<span className=\"tips-content\" dangerouslySetInnerHTML={{ __html: text }}></span>} placement=\"topLeft\">\n                                <div className={cols_width[column].type} dangerouslySetInnerHTML={{ __html: text }}>\n                                </div>\n                            </Tooltip>\n                        }\n                        if (Object.prototype.toString.call(text) === '[object Object]') {\n                            const tarRes = Object.entries(text).reduce((pre: any, [label, value]) => [...pre, { label, value }], [])\n                            if (!tarRes.length) {\n                                return '-'\n                            }\n                            return <div style={{ overflow: 'auto', maxHeight: 100 }}>\n                                {\n                                    tarRes.map((item: any, index: number) => {\n                                        return <div key={`table_itemvalue_${index}`}>{label_columns[item.label] || item.label}:{item.value}</div>\n                                    })\n                                }\n                            </div>\n                        }\n                        return <div style={{ overflow: 'auto', maxHeight: 100 }} dangerouslySetInnerHTML={{ __html: text }}></div>\n                    },\n                    width: cacheColumnsWidthMap[column] || (cols_width[column] && cols_width[column].width) || 100\n                }\n            })\n\n            const actionList = Object.entries(action || {}).reduce((pre: any, [name, value]) => ([...pre, { ...value }]), [])\n            const multipleAction: IADUGTemplateActionItem[] = actionList.filter((item: any) => !!item.multiple)\n            const singleAction: IADUGTemplateActionItem[] = actionList.filter((item: any) => !!item.single)\n\n            const tableAction: any = {\n                title: t('操作'),\n                width: actionwidth,\n                dataIndex: 'handle',\n                key: 'handle',\n                align: 'right',\n                fixed: 'right',\n                render: (text: any, record: any) => {\n                    return (\n                        <Space size=\"middle\">\n                            {\n                                hasAction ? <Dropdown overlay={<Menu>\n                                    {\n                                        isShowCollectRef.current && isAllDataListRef.current ? <Menu.Item><div className=\"link\" onClick={() => {\n                                            Modal.confirm({\n                                                title: t('收藏'),\n                                                icon: <ExclamationCircleOutlined />,\n                                                content: `${t('确定收藏')}?`,\n                                                okText: t('确认收藏'),\n                                                cancelText: t('取消'),\n                                                onOk() {\n                                                    return new Promise((resolve, reject) => {\n                                                        actionADUGTemplateFavorite(`${route_base}favorite/${record[primary_key]}`)\n                                                            .then((res) => {\n                                                                resolve('');\n                                                            })\n                                                            .catch((err) => {\n                                                                reject();\n                                                            });\n                                                    })\n                                                        .then((res) => {\n                                                            message.success(t('收藏成功'));\n                                                            fetchData({\n                                                                ...fetchDataParams,\n                                                                pageConf: pageInfo,\n                                                                params: filterValuesRef.current,\n                                                                paramsMap: filters\n                                                            });\n                                                        })\n                                                        .catch(() => {\n                                                            message.error(t('收藏失败'));\n                                                        });\n                                                },\n                                                onCancel() { },\n                                            });\n                                        }}>{t('收藏')}</div></Menu.Item> : null\n                                    }\n                                    {\n                                        isShowCollectRef.current && !isAllDataListRef.current ? <Menu.Item><div className=\"link\" onClick={() => {\n                                            Modal.confirm({\n                                                title: t('取消收藏'),\n                                                icon: <ExclamationCircleOutlined />,\n                                                content: `${t('确定取消收藏')}?`,\n                                                okText: t('确认取消收藏'),\n                                                cancelText: t('取消'),\n                                                onOk() {\n                                                    return new Promise((resolve, reject) => {\n                                                        actionADUGTemplateCancelFavorite(`${route_base}favorite/${record[primary_key]}`)\n                                                            .then((res) => {\n                                                                resolve('');\n                                                            })\n                                                            .catch((err) => {\n                                                                reject();\n                                                            });\n                                                    })\n                                                        .then((res) => {\n                                                            message.success(t('操作成功'));\n                                                            fetchData({\n                                                                ...fetchDataParams,\n                                                                pageConf: pageInfo,\n                                                                params: filterValuesRef.current,\n                                                                paramsMap: filters\n                                                            });\n                                                        })\n                                                        .catch(() => {\n                                                            message.error(t('操作失败'));\n                                                        });\n                                                },\n                                                onCancel() { },\n                                            });\n                                        }}>{t('取消收藏')}</div></Menu.Item> : null\n                                    }\n                                    {\n                                        permissions.includes('can_show') ? <Menu.Item><div className=\"link\" onClick={() => {\n                                            setVisableDetail(true)\n                                            fetchDataDetail(record[primary_key])\n                                        }}>\n                                            {t('详情')}\n                                        </div></Menu.Item> : null\n                                    }\n                                    {\n                                        permissions.includes('can_edit') ? <Menu.Item><div className=\"link\" onClick={() => {\n                                            setVisableUpdate(true)\n                                            getADUGTemplateApiInfo(route_base, record[primary_key]).then(res => {\n                                                const { edit_columns, label_columns, description_columns,edit_fieldsets } = res.data\n                                                const formConfigUpdate: IDynamicFormConfigItem[] = createDyFormConfig(edit_columns, label_columns, description_columns)\n                                                const updateColumnsMap = edit_columns.reduce((pre: any, next: any) => ({ ...pre, [next.name]: next }), {})\n                                                edit_columns.forEach((item) => {\n                                                    if (item['ui-type'] === 'list') {\n                                                        item.info.forEach((itemInfo: any) => {\n                                                            updateColumnsMap[itemInfo.name] = itemInfo\n                                                        })\n                                                    }\n                                                })\n                                                setUpdateColumnsMap(updateColumnsMap)\n                                                const formGroupConfigUpdate: IDynamicFormGroupConfigItem[] = edit_fieldsets.map(group => {\n                                                    const currentData = group.fields.map(field => updateColumnsMap[field]).filter(item => !!item)\n                                                    return {\n                                                        group: group.group,\n                                                        expanded: group.expanded,\n                                                        config: createDyFormConfig(currentData, label_columns, description_columns)\n                                                    }\n                                                })\n\n                                                setDynamicFormConfigUpdate(formConfigUpdate)\n                                                setDynamicFormGroupConfigUpdate(formGroupConfigUpdate)\n\n                                                fetchDataDetail(record[primary_key])\n                                            }).catch(() => {\n                                                message.warn(t('用户没有修改权限'))\n                                            })\n                                        }}>\n                                            {t('修改')}\n                                        </div></Menu.Item> : null\n                                    }\n                                    {\n                                        permissions.includes('can_delete') ? <Menu.Item><div className=\"c-fail cp\" onClick={() => {\n                                            Modal.confirm({\n                                                title: t('删除'),\n                                                icon: <ExclamationCircleOutlined />,\n                                                content: `${t('确定删除')}?`,\n                                                okText: t('确认删除'),\n                                                cancelText: t('取消'),\n                                                okButtonProps: { danger: true },\n                                                onOk() {\n                                                    return new Promise((resolve, reject) => {\n                                                        actionADUGTemplateDelete(`${route_base}${record[primary_key]}`)\n                                                            .then((res) => {\n                                                                resolve('');\n                                                            })\n                                                            .catch((err) => {\n                                                                reject();\n                                                            });\n                                                    })\n                                                        .then((res) => {\n                                                            message.success(t('删除成功'));\n                                                            fetchData({\n                                                                ...fetchDataParams,\n                                                                pageConf: pageInfo,\n                                                                params: filterValuesRef.current,\n                                                                paramsMap: filters\n                                                            });\n                                                        })\n                                                        .catch(() => {\n                                                            message.error(t('删除失败'));\n                                                        });\n                                                },\n                                                onCancel() { },\n                                            });\n                                        }}>\n                                            {t('删除')}\n                                        </div></Menu.Item> : null\n                                    }\n                                    {\n                                        props?.related?.map((item, index) => {\n                                            return <Menu.Item key={`moreAction_${index}`}>\n                                                <div className=\"link\" onClick={() => {\n                                                    navigate(`${location.pathname}/${item.name}?id=${record[primary_key]}`)\n                                                }}>\n                                                    {item.title}\n                                                </div>\n                                            </Menu.Item>\n                                        })\n                                    }\n                                    {\n                                        !!singleAction.length && singleAction.map((action, index) => {\n                                            return <Menu.Item key={`table_action_${index}`}><div className=\"link\" onClick={() => {\n                                                Modal.confirm({\n                                                    title: action.confirmation,\n                                                    icon: <ExclamationCircleOutlined />,\n                                                    content: '',\n                                                    okText: t('确认'),\n                                                    cancelText: t('取消'),\n                                                    onOk() {\n                                                        return new Promise((resolve, reject) => {\n                                                            actionADUGTemplateSingle(`${route_base}action/${action.name}/${record[primary_key]}`)\n                                                                .then((res) => {\n                                                                    resolve(res);\n                                                                })\n                                                                .catch((err) => {\n                                                                    reject(err);\n                                                                });\n                                                        })\n                                                            .then((res: any) => {\n                                                                message.success(t('操作成功'));\n\n                                                                if (res.data.result.link) {\n                                                                    window.open(res.data.result.link, 'bank')\n                                                                }\n                                                                fetchData({\n                                                                    ...fetchDataParams,\n                                                                    pageConf: pageInfo,\n                                                                    params: filterValuesRef.current,\n                                                                    paramsMap: filters\n                                                                });\n                                                            })\n                                                            .catch(() => {\n                                                                message.error(t('操作失败'));\n                                                            });\n                                                    },\n                                                    onCancel() { },\n                                                });\n                                            }}>\n                                                {t(`${action.text}`)}\n                                            </div></Menu.Item>\n                                        })\n                                    }\n                                </Menu>}>\n                                    <div className=\"link\">{t('更多')}<DownOutlined /></div>\n                                </Dropdown> : null\n                            }\n                        </Space>\n                    );\n                },\n            }\n            const tarColumns: React.SetStateAction<ColumnsType<any>> = [...listColumns]\n            if (hasAction) {\n                tarColumns.push(tableAction)\n            }\n\n            const addColumnsMap = add_columns.reduce((pre: any, next: any) => ({ ...pre, [next.name]: next }), {})\n            if (customFormData && Object.keys(customFormData).length) {\n                const reTryInfoQuene = (Object.keys(customFormData) || []).filter(key => customFormData[key] && addColumnsMap[key] && addColumnsMap[key].retry_info)\n                let reTryInfoFlag = reTryInfoQuene.length\n\n                const handleReTryInfo = (tar: string) => {\n                    reTryInfoFlag = reTryInfoFlag - 1;\n\n                    actionADUGTemplateRetryInfo(`${route_base}_info`, { exist_add_args: tar }).then(res => {\n                        const { add_columns, label_columns, description_columns, add_fieldsets } = res.data;\n                        const addColumnsMap = add_columns.reduce((pre: any, next: any) => ({ ...pre, [next.name]: next }), {})\n                        const formConfigAdd: IDynamicFormConfigItem[] = createDyFormConfig(add_columns, label_columns, description_columns)\n                        const formGroupConfigAdd: IDynamicFormGroupConfigItem[] = add_fieldsets.map(group => {\n                            const currentData = group.fields.map(field => addColumnsMap[field]).filter(item => !!item)\n                            return {\n                                group: group.group,\n                                expanded: group.expanded,\n                                config: createDyFormConfig(currentData, label_columns, description_columns)\n                            }\n                        })\n                        const formReset = add_columns.filter((item) => item.default !== '').map(column => ({ [column.name]: column.default })).reduce((pre, next) => ({ ...pre, ...next }), {})\n\n                        setDynamicFormDataAdd(formReset)\n                        setDynamicFormConfigAdd(formConfigAdd)\n                        setDynamicFormGroupConfigAdd(formGroupConfigAdd)\n\n                        if (reTryInfoFlag) {\n                            const resTar = JSON.stringify(formReset)\n                            handleReTryInfo(resTar)\n                        }\n                    }).catch(err => {\n                        message.error(t('字段切换错误'))\n                    }).finally(() => {\n                        setLoadingAdd(false)\n                    })\n                }\n\n                if (reTryInfoQuene.length) {\n                    const formRes = customFormData\n                    for (const key in formRes) {\n                        if (Object.prototype.hasOwnProperty.call(formRes, key)) {\n                            const value = formRes[key];\n                            if (value === undefined) {\n                                delete formRes[key]\n                            }\n                        }\n                    }\n                    const tar = JSON.stringify(formRes)\n\n                    handleReTryInfo(tar)\n                }\n            }\n\n            const updateColumnsMap = edit_columns.reduce((pre: any, next: any) => ({ ...pre, [next.name]: next }), {})\n            edit_columns.forEach((item) => {\n                if (item['ui-type'] === 'list') {\n                    item.info.forEach((itemInfo: any) => {\n                        updateColumnsMap[itemInfo.name] = itemInfo\n                    })\n                }\n            })\n            const formConfigAdd: IDynamicFormConfigItem[] = createDyFormConfig(add_columns, label_columns, description_columns)\n            const formGroupConfigAdd: IDynamicFormGroupConfigItem[] = add_fieldsets.map(group => {\n                const currentData = group.fields.map(field => addColumnsMap[field]).filter(item => !!item)\n                return {\n                    group: group.group,\n                    expanded: group.expanded,\n                    config: createDyFormConfig(currentData, label_columns, description_columns)\n                }\n            })\n\n            const tarFilter: IMixSearchParamItem[] = Object.entries(filters)\n                .reduce((pre: any, [name, value]) => {\n                    return [...pre, {\n                        name,\n                        type: value['ui-type'] || 'input',\n                        title: label_columns[name],\n                        oprList: value.filter.map(item => item.operator),\n                        defalutValue: value.default === '' ? undefined : value.default,\n                        option: value.values ? value.values.map(item => ({ label: item.value, value: item.id })) : undefined\n                    }]\n                }, [])\n\n            let currentFilterValues = Object.entries(filters)\n                .reduce((pre: any, [key, value]) => {\n                    return [...pre, {\n                        key,\n                        value: value.default\n                    }]\n                }, []).filter((item: any) => item.value)\n\n            const localCacheFilter = JSON.parse(localStorage.getItem(`filter_${location.pathname}${location.search}`) || '[]')\n            let urlFilter = undefined\n            if (getParam('filter')) {\n                try {\n                    urlFilter = JSON.parse(getParam('filter') || '[]')\n                } catch (error) {\n                    message.error(t('filter解析异常'))\n                }\n            }\n            const localFilter = urlFilter || localCacheFilter\n            if (localFilter && localFilter.length) {\n                currentFilterValues = localFilter\n            }\n\n            setListTitle(list_title)\n            setPageSize(page_size)\n            setIsEchartShow(echart)\n            setIsShowCollect(enable_favorite)\n            setOpsLink(ops_link)\n            setListColumns(list_columns)\n            setList_ui_type(list_ui_type)\n            setList_ui_args(list_ui_args)\n            setIsDownLoadData(download_data)\n            setIsImportData(import_data)\n            setLabelTitle(label_title)\n            setPrimaryKey(primary_key)\n            setColumnRelateFormat(columnRelatedFormat)\n            setMultipleAction(multipleAction)\n            setBaseUrl(route_base)\n            setUpdateColumnsMap(updateColumnsMap)\n            setFilterParamsMap(filters)\n            setCurrentColumns(tarColumns)\n            setFilterParams(tarFilter)\n            setDynamicFormConfigAdd(formConfigAdd)\n            setDynamicFormGroupConfigAdd(formGroupConfigAdd)\n\n            setLabelMap(label_columns)\n            setPermissions(permissions)\n            const currentTableWidth = cacheColumns ? tarColumns.reduce((pre: any, next: any) => pre + next.width || 100, 0) + 80 : tarColumns.length * 100 + 80 + actionwidth\n            setTableWidth(currentTableWidth)\n            setHelpUrl(help_url)\n            setFilterValues(currentFilterValues)\n\n            fetchData({\n                pageConf: {\n                    ...pageInfoInit,\n                    pageSize: page_size\n                },\n                params: currentFilterValues,\n                paramsMap: filters,\n                sorter: undefined\n            });\n\n        }).catch(err => {\n            console.log(err);\n        }).finally(() => {\n            setLoading(false)\n            setLoadingAdd(false)\n        })\n    }, []);\n\n    const formatFilterParams = (params: any[], paramsMap: Record<string, any>) => {\n        let formatData = undefined\n        const temlateId = getParam('id')\n\n        formatData = {\n            filters: [\n                temlateId ? {\n                    col: props?.model_name,\n                    opr: \"rel_o_m\",\n                    value: +temlateId\n                } : undefined,\n                ...params.filter(param => param.value !== undefined).map((param: any) => {\n                    let opr = ''\n                    const oprList = ['rel_o_m', 'ct', 'eq']\n                    const sourceOprList: string[] = paramsMap[param.key].filter.map((item: any) => item.operator) || []\n\n                    for (let i = 0; i < oprList.length; i++) {\n                        const currentOpr = oprList[i];\n                        if (sourceOprList.includes(currentOpr)) {\n                            opr = currentOpr\n                            break\n                        }\n                    }\n\n                    return {\n                        col: param.key,\n                        opr: opr,\n                        value: param.value\n                    }\n                })\n            ].filter(item => !!item),\n        }\n        return formatData\n    }\n\n    const fetchData = ({\n        pageConf,\n        params,\n        paramsMap,\n        sorter,\n        only_favorite\n    }: fatchDataParams = {\n            pageConf: pageInfoInit,\n            params: filterValues,\n            paramsMap: filterParamsMap,\n            sorter: undefined,\n            only_favorite: false\n        }) => {\n        setLoading(true);\n\n        const form_data = JSON.stringify({\n            ...formatFilterParams(params, paramsMap),\n            only_favorite,\n            str_related: 1,\n            page: (pageConf.current || 1) - 1,\n            page_size: pageConf.pageSize || 10,\n            ...sorter\n        })\n\n        getADUGTemplateList(baseUrlRef.current, {\n            form_data,\n        })\n            .then((res) => {\n                const { count, data } = res.data.result\n                setDataList(data);\n                setSelectedRowKeys([])\n                setPageInfo({ ...pageInfoInit, ...pageConf, total: count });\n                setSorterParam(sorter)\n            })\n            .catch((error) => {\n                console.log(error);\n            })\n            .finally(() => setLoading(false));\n    };\n\n    const fetchDataDetail = (id: string) => {\n        setLoadingDetail(true)\n        setDataDetail([])\n        getADUGTemplateDetail(`${baseUrlRef.current}${id}`)\n            .then(res => {\n                const data = res.data.result\n                const detail: any[] = []\n                const formatValue = (data: any) => {\n                    if (Object.prototype.toString.call(data) === '[object Object]') {\n                        return data.last_name\n                    }\n                    return data\n                }\n                Object.keys(data).forEach(key => {\n                    detail.push({\n                        label: labelMapRef.current[key] || key,\n                        value: formatValue(data[key]),\n                        key\n                    })\n                })\n                setDataDetail(detail)\n            })\n            .catch(err => { })\n            .finally(() => { setLoadingDetail(false) })\n    }\n\n    const handleMultiRecord = (action: IADUGTemplateActionItem) => {\n        if (selectedRowKeys.length) {\n            Modal.confirm({\n                title: action.confirmation,\n                icon: <ExclamationCircleOutlined />,\n                content: '',\n                okText: t('确认'),\n                cancelText: t('取消'),\n                onOk() {\n                    return new Promise((resolve, reject) => {\n                        actionADUGTemplateMuliple(`${baseUrlRef.current}multi_action/${action.name}`, {\n                            ids: selectedRowKeys.map((item: any) => JSON.parse(item || '{}')[primaryKey])\n                        })\n                            .then((res) => {\n                                resolve('');\n                            })\n                            .catch((err) => {\n                                reject();\n                            });\n                    })\n                        .then((res) => {\n                            message.success(t('操作成功'));\n                            fetchData({\n                                ...fetchDataParams,\n                                pageConf: pageInfo,\n                                params: filterValues,\n                                sorter: sorterParam,\n                                paramsMap: filterParamsMap\n                            });\n                        })\n                        .catch(() => {\n                            message.error(t('操作失败'));\n                        });\n                },\n                onCancel() { },\n            });\n        } else {\n            message.warn(t('请先选择'))\n        }\n    }\n\n    const uploadConfig: UploadProps = {\n        name: 'csv_file',\n        maxCount: 1,\n        action: `${baseUrl}upload/`,\n        headers: {\n            authorization: 'authorization-text',\n        },\n        beforeUpload: file => {\n            const isCSV = file.name.indexOf('.csv') !== -1;\n            const isXLS = file.name.indexOf('.xls') !== -1;\n            const isJson = file.name.indexOf('.json') !== -1;\n            const isXLSX = file.name.indexOf('.xlsx') !== -1;\n            if (isCSV || isJson || isXLS || isXLSX) {\n                return true\n            } else {\n                message.error(`文件格式支持CSV/JSON/XLS/XLSX`);\n            }\n        },\n        onChange(info) {\n            if (info.file.status === 'done') {\n                notification['success']({\n                    message: t('导入成功'),\n                    description: JSON.stringify(info.file.response),\n                });\n            } else if (info.file.status === 'error') {\n                notification['error']({\n                    message: t('导入失败'),\n                    description: JSON.stringify(info.file.response),\n                });\n            }\n        },\n    };\n\n    return (\n        <div className=\"fade-in h100 d-f fd-c\">\n            {/* 添加 */}\n            <ModalForm\n                title={`${t('添加')} ${labelTitle}`}\n                // width={1000}\n                formData={dynamicFormDataAdd}\n                loading={loadingAdd}\n                visible={visableAdd}\n                onCancel={() => { setVisableAdd(false) }}\n                onCreate={(values, form) => {\n                    setLoadingAdd(true)\n                    for (const key in values) {\n                        if (Object.prototype.hasOwnProperty.call(values, key)) {\n                            const value = values[key];\n                            if (Array.isArray(value)) {\n                                if (value[0] && Object.prototype.toString.call(value[0]) === '[object Object]') {\n                                    continue\n                                }\n                                values[key] = values[key].join(',')\n                            }\n                        }\n                    }\n                    actionADUGTemplateAdd(baseUrlRef.current, values).then((res: any) => {\n                        message.success(`${t('添加')} ${labelTitle} ${t('成功')}`)\n                        form.resetFields()\n                        setVisableAdd(false)\n                        fetchData({\n                            ...fetchDataParams,\n                            pageConf: pageInfo,\n                            params: filterValues,\n                            sorter: sorterParam,\n                            paramsMap: filterParamsMap\n                        });\n                    }).catch(err => {\n                        message.error(`${t('添加')} ${labelTitle} ${t('失败')}`)\n                    }).finally(() => {\n                        setLoadingAdd(false)\n                    })\n                }}\n            >\n                {\n                    (form: FormInstance, formChangeRes: any) => <DynamicForm form={form} onRetryInfoChange={(value) => {\n                        setLoadingAdd(true)\n\n                        const formRes = form.getFieldsValue()\n                        for (const key in formRes) {\n                            if (Object.prototype.hasOwnProperty.call(formRes, key)) {\n                                const value = formRes[key];\n                                if (value === undefined) {\n                                    delete formRes[key]\n                                }\n                            }\n                        }\n                        const tar = JSON.stringify(formRes)\n                        form.resetFields()\n\n                        actionADUGTemplateRetryInfo(`${baseUrlRef.current}_info`, { exist_add_args: tar }).then(res => {\n                            const { add_columns, label_columns, description_columns, add_fieldsets } = res.data;\n                            const addColumnsMap = add_columns.reduce((pre: any, next: any) => ({ ...pre, [next.name]: next }), {})\n                            const formConfigAdd: IDynamicFormConfigItem[] = createDyFormConfig(add_columns, label_columns, description_columns)\n                            const formGroupConfigAdd: IDynamicFormGroupConfigItem[] = add_fieldsets.map(group => {\n                                const currentData = group.fields.map(field => addColumnsMap[field]).filter(item => !!item)\n                                return {\n                                    group: group.group,\n                                    expanded: group.expanded,\n                                    config: createDyFormConfig(currentData, label_columns, description_columns)\n                                }\n                            })\n                            const formReset = add_columns.filter((item) => item.default !== '').map(column => ({ [column.name]: column.default })).reduce((pre, next) => ({ ...pre, ...next }), {})\n\n                            form.setFieldsValue(formReset)\n                            setDynamicFormConfigAdd(formConfigAdd)\n                            setDynamicFormGroupConfigAdd(formGroupConfigAdd)\n                        }).catch(err => {\n                            message.error(t('字段切换错误'))\n                        }).finally(() => {\n                            setLoadingAdd(false)\n                        })\n\n                    }} formChangeRes={formChangeRes} linkageConfig={columnRelateFormat} config={dynamicFormConfigAdd} configGroup={dynamicFormGroupConfigAdd} />\n                }\n            </ModalForm>\n            {/* 修改 */}\n            <ModalForm\n                title={`${t('修改')} ${labelTitle}`}\n                // width={1500}\n                formData={dataDetail.reduce((pre, next) => {\n                    if ((updateColumnsMap[next.key] || {})['ui-type'] === 'select') {\n                        let value = next.value\n                        const options = (updateColumnsMap[next.key] || {}).values || []\n                        const tarIndex = options.map((item: any) => item.value).indexOf(next.value)\n                        if (~tarIndex) {\n                            value = options[tarIndex].id\n                        }\n                        return { ...pre, [next.key]: value }\n                    }\n                    if ((updateColumnsMap[next.key] || {})['ui-type'] === 'select2') {\n                        let processedValue;\n                        if(Array.isArray(next.value)){\n                            processedValue = next.value.map((item: any) => {\n                                    if (typeof item === 'object' && item !== null && 'id' in item) {\n                                        return item.id;\n                                    } else {\n                                        return item;\n                                    }\n                                }\n                            )\n                        } else {\n                            processedValue = (next.value || '').split(',');\n                          }\n                        return { ...pre, [next.key]: processedValue }\n                    }\n\n                    if ((updateColumnsMap[next.key] || {})['ui-type'] === 'datePicker') {\n                        let value = next.value;\n                        value = moment(value)\n                        return { ...pre, [next.key]: value }\n                    }\n\n                    if ((updateColumnsMap[next.key] || {})['ui-type'] === 'list') {\n                        const value = (next.value || []).map((item: any) => {\n                            for (const listItemKey in item) {\n                                if (Object.prototype.hasOwnProperty.call(item, listItemKey)) {\n                                    const listItemValue = item[listItemKey];\n                                    if ((updateColumnsMap[listItemKey] || {})['ui-type'] === 'datePicker') {\n                                        item[listItemKey] = moment(listItemValue)\n                                    }\n                                }\n                            }\n                            return item\n                        })\n                        return { ...pre, [next.key]: value }\n                    }\n\n                    return { ...pre, [next.key]: next.value }\n                }, {})}\n                loading={loadingUpdate || loadingDetail}\n                visible={visableUpdate}\n                onCancel={() => { setVisableUpdate(false) }}\n                onCreate={(values) => {\n                    setLoadingUpdate(true)\n                    setDataDetail([])\n                    for (const key in values) {\n                        if (Object.prototype.hasOwnProperty.call(values, key)) {\n                            const value = values[key];\n                            if (Array.isArray(value)) {\n                                if (value[0] && Object.prototype.toString.call(value[0]) === '[object Object]') {\n                                    continue\n                                }\n                                values[key] = values[key].join(',')\n                            }\n                        }\n                    }\n                    actionADUGTemplateUpdate(`${baseUrlRef.current}${values[primaryKey]}`, values)\n                        .then(res => {\n                            message.success(`${t('更新')} ${labelTitle} ${t('成功')}`)\n                            setVisableUpdate(false)\n                            fetchData({\n                                ...fetchDataParams,\n                                pageConf: pageInfo,\n                                params: filterValues,\n                                sorter: sorterParam,\n                                paramsMap: filterParamsMap\n                            });\n                        })\n                        .catch(err => {\n                            message.error(`${t('更新')} ${labelTitle} ${t('失败')}`)\n                        })\n                        .finally(() => { setLoadingUpdate(false) })\n                }}\n            >\n                {\n                    (form: FormInstance) => <DynamicForm form={form} primaryKey={primaryKey} config={dynamicFormConfigUpdate} linkageConfig={columnRelateFormat} configGroup={dynamicFormGroupConfigUpdate} />\n                }\n            </ModalForm>\n            {/* 详情 */}\n            <Modal\n                title={`${labelTitle} ${t('详情')}`}\n                visible={visableDetail}\n                footer={null}\n                width={800}\n                destroyOnClose\n                onCancel={() => { setVisableDetail(false) }}>\n                <Spin spinning={loadingDetail}>\n                    <div className=\"pb8\"\n                        style={{\n                            paddingBottom: '8px',\n                            minHeight: '400px',\n                            maxWidth: '70vw',\n                            margin: '0 auto',\n                            overflowX: 'auto',\n                        }}\n                    >\n                    {dataDetail.map((item, index) => (\n                        <div\n                        key={`dataDetail_${index}`}\n                        style={{\n                            display: 'flex',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        }}\n                        >\n                        {/* Label列 */}\n                        <div\n                            style={{\n                            display: 'flex',\n                            justifyContent: 'right',\n                            width:'130px'\n                            }}\n                        >\n                            <strong>{item.label}：</strong>\n                        </div>\n\n                        {/* Value列 */}\n                        <div\n                            style={{\n                            flex: '1',\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            }}\n                        >\n                            <pre\n                            style={{\n                                whiteSpace: 'pre-wrap',\n                                wordBreak: 'break-word',\n                                overflowX: 'auto',\n                                margin: 0,\n                            }}\n                            dangerouslySetInnerHTML={{\n                                __html: (() => {\n                                let content = item.value;\n                                if (\n                                    Object.prototype.toString.call(item.value) === '[object Object]' ||\n                                    Object.prototype.toString.call(item.value) === '[object Array]'\n                                ) {\n                                    try {\n                                    content = JSON.stringify(item.value, null, 2); // 格式化输出\n                                    } catch (error) {}\n                                }\n                                return content;\n                                })(),\n                            }}\n                            ></pre>\n                        </div>\n                        </div>\n                    ))}\n                    </div>\n                </Spin>\n            </Modal>\n            {/* tabs详情 */}\n            <TabsModal visible={visibleTabsModal} url={enhancedDetailsUrl} onVisibilityChange={setVisibleTabsModal}/>\n            <TitleHeader title={<>\n                {\n                    (props?.isSubRoute) ? <Button className=\"mr16\" onClick={() => {\n                        window.history.back();\n                    }}><RollbackOutlined />{t('返回')}</Button> : null\n                }\n                <span>{labelTitle}</span>\n            </>} breadcrumbs={(props?.breadcrumbs || []).map((crumbs, idx) => {\n                return <span key={`templateTitle_${props?.name}_${idx}`} className=\"c-icon-b fs12\">/<span className=\"plr2\">{crumbs}</span></span>\n            })} >\n                {\n                    helpUrl ? <div className=\"link\"><span className=\"pr4\" onClick={() => {\n                        window.open(helpUrl, 'blank')\n                    }}>{t('帮助链接')}</span><QuestionCircleOutlined /></div> : null\n                }\n            </TitleHeader>\n            <Content className=\"appmgmt-content bg-title h100 d-f fd-c\">\n                <div className=\"mlr16 mb16 flex1 bg-w\">\n                    {\n                        !!filterParams.length && <MixSearch values={filterValues} params={filterParams} onChange={(values) => {\n                            localStorage.setItem(`filter_${location.pathname}${location.search}`, JSON.stringify(values))\n                            setFilterValues(values)\n                            fetchData({\n                                ...fetchDataParams,\n                                pageConf: pageInfoInit,\n                                params: values,\n                                sorter: sorterParam,\n                                paramsMap: filterParamsMap\n                            });\n                        }} />\n                    }\n\n                    {\n                        isEchartShow ? <ChartOptionTempalte url={baseUrl} /> : null\n                    }\n\n                    {\n                        list_ui_type !== 'card' ? <TableBox\n                            cancelExportData={true}\n                            tableKey={`tablebox_${location.pathname}`}\n                            titleNode={<Col className=\"tablebox-title\">\n                                <div className=\"d-f ac\">\n                                    <div className=\"mr8\">{listTitle || ''}</div>\n                                    {\n                                        isShowCollect ? <div className=\"pb2\">\n                                            <Switch checked={isAllDataList} checkedChildren={t('全部')} unCheckedChildren={t('我的收藏')} defaultChecked onChange={(checked) => {\n                                                setIsAllDataList(checked)\n                                                fetchData({\n                                                    ...fetchDataParams,\n                                                    pageConf: pageInfoInit,\n                                                    params: filterValues,\n                                                    sorter: sorterParam,\n                                                    paramsMap: filterParamsMap,\n                                                    only_favorite: !checked\n                                                });\n                                            }} />\n                                        </div> : null\n                                    }\n                                </div>\n                            </Col>}\n                            buttonNode={<div className=\"d-f ac\">\n                                {\n                                    opsLink && opsLink.length ? opsLink.map(config => {\n                                        return <Button type=\"primary\" className=\"mr16\" onClick={() => {\n                                            window.open(config.url, 'bank')\n                                        }}>{config.text}</Button>\n                                    }) : null\n                                }\n\n                                {\n                                    permissions.includes('can_add') ? <Button className=\"mr16\" type=\"primary\" onClick={() => setVisableAdd(true)}>{t('添加')}{labelTitle}<PlusOutlined /></Button> : null\n                                }\n                                {\n                                    multipleAction && multipleAction.length ? <div>\n                                        <Dropdown overlay={<Menu>\n                                            {\n                                                multipleAction.map((action, index) => {\n                                                    return <Menu.Item key={`table_muliple_${index}`}>\n                                                        <span className=\"link\" onClick={() => handleMultiRecord(action)}>\n                                                            {`${t('批量')} ${action.text}`}\n                                                        </span>\n                                                    </Menu.Item>\n                                                })\n                                            }\n\n                                        </Menu>}>\n                                            <Button>{t('批量操作')} <DownOutlined /></Button>\n                                        </Dropdown>\n                                    </div> : null\n                                }\n                                {\n                                    isImportData ? <div className=\"d-f ml16\">\n                                        <Tooltip color=\"#fff\" title={<span className=\"tips-content-b\"><div>{t('注意：csv逗号分隔')}，</div><div>{t('第一行为列的英文名')}</div> <div className=\"link\" onClick={() => {\n                                            window.open(`${window.location.origin}${baseUrlRef.current}download_template`)\n                                        }}>{(t('下载导入模板'))}</div></span>} placement=\"topLeft\">\n                                            <Upload {...uploadConfig}>\n                                                <Button className=\"\" icon={<UploadOutlined />}>{t('批量导入数据')}</Button>\n                                            </Upload>\n                                        </Tooltip>\n                                    </div> : null\n                                }\n                                {\n                                    isDownLoadData ? <Button className=\"ml16\" onClick={() => {\n                                        Modal.confirm({\n                                            title: t('导出数据'),\n                                            icon: <ExclamationCircleOutlined />,\n                                            content: '',\n                                            okText: t('确认导出数据'),\n                                            cancelText: t('取消'),\n                                            onOk() {\n                                                const formatData = formatFilterParams(filterValues, filterParamsMap)\n                                                const form_data = JSON.stringify(formatData)\n                                                window.open(`${window.location.origin}${baseUrlRef.current}download?form_data=${form_data}`)\n                                                message.success(t('导出成功'));\n                                            },\n                                            onCancel() { },\n                                        });\n                                    }}>{t('批量导出')}  <ExportOutlined /></Button> : null\n                                }\n\n                            </div>}\n                            rowKey={(record: any) => {\n                                return JSON.stringify(record)\n                            }}\n                            columns={currentColumns}\n                            loading={loading}\n                            pagination={pageInfo}\n                            dataSource={dataList}\n                            onChange={(pageInfo: any, filters, sorter: any) => {\n                                const tarSorter = sorter.order ? {\n                                    order_column: sorter.columnKey,\n                                    order_direction: sorter.order === \"ascend\" ? 'asc' : 'desc'\n                                } as ISorterParam : undefined\n\n                                fetchData({\n                                    ...fetchDataParams,\n                                    pageConf: pageInfo,\n                                    params: filterValues,\n                                    paramsMap: filterParamsMap,\n                                    sorter: tarSorter\n                                });\n                            }}\n                            rowSelection={{\n                                type: 'checkbox',\n                                fixed: 'left',\n                                columnWidth: 32,\n                                selectedRowKeys,\n                                onChange: (selectedRowKeys) => {\n                                    setSelectedRowKeys(selectedRowKeys)\n                                }\n                            }}\n                            scroll={{ x: tableWidth, y: scrollY }}\n                        /> : <div className=\"bg-w p16\">\n                            <div className=\"d-f fw\">\n                                {\n                                    dataList.map((row, rowIndex) => {\n                                        return <div style={{ overflowY: 'auto', width: list_ui_args?.card_width, height: list_ui_args?.card_height }} key={`card${rowIndex}`} className=\"card-row mr16 mb16\" >\n                                            {\n                                                Object.keys(row).map((key, itemIndex) => {\n                                                    if (listColumns.includes(key)) {\n                                                        return <div style={{ wordBreak: 'break-all' }} key={`row${rowIndex}${itemIndex}`} dangerouslySetInnerHTML={{ __html: row[key] }}></div>\n                                                    }\n                                                    return null\n                                                })\n                                            }\n                                        </div>\n                                    })\n                                }\n                            </div>\n                            <div className=\"ta-r\">\n                                <Pagination {...pageInfo} onChange={(page, pageSize) => {\n                                    fetchData({\n                                        ...fetchDataParams,\n                                        pageConf: {\n                                            ...pageInfo,\n                                            current: page,\n                                            pageSize\n                                        },\n                                        params: filterValues,\n                                        paramsMap: filterParamsMap,\n                                    });\n                                }} />\n                            </div>\n                        </div>\n                    }\n                </div>\n            </Content>\n        </div >\n    );\n}\n\n", "import React, { useEffect, useState } from 'react'\nimport { actionADUGTemplateChartOption } from '../api/kubeflowApi'\nimport EchartCore, { ECOption } from '../components/EchartCore/EchartCore'\n\ninterface IProps {\n    url?: string\n}\n\nexport default function ChartOptionTempalte(props: IProps) {\n    const [option, setOption] = useState<ECOption>({})\n    const [loading, setLoading] = useState(true)\n\n    useEffect(() => {\n        if (props.url) {\n            actionADUGTemplateChartOption(`${props.url}echart`, {}).then(res => {\n                const option = res.data.result\n                var currentOps: any = {}\n                eval(`currentOps=${option}`)\n                setOption(currentOps)\n            }).catch(err => { }).finally(() => {\n                setLoading(false)\n            })\n        }\n    }, [props.url])\n\n    return (\n        <EchartCore option={option} loading={loading} />\n    )\n}\n"], "names": ["defaultChartStyle", "height", "EchartCore", "props", "useState", "chartInstance", "setChartInstance", "id", "Math", "random", "toString", "substring", "useTranslation", "t", "option", "i18n", "useEffect", "chartDom", "document", "getElementById", "chart", "echarts", "setOption", "data", "spinning", "loading", "className", "style", "isNoData", "InputSearch", "inputRef", "options", "dataCache", "setDataCache", "value", "setValue", "dataFilter", "isOpenSearchMatch", "filter", "item", "indexOf", "handleChange", "onChange", "highlight<PERSON>ey<PERSON>ord", "key<PERSON>ord", "index", "preStr", "nextStr", "length", "debounceScroll", "fun", "timer", "time", "args", "clearTimeout", "setTimeout", "apply", "debounce", "onScrollButtom", "labelName", "htmlFor", "width", "disabled", "placeholder", "max<PERSON><PERSON><PERSON>", "e", "target", "onKeyPress", "nativeEvent", "keyCode", "blur", "onSearch", "currentTarget", "ref", "element", "SearchOutlined", "onScroll", "stopPropagation", "scrollTop", "clientHeight", "scrollHeight", "maxHeight", "right", "top", "map", "onMouseDown", "onClick", "handleClick", "CopyToClipboard", "require", "ResizableTitle", "onResize", "restProps", "Resizable", "handle", "draggableOpts", "enableUserSelectHack", "userSelect", "exportDataVisible", "setExportDataVisible", "header", "dataFormat", "setDataFormat", "filterValue", "setFilterValue", "columns", "cols", "setCols", "handleResize", "_", "size", "temp", "tableWidth", "reduce", "pre", "next", "localStorage", "setItem", "table<PERSON><PERSON>", "JSON", "stringify", "setCurrentTableScroll", "currentTableScroll", "x", "customColumns", "col", "onHeaderCell", "column", "scroll", "dataSource", "dataIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataColumns", "sheetHeader", "title", "tarData", "for<PERSON>ach", "dataRow", "row", "colName", "res", "push", "handleExportJira", "str", "join", "<PERSON><PERSON><PERSON>", "Object", "values", "handleExportText", "direction", "maskClosable", "centered", "bodyStyle", "overflow", "visible", "onCancel", "footer", "position", "label", "defaultValue", "bottom", "type", "tab", "text", "onCopy", "message", "cursor", "minHeight", "titleNode", "buttonNode", "cancelExportData", "justify", "align", "marginLeft", "renderEmpty", "flexDirection", "src", "emptyImg", "alt", "components", "cell", "pagination", "rowSelection", "TabsDetail", "markdownMap", "setMarkdownMap", "renderMarkdown", "group", "marked", "html", "prev", "content", "groupContent", "tabName", "groupName", "handleGroupContent", "renderMapComponent", "renderEchart", "renderMapText", "renderMapIframe", "renderHtml", "wordBreak", "whiteSpace", "dangerouslySetInnerHTML", "__html", "dataList", "entries", "key", "val", "span", "currentOps", "eval", "url", "allowFullScreen", "allow", "border", "markdownHtml", "tabIndex", "groupIndex", "borderLeftWidth", "bottomButton", "button", "window", "open", "icon", "Title", "Typography", "styles", "noBorderBottom", "borderBottom", "level", "marginBottom", "breadcrumbs", "children", "Form", "form", "updateState", "React", "formData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentChange", "allValues", "formChangeRes", "setFormChangeRes", "destroyOnClose", "okText", "cancelText", "resetFields", "onOk", "console", "log", "getFieldsValue", "validateFields", "then", "onCreate", "catch", "info", "onValuesChange", "labelCol", "wrapperCol", "layout", "name", "prototype", "call", "Option", "Select", "collapsed", "setCollapsed", "formatParamsData", "indexKey", "params", "paramsData", "setParamsData", "currentParamsData", "setCurrentParamsData", "Map", "paramsDataMap", "setParamsDataMap", "undefined", "i", "j", "used", "formatData", "dataMap", "param", "set", "handleFinishForm", "preVal", "tarVal", "handleRenderValueEl", "getFieldValue", "onPressEnter", "handlePressEnter", "currentItem", "get", "defalutValue", "placeHolder", "currentOptions", "dropdownMatchSelectWidth", "showSearch", "mode", "optionFilterProp", "onFinish", "initialValues", "gutter", "marginRight", "fields", "add", "remove", "field", "compact", "noStyle", "rules", "required", "usedKey", "includes", "selectActionRemove", "display", "shouldUpdate", "DeleteOutlined", "PlusOutlined", "flex", "htmlType", "DownOutlined", "UpOutlined", "FileUploadPlus", "setFileLoading", "fileList", "setFileList", "createMediaPreview", "file", "fileIndex", "filetype", "createObjectURL", "URL", "webkitURL", "getObjectURL", "currentFileList", "splice", "borderBottomLeftRadius", "zIndex", "color", "controls", "borderRadius", "showUploadList", "customRequest", "tarList", "Promise", "all", "resolve", "reject", "replace", "reader", "FileReader", "readAsDataURL", "onload", "result", "beforeUpload", "maxCount", "isFormatOk", "format", "isLt2M", "maxSize", "status", "InboxOutlined", "VideoCameraAddOutlined", "AudioOutlined", "JsonEditor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "code", "parsed", "parse", "formatJson", "formattedJson", "handleEditorChange", "readOnly", "extensions", "json", "Editor<PERSON><PERSON><PERSON>", "basicSetup", "lineNumbers", "marginTop", "calculateId", "strList", "split", "charCodeAt", "DynamicForm", "dataOptions", "current", "setCurrent", "config", "currentConfig", "_setCurrentConfig", "currentConfigRef", "useRef", "setCurrentConfig", "configGroup", "currentConfigGroup", "_setCurrentConfigGroup", "currentConfigGroupRef", "setCurrentConfigGroup", "resetFieldProps", "linkageConfig", "optionInlinkAge", "configItem", "dep", "valueId", "effect", "effectOption", "findOptionInLinkAge", "tarConfig", "setValueInConfig", "tarConfigGroup", "configList", "setValueInConfigGroup", "keys", "formValues", "renderInput", "itemProps", "initialValue", "extra", "tips", "placement", "QuestionCircleOutlined", "description", "disable", "dispatchRenderFormItem", "retry_info", "onRetryInfoChange", "renderInputSelect", "renderCascader", "autoSize", "minRows", "renderTextArea", "renderJsonEditor", "isRefresh", "SyncOutlined", "multiple", "renderSelect", "locale", "showTime", "disabledDate", "moment", "endOf", "renderDatePicker", "renderRangePicker", "renderRadio", "renderFileUpload", "renderFormItem", "list", "restField", "alignItems", "min<PERSON><PERSON><PERSON>", "listItem", "labelAlign", "danger", "block", "MinusCircleOutlined", "<PERSON><PERSON><PERSON>", "hidden", "Step", "currentConfigGroupNameList", "err", "TabsModal", "setLoading", "setData", "fatchData", "actionTabsModalInfo", "error", "finally", "onVisibilityChange", "actionTabsModal", "method", "arg", "TaskListManager", "navigate", "useNavigate", "location", "useLocation", "setDataList", "loadingAdd", "setLoadingAdd", "getPara<PERSON>", "visableAdd", "setVisableAdd", "loadingUpdate", "setLoadingUpdate", "visableUpdate", "setVisableUpdate", "visibleTabsModal", "setVisibleTabsModal", "enhancedDetailsUrl", "setEnhancedDetailsUrl", "loadingDetail", "setLoadingDetail", "visableDetail", "setVisableDetail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "pageInfoInit", "pageSize", "total", "showSizeChanger", "showQuickJumper", "pageSizeOptions", "showTotal", "pageInfo", "setPageInfo", "currentColumns", "setCurrentColumns", "filterParams", "setFilterParams", "filterValues", "_setFilter<PERSON><PERSON>ues", "filterValuesRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicFormConfigAdd", "setDynamicFormConfigAdd", "dynamicFormConfigUpdate", "setDynamicFormConfigUpdate", "dynamicFormGroupConfigAdd", "setDynamicFormGroupConfigAdd", "dynamicFormGroupConfigUpdate", "setDynamicFormGroupConfigUpdate", "customFormData", "dynamicFormDataAdd", "setDynamicFormDataAdd", "updateColumnsMap", "setUpdateColumnsMap", "labelMap", "_setLabelMap", "labelMapRef", "dataDetail", "setDataDetail", "setTableWidth", "permissions", "setPermissions", "filterParamsMap", "setFilterParamsMap", "helpUrl", "setHelpUrl", "baseUrl", "_setBaseUrl", "baseUrlRef", "isImportData", "setIsImportData", "isDownLoadData", "setIsDownLoadData", "columnRelateFormat", "setColumnRelateFormat", "multipleAction", "setMultipleAction", "sorterParam", "setSorterParam", "setPrimaryKey", "labelTitle", "setLabelTitle", "list_ui_type", "setList_ui_type", "list_ui_args", "setList_ui_args", "opsLink", "setOpsLink", "listColumns", "setListColumns", "isAllDataList", "_setIsAllDataList", "isAllDataListRef", "isShowCollect", "_setIsShowCollect", "isShowCollectRef", "isEchartShow", "setIsEchartShow", "setPageSize", "listTitle", "setListTitle", "scrollY", "setScrollY", "fetchDataParams", "pageConf", "paramsMap", "sorter", "getTableScroll", "createDyFormConfig", "label_columns", "description_columns", "validators", "pattern", "RegExp", "regex", "min", "max", "default", "targetId", "getADUGTemplateApiInfo", "list_columns", "filters", "add_columns", "edit_columns", "add_fieldsets", "help_url", "edit_fieldsets", "order_columns", "action", "route_base", "column_related", "primary_key", "label_title", "cols_width", "import_data", "download_data", "ops_link", "enable_favorite", "echart", "page_size", "list_title", "hasAction", "related", "cacheColumns", "getItem", "pathname", "cacheColumnsWidthMap", "columnRelatedFormat", "src_columns", "des_columns", "ePre", "eNext", "src_value", "des_value", "before", "after", "InfoCircleOutlined", "a", "b", "render", "record", "isDomString", "tempDiv", "createElement", "innerHTML", "topElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "typeValue", "getAttribute", "addedValue", "tarRes", "actionList", "singleAction", "single", "tableAction", "fixed", "overlay", "Modal", "ExclamationCircleOutlined", "actionADUGTemplateFavorite", "fetchData", "actionADUGTemplateCancelFavorite", "fetchDataDetail", "formConfigUpdate", "itemInfo", "formGroupConfigUpdate", "currentData", "expanded", "okButtonProps", "actionADUGTemplateDelete", "confirmation", "actionADUGTemplateSingle", "link", "tarColumns", "addColumnsMap", "reTryInfoQuene", "reTryInfoFlag", "formRes", "hasOwnProperty", "handleReTryInfo", "tar", "actionADUGTemplateRetryInfo", "exist_add_args", "formConfigAdd", "formGroupConfigAdd", "formReset", "resTar", "tarFilter", "oprList", "operator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "localCacheFilter", "search", "url<PERSON><PERSON><PERSON>", "localFilter", "setBaseUrl", "setLabelMap", "currentTableWidth", "formatFilterParams", "temlateId", "model_name", "opr", "sourceOprList", "currentOpr", "only_favorite", "form_data", "str_related", "page", "getADUGTemplateList", "count", "getADUGTemplateDetail", "detail", "formatValue", "last_name", "uploadConfig", "headers", "authorization", "isCSV", "isXLS", "isJson", "isXLSX", "notification", "response", "Array", "isArray", "actionADUGTemplateAdd", "tarIndex", "processedValue", "listItemKey", "listItemValue", "actionADUGTemplateUpdate", "paddingBottom", "max<PERSON><PERSON><PERSON>", "margin", "overflowX", "gap", "justifyContent", "isSubRoute", "history", "back", "RollbackOutlined", "crumbs", "idx", "ChartOptionTempalte", "TableBox", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "defaultChecked", "actionADUGTemplateMuliple", "ids", "handleMultiRecord", "origin", "UploadOutlined", "ExportOutlined", "tarSorter", "order", "order_column", "column<PERSON>ey", "order_direction", "columnWidth", "y", "rowIndex", "overflowY", "card_width", "card_height", "itemIndex", "actionADUGTemplateChartOption"], "sourceRoot": ""}