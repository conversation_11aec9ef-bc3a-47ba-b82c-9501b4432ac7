"use strict";(self.webpackChunkvite_ml_platform=self.webpackChunkvite_ml_platform||[]).push([[376],{87376:function(e,n,t){t.r(n),t.d(n,{default:function(){return wn}});var o=t(72791),i=t(59332),l=t(29439),r=t(29093),a=t(18511),s=t(53742),d=t(77291),c=t(70927),u=t(81318),p=t(15853),f=t(16030),x=function(){return(0,f.I0)()},m=f.v9,h=t(16097);function g(e,n){var t=null;return function(){for(var o=this,i=arguments.length,l=new Array(i),r=0;r<i;r++)l[r]=arguments[r];clearTimeout(t),t=window.setTimeout((function(){return e.apply(o,l)}),n)}}var v=t(42110),b={modalStyles:(0,v.y0)({position:"fixed",width:"100%",height:"100vh",backgroundColor:"rgba(0, 0, 0, .5)",zIndex:999,display:"flex",justifyContent:"center",alignItems:"center"}),cancelIcon:{iconName:"Cancel"},contentStyles:(0,v.ZC)({container:{minWidth:"75%",minHeight:"80%",overflow:"hidden",display:"flex",borderRadius:2,backgroundColor:"#fff",padding:"0 24px"},header:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"10px 0 14px 0"},body:{flex:1,height:0,position:"relative",display:"flex"},footer:{position:"relative",display:"flex",padding:"24px 0",justifyContent:"flex-end"}}),resizeLine:(0,v.y0)({width:8,height:"100%",cursor:"col-resize",background:"#eee",transition:"background 0.2s",margin:"0 15px 0 2px",display:"flex",alignItems:"center",justifyContent:"center",overflow:"hidden"}),resizeLineIconStyles:(0,v.y0)({display:"flex",justifyContent:"center",alignItems:"center",boxSizing:"border-box",paddingRight:"19px",width:8,height:20,fontSize:20,pointerEvents:"none"})},y=t(39230),j=t(80184),w=function(e){var n=x(),t=m(h.ld),f=m(h.PF),g=m(h.lr),v=(0,o.useState)(""),w=(0,l.Z)(v,2),S=w[0],C=w[1],k=(0,o.useState)("100%"),N=(0,l.Z)(k,2),_=N[0],Z=N[1],L=(0,o.useState)(!1),I=(0,l.Z)(L,2),O=I[0],D=I[1],z=(0,o.useState)(!1),T=(0,l.Z)(z,2),B=T[0],H=T[1],R=(0,y.$G)(),E=R.t,M=(R.i18n,function(){H(!1),n((0,h.y7)(!1))}),W=function(){D(!1)};return(0,o.useEffect)((function(){C(f)}),[t]),(0,j.jsx)("div",{className:b.modalStyles,style:{display:t?"flex":"none"},children:(0,j.jsxs)(i.K,{className:b.contentStyles.container,children:[(0,j.jsxs)("div",{className:b.contentStyles.header,children:[(0,j.jsx)("span",{children:E("\u7f16\u8f91")}),(0,j.jsx)(r.h,{iconProps:b.cancelIcon,onClick:M})]}),(0,j.jsxs)("div",{className:b.contentStyles.body,onMouseMove:function(e){O&&e.clientX>370&&Z(e.clientX-270)},onMouseUp:W,onMouseLeave:W,children:[(0,j.jsx)("div",{style:{height:"100%",width:_},children:(0,j.jsx)(p.ZP,{language:g||"json",theme:"vs",value:S,onChange:function(e){H(!1),C(e)},height:500,options:{renderValidationDecorations:"on",automaticLayout:!0,autoClosingOvertype:"always",cursorStyle:"block",quickSuggestions:!1,scrollBeyondLastLine:!1,snippetSuggestions:"none",minimap:{enabled:!1}}})}),(0,j.jsx)("div",{className:b.resizeLine,onMouseDown:function(){D(!0)},children:(0,j.jsx)(a.J,{iconName:"BulletedListBulletMirrored",className:b.resizeLineIconStyles})}),(0,j.jsx)("div",{style:{flex:1}})]}),(0,j.jsxs)("div",{className:b.contentStyles.footer,children:[B?(0,j.jsx)(s.c,{messageBarType:d.f.error,styles:{root:{position:"absolute",width:"50%",left:0}},isMultiline:!1,children:E("\u683c\u5f0f\u9519\u8bef")}):null,(0,j.jsx)(c.K,{styles:{root:{marginRight:10}},onClick:function(){!e.isStrictJson||function(e){try{return JSON.parse(e),!0}catch(n){return!1}}(S)?(n((0,h.sW)(S)),n((0,h.y7)(!1))):H(!0)},children:E("\u786e\u8ba4")}),(0,j.jsx)(u.a,{onClick:M,children:E("\u53d6\u6d88")})]})]})})},S=t(48636),C=t(93433),k=t(1413),N=t(4942),_=t(2692),Z=t(97708),L=t(90110),I=t(17504),O=t(53997),D=t(64011),z=t(42081),T=t(65223),B=t(57217),H=t(33860),R=t(62551),E=t(54794),M=t(63934),W=(0,v.y0)({display:"flex",flexDirection:"column",width:350,position:"absolute",top:0,right:0,bottom:0,height:"auto",backgroundColor:"#fff",borderLeft:"1px solid rgb(234, 234, 234)",boxShadow:"rgb(0 0 0 / 18%) 0px 1.6px 3.6px 0px, rgb(0 0 0 / 22%) 0px 0.3px 0.9px 0px",zIndex:999}),J=(0,v.y0)({display:"flex",width:"100%",alignItems:"center",justifyContent:"space-between",color:"balck",boxSizing:"border-box",padding:"10px 20px"}),G=(0,v.y0)({fontSize:20,fontWeight:600,textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",color:"black"}),K=(0,v.y0)({flex:"1 1 0%",overflowY:"auto","&::-webkit-scrollbar":{width:4},"&::-webkit-scrollbar-thumb":{minHeight:"15px",border:"6px solid transparent",backgroundClip:"padding-box",backgroundColor:"rgb(200, 200, 200)"}}),F=(0,v.y0)({padding:"0 20px 20px",overflowX:"hidden",wordBreak:"break-word",userSelect:"text",borderTop:"1px solid #eaeaea"}),P=(0,v.y0)({margin:"10px -20px 0 -20px",borderTop:"1px solid #eaeaea"}),A=(0,v.y0)({color:"rgb(96, 94, 92)",fontSize:10,"> a":{textDecoration:"none"}}),$={textLabelStyle:(0,v.y0)({display:"flex",alignItems:"center",justifyContent:"space-between",fontWeight:600,color:"rgb(50, 49, 48)",boxSizing:"border-box",boxShadow:"none",margin:"0px",padding:"5px 0px",overflowWrap:"break-word",lineHeight:"30px"}),argsDescription:A,settingContainer:W,settingHeader:J,headerTitle:G,settingContent:K,contentWrapper:F,splitLine:P},X=function(){var e=x(),n=m(S.H6),t=m(T.RU),i=m(S.mg),a=m(z.hr),s=(0,o.useState)({}),d=(0,l.Z)(s,2),c=d[0],u=d[1],p=(0,o.useState)([]),f=(0,l.Z)(p,2),g=(f[0],f[1],(0,o.useState)()),v=(0,l.Z)(g,2),b=(v[0],v[1]),w=(0,o.useState)({}),C=(0,l.Z)(w,2),N=C[0],_=C[1],Z=(0,o.useState)({}),L=(0,l.Z)(Z,2),O=L[0],D=L[1],B=(0,y.$G)(),W=B.t,J=(B.i18n,function(t,o,l){var r={},a=null;switch(l){case"json":try{a=JSON.parse("".concat(o))}catch(p){a=o}break;case"int":a=+o;break;default:a=o}if(l){var s=JSON.parse(JSON.stringify(N));s[t]=a,r.args=JSON.stringify(s)}else r[t]=o;if("label"===t){var d=o;b(d),r[t]=d.key}null!==r&&void 0!==r&&r.args&&_((0,k.Z)((0,k.Z)({},N),JSON.parse(r.args))),n&&(u((0,k.Z)((0,k.Z)({},c),r)),e((0,S.v_)((0,k.Z)((0,k.Z)({},i),r))))});return(0,o.useEffect)((function(){if(console.log("current pipeline msg",n),n){var e,t;u(n);var o={key:null===n||void 0===n||null===(e=n.project)||void 0===e?void 0:e.id,text:null===n||void 0===n||null===(t=n.project)||void 0===t?void 0:t.name};b(o);var i=n.pipeline_ui_config,l=n.config,r=i,a=Object.keys(r||{}).reduce((function(e,n){var t=r[n];return Object.keys(t).forEach((function(n){l[n]?e[n]=l[n]:e[n]=t[n].default})),e}),{});_(a),D(r)}}),[n]),(0,o.useEffect)((function(){Object.keys(i).length>1&&e((0,S.u6)(!0))}),[i]),(0,o.useEffect)((function(){var n={};a.forEach((function(e){if((0,I.un)(e)){var t,o=a.filter((function(n){return n.id===e.source}))[0],i=a.filter((function(n){return n.id===e.target}))[0];null!==(t=n["".concat(i.data.name)])&&void 0!==t&&t.upstream?n["".concat(i.data.name)].upstream.push("".concat(o.data.name)):(n["".concat(i.data.name)]={},n["".concat(i.data.name)].upstream=["".concat(o.data.name)])}}));var t=(0,k.Z)({},n);u((0,k.Z)((0,k.Z)({},c),{dag_json:JSON.stringify(t,void 0,4)})),e((0,S.v_)((0,k.Z)((0,k.Z)({},i),{dag_json:JSON.stringify(t)})))}),[a]),(0,j.jsxs)("div",{style:{visibility:t?"visible":"hidden"},className:$.settingContainer,children:[(0,j.jsxs)("div",{className:$.settingHeader,children:[(0,j.jsx)("div",{className:$.headerTitle,children:W("\u9879\u76ee\u8bbe\u7f6e")}),(0,j.jsx)(r.h,{iconProps:{iconName:"ChromeClose",styles:{root:{fontSize:12,color:"#000"}}},onClick:function(){e((0,T.ZN)())}})]}),(0,j.jsx)("div",{className:$.settingContent,children:(0,j.jsxs)("div",{className:$.contentWrapper,children:[(0,j.jsx)(H.n,{label:W("\u540d\u79f0"),description:W("\u82f1\u6587\u540d(\u5b57\u6bcd\u3001\u6570\u5b57\u3001- \u7ec4\u6210)\uff0c\u6700\u957f50\u4e2a\u5b57\u7b26"),onChange:function(e,n){J("label",n||"")},value:(null===c||void 0===c?void 0:c.label)||"",disabled:!0}),(0,j.jsx)("div",{className:$.splitLine}),Object.keys(O||{}).reduce((function(n,t){var i=O[t],l=Object.keys(i).map((function(n){var t=i[n],l=t.choice.map((function(e){return{key:e,text:e}})),r=N&&N[n],a="json"===t.type?JSON.stringify(r,void 0,4):r;return l.length>0?(0,j.jsx)(o.Fragment,{children:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(R.L,{label:"".concat(n),onChange:function(e,o){J(n,"".concat(null===o||void 0===o?void 0:o.text)||"",t.type)},defaultSelectedKey:a||t.default,options:l,required:1===t.require,disabled:1!==t.editable}),(0,j.jsx)("div",{className:$.argsDescription,dangerouslySetInnerHTML:{__html:t.describe}})]})},n):(0,j.jsx)(o.Fragment,{children:(0,j.jsx)(H.n,{onRenderLabel:function(){return(0,j.jsxs)("div",{className:$.textLabelStyle,children:["".concat(n),"json"===t.type?(0,j.jsx)(E.K,{iconProps:{iconName:"FullWidthEdit"},onClick:function(){e((0,h.vh)({key:n,value:a})),e((0,h.y7)(!0))},children:W("\u7f16\u8f91")}):null]})},onRenderDescription:function(){return(0,j.jsx)("div",{className:$.argsDescription,dangerouslySetInnerHTML:{__html:t.describe}})},multiline:"json"===t.type||"text"===t.type,autoAdjustHeight:"json"===t.type||"text"===t.type,onChange:function(e,o){J(n,o||"",t.type)},value:a,required:1===t.require,disabled:1!==t.editable||"json"===t.type})},n)}));return n.push((0,j.jsxs)(o.Fragment,{children:[(0,j.jsx)(M._,{children:t}),l.flat(),(0,j.jsx)("div",{className:$.splitLine})]},t)),n}),[])]})})]})},V=t(50656),q=t(15861),U=t(64687),Y=t.n(U),Q=(0,v.y0)({display:"flex",flexDirection:"column",width:350,position:"absolute",top:0,right:0,bottom:0,height:"auto",backgroundColor:"#fff",borderLeft:"1px solid rgb(234, 234, 234)",boxShadow:"rgb(0 0 0 / 18%) 0px 1.6px 3.6px 0px, rgb(0 0 0 / 22%) 0px 0.3px 0.9px 0px",zIndex:998}),ee=(0,v.y0)({display:"flex",width:"100%",alignItems:"center",justifyContent:"space-between",color:"balck",boxSizing:"border-box",padding:"10px 20px"}),ne=(0,v.y0)({fontSize:20,textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",color:"black",fontWeight:600}),te=(0,v.y0)({flex:"1 1 0%",overflowY:"auto","&::-webkit-scrollbar":{width:4},"&::-webkit-scrollbar-thumb":{minHeight:"15px",border:"6px solid transparent",backgroundClip:"padding-box",backgroundColor:"rgb(200, 200, 200)"}}),oe=(0,v.y0)({padding:"0 20px 20px",overflowX:"hidden",wordBreak:"break-word",userSelect:"text",borderTop:"1px solid #eaeaea"}),ie=(0,v.y0)({margin:"10px -20px 0 -20px",borderTop:"1px solid #eaeaea"}),le=(0,v.y0)({height:"auto",display:"flex",flexDirection:"column",paddingTop:20}),re=(0,v.y0)({alignSelf:"center"}),ae=(0,v.y0)({display:"flex",flexDirection:"row",justifyContent:"space-evenly",paddingTop:20}),se=(0,v.y0)({color:"rgb(96, 94, 92)",fontSize:10,paddingTop:5,paddingLeft:2,"> a":{textDecoration:"none"}}),de=(0,v.y0)({color:"rgb(96, 94, 92)",fontSize:10,"> a":{textDecoration:"none"}}),ce=(0,v.y0)({display:"flex",alignItems:"center",justifyContent:"space-between",fontWeight:600,color:"rgb(50, 49, 48)",boxSizing:"border-box",boxShadow:"none",margin:"0px",padding:"5px 0px",overflowWrap:"break-word",lineHeight:"30px"}),ue=(0,v.y0)({fontSize:16,borderLeft:"2px solid rgb(0, 120, 212)",paddingLeft:4,marginTop:8}),pe={btnIcon:(0,v.y0)({display:"flex",alignItems:"center",svg:{width:16,height:16,marginRight:8},span:{display:"inline-flex"}}),borTitle:ue,modelContainer:Q,modelHeader:ee,headerTitle:ne,modelContent:te,contentWrapper:oe,splitLine:ie,settingControl:le,saveButton:re,debugButton:ae,templateConfig:se,argsDescription:de,textLabelStyle:ce},fe=function(e){var n,t,i,r=x(),a=m(z.hr),s=m(B.Aj),d=m(h.ld),c=m(h.OA),p=m(h.PF),f=(0,o.useState)({}),g=(0,l.Z)(f,2),v=g[0],b=g[1],w=(0,o.useState)({}),C=(0,l.Z)(w,2),N=C[0],_=C[1],Z=(0,o.useState)({}),L=(0,l.Z)(Z,2),I=(L[0],L[1],(0,o.useState)({})),O=(0,l.Z)(I,2),D=O[0],T=O[1],W=(0,o.useState)({}),J=(0,l.Z)(W,2),G=J[0],K=J[1],F=(m(S.H6),(0,y.$G)()),P=F.t,A=(F.i18n,function(e,n,t){var o={},i=null;switch(t){case"json":try{i=JSON.parse("".concat(n))}catch(s){i=n}break;case"int":i=+n;break;default:i=n}if(t){var l=JSON.parse(JSON.stringify(G));l[e]=i,o=l}else o[e]=n;var r=(0,k.Z)((0,k.Z)({},N),o),a=(0,k.Z)((0,k.Z)({},v),o);console.log("objChanged",r),console.log("taskChanged",N),console.log("obj",o),console.log("task",v),_(r),b(a),K((0,k.Z)((0,k.Z)({},G),o))});return(0,o.useEffect)((function(){if(console.log("modelProps",e),e.model.selected&&(r((0,B.kQ)(e.model.id)),0===Object.keys(v).length)){var n,t=e.model.data.config,o=(null===(n=e.model.data)||void 0===n?void 0:n.info["task-config"])||{},i=Object.keys(t).reduce((function(e,n){var i=t[n];return Object.keys(i).forEach((function(n){o[n]?e[n]=o[n]:e[n]=i[n].default})),e}),{});b(e.model.data),_(i),K(i),T(t),console.log("---",t,i)}}),[e.model.selected]),(0,o.useEffect)((function(){if(e.model.id&&(console.log("handleRdxArgs",N),r((0,B.hs)({id:e.model.id,changed:N}))),null!==N&&void 0!==N&&N.label){var n=a.map((function(n){if(n.id===e.model.id){var t=(0,k.Z)((0,k.Z)({},n.data),{label:N.label});return(0,k.Z)((0,k.Z)({},n),{data:t})}return n}));console.log("handleRdxArgs",n),r((0,z.OV)(n))}}),[N]),(0,o.useEffect)((function(){Object.keys(D).length>0&&e.model.id===s&&!d&&A(c,p,"json")}),[p]),(0,j.jsxs)("div",{className:pe.modelContainer,style:{visibility:e.model.selected?"visible":"hidden"},children:[(0,j.jsx)("div",{className:pe.modelHeader,children:(0,j.jsx)("div",{className:pe.headerTitle,children:(null===v||void 0===v?void 0:v.name)||""})}),(0,j.jsx)("div",{className:pe.modelContent,children:(0,j.jsxs)("div",{className:pe.contentWrapper,children:[(0,j.jsx)("div",{style:{paddingTop:12},children:((null===e||void 0===e||null===(n=e.model)||void 0===n||null===(t=n.data)||void 0===t||null===(i=t.info)||void 0===i?void 0:i.task_jump_button)||[]).map((function(e,n){return(0,j.jsx)(u.a,{style:{marginRight:16},onClick:(0,q.Z)(Y().mark((function n(){return Y().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:window.open("".concat(window.location.origin).concat(e.action_url));case 1:case"end":return n.stop()}}),n)}))),children:(0,j.jsxs)("div",{className:pe.btnIcon,children:[(0,j.jsx)("span",{dangerouslySetInnerHTML:{__html:e.icon_svg}}),(0,j.jsx)("span",{children:e.name})]})},"task_jump_button".concat(n))}))}),(0,j.jsx)("div",{className:pe.splitLine}),(0,j.jsx)(H.n,{label:P("\u522b\u540d"),description:P("\u8282\u70b9\u522b\u540d"),required:!0,onChange:function(e,n){A("label",n||"")},value:(null===v||void 0===v?void 0:v.label)||""}),(0,j.jsx)("div",{className:pe.splitLine}),Object.keys(D).reduce((function(e,n){var t=D[n],i=Object.keys(t).map((function(e){var n=t[e],i=n.choice.map((function(e){return{key:e,text:e}})),l=G&&G[e],a="json"===n.type?JSON.stringify(l,void 0,4):l;return(0,j.jsx)(o.Fragment,{children:i.length>0?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(R.L,{label:"".concat(n.label),onChange:function(t,o){A(e,"".concat(null===o||void 0===o?void 0:o.text)||"",n.type)},defaultSelectedKey:a||n.default,options:i,required:1===n.require,disabled:1!==n.editable}),(0,j.jsx)("div",{className:pe.argsDescription,dangerouslySetInnerHTML:{__html:n.describe}})]}):(0,j.jsx)(H.n,{onRenderLabel:function(){return(0,j.jsxs)("div",{className:pe.textLabelStyle,children:["".concat(n.label),"str"!==n.type?(0,j.jsx)(E.K,{iconProps:{iconName:"FullWidthEdit"},onClick:function(){r((0,h.vh)({key:e,value:a,type:n.item_type})),r((0,h.y7)(!0))},children:P("\u7f16\u8f91")}):null]})},onRenderDescription:function(){return(0,j.jsx)("div",{className:pe.argsDescription,dangerouslySetInnerHTML:{__html:n.describe}})},multiline:"str"!==n.type,autoAdjustHeight:"str"!==n.type,onChange:function(t,o){A(e,o||"",n.type)},value:a,required:1===n.require,disabled:1!==n.editable})},e)}));return e.push((0,j.jsxs)(o.Fragment,{children:[(0,j.jsx)(M._,{children:(0,j.jsx)("div",{className:pe.borTitle,children:n})}),i.flat(),(0,j.jsx)("div",{className:pe.splitLine})]},n)),e}),[])]})})]})},xe={height:54,width:272,borderRadius:100,borderStyle:"solid",display:"flex",flexDirection:"row",backgroundColor:"#fff",fontSize:12,cursor:"move",boxSizing:"border-box",transition:"all 0.3s"},me=(0,v.y0)((0,k.Z)((0,k.Z)({},xe),{borderWidth:1,borderColor:"#b1b1b7"})),he=(0,v.y0)((0,k.Z)((0,k.Z)({},xe),{borderWidth:1,borderColor:"#006dce",backgroundColor:"#f1f7fd"})),ge=(0,v.y0)({width:8,flexShrink:0,borderRadius:"3px 0 0 3px",borderRight:"1px solid #8a8886",margin:"-8px 0 -8px -8px"}),ve=(0,v.y0)({boxSizing:"border-box",display:"flex",flexDirection:"column",overflow:"hidden"}),be=(0,v.y0)({display:"flex",marginLeft:7,minHeight:26}),ye=(0,v.y0)({fontSize:16,width:64,display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgb(0, 120, 212)",borderTopLeftRadius:100,borderBottomLeftRadius:100,margin:"-1px 0 -1px -1px"}),je=(0,v.y0)({userSelect:"none",boxSizing:"border-box",color:"#fff",fontSize:20,marginLeft:8}),we=(0,v.y0)({whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",paddingLeft:8,paddingBottom:2,marginBottom:2,fontWeight:600,fontSize:14,borderBottom:"1px dashed #c1c1c1",userSelect:"none"}),Se=(0,v.y0)({width:"12px !important",height:"12px !important",bottom:"-7px !important",borderColor:"#b1b1b7 !important",backgroundColor:"#fff !important",transition:"all 0.3s","&:hover":{borderWidth:"2px !important",borderColor:"var(--hover-color) !important",cursor:"pointer !important"}}),Ce=(0,v.y0)({visibility:"hidden"}),ke=(0,v.y0)({height:"100%",width:"100%",display:"flex",justifyContent:"center",flexDirection:"column"}),Ne={nodeTips:(0,v.y0)({color:"rgb(177, 177, 183)",paddingLeft:8}),nodeContentWrapper:ke,nodeContainer:me,nodeOnSelect:he,nodeBar:ge,nodeContent:ve,nodeConentTitleBar:be,nodeIconWrapper:ye,nodeIcon:je,nodeTitle:we,handleStyle:Se,hidden:Ce},_e=t(98867),Ze=t(98937),Le=t(42657),Ie=t(25303),Oe={get:function(e){var n=window.localStorage.getItem(e);return n&&n.match(/^(\{|\[).*(?=[}\]])/)?JSON.parse(n):n||""},set:function(e,n){window.localStorage.setItem(e,"string"===typeof n?n:JSON.stringify(n))}},De={dataSet:function(e){var n,t,i,r,s,d,c,u,p,f,h,g,v,b,w,C,_,Z,L=(0,o.useState)(!1),O=(0,l.Z)(L,2),D=O[0],T=O[1],H=(0,o.useState)([]),R=(0,l.Z)(H,2),E=R[0],M=R[1],W=(0,o.useState)(),J=(0,l.Z)(W,2),G=J[0],K=J[1],F=(Oe.get("job_template").value||[]).reduce((function(e,n){return(0,k.Z)((0,k.Z)({},e),{},(0,N.Z)({},n.name,n))}),{}),P=m(S.Bn),A=(m(z.hr),x()),$=(0,y.$G)(),X=$.t;$.i18n;return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(_e.Z,{title:X("\u667a\u80fd\u63a8\u8350\u4e0b\u6e38\u8282\u70b9"),visible:D,onCancel:function(){K(void 0),T(!1)},onOk:function(){if(G){var n=F[G],t=JSON.parse(n.args);Object.keys(t).reduce((function(e,n){var o={};return Object.keys(t[n]).reduce((function(e,o){var i=t[n][o].default;return Object.assign(e,(0,N.Z)({},o,i)),e}),o),Object.assign(e,o),e}),{});e.xPos,null===e||void 0===e||e.yPos;if(P){A((0,B.Xt)(!0));"".concat(n.name.replace(/\.|[\u4e00-\u9fa5]/g,"").replace(/_|\s/g,"-")||"task","-").concat(Date.now()).substring(0,49);A((0,B.Xt)(!1)),T(!1)}}else Ze.ZP.warn(X("\u8bf7\u5148\u9009\u62e9\u63a8\u8350\u8282\u70b9"))},children:(0,j.jsx)("div",{children:(0,j.jsx)(Le.ZP.Group,{onChange:function(e){K(e.target.value)},value:G,children:(0,j.jsx)(Ie.Z,{direction:"vertical",children:E.map((function(e){return(0,j.jsx)(Le.ZP,{value:e.name,children:e.name},"recommend_".concat(e.name))}))})})})}),(0,j.jsx)(I.HH,{type:"target",position:I.Ly.Top,style:{top:"-7px","--hover-color":null===e||void 0===e||null===(n=e.data)||void 0===n||null===(t=n.info)||void 0===t||null===(i=t.color)||void 0===i?void 0:i.color},className:Ne.handleStyle}),(0,j.jsxs)("div",{className:e.selected?Ne.nodeOnSelect:Ne.nodeContainer,style:{borderColor:e.selected?null===e||void 0===e||null===(r=e.data)||void 0===r||null===(s=r.info)||void 0===s||null===(d=s.color)||void 0===d?void 0:d.color:"",backgroundColor:e.selected?null===e||void 0===e||null===(c=e.data)||void 0===c||null===(u=c.info)||void 0===u||null===(p=u.color)||void 0===p?void 0:p.bg:""},children:[(0,j.jsx)("div",{className:Ne.nodeIconWrapper,style:{backgroundColor:null===e||void 0===e||null===(f=e.data)||void 0===f||null===(h=f.info)||void 0===h||null===(g=h.color)||void 0===g?void 0:g.color},children:(0,j.jsx)(a.J,{iconName:"Database",className:Ne.nodeIcon})}),(0,j.jsxs)("div",{className:Ne.nodeContentWrapper,children:[(0,j.jsx)("div",{className:Ne.nodeTitle,children:null===e||void 0===e||null===(v=e.data)||void 0===v?void 0:v.label}),(0,j.jsxs)("div",{className:Ne.nodeTips,children:[null===e||void 0===e||null===(b=e.data)||void 0===b?void 0:b.info["template-group"]," - ",null===e||void 0===e||null===(w=e.data)||void 0===w?void 0:w.info.template]})]})]}),(0,j.jsx)(I.HH,{onClick:function(){var n,t;console.log("props",e);var o,i,l=null===(n=e.data)||void 0===n||null===(t=n.info)||void 0===t?void 0:t.expand;"[object String]"===Object.prototype.toString.call(l)&&(l=JSON.parse((null===(o=e.data)||void 0===o||null===(i=o.info)||void 0===i?void 0:i.expand)||"{}"));var r=l.rec_job_template;r&&(M([F[r]]),T(!0))},type:"source",position:I.Ly.Bottom,className:Ne.handleStyle,style:{"--hover-color":null===e||void 0===e||null===(C=e.data)||void 0===C||null===(_=C.info)||void 0===_||null===(Z=_.color)||void 0===Z?void 0:Z.color}}),(0,j.jsx)(V.m,{hostId:"hostId_model",className:"data-set-layer",children:(0,j.jsx)(fe,{model:e})})]})}},ze={itemStyles:(0,v.y0)({position:"relative"}),flowWrapper:(0,v.y0)({width:"100%",height:"100%"}),spinnerWrapper:(0,v.y0)({position:"absolute",top:0,left:0,display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",overflow:"hidden",backgroundColor:"rgba(255, 255, 255, 0.6)",zIndex:9999})},Te=i.K.Item,Be=function(){var e=x(),n=m(z.hr),t=m(S.Bn),i=m(S.Q),r=m(S.H6),a=m(T.RU),s=m(B.NH),d=(0,o.useRef)(null),c=(0,o.useState)(null),u=(0,l.Z)(c,2),p=u[0],f=u[1],h=(0,y.$G)(),g=h.t;h.i18n;return(0,o.useEffect)((function(){Object.keys(r).length&&O.Z.getTemplateCommandConfig(t,i).then((function(n){if(console.log("job_template_modelview",n),0===(null===n||void 0===n?void 0:n.status)&&"success"===(null===n||void 0===n?void 0:n.message)){var t={};for(var o in n.templte_list)if(Object.prototype.hasOwnProperty.call(n.templte_list,o))for(var i=n.templte_list[o],a=0;a<i.length;a++){var s=i[a];t[s.template_name]=s.templte_ui_config}var d=[];Object.keys(r.dag_json).forEach((function(e){var o=r.dag_json[e],i=(0,l.Z)(o.location,2),a=i[0],s=i[1],c=o.upstream,u=void 0===c?[]:c,p=o.templte_ui_config,f=void 0===p?{}:p,x=o.label,m=(0,k.Z)({},n.templte_common_ui_config);if(m)for(var h in m)if(Object.prototype.hasOwnProperty.call(m,h)){var g=(0,k.Z)({},m[h]);for(var v in g){if(Object.prototype.hasOwnProperty.call(g,v))if((0,k.Z)({},g[v]).addable)try{m[h][v].editable=0}catch(j){console.log(j)}}}var b={data:{config:(0,k.Z)((0,k.Z)({},n.templte_common_ui_config),t[o.template]||f),info:(0,k.Z)({},o),label:x,name:e},id:e,position:{x:a,y:s},type:"dataSet"},y=u.map((function(n){return{arrowHeadType:"arrow",id:"reactflow__edge-".concat(n,"-").concat(e),source:n,sourceHandle:null,target:e,targetHandle:null}}));d.push.apply(d,[b].concat((0,C.Z)(y)))})),e((0,z.OV)(d))}})).catch((function(n){var t,o;n.response&&e((0,D.L$)({msg:null===(t=n.response)||void 0===t||null===(o=t.data)||void 0===o?void 0:o.message}))})).finally((function(){console.log("fin")}))}),[r]),(0,j.jsx)(Te,{shrink:!0,grow:1,className:ze.itemStyles,children:(0,j.jsxs)("div",{className:ze.flowWrapper,ref:d,children:[(0,j.jsxs)(I.ZP,{elements:n,nodeTypes:De,snapToGrid:!0,snapGrid:[16,16],defaultZoom:1,onLoad:function(e){f(e)},onDrop:function(o){o.preventDefault();var l=d.current.getBoundingClientRect(),r=JSON.parse(o.dataTransfer.getData("application/reactflow"));console.log("modelInfo",r);var a=p.project({x:o.clientX-l.left,y:o.clientY-l.top}),s=r.args;if(Object.keys(s).reduce((function(e,n){var t={};return Object.keys(s[n]).reduce((function(e,t){var o=s[n][t].default;return Object.assign(e,(0,N.Z)({},t,o)),e}),t),Object.assign(e,t),e}),{}),t&&i){"".concat(r.name.replace(/\.|[\u4e00-\u9fa5]/g,"").replace(/_|\s/g,"-")||"task","-").concat(Date.now()).substring(0,49);var c={id:"".concat(r.name,"-").concat((new Date).valueOf()),type:"dataSet",position:a,data:{name:"".concat(r.name,"-").concat((new Date).valueOf()),label:"".concat(g("\u65b0\u5efa")," ").concat(r.name," ").concat(g("\u4efb\u52a1")),args:{},info:r,config:(0,k.Z)((0,k.Z)({},r.templte_common_ui_config),r.templte_ui_config)}};console.log("newNode",c);var u=n.concat(c);e((0,z.OV)(u)),e((0,S.oL)())}},onDragOver:function(e){e.preventDefault(),e.dataTransfer.dropEffect="move"},onElementClick:function(n,t){a&&t.data&&e((0,T.ZN)())},onElementsRemove:function(t){t.forEach((function(n){null!==n&&void 0!==n&&n.id&&(0,I.UG)(n)&&e((0,S.oL)())})),e((0,S.u6)(!0)),e((0,z.OV)((0,I.d0)(t,n)))},onConnect:function(t){e((0,S.u6)(!0)),e((0,z.OV)((0,I.Z_)(Object.assign(t,{arrowHeadType:"arrow"}),n)))},onNodeDragStop:function(t,o){e((0,z.OV)(n.map((function(e){return(null===e||void 0===e?void 0:e.id)===(null===o||void 0===o?void 0:o.id)?o:e})))),e((0,S.u6)(!0))},onSelectionChange:function(n){console.log("current elements",n),n&&e((0,z.pM)(n))},children:[(0,j.jsx)(X,{}),(0,j.jsx)(I.a9,{nodeStrokeColor:"#8a8886",nodeColor:"#c8c8c8"}),(0,j.jsx)(I.ZX,{}),(0,j.jsx)(_.Z,{id:"hostId_model",className:"layer-host"})]}),(0,j.jsx)("div",{className:ze.spinnerWrapper,style:{visibility:s?"visible":"hidden"},children:(0,j.jsx)(Z.$,{size:L.E.large,label:"Loading"})})]})})},He=t(49161),Re=function(){var e=x(),n=m(D.V0);return(0,o.useEffect)((function(){n&&setTimeout((function(){e((0,D.L$)(null))}),3e3)}),[n]),n&&(0,j.jsx)(s.c,{messageBarType:d.f.error,styles:{root:{minHeight:32,width:"80%",margin:"0 auto"}},dismissButtonAriaLabel:"Close",onDismiss:function(){e((0,D.L$)(null))},isMultiline:!1,children:(null===n||void 0===n?void 0:n.msg)||""})},Ee=(0,v.y0)({boxSizing:"border-box",marginLeft:"8px",padding:"0 8px",height:"32px",lineHeight:"32px",cursor:"text",marginBottom:"4px",fontSize:"16px",fontWeight:600,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:"black"}),Me=(0,v.y0)({visibility:"hidden"}),We=(0,v.y0)({marginRight:"8px","> svg":{width:"14px",height:"14px"}}),Je={iconContainerStyle:(0,v.y0)({display:"flex",alignItems:"center",justifyContent:"center"}),iconStyle:We,headStyle:{root:{backgroundColor:"#fff",padding:"10px 16px",borderBottom:"1px solid #dadada"}},headNameStyle:Ee,buttonItemStyle:{root:{marginLeft:"16px !important"}},hidden:Me},Ge=i.K.Item,Ke=function(){var e=x(),n=m(S.Bn),t=m(S.H6),o=(m(D.zr),(0,y.$G)()),l=o.t;o.i18n;return(0,j.jsx)(Ge,{className:"editor-head",children:(0,j.jsxs)(i.K,{horizontal:!0,horizontalAlign:"space-between",verticalAlign:"center",styles:Je.headStyle,children:[(0,j.jsx)(Ge,{grow:1,children:(0,j.jsxs)(i.K,{horizontal:!0,children:[(0,j.jsx)(Ge,{children:t.name?(0,j.jsx)("div",{className:Je.headNameStyle,children:t.label}):(0,j.jsx)(c.K,{styles:{root:{padding:"17px 16px"}},iconProps:{iconName:"Add",styles:{root:{fontSize:10}}},children:l("\u65b0\u5efa\u9879\u76ee")})}),(0,j.jsx)(Ge,{className:t.name?"":Je.hidden,children:(0,j.jsx)(He.G,{content:l("\u8bbe\u7f6e"),children:(0,j.jsx)(r.h,{iconProps:{iconName:"Settings"},onClick:function(){e((0,T.ZN)())}})})}),(0,j.jsx)(Ge,{grow:1,align:"center",children:(0,j.jsx)(Re,{})})]})}),(0,j.jsx)(Ge,{className:t.name?"":Je.hidden,children:(0,j.jsx)(i.K,{horizontal:!0,children:((null===t||void 0===t?void 0:t.pipeline_run_button)||[]).map((function(t){return(0,j.jsx)(Ge,{styles:Je.buttonItemStyle,children:(0,j.jsx)(c.K,{onClick:(0,q.Z)(Y().mark((function o(){return Y().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:if(!n){o.next=9;break}return o.t0=e,o.next=4,(0,B.C)();case 4:return o.t1=o.sent,o.next=7,(0,o.t0)(o.t1);case 7:e((0,S.oL)()),window.open("".concat(window.location.origin).concat(t.action_url));case 9:case"end":return o.stop()}}),o)}))),children:t.name})},"runBtnList_".concat(t.name))}))})})]})})},Fe=t(68961),Pe=t(65323),Ae=t(82622),$e=t(51244),Xe=t(87309),Ve=(0,v.y0)({paddingRight:20,lineHeight:"1"}),qe=(0,v.y0)({display:"flex",alignItems:"center",height:"40px",padding:"0px 14px 0px 8px",borderBottom:"none",backgroundColor:"#fff"}),Ue=(0,v.y0)({padding:"4px 10px !important",fontSize:"15px !important"}),Ye=(0,v.y0)({color:"#015cda !important"}),Qe={btnIcon:(0,v.y0)({display:"flex !important",alignItems:"center",svg:{width:16,height:16,marginTop:0,marginRight:8},span:{display:"inline-flex"}}),commandBarStyleCustom:qe,commandButtonStyle:Ue,editorToolStyle:{root:{display:"flex",alignItems:"center",justifyContent:"space-between",flexDirection:"row",backgroundColor:"#fff",padding:"2px 0px",borderBottom:"1px solid #dadada","#authoring-page-toolbar":{flex:1}}},autoSavedTips:Ve,commandBarStyle:{root:{height:"40px",padding:"0px 14px 0px 8px",borderBottom:"none",backgroundColor:"#fff"}},commonIcon:Ye,toggleStyle:{root:{display:"flex",alignItems:"center",padding:"8px 0px 8px 8px",height:"100%",boxSizing:"border-box",borderLeft:"1px solid rgb(234, 234, 234)",marginLeft:"8px"},container:{display:"inline-flex"}},comboBoxStyle:{container:{borderLeft:"1px solid #eaeaea",marginLeft:"4px",padding:"0px 5px 0px 10px",width:"auto"},root:{backgroundColor:"#ffffff",padding:"0px 20px 0px 4px",selectors:{"&.is-open::after":{borderBottom:"2px solid #015cda"},"&::after":{border:"none",borderBottom:0,borderRadius:"none"},".ms-Button":{width:20},".ms-Icon":{fontSize:8}}},input:{width:35,backgroundColor:"#ffffff"}}},en=i.K.Item,nn=function(){var e=x(),n=m(z.hr),t=m(S.N1),r=m(S.H6),s=m(S.Bn),d=m(B.BX),c=m(S.B5),u=m(z.tT),p=(0,y.$G)(),f=p.t,h=(p.i18n,(0,o.useState)([])),g=(0,l.Z)(h,2),v=g[0],b=g[1];return(0,o.useEffect)((function(){if(r){var e=((null===r||void 0===r?void 0:r.pipeline_jump_button)||[]).map((function(e){return(0,j.jsx)(Xe.Z,{className:Qe.commandButtonStyle,type:"text",onClick:function(){window.open("".concat(window.location.origin).concat(e.action_url))},children:(0,j.jsxs)("div",{className:Qe.btnIcon,children:[(0,j.jsx)("span",{dangerouslySetInnerHTML:{__html:e.icon_svg}}),(0,j.jsx)("span",{children:e.name})]})},Math.random().toString(36).substring(2))}));b(e)}}),[r]),(0,o.useEffect)((function(){d&&d.forEach((function(n){Object.keys(n).length>0&&e((0,S.u6)(!0))}))}),[d]),(0,j.jsxs)(en,{shrink:!0,styles:Qe.editorToolStyle,children:[(0,j.jsxs)("div",{className:Qe.commandBarStyleCustom,children:[(0,j.jsx)(Xe.Z,{className:Qe.commandButtonStyle,type:"text",icon:(0,j.jsx)(Fe.Z,{className:Qe.commonIcon}),onClick:function(){e((0,$e.ZN)())},children:f("\u5c55\u5f00/\u5173\u95ed\u83dc\u5355")}),(0,j.jsx)(Xe.Z,{className:Qe.commandButtonStyle,type:"text",icon:(0,j.jsx)(Pe.Z,{className:Qe.commonIcon}),onClick:function(){Ze.ZP.success(f("\u4fdd\u5b58\u6210\u529f")),e((0,S.oL)())},children:f("\u4fdd\u5b58")}),(0,j.jsx)(Xe.Z,{className:Qe.commandButtonStyle,type:"text",icon:(0,j.jsx)(Ae.Z,{className:Qe.commonIcon}),onClick:function(){(0,I.UG)(u[0])&&e((0,S.oL)()),e((0,S.u6)(!0)),e((0,z.OV)((0,I.d0)(u,n)))},children:f("\u5220\u9664\u8282\u70b9")}),v]}),(0,j.jsx)(i.K,{horizontal:!0,verticalAlign:"center",className:Qe.autoSavedTips,style:{visibility:s?"visible":"hidden"},children:t?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(a.J,{iconName:c?"AlertSolid":"SkypeCircleCheck",styles:{root:{color:c?"#e95f39":"#8cb93c",marginRight:5}}}),f(c?"\u672a\u4fdd\u5b58":"\u5df2\u4fdd\u5b58")]}):(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(Z.$,{styles:{root:{marginRight:5}},size:L.E.small}),f("\u4fdd\u5b58\u4e2d")]})})]})},tn=i.K.Item,on=function(){var e=x();return(0,o.useEffect)((function(){e((0,S.e4)())}),[]),(0,j.jsx)(tn,{className:"flow-editor",styles:{root:{flexGrow:1,flexShrink:1,background:"#f4f4f4",overflow:"hidden",display:"flex",flexDirection:"column"}},children:(0,j.jsxs)(i.K,{grow:1,children:[(0,j.jsx)(Ke,{}),(0,j.jsx)(nn,{}),(0,j.jsx)(Be,{})]})})},ln=t(54039),rn=t(2972),an=t(4026),sn=t(6975),dn={calloutContent:(0,v.y0)({display:"flex",flexFlow:"column nowrap",width:"360px",height:"auto",boxSizing:"border-box",maxHeight:"inherit",padding:"16px 0px",fontSize:"12px",background:"#fff",borderRadius:"2px"}),moduleDetailItemStyle:(0,v.y0)({fontSize:"12px",padding:"0 16px",overflowX:"visible",overflowY:"hidden"}),moduleDetailTitle:(0,v.y0)({padding:"0px 16px",fontWeight:600,fontSize:"14px",lineHeight:"1",marginTop:"0px",marginBottom:"8px"}),moduleDetailLabel:(0,v.y0)({fontSize:"12px",fontWeight:"bold",lineHeight:"16px",color:"rgb(89, 89, 89)",padding:"0px",margin:"0px 0px 4px"}),moduleDetailBody:(0,v.y0)({lineHeight:"14px",marginBottom:"22px",p:{margin:"0",padding:"0"}}),moduleButton:(0,v.y0)({display:"flex",justifyContent:"center",alignItems:"center"})},cn=i.K.Item,un=function(){var e=x(),n=m($e.p),t=m($e.b2),o=m($e.H6),l=(0,y.$G)(),r=l.t,a=(l.i18n,function(n){e((0,$e.DH)("mouseenter"!==n.type))});return(0,j.jsx)(rn.U,{gapSpace:-10,hidden:n,hideOverflow:!0,calloutMaxHeight:480,isBeakVisible:!1,preventDismissOnLostFocus:!0,target:{current:t},directionalHint:12,children:(0,j.jsxs)(i.K,{className:dn.calloutContent,onMouseEnter:a,onMouseLeave:a,children:[(0,j.jsx)(cn,{children:(0,j.jsx)("h3",{className:dn.moduleDetailTitle,children:o.name})}),(0,j.jsx)(cn,{grow:1,shrink:1,className:dn.moduleDetailItemStyle,children:(0,j.jsxs)("div",{children:[(0,j.jsx)(M._,{title:"Description",className:dn.moduleDetailLabel,children:r("\u63cf\u8ff0")}),(0,j.jsx)("div",{className:dn.moduleDetailBody,children:(0,j.jsx)("p",{children:o.describe})})]})}),(0,j.jsx)(cn,{grow:1,shrink:1,className:dn.moduleDetailItemStyle,children:(0,j.jsxs)("div",{children:[(0,j.jsx)(M._,{title:"Description",className:dn.moduleDetailLabel,children:r("\u521b\u5efa\u4eba")}),(0,j.jsx)("div",{className:dn.moduleDetailBody,children:(0,j.jsx)("p",{children:o.createdBy})})]})}),(0,j.jsx)(cn,{grow:1,shrink:1,className:dn.moduleDetailItemStyle,children:(0,j.jsxs)("div",{children:[(0,j.jsx)(M._,{title:"Description",className:dn.moduleDetailLabel,children:r("\u4e0a\u6b21\u4fee\u6539\u65f6\u95f4")}),(0,j.jsx)("div",{className:dn.moduleDetailBody,children:(0,j.jsx)("p",{children:o.lastChanged})})]})}),null!==o&&void 0!==o&&o.help_url?(0,j.jsx)(cn,{grow:1,shrink:1,className:dn.moduleButton,children:(0,j.jsx)(c.K,{onClick:function(){window.open(o.help_url)},children:r("\u914d\u7f6e\u6587\u6863")})}):null]})})},pn={moduleItem:(0,v.y0)({width:"100%",userSelect:"none",cursor:"pointer"}),moduleListModuleCard:(0,v.y0)({margin:"8px 16px",border:"1px solid rgb(234, 234, 234)",background:"rgb(248, 248, 248)",color:"rgb(55, 55, 55)",borderRadius:"2px",padding:"8px",display:"flex",fontSize:"12px",flexDirection:"column","&:hover":{backgroundColor:"#eee"},"> div":{pointerEvents:"none",display:"flex",flexDirection:"row",flexGrow:1,alignItems:"center","> header":{flexGrow:1,color:"rgb(0, 0, 0)",fontWeight:600,fontSize:"12px",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",lineHeight:"16px"},"> summary":{flexGrow:1,color:"rgb(55, 55, 55)",fontSize:"12px"}}})},fn=function(e){var n=x(),t=(0,o.useState)(e.model),i=(0,l.Z)(t,2),r=i[0],s=i[1],d=function(e){"mouseenter"===e.type&&n((0,$e.VF)(e.target)),n((0,$e.DH)("mouseenter"!==e.type)),n((0,$e.rn)(r))};return(0,o.useEffect)((function(){s(e.model)}),[e.model]),(0,j.jsx)("div",{className:pn.moduleItem,children:(0,j.jsx)("div",{className:"module-card-with-hover-wrap",children:(0,j.jsx)("div",{className:"module-card-content-wrap",children:(0,j.jsxs)("div",{className:pn.moduleListModuleCard,onMouseEnter:d,onMouseLeave:d,onMouseDown:function(){n((0,$e.DH)(!0))},onDragStart:function(e){e.dataTransfer.setData("application/reactflow",JSON.stringify(r)),e.dataTransfer.effectAllowed="move"},draggable:!0,children:[(0,j.jsxs)("div",{children:[(0,j.jsx)(a.J,{iconName:"Database",styles:{root:{marginRight:"4px",lineHeight:"16px"}}}),(0,j.jsx)("header",{children:r.name})]}),(0,j.jsx)("div",{children:(0,j.jsx)("summary",{children:(0,j.jsx)("span",{children:r.describe})})})]})})})})},xn={moduleItem:(0,v.y0)({width:"100%",userSelect:"none",cursor:"pointer"}),moduleListModuleCard:(0,v.y0)({margin:"4px 8px",border:"1px solid rgb(234, 234, 234)",background:"rgb(248, 248, 248)",color:"rgb(55, 55, 55)",borderRadius:"2px",padding:"5px 8px",display:"flex",fontSize:"12px",flexDirection:"column","&:hover":{backgroundColor:"#eee"},"> div":{pointerEvents:"none",display:"flex",flexDirection:"row",flexGrow:1,alignItems:"center","> header":{flexGrow:1,color:"rgb(0, 0, 0)",fontWeight:600,fontSize:"12px",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",lineHeight:"16px"},"> summary":{flexGrow:1,color:"rgb(55, 55, 55)",fontSize:"12px"}}})},mn=function(e){var n=(0,o.useState)(e.model),t=(0,l.Z)(n,2),i=t[0],r=t[1];return(0,o.useEffect)((function(){r(e.model)}),[e.model]),(0,j.jsx)("div",{className:xn.moduleItem,children:(0,j.jsx)("div",{className:"module-card-with-hover-wrap",children:(0,j.jsx)("div",{className:"module-card-content-wrap",children:(0,j.jsxs)("div",{className:xn.moduleListModuleCard,onClick:e.onClick,children:[(0,j.jsxs)("div",{children:[(0,j.jsx)(a.J,{iconName:"Database",styles:{root:{marginRight:"4px",lineHeight:"16px"}}}),(0,j.jsx)("header",{children:i.name})]}),(0,j.jsx)("div",{children:(0,j.jsx)("summary",{children:(0,j.jsx)("span",{children:i.describe})})})]})})})})},hn={"::-webkit-scrollbar":{width:4},"::-webkit-scrollbar-thumb":{minHeight:"15px",border:"6px solid transparent",backgroundClip:"padding-box",backgroundColor:"rgb(200, 200, 200)"}},gn={showModuleTree:(0,v.y0)({width:"320px",height:"100%",transition:"all 0.35s ease 0s",overflowX:"hidden",visibility:"visible"}),hideModuleTree:(0,v.y0)({width:"0px",height:"100%",transition:"width 0.35s ease 0s",overflowX:"hidden",visibility:"hidden"}),treeContainer:(0,v.y0)({width:"320px",boxSizing:"border-box",height:"100%",display:"flex",flexDirection:"column",borderRight:"1px solid rgb(234, 234, 234)",backgroundColor:"#fff"}),searchBoxStyle:(0,v.y0)({margin:"12px 16px",padding:"1px 0px",color:"rgb(1, 92, 218)",lineHeight:"32px",minHeight:"32px",flexGrow:"1"}),searchCallout:(0,v.y0)({".ms-Callout-main":(0,k.Z)({overflowY:"overlay",willChange:"transform"},hn)}),searchListStyle:(0,v.y0)({width:"287px",padding:"8px 0"}),summaryStyle:(0,v.y0)({display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderTop:"1px solid rgb(218, 218, 218)"}),moduleTreeStyle:(0,v.y0)({position:"relative",display:"flex",flexDirection:"column",overflowX:"hidden",overflowY:"auto",flexGrow:"1"}),moduleTreeBody:(0,v.y0)({position:"relative",height:"100%"}),listIconStyle:(0,v.y0)({display:"inline-flex",fontSize:"12px",lineHeight:"12px",marginRight:"4px",height:"6px",color:"rgb(55, 55, 55)",userSelect:"none"}),spinnerContainer:(0,v.y0)({width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center"}),moduleListStyle:(0,v.y0)((0,k.Z)({padding:0,margin:"0 auto",overflowY:"overlay",height:"100%",willChange:"transform"},hn)),moduleListItem:(0,v.y0)({listStyle:"none",outline:"none"}),itemFolderNode:(0,v.y0)({display:"flex",alignItems:"center",cursor:"pointer",padding:"7px 15px",border:"1px solid transparent",height:16,fontFamily:"Segoe UI,sans-serif",fontSize:12,lineHeight:16,fontWeight:600,color:"black","&:hover":{backgroundColor:"#eaeaea"}})},vn=i.K.Item,bn=[{color:"rgba(0,120,212,1)",bg:"rgba(0,120,212,0.02)"},{color:"rgba(0,170,200,1)",bg:"rgba(0,170,200,0.02)"},{color:"rgba(0,200,153,1)",bg:"rgba(0,200,153,0.02)"},{color:"rgba(0,6,200,1)",bg:"rgba(0,6,200,0.02)"},{color:"rgba(212,65,0,1)",bg:"rgba(212,65,0,0.02)"},{color:"rgba(212,176,0,1)",bg:"rgba(212,176,0,0.02)"}],yn=function(){var e=x(),n=m($e.RU),t=(0,o.useState)(new Set),s=(0,l.Z)(t,2),d=s[0],c=s[1],u=(0,o.useState)(new Map),p=(0,l.Z)(u,2),f=p[0],h=p[1],v=(0,o.useState)(0),b=(0,l.Z)(v,2),w=b[0],C=b[1],k=(0,o.useState)(!1),N=(0,l.Z)(k,2),_=N[0],I=N[1],z=(0,o.useState)(!1),T=(0,l.Z)(z,2),B=T[0],H=T[1],R=(0,o.useState)(new Map),E=(0,l.Z)(R,2),M=E[0],W=E[1],J=m(S.Bn),G=m(S.Q),K=(0,y.$G)(),F=K.t,P=(K.i18n,function(){I(!0),C(0),O.Z.getTemplateCommandConfig(J,G).then((function(e){if(console.log("job_template_modelview",e),0===(null===e||void 0===e?void 0:e.status)&&"success"===(null===e||void 0===e?void 0:e.message)){for(var n=e.templte_common_ui_config,t=e.templte_list,o=e.template_group_order,i={},l=0;l<o.length;l++){var r=o[l];i[r]=t[r]}!function(e,n){var t=new Map,o=0,i=0,l=function(l){if(Object.prototype.hasOwnProperty.call(e,l)){var r=e[l].map((function(e){return{id:e.template_id,name:e.template_name,createdBy:e.username,lastChanged:e.changed_on,describe:e.describe,help_url:e.help_url,templte_ui_config:e.templte_ui_config,templte_common_ui_config:n,args:{},expand:{},template:e.template_name,"template-group":l,color:bn[i%bn.length]}}));o+=r.length,t.set(l,{id:l,title:l,children:r}),i+=1}};for(var r in e)l(r);console.log(t),C(o),h(t)}(i,n);var a=Date.now();Oe.set("ft_job_template_common",{update:a,value:n,expire:864e5}),Oe.set("ft_job_template",{update:a,value:i,expire:864e5})}})).catch((function(n){var t,o;n.response&&e((0,D.L$)({msg:null===(t=n.response)||void 0===t||null===(o=t.data)||void 0===o?void 0:o.message}))})).finally((function(){I(!1)}))});return(0,o.useEffect)((function(){P()}),[]),(0,j.jsxs)(vn,{shrink:!0,children:[(0,j.jsx)("div",{className:n?gn.showModuleTree:gn.hideModuleTree,children:(0,j.jsxs)("div",{className:gn.treeContainer,children:[(0,j.jsxs)(i.K,{horizontal:!0,horizontalAlign:"space-between",children:[(0,j.jsx)(ln.R,{placeholder:F("\u641c\u7d22\u6a21\u677f\u540d\u79f0\u6216\u63cf\u8ff0"),role:"search",className:gn.searchBoxStyle,onChange:g((function(e,n){var t=new Map;f.forEach((function(e,o){var i=e.children;i.length>0&&i.forEach((function(e){(e.name.indexOf(n)>-1||e.describe.indexOf(n)>-1)&&(t.has(o)?t.set(o,t.get(o).concat(e)):t.set(o,[e]))}))})),W(t),H(!0)}),1e3),onBlur:function(){setTimeout((function(){H(!1)}),300)}}),(0,j.jsx)(rn.U,{className:gn.searchCallout,isBeakVisible:!1,preventDismissOnLostFocus:!0,hidden:!B,calloutMaxHeight:300,target:".ms-SearchBox",children:(0,j.jsx)(i.K,{className:gn.searchListStyle,children:Array.from(M.keys()).map((function(e){var n=M.get(e);return(0,j.jsx)(o.Fragment,{children:null===n||void 0===n?void 0:n.map((function(n){return(0,j.jsx)(mn,{model:n,onClick:function(){console.log(d),d.has(e)||(d.add(e),c(new Set(d)))}},n.id)}))},e)}))})})]}),(0,j.jsxs)("div",{className:gn.summaryStyle,children:[(0,j.jsxs)(an.x,{children:[w," assets in total"]}),(0,j.jsx)(sn.k,{children:(0,j.jsx)(r.h,{iconProps:{iconName:"Refresh"},onClick:function(){_||P()}})})]}),(0,j.jsx)("div",{className:gn.moduleTreeStyle,children:(0,j.jsx)("div",{className:gn.moduleTreeBody,children:_?(0,j.jsx)(i.K,{className:gn.spinnerContainer,children:(0,j.jsx)(Z.$,{size:L.E.large,label:"Loading"})}):(0,j.jsx)("ul",{className:gn.moduleListStyle,children:Array.from(f.keys()).map((function(e){var n,t=f.get(e);return(0,j.jsxs)("li",{className:gn.moduleListItem,children:[(0,j.jsx)("div",{role:"button",onClick:function(){d.has(e)?d.delete(e):d.add(e),c(new Set(d))},children:(0,j.jsxs)("div",{className:gn.itemFolderNode,children:[(0,j.jsx)(a.J,{iconName:d.has(e)?"FlickUp":"FlickLeft",styles:{root:{alignItems:d.has(e)?"baseline":"center"}},className:gn.listIconStyle}),t.title]})}),d.has(e)?(0,j.jsx)("ul",{role:"group",style:{paddingLeft:"0px"},children:null===(n=t.children)||void 0===n?void 0:n.map((function(e){return(0,j.jsx)("li",{className:gn.moduleListItem,children:(0,j.jsx)("div",{role:"button",children:(0,j.jsx)(fn,{model:e})})},e.id)}))}):null]},e)}))})})})]})}),(0,j.jsx)(un,{})]})},jn={root:{width:"100%",height:"100%",overflow:"hidden"}},wn=function(){var e=m(S.Xd),n=(0,y.$G)(),t=n.t;n.i18n;return(0,j.jsxs)(i.K,{className:"app-container",horizontal:!0,styles:jn,children:[e?(0,j.jsx)("div",{style:{width:"100vw",height:"100vh",position:"absolute",backgroundColor:"rgba(0,0,0,0.5)",zIndex:99,display:"flex",alignItems:"center",justifyContent:"center",color:"#fff",cursor:"pointer",fontSize:20},onClick:function(){window.location.reload()},children:t("\u89e3\u6790\u5f02\u5e38\uff0c\u8bf7\u70b9\u51fb\u5237\u65b0\u91cd\u8bd5")}):null,(0,j.jsx)(yn,{}),(0,j.jsx)(on,{}),(0,j.jsx)(w,{})]})}}}]);