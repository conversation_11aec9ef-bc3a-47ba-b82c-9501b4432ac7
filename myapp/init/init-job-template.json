{"自定义镜像": {"project_name": "基础命令", "image_name": "ubuntu:20.04", "gitpath": "", "image_describe": "开源ubuntu:20.04基础镜像", "job_template_name": "自定义镜像", "job_template_describe": "使用用户自定义镜像作为运行镜像", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像"}, "job_template_args": {"任务": {"images": {"type": "str", "item_type": "str", "label": "要调试的镜像", "require": 1, "choice": [], "range": "", "default": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "placeholder": "", "describe": "要调试的镜像，<a target='_blank' href='https://github.com/data-infra/cube-studio/tree/main/images'>基础镜像参考<a>", "editable": 1}, "workdir": {"type": "str", "item_type": "str", "label": "启动目录", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/", "placeholder": "", "describe": "启动目录", "editable": 1}, "command": {"type": "str", "item_type": "str", "label": "启动命令", "require": 1, "choice": [], "range": "", "default": "echo 'test'", "placeholder": "", "describe": "启动命令。对于镜像中不包含的环境，可以在启动脚本中现场安装", "editable": 1}}}}, "logical": {"project_name": "基础命令", "image_name": "python:3.9", "gitpath": "", "image_describe": "python3.9", "job_template_name": "logical", "job_template_describe": "任务流量的逻辑节点(todo)", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像"}, "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=1\nTASK_RESOURCE_MEMORY=1G\nTASK_RESOURCE_GPU=0", "job_template_args": {}}, "python": {"project_name": "基础命令", "image_name": "python:3.9", "gitpath": "", "image_describe": "python3.9基础镜像", "job_template_name": "python", "job_template_describe": "python代码执行(todo)", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像"}, "job_template_args": {}}, "drop-duplicates": {"project_name": "特征处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/data-process:********", "gitpath": "/job-template/job/data-process", "image_describe": "去除重复样本", "job_template_name": "drop-duplicates", "job_template_describe": "去除重复样本(todo)", "job_template_command": "python3 launcher.py --process_type drop_duplicates", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "drop-missing": {"project_name": "特征处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/data-process:********", "gitpath": "/job-template/job/data-process", "image_describe": "删除缺失率过高的值", "job_template_name": "drop-missing", "job_template_describe": "删除缺失率过高的值(todo)", "job_template_command": "python3 launcher.py --process_type drop_high_missing", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "drop-stablize": {"project_name": "特征处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/data-process:********", "gitpath": "/job-template/job/data-process", "image_describe": "去除值过于单一的变量", "job_template_name": "drop-stablize", "job_template_describe": "去除值过于单一的变量(todo)", "job_template_command": "python3 launcher.py --process_type drop_stablize", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "fill-missing": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/data-process", "image_describe": "填充缺失值", "job_template_name": "fill-missing", "job_template_describe": "填充缺失值(todo)", "job_template_command": "python3 launcher.py --process_type fill_missing", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 4, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "one-hot": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/data-process", "image_describe": "枚举类one hot展开", "job_template_name": "one-hot", "job_template_version": "Alpha", "job_template_describe": "枚举类one hot展开(todo)", "job_template_command": "python lunacher.py --process_type one_hot", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "calculate-metric": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/data-process", "image_describe": "获取变量的统计量", "job_template_name": "calculate-metric", "job_template_describe": "获取变量的统计量(todo)", "job_template_command": "python3 launcher.py --process_type calculate_metric", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "outlier-detection": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/data-process", "image_describe": "异常值检测", "job_template_name": "outlier-detection", "job_template_describe": "异常值检测(todo)", "job_template_command": "python launcher.py --process_type outlier_detection", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "pca": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/data-process", "image_describe": "pca降维", "job_template_name": "pca", "job_template_describe": "pca降维(todo)", "job_template_version": "Alpha", "job_template_command": "python launcher.py --process_type pca", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 14, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/data-process"}, "job_template_args": {}}, "calculate-correlation": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process", "image_describe": "计算列的相关性计算", "job_template_name": "calculate-correlation", "job_template_describe": "特征向量间的相关性计算(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 6, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process"}, "job_template_args": {}}, "feature-combine": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-combine", "image_describe": "特征组合", "job_template_name": "feature-combine", "job_template_describe": "特征组合，用于衍生特征(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-combine"}, "job_template_args": {}}, "feature-importance": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-importance", "image_describe": "特征重要性", "job_template_name": "feature-importance", "job_template_describe": "特征重要性，通过随机森林、逻辑回归、xgboost等模型计算特征重要性，可计算特征的iv值、互信息值、方差等(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 6, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-importance"}, "job_template_args": {}}, "hadamard-multiply": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/hadamard-multiply", "image_describe": "hadamard乘积", "job_template_name": "hadamard-multiply", "job_template_describe": "hadamard乘积(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/hadamard-multiply"}, "job_template_args": {}}, "union-join-data": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/union-join-data", "image_describe": "数据合并", "job_template_name": "union-join-data", "job_template_describe": "数据合并，包含union、join操作(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/union-join-data"}, "job_template_args": {}}, "run-sql": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "run-sql", "job_template_describe": "执行sql(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "sort": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "sort", "job_template_describe": "排序(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "index-process": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "index-process", "job_template_describe": "索引处理，包含增加索引、索引转列、列索引重命名(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "objective-process": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "objective-process", "job_template_describe": "非数值型变量处理，包括hash、根据统计量转换、one-hot(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "data-tranform": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "data-tranform", "job_template_describe": "数据变换，包括boxcox转换、二值化、数据类型转换、dct变换、根据函数转换、ma移动平均、多项式展开(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "data-split": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "data-split", "job_template_describe": "数据拆分，包括列内拆分、列间拆分、行间拆分、svd奇异值分解(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 7, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "dimension-reduction": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "dimension-reduction", "job_template_describe": "降维，包括pca降维和卡方降维(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 6, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "sample": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "sample", "job_template_describe": "采样，包括随机采样、分层采样、过采样、欠采样(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 8, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "standardize-normalize": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "standardize-normalize", "job_template_describe": "标准化、正则化、归一化，有最大绝对值归一化、最大最小归一化、z_score标准化(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "discretization": {"project_name": "特征处理", "image_name": "python:3.9", "gitpath": "/job-template/job/feature-process-all", "image_describe": "特征处理相关函数", "job_template_name": "discretization", "job_template_describe": "数据离散化，等宽、等频、聚类离散化(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/feature-process-all"}, "job_template_args": {}}, "datax": {"project_name": "数据导入导出", "image_name": "ccr.ccs.tencentyun.com/cube-studio/datax:********", "gitpath": "/job-template/job/datax", "image_describe": "datax异构数据源同步", "job_template_name": "datax", "job_template_describe": "datax异构数据源同步", "job_template_command": "bash start.sh", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/datax"}, "job_template_args": {"数据导入": {"-f": {"type": "str", "item_type": "str", "label": "job.json文件地址，<a target='_blank' href='https://github.com/alibaba/DataX?tab=readme-ov-file#support-data-channels'>书写格式参考</a>", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/ml/mysql-csv.json", "placeholder": "", "describe": "job.json文件地址，<a target='_blank' href='https://github.com/alibaba/DataX?tab=readme-ov-file#support-data-channels'>书写格式参考</a>", "editable": 1}}}}, "datax-import": {"project_name": "数据导入导出", "image_name": "ccr.ccs.tencentyun.com/cube-studio/datax:********", "gitpath": "/job-template/job/datax", "image_describe": "datax mysql postgresql 数据导入", "job_template_name": "datax-import", "job_template_describe": "datax mysql postgresql 数据导入", "job_template_command": "python start.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/datax"}, "job_template_args": {"数据导入": {"--db_type": {"type": "str", "item_type": "str", "label": "数据库类型", "require": 1, "choice": ["mysql", "postgresql", "clickhouse"], "range": "", "default": "mysql", "placeholder": "", "describe": "远程数据库的类型", "editable": 1}, "--host": {"type": "str", "item_type": "str", "label": "数据库地址", "require": 1, "choice": [], "range": "", "default": "mysql-service.infra:3306", "placeholder": "", "describe": "数据库地址", "editable": 1}, "--username": {"type": "str", "item_type": "str", "label": "数据库用户名", "require": 1, "choice": [], "range": "", "default": "root", "placeholder": "", "describe": "数据库用户名", "editable": 1}, "--password": {"type": "str", "item_type": "str", "label": "数据库密码", "require": 1, "choice": [], "range": "", "default": "admin", "placeholder": "", "describe": "数据库密码", "editable": 1}, "--database": {"type": "str", "item_type": "str", "label": "数据库，库名", "require": 1, "choice": [], "range": "", "default": "example", "placeholder": "", "describe": "数据库，库名", "editable": 1}, "--table": {"type": "str", "item_type": "str", "label": "数据库，表名", "require": 1, "choice": [], "range": "", "default": "train", "placeholder": "", "describe": "数据库，表名", "editable": 1}, "--columns": {"type": "str", "item_type": "str", "label": "要导入的列名，逗号分隔", "require": 1, "choice": [], "range": "", "default": "age,duration,campaign,pdays,previous,emp_var_rate,cons_price_idx,cons_conf_idx,euribor3m,nr_employed,y", "placeholder": "", "describe": "要导入的列名，逗号分隔", "editable": 1}, "--save_path": {"type": "str", "item_type": "str", "label": "csv存储目的地", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/ml/data-test.csv", "placeholder": "", "describe": "csv存储目的地", "editable": 1}}}}, "dataset": {"project_name": "数据导入导出", "image_name": "ccr.ccs.tencentyun.com/cube-studio/dataset:********", "gitpath": "/job-template/job/dataset", "image_describe": "数据集导入", "job_template_name": "dataset", "job_template_describe": "数据集导入", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "HF_ENDPOINT=https://hf-mirror.com\nHF_HUB_ENABLE_HF_TRANSFER=1", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/dataset"}, "job_template_args": {"数据集导入": {"--src_type": {"type": "str", "item_type": "str", "label": "数据集的来源", "require": 1, "choice": ["当前平台", "huggingface"], "range": "", "default": "当前平台", "placeholder": "", "describe": "数据集的来源，“当前平台”为本平台“数据集”模块", "editable": 1}, "--name": {"type": "str", "item_type": "str", "label": "数据集的名称", "require": 1, "choice": [], "range": "", "default": "mnist", "placeholder": "", "describe": "数据集的名称", "editable": 1}, "--version": {"type": "str", "item_type": "str", "label": "数据集的版本", "require": 1, "choice": [], "range": "", "default": "1998", "placeholder": "", "describe": "数据集的版本", "editable": 1}, "--partition": {"type": "str", "item_type": "str", "label": "数据集的分区，或者子数据集", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "数据集的分区，或者子数据集", "editable": 1}, "--save_dir": {"type": "str", "item_type": "str", "label": "数据集的保存地址", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/dataset/", "placeholder": "", "describe": "数据集的保存地址", "editable": 1}}}}, "model-download": {"project_name": "数据导入导出", "image_name": "ccr.ccs.tencentyun.com/cube-studio/model_download:********", "gitpath": "/job-template/job/model_download", "image_describe": "模型导入", "job_template_name": "model-download", "job_template_describe": "模型导入", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "HF_ENDPOINT=https://hf-mirror.com\nHF_HUB_ENABLE_HF_TRANSFER=1", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/model_download"}, "job_template_args": {"模型导入": {"--from": {"type": "str", "item_type": "str", "label": "模型来源地", "require": 1, "choice": ["模型管理", "推理服务", "huggingface"], "range": "", "default": "模型管理", "placeholder": "", "describe": "模型来源地", "editable": 1}, "--model_name": {"type": "str", "item_type": "str", "label": "模型名(a-z0-9-字符组成，最长54个字符)", "require": 1, "choice": [], "range": "", "default": "mnist", "placeholder": "", "describe": "模型名", "editable": 1}, "--sub_model_name": {"type": "str", "item_type": "str", "label": "子模型名(a-z0-9-字符组成，最长54个字符)", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "子模型名，对于包含多个子模型的用户填写", "editable": 1}, "--model_version": {"type": "str", "item_type": "str", "label": "模型版本号", "require": 1, "choice": [], "range": "", "default": "v2022.08.01.1", "placeholder": "", "describe": "模型版本号", "editable": 1}, "--model_status": {"type": "str", "item_type": "str", "label": "模型状态", "require": 1, "choice": ["online", "offline", "test"], "range": "", "default": "online", "placeholder": "", "describe": "来源模型的状态，模型来自推理服务时有效", "editable": 1}, "--save_path": {"type": "str", "item_type": "str", "label": "下载保存目录", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/model/", "placeholder": "", "describe": "下载保存目录", "editable": 1}}}}, "hadoop": {"project_name": "数据处理工具", "image_name": "python:3.9", "gitpath": "/job-template/job/hadoop", "image_describe": "hadoop大数据组件客户端", "job_template_name": "hadoop", "job_template_describe": "hadoop大数据组件，hdfs,hbase,sqoop,spark(todo)", "job_template_command": "bash start.sh", "job_template_volume": "", "job_template_account": "", "job_template_expand": {"index": 0, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/hadoop", "HostNetwork": true}, "job_template_env": "FS_DEFAULTFS=hdfs://xx.xx.xx:9000\nYARN_RESOURCEMANAGER_ADDRESS=xx.xx.xx.xx:8032\nHIVE_METASTORE_URIS=thrift://xx.xx.xx.xx:9083", "job_template_args": {"hadoop任务": {"--command": {"type": "str", "item_type": "str", "label": "执行命令", "require": 1, "choice": [], "range": "", "default": "hdfs dfs -ls /", "placeholder": "", "describe": "执行命令。注意：由于大数据组件版本可能存在不一致的情况，这里可能需要基于原始模板构建跟内部大数据系统版本一致的hadoop模板", "editable": 1}}}}, "volcanojob": {"project_name": "数据处理工具", "image_name": "ccr.ccs.tencentyun.com/cube-studio/volcano:********", "gitpath": "/job-template/job/volcano", "image_describe": "有序分布式任务", "job_template_name": "volcanojob", "job_template_describe": "有序分布式任务", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/volcano"}, "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=2\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_args": {"分布式任务": {"--image": {"type": "str", "item_type": "str", "label": "", "require": 1, "choice": [], "range": "", "default": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "placeholder": "", "describe": "worker镜像，直接运行你代码的环境镜像。<a target='_blank' href='https://github.com/data-infra/cube-studio/tree/main/images'>基础镜像</a>", "editable": 1}, "--working_dir": {"type": "str", "item_type": "str", "label": "启动目录", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/volcano/", "placeholder": "", "describe": "启动目录", "editable": 1}, "--command": {"type": "str", "item_type": "str", "label": "启动命令", "require": 1, "choice": [], "range": "", "default": "python demo.py", "placeholder": "", "describe": "启动命令。对于镜像中不包含的环境，可以在启动脚本中现场安装", "editable": 1}, "--num_worker": {"type": "str", "item_type": "str", "label": "占用机器个数", "require": 1, "choice": [], "range": "", "default": "3", "placeholder": "", "describe": "占用机器个数", "editable": 1}}}}, "ray": {"project_name": "数据处理工具", "image_name": "ccr.ccs.tencentyun.com/cube-studio/ray:gpu-********", "gitpath": "https://github.com/data-infra/cube-studio/tree/main/job-template/job/ray", "image_describe": "ray分布式任务", "job_template_name": "ray", "job_template_describe": "python多机分布式任务", "job_template_command": "python launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/ray"}, "job_template_args": {"分布式任务": {"images": {"type": "str", "item_type": "str", "label": "启动镜像", "require": 0, "choice": [], "range": "", "default": "ccr.ccs.tencentyun.com/cube-studio/ray:gpu-********", "placeholder": "", "describe": "启动镜像，基础镜像需为该镜像，可自行添加封装自己的环境", "editable": 1}, "--workdir": {"type": "str", "item_type": "", "label": "工作目录", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/ray/", "placeholder": "", "describe": "分布式任务worker的数量", "editable": 1}, "--init": {"type": "str", "item_type": "str", "label": "每个worker的初始化脚本文件地址，用来安装环境", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "每个worker的初始化脚本文件地址，用来安装环境", "describe": "每个worker的初始化脚本文件地址，用来安装环境", "editable": 1}, "--command": {"type": "str", "item_type": "str", "label": "python启动命令，例如 python3 /mnt/xx/xx.py", "require": 1, "choice": [], "range": "", "default": "python demo.py", "placeholder": "", "describe": "python启动命令，例如 python3 /mnt/xx/xx.py", "editable": 1}, "--num_worker": {"type": "str", "item_type": "", "label": "分布式任务worker的数量", "require": 1, "choice": [], "range": "$min,$max", "default": "3", "placeholder": "", "describe": "分布式任务worker的数量", "editable": 1}}}}, "ray-sklearn": {"project_name": "机器学习框架", "image_name": "ccr.ccs.tencentyun.com/cube-studio/ray-sklearn:********", "gitpath": "/job-template/job/ray_sklearn", "image_describe": "sklearn基于ray的分布式", "job_template_name": "ray-sklearn", "job_template_describe": "sklearn基于ray的分布式", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/ray_sklearn"}, "job_template_args": {"训练": {"--train_csv_file_path": {"type": "str", "item_type": "str", "label": "训练集csv", "require": 0, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/sklearn/ml-label.csv", "placeholder": "", "describe": "训练集csv，逗号分割符，首行是列名", "editable": 1}, "--feature_columns": {"type": "str", "item_type": "str", "label": "特征列，逗号分隔", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "特征列，逗号分隔，空白则认为去除label列外的其他所有列", "editable": 1}, "--label_columns": {"type": "str", "item_type": "str", "label": "label的列名，训练时必填，逗号分割", "require": 0, "choice": [], "range": "", "default": "y", "placeholder": "", "describe": "label的列名，训练时必填，逗号分割", "editable": 1}, "--model_name": {"type": "str", "item_type": "str", "label": "模型名称，必填", "require": 1, "choice": ["GaussianNB", "MultinomialNB", "BernoulliNB", "CategoricalNB", "ComplementNB", "KNeighborsClassifier", "LogisticRegression", "RandomForestClassifier", "DecisionTreeClassifier", "GradientBoostingClassifier", "SVC", "SVR"], "range": "", "default": "DecisionTreeClassifier", "placeholder": "", "describe": "训练用到的模型名称，如LogisticRegression，必填。常用的都支持，要加联系管理员", "editable": 1}, "--model_args_dict": {"type": "json", "item_type": "str", "label": "模型参数", "require": 0, "choice": [], "range": "", "default": {"max_depth": 5, "min_samples_leaf": 10}, "placeholder": "", "describe": "sklearn 模型参数，json格式，默认为空", "editable": 1}}, "模型": {"--model_file_path": {"type": "str", "item_type": "str", "label": "模型文件保存文件名，必填", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/sklearn/model.joblib", "placeholder": "", "describe": "模型文件保存文件名，必填", "editable": 1}}, "推理": {"--predict_csv_file_path": {"type": "str", "item_type": "str", "label": "预测数据集csv", "require": 0, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/sklearn/ml-nolabel.csv", "placeholder": "", "describe": "预测数据集csv，格式和训练集一致，默认为空，需要predict时填", "editable": 1}, "--predict_result_path": {"type": "str", "item_type": "str", "label": "预测结果保存文件名，默认为空，需要predict时填", "require": 0, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/sklearn/result.csv", "placeholder": "", "describe": "预测结果保存文件名，默认为空，需要predict时填", "editable": 1}}, "公共配置": {"--num_worker": {"type": "str", "item_type": "str", "label": "ray worker数量", "require": 1, "choice": [], "range": "", "default": "2", "placeholder": "", "describe": "ray worker数量", "editable": 1}, "--init_file": {"type": "str", "item_type": "str", "label": "初始化sh脚本地址", "require": 1, "choice": [], "range": "3", "default": "/mnt/{{creator}}/pipeline/example/sklearn/init.sh", "placeholder": "", "describe": "初始化sh脚本地址，可用来为每个分布式worker安装环境", "editable": 1}}}}, "adaboost": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/adaboost", "image_describe": "adaboost算法", "job_template_name": "adaboost", "job_template_describe": "adaboost算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/adaboost"}, "job_template_args": {}}, "xgb": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/xgb", "image_describe": "xgb算法单机", "job_template_name": "xgb", "job_template_describe": "xgb算法单机(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/xgb"}, "job_template_args": {}}, "bayesian": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/bayesian", "image_describe": "bayesian算法", "job_template_name": "bayesian", "job_template_describe": "bayesian算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/bayesian"}, "job_template_args": {}}, "decision-tree": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/decision-tree", "image_describe": "decision-tree算法", "job_template_name": "decision-tree", "job_template_describe": "decision-tree算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/decision-tree"}, "job_template_args": {}}, "gbdt": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/gbdt", "image_describe": "gbdt算法", "job_template_name": "gbdt", "job_template_describe": "gbdt算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/gbdt"}, "job_template_args": {}}, "hyperparam-search-ray": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/hyperparameter-search-ray", "image_describe": "超参搜索算法", "job_template_name": "hyperparam-search", "job_template_describe": "超参搜索算法(todo)", "job_template_command": "python launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/hyperparameter-search-ray"}, "job_template_args": {}}, "hyperparam-search-nni": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/hyperparameter-search-nni", "image_describe": "nni超参搜索算法", "job_template_name": "hyperparam-search-nni", "job_template_describe": "超参搜索算法(todo)", "job_template_workdir": "/app", "job_template_command": "python launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/hyperparameter-search-nni", "HostNetwork": true}, "job_template_args": {}}, "kmean": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/kmean", "image_describe": "kmean算法", "job_template_name": "kmean", "job_template_describe": "kmean算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/kmean"}, "job_template_args": {}}, "knn": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/knn", "image_describe": "knn算法", "job_template_name": "knn", "job_template_describe": "knn算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/knn"}, "job_template_args": {}}, "lightgbm": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/lightgbm", "image_describe": "lightgbm算法", "job_template_name": "lightgbm", "job_template_describe": "lightgbm算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/lightgbm"}, "job_template_args": {}}, "lr": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/lr", "image_describe": "lr算法", "job_template_name": "lr", "job_template_describe": "lr算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/lr"}, "job_template_args": {}}, "random-forest": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/random-forest", "image_describe": "random-forest算法", "job_template_name": "random-forest", "job_template_describe": "random-forest算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/random-forest"}, "job_template_args": {}}, "random-forest-regression": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/rf-regression", "image_describe": "random-forest-regression算法", "job_template_name": "random-forest-regression", "job_template_describe": "random-forest-regression算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/rf-regression"}, "job_template_args": {}}, "arima": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/arima", "image_describe": "arima 时间序列算法", "job_template_name": "arima", "job_template_describe": "arima时间序列算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/arima"}, "job_template_args": {}}, "ar": {"project_name": "机器学习算法", "image_name": "python:3.9", "gitpath": "/job-template/job/ar", "image_describe": "ar 时间序列算法", "job_template_name": "ar", "job_template_describe": "ar 时间序列算法(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/ar"}, "job_template_args": {}}, "tfjob": {"project_name": "深度学习", "image_name": "ccr.ccs.tencentyun.com/cube-studio/tf:********", "gitpath": "/job-template/job/tf", "image_describe": "tf 分布式训练", "job_template_name": "tfjob", "job_template_describe": "tf 分布式训练", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=4\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/tf"}, "job_template_args": {"分布式训练": {"--image": {"type": "str", "item_type": "str", "label": "worker镜像，直接运行你代码的环境镜像", "require": 1, "choice": [], "range": "", "default": "kubeflow/tf-mnist-with-summaries:latest", "placeholder": "", "describe": "worker镜像，直接运行你代码的环境镜像 <a target='_blank' href='https://github.com/data-infra/cube-studio/tree/main/images'>基础镜像</a>", "editable": 1}, "--working_dir": {"type": "str", "item_type": "str", "label": "命令的启动目录", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/tf/", "placeholder": "", "describe": "命令的启动目录", "editable": 1}, "--command": {"type": "str", "item_type": "str", "label": "启动命令，例如 python3 xxx.py", "require": 1, "choice": [], "range": "", "default": "python /var/tf_mnist/mnist_with_summaries.py", "placeholder": "启动命令，例如 python3 xxx.py", "describe": "启动命令，例如 python3 xxx.py。对于镜像中不包含的环境，可以在启动后脚本内自行现场安装", "editable": 1}, "--num_worker": {"type": "str", "item_type": "str", "label": "分布式训练worker的数目", "require": 1, "choice": [], "range": "", "default": "2", "placeholder": "分布式训练worker的数目", "describe": "分布式训练worker的数目", "editable": 1}}}}, "pytorchjob": {"project_name": "深度学习", "image_name": "ccr.ccs.tencentyun.com/cube-studio/pytorch:********", "gitpath": "/job-template/job/pytorch", "image_describe": "pytorch分布式训练", "job_template_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "job_template_describe": "pytorch 分布式训练", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=2\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/pytorch"}, "job_template_args": {"分布式训练": {"--image": {"type": "str", "item_type": "str", "label": "worker镜像，直接运行你代码的环境镜像", "require": 1, "choice": [], "range": "", "default": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "placeholder": "", "describe": "worker镜像，直接运行你代码的环境镜像 <a target='_blank' href='https://github.com/data-infra/cube-studio/tree/main/images'>基础镜像</a>", "editable": 1}, "--working_dir": {"type": "str", "item_type": "str", "label": "命令的启动目录", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/pytorch", "placeholder": "", "describe": "命令的启动目录", "editable": 1}, "--command": {"type": "str", "item_type": "str", "label": "启动命令，例如 python3 xxx.py", "require": 1, "choice": [], "range": "", "default": "bash start.sh", "placeholder": "启动命令，例如 python3 xxx.py", "describe": "启动命令，例如 python3 xxx.py。对于镜像中不包含的环境，可以在启动后脚本内自行现场安装", "editable": 1}, "--num_worker": {"type": "str", "item_type": "str", "label": "分布式训练worker的数目", "require": 1, "choice": [], "range": "", "default": "3", "placeholder": "分布式训练worker的数目", "describe": "分布式训练worker的数目", "editable": 1}}}}, "paddlejob": {"project_name": "深度学习", "image_name": "python:3.9", "gitpath": "/job-template/job/paddle", "image_describe": "paddle分布式训练", "job_template_name": "paddlejob", "job_template_describe": "paddle 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=2\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_expand": {"index": 4, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/paddle"}, "job_template_args": {}}, "mindspore": {"project_name": "深度学习", "image_name": "python:3.9", "gitpath": "/job-template/job/mindspore", "image_describe": "mindspore 分布式训练", "job_template_name": "mindspore", "job_template_describe": "mindspore 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=2\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_expand": {"index": 5, "help_url": "/job-template/job/mindspore"}, "job_template_args": {}}, "mxnet": {"project_name": "深度学习", "image_name": "python:3.9", "gitpath": "/job-template/job/mxnet", "image_describe": "mxnet分布式训练", "job_template_name": "mxnet", "job_template_describe": "mxnet 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=2\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_expand": {"index": 7, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/mxnet"}, "job_template_args": {}}, "horovod": {"project_name": "分布式加速", "image_name": "python:3.9", "gitpath": "/job-template/job/horovod", "image_describe": "horovod 分布式训练", "job_template_name": "horovod", "job_template_describe": "horovod 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/horovod"}, "job_template_args": {}}, "megatron": {"project_name": "分布式加速", "image_name": "python:3.9", "gitpath": "/job-template/job/megatron", "image_describe": "megatron 分布式训练", "job_template_name": "megatron", "job_template_describe": "megatron 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/megatron"}, "job_template_args": {}}, "deepspeed": {"project_name": "分布式加速", "image_name": "python:3.9", "gitpath": "/job-template/job/deepspeed", "image_describe": "deepspeed 分布式训练", "job_template_name": "deepspeed", "job_template_describe": "deepspeed 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=2\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/deepspeed"}, "job_template_args": {}}, "colossalai": {"project_name": "分布式加速", "image_name": "python:3.9", "gitpath": "/job-template/job/colossalai", "image_describe": "colossalai 分布式训练", "job_template_name": "colossalai", "job_template_describe": "colossalai 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/colossalai"}, "job_template_args": {}}, "mpi": {"project_name": "分布式加速", "image_name": "python:3.9", "gitpath": "/job-template/job/mpi", "image_describe": "mpi 分布式训练", "job_template_name": "mpi", "job_template_describe": "mpi 分布式训练(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/mpi"}, "job_template_args": {}}, "model-evaluation": {"project_name": "模型处理", "image_name": "python:3.9", "gitpath": "/job-template/job/model_evaluation", "image_describe": "模型评估", "job_template_name": "model-evaluation", "job_template_describe": "模型评估(todo)", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/model_evaluation"}, "job_template_args": {}}, "model-convert": {"project_name": "模型处理", "image_name": "python:3.9", "gitpath": "/job-template/job/model_convert", "image_describe": "模型转换", "job_template_name": "model-convert", "job_template_describe": "模型转换(todo)", "job_template_command": "python3 transformer2onnx.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/model_convert"}, "job_template_args": {}}, "model-register": {"project_name": "模型服务化", "image_name": "ccr.ccs.tencentyun.com/cube-studio/model_register:********", "gitpath": "/job-template/job/model_register", "image_describe": "注册模型", "job_template_name": "model-register", "job_template_describe": "注册模型", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/model_register"}, "job_template_args": {"模型注册": {"--project_name": {"type": "str", "item_type": "str", "label": "部署项目名", "require": 1, "choice": [], "range": "", "default": "public", "placeholder": "", "describe": "部署项目名", "editable": 1}, "--model_name": {"type": "str", "item_type": "str", "label": "模型名", "require": 1, "choice": [], "range": "", "default": "mnist", "placeholder": "", "describe": "模型名(a-z0-9-字符组成，最长54个字符)", "editable": 1}, "--model_version": {"type": "str", "item_type": "str", "label": "模型版本号", "require": 1, "choice": [], "range": "", "default": "{{ datetime.datetime.now().strftime('v%Y.%m.%d.1') }}", "placeholder": "", "describe": "模型版本号", "editable": 1}, "--model_path": {"type": "str", "item_type": "str", "label": "模型地址", "require": 1, "choice": [], "range": "", "default": "https://cube-studio.oss-cn-hangzhou.aliyuncs.com/inference/tf-mnist.tar.gz", "placeholder": "", "describe": "模型地址", "editable": 1}, "--model_metric": {"type": "str", "item_type": "str", "label": "模型指标", "require": 0, "choice": [], "range": "", "default": "accuracy:0.8", "placeholder": "", "describe": "模型指标", "editable": 1}, "--describe": {"type": "str", "item_type": "str", "label": "模型描述", "require": 1, "choice": [], "range": "", "default": "mnist模型", "placeholder": "", "describe": "模型描述", "editable": 1}, "--framework": {"type": "str", "item_type": "str", "label": "模型框架", "require": 1, "choice": ["sklearn", "xgb", "tf", "pytorch", "onnx", "tensorrt", "<PERSON><PERSON><PERSON>"], "range": "", "default": "tf", "placeholder": "", "describe": "模型框架", "editable": 1}, "--inference_framework": {"type": "str", "item_type": "str", "label": "推理框架", "require": 1, "choice": ["serving", "ml-server", "tfserving", "torch-server", "onnxruntime", "triton-server", "vllm", "<PERSON><PERSON><PERSON>"], "range": "", "default": "tfserving", "placeholder": "", "describe": "推理框架", "editable": 1}}}}, "model-offline-predict": {"project_name": "模型服务化", "image_name": "ccr.ccs.tencentyun.com/cube-studio/offline-predict:********", "gitpath": "/job-template/job/model_offline_predict", "image_describe": "分布式离线推理", "job_template_name": "model-offline-predict", "job_template_describe": "分布式离线推理", "job_template_command": "python3 launcher-rabbitmq.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_env": "NO_RESOURCE_CHECK=true\nTASK_RESOURCE_CPU=4\nTASK_RESOURCE_MEMORY=4G\nTASK_RESOURCE_GPU=0", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/model_offline_predict"}, "job_template_args": {"模型离线推理": {"--image": {"type": "str", "item_type": "str", "label": "", "require": 1, "choice": [], "range": "", "default": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9-dev", "placeholder": "", "describe": "worker镜像，直接运行你代码的环境镜像<a target='_blank' href='https://github.com/data-infra/cube-studio/tree/main/images'>基础镜像</a>", "editable": 1}, "--working_dir": {"type": "str", "item_type": "str", "label": "启动目录", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/offline-inference/", "placeholder": "", "describe": "启动目录", "editable": 1}, "--command": {"type": "str", "item_type": "str", "label": "环境安装和任务启动命令", "require": 1, "choice": [], "range": "", "default": "bash start.sh", "placeholder": "", "describe": "环境安装和任务启动命令", "editable": 1}, "--num_worker": {"type": "str", "item_type": "str", "label": "占用机器个数", "require": 1, "choice": [], "range": "", "default": "3", "placeholder": "", "describe": "占用机器个数", "editable": 1}}}}, "deploy-service": {"project_name": "模型服务化", "image_name": "ccr.ccs.tencentyun.com/cube-studio/deploy-service:********", "gitpath": "/job-template/job/deploy-service", "image_describe": "模型部署推理服务", "job_template_name": "deploy-service", "job_template_describe": "模型部署推理服务", "job_template_command": "python3 launcher.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/deploy-service"}, "job_template_args": {"模型信息": {"--project_name": {"type": "str", "item_type": "str", "label": "项目组名称", "require": 1, "choice": [], "range": "", "default": "public", "placeholder": "", "describe": "项目组名称", "editable": 1}, "--label": {"type": "str", "item_type": "str", "label": "中文描述描述", "require": 1, "choice": [], "range": "", "default": "demo推理服务", "placeholder": "", "describe": "推理服务描述", "editable": 1}, "--model_name": {"type": "str", "item_type": "str", "label": "模型名", "require": 1, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "模型名", "editable": 1}, "--model_version": {"type": "str", "item_type": "str", "label": "模型版本号", "require": 1, "choice": [], "range": "", "default": "v2022.10.01.1", "placeholder": "", "describe": "模型版本号", "editable": 1}, "--model_path": {"type": "str", "item_type": "str", "label": "模型地址", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "模型地址", "editable": 1}}, "部署信息": {"--service_type": {"type": "str", "item_type": "str", "label": "推理服务类型", "require": 1, "choice": ["serving", "ml-server", "tfserving", "torch-server", "onnxruntime", "triton-server", "vllm"], "range": "", "default": "service", "placeholder": "", "describe": "推理服务类型", "editable": 1}, "--images": {"type": "str", "item_type": "str", "label": "推理服务镜像", "require": 1, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "推理服务镜像", "editable": 1}, "--working_dir": {"type": "str", "item_type": "str", "label": "推理容器工作目录", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "推理容器工作目录,个人工作目录/mnt/$username", "editable": 1}, "--command": {"type": "str", "item_type": "str", "label": "推理容器启动命令", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "推理容器启动命令", "editable": 1}, "--env": {"type": "text", "item_type": "str", "label": "推理容器环境变量", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "推理容器环境变量", "editable": 1}, "--host": {"type": "str", "item_type": "str", "label": "部署域名", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "部署域名，留空自动生成", "editable": 1}, "--ports": {"type": "str", "item_type": "str", "label": "推理容器暴露端口", "require": 0, "choice": [], "range": "", "default": "80", "placeholder": "", "describe": "推理容器暴露端口", "editable": 1}, "--replicas": {"type": "str", "item_type": "str", "label": "pod副本数", "require": 1, "choice": [], "range": "", "default": "1", "placeholder": "", "describe": "pod副本数", "editable": 1}, "--resource_memory": {"type": "str", "item_type": "str", "label": "每个pod占用内存", "require": 1, "choice": [], "range": "", "default": "2G", "placeholder": "", "describe": "每个pod占用内存", "editable": 1}, "--resource_cpu": {"type": "str", "item_type": "str", "label": "每个pod占用cpu", "require": 1, "choice": [], "range": "", "default": "2", "placeholder": "", "describe": "每个pod占用cpu", "editable": 1}, "--resource_gpu": {"type": "str", "item_type": "str", "label": "每个pod占用gpu", "require": 1, "choice": [], "range": "", "default": "0", "placeholder": "", "describe": "每个pod占用gpu", "editable": 1}, "--volume_mount": {"type": "str", "item_type": "str", "label": "挂载", "require": 0, "choice": [], "range": "", "default": "kubeflow-user-workspace(pvc):/mnt", "placeholder": "", "describe": "容器的挂载，支持pvc/hostpath/configmap三种形式,格式示例:$pvc_name1(pvc):/$container_path1,$hostpath1(hostpath):/$container_path2,注意pvc会自动挂载对应目录下的个人rtx子目录", "editable": 1}, "--inference_config": {"type": "text", "item_type": "str", "label": "配置文件", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "会配置文件的形式挂载到容器/config/目录下。<font color='#FF0000'>留空时将被自动重置</font>，格式：<br>---文件名<br>多行文件内容<br>---文件名<br>多行文件内容", "editable": 1}, "--metrics": {"type": "str", "item_type": "str", "label": "指标采集接口", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "请求指标采集，配置端口+url，示例：8080:/metrics", "editable": 1}, "--health": {"type": "str", "item_type": "str", "label": "健康检查", "require": 0, "choice": [], "range": "", "default": "", "placeholder": "", "describe": "健康检查接口，使用http接口或者shell命令，示例：8080:/health或者 shell:python health.py", "editable": 1}}}}, "media-download": {"project_name": "多媒体类模板", "image_name": "ccr.ccs.tencentyun.com/cube-studio/video-audio:********", "gitpath": "/job-template/job/video-audio", "image_describe": "分布式媒体文件处理", "job_template_name": "media-download", "job_template_describe": "分布式下载媒体文件", "job_template_command": "python start_download.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/video-audio"}, "job_template_args": {"模型下载": {"--num_worker": {"type": "str", "item_type": "str", "label": "分布式任务的worker数目", "require": 1, "choice": [], "range": "", "default": "3", "placeholder": "分布式任务的worker数目", "describe": "分布式任务的worker数目", "editable": 1}, "--download_type": {"type": "enum", "item_type": "str", "label": "下载类型", "require": 1, "choice": ["url"], "range": "", "default": "url", "placeholder": "", "describe": "下载类型", "editable": 1}, "--input_file": {"type": "str", "item_type": "str", "label": "配置文件地址", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/media/video_url.txt", "placeholder": "", "describe": "配置文件地址<br>url类型，每行格式：$url $local_path", "editable": 1}}}}, "video-img": {"project_name": "多媒体类模板", "image_name": "ccr.ccs.tencentyun.com/cube-studio/video-audio:********", "gitpath": "/job-template/job/video-audio", "image_describe": "分布式媒体文件处理", "job_template_name": "video-img", "job_template_describe": "视频提取图片(分布式版)", "job_template_command": "python start_video_img.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/video-audio"}, "job_template_args": {"视频抽帧": {"--num_workers": {"type": "str", "item_type": "str", "label": "", "require": 1, "choice": [], "range": "", "default": "3", "placeholder": "", "describe": "worker数量", "editable": 1}, "--input_file": {"type": "str", "item_type": "str", "label": "", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/media/video_image.txt", "placeholder": "配置文件地址，每行格式：<br>$local_video_path $des_img_dir $frame_rate", "describe": "配置文件地址，每行格式：<br>$local_video_path $des_img_dir $frame_rate", "editable": 1}}}}, "video-audio": {"project_name": "多媒体类模板", "image_name": "ccr.ccs.tencentyun.com/cube-studio/video-audio:********", "gitpath": "/job-template/job/video-audio", "image_describe": "分布式媒体文件处理", "job_template_name": "video-audio", "job_template_describe": "视频提取音频(分布式版)", "job_template_command": "python start_video_audio.py", "job_template_volume": "", "job_template_account": "kubeflow-pipeline", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/video-audio"}, "job_template_args": {"提取音频": {"--num_workers": {"type": "str", "item_type": "str", "label": "worker数量", "require": 1, "choice": [], "range": "", "default": "3", "placeholder": "", "describe": "worker数量", "editable": 1}, "--input_file": {"type": "str", "item_type": "str", "label": "", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/pipeline/example/media/video_audio.txt", "placeholder": "", "describe": "配置文件地址，每行格式：<br>$local_video_path $des_audio_path", "editable": 1}}}}, "yolov7": {"project_name": "机器视觉", "image_name": "ccr.ccs.tencentyun.com/cube-studio/yolov7:2024.01", "gitpath": "/job-template/job/yolov7", "image_describe": "yolo目标识别", "job_template_name": "yolov7", "job_template_describe": "yolo目标识别", "job_template_workdir": "/yolov7", "job_template_command": "bash launcher.sh", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 4, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/yolov7"}, "job_template_args": {"训练参数": {"--train": {"type": "str", "item_type": "str", "label": "训练数据集", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/coco_data_sample/train.txt", "placeholder": "", "describe": "训练数据集，txt配置地址", "editable": 1}, "--val": {"type": "str", "item_type": "str", "label": "验证数据集", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/coco_data_sample/valid.txt", "placeholder": "", "describe": "验证数据集，txt配置地址", "editable": 1}, "--classes": {"type": "text", "item_type": "str", "label": "目标分类", "require": 1, "choice": [], "range": "", "default": "person,bicycle,car,motorcycle,airplane,bus,train,truck,boat,trafficlight,firehydrant,stopsign,parkingmeter,bench,bird,cat,dog,horse,sheep,cow,elephant,bear,zebra,giraffe,backpack,umbrella,handbag,tie,suitcase,frisbee,skis,snowboard,sportsball,kite,baseballbat,baseballglove,skateboard,surfboard,tennisracket,bottle,wineglass,cup,fork,knife,spoon,bowl,banana,apple,sandwich,orange,broccoli,carrot,hotdog,pizza,donut,cake,chair,couch,pottedplant,bed,diningtable,toilet,tv,laptop,mouse,remote,keyboard,cellphone,microwave,oven,toaster,sink,refrigerator,book,clock,vase,scissors,teddybear,hairdrier,toothbrush", "placeholder": "", "describe": "目标分类,逗号分割", "editable": 1}, "--batch_size": {"type": "str", "item_type": "str", "label": "batch-size", "require": 1, "choice": [], "range": "", "default": "1", "placeholder": "", "describe": "batch-size", "editable": 1}, "--epoch": {"type": "str", "item_type": "str", "label": "epoch", "require": 1, "choice": [], "range": "", "default": "1", "placeholder": "", "describe": "epoch", "editable": 1}, "--weights": {"type": "str", "item_type": "str", "label": "权重文件", "require": 1, "choice": [], "range": "", "default": "/yolov7/weights/yolov7_training.pt", "placeholder": "", "describe": "权重文件", "editable": 1}, "--save_model_path": {"type": "str", "item_type": "str", "label": "模型保存地址", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/coco_data_sample/yolov7_best.pt", "placeholder": "", "describe": "模型保存地址", "editable": 1}}}}, "yolov8": {"project_name": "机器视觉", "image_name": "ccr.ccs.tencentyun.com/cube-studio/yolov8:2024.10", "gitpath": "/job-template/job/yolov8", "image_describe": "yolo目标识别", "job_template_name": "yolov8", "job_template_describe": "yolo目标识别", "job_template_workdir": "/yolov8", "job_template_command": "python train.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 4, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/yolov8"}, "job_template_args": {"训练参数": {"--train": {"type": "str", "item_type": "str", "label": "训练数据集", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/coco_data_sample/train.txt", "placeholder": "", "describe": "训练数据集，txt配置地址", "editable": 1}, "--val": {"type": "str", "item_type": "str", "label": "验证数据集", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/coco_data_sample/valid.txt", "placeholder": "", "describe": "验证数据集，txt配置地址", "editable": 1}, "--classes": {"type": "text", "item_type": "str", "label": "目标分类", "require": 1, "choice": [], "range": "", "default": "person,bicycle,car,motorcycle,airplane,bus,train,truck,boat,trafficlight,firehydrant,stopsign,parkingmeter,bench,bird,cat,dog,horse,sheep,cow,elephant,bear,zebra,giraffe,backpack,umbrella,handbag,tie,suitcase,frisbee,skis,snowboard,sportsball,kite,baseballbat,baseballglove,skateboard,surfboard,tennisracket,bottle,wineglass,cup,fork,knife,spoon,bowl,banana,apple,sandwich,orange,broccoli,carrot,hotdog,pizza,donut,cake,chair,couch,pottedplant,bed,diningtable,toilet,tv,laptop,mouse,remote,keyboard,cellphone,microwave,oven,toaster,sink,refrigerator,book,clock,vase,scissors,teddybear,hairdrier,toothbrush", "placeholder": "", "describe": "目标分类,逗号分割", "editable": 1}, "--batch_size": {"type": "str", "item_type": "str", "label": "batch-size", "require": 1, "choice": [], "range": "", "default": "1", "placeholder": "", "describe": "batch-size", "editable": 1}, "--epoch": {"type": "str", "item_type": "str", "label": "epoch", "require": 1, "choice": [], "range": "", "default": "1", "placeholder": "", "describe": "epoch", "editable": 1}, "--weights": {"type": "str", "item_type": "str", "label": "权重文件", "require": 1, "choice": [], "range": "", "default": "/yolov8/yolov8n.pt", "placeholder": "", "describe": "权重文件，包含yolov8n.pt,yolov8s.pt,yolov8m.pt,yolov8l.pt,yolov8x.pt，或使用自己微调后的模型", "editable": 1}, "--save_model_path": {"type": "str", "item_type": "str", "label": "模型保存地址", "require": 1, "choice": [], "range": "", "default": "/mnt/{{creator}}/coco_data_sample/yolov8_best.pt", "placeholder": "", "describe": "模型保存地址", "editable": 1}}}}, "chatglm4": {"project_name": "大模型", "image_name": "python:3.9", "gitpath": "/job-template/job/llama_factory", "image_describe": "chatglm4", "job_template_name": "chatglm4", "job_template_describe": "chatglm4 lora微调(todo)", "job_template_version": "Release", "job_template_workdir": "/root/llama_factory", "job_template_command": "python start.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "HF_ENDPOINT=https://hf-mirror.com", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/llama_factory"}, "job_template_args": {}}, "deepseek": {"project_name": "大模型", "image_name": "python:3.9", "gitpath": "/job-template/job/llama_factory", "image_describe": "deepseek", "job_template_name": "deepseek", "job_template_describe": "deepseek 微调(todo)", "job_template_version": "Release", "job_template_workdir": "/root/llama_factory", "job_template_command": "python start.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "HF_ENDPOINT=https://hf-mirror.com", "job_template_expand": {"index": 4, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/llama_factory"}, "job_template_args": {}}, "llama3": {"project_name": "大模型", "image_name": "python:3.9", "gitpath": "https://github.com/data-infra/cube-studio/tree/main/job-template/job/llama3", "image_describe": "llama3", "job_template_name": "llama3", "job_template_describe": "llama3 lora微调(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "xxxx ", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5}, "job_template_args": {}}, "baichuan2": {"project_name": "大模型", "image_name": "python:3.9", "gitpath": "/job-template/job/baichuan2", "image_describe": "baichuan2", "job_template_name": "baichuan2", "job_template_describe": "baichuan2 微调(todo)", "job_template_version": "Release", "job_template_workdir": "/root/baichuan2/fine-tune/", "job_template_command": "deepspeed --hostfile='' fine-tune.py --report_to 'none'  --save_strategy epoch --lr_scheduler_type constant ", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/baichuan2"}, "job_template_args": {}}, "qwen2": {"project_name": "大模型", "image_name": "ccr.ccs.tencentyun.com/cube-studio/llama-factory:********", "gitpath": "/job-template/job/llama_factory", "image_describe": "qwen2", "job_template_name": "qwen2", "job_template_describe": "qwen2 lora微调(todo)", "job_template_version": "Release", "job_template_workdir": "/root/llama_factory", "job_template_command": "python start.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "HF_ENDPOINT=https://hf-mirror.com", "job_template_expand": {"index": 6, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/llama_factory"}, "job_template_args": {}}, "llama-factory": {"project_name": "大模型", "image_name": "python:3.9", "gitpath": "/job-template/job/llama_factory", "image_describe": "llama-factory", "job_template_name": "llama-factory", "job_template_describe": "llama-factory 100+llm大模型微调(todo)", "job_template_version": "Release", "job_template_workdir": "/root/llama_factory", "job_template_command": "python start.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "HF_ENDPOINT=https://hf-mirror.com", "job_template_expand": {"index": 7, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/llama_factory"}, "job_template_args": {}}, "nlp-clean-data": {"project_name": "文本处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/nlp-process:********", "gitpath": "/job-template/job/nlp-process", "image_describe": "文本算法类的数据处理", "job_template_name": "nlp-clean-data", "job_template_describe": "异常数据清洗(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python clean-data.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/nlp-process"}, "job_template_args": {}}, "nlp-filter-data": {"project_name": "文本处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/nlp-process:********", "gitpath": "/job-template/job/nlp-process", "image_describe": "文本算法类的数据处理", "job_template_name": "nlp-filter-data", "job_template_describe": "过滤异常文档(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python filter-data.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/nlp-process"}, "job_template_args": {}}, "nlp-replace-private-data": {"project_name": "文本处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/nlp-process:********", "gitpath": "/job-template/job/nlp-process", "image_describe": "文本算法类的数据处理", "job_template_name": "nlp-replace-private-data", "job_template_describe": "去除隐私信息(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python replace-data.py", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/nlp-process"}, "job_template_args": {}}, "vision-image-blur": {"project_name": "图像处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/vision-process:********", "gitpath": "/job-template/job/vision-process", "image_describe": "视觉算法类的数据处理", "job_template_name": "vision-image-blur", "job_template_describe": "图片去噪声(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python launcher.py --deal_type blur", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 1, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/vision-process"}, "job_template_args": {}}, "vision-image-resize": {"project_name": "图像处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/vision-process:********", "gitpath": "/job-template/job/vision-process", "image_describe": "视觉算法类的数据处理", "job_template_name": "vision-image-resize", "job_template_describe": "图片缩放(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python launcher.py --deal_type resize", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 2, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/vision-process"}, "job_template_args": {}}, "vision-image-normalize": {"project_name": "图像处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/vision-process:********", "gitpath": "/job-template/job/vision-process", "image_describe": "视觉算法类的数据处理", "job_template_name": "vision-image-normalize", "job_template_describe": "图片标准化(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python launcher.py --deal_type normalize", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 3, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/vision-process"}, "job_template_args": {}}, "vision-image-cropping": {"project_name": "图像处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/vision-process:********", "gitpath": "/job-template/job/vision-process", "image_describe": "视觉算法类的数据处理", "job_template_name": "vision-image-cropping", "job_template_describe": "图片裁剪(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python launcher.py --deal_type cropping", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 4, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/vision-process"}, "job_template_args": {}}, "vision-image-equalize": {"project_name": "图像处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/vision-process:********", "gitpath": "/job-template/job/vision-process", "image_describe": "视觉算法类的数据处理", "job_template_name": "vision-image-equalize", "job_template_describe": "图片均衡化，增强对比度(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python launcher.py --deal_type equalize", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 5, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/vision-process"}, "job_template_args": {}}, "vision-image-cvtcolor": {"project_name": "图像处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/vision-process:********", "gitpath": "/job-template/job/vision-process", "image_describe": "视觉算法类的数据处理", "job_template_name": "vision-image-cvtcolor", "job_template_describe": "图片的空间转换(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python launcher.py --deal_type cvtcolor", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 6, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/vision-process"}, "job_template_args": {}}, "vision-image-augmentation": {"project_name": "图像处理", "image_name": "ccr.ccs.tencentyun.com/cube-studio/vision-process:********", "gitpath": "/job-template/job/vision-process", "image_describe": "视觉算法类的数据处理", "job_template_name": "vision-image-augmentation", "job_template_describe": "图片变换，图像增强(todo)", "job_template_version": "Release", "job_template_workdir": "", "job_template_command": "python launcher.py --deal_type augmentation", "job_template_volume": "", "job_template_account": "", "job_template_env": "", "job_template_expand": {"index": 7, "rec_job_template": "自定义镜像", "help_url": "/job-template/job/vision-process"}, "job_template_args": {}}}