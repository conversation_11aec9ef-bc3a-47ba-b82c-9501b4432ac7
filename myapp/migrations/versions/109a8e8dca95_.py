"""empty message

Revision ID: 109a8e8dca95
Revises: 444d4749dfa1
Create Date: 2021-11-25 08:31:13.167976

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '109a8e8dca95'
down_revision = '444d4749dfa1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('docker', sa.Column('project_id', sa.Integer(), nullable=False))
    op.create_foreign_key(None, 'docker', 'project', ['project_id'], ['id'])
    op.add_column('notebook', sa.Column('expand', sa.Text(length=65536), nullable=True))
    op.drop_index('name', table_name='project')
    op.create_unique_constraint(None, 'project', ['name', 'type'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'project', type_='unique')
    op.create_index('name', 'project', ['name'], unique=True)
    op.drop_column('notebook', 'expand')
    op.drop_constraint(None, 'docker', type_='foreignkey')
    op.drop_column('docker', 'project_id')
    # ### end Alembic commands ###
