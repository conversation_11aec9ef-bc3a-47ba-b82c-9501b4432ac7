{% import 'appbuilder/general/lib.html' as lib %}
{% include 'appbuilder/general/confirm.html' %}
{% include 'appbuilder/general/alert.html' %}

{% if fieldsets %}

    {% for fieldset_item in fieldsets %}
        {% if fieldset_item[1].get('expanded') == None %}
            {% set expanded = True %}
        {% else %}
            {% set expanded = fieldset_item[1].get('expanded') %}
        {% endif %}
        {% call lib.accordion_tag(loop.index,fieldset_item[0], expanded) %}
        <div class="table-responsive">
        <table class="table table-bordered">
        {% for item in fieldset_item[1].get('fields') %}
            <tr>
            <th class="col-lg-2 col-md-2 col-sm-2">{{label_columns.get(item)}}</th>
            <td><span style="white-space: pre-line">{{value_columns[include_columns.index(item)]}}</span></td>
            </tr>
        {% endfor %}
        </table></div>
        {% endcall %}
    {% endfor %}

{% else %}
    <div class="table-responsive">
    <table class="table table-bordered">

    {% for item in include_columns %}
        <tr>
            <th class="col-lg-2 col-md-2 col-sm-2">{{label_columns.get(item)}}</th>
            <td>
                {% set formatter = formatters_columns.get(item) %}
                {% set v = value_columns[loop.index-1]%}
                <span style="white-space: pre-line">{{formatter(v) if formatter else v}}</span>
            </td>
        </tr>
    {% endfor %}
    </table></div>
{% endif %}


<div class="well well-sm">
    {{ lib.render_action_links(actions, pk, modelview_name) }}
<!--    {{ lib.lnk_back() }}-->
</div>
