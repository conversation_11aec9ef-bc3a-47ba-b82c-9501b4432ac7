from flask import jsonify
from myapp.proxy.proxy import proxy_bp
from flask import request
from myapp.exceptions import MyappException
import requests
import os
from myapp.proxy.common import HyperParamsKind

# 访问cleaml的接口地址
CLEARML_ENDPOINT = os.environ.get("CLEARML_ENDPOINT", "http://localhost:8008")


class ExternalUrls(object):
    ALGO_GET_ALL = CLEARML_ENDPOINT + "/algo.get_all"
    ALGO_GET_VERSION_ALL = CLEARML_ENDPOINT + "/algo.get_all_release"

    def __init__(self, cookies) -> None:
        self.cookies = cookies
        pass

    def go_request(self, method, url, params, json_data):
        """发送http请求"""
        with requests.Session() as session:
            try:
                response = session.request(method=method, url=url, params=params, json=json_data, cookies=self.cookies)
                response.raise_for_status()
            except requests.exceptions.RequestException as e:
                raise MyappException(f"HTTP 错误: {e}")
            except Exception as e:
                raise MyappException("调用外部接口失败")
    
        datas = response.json()
        return datas
    
    def get_algo_list(self, args):
        """获取算法列表"""
        algos =  self.go_request(method="POST", url=self.ALGO_GET_ALL, params={}, json_data={
            "args": args,
        })
        data =  [ {"id": algo.get("id"), "name": algo.get("name")} for algo in algos.get("data", {}).get("list", []) ]
        return {"data": data, "pager": algos.get("data", {}).get("pager", {})}
    
    def get_algo_vsrsion_list(self, algo_id, args):
        """获取算法版本列表"""
        versions =  self.go_request(method="POST", url=self.ALGO_GET_VERSION_ALL, params=args, json_data={
            "algo": algo_id,
        })
        data = [
            {
            "id": version.get("id"), 
            "version": version.get("version"), 
            "input_train": version.get("args", {}).get("input_train"), 
            "output_train": version.get("args", {}).get("output_train")
            } 
            for version in versions.get("data", {}).get("list", [])
        ]
        return {"data": data, "pager": versions.get("data", {}).get("pager", {})}


@proxy_bp.route('/algos')
def get_algos():
    """获取算法列表"""
    args = request.args.to_dict()
    cookies = request.cookies.to_dict()

    externam_url = ExternalUrls(cookies=cookies)
    algos = externam_url.get_algo_list(args)
    
    return jsonify(algos)


@proxy_bp.route('/algos/<algo_id>/versions')
def get_algo_versions(algo_id):
    """获取算法版本列表与参数列表"""
    args = request.args.to_dict()
    cookies = request.cookies.to_dict()

    externam_url = ExternalUrls(cookies=cookies)
    algos = externam_url.get_algo_vsrsion_list(algo_id, args)
    
    return jsonify(algos)


@proxy_bp.route('/algos/hyperparams/kind', methods=["GET"])
def get_algo_hyperparameter_kind():
    """获取生成超惨的类型"""
    return jsonify(HyperParamsKind.get_value_map_name())


@proxy_bp.route('/algos/hyperparams', methods=["POST"])
def generate_algo_hyperparameter():
    """获取生成超惨的类型"""
    hyper_params = request.get_json()
    return jsonify(HyperParamsKind.generate_params(hyper_params))


