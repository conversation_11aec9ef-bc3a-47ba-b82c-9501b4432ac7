{"cells": [{"cell_type": "code", "execution_count": 1, "id": "74e98673-cb92-4b3f-ac12-5730c668248e", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: tritonclient[all] in /root/miniconda3/envs/python39/lib/python3.9/site-packages (2.33.0)\n", "Requirement already satisfied: pysnooper in /root/miniconda3/envs/python39/lib/python3.9/site-packages (1.1.1)\n", "Requirement already satisfied: requests in /root/miniconda3/envs/python39/lib/python3.9/site-packages (2.29.0)\n", "Requirement already satisfied: Pillow in /root/miniconda3/envs/python39/lib/python3.9/site-packages (9.5.0)\n", "Collecting attrdict\n", "  Downloading attrdict-2.0.1-py2.py3-none-any.whl (9.9 kB)\n", "Requirement already satisfied: numpy>=1.19.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from tritonclient[all]) (1.23.5)\n", "Requirement already satisfied: python-<PERSON><PERSON><PERSON>>=0.9.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from tritonclient[all]) (1.10)\n", "Requirement already satisfied: protobuf<4,>=3.5.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from tritonclient[all]) (3.20.3)\n", "Requirement already satisfied: grpcio>=1.41.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from tritonclient[all]) (1.54.0)\n", "Requirement already satisfied: packaging>=14.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from tritonclient[all]) (23.1)\n", "Requirement already satisfied: geventhttpclient<=2.0.2,>=1.4.4 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from tritonclient[all]) (2.0.2)\n", "Requirement already satisfied: aiohttp>=3.8.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from tritonclient[all]) (3.8.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (3.1.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (3.4)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from requests) (2022.12.7)\n", "Requirement already satisfied: six in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from attrdict) (1.16.0)\n", "Requirement already satisfied: attrs>=17.3.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from aiohttp>=3.8.1->tritonclient[all]) (23.1.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from aiohttp>=3.8.1->tritonclient[all]) (6.0.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from aiohttp>=3.8.1->tritonclient[all]) (4.0.2)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from aiohttp>=3.8.1->tritonclient[all]) (1.9.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from aiohttp>=3.8.1->tritonclient[all]) (1.3.3)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from aiohttp>=3.8.1->tritonclient[all]) (1.3.1)\n", "Requirement already satisfied: gevent>=0.13 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from geventhttpclient<=2.0.2,>=1.4.4->tritonclient[all]) (22.10.2)\n", "Requirement already satisfied: brotli in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from geventhttpclient<=2.0.2,>=1.4.4->tritonclient[all]) (1.0.9)\n", "Requirement already satisfied: zope.event in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from gevent>=0.13->geventhttpclient<=2.0.2,>=1.4.4->tritonclient[all]) (4.6)\n", "Requirement already satisfied: zope.interface in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from gevent>=0.13->geventhttpclient<=2.0.2,>=1.4.4->tritonclient[all]) (6.0)\n", "Requirement already satisfied: setuptools in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from gevent>=0.13->geventhttpclient<=2.0.2,>=1.4.4->tritonclient[all]) (66.0.0)\n", "Requirement already satisfied: greenlet>=2.0.0 in /root/miniconda3/envs/python39/lib/python3.9/site-packages (from gevent>=0.13->geventhttpclient<=2.0.2,>=1.4.4->tritonclient[all]) (2.0.2)\n", "Installing collected packages: attrdict\n", "Successfully installed attrdict-2.0.1\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["!pip install tritonclient[all] pysnooper requests Pillow attrdict"]}, {"cell_type": "code", "execution_count": 2, "id": "1cedfb28-241d-43ea-8b07-9933a7fb9e4c", "metadata": {"tags": []}, "outputs": [], "source": ["# 客户端请求\n", "import datetime\n", "import argparse\n", "from functools import partial\n", "import os\n", "import sys\n", "\n", "from PIL import Image\n", "import numpy as np\n", "from attrdict import AttrDict\n", "import struct\n", "import tritonclient.grpc.model_config_pb2 as mc\n", "import tritonclient.http as httpclient\n", "from tritonclient.utils import InferenceServerException\n", "from tritonclient.utils import triton_to_np_dtype\n", "import json,time\n", "import pysnooper\n", "# pip3 install Pillow\n", "import requests\n", "import base64\n", "\n", "\n", "# @pysnooper.snoop()\n", "def parse_model(model_metadata, model_config):\n", "    \"\"\"\n", "    Check the configuration of a model to make sure it meets the\n", "    requirements for an image classification network (as expected by\n", "    this client)\n", "    \"\"\"\n", "    if len(model_metadata.inputs) != 1:\n", "        raise Exception(\"expecting 1 input, got {}\".format(\n", "            len(model_metadata.inputs)))\n", "    if len(model_metadata.outputs) != 1:\n", "        raise Exception(\"expecting 1 output, got {}\".format(\n", "            len(model_metadata.outputs)))\n", "\n", "    if len(model_config.input) != 1:\n", "        raise Exception(\n", "            \"expecting 1 input in model configuration, got {}\".format(\n", "                len(model_config.input)))\n", "\n", "    input_metadata = model_metadata.inputs[0]\n", "    input_config = model_config.input[0]\n", "    output_metadata = model_metadata.outputs[0]\n", "\n", "    if output_metadata.datatype != \"FP32\":\n", "        raise Exception(\"expecting output datatype to be FP32, model '\" +\n", "                        model_metadata.name + \"' output type is \" +\n", "                        output_metadata.datatype)\n", "\n", "    # Output is expected to be a vector. But allow any number of\n", "    # dimensions as long as all but 1 is size 1 (e.g. { 10 }, { 1, 10\n", "    # }, { 10, 1, 1 } are all ok). Ignore the batch dimension if there\n", "    # is one.\n", "    output_batch_dim = (model_config.max_batch_size > 0)\n", "    non_one_cnt = 0\n", "    for dim in output_metadata.shape:\n", "        if output_batch_dim:\n", "            output_batch_dim = False\n", "        elif dim > 1:\n", "            non_one_cnt += 1\n", "            if non_one_cnt > 1:\n", "                raise Exception(\"expecting model output to be a vector\")\n", "\n", "    # Model input must have 3 dims, either CHW or HWC (not counting\n", "    # the batch dimension), either CHW or HWC\n", "    input_batch_dim = (model_config.max_batch_size > 0)\n", "    expected_input_dims = 3 + (1 if input_batch_dim else 0)\n", "    if len(input_metadata.shape) != expected_input_dims:\n", "        raise Exception(\n", "            \"expecting input to have {} dimensions, model '{}' input has {}\".\n", "                format(expected_input_dims, model_metadata.name,\n", "                       len(input_metadata.shape)))\n", "\n", "    if type(input_config.format) == str:\n", "        FORMAT_ENUM_TO_INT = dict(mc.ModelInput.Format.items())\n", "        input_config.format = FORMAT_ENUM_TO_INT[input_config.format]\n", "\n", "    if ((input_config.format != mc.ModelInput.FORMAT_NCHW) and\n", "            (input_config.format != mc.ModelInput.FORMAT_NHWC)):\n", "        raise Exception(\"unexpected input format \" +\n", "                        mc.ModelInput.Format.Name(input_config.format) +\n", "                        \", expecting \" +\n", "                        mc.ModelInput.Format.Name(mc.ModelInput.FORMAT_NCHW) +\n", "                        \" or \" +\n", "                        mc.ModelInput.Format.Name(mc.ModelInput.FORMAT_NHWC))\n", "\n", "    if input_config.format == mc.ModelInput.FORMAT_NHWC:\n", "        h = input_metadata.shape[1 if input_batch_dim else 0]\n", "        w = input_metadata.shape[2 if input_batch_dim else 1]\n", "        c = input_metadata.shape[3 if input_batch_dim else 2]\n", "    else:\n", "        c = input_metadata.shape[1 if input_batch_dim else 0]\n", "        h = input_metadata.shape[2 if input_batch_dim else 1]\n", "        w = input_metadata.shape[3 if input_batch_dim else 2]\n", "\n", "    return (model_config.max_batch_size, input_metadata.name,\n", "            output_metadata.name, c, h, w, input_config.format,\n", "            input_metadata.datatype)\n", "\n", "\n", "def preprocess(img, format, dtype, c, h, w, scaling, protocol):\n", "    \"\"\"\n", "    Pre-process an image to meet the size, type and format\n", "    requirements specified by the parameters.\n", "    \"\"\"\n", "    # np.set_printoptions(threshold='nan')\n", "\n", "    if c == 1:\n", "        sample_img = img.convert('L')\n", "    else:\n", "        sample_img = img.convert('RGB')\n", "\n", "    resized_img = sample_img.resize((w, h), Image.BILINEAR)\n", "    resized = np.array(resized_img)\n", "    if resized.ndim == 2:\n", "        resized = resized[:, :, np.newaxis]\n", "\n", "    npdtype = triton_to_np_dtype(dtype)\n", "    typed = resized.astype(npdtype)\n", "\n", "    if scaling == 'INCEPTION':\n", "        scaled = (typed / 127.5) - 1\n", "    elif scaling == 'VGG':\n", "        if c == 1:\n", "            scaled = typed - np.asarray((128,), dtype=npdtype)\n", "        else:\n", "            scaled = typed - np.asarray((123, 117, 104), dtype=npdtype)\n", "    else:\n", "        scaled = typed\n", "\n", "    # Swap to CHW if necessary\n", "    if format == mc.ModelInput.FORMAT_NCHW:\n", "        ordered = np.transpose(scaled, (2, 0, 1))\n", "    else:\n", "        ordered = scaled\n", "\n", "    # Channels are in RGB order. Currently model configuration data\n", "    # doesn't provide any information as to other channel orderings\n", "    # (like BGR) so we just assume RGB.\n", "    return ordered\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 22, "id": "84c1c5c5-86d2-4859-be88-848db7e597c6", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型信息： {'name': 'resnet50', 'versions': ['202208014'], 'platform': 'onnxruntime_onnx', 'inputs': [{'name': 'input_name', 'datatype': 'FP32', 'shape': [3, 224, 224]}], 'outputs': [{'name': 'output_name', 'datatype': 'FP32', 'shape': [1000]}]}\n"]}], "source": ["model_name='resnet50'\n", "model_version='202208014'\n", "url = 'resnet50-202208014.service:8000'\n", "classes=1000\n", "\n", "triton_client = httpclient.InferenceServerClient(url=url, verbose=False, concurrency=1)\n", "\n", "# 获取模型的配置信息\n", "model_metadata = triton_client.get_model_metadata(model_name=model_name, model_version=model_version)\n", "print('模型信息：',model_metadata)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "cf5b6373-4b23-4e56-8fbc-3c7379627105", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型配置： {'name': 'resnet50', 'platform': 'onnxruntime_onnx', 'backend': 'onnxruntime', 'version_policy': {'latest': {'num_versions': 1}}, 'max_batch_size': 0, 'input': [{'name': 'input_name', 'data_type': 'TYPE_FP32', 'format': 'FORMAT_NCHW', 'dims': [3, 224, 224], 'reshape': {'shape': [1, 3, 224, 224]}, 'is_shape_tensor': False, 'allow_ragged_batch': False, 'optional': False}], 'output': [{'name': 'output_name', 'data_type': 'TYPE_FP32', 'dims': [1000], 'reshape': {'shape': [1, 1000]}, 'label_filename': '', 'is_shape_tensor': False}], 'batch_input': [], 'batch_output': [], 'optimization': {'priority': 'PRIORITY_DEFAULT', 'input_pinned_memory': {'enable': True}, 'output_pinned_memory': {'enable': True}, 'gather_kernel_buffer_threshold': 0, 'eager_batching': False}, 'instance_group': [{'name': 'resnet50', 'kind': 'KIND_CPU', 'count': 2, 'gpus': [], 'secondary_devices': [], 'profile': [], 'passive': False, 'host_policy': ''}], 'default_model_filename': 'model.onnx', 'cc_model_filenames': {}, 'metric_tags': {}, 'parameters': {'execution_mode': {'string_value': '1'}, 'inter_op_thread_count': {'string_value': '10'}, 'intra_op_thread_count': {'string_value': '10'}}, 'model_warmup': []}\n"]}], "source": ["model_config = triton_client.get_model_config(model_name=model_name, model_version=model_version)\n", "print('模型配置：',model_config)\n"]}, {"cell_type": "code", "execution_count": 24, "id": "b49f8e13-a1d8-4e24-9fb7-aee413cb108e", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["请求参数： 0 input_name output_name 3 224 224 2 FP32\n"]}], "source": ["max_batch_size, input_name, output_name, c, h, w, format, dtype = parse_model(AttrDict(model_metadata), AttrDict(model_config))\n", "# max_batch_size, input_name, output_name, c, h, w, format, dtype = 0,'input_name','output_name',3,224,224,2,'FP32'\n", "print('请求参数：',max_batch_size, input_name, output_name, c, h, w, format, dtype)\n"]}, {"cell_type": "code", "execution_count": 25, "id": "64b086f3-0205-478a-9453-e4572acac185", "metadata": {"tags": []}, "outputs": [], "source": ["image_base64 ='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'\n", "byte_content=base64.b64decode(image_base64)\n", "filename = 'smallcat.jpg'\n", "file = open(filename,mode='wb')\n", "file.write(byte_content)\n", "file.close()"]}, {"cell_type": "markdown", "id": "414ee307-0318-4209-a782-5d205e3005e1", "metadata": {}, "source": ["![示例输入图片](smallcat.jpg)"]}, {"cell_type": "code", "execution_count": 26, "id": "f3ee26f7-0c29-49dc-bbd3-22a297fdbf28", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  ...\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]]\n", "\n", " [[255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  ...\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]]\n", "\n", " [[255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  ...\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]\n", "  [255. 255. 255. ... 255. 255. 255.]]]\n", "响应： 1000 [b'2146.753174:818' b'1593.821533:862' b'1580.508667:920'\n", " b'1497.157349:819' b'1446.738281:619' b'1390.397339:437'\n", " b'1384.842041:555' b'1364.949707:530' b'1294.385498:754'\n", " b'1257.208740:778' b'1255.856934:731' b'1251.588379:632'\n", " b'1234.268677:968' b'1149.677612:407' b'1127.524658:817'\n", " b'1120.258179:627' b'1090.039429:734' b'1083.441284:867'\n", " b'1057.488159:982' b'1046.120117:94' b'1030.905640:468'\n", " b'1016.339783:664' b'1014.331299:498' b'999.077087:479' b'998.746521:779'\n", " b'991.391235:656' b'974.361877:650' b'970.356079:745' b'968.627869:463'\n", " b'941.922302:800' b'937.422974:864' b'931.571106:579' b'931.442322:421'\n", " b'921.547241:685' b'909.910034:781' b'909.362793:617' b'906.689392:919'\n", " b'891.300964:837' b'889.426880:542' b'887.116394:782' b'877.967773:578'\n", " b'866.516296:644' b'863.523438:902' b'862.775269:412' b'861.634949:409'\n", " b'858.461731:557' b'855.091736:762' b'854.653625:799' b'850.362305:562'\n", " b'850.269592:899' b'837.658997:559' b'834.099548:718' b'833.294800:404'\n", " b'815.659851:916' b'814.179382:654' b'812.404175:900' b'811.898010:882'\n", " b'811.678345:620' b'809.714294:571' b'809.163696:527' b'808.774536:846'\n", " b'808.684814:829' b'796.039795:711' b'795.891907:531' b'795.385620:839'\n", " b'793.026001:460' b'788.068848:497' b'785.678162:908' b'783.166260:607'\n", " b'779.301147:836' b'778.679138:581' b'773.208435:736' b'771.440796:536'\n", " b'763.201050:733' b'753.417786:742' b'746.605225:760' b'741.320374:716'\n", " b'740.100830:854' b'728.802551:980' b'725.856934:572' b'725.148193:744'\n", " b'723.739319:907' b'716.864929:681' b'712.193604:966' b'709.229004:462'\n", " b'701.515320:626' b'701.057678:751' b'694.283142:546' b'691.270203:60'\n", " b'687.743958:827' b'685.725098:769' b'678.468506:435' b'677.163635:717'\n", " b'676.148132:402' b'675.855469:879' b'671.024475:673' b'658.869568:417'\n", " b'653.858704:628' b'653.157898:638' b'652.191895:538' b'650.701111:641'\n", " b'647.038818:704' b'643.184265:852' b'638.519897:840' b'637.155945:822'\n", " b'636.917542:826' b'632.140320:471' b'627.100464:254' b'623.778870:447'\n", " b'621.794617:541' b'621.620789:655' b'620.829407:523' b'609.948303:828'\n", " b'605.528259:896' b'604.099548:710' b'600.727295:506' b'599.337708:575'\n", " b'598.149719:757' b'589.631104:552' b'589.144592:480' b'587.730713:861'\n", " b'586.690918:569' b'585.639221:755' b'584.357666:876' b'582.335571:811'\n", " b'578.440308:886' b'574.365112:872' b'570.487366:518' b'569.142090:823'\n", " b'568.909058:962' b'565.169922:720' b'564.888367:723' b'559.541992:950'\n", " b'559.463013:441' b'556.214783:613' b'554.123535:440' b'553.865906:511'\n", " b'552.610962:504' b'552.369446:659' b'548.392700:625' b'546.688965:851'\n", " b'537.161804:470' b'536.652405:806' b'532.332886:427' b'532.320496:466'\n", " b'529.733948:696' b'529.451660:785' b'528.766907:299' b'528.598267:629'\n", " b'522.319336:812' b'520.872620:637' b'518.951904:869' b'517.028137:660'\n", " b'515.348450:436' b'513.294189:601' b'512.758057:705' b'512.289124:582'\n", " b'507.551025:880' b'501.609924:606' b'500.263916:517' b'499.712891:892'\n", " b'492.501221:457' b'491.908630:821' b'491.661407:898' b'489.891571:276'\n", " b'488.760559:737' b'486.863281:875' b'481.715485:670' b'473.971283:508'\n", " b'472.958496:971' b'472.192810:521' b'471.912201:666' b'467.154388:722'\n", " b'466.622437:725' b'465.815216:651' b'456.412170:505' b'455.602051:604'\n", " b'450.838348:570' b'448.806488:860' b'447.439453:791' b'442.580139:598'\n", " b'440.455566:554' b'439.591675:845' b'438.798218:475' b'438.560455:727'\n", " b'432.788147:453' b'432.192749:442' b'428.851959:694' b'428.051392:503'\n", " b'427.459930:373' b'423.858002:600' b'422.925354:657' b'422.357147:888'\n", " b'419.940247:682' b'419.383942:510' b'419.135986:469' b'417.483154:321'\n", " b'417.287079:868' b'417.277435:486' b'412.793549:960' b'407.749878:553'\n", " b'406.209686:935' b'405.666595:910' b'400.306732:515' b'394.899872:672'\n", " b'393.380157:645' b'392.186279:192' b'391.715057:814' b'391.621552:264'\n", " b'391.054138:643' b'387.020172:917' b'386.639374:544' b'385.867859:830'\n", " b'376.856415:844' b'370.562561:923' b'370.200500:485' b'369.586792:838'\n", " b'364.425323:928' b'361.451111:585' b'360.147400:784' b'358.607269:678'\n", " b'358.026398:937' b'355.763855:256' b'355.268433:847' b'354.850128:592'\n", " b'354.467468:714' b'354.130707:635' b'351.464569:599' b'350.629547:809'\n", " b'336.643677:897' b'336.244141:693' b'334.341461:883' b'332.051025:766'\n", " b'330.046844:526' b'327.676666:534' b'327.351868:804' b'326.777496:476'\n", " b'325.964874:909' b'325.615814:1' b'325.452545:807' b'321.100952:208'\n", " b'320.057220:773' b'317.812805:708' b'317.103851:761' b'315.952515:767'\n", " b'315.858917:958' b'311.868469:420' b'310.395996:455' b'309.338257:688'\n", " b'304.475098:513' b'303.552795:642' b'298.897491:283' b'298.791748:877'\n", " b'297.302521:610' b'294.747833:795' b'294.513550:155' b'293.812439:859'\n", " b'293.295685:926' b'292.960907:282' b'292.929779:459' b'291.972168:709'\n", " b'291.516235:924' b'287.135193:438' b'283.723938:605' b'282.726135:16'\n", " b'282.209015:813' b'274.363525:170' b'272.729248:398' b'270.299988:793'\n", " b'268.099030:298' b'266.016632:323' b'263.211884:622' b'259.168060:556'\n", " b'258.547516:566' b'255.969559:220' b'255.722321:889' b'254.935944:568'\n", " b'253.585052:796' b'252.212112:596' b'244.018784:464' b'242.964539:419'\n", " b'237.935608:418' b'236.074234:255' b'235.708664:832' b'234.646820:772'\n", " b'233.687317:454' b'229.610870:652' b'228.579437:509' b'226.174606:631'\n", " b'226.102997:224' b'219.738586:251' b'218.370987:401' b'216.703369:182'\n", " b'215.171341:561' b'211.625153:408' b'210.705688:281' b'209.094940:338'\n", " b'209.038071:805' b'208.042694:967' b'207.512558:750' b'206.991486:675'\n", " b'206.156036:602' b'205.683685:783' b'204.557144:903' b'203.376877:865'\n", " b'202.264389:938' b'199.144257:929' b'198.933350:235' b'198.086777:489'\n", " b'196.967468:732' b'195.289597:215' b'194.466522:848' b'193.895340:802'\n", " b'193.831604:567' b'191.013870:216' b'189.197006:747' b'188.890015:646'\n", " b'188.523010:245' b'188.478119:978' b'187.337830:904' b'187.248199:560'\n", " b'187.058746:185' b'184.814941:881' b'184.183792:946' b'183.882294:894'\n", " b'181.209900:243' b'180.314087:969' b'180.240662:873' b'178.564423:134'\n", " b'178.463654:665' b'176.210922:574' b'175.757538:545' b'174.711456:850'\n", " b'174.531631:771' b'173.009827:803' b'170.382034:154' b'168.078918:974'\n", " b'166.490997:186' b'166.266388:815' b'165.161011:841' b'164.823593:550'\n", " b'163.433594:372' b'159.615814:797' b'154.954132:758' b'154.159805:594'\n", " b'153.555389:415' b'151.430878:199' b'150.311981:949' b'149.424301:965'\n", " b'147.471817:740' b'146.511307:808' b'146.247177:380' b'145.010117:831'\n", " b'139.089890:301' b'137.524170:608' b'136.599426:957' b'131.620789:90'\n", " b'128.355804:984' b'127.982056:776' b'127.062531:934' b'126.243713:985'\n", " b'125.466003:918' b'121.863663:609' b'121.121155:285' b'120.785568:590'\n", " b'117.845840:866' b'117.630844:713' b'117.577507:325' b'117.014954:624'\n", " b'115.379456:416' b'113.632477:577' b'112.864471:849' b'109.799797:786'\n", " b'108.610756:789' b'107.180931:247' b'106.014114:639' b'103.654694:618'\n", " b'101.116119:293' b'97.774269:768' b'96.630501:698' b'95.063141:179'\n", " b'94.800781:636' b'93.384750:583' b'93.150253:326' b'91.546036:263'\n", " b'91.373947:612' b'90.138596:258' b'87.170692:414' b'85.042664:764'\n", " b'83.974609:324' b'83.918411:532' b'83.325455:217' b'82.908401:223'\n", " b'82.719559:522' b'82.296638:195' b'79.370903:648' b'76.031067:551'\n", " b'75.765244:141' b'75.003189:261' b'74.931526:426' b'73.599777:472'\n", " b'71.520645:587' b'71.324966:328' b'70.993225:246' b'70.743103:788'\n", " b'70.220467:493' b'69.488922:488' b'69.443878:104' b'68.150917:406'\n", " b'65.080643:267' b'64.889526:445' b'64.393562:446' b'63.366272:891'\n", " b'63.116421:790' b'61.547760:647' b'61.302002:491' b'60.932770:204'\n", " b'60.382324:697' b'60.060802:330' b'57.920101:432' b'57.296883:272'\n", " b'54.173538:411' b'52.072136:257' b'51.912231:433' b'51.800018:205'\n", " b'49.534241:700' b'48.222088:963' b'47.444855:540' b'47.108284:932'\n", " b'46.895763:23' b'45.642941:483' b'45.060242:403' b'44.669861:210'\n", " b'43.472633:752' b'42.247505:231' b'42.173607:232' b'41.871262:565'\n", " b'39.377697:309' b'39.209015:7' b'37.206467:792' b'34.251114:413'\n", " b'34.039032:905' b'33.509743:151' b'32.166756:824' b'32.143536:193'\n", " b'30.923946:284' b'29.648346:976' b'28.675076:273' b'26.542435:951'\n", " b'26.051731:874' b'24.379471:683' b'22.623951:765' b'21.829950:337'\n", " b'21.558380:12' b'21.308922:616' b'18.195068:691' b'15.689178:702'\n", " b'12.796059:684' b'8.622593:425' b'5.165009:633' b'4.568794:842'\n", " b'4.539871:490' b'3.465836:184' b'2.959312:543' b'-1.466583:856'\n", " b'-1.898338:810' b'-2.550278:770' b'-3.749325:514' b'-4.607277:203'\n", " b'-5.171110:667' b'-7.793217:753' b'-8.405899:422' b'-8.891121:444'\n", " b'-11.416916:143' b'-12.046928:81' b'-15.577740:253' b'-15.969934:333'\n", " b'-16.596569:922' b'-17.061501:798' b'-17.641815:870' b'-19.047659:959'\n", " b'-19.330744:198' b'-20.378550:987' b'-20.429672:219' b'-20.854837:492'\n", " b'-21.061520:719' b'-21.262150:431' b'-22.160629:834' b'-25.231508:288'\n", " b'-26.679192:687' b'-26.918503:547' b'-28.251274:173' b'-32.056526:915'\n", " b'-32.169659:230' b'-33.120399:954' b'-34.213493:449' b'-35.422951:494'\n", " b'-35.564430:260' b'-40.892811:67' b'-41.168091:467' b'-42.765747:520'\n", " b'-43.563480:430' b'-46.892899:482' b'-48.403416:933' b'-50.383373:259'\n", " b'-51.118515:746' b'-52.963379:46' b'-53.226074:535' b'-54.723457:501'\n", " b'-55.103821:730' b'-55.570465:314' b'-55.848427:674' b'-56.673283:207'\n", " b'-56.750610:310' b'-60.937546:428' b'-62.022362:623' b'-62.879650:399'\n", " b'-64.417122:677' b'-66.922729:144' b'-68.912811:209' b'-70.384941:549'\n", " b'-70.644592:930' b'-71.486359:221' b'-74.552605:244' b'-76.266754:228'\n", " b'-79.373955:558' b'-79.381744:516' b'-81.143051:953' b'-81.567619:448'\n", " b'-81.862068:906' b'-82.069504:11' b'-83.457947:336' b'-84.248344:835'\n", " b'-85.660461:347' b'-85.799591:27' b'-86.179276:250' b'-86.832474:956'\n", " b'-89.352173:999' b'-92.421074:689' b'-93.824745:663' b'-94.501266:658'\n", " b'-94.707512:961' b'-95.328773:927' b'-96.862656:948' b'-98.919289:662'\n", " b'-99.307999:200' b'-100.657051:405' b'-101.822510:484'\n", " b'-102.571739:573' b'-103.827377:262' b'-106.076111:589'\n", " b'-107.324905:970' b'-107.812393:234' b'-110.389870:998'\n", " b'-110.593361:721' b'-111.136505:668' b'-112.319733:885'\n", " b'-112.847450:275' b'-113.552780:377' b'-114.973015:703'\n", " b'-115.287704:169' b'-115.447998:621' b'-115.965103:943'\n", " b'-116.604118:359' b'-117.395630:159' b'-119.252762:265'\n", " b'-119.665482:423' b'-120.287300:820' b'-120.641861:308'\n", " b'-121.762993:774' b'-122.577759:952' b'-127.099243:92'\n", " b'-127.859756:269' b'-127.865181:595' b'-127.885315:40'\n", " b'-130.197418:680' b'-131.671478:135' b'-132.061264:180'\n", " b'-133.716034:593' b'-133.941650:178' b'-134.941132:136'\n", " b'-138.289062:964' b'-139.632706:174' b'-139.788651:539'\n", " b'-139.903564:787' b'-140.445908:307' b'-140.525818:188'\n", " b'-142.031281:277' b'-142.596664:78' b'-143.797470:707'\n", " b'-144.443542:975' b'-147.968460:211' b'-148.384933:8' b'-149.637878:878'\n", " b'-149.653793:496' b'-151.529388:712' b'-153.015015:706'\n", " b'-154.012756:355' b'-155.059601:533' b'-155.223740:236'\n", " b'-155.232666:163' b'-156.066086:233' b'-156.443665:759'\n", " b'-158.059753:775' b'-158.140747:172' b'-158.263626:911'\n", " b'-158.301971:171' b'-159.824066:31' b'-162.103882:242'\n", " b'-162.711517:528' b'-168.046570:564' b'-170.264496:947'\n", " b'-171.486786:728' b'-172.190002:936' b'-173.939209:981'\n", " b'-174.852997:481' b'-174.941452:576' b'-176.429367:270'\n", " b'-177.401474:887' b'-178.186615:944' b'-178.192612:201'\n", " b'-178.845139:225' b'-180.699448:9' b'-180.827362:989' b'-182.676697:458'\n", " b'-186.226135:895' b'-186.716125:478' b'-186.756912:794'\n", " b'-188.003098:429' b'-188.561356:152' b'-188.967316:157'\n", " b'-189.398300:311' b'-191.251160:756' b'-193.287292:342'\n", " b'-193.517426:227' b'-193.601471:507' b'-196.191650:106'\n", " b'-196.473007:686' b'-199.143448:699' b'-201.313354:248'\n", " b'-201.851425:614' b'-203.159332:495' b'-203.309296:499'\n", " b'-203.681061:400' b'-203.887909:424' b'-206.064056:266'\n", " b'-214.726303:51' b'-216.878571:548' b'-217.852264:111'\n", " b'-218.313873:653' b'-218.463181:24' b'-220.968521:661'\n", " b'-224.143738:525' b'-225.739731:925' b'-229.761688:487'\n", " b'-230.075333:692' b'-233.329956:512' b'-234.863800:990'\n", " b'-235.082809:313' b'-236.038803:80' b'-236.847092:340'\n", " b'-236.921188:381' b'-237.301270:871' b'-240.085266:439'\n", " b'-243.348267:855' b'-243.354218:382' b'-243.612289:327'\n", " b'-243.791321:591' b'-244.450653:334' b'-245.000397:271'\n", " b'-245.115143:777' b'-245.277054:945' b'-245.425461:161'\n", " b'-247.479767:71' b'-250.276840:59' b'-250.440567:286' b'-251.668335:451'\n", " b'-252.247986:941' b'-252.498520:139' b'-252.992889:843'\n", " b'-253.612442:18' b'-255.339005:74' b'-255.407196:724' b'-256.403687:79'\n", " b'-257.392365:942' b'-258.264771:519' b'-260.522705:238'\n", " b'-261.041351:168' b'-261.447601:371' b'-262.878845:194'\n", " b'-264.629700:167' b'-264.801453:306' b'-265.960358:679'\n", " b'-266.066986:102' b'-266.675812:410' b'-268.036224:118'\n", " b'-271.173767:75' b'-272.784332:124' b'-273.329590:294'\n", " b'-274.059662:901' b'-274.122253:385' b'-275.081665:701'\n", " b'-276.717346:443' b'-277.253479:315' b'-279.066803:580'\n", " b'-280.406006:274' b'-282.663391:858' b'-285.376251:166'\n", " b'-285.633606:931' b'-288.118286:671' b'-288.428772:249'\n", " b'-288.501190:122' b'-289.773834:42' b'-290.146210:99' b'-292.528931:115'\n", " b'-299.879272:502' b'-300.152954:43' b'-301.563843:364'\n", " b'-302.148163:318' b'-302.528473:187' b'-302.999603:921'\n", " b'-303.432800:780' b'-306.272308:748' b'-306.548004:588'\n", " b'-313.071716:529' b'-315.188385:303' b'-319.452271:884'\n", " b'-320.194214:649' b'-322.340088:695' b'-322.687622:113'\n", " b'-325.361755:461' b'-325.537811:833' b'-326.306915:801'\n", " b'-326.782959:630' b'-328.091522:240' b'-330.511230:563'\n", " b'-331.553314:640' b'-332.050262:162' b'-332.443878:584'\n", " b'-334.438629:15' b'-334.590149:183' b'-339.247131:112'\n", " b'-341.651794:690' b'-343.662079:341' b'-344.549042:156'\n", " b'-345.805878:177' b'-345.996552:30' b'-346.599274:47' b'-349.242188:54'\n", " b'-351.746674:912' b'-352.711029:992' b'-352.883728:362'\n", " b'-354.987427:291' b'-355.989868:13' b'-356.923615:857'\n", " b'-361.417572:743' b'-361.714874:145' b'-365.787231:28' b'-365.911438:53'\n", " b'-366.940857:222' b'-368.583557:239' b'-369.400940:123'\n", " b'-369.846649:348' b'-371.570282:197' b'-372.307404:319'\n", " b'-372.870850:343' b'-374.163635:741' b'-376.707001:83'\n", " b'-377.793030:100' b'-379.096222:213' b'-380.306824:977'\n", " b'-380.476776:335' b'-380.869446:356' b'-381.054688:735'\n", " b'-381.451019:749' b'-381.669373:105' b'-382.188110:816'\n", " b'-383.960907:82' b'-384.644409:196' b'-385.102112:158'\n", " b'-385.516235:379' b'-386.589294:537' b'-392.424103:214'\n", " b'-394.270386:77' b'-395.620972:190' b'-395.953522:474'\n", " b'-396.356445:252' b'-397.101807:86' b'-397.228485:825'\n", " b'-397.975464:331' b'-399.839783:988' b'-400.035217:191'\n", " b'-403.418762:287' b'-403.979614:354' b'-404.909332:226'\n", " b'-407.190826:738' b'-408.202087:465' b'-408.426941:4' b'-409.441315:361'\n", " b'-411.137268:296' b'-412.714386:603' b'-413.467834:329'\n", " b'-414.115234:212' b'-416.874451:669' b'-417.235321:117'\n", " b'-417.787537:127' b'-418.652435:280' b'-420.705170:295'\n", " b'-423.445496:320' b'-423.468842:96' b'-424.224731:241'\n", " b'-426.297729:634' b'-426.882812:434' b'-427.088562:2' b'-428.382568:278'\n", " b'-429.018860:160' b'-433.798462:349' b'-433.831757:345'\n", " b'-435.720093:165' b'-436.024933:729' b'-436.192627:87'\n", " b'-437.241455:119' b'-437.877899:175' b'-440.960571:91'\n", " b'-442.651581:322' b'-442.746277:45' b'-443.163818:973'\n", " b'-444.281921:890' b'-445.237915:121' b'-446.326721:132'\n", " b'-446.352448:615' b'-447.723663:332' b'-448.879578:611'\n", " b'-451.010223:164' b'-452.475983:25' b'-452.534973:390' b'-453.129059:85'\n", " b'-454.024109:56' b'-457.997253:76' b'-458.011505:181' b'-458.464966:89'\n", " b'-463.782440:229' b'-472.145538:304' b'-475.333099:5' b'-476.866638:279'\n", " b'-478.078247:940' b'-479.155579:979' b'-479.656738:10'\n", " b'-479.955261:128' b'-480.394806:456' b'-480.760620:19'\n", " b'-481.665955:290' b'-482.528351:393' b'-482.582031:913'\n", " b'-484.141113:452' b'-486.362366:300' b'-490.361816:101'\n", " b'-490.400970:450' b'-490.501526:153' b'-493.034058:366'\n", " b'-498.134460:289' b'-499.095856:147' b'-499.179626:95'\n", " b'-499.437988:150' b'-500.842987:939' b'-501.688843:218'\n", " b'-505.708954:473' b'-505.986847:302' b'-509.872864:676'\n", " b'-512.293457:206' b'-517.632507:395' b'-518.905151:14'\n", " b'-520.798218:370' b'-521.400208:37' b'-521.909241:763'\n", " b'-523.112976:972' b'-527.657654:110' b'-529.512512:140'\n", " b'-536.023254:312' b'-536.323059:374' b'-543.523438:292'\n", " b'-543.597168:202' b'-545.373535:189' b'-547.653625:130'\n", " b'-550.935730:93' b'-552.994019:477' b'-558.331299:378'\n", " b'-558.368286:114' b'-563.663818:983' b'-564.669067:73' b'-569.149414:36'\n", " b'-570.056885:62' b'-570.380005:863' b'-570.837219:88' b'-576.897156:358'\n", " b'-578.786316:84' b'-579.419434:97' b'-585.628235:48' b'-586.436707:586'\n", " b'-587.382812:383' b'-589.338562:357' b'-589.563904:367'\n", " b'-590.030212:66' b'-590.437805:352' b'-591.159180:41' b'-593.369629:268'\n", " b'-595.503845:52' b'-596.364868:853' b'-599.924377:21' b'-602.829041:109'\n", " b'-604.180908:955' b'-606.460938:176' b'-608.250122:394'\n", " b'-608.774353:61' b'-609.079773:116' b'-609.805298:69' b'-610.083374:70'\n", " b'-610.776428:32' b'-613.760010:44' b'-614.120056:68' b'-614.425415:138'\n", " b'-615.520264:129' b'-621.391052:524' b'-627.116272:39'\n", " b'-628.523499:376' b'-631.628601:305' b'-636.052490:34'\n", " b'-636.779358:914' b'-640.095825:137' b'-642.567444:126'\n", " b'-647.595032:386' b'-652.127197:346' b'-652.248840:6' b'-662.094543:55'\n", " b'-664.856750:50' b'-665.848755:392' b'-668.172852:98' b'-670.131836:397'\n", " b'-670.180603:20' b'-670.818237:996' b'-677.295532:22' b'-679.656128:893'\n", " b'-680.433472:369' b'-688.252747:38' b'-692.685791:317' b'-696.307922:72'\n", " b'-696.487122:994' b'-696.735901:384' b'-697.366089:360'\n", " b'-701.053833:107' b'-702.588379:17' b'-709.740234:388'\n", " b'-713.066528:148' b'-716.471191:142' b'-722.638062:363'\n", " b'-730.443726:339' b'-731.611450:108' b'-734.973328:365'\n", " b'-735.422668:991' b'-753.249084:125' b'-755.210205:316'\n", " b'-759.345459:389' b'-771.197205:986' b'-785.781799:375'\n", " b'-799.196106:368' b'-809.601501:57' b'-817.644958:237' b'-832.416992:33'\n", " b'-838.966431:49' b'-839.609131:350' b'-841.657410:396'\n", " b'-871.954956:120' b'-875.521484:64' b'-875.647827:391'\n", " b'-877.396545:500' b'-885.154175:353' b'-885.222900:149'\n", " b'-886.784363:35' b'-887.808716:297' b'-889.185059:997'\n", " b'-894.974243:131' b'-899.001221:739' b'-912.835205:597'\n", " b'-913.553528:146' b'-914.563354:133' b'-915.352966:344'\n", " b'-921.376709:995' b'-921.399475:993' b'-925.249023:103'\n", " b'-949.642151:715' b'-950.522705:63' b'-955.211670:29' b'-964.210815:387'\n", " b'-977.646973:58' b'-994.901611:26' b'-997.362427:726'\n", " b'-1047.918335:351' b'-1051.183716:65' b'-1063.572510:0'\n", " b'-1094.195801:3']\n"]}], "source": ["image_data = preprocess(Image.open(filename), format, dtype, c, h, w, None,'http')\n", "batched_image_data = image_data\n", "print(batched_image_data)\n", "# Send request\n", "input = httpclient.InferInput(input_name, batched_image_data.shape, dtype)\n", "input.set_data_from_numpy(batched_image_data)\n", "output = httpclient.InferRequestedOutput(output_name, class_count=classes)\n", "\n", "response = triton_client.infer(model_name,[input],request_id=str(time.time()),model_version=model_version,outputs=[output])\n", "output_array = response.as_numpy(output_name)\n", "print('响应：',len(output_array),output_array)"]}, {"cell_type": "code", "execution_count": null, "id": "e81d88e2-4b67-41ed-b2fb-8f296ef8299e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}