apiVersion: v1
kind: Service
metadata:
  name: zk-external
  namespace: logging
spec:
  ports:
  - name: tcp-clients
    port: 2181
    protocol: TCP
    targetPort: 2181
  selector:
    strimzi.io/cluster: kafka-cluster
    strimzi.io/kind: Kafka
    strimzi.io/name: kafka-cluster-zookeeper
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: kafka-cluster-kafka-0
  namespace: logging
spec:
  ports:
    - name: tcp-external
      nodePort: 29090
      port: 9094
      protocol: TCP
      targetPort: 9094
  selector:
    statefulset.kubernetes.io/pod-name: kafka-cluster-kafka-0
    strimzi.io/cluster: kafka-cluster
    strimzi.io/kind: Kafka
    strimzi.io/name: kafka-cluster-kafka
  type: NodePort

---
apiVersion: v1
kind: Service
metadata:
  name: kafka-cluster-kafka-external-bootstrap
  namespace: logging
spec:
  ports:
    - name: tcp-external
      nodePort: 29094
      port: 9094
      protocol: TCP
      targetPort: 9094
  selector:
    strimzi.io/cluster: kafka-cluster
    strimzi.io/kind: Kafka
    strimzi.io/name: kafka-cluster-kafka
  sessionAffinity: None
  type: NodePort