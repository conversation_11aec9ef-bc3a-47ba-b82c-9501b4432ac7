"""empty message

Revision ID: 7227cb38c9b1
Revises: 6d937667d332
Create Date: 2023-09-03 01:26:09.893112

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7227cb38c9b1'
down_revision = '6d937667d332'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sqlab_query', 'err_msg',
               existing_type=mysql.VARCHAR(length=5000),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sqlab_query', 'err_msg',
               existing_type=mysql.VARCHAR(length=5000),
               nullable=False)
    # ### end Alembic commands ###
