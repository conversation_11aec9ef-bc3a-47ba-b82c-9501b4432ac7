apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: kong-admin
  name: kong-admin
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
#    honorLabels: true
#    kubernetes_sd_config:    # 不是这种格式
#    - role: service      # 不是这种格式
    port: kong-admin    # 端口号只能是字符串, 在pod中定义的端口name
  jobLabel: app
  namespaceSelector:
    matchNames:
    - kong
  selector:
    matchLabels:
      app: kong
      release: aicloud-kong
