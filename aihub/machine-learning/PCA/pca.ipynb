{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "class PCA():\n", "    # 计算协方差矩阵\n", "    def calc_cov(self, X):\n", "        m = X.shape[0]\n", "        # 数据标准化\n", "        X = (X - np.mean(X, axis=0)) / np.var(X, axis=0)\n", "        return 1 / m * np.matmul(X.T, X)\n", "\n", "    def pca(self, X, n_components):\n", "        # 计算协方差矩阵\n", "        cov_matrix = self.calc_cov(X)\n", "        # 计算协方差矩阵的特征值和对应特征向量\n", "        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)\n", "        # 对特征值排序\n", "        idx = eigenvalues.argsort()[::-1]\n", "        # 取最大的前n_component组\n", "        eigenvectors = eigenvectors[:, idx]\n", "        eigenvectors = eigenvectors[:, :n_components]\n", "        # Y=PX转换\n", "        return np.matmul(X, eigenvectors)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from sklearn import datasets\n", "import matplotlib.pyplot as plt\n", "\n", "# 导入sklearn数据集\n", "iris = datasets.load_iris()\n", "X = iris.data\n", "y = iris.target\n", "\n", "# 将数据降维到3个主成分\n", "X_trans = PCA().pca(X, 3)\n", "# 颜色列表\n", "colors = ['navy', 'turquoise', 'darkorange']\n", "\n", "# 绘制不同类别\n", "for c, i, target_name in zip(colors, [0,1,2], iris.target_names):\n", "    plt.scatter(X_trans[y == i, 0], X_trans[y == i, 1],\n", "            color=c, lw=2, label=target_name)\n", "# 添加图例\n", "plt.legend()\n", "plt.show();"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# 导入sklearn降维模块\n", "from sklearn import decomposition\n", "# 创建pca模型实例，主成分个数为3个\n", "pca = decomposition.PCA(n_components=3)\n", "# 模型拟合\n", "pca.fit(X)\n", "# 拟合模型并将模型应用于数据X\n", "X_trans = pca.transform(X)\n", "\n", "# 颜色列表\n", "colors = ['navy', 'turquoise', 'darkorange']\n", "# 绘制不同类别\n", "for c, i, target_name in zip(colors, [0,1,2], iris.target_names):\n", "    plt.scatter(X_trans[y == i, 0], X_trans[y == i, 1], \n", "            color=c, lw=2, label=target_name)\n", "# 添加图例\n", "plt.legend()\n", "plt.show();"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 4}