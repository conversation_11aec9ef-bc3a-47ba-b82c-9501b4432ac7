# 算法保存算子 (Algorithm Saver)

## 功能概述

算法保存算子是一个独立的微服务组件，用于将训练完成的模型保存回算法仓并生成新的算法版本。该算子支持从 ClearML 任务中自动获取最佳模型，并将其注册到 Cube Studio 的模型管理系统中。

## 主要特性

- ✅ **自动版本管理**: 基于时间戳生成新的算法版本号
- ✅ **ClearML 集成**: 支持从 ClearML 任务中获取模型和指标
- ✅ **最佳模型选择**: 自动选择性能最佳的训练任务
- ✅ **指标记录**: 保存训练过程中的评估指标
- ✅ **灵活配置**: 支持多种训练和推理框架
- ✅ **错误处理**: 完善的异常处理和日志记录

## 使用方法

### 基本用法

```bash
python algorithm_saver.py \
  --algo_id "algorithm_123" \
  --algo_version "v1.0" \
  --model_path "/mnt/models/trained_model.pt" \
  --framework "pytorch" \
  --model_metrics '{"accuracy": 0.95, "loss": 0.05}'
```

### 与 ClearML 集成使用

```bash
python algorithm_saver.py \
  --algo_id "algorithm_123" \
  --algo_version "v1.0" \
  --model_path "/mnt/models/trained_model.pt" \
  --clearml_project_name "my_training_project" \
  --use_best_task \
  --clearml_metric_name "accuracy" \
  --framework "pytorch"
```

### 指定 ClearML 任务ID

```bash
python algorithm_saver.py \
  --algo_id "algorithm_123" \
  --algo_version "v1.0" \
  --model_path "/mnt/models/trained_model.pt" \
  --clearml_task_id "task_abc123" \
  --framework "tensorflow"
```

## 参数说明

### 必需参数

| 参数 | 类型 | 说明 |
|------|------|------|
| `--algo_id` | string | 原始算法ID |
| `--algo_version` | string | 原始算法版本 |
| `--model_path` | string | 训练后的模型文件路径 |

### 可选参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--framework` | string | pytorch | 训练框架 |
| `--inference_framework` | string | custom | 推理框架 |
| `--description` | string | "" | 模型描述 |
| `--model_metrics` | string | {} | 模型评估指标(JSON格式) |

### ClearML 相关参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--clearml_project_name` | string | "" | ClearML项目名称 |
| `--clearml_task_id` | string | "" | ClearML任务ID |
| `--clearml_metric_name` | string | accuracy | ClearML评估指标名称 |
| `--use_best_task` | flag | false | 是否使用ClearML最佳任务 |

## 环境变量

算子运行时需要以下环境变量：

| 环境变量 | 说明 | 示例 |
|----------|------|------|
| `KFJ_TASK_PROJECT_NAME` | 项目组名称 | public |
| `KFJ_CREATOR` | 创建者 | admin |
| `KFJ_RUN_ID` | 运行ID | run_abc123 |
| `KFJ_PIPELINE_ID` | 流水线ID | 123 |
| `HOST` | Cube Studio API地址 | http://kubeflow-dashboard.infra |
| `CLEARML_API_HOST` | ClearML API地址 | http://clearml-api:8008 |

## 输出结果

算子执行成功后会生成结果文件 `/tmp/algorithm_save_result.json`：

```json
{
  "success": true,
  "original_algo_id": "algorithm_123",
  "original_algo_version": "v1.0",
  "new_algo_version": "v1.0_trained_20231201_143022",
  "model_path": "/mnt/models/trained_model.pt",
  "metrics": {
    "accuracy": 0.95,
    "loss": 0.05
  },
  "clearml_task_id": "task_abc123"
}
```

## 在 Pipeline 中使用

### 1. 添加任务模板配置

在 `init-job-template.json` 中添加算法保存器的配置：

```json
{
  "algorithm_saver": {
    "project_name": "算法管理",
    "image_name": "ccr.ccs.tencentyun.com/cube-studio/algorithm_saver:latest",
    "gitpath": "/job-template/job/algorithm_saver",
    "job_template_name": "algorithm_saver",
    "job_template_describe": "算法保存算子",
    "job_template_command": "python algorithm_saver.py",
    "job_template_args": {
      "算法信息": {
        "--algo_id": {
          "type": "str",
          "label": "算法ID",
          "require": 1,
          "describe": "原始算法的ID"
        },
        "--algo_version": {
          "type": "str", 
          "label": "算法版本",
          "require": 1,
          "describe": "原始算法的版本号"
        },
        "--model_path": {
          "type": "str",
          "label": "模型路径", 
          "require": 1,
          "describe": "训练后的模型文件路径"
        }
      }
    }
  }
}
```

### 2. 在 Pipeline 中连接

```
训练任务 → 算法保存算子 → 模型部署任务
```

## 错误处理

算子包含完善的错误处理机制：

- **网络错误**: 自动重试和超时处理
- **参数验证**: 检查必需参数的有效性
- **API错误**: 详细的错误信息记录
- **文件操作**: 安全的文件读写操作

## 日志记录

算子使用标准的 Python logging 模块，支持以下日志级别：

- `INFO`: 正常操作信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息

## 扩展性

算子设计时考虑了扩展性：

- **插件化架构**: 易于添加新的模型格式支持
- **配置驱动**: 通过参数控制行为
- **API兼容**: 支持不同版本的 API

## 最佳实践

1. **版本管理**: 建议使用语义化版本号
2. **路径规范**: 使用绝对路径指定模型文件
3. **指标记录**: 记录关键的评估指标
4. **错误监控**: 监控算子的执行状态
5. **资源清理**: 及时清理临时文件
