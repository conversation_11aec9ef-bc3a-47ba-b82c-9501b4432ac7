#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ if .Values.logserver.enabled -}}
{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullName }}-logserver
  labels:
    name: {{ $fullName }}-logserver
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-logserver
spec:
  replicas: 1
  {{ with .Values.logserver.strategy -}}
  strategy:
{{ toYaml . | nindent 4 }}
  {{ end -}}
  selector:
    matchLabels:
      app: {{ $name }}
      role: alluxio-logserver
      name: {{ $fullName }}-logserver
  template:
    metadata:
      labels:
        name: {{ $fullName }}-logserver
        app: {{ $name }}
        chart: {{ $chart }}
        release: {{ .Release.Name }}
        heritage: {{ .Release.Service }}
        role: alluxio-logserver
    spec:
      tolerations:
      {{- if .Values.logserver.tolerations }}
{{ toYaml .Values.logserver.tolerations | trim | indent 8  }}
      {{- end }}
      {{- if .Values.tolerations }}
{{ toYaml .Values.tolerations | trim | indent 8  }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
{{ include "alluxio.imagePullSecrets" . | indent 6 }}
      {{- end}}
      containers:
        - name: alluxio-logserver
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          securityContext:
            runAsUser: {{ .Values.user }}
            runAsGroup: {{ .Values.group }}
          {{- if .Values.logserver.resources  }}
{{ include "alluxio.logserver.resources" . | indent 10 }}
          {{- end }}
          command: ["tini", "--", "/entrypoint.sh"]
            {{- if .Values.logserver.args }}
          args:
{{ toYaml .Values.logserver.args | trim | indent 12 }}
            {{- end }}
          env:
            {{- range $key, $value := .Values.logserver.env }}
          - name: "{{ $key }}"
            value: "{{ $value }}"
            {{- end }}
          envFrom:
          - configMapRef:
              name: {{ $fullName }}-config
          ports:
          - containerPort: {{ .Values.logserver.ports.logging }}
            name: logging
          volumeMounts:
            {{- if not (eq .Values.logserver.volumeType "none") }}
          - name: alluxio-logs
            mountPath: /opt/alluxio/logs
            {{- end }}
            {{- if .Values.secrets }}
              {{- if .Values.secrets.logserver }}
            {{- include "alluxio.logserver.secretVolumeMounts" . }}
              {{- end }}
            {{- end }}
            {{- if .Values.configmaps }}
              {{- if .Values.configmaps.logserver }}
            {{- include "alluxio.logserver.configmapVolumeMounts" . }}
              {{- end }}
            {{- end }}
            {{- if .Values.mounts }}
              {{- range .Values.mounts }}
          - name: "{{ .name }}"
            mountPath: "{{ .path }}"
              {{- end }}
            {{- end }}
      restartPolicy: Always
      volumes:
{{- include "alluxio.logserver.log.volume" . | indent 6 }}
      {{- if .Values.secrets }}
        {{- if .Values.secrets.logserver }}
          {{- range $key, $val := .Values.secrets.logserver }}
      - name: secret-{{ $key }}-volume
        secret:
          secretName: {{ $key }}
          defaultMode: 256
          {{- end }}
        {{- end }}
      {{- end }}
      {{- if .Values.mounts }}
        {{- range .Values.mounts }}
      - name: "{{ .name }}"
        persistentVolumeClaim:
          claimName: "{{ .name }}"
        {{- end }}
      {{- end }}
{{- end }}
