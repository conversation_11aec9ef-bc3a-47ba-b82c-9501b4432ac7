---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: minio
  name: minio
  namespace: kubeflow
spec:
  ports:
  - name: api
    port: 9000
    protocol: TCP
    targetPort: 9000
  - name: dashboard
    port: 9001
    protocol: TCP
    targetPort: 9001
  selector:
    app: minio
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: minio
  name: minio
  namespace: kubeflow
spec:
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      nodeSelector:
        kubeflow: 'true'
      containers:
      - args:
        - server
        - /data
        - --console-address
        - :9001
        env:
        - name: MINIO_ROOT_USER
          value: minio
        - name: MINIO_ROOT_PASSWORD
          value: minio123
        image: minio/minio:RELEASE.2023-04-20T17-56-55Z
        imagePullPolicy: IfNotPresent
        volumeMounts:
          - name: data
            mountPath: /data
        lifecycle:
          postStart:
            exec:
              command:
              - mkdir
              - -p
              - /data/mlpipeline
        livenessProbe:
          httpGet:
            path: /minio/health/live
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 10
        name: main
        ports:
        - containerPort: 9000
          name: api
        - containerPort: 9001
          name: dashboard
        readinessProbe:
          httpGet:
            path: /minio/health/ready
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: minio-pvc
---