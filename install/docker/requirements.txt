Flask==2.3.3
flask-appbuilder==4.3.7     # Flask应用构建库
Flask-Migrate==4.0.5        # 数据库版本变更
flask-compress==1.14       #  Flask响应压缩库
flask-caching==2.0.2        # Flask缓存库
flask-talisman==1.1.0       #  Flask安全头库
Flask-OpenID==1.3.0         # via flask-appbuilder  Flask OpenID库
Flask-SQLAlchemy==2.5.1     # via flask-appbuilder
Flask-Login==0.6.2          # via flask-appbuilder
Flask-Limiter==3.5.0        # via flask-appbuilder
Flask-JWT-Extended==4.5.2   # via flask-appbuilder
Flask-WTF==1.2.1            # via flask-appbuilder
Jinja2==3.1.2               # via flask-appbuilder
kombu==5.3.2                # via flask-appbuilder
SQLAlchemy==1.4.49          # via flask-appbuilder
SQLAlchemy-Utils==0.41.1    # via flask-appbuilder
werkzeug==2.3.7
wtforms==2.3.3
wtforms-json==0.3.5
Flask-Cors==3.0.10          # Flask跨域资源共享（CORS）库
# Flask-Babel==4.0.0          # 翻译专用

celery==5.2.2               # 异步任务队列库,5.2.2
gunicorn==21.2.0            # Python WSGI HTTP服务器
humanize==4.8.0             # 使数字更具可读性的库
emoji==2.8.0                # 表情字符处理
psycopg2-binary==2.9.8      # postgresql数据库
wechatpy==1.8.18            # 是一个用于处理微信公众号API的库
pycryptodome==3.19.0        # wechat需要的基础库
xlrd==2.0.1                 # 是一个用于读取Excel文件的库
pydub==0.25.1               # 是一个用于处理音频文件的库
openpyxl==3.1.2             # 是一个用于处理Excel文件的库
elasticsearch==7.12.1       # es数据库
jieba==0.42.1               # 中文分词
sseclient-py==1.8.0         # openai 流式客户端
simplejson==3.19.1          # 是一个处理JSON数据的库
PySnooper==1.2.0            # 函数调用跟踪
kubernetes==25.3.0          #  Kubernetes Python客户端库
statsd==4.0.1               # 是一个用于发送统计数据到StatsD服务器的库。
redis==5.0.1                # redis数据库连接
pymysql==1.1.0              # 连接mysql数据库
minio==7.1.17               # MinIO Python客户端库
markdown==3.4.4             # Markdown文本转换库
croniter==1.4.1             # cron表达式解析库
contextlib2==21.6.0         # 用于提供与contextlib模块相似的功能
parsedatetime==2.6          # 日期时间解析库
pylint==3.0.0               # Python源代码分析器，用于查找编程错误、遵循代码风格指南
bleach==6.0.0               # HTML清理和链接化库
pandas==2.1.2               # 数据处理
beautifulsoup4==4.12.2      # 用于解析HTML和XML文档
cffi==1.16.0
clickhouse-driver==0.2.7    # sqllab对接clickhouse
pyhive==0.7.0               # sqllab对接hive
thrift==0.20.0              # sqllab对接hive
thrift-sasl==0.4.3          # sqllab对接hive
sasl==0.3.1                 # sqllab对接hive
cryptography==43.0.0        # 加解密
jwcrypto==1.5.6             # 推理服务支持启动认证jwt
openai==1.34.0              # openai接口协议
ldap3==2.9.1                # ldap单点登录
gmssl==3.2.2                # sm4加解密，单点登录