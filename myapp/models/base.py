
from flask_babel import gettext as __
from flask_babel import lazy_gettext as _
import re
from myapp.utils import core
from enum import Enum

class MyappModelBase():

    label_columns={
        "name": _("名称"),
        "name_url": _("名称"),
        "name_title": _("名称(移动鼠标，查看描述)"),
        'job_type': _('任务类型'),
        "project": _("项目组"),
        "project_url": _("项目组"),
        "namespace": _("命名空间"),
        "namespace_url": _("命名空间"),
        "describe": _("描述"),
        "describe_url": _("描述"),
        "password": _('密码'),
        "workdir": _("工作目录"),
        "images": _(_("镜像")),
        "repository": _("仓库"),
        "args": _("启动参数"),
        "args_html": _("启动参数"),
        "demo": _("参数示例"),
        "demo_html": _("参数示例"),
        "entrypoint": _('启动命令'),
        "dockerfile": _('Dockerfile'),
        "gitpath": _('git地址'),
        "env": _("环境变量"),
        "privileged": _("特权模式"),
        "accounts": _("k8s账号"),
        "images_url": _("镜像"),
        "hostAliases": _("host"),
        "overwrite_entrypoint": _("覆盖原始启动命令"),
        "command": _("启动命令"),
        "working_dir": _("启动目录"),
        "volume_mount": _("挂载目录"),
        "node_selector": _("调度机器"),
        "image_pull_policy": _("镜像拉取策略"),
        "resource_memory": _("内存申请"),
        "resource_cpu": _("cpu申请"),
        "resource_gpu": _("gpu申请"),
        "resource_rdma": _("rdma申请"),
        "resource": _("资源"),
        "timeout": _("超时中断"),
        "retry": _("重试次数"),
        "outputs": _("输出"),
        "version": _("版本"),
        "model_name": _("模型名称"),
        "model_version": _("模型版本"),
        "model_path": _("模型地址"),
        "model_name_url": _("模型名称"),
        "model_version_url": _("模型版本"),
        "model_path_url": _("模型地址"),
        "is_fallback": _("兜底版本"),
        "check_service": _("检查服务"),
        "status": _("状态"),
        "model_status": _("状态"),
        "status_url": _("状态"),
        "final_status": _("最终状态"),
        "pipeline": _("任务流"),
        "pipeline_id": _("任务流id"),
        "pipeline_url": _("任务流"),
        "model_metric": _("模型指标"),
        "etl_pipeline_url": _("任务流"),
        "service_pipeline_url": _("推理编排"),
        "run_id": _("运行id"),
        "run_time": _("运行时间"),
        "type": _("类型"),
        "reset": _("重置"),
        "user": _("用户"),
        "project_user": _("用户"),
        "role": _("角色"),
        "dag_json": _("流向图"),
        "dag_json_html": _("流向图"),
        "username": _("用户名"),
        "schedule_type": _("调度类型"),
        "cron_time": _("调度周期"),
        "global_args": _("全局参数"),
        "global_env": _("全局环境变量"),
        "parallelism": _("任务并行数"),
        "run_pipeline": _("运行"),
        "status_more": _("状态详情"),
        "status_more_html": _("状态详情"),
        "execution_date": _("执行时间"),
        "base_image": _("基础镜像"),
        "tag": _("tag"),
        "save": _("保存"),
        "history": _("历史"),
        "consecutive_build": _("连续构建"),
        "build": _("构建"),
        "need_gpu": _("需要gpu"),
        "last_image": _("最新镜像"),
        "expired_limit": _("有效实例数目"),
        "canary": _("分流"),
        "shadow": _("流量复制"),
        "log": _("日志"),
        "pod": _("容器"),
        "ide_type": _("IDE类型"),
        "ide_type_html": _("IDE类型"),
        "annotations": _("注释"),
        "annotations_html": _("注释"),
        "spec": _("属性"),
        "spec_html": _("属性"),
        "info_json": _("通知"),
        "info_json_html": _("通知"),
        "labels": _("标签"),
        "label": _("标签"),
        "labels_html": _("标签"),
        "label_url": _("标签"),
        "add_row_time": _("添加时间"),
        "experiment_id": _("实验id"),
        "pipeline_file": _("workflow yaml"),
        "pipeline_file_html": _("workflow yaml"),
        "pipeline_argo_id": _("任务流id"),
        "version_id": _("版本id"),
        "job_template": _("任务模板"),
        "job_template_url": _("任务模板"),
        "template": _("功能模板"),
        "alert_status": _("监控状态"),
        "task_args": _("任务参数"),
        "url_html": _("网址"),
        "url": _("网址"),
        "public": _("公开"),
        "alert_user": _("通知用户"),
        "experiment": _("Experiment yaml"),
        "experiment_html": _("Experiment yaml"),
        "train_model": _("训练模型"),
        "ip": "ip",
        "deploy_time": _("部署时间"),
        "host": _("域名"),
        "host_url": _("域名"),
        "deploy": _("部署"),
        "test_deploy": _("测试部署"),
        "prod_deploy": _("生产部署"),
        "check_test_service": _('检测测试服务'),
        "min_replicas": _("最小副本数"),
        "max_replicas": _("最大副本数"),
        "replicas": _("副本数"),
        "replicas_html": _("副本数"),
        "ports": _("端口"),
        "roll": _("滚动发布"),
        "k8s_yaml": "yaml",
        "service": _("服务"),
        "download_url": _("下载地址"),
        "download_url_html": _("下载地址"),
        "metrics": _("指标"),
        "metrics_html": _("指标"),
        "operate_html": _("操作"),
        "md5": "md5",
        "service_type": _("服务类型"),
        "job_args_definition": _("模板参数定义示例"),
        "job_describe": _("模板描述"),
        "job_args_demo": _("模板参数示例"),
        "stop": _("停止"),
        "parallel_trial_count": _("搜索并行数"),
        "parallel_trial_type": _("搜索并行方式"),
        "max_trial_count": _("最多搜索次数"),
        "max_failed_trial_count": _("最多失败搜索次数"),
        "objective_type": _("目标函数类型"),
        "objective_goal": _("目标值"),
        "objective_metric_name": _("目标度量"),
        "objective_additional_metric_names": _("附加目标度量"),
        "algorithm_name": _("搜索算法"),
        "algorithm_setting": _("搜索算法配置"),
        "parameters": _("超参数配置"),
        "parameters_demo": _("超参配置示例"),
        "parameters_html": _("超参数配置"),
        "job_json": _("搜索任务配置"),
        "trial_spec": _("任务 yaml"),
        "trial_spec_html": _("任务 yaml"),
        "create_experiment": _("启动调度"),
        "run_instance": _("运行实例"),
        "monitoring": _("监控"),
        "monitoring_html": _("监控"),
        "monitoring_url": _("监控"),
        "link": _("链接"),
        "clear": _("清理"),
        "expand": _("扩展"),
        "expand_html": _("扩展"),
        "parameter": _("扩展参数"),
        "parameter_html": _("扩展参数"),
        "renew": _("续期"),
        "api_type": _("接口类型"),
        "code_dir": _("代码目录"),
        "id_url":"id",
        "debug": _("调试"),
        "run": _("运行"),
        "run_url": _("运行"),
        "depends_on_past": _("过往依赖"),
        "max_active_runs": _("最大激活运行数"),
        "des_image": _("目标镜像"),
        "target_image": _("目标镜像"),
        "image_history": _("镜像历史"),
        "elapsed_time": _("耗时"),
        "task_status": _("任务状态"),
        "hpa": _("弹性伸缩容"),
        "cronhpa": _("定时伸缩容"),
        "health": _("健康检查"),
        "transformer": _("处理变换"),
        "deploy_history": _("部署记录"),
        "model_input": _("模型输入"),
        "input_html": _("输入"),
        "model_output": _("模型输出"),
        "output_html": _("输出"),
        "config": _("配置"),
        "config_html": _("配置"),
        "app": _("产品"),
        "field": _("领域"),
        "scenes": _("场景"),
        "cluster": _("集群"),
        "clusters": _("集群"),
        "db_name": _("数据库"),
        "database": _("数据库"),
        "table": _("表名"),
        "table_html": _("表名"),
        "table_name": _("表名"),
        "db": _("数据库"),
        "metadata_db": _("数据库"),
        "metadata_column": _("列信息"),
        "columns": _("列信息"),
        "security_level": _("安全等级"),
        "value_score": _("价值评分"),
        "storage_size": _("存储大小"),
        "storage_type": _("存储类型"),
        "warehouse_level": _("数仓类型"),
        "crontab": _("调度周期"),
        "cost": _("数据成本"),
        "column_type": _("列类型"),
        "remark": _("备注"),
        "remark_html": _("备注"),
        "ttl": _("保留时长"),
        "sql": "sql",
        "sql_html": "sql",
        "primary_part_col_name": _("主分区列"),
        "app_group": _("应用组"),
        "sql_demo":"sql示例",
        "create_table_ddl": _("建表sql"),
        "insert_sql": _("数据导入sql"),
        "metric_type": _("指标类型"),
        "metric_data_type": _("指标数据类型"),
        "metric_dim": _("指标维度"),
        "metric_level": _("指标级别"),
        "metric_responsible": _("指标责任人"),
        "caliber": _("口径"),
        "is_partition": _("是否分区列"),
        "partition_type": _("分区类型"),
        "c_org_fullname": _("组织架构"),
        "lifecycle": _("当前生命周期(天)"),
        "rec_lifecycle": _("推荐生命周期(天)"),
        "storage_cost": _("存储成本(元/月)"),
        "visits_seven": _("7日访问次数"),
        "recent_visit": _("最近访问日期"),
        "partition_start": _("分区开始时间"),
        "partition_end": _("分区结束时间"),
        "visits_thirty": _("30天访问次数"),
        "product_name": _("产品名"),
        "product_id": _("产品id"),
        "product_desc": _("产品描述"),
        "active": _("激活"),
        "job_worker_image": _("工作镜像"),
        "job_worker_command": _("启动命令"),
        "source_type": _("数据源类型"),
        "priority": _("优先级"),
        "owner": _("责任人"),
        "owners": _("责任人"),
        "industry": _("行业"),
        "skip": _("跳过"),
        "etl_pipeline": _("任务流"),
        "etl_pipeline_id": _("任务流id"),
        "etl_task": _("任务"),
        "etl_task_id": _("任务id"),
        "access": _("接入"),
        "inference_config": _("推理配置"),
        "file_type": _("文件类型"),
        "responsible": _("责任人"),
        "cycle_unit": _("周期单位"),
        "task_type": _("任务类型"),
        "task_id": _("任务 id"),
        "creator": _("创建者"),
        "created_by": _("创建者"),
        "changed_by": _("修改者"),
        "created_on": _("创建时间"),
        "create_time": _("创建时间"),
        "update_time": _("更新时间"),
        "changed_on": _("修改时间"),
        "change_time": _("更新时间"),
        "modified": _("修改时间"),
        "cronjob_start_time": _("补录起点"),
        "help": _("帮助"),
        "method": _("方式"),
        "icon": _("图标"),
        "doc": _("文档"),
        "quota": _("资源额度"),
        "bill":_("账单"),
        "invoice":_("发票"),
        "user_id":_("用户id"),
        "email":_("邮箱"),
        "roles":_("角色"),
        "roles_html": _("角色"),
        "dataset": _("数据集"),
        "pre_train_model": _("预训练模型"),
        "source": _("来源"),
        "hot": _("热度"),
        "price": _("价格")
    }

    # print(label_columns)

    # 获取node选择器
    def get_default_node_selector(self,node_selector,resource_gpu,model_type):
        # 先使用项目中定义的选择器
        if not node_selector:
            node_selector=''

        # 不使用用户的填写，完全平台决定
        gpu_num = core.get_gpu(resource_gpu)[0]
        if type(gpu_num)==str and ',' in gpu_num:
            node_selector = node_selector.replace('cpu=true', 'vgpu=true') + ",vgpu=true,%s=true" % model_type
        elif gpu_num>=1 or gpu_num==-1:
            node_selector = node_selector.replace('cpu=true', 'gpu=true') + ",gpu=true,%s=true"%model_type
        elif 1>gpu_num>0:
            node_selector = node_selector.replace('cpu=true', 'vgpu=true') + ",vgpu=true,%s=true" % model_type
        else:
            node_selector = node_selector.replace('gpu=true', 'cpu=true') + ",cpu=true,%s=true"%model_type
        if 'org' not in node_selector:
            node_selector += ',org=public'
        node_selector = re.split(',|;|\n|\t', str(node_selector))
        node_selector = [selector.strip() for selector in node_selector if selector.strip()]
        node_selector = ','.join(list(set(node_selector)))
        return node_selector


class DefaultValue(Enum):
    """定义使用的默认值"""
    # 默认资源配置，内存单位为G
    DEFAULT_RESOURCES_CONFIG = '1核2G'