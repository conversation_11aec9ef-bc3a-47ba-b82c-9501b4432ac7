{"yolov7": {"pipeline": {"name": "imageai", "describe": "深度学习：目标识别训练部署", "project": "public", "parameter": {"demo": "true", "img": "/static/assets/images/pipeline/yolo.jpg"}, "dag_json": {"download-data": {"upstream": []}, "yolov7-object-recognition": {"upstream": ["download-data"]}, "deploy-yolov7-web-service": {"upstream": ["yolov7-object-recognition"]}}}, "tasks": [{"job_templete": "自定义镜像", "name": "download-data", "label": "下载处理标注数据", "volume_mount": "kubeflow-user-workspace(pvc):/mnt/", "args": {"images": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "workdir": "/mnt/{{creator}}/", "command": "rm -rf coco.zip && wget https://cube-studio.oss-cn-hangzhou.aliyuncs.com/pipeline/coco.zip --no-check-certificate && unzip -o coco.zip && cd  coco_data_sample && bash reset_file.sh"}}, {"job_templete": "yolov7", "name": "yolov7-object-recognition", "label": "目标识别训练", "volume_mount": "kubeflow-user-workspace(pvc):/mnt/", "resource_memory": "5G", "resource_cpu": "5", "args": {"--train": "/mnt/{{creator}}/coco_data_sample/train.txt", "--val": "/mnt/{{creator}}/coco_data_sample/valid.txt", "--classes": "person,bicycle,car,motorcycle,airplane,bus,train,truck,boat,trafficlight,firehydrant,stopsign,parkingmeter,bench,bird,cat,dog,horse,sheep,cow,elephant,bear,zebra,giraffe,backpack,umbrella,handbag,tie,suitcase,frisbee,skis,snowboard,sportsball,kite,baseballbat,baseballglove,skateboard,surfboard,tennisracket,bottle,wineglass,cup,fork,knife,spoon,bowl,banana,apple,sandwich,orange,broccoli,carrot,hotdog,pizza,donut,cake,chair,couch,pottedplant,bed,diningtable,toilet,tv,laptop,mouse,remote,keyboard,cellphone,microwave,oven,toaster,sink,refrigerator,book,clock,vase,scissors,teddybear,hairdrier,toothbrush", "--batch_size": "1", "--epoch": "1", "--weights": "/yolov7/weights/yolov7_training.pt", "--save_model_path": "/mnt/{{creator}}/coco_data_sample/yolov7_best.pt"}}, {"job_templete": "deploy-service", "name": "deploy-yolov7-web-service", "volume_mount": "kubeflow-user-workspace(pvc):/mnt/", "label": "部署模型web服务", "args": {"--label": "目标识别推理服务", "--model_name": "yolov7", "--model_version": "v2024.01.01.1", "--model_path": "/mnt/{{creator}}/coco_data_sample/yolov7_best.pt", "--service_type": "serving", "--images": "ccr.ccs.tencentyun.com/cube-studio/yolov7:2024.01", "--working_dir": "/yolov7", "--command": "python server.py", "--env": "MODELPATH=/mnt/{{creator}}/coco_data_sample/yolov7_best.pt\nYOLO_EXAMPLE=/mnt/{{creator}}/coco_data_sample/images/train2014/COCO_train2014_000000000597.jpg", "--ports": "8080", "--replicas": "1", "--resource_memory": "5G", "--resource_cpu": "5", "--resource_gpu": "0"}}]}, "ml": {"pipeline": {"name": "ml", "describe": "机器学习：决策树训练部署", "project": "public", "parameter": {"demo": "true", "img": "/static/assets/images/pipeline/tree.jpg"}, "dag_json": {"data-import": {}, "data-process": {"upstream": ["data-import"]}, "model-train": {"upstream": ["data-process"]}, "model-val": {"upstream": ["model-train"]}, "model-register": {"upstream": ["model-val"]}, "model-offline-inference": {"upstream": ["model-val"]}, "deploy-service": {"upstream": ["model-register"]}}, "global_env": "", "expand": [{"id": "model-train", "type": "dataSet", "position": {"x": 384, "y": 232}, "data": {"info": {"describe": "decision-tree算法"}, "name": "model-train", "label": "模型训练"}}, {"id": "model-val", "type": "dataSet", "position": {"x": 384, "y": 328}, "data": {"info": {"describe": "decision-tree算法"}, "name": "model-val", "label": "模型评估"}}, {"id": "model-offline-inference", "type": "dataSet", "position": {"x": 224, "y": 440}, "data": {"info": {"describe": "decision-tree算法"}, "name": "model-offline-inference", "label": "模型离线推理"}}, {"id": "data-import", "type": "dataSet", "position": {"x": 380, "y": -16}, "data": {"info": {"describe": "datax异构数据源同步"}, "name": "data-import", "label": "数据导入"}}, {"id": "data-process", "type": "dataSet", "position": {"x": 380, "y": 111}, "data": {"info": {"describe": "使用用户自定义镜像作为运行镜像"}, "name": "data-process", "label": "数据处理"}}, {"id": "model-register", "type": "dataSet", "position": {"x": 524, "y": 447}, "data": {"info": {"describe": "注册模型"}, "name": "model-register", "label": "模型注册"}}, {"id": "deploy-service", "type": "dataSet", "position": {"x": 524, "y": 556}, "data": {"info": {"describe": "模型部署推理服务"}, "name": "deploy-service", "label": "服务部署"}}, {"source": "data-import", "arrowHeadType": "arrow", "target": "data-process", "id": "logic__edge-data-importnull-data-processnull"}, {"source": "data-process", "arrowHeadType": "arrow", "target": "model-train", "id": "logic__edge-data-processnull-model-trainnull"}, {"source": "model-train", "arrowHeadType": "arrow", "target": "model-val", "id": "logic__edge-model-trainnull-model-valnull"}, {"source": "model-val", "arrowHeadType": "arrow", "target": "model-offline-inference", "id": "logic__edge-model-valnull-model-offline-inferencenull"}, {"source": "model-val", "arrowHeadType": "arrow", "target": "model-register", "id": "logic__edge-model-valnull-model-registernull"}, {"source": "model-register", "arrowHeadType": "arrow", "target": "deploy-service", "id": "logic__edge-model-registernull-deploy-servicenull"}]}, "tasks": [{"job_templete": "decision-tree", "name": "model-train", "label": "模型训练", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "args": {"--train_dataset": "/mnt/{{creator}}/pipeline/example/ml/train.csv", "--label_columns": "y", "--save_model_dir": "/mnt/{{creator}}/pipeline/example/ml/", "--feature_columns": "age,duration,campaign,pdays,previous,emp_var_rate,cons_price_idx,cons_conf_idx,euribor3m,nr_employed", "--model_params": {"max_depth": 5, "min_samples_leaf": 10}, "--inference_dataset": "", "--val_dataset": ""}}, {"job_templete": "decision-tree", "name": "model-val", "label": "模型评估", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "args": {"--train_dataset": "", "--label_columns": "y", "--save_model_dir": "/mnt/{{creator}}/pipeline/example/ml/", "--feature_columns": "age,duration,campaign,pdays,previous,emp_var_rate,cons_price_idx,cons_conf_idx,euribor3m,nr_employed", "--model_params": "", "--inference_dataset": "", "--val_dataset": "/mnt/{{creator}}/pipeline/example/ml/train.csv"}}, {"job_templete": "decision-tree", "name": "model-offline-inference", "label": "模型离线推理", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "args": {"--train_dataset": "", "--label_columns": "", "--save_model_dir": "/mnt/{{creator}}/pipeline/example/ml/", "--feature_columns": "age,duration,campaign,pdays,previous,emp_var_rate,cons_price_idx,cons_conf_idx,euribor3m,nr_employed", "--model_params": "", "--inference_dataset": "/mnt/{{creator}}/pipeline/example/ml/inference.csv", "--val_dataset": ""}}, {"job_templete": "datax", "name": "data-import", "label": "数据导入", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "args": {"-f": "/mnt/{{creator}}/pipeline/example/ml/mysql-csv.json"}}, {"job_templete": "自定义镜像", "name": "data-process", "label": "数据处理", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "args": {"images": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "workdir": "/mnt/{{creator}}/pipeline/example/ml/", "command": "sh fix.sh"}}, {"job_templete": "model-register", "name": "model-register", "label": "模型注册", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "args": {"--project_name": "public", "--model_name": "decision-tree", "--model_version": "{{ datetime.datetime.now().strftime('v%Y.%m.%d.1') }}", "--model_path": "/mnt/{{creator}}/pipeline/example/ml/decisionTree_model.pkl", "--model_metric": "", "--describe": "决策树模型", "--framework": "sklearn", "--inference_framework": "ml-server"}}, {"job_templete": "deploy-service", "name": "deploy-service", "label": "服务部署", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "args": {"--project_name": "public", "--label": "决策树模型", "--model_name": "decisiontree", "--model_version": "v2023.11.01.1", "--model_path": "/mnt/{{creator}}/pipeline/example/ml/decisionTree_model.pkl", "--service_type": "ml-server", "--images": "ccr.ccs.tencentyun.com/cube-studio/ml-server:20240601", "--working_dir": "", "--command": "", "--env": "", "--host": "", "--ports": "80", "--replicas": "1", "--resource_memory": "2G", "--resource_cpu": "2", "--resource_gpu": "0", "--volume_mount": "kubeflow-user-workspace(pvc):/mnt", "--inference_config": "---config.json\n\n[\n    {\n        \"name\": \"decisiontree\",\n        \"model_path\": \"/mnt/admin/pipeline/example/ml/decisionTree_model.pkl\",\n        \"framework\": \"sklearn\",\n        \"version\": \"v2023.10.01.1\",\n        \"output\": [\n            { \"name\": \"y\", \"type\": \"enum\", \"choices\": [0,1] }\n        ],\n        \"input\": [\n            { \"name\": \"age\", \"type\": \"int\" },\n            { \"name\": \"duration\", \"type\": \"int\" },\n            { \"name\": \"campaign\", \"type\": \"int\" },\n            { \"name\": \"pdays\", \"type\": \"int\" },\n            { \"name\": \"previous\", \"type\": \"enum\", \"choices\": [0,1,2,3,4,5,6] },\n            { \"name\": \"emp_var_rate\", \"type\": \"float\" },\n            { \"name\": \"cons_price_idx\", \"type\": \"float\" },\n            { \"name\": \"cons_conf_idx\", \"type\": \"float\" },\n            { \"name\": \"euribor3m\", \"type\": \"float\" },\n            { \"name\": \"nr_employed\", \"type\": \"float\" }\n        ],\n        \"example\": [\n            { \"age\": 30, \"duration\": 487, \"campaign\": 2, \"pdays\": 999, \"previous\": 0, \"emp_var_rate\": -1.8, \"cons_price_idx\": 92.893, \"cons_conf_idx\": -46.2, \"euribor3m\": 1.313, \"nr_employed\": 5099.1 }\n        ],\n        \"enable\": true\n    }\n]"}}]}, "deepseek": {"pipeline": {"name": "deepseek", "describe": "大模型：deepseek微调部署", "project": "public", "parameter": {"demo": "true", "img": "/static/assets/images/pipeline/deepseek.jpg"}, "dag_json": {"task-data-process": {}, "deepseek-lora": {"upstream": ["task-data-process"]}, "deploy-deepseek": {"upstream": ["deepseek-lora"]}}, "global_env": "", "expand": [{"id": "deploy-deepseek", "type": "dataSet", "position": {"x": 224, "y": 442}, "data": {"info": {"describe": "模型部署推理服务"}, "name": "deploy-deepseek", "label": "部署deepseek"}}, {"id": "task-data-process", "type": "dataSet", "position": {"x": 224, "y": 133}, "data": {"info": {"describe": "使用用户自定义镜像作为运行镜像"}, "name": "task-data-process", "label": "数据准备"}}, {"id": "deepseek-lora", "type": "dataSet", "position": {"x": 224, "y": 272}, "data": {"info": {"describe": "deepseek lora微调"}, "name": "deepseek-lora", "label": "deepseek lora微调"}}, {"source": "task-data-process", "arrowHeadType": "arrow", "target": "deepseek-lora", "id": "logic__edge-task-null-deepseek-null"}, {"source": "deepseek-lora", "arrowHeadType": "arrow", "target": "deploy-deepseek", "id": "logic__edge-deepseek-null-deploy-deepseeknull"}]}, "tasks": [{"job_templete": "deploy-service", "name": "deploy-deepseek", "label": "部署deepseek", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "resource_rdma": "0", "args": {"--project_name": "public", "--label": "deepseek推理部署", "--model_name": "deepseek", "--model_version": "v2025.03.01.1", "--model_path": "/mnt/{{creator}}/pipeline/example/deepseek/result/exported_model", "--service_type": "vllm", "--images": "vllm/vllm-openai:latest", "--working_dir": "", "--command": "python3 -m vllm.entrypoints.openai.api_server --trust-remote-code --max-model-len 8192 --model $KUBEFLOW_MODEL_PATH --host 0.0.0.0 --port 8000 --dtype float16 --tensor-parallel-size $RESOURCE_GPU --served-model-name $KUBEFLOW_MODEL_NAME", "--env": "HF_ENDPOINT=https://hf-mirror.com", "--host": "/v1/models/", "--ports": "8000", "--replicas": "1", "--resource_memory": "20G", "--resource_cpu": "10", "--resource_gpu": "1", "--volume_mount": "kubeflow-user-workspace(pvc):/mnt", "--inference_config": "", "--metrics": "", "--health": "8000:/v1/models/"}}, {"job_templete": "自定义镜像", "name": "task-data-process", "label": "数据准备", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "resource_rdma": "0", "args": {"images": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "workdir": "/mnt/{{creator}}/pipeline/example/deepseek/", "command": "python data-process-identiy.py"}}, {"job_templete": "deepseek", "name": "deepseek-lora", "label": "deepseek lora微调", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "40G", "resource_cpu": "10", "resource_gpu": "1", "resource_rdma": "0", "args": {"--model_name": "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B", "--model_path": "/mnt/{{creator}}/pipeline/example/deepseek/DeepSeek-R1-Distill-Qwen-7B/", "--dataset": {"path": "/mnt/{{creator}}/pipeline/example/deepseek/identity.json"}, "--template": "deepseek3", "--finetuning_type": "lora", "--lora_target": "all", "--output_dir": "/mnt/{{creator}}/pipeline/example/deepseek/result/", "--per_device_train_batch_size": "4", "--gradient_accumulation_steps": "4", "--lr_scheduler_type": "cosine", "--logging_steps": "10", "--save_steps": "100", "--learning_rate": "5e-5", "--num_train_epochs": "5", "--max_samples": "500", "--max_grad_norm": "1", "--fp16": "true", "--merge_lora": "true"}}]}, "electric-bicycle": {"pipeline": {"name": "electric-bicycle", "describe": "示例：电瓶车全流程(自动化标注-微调-部署)", "project": "public", "parameter": {}, "dag_json": {"download-model": {}, "labelstudio-train": {"upstream": ["download-model"]}, "yolov8-object-recognition": {"upstream": ["labelstudio-train"]}, "deploy-yolov8-web-service": {"upstream": ["yolov8-object-recognition"]}}, "global_env": "YYYYMMDD={{datetime.datetime.now().strftime('%Y-%m-%d')}}", "expand": [{"id": "labelstudio-train", "type": "dataSet", "position": {"x": 400, "y": 247}, "data": {"info": {"describe": "使用用户自定义镜像作为运行镜像"}, "name": "labelstudio-train", "label": "标注结果转为训练数据"}}, {"id": "yolov8-object-recognition", "type": "dataSet", "position": {"x": 400, "y": 363}, "data": {"info": {"describe": "yolo目标识别"}, "name": "yolov8-object-recognition", "label": "目标识别训练"}}, {"id": "deploy-yolov8-web-service", "type": "dataSet", "position": {"x": 400, "y": 463}, "data": {"info": {"describe": "模型部署推理服务"}, "name": "deploy-yolov8-web-service", "label": "部署模型web服务"}}, {"id": "download-model", "type": "dataSet", "position": {"x": 400, "y": 127}, "data": {"info": {"describe": "使用用户自定义镜像作为运行镜像"}, "name": "download-model", "label": "下载校验数据和预训练模型"}}, {"source": "download-model", "arrowHeadType": "arrow", "target": "labelstudio-train", "id": "logic__edge-download-modelnull-labelstudio-trainnull"}, {"source": "labelstudio-train", "arrowHeadType": "arrow", "target": "yolov8-object-recognition", "id": "logic__edge-labelstudio-trainnull-yolov8-object-recognitionnull"}, {"source": "yolov8-object-recognition", "arrowHeadType": "arrow", "target": "deploy-yolov8-web-service", "id": "logic__edge-yolov8-object-recognitionnull-deploy-yolov8-web-servicenull"}]}, "tasks": [{"job_templete": "自定义镜像", "name": "labelstudio-train", "label": "标注结果转为训练数据", "volume_mount": "kubeflow-user-workspace(pvc):/mnt/,/data/k8s/kubeflow/labelstudio(hostpath):/labelstudio/", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "resource_rdma": "0", "args": {"images": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "workdir": "/mnt/{{creator}}/pipeline/example/yolo/electric-bicycle", "command": "python yolo-data-process.py"}}, {"job_templete": "yolov8", "name": "yolov8-object-recognition", "label": "目标识别训练", "volume_mount": "kubeflow-user-workspace(pvc):/mnt/", "resource_memory": "5G", "resource_cpu": "5", "resource_gpu": "1", "resource_rdma": "0", "args": {"--train": "/mnt/{{creator}}/pipeline/example/yolo/electric-bicycle/dataset/train.txt", "--val": "/mnt/{{creator}}/pipeline/example/yolo/electric-bicycle/val1.txt", "--classes": "Electric-bicycle", "--batch_size": "1", "--epoch": "1", "--weights": "/mnt/{{creator}}/pipeline/example/yolo/electric-bicycle/electric-bicycle-new.pt", "--save_model_path": "/mnt/{{creator}}/pipeline/example/yolo/electric-bicycle/electric-bicycle-new.pt"}}, {"job_templete": "deploy-service", "name": "deploy-yolov8-web-service", "label": "部署模型web服务", "volume_mount": "kubeflow-user-workspace(pvc):/mnt/", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "resource_rdma": "0", "args": {"--project_name": "public", "--label": "目标识别推理服务", "--model_name": "electric-bicycle", "--model_version": "v20240701", "--model_path": "/mnt/{{creator}}/pipeline/example/yolo/electric-bicycle/electric-bicycle-new.pt", "--service_type": "serving", "--images": "ccr.ccs.tencentyun.com/cube-studio/yolov8:2024.07", "--working_dir": "/yolov8", "--command": "python server.py", "--env": "MODELPATH=/mnt/admin/pipeline/example/yolo/electric-bicycle/electric-bicycle-new.pt\nYOLO_SAVE_DIR=/mnt/admin/pipeline/example/yolo/inference/\nYOLO_EXAMPLE=https://cube-studio.oss-cn-hangzhou.aliyuncs.com/labelstudio/vision/electric-bicycle/2020_2582.jpg", "--host": "", "--ports": "8080", "--replicas": "1", "--resource_memory": "5G", "--resource_cpu": "5", "--resource_gpu": "1", "--volume_mount": "kubeflow-user-workspace(pvc):/mnt,/data/k8s/kubeflow/labelstudio(hostpath):/labelstudio/", "--inference_config": "", "--metrics": "", "--health": ""}}, {"job_templete": "自定义镜像", "name": "download-model", "label": "下载校验数据和预训练模型", "volume_mount": "kubeflow-user-workspace(pvc):/mnt", "resource_memory": "2G", "resource_cpu": "2", "resource_gpu": "0", "resource_rdma": "0", "args": {"images": "ccr.ccs.tencentyun.com/cube-studio/ubuntu-gpu:cuda11.8.0-cudnn8-python3.9", "workdir": "/mnt/{{creator}}/pipeline/example/yolo/", "command": "rm -rf electric-bicycle.zip && wget https://cube-studio.oss-cn-hangzhou.aliyuncs.com/pipeline/electric-bicycle/electric-bicycle.zip --no-check-certificate && unzip -n electric-bicycle.zip && cd electric-bicycle && sh reset.sh"}}]}}