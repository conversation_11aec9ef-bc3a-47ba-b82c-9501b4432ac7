<div id="di-feature" class="di-feature e-ani">
  <a href="javascript:;" class="close">知道了</a>
  <h5 class="title"></h5>
  <div class="content"></div>
  <script>
    $(function ($) {
      $.getJSON('/myapp/feature/check', { url: location.href.substr(location.href.indexOf('/', 8)) }, (data) => {
        if (data && data.hit) {
          const view = $('#di-feature');
          view.find('.close').one('click', () => {
            view.removeClass('on');
          });
          setTimeout(() => {
            view.find('.title').text(data.title || '新特性');
            view.find('.content').html(data.content);
            view.addClass('on');
          }, 500);
          if (data.delay) {
            setTimeout(() => {
              view.removeClass('on');
            }, data.delay);
          }
        }
      });
    });
  </script>
</div>