[supervisord]
nodaemon=true  # 在前台运行 supervisord 主进程
pidfile=/var/run/supervisord.pid
logfile=/var/log/supervisor/supervisord.log

[program:watch-workflow]
directory=/home/<USER>
autostart=true
autorestart=true
command=python myapp/tools/watch_workflow.py
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_maxbytes=0
stderr_maxbytes=0
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0


[program:watch-service]
directory=/home/<USER>
autostart=true
autorestart=true
command=python myapp/tools/watch_service.py
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_maxbytes=0
stderr_maxbytes=0
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0
