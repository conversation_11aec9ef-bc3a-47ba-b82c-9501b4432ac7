#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{- if not (eq .Values.worker.enabled false) -}}

{{- $shortCircuitEnabled := .Values.shortCircuit.enabled }}
{{- $needDomainSocketVolume := and $shortCircuitEnabled (eq .Values.shortCircuit.policy "uuid") }}
{{- $hostNetwork := .Values.worker.hostNetwork }}
{{- $hostPID := .Values.worker.hostPID }}
{{- $fullName := include "alluxio.fullname" . }}

apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ template "alluxio.fullname" . }}-worker
  labels:
    app: {{ template "alluxio.name" . }}
    chart: {{ template "alluxio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-worker
spec:
  selector:
    matchLabels:
      app: {{ template "alluxio.name" . }}
      release: {{ .Release.Name }}
      role: alluxio-worker
  template:
    metadata:
      labels:
        app: {{ template "alluxio.name" . }}
        chart: {{ template "alluxio.chart" . }}
        release: {{ .Release.Name }}
        heritage: {{ .Release.Service }}
        role: alluxio-worker
    {{- if or .Values.worker.podAnnotations .Values.metrics.enabled }}
      annotations:
      {{- with .Values.worker.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.metrics.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- end }}
    spec:
      hostNetwork: {{ $hostNetwork }}
      hostPID: {{ $hostPID }}
      dnsPolicy: {{ .Values.worker.dnsPolicy | default ($hostNetwork | ternary "ClusterFirstWithHostNet" "ClusterFirst") }}
      {{- if .Values.hostAliases }}
      {{- include "alluxio.hostAliases" . | nindent 6 }}
      {{- end }}
      nodeSelector:
      {{- if .Values.worker.nodeSelector }}
{{ toYaml .Values.worker.nodeSelector | trim | indent 8  }}
      {{- else if .Values.nodeSelector }}
{{ toYaml .Values.nodeSelector | trim | indent 8  }}
      {{- end }}
      shareProcessNamespace: {{ .Values.worker.shareProcessNamespace | default false }}
      tolerations:
      {{- if .Values.worker.tolerations }}
{{ toYaml .Values.worker.tolerations | trim | indent 8  }}
      {{- end }}
      {{- if .Values.tolerations }}
{{ toYaml .Values.tolerations | trim | indent 8  }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.user }}
        runAsGroup: {{ .Values.group }}
        fsGroup: {{ .Values.fsGroup }}
      {{- if eq .Values.worker.fuseEnabled true }}
        {{- if or (or (ne .Values.user 0.0) (ne .Values.group 0.0)) (ne .Values.fsGroup 0.0) }}
          {{- fail ".Values.{user, group, fsGroup} have to be 0 in order to launch Fuse in worker process" }}
        {{- end }}
      {{- end }}
      {{- if .Values.worker.serviceAccount }}
      serviceAccountName: {{ .Values.worker.serviceAccount }}
      {{- else if .Values.serviceAccount }}
      serviceAccountName: {{ .Values.serviceAccount }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
{{ include "alluxio.imagePullSecrets" . | indent 6 }}
      {{- end}}
      containers:
        {{- if .Values.worker.extraContainers }}
{{ include "alluxio.extraContainers" (dict "extraContainers" .Values.worker.extraContainers) | indent 8 }}
        {{- end }}
        - name: alluxio-worker
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          {{- if .Values.worker.resources  }}
{{ include "alluxio.worker.resources" . | indent 10 }}
          {{- end }}
          command: ["tini", "--", "/entrypoint.sh"]
          {{- if .Values.worker.args }}
          args:
{{ toYaml .Values.worker.args | trim | indent 12 }}
          {{- end }}
          env:
          - name: ALLUXIO_WORKER_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          {{- if not .Values.worker.hostNetwork }}
          - name: ALLUXIO_WORKER_CONTAINER_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          {{- end }}
          {{- if eq .Values.worker.fuseEnabled true }}
          - name: ALLUXIO_CLIENT_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          securityContext:
            privileged: true
            capabilities:
              add:
                - SYS_ADMIN
          {{- end }}
          {{- range $key, $value := .Values.worker.env }}
          - name: "{{ $key }}"
            value: "{{ $value }}"
          {{- end }}
          envFrom:
          - configMapRef:
              name: {{ template "alluxio.fullname" . }}-config
          readinessProbe:
            tcpSocket:
              port: rpc
            initialDelaySeconds: {{ .Values.worker.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.worker.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.worker.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.worker.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.worker.readinessProbe.successThreshold }}
          livenessProbe:
            tcpSocket:
              port: rpc
            initialDelaySeconds: {{ .Values.worker.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.worker.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.worker.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.worker.livenessProbe.failureThreshold }}
          {{- if .Values.worker.startupProbe }}
          startupProbe:
            tcpSocket:
              port: rpc
            initialDelaySeconds: {{ .Values.worker.startupProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.worker.startupProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.worker.startupProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.worker.startupProbe.failureThreshold }}
          {{- end}}
          ports:
          - containerPort: {{ .Values.worker.ports.rpc }}
            name: rpc
          - containerPort: {{ .Values.worker.ports.web }}
            name: web
          volumeMounts:
            {{- if .Values.worker.extraVolumeMounts }}
{{- include "alluxio.extraVolumeMounts" (dict "extraVolumeMounts" .Values.worker.extraVolumeMounts) | indent 12 }}
            {{- end }}
            {{- if .Values.metrics.enabled }}
            - name: {{ $fullName }}-metrics-volume
              mountPath: /config/metrics
            {{- end }}
            {{- if $needDomainSocketVolume }}
            - name: alluxio-domain
              mountPath: /opt/domain
            {{- end }}
            {{- if .Values.secrets -}}
              {{- if .Values.secrets.worker -}}
{{- include "alluxio.worker.secretVolumeMounts" . }}
              {{- end -}}
            {{- end -}}
            {{- if .Values.configmaps }}
              {{- if .Values.configmaps.worker }}
{{- include "alluxio.worker.configmapVolumeMounts" . }}
              {{- end }}
            {{- end }}
            {{- if .Values.tieredstore -}}
{{- include "alluxio.worker.tieredstoreVolumeMounts" . }}
            {{- end -}}
            {{- if .Values.mounts -}}
{{- include "alluxio.worker.otherVolumeMounts" . }}
            {{- end }}
            {{- if eq .Values.worker.fuseEnabled true }}
            - name: alluxio-fuse-device
              mountPath: /dev/fuse
            - name: alluxio-fuse-mount
              mountPath: {{ .Values.fuse.mountPath | dir }}
              mountPropagation: Bidirectional
            {{- end}}
        - name: alluxio-job-worker
          image: {{ .Values.image }}:{{ .Values.imageTag }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          {{- if .Values.jobWorker.resources  }}
{{ include "alluxio.jobWorker.resources" . | indent 10 }}
          {{- end }}
          command: ["tini", "--", "/entrypoint.sh"]
          {{- if .Values.jobWorker.args }}
          args:
{{ toYaml .Values.jobWorker.args | trim | indent 12 }}
          {{- end }}
          env:
          - name: ALLUXIO_WORKER_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          {{- if not .Values.worker.hostNetwork }}
          - name: ALLUXIO_WORKER_CONTAINER_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          {{- end }}
          {{- range $key, $value := .Values.jobWorker.env }}
          - name: "{{ $key }}"
            value: "{{ $value }}"
          {{- end }}
          envFrom:
          - configMapRef:
              name: {{ template "alluxio.fullname" . }}-config
          readinessProbe:
            tcpSocket:
              port: job-rpc
            initialDelaySeconds: {{ .Values.jobWorker.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.jobWorker.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.jobWorker.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.jobWorker.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.jobWorker.readinessProbe.successThreshold }}
          livenessProbe:
            tcpSocket:
              port: job-rpc
            initialDelaySeconds: {{ .Values.jobWorker.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.jobWorker.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.jobWorker.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.jobWorker.livenessProbe.failureThreshold }}
          {{- if .Values.jobWorker.startupProbe }}
          startupProbe:
            tcpSocket:
              port: job-rpc
            initialDelaySeconds: {{ .Values.jobWorker.startupProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.jobWorker.startupProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.jobWorker.startupProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.jobWorker.startupProbe.failureThreshold }}
          {{- end}}
          ports:
          - containerPort: {{ .Values.jobWorker.ports.rpc }}
            name: job-rpc
          - containerPort: {{ .Values.jobWorker.ports.data }}
            name: job-data
          - containerPort: {{ .Values.jobWorker.ports.web }}
            name: job-web
          volumeMounts:
            {{- if .Values.worker.extraVolumeMounts }}
{{- include "alluxio.extraVolumeMounts" (dict "extraVolumeMounts" .Values.worker.extraVolumeMounts) | indent 12 }}
            {{- end }}
            {{- if .Values.metrics.enabled }}
            - name: {{ $fullName }}-metrics-volume
              mountPath: /config/metrics
            {{- end }}
            {{- if $needDomainSocketVolume }}
            - name: alluxio-domain
              mountPath: /opt/domain
            {{- end }}
            {{- if .Values.secrets }}
              {{- if .Values.secrets.worker }}
{{- include "alluxio.worker.secretVolumeMounts" . }}
              {{- end -}}
            {{- end }}
            {{- if .Values.configmaps }}
              {{- if .Values.configmaps.worker }}
{{- include "alluxio.worker.configmapVolumeMounts" . }}
              {{- end }}
            {{- end }}
            {{- if .Values.tieredstore }}
{{- include "alluxio.worker.tieredstoreVolumeMounts" . }}
            {{- end }}
            {{- if .Values.mounts }}
{{- include "alluxio.worker.otherVolumeMounts" . }}
            {{- end }}
      restartPolicy: Always
      volumes:
        {{- if .Values.worker.extraVolumes }}
{{- include "alluxio.extraVolumes" (dict "extraVolumes" .Values.worker.extraVolumes) | indent 8 }}
        {{- end }}
        {{- if .Values.metrics.enabled }}
        - name: {{ $fullName }}-metrics-volume
          configMap:
            name: {{ $fullName }}-metrics
        {{- end }}
        {{- if $needDomainSocketVolume }}
{{- include "alluxio.worker.shortCircuit.volume" . }}
        {{- end }}
        {{- if .Values.secrets }}
          {{- if .Values.secrets.worker }}
{{- include "alluxio.worker.secretVolumes" . }}
          {{- end }}
        {{- end }}
        {{- if .Values.tieredstore }}
{{- include "alluxio.worker.tieredstoreVolumes" . }}
        {{- end}}
        {{- if .Values.mounts }}
          {{- range .Values.mounts }}
        - name: "{{ .name }}"
          persistentVolumeClaim:
            claimName: "{{ .name }}"
          {{- end }}
        {{- end }}
        {{- if .Values.worker.fuseEnabled }}
        - name: alluxio-fuse-device
          hostPath:
            path: /dev/fuse
            type: CharDevice
        - name: alluxio-fuse-mount
          hostPath:
            path: {{ .Values.fuse.mountPath | dir }}
            type: Directory
        {{- end }}
{{- end }}
