{"version": 3, "file": "static/js/204.aa9db224.chunk.js", "mappings": "mNA6Be,SAASA,EAAYC,GAChC,IACIC,EADEC,EAAKC,KAAKC,SAASC,SAAS,IAAIC,UAAU,GAGhD,GAAkCC,EAAAA,EAAAA,UAAmBP,EAAMQ,SAAW,IAAG,eAAlEC,EAAS,KAAEC,EAAY,KAC9B,GAA0BH,EAAAA,EAAAA,UAASP,EAAMW,OAAS,IAAG,eAA9CA,EAAK,KAAEC,EAAQ,MAEtBC,EAAAA,EAAAA,YAAU,WACN,IAAMC,EAAad,EAAMe,mBAAqBf,EAAMQ,SAAW,IAAIQ,QAAO,SAAAC,GACtE,OAAgC,IAAzBA,EAAKC,QAAQP,EACxB,IAAMX,EAAMQ,SAAW,GACvBE,EAAaI,EACjB,GAAG,CAACd,EAAMQ,WAEVK,EAAAA,EAAAA,YAAU,WACND,EAASZ,EAAMW,OAAS,GAE5B,GAAG,CAACX,EAAMW,QAEV,IAAMQ,EAAe,SAACR,GAClBC,EAASD,GACTX,EAAMoB,UAAYpB,EAAMoB,SAAST,EACrC,EAeMU,EAAmB,SAACJ,GACtB,IAAMK,EAAUX,EACVY,EAAQN,EAAKC,QAAQP,GAC3B,IAAe,IAAXY,EACA,OAAO,0BAAON,IAElB,IAAMO,EAASP,EAAKX,UAAU,EAAGiB,GAC3BE,EAAUR,EAAKX,UAAUiB,EAAQZ,EAAMe,QAC7C,OAAO,4BAAOF,GAAO,iBAAMG,UAAU,YAAW,SAAEL,IAAgBG,IACtE,EAYMG,EAVW,SAACC,GAA+B,IACzCC,EADoBC,EAAI,uDAAG,IAE/B,OAAO,WAA+B,IAAD,uBAAjBC,EAAI,yBAAJA,EAAI,gBACpBC,aAAaH,GACbA,EAAQI,YAAW,WACfL,GAAOA,EAAIM,MAAM,KAAK,GAAD,OAAMH,GAC/B,GAAGD,EACP,CACJ,CAEuBK,CAASpC,EAAMqC,gBAoBtC,OACI,iBAAKV,UAAU,qBAAoB,UAE3B3B,EAAMsC,WAAY,kBAAOC,QAASrC,EAAIyB,UAAU,mBAAkB,SAAE3B,EAAMsC,YAAqB,MAEnG,iBAAKX,UAAU,aAAaa,MAAO,CAAEC,MAAOzC,EAAMyC,OAAS,QAAY,WACnE,SAAC,IAAK,CACFD,MAAO,CAAEC,MAAO,QAChBC,SAAU1C,EAAM0C,SAChBxC,GAAIA,EACJyC,YAAa3C,EAAM2C,aAAe,GAClCC,UAAW5C,EAAM4C,WAAa,IAC9BxB,SAAU,SAACyB,GAAgC,OAAW1B,EAAa0B,EAAEC,OAAOnC,MAAM,EAClFoC,WA9DO,SAACF,GAEU,KAA1BA,EAAEG,YAAYC,UACdhD,EAASiD,MAAQjD,EAASiD,OAC1BlD,EAAMmD,UAAYnD,EAAMmD,SAASN,EAAEO,cAAczC,OAEzD,EAyDgBA,MAAOA,EACP0C,IAAK,SAAAC,GAAO,OAAIrD,EAAWqD,CAAO,KAEtC,SAACC,EAAA,EAAc,CAAC5B,UAAU,kBAK1BlB,EAAUiB,QAAS,gBAAIC,UAAU,uBAAuB6B,SAxC/C,SAACX,GAClBA,EAAEY,kBASF,IAAQL,EAAkBP,EAAlBO,cACAM,EAA0CN,EAA1CM,UAAWC,EAA+BP,EAA/BO,aAA+BP,EAAjBQ,aACCD,EAAeD,EAChC,IACb1D,EAAMqC,gBAAkBT,GAEhC,EAwB4FY,MAAO,CAAE,UAAY,GAAD,OAAKxC,EAAM6D,UAAS,OAAO,UAEvH7D,EAAM8D,SAAU,gBAAKnC,UAAU,cAAca,MAAO,CAAEuB,MAAM,GAAD,OAAK,EAAC,MAAMC,IAAI,GAAD,OAAK,EAAC,OAAO,UACnF,gBAAKrC,UAAU,WAAWa,MAAO,CAAEuB,MAAM,GAAD,OAAK,EAAC,MAAMC,IAAI,GAAD,OAAK,EAAC,WACxD,KAGTvD,EAAUwD,KAAI,SAAChD,EAAMM,GACjB,OAAO,eAAII,UAAU,SAASuC,YAAa,kBApF/C,SAACvD,GACjBQ,EAAaR,GACbX,EAAMmE,SAAWnE,EAAMmE,QAAQxD,EACnC,CAiF+EyD,CAAYnD,EAAK,EAAC,SAAcI,EAAiBJ,IAAzBM,EACnF,OAEA,OAIxB,C,0UCtIM8C,EAAkBC,EAAQ,OA6B1BC,EAAiB,SAAH,GAAgD,IAA1CC,EAAQ,EAARA,SAAU/B,EAAK,EAALA,MAAUgC,GAAS,YACtD,OAAKhC,GAKJ,SAAC,EAAAiC,UAAS,CACTjC,MAAOA,EACPkC,OAAQ,EACRC,QACC,iBACCjD,UAAU,yBACVwC,QAAS,SAACtB,GACTA,EAAEY,iBACH,IAGFe,SAAUA,EACVK,cAAe,CAAEC,sBAAsB,GAAQ,UAE/C,iCAAQL,GAAS,IAAEjC,OAAK,kBAAgB,OAATiC,QAAS,IAATA,OAAS,EAATA,EAAWjC,OAAK,IAAEuC,WAAY,eAlBvD,yBAAQN,GAqBjB,EAiRA,EA/QiB,SAACzE,GACjB,OAAkDO,EAAAA,EAAAA,WAAS,GAAM,eAA1DyE,EAAiB,KAAEC,EAAoB,KAC9C,GAAoC1E,EAAAA,EAAAA,UAAyC,CAC5E2E,OAAQ,GACRC,KAAM,KACL,eAHKC,EAAU,KAAEC,EAAa,KAIhC,GAAsC9E,EAAAA,EAAAA,UAAgB,IAAG,eAAlD+E,EAAW,KAAEC,EAAc,KAGlC,GAAwBhF,EAAAA,EAAAA,UAASP,EAAMwF,SAAQ,eAAxCC,EAAI,KAAEC,EAAO,KACdC,EAAe,SAACpE,GACrB,OAAO,SAACqE,EAAO,GAAoB,IAAjBC,EAAI,EAAJA,KACjB,KAAIA,EAAKpD,MAAQ,KAAjB,CACA,IAAMqD,GAAI,OAAOL,GACjBK,EAAKvE,IAAM,kBAAQuE,EAAKvE,IAAM,IAAEkB,MAAOoD,EAAKpD,QAC5C,IAAMsD,EAAaD,EAAKE,QAAO,SAACC,EAAUC,GAAS,OAAKD,EAAMC,EAAKzD,OAAS,GAAG,GAAE,GAAK,IACtF0D,aAAaC,QAAQpG,EAAMqG,UAAY,GAAIC,KAAKC,UAAUT,IAE1DU,GAAsB,kBAAKC,GAAkB,IAAEC,EAAGX,KAClDL,EAAQI,EAPoB,CAQ7B,CACD,EACMa,EAAgBlB,EAAKxB,KAAI,SAAC2C,EAAUrF,GACzC,OAAO,kBACHqF,GAAG,IACNnE,MAAOmE,EAAInE,OAAS,IACpBoE,aAAc,SAACC,GACd,MAAO,CACNrE,MAAOqE,EAAOrE,MACd+B,SAAUmB,EAAapE,GAEzB,GAEF,IACA,GAAoDhB,EAAAA,EAAAA,UAASP,EAAM+G,QAAO,eAAnEN,EAAkB,KAAED,EAAqB,KAChD,GAAoBQ,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAAO,EAAJC,MAEXrG,EAAAA,EAAAA,YAAU,WACT6E,EAAQ1F,EAAMwF,QACf,GAAG,CAACxF,EAAMwF,WAEV3E,EAAAA,EAAAA,YAAU,WACT2F,EAAsBxG,EAAM+G,OAC7B,GAAG,CAAC/G,EAAM+G,UAEVlG,EAAAA,EAAAA,YAAU,WACT,GAAIb,EAAMmH,WAAY,CACrB,IAAM3B,EAAUxF,EAAMwF,QAAQxE,QAAO,SAACC,GAAS,OAAMqE,EAAYpE,QAAQD,EAAKmG,UAAU,IACxFC,EAAoB7B,EAASxF,EAAMmH,WACpC,CACD,GAAG,CAACnH,EAAMmH,WAAYnH,EAAMwF,UAE5B,IAOM6B,EAAsB,WAAoC,IAAnCC,EAAW,uDAAG,GAAInC,EAAW,uCACnDK,EAAU8B,EAAYrD,KAAI,SAAChD,GAAS,OAAKA,EAAKmG,SAAS,IAAEpG,QAAO,SAACC,GAAY,MAAc,WAATA,CAAiB,IACnGsG,EAAcD,EAAYrD,KAAI,SAAChD,GAAS,OAAKA,EAAKuG,KAAK,IAAExG,QAAO,SAACC,GAAY,OAAKA,IAASgG,EAAE,eAAK,IAClGQ,EAAe,GAErBtC,EAAKuC,SAAQ,SAACC,GACb,IAAMC,EAAW,CAAC,EAClBpC,EAAQvB,KAAI,SAAC4D,GACZ,IAAMC,EAAMH,EAAQE,GACpBD,EAAIC,GAAWC,GAAO,EACvB,IACAL,EAAQM,KAAKH,EACd,IAEAvC,EAAc,CACbH,OAAQqC,EACRpC,KAAMsC,GAER,EAgBMO,EAAmB,WACxB,IAAM9C,EAASE,EAAWF,OACpBC,EAAOC,EAAWD,KACpB8C,EAAM,GA2BV,OA1BI/C,EAAOxD,QAAUyD,EAAKzD,QACzBuG,EACC,IACA/C,EAAOgD,KAAK,KADZ,MAKD/C,EAAKuC,SAAQ,SAACE,GACb,IAAMO,EAASC,OAAOC,OAAOT,GAAK3D,KAAI,SAAChD,GACtC,MAAa,KAATA,EACI,IAEDA,CACR,IACAgH,EACCA,EACA,IACAE,EAAOD,KAAK,KAFZD,KAMF,KAEAA,EAAM,GAGAA,CACR,EAEMK,EAAmB,WACxB,IAAMpD,EAASE,EAAWF,OACpBC,EAAOC,EAAWD,KACpB8C,EAAM,GAsBV,OArBI/C,EAAOxD,QAAUyD,EAAKzD,QACzBuG,EACC/C,EAAOgD,KAAK,MAAI,KAGjB/C,EAAKuC,SAAQ,SAACE,GACb,IAAMO,EAASC,OAAOC,OAAOT,GAAK3D,KAAI,SAAChD,GACtC,MAAa,KAATA,EACI,IAEDA,CACR,IACAgH,EACCA,EACAE,EAAOD,KAAK,MAAI,IAGlB,KAEAD,EAAM,GAEAA,CACR,EAEA,OACC,UAAC,IAAK,CAACtG,UAAU,WAAW4G,UAAU,WAAW1C,KAAK,SAAQ,WAC7D,UAAC,IAAK,CACLpD,MAAO,IACP+F,cAAc,EACdC,UAAU,EACVC,UAAW,CAAE7E,UAAW,IAAK8E,SAAU,QACvCC,QAAS5D,EACTwC,MAAOP,EAAE,4BACT4B,SAAU,WACT5D,GAAqB,EACtB,EACA6D,OAAQ,KAAK,WAEb,iBAAKtG,MAAO,CAAEuG,SAAU,YAAa,WACpC,iBAAKpH,UAAU,OAAM,WAAC,kBAAMA,UAAU,MAAK,UAAEsF,EAAE,oDAAY,aAAQ,SAAC,UAAc,CACjFzG,QAASR,EAAMwF,QACbvB,KAAI,SAAChD,GAAS,MAAM,CAAE+H,MAAO/H,EAAKuG,MAAO7G,MAAOM,EAAKmG,UAAW,IAChEpG,QAAO,SAACC,GAAS,MAAoB,WAAfA,EAAKN,KAAkB,IAC/CsI,aAAc,GACdtI,MAAO2E,EACPlE,SAAU,SAACiH,GACV9C,EAAe8C,GACf,IAAM7C,EAAUxF,EAAMwF,QAAQxE,QAAO,SAACC,GAAS,OAAMoH,EAAOnH,QAAQD,EAAKmG,UAAU,IACnFC,EAAoB7B,EAASxF,EAAMmH,WACpC,QAED,iBAAK3E,MAAO,CAAEuG,SAAU,WAAYhF,MAAO,EAAGmF,OAAQ,GAAI,WACzD,SAAC,IAAM,CACNrD,KAAK,QACLsD,KAAK,OACLhF,QAAS,WACRoB,EACCvF,EAAMwF,QACJvB,KAAI,SAAChD,GAAS,OAAKA,EAAKmG,SAAS,IACjCpG,QAAO,SAACC,GAAS,MAAc,WAATA,CAAiB,KAE1CoG,EAAoBrH,EAAMwF,QAASxF,EAAMmH,WAC1C,EAAE,SAEDF,EAAE,mBAEJ,SAAC,IAAM,CACNpB,KAAK,QACLsD,KAAK,OACLhF,QAAS,WACRoB,EAAe,IACf8B,EAAoB,GAAIrH,EAAMmH,WAC/B,EAAE,SAEDF,EAAE,yBAKN,UAAC,IAAI,YACJ,SAAC,YAAY,CAACmC,IAAI,mBAAQ,UACzB,SAAC/E,EAAe,CAACgF,KAAMrB,IAAoBsB,OAAQ,kBAAMC,EAAAA,GAAAA,QAAgBtC,EAAE,8CAAW,EAAC,UACtF,gBAAKzE,MAAO,CAAEgH,OAAQ,UAAWC,UAAW,KAAM,UACjD,0BAAOzB,WAHqB,SAO/B,SAAC,YAAY,CAACoB,IAAI,mBAAQ,UACzB,SAAC/E,EAAe,CAACgF,KAAMf,IAAoBgB,OAAQ,kBAAMC,EAAAA,GAAAA,QAAgBtC,EAAE,8CAAW,EAAC,UACtF,gBAAKzE,MAAO,CAAEgH,OAAQ,UAAWC,UAAW,KAAM,UACjD,0BAAOnB,WAHqB,cAmBhCtI,EAAM0J,WAAa1J,EAAM2J,aAAe3J,EAAM4J,kBAAmB,UAAC,IAAG,CAACC,QAAQ,gBAAgBC,MAAM,SAAQ,WAC3G,SAAC,IAAG,WACH,SAAC,IAAK,CAACA,MAAM,SAAQ,SAAE9J,EAAM0J,eAE9B,SAAC,IAAG,WACH,UAAC,IAAK,CAACI,MAAM,SAAQ,UACnB9J,EAAM2J,WACN3J,EAAM4J,iBAAmB,MACzB,SAAC,IAAM,CAACpH,MAAO,CAAEuH,WAAY,GAAK5F,QAAS,kBAAMc,GAAqB,EAAK,EAAC,SAC1EgC,EAAE,sCAKC,MAEV,SAAC,KAAc,CAAC+C,YAxMW,WAAH,OACzB,UAAC,IAAG,CAACH,QAAQ,SAASC,MAAM,SAAStH,MAAO,CAAEmC,OAAQ,IAAKsF,cAAe,UAAW,WACpF,gBAAKC,IAAKC,EAAU3H,MAAO,CAAEC,MAAO,KAAO2H,IAAI,MAC/C,yBAAMnD,EAAE,gCACH,EAoM6C,UACjD,SAAC,IAAK,CACLpB,KAAM7F,EAAM6F,MAAQ,SACpBsC,OAAQnI,EAAMmI,OAASnI,EAAMmI,OAAS,KACtChB,WAAYnH,EAAMmH,WAElBkD,WAAY,CAAEnF,OAAQ,CAAEoF,KAAM/F,IAC9BiB,QAASmB,EACT4D,YAAiC,IAArBvK,EAAMuK,aAAoB,UAAQvK,EAAMuK,YACpDxD,OAAQN,EACR3C,QAAS9D,EAAM8D,QACf1C,SAAUpB,EAAMoB,SAChBoJ,aAAcxK,EAAMwK,mBAKzB,C,iOCzUaC,EAAY,SAACC,GAStB,OAAOC,EAAAA,EAAAA,KAAW,oBAAqBD,EAC3C,EAEaE,EAAyB,SAACC,GAUnC,OAAOF,EAAAA,EAAAA,IAAU,cAAD,OAAeE,GACnC,EAcaC,EAAyB,SAAC5K,EAAY6K,GAG/C,OAAOJ,EAAAA,EAAAA,IAAU,sBAAD,OAAuBzK,GAAM,CACzCwK,OAAQ,CACJK,UAAAA,IAGZ,EAEaC,EAAoB,SAAC9K,GAI9B,OAAOyK,EAAAA,EAAAA,IAAU,gBAAD,OAAiBzK,GACrC,EAEa+K,EAAe,SAAC/K,GAIzB,OAAOyK,EAAAA,EAAAA,IAAU,cAAD,OAAezK,GACnC,EA8BagL,EAAoB,WAG7B,OAAOP,EAAAA,EAAAA,IAAU,eACrB,E,sDC9Be,SAASQ,EAASnL,GAC/B,OACE,+BACE,SAAC,KAAU,CACToL,MAAOC,EAAAA,EACP1K,MAAOX,EAAMW,MACbS,SAAUpB,EAAMoB,SAChBkK,WAAY,EACVC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,GAAa,CACXC,KAAM,EACNC,WAAY,UACZC,UAAW,gBAIvB,C,2LCjEMC,EAAqC,CACvC,MAAS,EACT,MAAS,EACT,QAAW,EACX,IAAO,GAGLC,EAAgB,SAACC,GACnB,OAAQA,GACJ,IAAK,OACL,IAAK,UACD,OAAO,SAACC,EAAA,EAAe,IAC3B,IAAK,UACD,OAAO,SAACC,EAAA,EAAmB,CAACxJ,MAAO,CAAEyJ,MAAO,aAChD,IAAK,OACD,OAAO,SAACC,EAAA,EAAsB,IAClC,QACI,OAAO,KAEnB,EAEe,SAASC,EAASnM,GAC7B,OAAoBgH,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EACR,GADe,EAAJC,MACmC3G,EAAAA,EAAAA,WAAS,IAAM,eAAtD6L,EAAe,KAAEC,EAAkB,KAC1C,GAA0C9L,EAAAA,EAAAA,WAAS,GAAM,eAAnC+L,GAAF,KAAkB,MAGtC,GAA0C/L,EAAAA,EAAAA,WAAS,GAAK,eAAlCgM,GAAF,KAAkB,MAgBtC,GAAmChM,EAAAA,EAAAA,UAAiB,KAAI,eAAjDwK,EAAS,KAAEyB,EAAa,KACzBC,GAAeC,EAAAA,EAAAA,QAAO3B,GACtB4B,EAAe,SAACxH,GAClBsH,EAAaG,QAAUzH,EACvBqH,EAAcrH,EAClB,EAEA,GAAyC5E,EAAAA,EAAAA,UAAiB,KAAI,eAAvDsM,EAAY,KAAEC,EAAgB,KAC/BC,GAAkBL,EAAAA,EAAAA,QAAOG,GAUzBG,EAAmB,SAACC,GACtB,IAAM/H,EAAS+H,EAAO,IAAM,GAEtBC,EADOD,EAAOE,MAAM,GACFlJ,KAAI,SAAC2D,GAEzB,OADgBA,EAAI5B,QAAO,SAACC,EAAKC,EAAM3E,GAAK,yBAAW0E,GAAG,cAAGf,EAAO3D,GAAS2E,GAAI,GAAK,CAAC,EAE3F,IAWA,MAAO,CACHkH,OAXiBlI,EAAOjB,KAAI,SAAAhD,GAAI,MAAK,CACrCuG,MAAOvG,EACPmG,UAAWnG,EACXoM,IAAKpM,EACLwB,MAAO,IACV,IAOG0C,KAAM+H,EAEd,EAkDA,OACI,4BAeI,SAAC,IAAK,CACF1F,MAAOP,EAAE,gBACT2B,QAASwD,EACTtD,OAAQ,KACRwE,gBAAc,EACdzE,SAAU,WACN8D,EAAa,KACbN,GAAmB,EACvB,EAAE,UACF,4BACI,iBAAK1K,UAAU,aAAY,WACvB,iBAAKA,UAAU,MAAK,UAAEsF,EAAE,kCAAS,aACjC,SAAC,IAAM,CAACzE,MAAO,CAAEC,MAAO,KAAO9B,MAAO8L,EAAaG,QAASpM,QAAS,CAAC,CAClEwI,MAAO,IACPrI,MAAO,KACR,CACCqI,MAAO,IACPrI,MAAO,KACR,CACCqI,MAAO,MACPrI,MAAO,QACPS,SAAU,SAACT,GACXgM,EAAahM,EACjB,QAEJ,gBAAKgB,UAAU,YAAW,UACtB,SAAC,IAAM,CAACwH,KAAK,UAAUhF,QAAS,WAC5B2G,EAAuBiC,EAAgBH,QAASH,EAAaG,SAASW,MAAK,SAAAzF,GACvE0F,OAAOC,KAAK3F,EAAI3C,KAAKuI,aAAc,OACvC,IAAGC,OAAM,SAAAC,GACLC,QAAQC,IAAIF,EAChB,GACJ,EAAE,SAAE3G,EAAE,0BAIlB,SAAC,IAAQ,CAACtF,UAAU,gCAAgCoM,iBAAkB,CAAC,UAAW3M,SA/HzE,SAACiM,GACdQ,QAAQC,IAAIT,EAChB,EA6H6G,UAE5FjF,OAAO4F,QAAQhO,EAAMiO,QAAQjI,QAAO,SAACC,EAAuB,GAAD,mBAAQtF,GAAF,KAAO,8BAAWsF,GAAG,CAAEtF,GAAK,GAAI,KAAO,IAAIuN,UAAUlN,QAAO,SAAAC,GAAI,QAAMA,EAAKkN,KAAK,IAAElK,KAAI,SAAChD,EAAMM,GAC3J,OACI,UAAC,UAAc,CAACI,UAAW,CAAC,6BAA6B,UAAD,OAAYV,EAAK6K,SAAU5D,KAAK,KAAMhD,OAAM,UAAK+B,EAAE,uBAAM,OAAGhG,EAAKkN,OAA+BC,OACpJ,iCACI,UAAC,IAAM,CAACzM,UAAU,OAAOwH,KAAK,UAAUtD,KAAK,QAAQ1B,QAAS,SAACtB,GAC3DA,EAAEY,kBACFzD,EAAMqO,SAASpN,EAAKkN,MACxB,EAAE,UAAElH,EAAE,iBAAM,SAACqH,EAAA,EAAc,QAC3B,UAAC,IAAM,CAACnF,KAAK,UAAUtD,KAAK,QAAQ1B,QAAS,SAACtB,GAC1CA,EAAEY,kBACFzD,EAAMuO,QAAQtN,EAAKkN,MACvB,EAAE,UACAlH,EAAE,iBAAM,SAACuH,EAAA,EAAY,UAE9B,WACG,UAAC,IAAK,CAAC3I,KAAK,QAAQ+G,QAAShB,EAAQ3K,EAAKwK,MAAM,WAC5C,SAAC,IAAAgD,KAAU,CAACjH,MAAOP,EAAE,4BAASyH,KAA6B,IAAvB9C,EAAQ3K,EAAKwK,MAAcI,EAAc5K,EAAK6K,QAAU,QAC5F,SAAC,IAAA2C,KAAU,CAACjH,MAAOP,EAAE,gBAAOyH,KAA6B,IAAvB9C,EAAQ3K,EAAKwK,MAAcI,EAAc5K,EAAK6K,QAAU,QAC1F,SAAC,IAAA2C,KAAU,CAACjH,MAAOP,EAAE,gBAAOyH,KAA6B,IAAvB9C,EAAQ3K,EAAKwK,MAAcI,EAAc5K,EAAK6K,QAAU,QAC1F,SAAC,IAAA2C,KAAU,CAACjH,MAAOP,EAAE,4BAASyH,KAA6B,IAAvB9C,EAAQ3K,EAAKwK,MAAcI,EAAc5K,EAAK6K,QAAU,WAEhG,SAAC6C,EAAA,EAAQ,CACL9I,KAAM,QACN/B,SAAS,EACT8F,kBAAkB,EAClBzB,OAAQ,SAACyG,GACL,OAAOtI,KAAKC,UAAUqI,EAC1B,EACApJ,QAAS,CAAC,CACNgC,MAAOP,EAAE,sBACTG,UAAW,UACXiG,IAAK,UACLwB,OAAQ,SAACxF,GACL,OAAO,SAAC,IAAO,CACXyF,UAAU,MACVtH,MAAO6B,EAAK,UAEZ,gBAAK1H,UAAU,cAAa,SAAE0H,KAEtC,GAWJ,CACI7B,MAAOP,EAAE,4BACTG,UAAW,WACXiG,IAAK,YACN,CACC7F,MAAOP,EAAE,4BACTG,UAAW,WACXiG,IAAK,YACN,CACC7F,MAAOP,EAAE,gBACTG,UAAW,SACXiG,IAAK,SACLwB,OAAQ,SAACxF,GACL,OAAO,iBAAM1H,UAAW,CAAC,KAAD,OAAMV,EAAK6K,SAAU5D,KAAK,KAAK,SAAEmB,GAC7D,GACD,CACC7B,MAAOP,EAAE,gBACTG,UAAW,SACXiG,IAAK,SACLwB,OAAQ,WACJ,OAAO,iCACH,iBAAMlN,UAAU,YAAYwC,QAAS,WAEjC4K,EAAAA,EAAAA,KAAW,CACPvH,MAAOP,EAAE,4BACTxE,MAAO,IACPuM,OAAQ/H,EAAE,gBACVgI,SACI,4BACI,UAAC,IAAG,CAACtN,UAAU,OAAM,WACjB,SAAC,IAAG,CAACuN,KAAM,EAAE,UAAC,gBAAKvN,UAAU,OAAM,UAAC,8BAASsF,EAAE,4BAAQ,iBACvD,SAAC,IAAG,CAACiI,KAAM,GAAG,SAAEjO,EAAKkO,gBAEzB,UAAC,IAAG,CAACxN,UAAU,OAAM,WACjB,SAAC,IAAG,CAACuN,KAAM,EAAE,UAAC,gBAAKvN,UAAU,OAAM,UAAC,8BAASsF,EAAE,4BAAQ,iBACvD,SAAC,IAAG,CAACiI,KAAM,GAAG,SAAEjO,EAAKmO,eAEzB,UAAC,IAAG,CAACzN,UAAU,OAAM,WACjB,SAAC,IAAG,CAACuN,KAAM,EAAE,UAAC,gBAAKvN,UAAU,OAAM,UAAC,8BAASsF,EAAE,gBAAM,iBACrD,SAAC,IAAG,CAACiI,KAAM,GAAG,SAAEjO,EAAK6K,aAEzB,UAAC,IAAG,CAACnK,UAAU,OAAM,WACjB,SAAC,IAAG,CAACuN,KAAM,EAAE,UAAC,gBAAKvN,UAAU,OAAM,UAAC,8BAASsF,EAAE,kCAAS,iBACxD,SAAC,IAAG,CAACiI,KAAM,GAAG,SAAEjO,EAAKgO,cAEzB,UAAC,IAAG,CAACtN,UAAU,OAAM,WACjB,SAAC,IAAG,CAACuN,KAAM,EAAE,UAAC,gBAAKvN,UAAU,OAAM,UAAC,8BAASsF,EAAE,4BAAQ,iBACvD,SAAC,IAAG,CAACiI,KAAM,GAAG,SAAEjO,EAAKsI,gBAIjC8F,KAAI,WAAK,GAEjB,EAAE,SAAEpI,EAAE,kBAEc,QAAdhG,EAAKwK,MAAkC,YAAhBxK,EAAK6K,QAAwB,iBAAMnK,UAAU,YAAYwC,QAAS,WACvF4K,EAAAA,EAAAA,QAAc,CACVvH,MAAOP,EAAE,4BACTyH,MAAM,SAACY,EAAA,EAAY,IACnBL,QAAS,GACTD,OAAQ/H,EAAE,gBACVsI,WAAYtI,EAAE,gBACdoI,KAAI,WACA,OAAO,IAAIG,SAAQ,SAACC,EAASC,GACzBzE,EAAahK,EAAKkN,OAAOZ,MAAK,SAAAzF,GAC1B2H,EAAQ,GACZ,IAAG9B,OAAM,SAAAC,GACL8B,GACJ,GACJ,IACKnC,MAAK,SAACzF,GACHyB,EAAAA,GAAAA,QAAgBtC,EAAE,4BACtB,IACC0G,OAAM,WACHpE,EAAAA,GAAAA,MAActC,EAAE,4BACpB,GACR,EACA4B,SAAQ,WAAK,GAErB,EAAE,SAAE5B,EAAE,kBAAgB,KAGpBhG,EAAK6M,KAAM,iBAAMnM,UAAU,YAAYwC,QAAS,WAC9CqJ,OAAOC,KAAKxM,EAAK6M,IAAK,OAC1B,EAAE,SAAE7G,EAAE,kBAAgB,KAGR,QAAdhG,EAAKwK,MAAkC,YAAhBxK,EAAK6K,QAAuB,iBAAMnK,UAAU,YAAYwC,QAAS,WACpFoI,GAAiB,GACjBvB,EAAkB/J,EAAKkN,OAAOZ,MAAK,SAAAzF,GAC/BwE,GAAiB,GACjB,IAAMW,EAASnF,EAAI3C,KAAK8H,OACxBD,EAAiBC,GACjB,IAAM0C,EAAa3C,EAAiBC,GAEpC8B,EAAAA,EAAAA,KAAW,CACPvH,MAAOP,EAAE,4BACTgI,SACI,0BAII,SAACN,EAAA,EAAQ,CAEL9I,KAAM,QACN/B,SAAS,EACT8F,kBAAkB,EAClBzB,OAAQ,SAACyG,GACL,OAAOtI,KAAKC,UAAUqI,EAC1B,EACApJ,QAASmK,EAAWvC,OACpB7C,YAAY,EACZpD,WAAYwI,EAAWxK,KACvB4B,OAAQ,CAAEL,EAAG,YAIzB2I,KAAI,WAAK,GAEjB,IAAG1B,OAAM,SAAAC,GAAS,IAAGgC,SAAQ,WACzBrD,GAAiB,EACrB,GACJ,EAAE,SAAEtF,EAAE,kBAAgB,KAGR,QAAdhG,EAAKwK,MAAkC,YAAhBxK,EAAK6K,QAAuB,iBAAMnK,UAAU,OAAOwC,QAAS,WArT3G,IAACgB,IAuT2ClE,EAAKkN,MAtTrEpB,EAAgBH,QAAUzH,EAC1B2H,EAAiB3H,GAsT+BkH,GAAmB,EACvB,EAAE,SAAEpF,EAAE,kBAAgB,OAGlC,IAEJsD,YAAY,EACZpD,WAAY,CAAC,CACT8H,QAAShO,EAAKgO,QACdY,SAAU5O,EAAK4O,UAAY,IAC3BC,MAAO7O,EAAK6O,OAAS,IACrBC,SAAU9O,EAAKkO,UACfC,SAAUnO,EAAKmO,UAAY,IAC3BtD,OAAQ7K,EAAK6K,aAEnB,eA/LyIvK,GAkMvJ,QA4DpB,C,gFCzbMyO,GAAiBC,EAAAA,YAAiB,SAACjQ,EAAeqD,GACpD,OAAoB2D,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EACR,GADe,EAAJC,KACIgJ,GAAAA,EAAAA,WAARC,GAAsB,YAAlB,GACX,GAAwB5P,EAAAA,EAAAA,aAAe,YAAnB,IAEpB6P,EAAAA,EAAAA,qBAAoB/M,GAAK,iBAAO,CAC5BgN,SAAU,WACN,OAAO,IAAIb,SAAQ,SAACC,EAASC,GACzBS,EAAKG,iBAAiB/C,MAAK,SAAAzF,GACvB2H,EAAQ3H,EACZ,IAAG6F,OAAM,SAAAC,GACL8B,EAAO9B,EACX,GACJ,GACJ,EACA2C,QAAS,SAACpL,GACNgL,EAAKK,eAAerL,EACxB,EACH,KAEDtE,EAAAA,EAAAA,YAAU,WACFb,EAAMyQ,WACNN,EAAKK,eAAexQ,EAAMyQ,UAElC,GAAG,CAACzQ,EAAMiO,SAEV,IAyEMyC,EAAyB,SAACzP,GAA8F,IAA7D0P,EAA8B,uDAAG,CAAC,EAC/F,OAAQ1P,EAAKkI,MACT,IAAK,QACD,OA3ED,mBA4EH,IAAK,SACD,OA1ES,SAACiE,EAAmCuD,GACrD,IAAMnQ,EAA0B4M,EAAOzM,OAAS,GAChD,OAAO,SAAC,WAAS,gBAEbqI,MAAOoE,EAAOpE,MACd4H,KAAMxD,EAAOlN,GACb2Q,MAAO,CACH,CACIC,UAAU,EACVvH,QAAQ,GAAD,OAAKtC,EAAE,uBAAM,OAAGmG,EAAOpE,SAGtC+H,aAAc3D,EAAOnE,aACrBzG,MAAO,CAAEwO,aAAc,EAAGC,YAAa,KACnCN,GAAS,cAEb,SAAC,KAAM,CACHnO,MAAO,CAAEC,MAAO,KAChByO,KAAM9D,EAAO+D,SAAW,gBAAaC,EACrCC,YAAU,EACV3O,SAAU0K,EAAOkE,QACjBC,iBAAiB,QACjB5O,YAAayK,EAAOoE,aAAW,UAAOvK,EAAE,sBAAM,YAAImG,EAAOpE,OACzDxI,QAASA,EACTY,SAAU,SAACT,EAAO8Q,GACd,GAAIA,EAAUC,OAAQ,CAIlB,IAHA,IAAMC,EAAWF,EAAUC,OAAOC,SAC5BC,EAAeH,EAAUC,OAAO/Q,MAChCkR,EAAgB7R,EAAMiO,OACnB6D,EAAI,EAAGA,EAAID,EAAcnQ,OAAQoQ,IAAK,CAC3C,IAAM7Q,EAAO4Q,EAAcC,GACvB7Q,EAAKf,KAAOyR,IACZ1Q,EAAKN,MAAQiR,EAErB,CACA5R,EAAM+R,gBAAkB/R,EAAM+R,eAAeF,EACjD,CACA7R,EAAMoB,UAAYpB,EAAMoB,SAAS+O,EAAK6B,iBAC1C,MAAK,yBAnCc5E,EAAOlN,IAqCtC,CAkCmB+R,CAAahR,EAAM0P,GAC9B,IAAK,eACD,OAlCc,SAACvD,EAAmCuD,GAC1D,IACMuB,GAD0B9E,EAAOzM,OAAS,IACdsD,KAAI,SAAAhD,GAAI,OAAKA,EAAKN,KAAK,IACzD,OAAO,SAAC,WAAS,gBAEbqI,MAAOoE,EAAOpE,MACd4H,KAAMxD,EAAOlN,GACb2Q,MAAO,CACH,CACIC,UAAU,EACVvH,QAAQ,GAAD,OAAKtC,EAAE,uBAAM,OAAGmG,EAAOpE,SAGtC+H,aAAc3D,EAAOnE,aACrBzG,MAAO,CAAEwO,aAAc,IACnBL,GAAS,cAEb,SAAC5Q,EAAA,EAAW,CACRgB,mBAAiB,EACjBK,SAAU,WACNpB,EAAMoB,UAAYpB,EAAMoB,SAAS+O,EAAK6B,iBAC1C,EACAxR,QAAS0R,EAAmBzP,MAAO,YAAW,yBAlB3B2K,EAAOlN,IAoBtC,CAUmBiS,CAAkBlR,EAAM0P,GACnC,QACI,OAAO,KAEnB,EAEA,OACI,gBAAKhP,UAAU,kCAAiC,UAC5C,UAAC,KAAI,CAACwO,KAAMA,EAAMiC,WAAW,EAAM,UAE3BvE,QAAQC,IAAI,eAAgB9N,EAAMiO,QAGlCjO,EAAMiO,OAAOhK,KAAI,SAACmO,GACd,OAAO1B,EAAuB0B,EAClC,QAKpB,IAEA,MChIMC,IAFWC,EAAAA,EAAAA,IAAY,kBAEZ,WACb,OAAOnS,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAChD,GAEMiS,GAAiC,CACnC,KAAO,wnDAuDI,SAASC,KACpB,OAAoBxL,EAAAA,EAAAA,MAAZC,EAAC,EAADA,EAEFwL,GAFS,EAAJvL,KAEgC,CACvCwL,MAFWL,KAGX7K,MAAM,GAAD,OAAKP,EAAE,sBAAM,MAClB6E,OAAQ,OACR6G,WAAW,EACXC,aAAc,GACdC,WAAY,GACZC,gBAAY1B,EACZtN,SAAS,EACTiP,QAAS,CAAC,IAERC,EAA8C1M,KAAK2M,MAAM9M,aAAa+M,QAAQ,gBAAkB5M,KAAKC,WAAU,UAChHkM,EAAsBC,MAAQD,KAE7BU,EAAqB/K,OAAO4F,QAAQgF,GAAgBhN,QAAO,SAACC,EAAmB,GAAD,mBAAQtF,GAAF,KAAO,8BAAWsF,GAAG,YAAOtF,IAAK,GAAM,IAEjI,GAAmCJ,EAAAA,EAAAA,UAAiB4S,EAAmB,GAAGT,OAAM,eAAzEU,EAAS,KAAEC,EAAa,KACzBC,GAAe5G,EAAAA,EAAAA,QAAO0G,GACtBG,EAAe,SAACpO,GAClBmO,EAAa1G,QAAUzH,EACvBkO,EAAclO,EAClB,EAEA,GAAuC5E,EAAAA,EAAAA,UAAuByS,GAAe,eAAtEQ,EAAW,KAAEC,EAAe,KAC7BC,GAAiBhH,EAAAA,EAAAA,QAAO8G,GACxBG,EAAiB,SAACxO,GACpBuO,EAAe9G,QAAUzH,EACzBsO,EAAgBtO,EACpB,EAEA,GAAyC5E,EAAAA,EAAAA,UAAsC,IAAG,eAA3EqT,EAAY,KAAEC,EAAgB,KAC/BC,GAAkBpH,EAAAA,EAAAA,QAAOkH,GACzBG,EAAkB,SAAC5O,GACrB2O,EAAgBlH,QAAUzH,EAC1B0O,EAAiB1O,EACrB,EAEM6O,EAAe5L,OAAO4F,QAAQwF,GAAaxN,QAAO,SAACC,EAAmB,GAAD,mBAAQtF,GAAF,KAAO,8BAAWsF,GAAG,YAAOtF,IAAK,GAAM,IACxH,GAA0BJ,EAAAA,EAAAA,UAASyT,GAAa,eAAzCC,EAAK,KAAEC,EAAQ,KAChBC,GAAczH,EAAAA,EAAAA,QAAOyG,EAAmBzR,QAE9C,IAAwCnB,EAAAA,EAAAA,UAAgB,IAAG,iBAAtC6T,IAAF,MAAiB,OACpC,IAAgC7T,EAAAA,EAAAA,UAAgB,IAAG,iBAAlC8T,IAAF,MAAa,OAEtBC,IAA+B5H,EAAAA,EAAAA,QAAO,MAEtC6H,GAAiB,SAACC,EAAiCnH,GAErD,IAAMoH,GAAuB,UACtBf,EAAe9G,SAElB8H,EAAiB,CAAC,EAElBF,EAAazB,UACb2B,EAAiB,CACb3B,SAAQ,kBACDW,EAAe9G,QAAQwG,GAAWL,SAClCyB,EAAazB,WAK5B0B,EAAUpH,GAAO+F,IAAU,0BACpBqB,EAAUpH,GAAO+F,IACjBoB,GACAE,GAGPvO,aAAaC,QAAQ,cAAeE,KAAKC,UAAUkO,IACnDf,EAAe9G,QAAU6H,EACzBd,EAAec,EACnB,GAEA5T,EAAAA,EAAAA,YAAU,WACNqK,IAAoBqC,MAAK,SAAAzF,GACrB,IAAMmG,EAASnG,EAAI3C,KAAK8H,OACxB8G,EAAgB9F,EACpB,GACJ,GAAG,KAEHpN,EAAAA,EAAAA,YAAU,WACN,IAKc8T,EALRC,EAAYC,SAASC,eAAe,cACtCF,KAIUD,EAHLC,GAIDG,YAAc,SAAUlS,GACxB,IAAImS,EAAM,GACNC,EAASpS,EAAEqS,QACXC,EAAStS,EAAEuS,QACX3S,EAAQkS,EAAIU,YACZ1Q,EAASgQ,EAAIW,aACbC,EAAOZ,EAAIa,WACXC,EAAMd,EAAIe,UAkCd,OAhCIT,EAASM,EAAO9S,EAAQ,GACxBuS,EAAM,QACCC,EAASM,EAAO,KACvBP,EAAM,QAENG,EAASM,EAAM9Q,EAAS,GACxBqQ,EAAM,OACCG,EAASM,EAAM,KACtBT,EAAM,OAGVH,SAASc,YAAc,SAAU9S,GAC7B,OAAQmS,GACJ,IAAK,QACDL,EAAInS,MAAa,MAAIC,GAASI,EAAEqS,QAAUD,GAAU,KACpD,MACJ,IAAK,OACDN,EAAInS,MAAa,MAAIC,GAASI,EAAEqS,QAAUD,GAAU,KACpDN,EAAInS,MAAY,KAAI+S,GAAQ1S,EAAEqS,QAAUD,GAAU,KAClD,MACJ,IAAK,MACDN,EAAInS,MAAc,OAAImC,GAAU9B,EAAEuS,QAAUD,GAAU,KACtDR,EAAInS,MAAW,IAAIiT,GAAO5S,EAAEuS,QAAUD,GAAU,KAChD,MACJ,IAAK,OACDR,EAAInS,MAAc,OAAImC,GAAU9B,EAAEuS,QAAUD,GAAU,KAGlE,EACAR,EAAIiB,UAAY,WACZf,SAASc,YAAc,IAC3B,GACO,CACX,EAER,GAAG,IAEH,IAAME,GAA4B,SAACzC,GACfhL,OAAO4F,QAAQ0F,EAAe9G,QAAQwG,IAAYpN,QAAO,SAACC,EAAmB,GAAD,mBAAQtF,GAAF,KAAO,8BAAWsF,GAAG,YAAOtF,IAAK,GAAM,IACjI+G,SAAQ,SAAAoO,GACZC,cAAcD,EAAIhD,WACtB,IACwB1K,OAAO4F,QAAQ0F,EAAe9G,QAAQwG,GAAWL,SAAS/M,QAAO,SAACC,EAAuB,GAAD,mBAAQtF,GAAF,KAAO,8BAAWsF,GAAG,YAAOtF,IAAK,GAAM,IAC7I+G,SAAQ,SAAAsO,GACpBD,cAAcC,EAAKlU,MACvB,GACJ,EAEMmU,GAAiB,SAAC7C,EAAmB8C,GACvC,IACMF,EADUtC,EAAe9G,QAAQwG,GAAWL,QAC7BmD,GACjBF,GACAD,cAAcC,EAAKlU,MAE3B,GAGAjB,EAAAA,EAAAA,YAAU,WAKN,OAJA0T,GAAe,CACXzQ,SAAS,IAGN,WACHsE,OAAO4F,QAAQwF,GAAa9L,SAAQ,SAACzG,GACjC,IAAOoM,GAAP,OAAcpM,EAAI,GAAR,GACV4U,GAA0BxI,EAC9B,GACJ,CACJ,GAAG,KAEHxM,EAAAA,EAAAA,YAAU,WAEN,IAAMsV,EAAqB3C,EAAYJ,GACfhL,OAAO4F,QAAQmI,EAAmBpD,SAAS/M,QAAO,SAACC,EAAuB,GAAD,mBAAQtF,GAAF,KAAO,8BAAWsF,GAAG,YAAOtF,IAAK,GAAM,IAC9H+G,SAAQ,SAAAsO,GACA,YAAhBA,EAAKlK,QACLsK,GAAWJ,EAAK7H,MAExB,GACJ,GAAG,CAACiF,IAGJ,IA+EMiD,GAAY,SAACxL,GACfD,EAAuBC,GAAS0C,MAAK,SAAAzF,GACjC,MAAqEA,EAAI3C,KAAjEmR,EAAK,EAALA,MAAOrJ,EAAM,EAANA,OAAQsJ,EAAO,EAAPA,QAASC,EAAU,EAAVA,WAAYC,EAAa,EAAbA,cAAeC,EAAK,EAALA,MACrDV,GAAqB,kBACpBtC,EAAe9G,QAAQwG,GAAWL,QAAQlI,IAAQ,IACrDiB,OAAQwK,EACR7K,KAAMiL,EACN5I,IAAK2I,EACLE,YAAaH,EACbvJ,OAAAA,EACA1D,QAASgN,IAEb,GAAc,YAAVD,GAAiC,YAAVA,EAAqB,CAC5C,IAAMM,EAAW,IAAIC,KAAKb,EAAK7G,WAAa,IAAI2H,UAC1CC,GAAU,IAAIF,MAAOC,UACrB1H,GAAW4H,EAAAA,EAAAA,KAAWD,EAAUH,GAAY,KAClDZ,EAAK5G,SAAWA,EAEhBmF,GAAe,CACXzI,OAAQ,UACRiH,SAAQ,UACHlI,EAAUmL,KAGnBC,GAAe7C,EAAWvI,EAC9B,MACI0J,GAAe,CACXzI,OAAQ,UACRiH,SAAQ,UACHlI,EAAUmL,IAI3B,IAAGrI,OAAM,WACLsI,GAAe7C,EAAWvI,GAC1BtB,EAAAA,GAAAA,MAActC,EAAE,mFAChBsN,GAAe,CACXzI,OAAQ,UACRiH,SAAQ,UACHlI,GAAO,kBACD6I,EAAe9G,QAAQwG,GAAWL,QAAQlI,IAAQ,IACrDiB,OAAQ,UACRL,KAAM,UAItB,GACJ,EAEM2K,GAAa,SAACvL,GAChBoL,GAAe7C,EAAWvI,GAE1B,IAAI/I,EAAQmV,aAAY,WACpBZ,GAAUxL,EACd,GAAG,KAEH0J,GAAe,CACXxB,SAAQ,UACHlI,EAAU,CACPsD,MAAOtD,EACPiB,OAAQ,OACRmD,QAASuE,EAAYJ,GAAWnE,QAChC2B,KAAK,GAAD,OAAK3J,EAAE,iBAAK,OAAG4D,GACnBY,KAAM,QACN0D,UAAW+H,MAASC,OAAO,uBAC3BtH,SAAU2D,EAAYJ,GAAWvD,SACjCC,MAAO0D,EAAYJ,GAAWtD,MAC9BhO,MAAAA,EACAyH,QAAS,OAIrB8M,GAAUxL,EACd,EA+BA,OACI,gBAAKlJ,UAAU,mCAAkC,UAC7C,gBAAKA,UAAU,mBAAkB,UAC7B,SAAC,IAAI,CAACwH,KAAK,gBAAgB/H,SA1LtB,SAACgW,GACdhP,OAAO4F,QAAQwF,GAAa9L,SAAQ,SAACzG,GACjC,IAAOoM,GAAP,OAAcpM,EAAI,GAAR,GACNoM,IAAQ+J,GACRvB,GAA0BxI,EAElC,IACA+G,GAAgB,IAChBC,GAAY,IACZd,EAAa6D,EACjB,EAgL2DhE,UAAWA,EAAWiE,OAnHlE,SAACC,EAAgBC,GACb,QAAXA,EA5DI,WACR1B,GAA0BzC,GAE1B,IAAMoE,IAAiBrD,EAAYvH,QACnC,GAAI4K,EAAe,GACfjO,EAAAA,GAAAA,KAAatC,EAAE,yDACZ,CACH,IAAMmQ,EAAe/E,KACf7K,EAAK,UAAMP,EAAE,sBAAM,YAAIuQ,GACvBC,GAAQ,OAAOxD,GACfyD,EAAyB,CAC3BlQ,MAAAA,EACAkL,MAAO0E,EACPtL,OAAQ,OACR6G,WAAW,EACXC,aAAc,GACdE,gBAAY1B,EACZyB,WAAY,GACZ/O,SAAS,EACTiP,QAAS,CAAC,GAEd0E,EAAS1P,KAAK2P,GACdxD,EAASuD,GACTlE,EAAa6D,GAEb,IAAItP,GAAiB,kBACd0L,GAAW,cAAG4D,EAAeM,IAGpC/D,EAAe7L,GACf3B,aAAaC,QAAQ,cAAeE,KAAKC,UAAUuB,GACvD,CACJ,CA6BQ6P,GA3BO,SAACL,GACZ,IAAIF,EAAehE,EACfwE,GAAa,EACjB3D,EAAMvM,SAAQ,SAACmQ,EAAM/F,GACb+F,EAAKnF,QAAU4E,IACfM,EAAY9F,EAAI,EAExB,IACA,IAAM2F,EAAWxD,EAAMjT,QAAO,SAAA6W,GAAI,OAAIA,EAAKnF,QAAU4E,CAAS,IAC1DG,EAAS/V,QAAU0V,IAAiBE,IAEhCF,EADAQ,GAAa,EACEH,EAASG,GAAWlF,MAEpB+E,EAAS,GAAG/E,OAGnCwB,EAASuD,GACTlE,EAAa6D,GAEb,IAAItP,GAAG,UAAQ0L,UACR1L,EAAIwP,GACX3D,EAAe7L,GACf3B,aAAaC,QAAQ,cAAeE,KAAKC,UAAUuB,GACvD,CAMQgQ,CAAOR,EAEf,EA6GgG,SAC/ErD,EAAMhQ,KAAI,SAAC4T,EAAMtW,GAAK,eACnB,SAAC,YAAY,CAAC6H,IAAG,UAAKnC,EAAE,sBAAM,YAAI1F,EAAQ,GAAsBwW,SAAoB,IAAVxW,EAAY,UAClF,iBAAKI,UAAU,gBAAe,WAC1B,iBAAKA,UAAU,gBAAe,UAEA,QAAtB,EAAA6R,EAAYJ,UAAU,OAAtB,EAAwBtP,SAAU,gBAAKnC,UAAU,gBAAe,UAC5D,iBAAKA,UAAU,iBAAgB,WAC3B,SAACqW,EAAA,EAAW,KACZ,yBACK/Q,EAAE,yCAGN,MAGb,SAACkE,EAAQ,CACLxK,MAA6B,QAAxB,EAAE6S,EAAYJ,UAAU,aAAtB,EAAwBnE,QAC/B7N,SAAU,SAACT,GACP4T,GAAe,CACXtF,QAAmB,KAAVtO,OAAeyQ,EAAYzQ,EACpC6G,MAAOqQ,EAAKrQ,OAEpB,QAGR,iBAAK7F,UAAU,OAAOzB,GAAG,UAAUsC,MAAO,CAAEmC,OAAQ,KAAM,WACtD,SAAC,IAAS,CACNsT,KAAK,IACLC,QAAS,WAAQ,EACjBC,OAAQ,SAACtV,GACL,IAAMuV,EAAavD,SAASC,eAAe,WAC3C,GAAIsD,EAAY,CACZ,IAAMtQ,EAAM+M,SAASwD,KAAK1U,aAAed,EAAEyV,EAC3CF,EAAW5V,MAAMmC,OAAM,UAAMmD,EAAG,KACpC,CACJ,EACAyQ,OAAQ,WAAQ,EAAE,UAClB,gBAAK5W,UAAU,OAAOa,MAAO,CAAEgH,OAAQ,aAAc,UAAC,SAACgP,EAAA,EAAY,SAEvE,iBAAK7W,UAAU,iDAAgD,WAC3D,iBAAKA,UAAU,SAAQ,WACnB,SAAC,IAAM,CAACA,UAAU,MACd8W,QAASjF,EAAYJ,GAAWT,UAChC+F,kBAAmBzR,EAAE,4BACrB0R,gBAAiB1R,EAAE,4BAAS7F,SAAU,SAACqX,GACnClE,GAAe,CAAE5B,UAAW8F,GAChC,IAEAjF,EAAYJ,GAAWT,WAAY,SAAC5S,EAAA,EAAW,CAC3CY,MAAO6S,EAAYJ,GAAWR,aAC9B7R,mBAAiB,EACjBK,SAAU,SAACT,GACP4T,GAAe,CACX3B,aAAcjS,GAEtB,EACAwC,SAAU,SAACxC,GACP4T,GAAe,CACX1B,WAAYN,GAAO5R,GACnBmD,SAAS,IAGb,IAAMhC,EAAQmV,aAAY,WACtB,IAAM2B,EAAiBlF,EAAe9G,QAAQwG,GAAWnE,SAAW,GACpE,GAAIyE,EAAe9G,QAAQwG,GAAWP,WAAY,CAC9C,IAAIA,EAAaa,EAAe9G,QAAQwG,GAAWP,YAAc,GAC3DgG,EAAShG,EAAWiG,OAAO,EAAG,IACpCjG,EAAaA,EAAWkG,QAAQF,EAAQ,IAExCtE,GAAe,CACX1B,WAAAA,EACA5D,QAAS2J,EAAiBC,GAElC,MACI9C,cAAcrC,EAAe9G,QAAQwG,GAAWN,YAChDyB,GAAe,CACX1B,WAAY,GACZC,gBAAY1B,EACZtN,SAAS,GAGrB,GAAG,KAEHyQ,GAAe,CACXzB,WAAYhR,GAEpB,EACAtB,QAAS,CACL,QACDmC,YAAasE,EAAE,8BAAWxE,MAAO,UAAc,SAG9D,iBAAKd,UAAU,SAAQ,WACnB,SAAC,GAAc,CACX0B,IAAKiR,GACL7D,UAAW+C,EAAYJ,GACvBhS,SAAU,SAACqP,GACP8D,GAAe9D,EACnB,EACAsB,eAAgB,SAAC9D,GACb8F,EAAgB9F,GAChBsG,GAAe,CACX1E,SAAU,MAElB,EACA5B,OAAQ6F,EAAgBlH,WAC5B,UAAC,IAAM,CAACjL,UAAU,OAAOwH,KAAK,UAAUrF,QAA2C,YAAlC0P,EAAYJ,GAAWtH,OAAsB3H,QAAS,WACnGmQ,GAAwB1H,QAAQyD,WAAW9C,MAAK,SAACzF,IA5I7E,WAAO,IAAD,EAClByM,GAAe,CAAEzI,OAAQ,YACzB,IAAMkN,EAAepF,EAAa3P,KAAI,SAAAhD,GAAI,OAAIA,EAAKf,EAAE,IAAE8F,QAAO,SAACC,EAAUC,GAAS,yBAAWD,GAAG,cAAGC,EAAOsN,EAAYJ,GAAWlN,IAAK,GAAK,CAAC,GAE5IuE,GAAU,QACNc,KAA2B,QAAtB,EAAAiI,EAAYJ,UAAU,aAAtB,EAAwBnE,UAAW,IACrC+J,IACJzL,MAAK,SAAAzF,GACJ,MAA6BA,EAAI3C,KAAzBoR,EAAO,EAAPA,QAAS1L,EAAO,EAAPA,QACb0L,GACAhC,GAAe,CACXzI,OAAQ,YAEZiD,EAAAA,EAAAA,MAAY,CACRvH,MAAOP,EAAE,4BACTyH,MAAM,SAACuK,EAAA,EAAyB,IAChCxW,MAAO,IACPwM,QAASsH,EACTvH,OAAQ/H,EAAE,mBAGP4D,GACPuL,GAAWvL,EAEnB,IAAG8C,OAAM,SAAAC,GACL2G,GAAe,CAAEzI,OAAQ,WAC7B,GACJ,CAkHgDoN,EACJ,GACJ,EAAE,UAAEjS,EAAE,iBAAM,SAACkS,EAAA,EAAmB,cAGxC,iBAAKxX,UAAU,oCAAoCa,MAAO,CAAEmC,OAAQ,qBAAsB,WACtF,gBAAKhD,UAAU,MAAK,UAChB,gBAAKA,UAAU,oCAAmC,SAC7CsF,EAAE,qBAGX,gBAAKtF,UAAU,YAAW,UACtB,SAACwK,EAAQ,CACL8B,OAAQuF,EAAYJ,GAAWL,QAC/B1E,SAAU,SAACnO,GACP6O,EAAAA,EAAAA,QAAc,CACVvH,MAAOP,EAAE,gBACTyH,MAAM,SAACuK,EAAA,EAAyB,IAChChK,QAAQ,GAAD,OAAKhI,EAAE,4BAAO,KACrB+H,OAAQ/H,EAAE,4BACVsI,WAAYtI,EAAE,gBACdmS,cAAe,CAAEC,QAAQ,GACzBhK,KAAI,WACA,IAAI0D,EAAUS,EAAYJ,GAAWL,QACrCkD,GAAe7C,EAAWlT,UACnB6S,EAAQ7S,GACfqU,GAAe,CACXxB,QAAAA,GAER,EACAlK,SAAQ,WAAK,GAErB,EACA0F,QAAS,SAACrO,GACNkW,GAAWlW,EACf,gBA9I4B2X,EAAKnF,MAoJ1C,SAMvC,C", "sources": ["components/InputSearch/InputSearch.tsx", "components/TableBox/TableBox.tsx", "api/dataSearchApi.ts", "components/CodeEdit.tsx", "pages/DataSearch/TaskList.tsx", "pages/DataSearch/ConfigFormData.tsx", "pages/DataSearch/DataSearch.tsx"], "sourcesContent": ["import { SearchOutlined } from '@ant-design/icons';\nimport { Input } from 'antd';\nimport React, { useState, ChangeEvent, useEffect } from 'react';\nimport './InputSearch.less';\n\ninterface IProps {\n    labelName?: string,\n    width?: string,\n    placeholder?: string,\n    maxLength?: number,\n    maxHeight?: number,\n    // 是否开启前端搜索匹配\n    isOpenSearchMatch?: boolean,\n    loading?: boolean | JSX.Element,\n    // 配置提示列表\n    options?: string[],\n    // 当配置value时，即为可控组件\n    value?: string,\n    disabled?: boolean\n    // 按回车时回调\n    onSearch?: (value: string) => void,\n    // 输入字符、按下回车时回调\n    onChange?: (value: string) => void,\n    // 点击option中的item\n    onClick?: (value: string) => void,\n    // 滚动条到底时触发\n    onScrollButtom?: () => void\n}\n\nexport default function InputSearch(props: IProps): JSX.Element {\n    const id = Math.random().toString(36).substring(2);\n    let inputRef: any;\n\n    const [dataCache, setDataCache] = useState<string[]>(props.options || []);\n    const [value, setValue] = useState(props.value || '');\n\n    useEffect(() => {\n        const dataFilter = props.isOpenSearchMatch ? (props.options || []).filter(item => {\n            return item.indexOf(value) !== -1;\n        }) : (props.options || []);\n        setDataCache(dataFilter);\n    }, [props.options]);\n\n    useEffect(() => {\n        setValue(props.value || '');\n        // props.onChange && props.onChange(props.value);\n    }, [props.value]);\n\n    const handleChange = (value: string): void => {\n        setValue(value);\n        props.onChange && props.onChange(value);\n    };\n\n    const handleClick = (value: string): void => {\n        handleChange(value);\n        props.onClick && props.onClick(value);\n    };\n\n    const handleEnterKey = (e: React.KeyboardEvent<HTMLInputElement>): void => {\n        // 回车\n        if (e.nativeEvent.keyCode === 13) {\n            inputRef.blur && inputRef.blur();\n            props.onSearch && props.onSearch(e.currentTarget.value);\n        }\n    };\n\n    const highlightKeyWord = (item: string): JSX.Element => {\n        const keyWord = value;\n        const index = item.indexOf(value);\n        if (index === -1) {\n            return <span>{item}</span>;\n        }\n        const preStr = item.substring(0, index);\n        const nextStr = item.substring(index + value.length);\n        return <span>{preStr}<span className=\"highlight\">{keyWord}</span>{nextStr}</span>;\n    };\n\n    const debounce = (fun: any, time = 500): any => {\n        let timer: ReturnType<typeof setTimeout>;\n        return function (...args: any): void {\n            clearTimeout(timer);\n            timer = setTimeout(() => {\n                fun && fun.apply(null, [...args]);\n            }, time);\n        };\n    };\n\n    const debounceScroll = debounce(props.onScrollButtom);\n\n    const handleScroll = (e: React.UIEvent<HTMLElement>): void => {\n        e.stopPropagation();\n        // console.log({\n        //     event: e,\n        //     target: e.target, // Note 1* scrollTop is undefined on e.target\n        //     currentTarget: e.currentTarget,\n        //     scrollTop: e.currentTarget.scrollTop,\n        //     scrollHeight: e.currentTarget.scrollHeight,\n        //     clientHeight: e.currentTarget.clientHeight\n        // });\n        const { currentTarget } = e;\n        const { scrollTop, clientHeight, scrollHeight } = currentTarget;\n        const difference = scrollHeight - clientHeight - scrollTop;\n        if (difference < 20) {\n            props.onScrollButtom && debounceScroll();\n        }\n    };\n\n    return (\n        <div className=\"select-down-modern\">\n            {\n                props.labelName ? <label htmlFor={id} className=\"pb4 mb0 fs12 d-b\">{props.labelName}</label> : null\n            }\n            <div className=\"p-r d-f ac\" style={{ width: props.width || 100 + '%' }}>\n                <Input\n                    style={{ width: '100%' }}\n                    disabled={props.disabled}\n                    id={id}\n                    placeholder={props.placeholder || ''}\n                    maxLength={props.maxLength || 200}\n                    onChange={(e: ChangeEvent<HTMLInputElement>): void => handleChange(e.target.value)}\n                    onKeyPress={handleEnterKey}\n                    value={value}\n                    ref={element => inputRef = element}\n                />\n                <SearchOutlined className=\"p-a r0 mr8\" />\n            </div>\n\n            {\n                // 输入提示\n                dataCache.length ? <ul className=\"select-option shadow\" onScroll={handleScroll} style={{ 'maxHeight': `${props.maxHeight}px` }}>\n                    {\n                        props.loading ? <div className=\"p-s z9 ta-r\" style={{ right: `${0}px`, top: `${0}px` }}>\n                            <div className=\"d-il p-a\" style={{ right: `${8}px`, top: `${0}px` }}></div>\n                        </div> : null\n                    }\n                    {\n                        dataCache.map((item, index) => {\n                            return <li className=\"ellip1\" onMouseDown={(): void => handleClick(item)} key={index}>{highlightKeyWord(item)}</li>;\n                        })\n                    }\n                </ul> : null\n            }\n        </div>\n    );\n}", "import React, { ReactNode, useEffect, useState } from 'react';\nimport { Row, Col, Space, Table, ConfigProvider, Button, Modal, Tabs, message, Checkbox } from 'antd';\nimport './TableBox.less';\nimport { TablePaginationConfig } from 'antd/lib/table/Table';\nimport emptyImg from '../../images/emptyBg.png';\nimport { GetRowKey, SorterResult, TableRowSelection } from 'antd/lib/table/interface';\n// import ExportJsonExcel from 'js-export-excel';\nimport { Resizable } from 'react-resizable';\nimport { useTranslation } from 'react-i18next';\n\nconst CopyToClipboard = require('react-copy-to-clipboard');\n\ninterface IProps {\n\tsize?: 'large' | 'middle' | 'small'\n\ttableKey?: string\n\trowKey?: string | GetRowKey<any>;\n\ttitleNode?: string | ReactNode;\n\tbuttonNode?: ReactNode;\n\tdataSource: any;\n\tcolumns: any;\n\tpagination?: false | TablePaginationConfig;\n\tscroll?:\n\t| ({\n\t\tx?: string | number | true | undefined;\n\t\ty?: string | number | undefined;\n\t} & {\n\t\tscrollToFirstRowOnChange?: boolean | undefined;\n\t})\n\t| undefined;\n\tloading?: boolean;\n\trowSelection?: TableRowSelection<any>;\n\tcancelExportData?: boolean;\n\tonChange?: (\n\t\tpagination: TablePaginationConfig,\n\t\tfilters: Record<string, (string | number | boolean)[] | null>,\n\t\tsorter: SorterResult<any> | SorterResult<any>[],\n\t) => void;\n}\n\nconst ResizableTitle = ({ onResize, width, ...restProps }: any) => {\n\tif (!width) {\n\t\treturn <th {...restProps} />;\n\t}\n\n\treturn (\n\t\t<Resizable\n\t\t\twidth={width}\n\t\t\theight={0}\n\t\t\thandle={\n\t\t\t\t<span\n\t\t\t\t\tclassName=\"react-resizable-handle\"\n\t\t\t\t\tonClick={(e) => {\n\t\t\t\t\t\te.stopPropagation();\n\t\t\t\t\t}}\n\t\t\t\t/>\n\t\t\t}\n\t\t\tonResize={onResize}\n\t\t\tdraggableOpts={{ enableUserSelectHack: false }}\n\t\t>\n\t\t\t<th {...restProps} style={{ ...restProps?.style, userSelect: 'none' }} />\n\t\t</Resizable>\n\t);\n};\n\nconst TableBox = (props: IProps) => {\n\tconst [exportDataVisible, setExportDataVisible] = useState(false);\n\tconst [dataFormat, setDataFormat] = useState<{ header: any[]; data: any[] }>({\n\t\theader: [],\n\t\tdata: [],\n\t});\n\tconst [filterValue, setFilterValue] = useState<any[]>([]);\n\n\t// 可伸缩列设置\n\tconst [cols, setCols] = useState(props.columns);\n\tconst handleResize = (index: any) => {\n\t\treturn (_: any, { size }: any) => {\n\t\t\tif (size.width < 100) return\n\t\t\tconst temp = [...cols];\n\t\t\ttemp[index] = { ...temp[index], width: size.width };\n\t\t\tconst tableWidth = temp.reduce((pre: any, next: any) => pre + next.width || 100, 0) + 200\n\t\t\tlocalStorage.setItem(props.tableKey || '', JSON.stringify(temp))\n\t\t\t// console.log(currentTableScroll, temp);\n\t\t\tsetCurrentTableScroll({ ...currentTableScroll, x: tableWidth })\n\t\t\tsetCols(temp);\n\t\t};\n\t};\n\tconst customColumns = cols.map((col: any, index: any) => {\n\t\treturn {\n\t\t\t...col,\n\t\t\twidth: col.width || 200,\n\t\t\tonHeaderCell: (column: any) => {\n\t\t\t\treturn {\n\t\t\t\t\twidth: column.width,\n\t\t\t\t\tonResize: handleResize(index),\n\t\t\t\t};\n\t\t\t},\n\t\t};\n\t});\n\tconst [currentTableScroll, setCurrentTableScroll] = useState(props.scroll)\n\tconst { t, i18n } = useTranslation();\n\n\tuseEffect(() => {\n\t\tsetCols(props.columns);\n\t}, [props.columns]);\n\n\tuseEffect(() => {\n\t\tsetCurrentTableScroll(props.scroll);\n\t}, [props.scroll]);\n\n\tuseEffect(() => {\n\t\tif (props.dataSource) {\n\t\t\tconst columns = props.columns.filter((item: any) => ~filterValue.indexOf(item.dataIndex));\n\t\t\thanddleFilterHeader(columns, props.dataSource);\n\t\t}\n\t}, [props.dataSource, props.columns]);\n\n\tconst customizeRenderEmpty = () => (\n\t\t<Row justify=\"center\" align=\"middle\" style={{ height: 360, flexDirection: 'column' }}>\n\t\t\t<img src={emptyImg} style={{ width: 266 }} alt=\"\" />\n\t\t\t<div>{t('暂无数据')}</div>\n\t\t</Row>\n\t);\n\n\tconst handdleFilterHeader = (dataColumns = [], data: any[]) => {\n\t\tconst columns = dataColumns.map((item: any) => item.dataIndex).filter((item: string) => item !== 'handle');\n\t\tconst sheetHeader = dataColumns.map((item: any) => item.title).filter((item: string) => item !== t('操作'));\n\t\tconst tarData: any = [];\n\n\t\tdata.forEach((dataRow: any) => {\n\t\t\tconst row: any = {};\n\t\t\tcolumns.map((colName: string) => {\n\t\t\t\tconst res = dataRow[colName];\n\t\t\t\trow[colName] = res || '';\n\t\t\t});\n\t\t\ttarData.push(row);\n\t\t});\n\n\t\tsetDataFormat({\n\t\t\theader: sheetHeader,\n\t\t\tdata: tarData,\n\t\t});\n\t};\n\n\t// const handleClickOutputExcel = () => {\n\t// \tconst option: any = {};\n\t// \toption.fileName = 'result';\n\t// \toption.datas = [\n\t// \t\t{\n\t// \t\t\tsheetData: dataFormat.data,\n\t// \t\t\tsheetName: 'sheet',\n\t// \t\t\tsheetHeader: dataFormat.header,\n\t// \t\t},\n\t// \t];\n\t// \tconst toExcel = new ExportJsonExcel(option);\n\t// \ttoExcel.saveExcel();\n\t// };\n\n\tconst handleExportJira = () => {\n\t\tconst header = dataFormat.header;\n\t\tconst data = dataFormat.data;\n\t\tlet str = '';\n\t\tif (header.length && data.length) {\n\t\t\tstr =\n\t\t\t\t'|' +\n\t\t\t\theader.join('|') +\n\t\t\t\t'|' +\n\t\t\t\t`\n`;\n\t\t\tdata.forEach((row: any) => {\n\t\t\t\tconst rowKey = Object.values(row).map((item) => {\n\t\t\t\t\tif (item === '') {\n\t\t\t\t\t\treturn ' ';\n\t\t\t\t\t}\n\t\t\t\t\treturn item;\n\t\t\t\t});\n\t\t\t\tstr =\n\t\t\t\t\tstr +\n\t\t\t\t\t'|' +\n\t\t\t\t\trowKey.join('|') +\n\t\t\t\t\t'|' +\n\t\t\t\t\t`\n`;\n\t\t\t});\n\t\t} else {\n\t\t\tstr = '';\n\t\t}\n\n\t\treturn str;\n\t};\n\n\tconst handleExportText = () => {\n\t\tconst header = dataFormat.header;\n\t\tconst data = dataFormat.data;\n\t\tlet str = '';\n\t\tif (header.length && data.length) {\n\t\t\tstr =\n\t\t\t\theader.join('\t') +\n\t\t\t\t`\n`;\n\t\t\tdata.forEach((row: any) => {\n\t\t\t\tconst rowKey = Object.values(row).map((item) => {\n\t\t\t\t\tif (item === '') {\n\t\t\t\t\t\treturn ' ';\n\t\t\t\t\t}\n\t\t\t\t\treturn item;\n\t\t\t\t});\n\t\t\t\tstr =\n\t\t\t\t\tstr +\n\t\t\t\t\trowKey.join('\t') +\n\t\t\t\t\t`\n`;\n\t\t\t});\n\t\t} else {\n\t\t\tstr = '';\n\t\t}\n\t\treturn str;\n\t};\n\n\treturn (\n\t\t<Space className=\"tablebox\" direction=\"vertical\" size=\"middle\">\n\t\t\t<Modal\n\t\t\t\twidth={1000}\n\t\t\t\tmaskClosable={false}\n\t\t\t\tcentered={true}\n\t\t\t\tbodyStyle={{ maxHeight: 500, overflow: 'auto' }}\n\t\t\t\tvisible={exportDataVisible}\n\t\t\t\ttitle={t('导出数据')}\n\t\t\t\tonCancel={() => {\n\t\t\t\t\tsetExportDataVisible(false);\n\t\t\t\t}}\n\t\t\t\tfooter={null}\n\t\t\t>\n\t\t\t\t<div style={{ position: 'relative' }}>\n\t\t\t\t\t<div className=\"mb16\"><span className=\"pr8\">{t('选择需要导出的列')}：</span><Checkbox.Group\n\t\t\t\t\t\toptions={props.columns\n\t\t\t\t\t\t\t.map((item: any) => ({ label: item.title, value: item.dataIndex }))\n\t\t\t\t\t\t\t.filter((item: any) => item.value !== 'handle')}\n\t\t\t\t\t\tdefaultValue={[]}\n\t\t\t\t\t\tvalue={filterValue}\n\t\t\t\t\t\tonChange={(values: any) => {\n\t\t\t\t\t\t\tsetFilterValue(values);\n\t\t\t\t\t\t\tconst columns = props.columns.filter((item: any) => ~values.indexOf(item.dataIndex));\n\t\t\t\t\t\t\thanddleFilterHeader(columns, props.dataSource);\n\t\t\t\t\t\t}}\n\t\t\t\t\t/></div>\n\t\t\t\t\t<div style={{ position: 'absolute', right: 0, bottom: 0 }}>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\ttype=\"link\"\n\t\t\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\t\t\tsetFilterValue(\n\t\t\t\t\t\t\t\t\tprops.columns\n\t\t\t\t\t\t\t\t\t\t.map((item: any) => item.dataIndex)\n\t\t\t\t\t\t\t\t\t\t.filter((item: any) => item !== 'handle'),\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\thanddleFilterHeader(props.columns, props.dataSource);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{t('全选')}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\ttype=\"link\"\n\t\t\t\t\t\t\tonClick={() => {\n\t\t\t\t\t\t\t\tsetFilterValue([]);\n\t\t\t\t\t\t\t\thanddleFilterHeader([], props.dataSource);\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{t('反选')}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Tabs>\n\t\t\t\t\t<Tabs.TabPane tab=\"Wiki格式\" key=\"jira\">\n\t\t\t\t\t\t<CopyToClipboard text={handleExportJira()} onCopy={() => message.success(t('已复制到粘贴板'))}>\n\t\t\t\t\t\t\t<pre style={{ cursor: 'pointer', minHeight: 100 }}>\n\t\t\t\t\t\t\t\t<code>{handleExportJira()}</code>\n\t\t\t\t\t\t\t</pre>\n\t\t\t\t\t\t</CopyToClipboard>\n\t\t\t\t\t</Tabs.TabPane>\n\t\t\t\t\t<Tabs.TabPane tab=\"Text格式\" key=\"test\">\n\t\t\t\t\t\t<CopyToClipboard text={handleExportText()} onCopy={() => message.success(t('已复制到粘贴板'))}>\n\t\t\t\t\t\t\t<pre style={{ cursor: 'pointer', minHeight: 100 }}>\n\t\t\t\t\t\t\t\t<code>{handleExportText()}</code>\n\t\t\t\t\t\t\t</pre>\n\t\t\t\t\t\t</CopyToClipboard>\n\t\t\t\t\t</Tabs.TabPane>\n\t\t\t\t\t{/* <Tabs.TabPane tab=\"Excel格式\" key=\"excel\">\n\t\t\t\t\t\t<Row justify=\"center\" align=\"middle\" style={{ minHeight: 100 }}>\n\t\t\t\t\t\t\t<Col>\n\t\t\t\t\t\t\t\t<Button type=\"primary\" onClick={handleClickOutputExcel}>\n\t\t\t\t\t\t\t\t\t导出Excel\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</Col>\n\t\t\t\t\t\t</Row>\n\t\t\t\t\t</Tabs.TabPane> */}\n\t\t\t\t</Tabs>\n\t\t\t</Modal>\n\t\t\t{\n\t\t\t\tprops.titleNode || props.buttonNode || !props.cancelExportData ? <Row justify=\"space-between\" align=\"middle\">\n\t\t\t\t\t<Col>\n\t\t\t\t\t\t<Space align=\"center\">{props.titleNode}</Space>\n\t\t\t\t\t</Col>\n\t\t\t\t\t<Col>\n\t\t\t\t\t\t<Space align=\"center\">\n\t\t\t\t\t\t\t{props.buttonNode}\n\t\t\t\t\t\t\t{props.cancelExportData ? null : (\n\t\t\t\t\t\t\t\t<Button style={{ marginLeft: 6 }} onClick={() => setExportDataVisible(true)}>\n\t\t\t\t\t\t\t\t\t{t('导出数据')}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t</Space>\n\t\t\t\t\t</Col>\n\t\t\t\t</Row> : null\n\t\t\t}\n\t\t\t<ConfigProvider renderEmpty={customizeRenderEmpty}>\n\t\t\t\t<Table\n\t\t\t\t\tsize={props.size || 'middle'}\n\t\t\t\t\trowKey={props.rowKey ? props.rowKey : 'id'}\n\t\t\t\t\tdataSource={props.dataSource}\n\t\t\t\t\t// columns={props.columns}\n\t\t\t\t\tcomponents={{ header: { cell: ResizableTitle } }}\n\t\t\t\t\tcolumns={customColumns}\n\t\t\t\t\tpagination={props.pagination !== false ? { ...props.pagination } : false}\n\t\t\t\t\tscroll={currentTableScroll}\n\t\t\t\t\tloading={props.loading}\n\t\t\t\t\tonChange={props.onChange}\n\t\t\t\t\trowSelection={props.rowSelection}\n\t\t\t\t/>\n\t\t\t</ConfigProvider>\n\t\t</Space>\n\t);\n};\n\nexport default TableBox;\n", "import { AxiosResponse } from 'axios'\nimport axios, { AxiosResFormat } from '.'\nimport { TTaskStatus } from '../pages/DataSearch/interface'\n\nexport const actionRun = (params: {\n    sql: string,\n    [key: string]: any\n}): Promise<AxiosResponse<{\n    err_msg: string\n    log_url: string\n    task_id: string\n    task_url: string\n}>> => {\n    return axios.post('/idex/submit_task', params)\n}\n\nexport const actionGetDataSearchRes = (task_id: string): Promise<AxiosResponse<{\n    err_msg: string\n    result: Array<Array<string | number>>\n    state: TTaskStatus\n    task_url: string\n    result_url: string\n    stage: any\n    spark_log_url: string\n    spark_ui_url: string\n}>> => {\n    return axios.get(`/idex/look/${task_id}`,)\n}\n\nexport const getIdexDBList = (): Promise<AxiosResponse<{\n    dbs: string[]\n}>> => {\n    return axios.get(`/idex/get_user_db`)\n}\n\nexport const getIdexTableList = (table: string): Promise<AxiosResponse<{\n    tables: string[]\n}>> => {\n    return axios.get(`/idex/get_user_db_tables/${table}`)\n}\n\nexport const getIdexTaskDownloadUrl = (id: string, separator: string): Promise<AxiosResponse<{\n    download_url: string\n}>> => {\n    return axios.get(`/idex/download_url/${id}`, {\n        params: {\n            separator\n        }\n    })\n}\n\nexport const getIdexTaskResult = (id: string): Promise<AxiosResponse<{\n    err_msg: string\n    result: Array<Array<string | number>>\n}>> => {\n    return axios.get(`/idex/result/${id}`)\n}\n\nexport const stopIndxTask = (id: string): Promise<AxiosResponse<{\n    err_msg: string\n    result: Array<Array<string | number>>\n}>> => {\n    return axios.get(`/idex/stop/${id}`)\n}\n\n\nexport const getIndexResourceOverview = (group_id: string): Promise<AxiosResponse<{\n    err_msg: string\n    result: Array<Array<string | number>>\n}>> => {\n    return axios.get(`/idex/get_resource/${group_id}`)\n}\n\nexport interface IIdexFormConfigItem {\n    id: string,\n    label: string,\n    type: 'input' | 'select' | 'input-select',\n    value: IIdexFormConfigOption[]\n    defaultValue: string\n    multiple: boolean\n    disable: boolean\n    placeHolder: string\n}\n\nexport interface IIdexFormConfigOption {\n    label: string\n    value: string\n    relate: {\n        relateId: string\n        value: IIdexFormConfigOption[]\n    }\n}\n\nexport const getIdexFormConfig = (): Promise<AxiosResponse<{\n    result: IIdexFormConfigItem[]\n}>> => {\n    return axios.get(`/idex/config`)\n}", "import React from 'react';\nimport CodeMirror, { ReactCodeMirrorProps } from '@uiw/react-codemirror';\nimport { bbedit } from '@uiw/codemirror-theme-bbedit';\nimport { zebraStripes } from '@uiw/codemirror-extensions-zebra-stripes';\nimport { sql } from '@codemirror/lang-sql';\n\ninterface IProps extends ReactCodeMirrorProps {\n\n}\n\nconst sqlLang = `CREATE TABLE dbo.EmployeePhoto\n(\n    EmployeeId INT NOT NULL PRIMARY KEY,\n    Photo VARBINARY(MAX) FILESTREAM NULL,\n    MyRowGuidColumn UNIQUEIDENTIFIER NOT NULL ROWGUIDCOL\n                    UNIQUE DEFAULT NEWID()\n);\n\nGO\n\n/*\ntext_of_comment\n/* nested comment */\n*/\n\n-- line comment\n\nCREATE NONCLUSTERED INDEX IX_WorkOrder_ProductID\n    ON Production.WorkOrder(ProductID)\n    WITH (FILLFACTOR = 80,\n        PAD_INDEX = ON,\n        DROP_EXISTING = ON);\nGO\n\nWHILE (SELECT AVG(ListPrice) FROM Production.Product) < $300\nBEGIN\n   UPDATE Production.Product\n      SET ListPrice = ListPrice * 2\n   SELECT MAX(ListPrice) FROM Production.Product\n   IF (SELECT MAX(ListPrice) FROM Production.Product) > $500\n      BREAK\n   ELSE\n      CONTINUE\nEND\nPRINT 'Too much for the market to bear';\n\nMERGE INTO Sales.SalesReason AS [Target]\nUSING (VALUES ('Recommendation','Other'), ('Review', 'Marketing'), ('Internet', 'Promotion'))\n       AS [Source] ([NewName], NewReasonType)\nON [Target].[Name] = [Source].[NewName]\nWHEN MATCHED\nTHEN UPDATE SET ReasonType = [Source].NewReasonType\nWHEN NOT MATCHED BY TARGET\nTHEN INSERT ([Name], ReasonType) VALUES ([NewName], NewReasonType)\nOUTPUT $action INTO @SummaryOfChanges;\n\nSELECT ProductID, OrderQty, SUM(LineTotal) AS Total\nFROM Sales.SalesOrderDetail\nWHERE UnitPrice < $5.00\nGROUP BY ProductID, OrderQty\nORDER BY ProductID, OrderQty\nOPTION (HASH GROUP, FAST 10);\n`;\n\n// https://uiwjs.github.io/react-codemirror/\n// https://www.npmjs.com/package/@uiw/react-codemirror\n// https://github.com/uiwjs/react-codemirror\nexport default function CodeEdit(props: IProps) {\n  return (\n    <>\n      <CodeMirror\n        theme={bbedit}\n        value={props.value}\n        onChange={props.onChange}\n        extensions={[\n          sql(),\n          zebraStripes({\n            step: 2,\n            lightColor: '#fafafa',\n            darkColor: '#fafafa',\n          })]} />\n    </>\n  )\n}\n\n// import React, { useEffect, useState } from 'react';\n// import { Controlled as CodeMirror } from 'react-codemirror2';\n// // 主题\n// import 'codemirror/lib/codemirror.css';\n// import 'codemirror/theme/solarized.css';\n// // 代码模式\n// import 'codemirror/mode/sql/sql';\n// // 代码补全\n// import 'codemirror/addon/hint/show-hint.css'\n// import 'codemirror/addon/hint/sql-hint';\n// import 'codemirror/addon/hint/show-hint';\n// import 'codemirror/addon/edit/closebrackets';\n// import 'codemirror/addon/hint/anyword-hint.js';\n// //折叠代码\n// import 'codemirror/addon/fold/foldgutter.css';\n// import 'codemirror/addon/fold/foldcode.js';\n// import 'codemirror/addon/fold/foldgutter.js';\n// import 'codemirror/addon/fold/brace-fold.js';\n// import 'codemirror/addon/fold/comment-fold.js';\n// // 代码高亮\n// import 'codemirror/addon/selection/active-line';\n\n// interface IProps {\n//   value?: string;\n//   onChange?: (value: string) => void;\n//   onSelect?: (value: string) => void\n//   readonly?: boolean\n// }\n\n// // https://github.com/scniro/react-codemirror2\n// // https://xudany.github.io/codemirror/2020/07/21/CodeMirror%E5%AE%9E%E7%8E%B0%E8%87%AA%E5%AE%9A%E4%B9%89%E6%8F%90%E7%A4%BA%E5%8A%9F%E8%83%BD/\n// export default function CodeEdit(props: IProps) {\n//   const [instance, setInstance] = useState<any>(null);\n\n//   return (\n//     <>\n//       <CodeMirror\n//         editorDidMount={(editor) => {\n//           setInstance(editor);\n//         }}\n//         value={props.value || ''}\n//         options={{\n//           placeholder: '输入SQL进行查询',\n//           mode: 'sql',\n//           theme: 'solarized',\n//           lineNumbers: true,\n//           smartIndent: true,\n//           lineWrapping: true,\n//           styleActiveLine: true,\n//           foldGutter: true,\n//           matchBrackets: true, //括号匹配，光标旁边的括号都高亮显示\n//           autoCloseBrackets: true, //键入时将自动关闭()[]{}''\"\"\n//           gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],\n//           extraKeys: { Ctrl: 'autocomplete' },\n//           hintOptions: {\n//             completeSingle: false,\n//             alignWithWord: true\n//           },\n//         }}\n//         onCursorActivity={(editor) => {\n//           // console.log(editor.getSelection())\n//           const value = editor.getSelection()\n//           props.onSelect && props.onSelect(value)\n//         }}\n//         onBeforeChange={(editor, data, value) => {\n//           if (!props.readonly) {\n//             if (data.origin !== 'complete') {\n//               editor.execCommand('autocomplete');\n//             }\n//             props.onChange && props.onChange(value);\n//           }\n//         }}\n//       // onChange={(editor, data, value) => {\n//       //   console.log(editor, data, value)\n//       //   props.onChange && props.onChange(value);\n//       // }}\n//       />\n//     </>\n//   );\n// }", "import { CaretRightOutlined, CloseCircleOutlined, CloseOutlined, DeleteOutlined, DownloadOutlined, LoadingOutlined, QuestionCircleOutlined, RedoOutlined, StopOutlined } from '@ant-design/icons';\nimport { Button, Col, Collapse, message, Modal, Row, Select, Spin, Steps, Tooltip } from 'antd'\nimport React, { useRef, useState } from 'react'\nimport TableBox from '../../components/TableBox/TableBox';\nimport { TTaskStep, IEditorTaskItem, TTaskStatus } from './interface'\nimport './DataSearch.less';\nimport { getIdexTaskDownloadUrl, getIdexTaskResult, stopIndxTask } from '../../api/dataSearchApi';\nimport { useTranslation } from 'react-i18next';\n// import Spreadsheet from '../../components/Spreadsheet/Spreadsheet';\n\nexport interface ITaskListItem { }\n\ninterface IProps {\n    onDelete: (id: string) => void,\n    onRetry: (id: string) => void,\n    option: Record<string, IEditorTaskItem>\n}\n\nconst stepMap: Record<TTaskStep, number> = {\n    'start': 0,\n    'parse': 1,\n    'execute': 2,\n    'end': 3\n}\n\nconst statusIconMap = (status: TTaskStatus) => {\n    switch (status) {\n        case 'init':\n        case 'running':\n            return <LoadingOutlined />\n        case 'failure':\n            return <CloseCircleOutlined style={{ color: '#ff4444' }} />\n        case 'stop':\n            return <QuestionCircleOutlined />\n        default:\n            return null\n    }\n}\n\nexport default function TaskList(props: IProps) {\n    const { t, i18n } = useTranslation();\n    const [visibleDownload, setVisibleDownload] = useState(false)\n    const [visibleResult, setVisibleResult] = useState(false)\n    // const [dataConfig, setDataConfig] = useState<any>([])\n    // const [dataResult, setDataResult] = useState<Record<string, any>[]>([])\n    const [loadingResult, setLoadingResult] = useState(true)\n\n    // const [dataConfig, _setDataConfig] = useState<any>([])\n    // const dataConfigRef = useRef(dataConfig);\n    // const setDataConfig = (data: any): void => {\n    //     dataConfigRef.current = data;\n    //     _setDataConfig(data);\n    // };\n\n    // const [dataResult, _setDataResult] = useState<any>([])\n    // const dataResultRef = useRef(dataResult);\n    // const setDataResult = (data: any): void => {\n    //     dataResultRef.current = data;\n    //     _setDataResult(data);\n    // };\n\n    const [separator, _setSeparator] = useState<string>('|')\n    const separatorRef = useRef(separator);\n    const setSeparator = (data: string): void => {\n        separatorRef.current = data;\n        _setSeparator(data);\n    };\n\n    const [currentReqId, _setCurrentReqId] = useState<string>('|')\n    const currentReqIdRef = useRef(currentReqId);\n    const setCurrentReqId = (data: string): void => {\n        currentReqIdRef.current = data;\n        _setCurrentReqId(data);\n    };\n\n    const onChange = (key: string | string[]) => {\n        console.log(key);\n    };\n\n    const handleResultData = (result: (string | number)[][]) => {\n        const header = result[0] || []\n        const data = result.slice(1)\n        const targetData = data.map((row) => {\n            const rowItem = row.reduce((pre, next, index) => ({ ...pre, [header[index]]: next }), {})\n            return rowItem\n        })\n        const headerConfig = header.map(item => ({\n            title: item,\n            dataIndex: item,\n            key: item,\n            width: 120,\n        }))\n        // targetData.unshift(header)\n        // setDataConfig(headerConfig)\n        // setDataResult(targetData)\n\n        return {\n            config: headerConfig,\n            data: targetData\n        }\n    }\n\n    // const handleClickDownload = (id: any) => {\n    //     Modal.confirm({\n    //         title: '下载结果',\n    //         icon: <DownloadOutlined />,\n    //         content: <div>\n    //             <div className=\"d-f ac pt16\">\n    //                 <div className=\"w72\">分隔符：</div>\n    //                 {console.log('separatorRef.current', separatorRef.current, separator)}\n    //                 <Select style={{ width: 256 }} value={separatorRef.current} options={[{\n    //                     label: '|',\n    //                     value: '|'\n    //                 }, {\n    //                     label: ',',\n    //                     value: ','\n    //                 }, {\n    //                     label: 'tab',\n    //                     value: 'tab'\n    //                 }]} onChange={(value) => {\n\n    //                     // separatorRef.current = value\n    //                     setSeparator(value)\n    //                     _setSeparator(value)\n    //                     console.log(value, separatorRef.current);\n    //                 }} />\n    //             </div>\n    //         </div>,\n    //         okText: `确认`,\n    //         cancelText: '取消',\n    //         onOk() {\n    //             return new Promise((resolve, reject) => {\n    //                 getIdexTaskDownloadUrl(id).then(res => {\n    //                     window.open(res.data.download_url, 'bank')\n    //                     resolve('')\n    //                 }).catch(err => {\n    //                     reject()\n    //                 })\n    //             })\n    //                 .then((res) => {\n    //                     message.success('下载成功');\n    //                 })\n    //                 .catch(() => {\n    //                     message.error('下载失败');\n    //                 });\n    //         },\n    //         onCancel() { },\n    //     });\n    // }\n\n    return (\n        <div>\n            {/* <Modal\n                title={`下载结果`}\n                visible={visibleResult}\n                footer={null}\n                width={1248}\n                destroyOnClose\n                onCancel={() => {\n                    setVisibleResult(false)\n                }}>\n                <Spin spinning={loadingResult}>\n                    <Spreadsheet height={700} width={1200} dataSource={dataResult} />\n                </Spin>\n            </Modal> */}\n\n            <Modal\n                title={t('结果')}\n                visible={visibleDownload}\n                footer={null}\n                destroyOnClose\n                onCancel={() => {\n                    setSeparator('|')\n                    setVisibleDownload(false)\n                }}>\n                <div>\n                    <div className=\"d-f ac pt8\">\n                        <div className=\"w96\">{t('选择分隔符')}：</div>\n                        <Select style={{ width: 256 }} value={separatorRef.current} options={[{\n                            label: '|',\n                            value: '|'\n                        }, {\n                            label: ',',\n                            value: ','\n                        }, {\n                            label: 'TAB',\n                            value: 'TAB'\n                        }]} onChange={(value) => {\n                            setSeparator(value)\n                        }} />\n                    </div>\n                    <div className=\"ta-r pt16\">\n                        <Button type=\"primary\" onClick={() => {\n                            getIdexTaskDownloadUrl(currentReqIdRef.current, separatorRef.current).then(res => {\n                                window.open(res.data.download_url, 'bank')\n                            }).catch(err => {\n                                console.log(err)\n                            })\n                        }}>{t('下载')}</Button>\n                    </div>\n                </div>\n            </Modal>\n            <Collapse className=\"site-collapse-custom-collapse\" defaultActiveKey={['task_0']} onChange={onChange}>\n                {\n                    (Object.entries(props.option).reduce((pre: IEditorTaskItem[], [key, value]) => ([...pre, value]), []) || []).reverse().filter(item => !!item.reqId).map((item, index) => {\n                        return (\n                            <Collapse.Panel className={['site-collapse-custom-panel', `status-${item.status}`].join(' ')} header={`${t('子任务')}${item.reqId}`} key={`task_${index}`} extra={\n                                <>\n                                    <Button className=\"mr16\" type=\"default\" size='small' onClick={(e) => {\n                                        e.stopPropagation();\n                                        props.onDelete(item.reqId)\n                                    }}>{t('删除')}<DeleteOutlined /></Button>\n                                    <Button type=\"primary\" size='small' onClick={(e) => {\n                                        e.stopPropagation();\n                                        props.onRetry(item.reqId)\n                                    }}\n                                    >{t('重试')}<RedoOutlined /></Button>\n                                </>\n                            }>\n                                <Steps size=\"small\" current={stepMap[item.step]}>\n                                    <Steps.Step title={t('准备开始')} icon={stepMap[item.step] === 0 ? statusIconMap(item.status) : null} />\n                                    <Steps.Step title={t('解析')} icon={stepMap[item.step] === 1 ? statusIconMap(item.status) : null} />\n                                    <Steps.Step title={t('执行')} icon={stepMap[item.step] === 2 ? statusIconMap(item.status) : null} />\n                                    <Steps.Step title={t('输出结果')} icon={stepMap[item.step] === 3 ? statusIconMap(item.status) : null} />\n                                </Steps>\n                                <TableBox\n                                    size={\"small\"}\n                                    loading={false}\n                                    cancelExportData={true}\n                                    rowKey={(record: any) => {\n                                        return JSON.stringify(record)\n                                    }}\n                                    columns={[{\n                                        title: t('子任务'),\n                                        dataIndex: 'content',\n                                        key: 'content',\n                                        render: (text: any) => {\n                                            return <Tooltip\n                                                placement=\"top\"\n                                                title={text}\n                                            >\n                                                <div className=\"ellip1 w256\">{text}</div>\n                                            </Tooltip>\n                                        }\n                                    },\n                                    // {\n                                    //     title: '数据库',\n                                    //     dataIndex: 'database',\n                                    //     key: 'database',\n                                    // }, {\n                                    //     title: '表',\n                                    //     dataIndex: 'table',\n                                    //     key: 'table',\n                                    // },\n                                    {\n                                        title: t('开始时间'),\n                                        dataIndex: 'startime',\n                                        key: 'startime',\n                                    }, {\n                                        title: t('运行时长'),\n                                        dataIndex: 'duration',\n                                        key: 'duration',\n                                    }, {\n                                        title: t('状态'),\n                                        dataIndex: 'status',\n                                        key: 'status',\n                                        render: (text: any) => {\n                                            return <span className={[`c-${item.status}`].join(' ')}>{text}</span>\n                                        }\n                                    }, {\n                                        title: t('操作'),\n                                        dataIndex: 'action',\n                                        key: 'action',\n                                        render: () => {\n                                            return <>\n                                                <span className=\"link mr16\" onClick={() => {\n                                                    // setVisibleDetail(true)\n                                                    Modal.info({\n                                                        title: t('任务详情'),\n                                                        width: 600,\n                                                        okText: t('关闭'),\n                                                        content: (\n                                                            <div>\n                                                                <Row className=\"mb16\">\n                                                                    <Col span={6}><div className=\"ta-r\"><strong>{t('开始时间')}：</strong></div></Col>\n                                                                    <Col span={18}>{item.startTime}</Col>\n                                                                </Row>\n                                                                <Row className=\"mb16\">\n                                                                    <Col span={6}><div className=\"ta-r\"><strong>{t('运行时长')}：</strong></div></Col>\n                                                                    <Col span={18}>{item.duration}</Col>\n                                                                </Row>\n                                                                <Row className=\"mb16\">\n                                                                    <Col span={6}><div className=\"ta-r\"><strong>{t('状态')}：</strong></div></Col>\n                                                                    <Col span={18}>{item.status}</Col>\n                                                                </Row>\n                                                                <Row className=\"mb16\">\n                                                                    <Col span={6}><div className=\"ta-r\"><strong>{t('子任务内容')}：</strong></div></Col>\n                                                                    <Col span={18}>{item.content}</Col>\n                                                                </Row>\n                                                                <Row className=\"mb16\">\n                                                                    <Col span={6}><div className=\"ta-r\"><strong>{t('任务信息')}：</strong></div></Col>\n                                                                    <Col span={18}>{item.message}</Col>\n                                                                </Row>\n                                                            </div>\n                                                        ),\n                                                        onOk() { },\n                                                    });\n                                                }}>{t('详情')}</span>\n                                                {\n                                                    !(item.step === 'end' && item.status === 'success') ? <span className=\"link mr16\" onClick={() => {\n                                                        Modal.confirm({\n                                                            title: t('终止任务'),\n                                                            icon: <StopOutlined />,\n                                                            content: '',\n                                                            okText: t('确认'),\n                                                            cancelText: t('取消'),\n                                                            onOk() {\n                                                                return new Promise((resolve, reject) => {\n                                                                    stopIndxTask(item.reqId).then(res => {\n                                                                        resolve('')\n                                                                    }).catch(err => {\n                                                                        reject()\n                                                                    })\n                                                                })\n                                                                    .then((res) => {\n                                                                        message.success(t('终止成功'));\n                                                                    })\n                                                                    .catch(() => {\n                                                                        message.error(t('终止失败'));\n                                                                    });\n                                                            },\n                                                            onCancel() { },\n                                                        });\n                                                    }}>{t('终止')}</span> : null\n                                                }\n                                                {\n                                                    !!item.log ? <span className=\"link mr16\" onClick={() => {\n                                                        window.open(item.log, 'bank')\n                                                    }}>{t('日志')}</span> : null\n                                                }\n                                                {\n                                                    item.step === 'end' && item.status === 'success' ? <span className=\"link mr16\" onClick={() => {\n                                                        setLoadingResult(true)\n                                                        getIdexTaskResult(item.reqId).then(res => {\n                                                            setVisibleResult(true)\n                                                            const result = res.data.result\n                                                            handleResultData(result)\n                                                            const handleData = handleResultData(result)\n                                                            // setDataResult(handleData)\n                                                            Modal.info({\n                                                                title: t('结果查看'),\n                                                                content: (\n                                                                    <div>\n                                                                        {/* {(result || [])?.map(item => {\n                                                                            return <div>{item}</div>\n                                                                        })} */}\n                                                                        <TableBox\n\n                                                                            size={\"small\"}\n                                                                            loading={false}\n                                                                            cancelExportData={true}\n                                                                            rowKey={(record: any) => {\n                                                                                return JSON.stringify(record)\n                                                                            }}\n                                                                            columns={handleData.config}\n                                                                            pagination={false}\n                                                                            dataSource={handleData.data}\n                                                                            scroll={{ x: 'auto' }}\n                                                                        />\n                                                                    </div>\n                                                                ),\n                                                                onOk() { },\n                                                            });\n                                                        }).catch(err => { }).finally(() => {\n                                                            setLoadingResult(false)\n                                                        })\n                                                    }}>{t('结果')}</span> : null\n                                                }\n                                                {\n                                                    item.step === 'end' && item.status === 'success' ? <span className=\"link\" onClick={() => {\n                                                        // handleClickDownload(item.reqId)\n                                                        setCurrentReqId(item.reqId)\n                                                        setVisibleDownload(true)\n                                                    }}>{t('下载')}</span> : null\n                                                }\n                                            </>\n                                        }\n                                    }]}\n                                    pagination={false}\n                                    dataSource={[{\n                                        content: item.content,\n                                        database: item.database || '-',\n                                        table: item.table || '-',\n                                        startime: item.startTime,\n                                        duration: item.duration || '-',\n                                        status: item.status\n                                    }]}\n                                />\n                            </Collapse.Panel>\n                        )\n                    })\n                }\n\n                {/* <Collapse.Panel className=\"site-collapse-custom-panel status-error\" header=\"子任务2\" key=\"2\" >\n                    <Steps size=\"small\" current={1}>\n                        <Steps.Step title=\"准备开始\" />\n                        <Steps.Step title=\"解析中\" icon={<LoadingOutlined />} />\n                        <Steps.Step title=\"执行中\" />\n                        <Steps.Step title=\"输出结果\" />\n                    </Steps>\n                    <TableBox\n                        size={\"small\"}\n                        loading={false}\n                        cancelExportData={true}\n                        rowKey={(record: any) => {\n                            return JSON.stringify(record)\n                        }}\n                        columns={[{\n                            title: '子任务',\n                            dataIndex: 'task',\n                            key: 'task',\n                        }, {\n                            title: '开始时间',\n                            dataIndex: 'startime',\n                            key: 'startime',\n                        }, {\n                            title: '运行时长',\n                            dataIndex: 'time',\n                            key: 'time',\n                        }, {\n                            title: '状态',\n                            dataIndex: 'status',\n                            key: 'status',\n                        }, {\n                            title: '操作',\n                            dataIndex: 'action',\n                            key: 'action',\n                            render: () => {\n                                return <>\n                                    <span className=\"link mr16\">详情</span>\n                                    <span className=\"link mr16\">日志</span>\n                                    <span className=\"link\">结果</span>\n                                </>\n                            }\n                        }]}\n                        pagination={false}\n                        dataSource={[{\n                            task: 'test',\n                            startime: '2022-08-09 17:45:46',\n                            time: '3分钟43秒',\n                            status: 'error'\n                        }]}\n                    />\n                </Collapse.Panel>\n                <Collapse.Panel className=\"site-collapse-custom-panel status-running\" header=\"子任务3\" key=\"3\">\n                    <p>{345}</p>\n                </Collapse.Panel> */}\n            </Collapse>\n        </div>\n    )\n}\n", "import { Form } from 'antd'\nimport Select, { LabeledValue } from 'antd/lib/select';\nimport React, { useEffect, useImperativeHandle, useState } from 'react'\nimport { useTranslation } from 'react-i18next';\nimport { IIdexFormConfigItem } from '../../api/dataSearchApi'\nimport InputSearch from '../../components/InputSearch/InputSearch';\nimport './ConfigFormData.less';\n\nexport type TDataValue = Record<any, any>\n\nexport interface IConfigFormDataOptionItem extends IIdexFormConfigItem { }\n\nexport interface IProps {\n    dataValue?: TDataValue\n    option: IConfigFormDataOptionItem[]\n    onChange: (dataValue: TDataValue) => void\n    onConfigChange?: (config: IConfigFormDataOptionItem[]) => void\n}\n\nconst ConfigFormData = React.forwardRef((props: IProps, ref) => {\n    const { t, i18n } = useTranslation();\n    const [form] = Form.useForm();\n    const [, updateState] = useState<any>();\n\n    useImperativeHandle(ref, () => ({\n        onSubmit: () => {\n            return new Promise((resolve, reject) => {\n                form.validateFields().then(res => {\n                    resolve(res)\n                }).catch(err => {\n                    reject(err)\n                })\n            })\n        },\n        setData: (data: Record<any, any>) => {\n            form.setFieldsValue(data)\n        }\n    }));\n\n    useEffect(() => {\n        if (props.dataValue) {\n            form.setFieldsValue(props.dataValue)\n        }\n    }, [props.option])\n\n    const renderInput = (config: IConfigFormDataOptionItem, itemProps: Record<string, any>) => {\n        return <div></div>\n    }\n\n    const renderSelect = (config: IConfigFormDataOptionItem, itemProps: Record<string, any>) => {\n        const options: LabeledValue[] = config.value || []\n        return <Form.Item\n            key={`configFormData_${config.id}`}\n            label={config.label}\n            name={config.id}\n            rules={[\n                {\n                    required: true,\n                    message: `${t('请选择')}${config.label}`,\n                },\n            ]}\n            initialValue={config.defaultValue}\n            style={{ marginBottom: 0, marginRight: 16 }}\n            {...itemProps}\n        >\n            <Select\n                style={{ width: 200 }}\n                mode={config.multiple ? 'multiple' : undefined}\n                showSearch\n                disabled={config.disable}\n                optionFilterProp=\"label\"\n                placeholder={config.placeHolder || `${t('请选择')} ${config.label}`}\n                options={options}\n                onChange={(value, rowOption: any) => {\n                    if (rowOption.relate) {\n                        const relateId = rowOption.relate.relateId\n                        const relateOption = rowOption.relate.value\n                        const currentOption = props.option\n                        for (let i = 0; i < currentOption.length; i++) {\n                            const item = currentOption[i];\n                            if (item.id === relateId) {\n                                item.value = relateOption\n                            }\n                        }\n                        props.onConfigChange && props.onConfigChange(currentOption)\n                    }\n                    props.onChange && props.onChange(form.getFieldsValue())\n                }} />\n        </Form.Item>\n    }\n\n    const renderInputSelect = (config: IConfigFormDataOptionItem, itemProps: Record<string, any>) => {\n        const options: LabeledValue[] = config.value || []\n        const inputSelectOption = options.map(item => (item.value)) as string[]\n        return <Form.Item\n            key={`configFormData_${config.id}`}\n            label={config.label}\n            name={config.id}\n            rules={[\n                {\n                    required: true,\n                    message: `${t('请选择')}${config.label}`,\n                },\n            ]}\n            initialValue={config.defaultValue}\n            style={{ marginBottom: 0 }}\n            {...itemProps}\n        >\n            <InputSearch\n                isOpenSearchMatch\n                onChange={() => {\n                    props.onChange && props.onChange(form.getFieldsValue())\n                }}\n                options={inputSelectOption} width={'500px'} />\n        </Form.Item>\n    }\n\n\n    const dispatchRenderFormItem = (item: IConfigFormDataOptionItem, itemProps: Record<string, any> = {}): JSX.Element | null => {\n        switch (item.type) {\n            case 'input':\n                return renderInput(item, itemProps)\n            case 'select':\n                return renderSelect(item, itemProps)\n            case 'input-select':\n                return renderInputSelect(item, itemProps)\n            default:\n                return null\n        }\n    }\n\n    return (\n        <div className=\"configformdata-container d-f ac\">\n            <Form form={form} component={false}>\n                {\n                    console.log('props.option', props.option)\n                }\n                {\n                    props.option.map((component) => {\n                        return dispatchRenderFormItem(component)\n                    })\n                }\n            </Form>\n        </div>\n    )\n})\n\nexport default ConfigFormData\n", "import Icon, { ExclamationCircleOutlined, MenuOutlined, ReloadOutlined, RightCircleOutlined, SaveOutlined, Slide<PERSON>Outlined, StarOutlined, StopOutlined } from '@ant-design/icons';\nimport { Button, Drawer, message, Modal, Select, Switch, Tabs, Tooltip } from 'antd'\nimport React, { useEffect, useRef, useState } from 'react'\nimport { actionGetDataSearchRes, actionRun, getIdexFormConfig } from '../../api/dataSearchApi';\nimport CodeEdit from '../../components/CodeEdit'\nimport InputSearch from '../../components/InputSearch/InputSearch';\nimport Draggable from 'react-draggable';\nimport './DataSearch.less';\nimport TaskList from './TaskList';\nimport { IEditorTaskItem, IEditorStore, IEditorItem, IEditorItemParams } from './interface';\nimport moment from 'moment'\nimport { data2Time } from '../../util';\n// import LineChartTemplate from '../../components/LineChartTemplate/LineChartTemplate';\nimport LoadingStar from '../../components/LoadingStar/LoadingStar';\nimport cookies from 'js-cookie';\nimport ConfigFormData, { IConfigFormDataOptionItem } from './ConfigFormData';\nimport { useTranslation } from 'react-i18next';\nconst userName = cookies.get('myapp_username')\n\nconst createId = () => {\n    return Math.random().toString(36).substring(2)\n}\n\nconst sqlMap: Record<string, string> = {\n    'test': `CREATE TABLE dbo.EmployeePhoto\n    (\n        EmployeeId INT NOT NULL PRIMARY KEY,\n        Photo VARBINARY(MAX) FILESTREAM NULL,\n        MyRowGuidColumn UNIQUEIDENTIFIER NOT NULL ROWGUIDCOL\n                        UNIQUE DEFAULT NEWID()\n    );\n    \n    GO\n    \n    /*\n    text_of_comment\n    /* nested comment */\n    */\n    \n    -- line comment\n    \n    CREATE NONCLUSTERED INDEX IX_WorkOrder_ProductID\n        ON Production.WorkOrder(ProductID)\n        WITH (FILLFACTOR = 80,\n            PAD_INDEX = ON,\n            DROP_EXISTING = ON);\n    GO\n    \n    WHILE (SELECT AVG(ListPrice) FROM Production.Product) < $300\n    BEGIN\n       UPDATE Production.Product\n          SET ListPrice = ListPrice * 2\n       SELECT MAX(ListPrice) FROM Production.Product\n       IF (SELECT MAX(ListPrice) FROM Production.Product) > $500\n          BREAK\n       ELSE\n          CONTINUE\n    END\n    PRINT 'Too much for the market to bear';\n    \n    MERGE INTO Sales.SalesReason AS [Target]\n    USING (VALUES ('Recommendation','Other'), ('Review', 'Marketing'), ('Internet', 'Promotion'))\n           AS [Source] ([NewName], NewReasonType)\n    ON [Target].[Name] = [Source].[NewName]\n    WHEN MATCHED\n    THEN UPDATE SET ReasonType = [Source].NewReasonType\n    WHEN NOT MATCHED BY TARGET\n    THEN INSERT ([Name], ReasonType) VALUES ([NewName], NewReasonType)\n    OUTPUT $action INTO @SummaryOfChanges;\n    \n    SELECT ProductID, OrderQty, SUM(LineTotal) AS Total\n    FROM Sales.SalesOrderDetail\n    WHERE UnitPrice < $5.00\n    GROUP BY ProductID, OrderQty\n    ORDER BY ProductID, OrderQty\n    OPTION (HASH GROUP, FAST 10);    \n`,\n}\n\nexport default function DataSearch() {\n    const { t, i18n } = useTranslation();\n    const initId = createId()\n    const initCurrentEditorData: IEditorItem = {\n        tabId: initId,\n        title: `${t('新查询')} 1`,\n        status: 'init',\n        smartShow: false,\n        smartContent: '',\n        smartCache: '',\n        smartTimer: undefined,\n        loading: false,\n        taskMap: {}\n    }\n    const initEditorData: Record<string, IEditorItem> = JSON.parse(localStorage.getItem('dataSearch2') || JSON.stringify({\n        [initCurrentEditorData.tabId]: initCurrentEditorData\n    }))\n    const initEditorDataList = Object.entries(initEditorData).reduce((pre: IEditorItem[], [key, value]) => ([...pre, { ...value }]), [])\n\n    const [activeKey, _setActiveKey] = useState<string>(initEditorDataList[0].tabId)\n    const activeKeyRef = useRef(activeKey);\n    const setActiveKey = (data: string): void => {\n        activeKeyRef.current = data;\n        _setActiveKey(data);\n    };\n\n    const [editorStore, _seteditorStore] = useState<IEditorStore>(initEditorData)\n    const editorStoreRef = useRef(editorStore);\n    const seteditorStore = (data: IEditorStore): void => {\n        editorStoreRef.current = data;\n        _seteditorStore(data);\n    };\n\n    const [configOption, _setConfigOption] = useState<IConfigFormDataOptionItem[]>([])\n    const configOptionRef = useRef(configOption);\n    const setConfigOption = (data: IConfigFormDataOptionItem[]): void => {\n        configOptionRef.current = data;\n        _setConfigOption(data);\n    };\n\n    const initialPanes = Object.entries(editorStore).reduce((pre: IEditorItem[], [key, value]) => ([...pre, { ...value }]), [])\n    const [panes, setPanes] = useState(initialPanes);\n    const newTabIndex = useRef(initEditorDataList.length);\n\n    const [columnConfig, setColumnConfig] = useState<any[]>([])\n    const [dataList, setDataList] = useState<any[]>([])\n\n    const configDataComponentRefs: any = useRef(null);\n\n    const setEditorState = (currentState: IEditorItemParams, key?: string) => {\n\n        const targetRes: IEditorStore = {\n            ...editorStoreRef.current\n        }\n        let currentTaskMap = {}\n\n        if (currentState.taskMap) {\n            currentTaskMap = {\n                taskMap: {\n                    ...editorStoreRef.current[activeKey].taskMap,\n                    ...currentState.taskMap\n                }\n            }\n        }\n\n        targetRes[key || activeKey] = {\n            ...targetRes[key || activeKey],\n            ...currentState,\n            ...currentTaskMap\n        }\n\n        localStorage.setItem('dataSearch2', JSON.stringify(targetRes))\n        editorStoreRef.current = targetRes\n        seteditorStore(targetRes)\n    }\n\n    useEffect(() => {\n        getIdexFormConfig().then(res => {\n            const option = res.data.result\n            setConfigOption(option)\n        })\n    }, [])\n\n    useEffect(() => {\n        const targetDom = document.getElementById(\"buttonDrag\")\n        if (targetDom) {\n            drag(targetDom);\n        }\n\n        function drag(obj: any) {\n            obj.onmousedown = function (e: any) {\n                var dir = \"\";  //设置好方向\n                var firstX = e.clientX;  //获取第一次点击的横坐标\n                var firstY = e.clientY;   //获取第一次点击的纵坐标\n                var width = obj.offsetWidth;  //获取到元素的宽度\n                var height = obj.offsetHeight;  //获取到元素的高度\n                var Left = obj.offsetLeft;   //获取到距离左边的距离\n                var Top = obj.offsetTop;   //获取到距离上边的距离\n                //下一步判断方向距离左边的距离+元素的宽度减去自己设定的宽度，只要点击的时候大于在这个区间，他就算右边\n                if (firstX > Left + width - 30) {\n                    dir = \"right\";\n                } else if (firstX < Left + 30) {\n                    dir = \"left\";\n                }\n                if (firstY > Top + height - 30) {\n                    dir = \"down\";\n                } else if (firstY < Top + 30) {\n                    dir = \"top\";\n                }\n                //判断方向结束\n                document.onmousemove = function (e) {\n                    switch (dir) {\n                        case \"right\":\n                            obj.style[\"width\"] = width + (e.clientX - firstX) + \"px\";\n                            break;\n                        case \"left\":\n                            obj.style[\"width\"] = width - (e.clientX - firstX) + \"px\";\n                            obj.style[\"left\"] = Left + (e.clientX - firstX) + \"px\";\n                            break;\n                        case \"top\":\n                            obj.style[\"height\"] = height - (e.clientY - firstY) + \"px\";\n                            obj.style[\"top\"] = Top + (e.clientY - firstY) + \"px\";\n                            break;\n                        case \"down\":\n                            obj.style[\"height\"] = height + (e.clientY - firstY) + \"px\";\n                            break;\n                    }\n                }\n                obj.onmouseup = function () {\n                    document.onmousemove = null;\n                }\n                return false;\n            }\n        }\n    }, [])\n\n    const clearEditorTaskTimerByKey = (activeKey: string) => {\n        const tagList = Object.entries(editorStoreRef.current[activeKey]).reduce((pre: IEditorItem[], [key, value]) => ([...pre, { ...value }]), [])\n        tagList.forEach(tag => {\n            clearInterval(tag.smartTimer)\n        })\n        const currentTaskList = Object.entries(editorStoreRef.current[activeKey].taskMap).reduce((pre: IEditorTaskItem[], [key, value]) => ([...pre, { ...value }]), [])\n        currentTaskList.forEach(task => {\n            clearInterval(task.timer)\n        })\n    }\n\n    const clearTaskTimer = (activeKey: string, taskId: string) => {\n        const taskMap = editorStoreRef.current[activeKey].taskMap\n        const task = taskMap[taskId]\n        if (task) {\n            clearInterval(task.timer)\n        }\n    }\n\n    // 清空定时器\n    useEffect(() => {\n        setEditorState({\n            loading: false\n        })\n\n        return () => {\n            Object.entries(editorStore).forEach((item) => {\n                const [key] = item\n                clearEditorTaskTimerByKey(key)\n            })\n        }\n    }, [])\n\n    useEffect(() => {\n        // 当前tab状态是runing，触发轮询\n        const currentEditorStore = editorStore[activeKey]\n        const currentTaskList = Object.entries(currentEditorStore.taskMap).reduce((pre: IEditorTaskItem[], [key, value]) => ([...pre, { ...value }]), [])\n        currentTaskList.forEach(task => {\n            if (task.status === 'running') {\n                pollGetRes(task.reqId)\n            }\n        })\n    }, [activeKey])\n\n\n    const onChange = (newActiveKey: string) => {\n        Object.entries(editorStore).forEach((item) => {\n            const [key] = item\n            if (key !== newActiveKey) {\n                clearEditorTaskTimerByKey(key)\n            }\n        })\n        setColumnConfig([])\n        setDataList([])\n        setActiveKey(newActiveKey);\n    };\n\n    const add = () => {\n        clearEditorTaskTimerByKey(activeKey)\n\n        const currentIndex = ++newTabIndex.current\n        if (currentIndex > 10) {\n            message.warn(t('标签数目达到限制'))\n        } else {\n            const newActiveKey = createId();\n            const title = `${t('新查询')} ${currentIndex}`\n            const newPanes = [...panes];\n            const initState: IEditorItem = {\n                title,\n                tabId: newActiveKey,\n                status: 'init',\n                smartShow: false,\n                smartContent: '',\n                smartTimer: undefined,\n                smartCache: '',\n                loading: false,\n                taskMap: {}\n            }\n            newPanes.push(initState);\n            setPanes(newPanes);\n            setActiveKey(newActiveKey);\n\n            let res: IEditorStore = {\n                ...editorStore, [newActiveKey]: initState\n            }\n\n            seteditorStore(res)\n            localStorage.setItem('dataSearch2', JSON.stringify(res))\n        }\n    };\n\n    const remove = (targetKey: string) => {\n        let newActiveKey = activeKey;\n        let lastIndex = -1;\n        panes.forEach((pane, i) => {\n            if (pane.tabId === targetKey) {\n                lastIndex = i - 1;\n            }\n        });\n        const newPanes = panes.filter(pane => pane.tabId !== targetKey);\n        if (newPanes.length && newActiveKey === targetKey) {\n            if (lastIndex >= 0) {\n                newActiveKey = newPanes[lastIndex].tabId;\n            } else {\n                newActiveKey = newPanes[0].tabId;\n            }\n        }\n        setPanes(newPanes);\n        setActiveKey(newActiveKey);\n\n        let res = { ...editorStore }\n        delete res[targetKey]\n        seteditorStore(res)\n        localStorage.setItem('dataSearch2', JSON.stringify(res))\n    };\n\n    const onEdit = (targetKey: any, action: 'add' | 'remove') => {\n        if (action === 'add') {\n            add();\n        } else {\n            remove(targetKey);\n        }\n    };\n\n    const fetchData = (task_id: string) => {\n        actionGetDataSearchRes(task_id).then(res => {\n            const { state, result, err_msg, result_url, spark_log_url, stage } = res.data\n            const task: IEditorTaskItem = {\n                ...editorStoreRef.current[activeKey].taskMap[task_id],\n                status: state,\n                step: stage,\n                log: spark_log_url,\n                downloadUrl: result_url,\n                result,\n                message: err_msg\n            }\n            if (state === 'success' || state === 'failure') {\n                const starTime = new Date(task.startTime || '').valueOf()\n                const nowTime = new Date().valueOf()\n                const duration = data2Time((nowTime - starTime) / 1000)\n                task.duration = duration\n\n                setEditorState({\n                    status: 'success',\n                    taskMap: {\n                        [task_id]: task\n                    }\n                })\n                clearTaskTimer(activeKey, task_id)\n            } else {\n                setEditorState({\n                    status: 'success',\n                    taskMap: {\n                        [task_id]: task\n                    }\n                })\n            }\n        }).catch(() => {\n            clearTaskTimer(activeKey, task_id)\n            message.error(t('查询结果失败，尝试重新运行'))\n            setEditorState({\n                status: 'failure',\n                taskMap: {\n                    [task_id]: {\n                        ...editorStoreRef.current[activeKey].taskMap[task_id],\n                        status: 'failure',\n                        step: 'end',\n                    }\n                }\n            })\n        })\n    }\n\n    const pollGetRes = (task_id: string) => {\n        clearTaskTimer(activeKey, task_id)\n\n        let timer = setInterval(() => {\n            fetchData(task_id)\n        }, 5000)\n\n        setEditorState({\n            taskMap: {\n                [task_id]: {\n                    reqId: task_id,\n                    status: 'init',\n                    content: editorStore[activeKey].content,\n                    name: `${t('任务')}${task_id}`,\n                    step: 'start',\n                    startTime: moment().format('YYYY-MM-DD HH:mm:ss'),\n                    database: editorStore[activeKey].database,\n                    table: editorStore[activeKey].table,\n                    timer,\n                    message: ''\n                }\n            }\n        })\n        fetchData(task_id)\n    }\n\n    const runTask = () => {\n        setEditorState({ status: 'running' })\n        const customParams = configOption.map(item => item.id).reduce((pre: any, next: any) => ({ ...pre, [next]: editorStore[activeKey][next] }), {})\n        // 运行子任务\n        actionRun({\n            sql: editorStore[activeKey]?.content || '',\n            ...customParams\n        }).then(res => {\n            const { err_msg, task_id } = res.data\n            if (err_msg) {\n                setEditorState({\n                    status: 'failure',\n                })\n                Modal.error({\n                    title: t('运行失败'),\n                    icon: <ExclamationCircleOutlined />,\n                    width: 1000,\n                    content: err_msg,\n                    okText: t('关闭'),\n                    // maskClosable: true\n                });\n            } else if (task_id) {\n                pollGetRes(task_id)\n            }\n        }).catch(err => {\n            setEditorState({ status: 'failure' })\n        })\n    }\n\n    return (\n        <div className=\"datasearch-container fade-in d-f\">\n            <div className=\"flex1 ptb16 pl16\">\n                <Tabs type=\"editable-card\" onChange={onChange} activeKey={activeKey} onEdit={onEdit}>\n                    {panes.map((pane, index) => (\n                        <Tabs.TabPane tab={`${t('新查询')} ${index + 1}`} key={pane.tabId} closable={index !== 0}>\n                            <div className=\"d-f fd-c h100\">\n                                <div className=\"flex2 s0 ov-a\">\n                                    {\n                                        editorStore[activeKey]?.loading ? <div className=\"codeedit-mark\">\n                                            <div className=\"d-f jc ac fd-c\">\n                                                <LoadingStar />\n                                                <div>\n                                                    {t('结果生成中')}\n                                                </div>\n                                            </div>\n                                        </div> : null\n                                    }\n\n                                    <CodeEdit\n                                        value={editorStore[activeKey]?.content}\n                                        onChange={(value: any) => {\n                                            setEditorState({\n                                                content: value === '' ? undefined : value,\n                                                title: pane.title,\n                                            })\n                                        }} />\n                                </div>\n\n                                <div className=\"ov-h\" id=\"showBox\" style={{ height: 500 }}>\n                                    <Draggable\n                                        axis=\"y\"\n                                        onStart={() => { }}\n                                        onDrag={(e: any) => {\n                                            const showBoxDom = document.getElementById('showBox')\n                                            if (showBoxDom) {\n                                                const res = document.body.clientHeight - e.y\n                                                showBoxDom.style.height = `${res}px`\n                                            }\n                                        }}\n                                        onStop={() => { }}>\n                                        <div className=\"ta-c\" style={{ cursor: 'ns-resize' }}><MenuOutlined /></div>\n                                    </Draggable>\n                                    <div className=\"ptb8 plr16 bor-l bor-r b-side d-f ac jc-b bg-w\">\n                                        <div className=\"d-f ac\">\n                                            <Switch className=\"mr8\"\n                                                checked={editorStore[activeKey].smartShow}\n                                                unCheckedChildren={t('正常模式')}\n                                                checkedChildren={t('智能模式')} onChange={(checked) => {\n                                                    setEditorState({ smartShow: checked })\n                                                }} />\n                                            {\n                                                editorStore[activeKey].smartShow ? <InputSearch\n                                                    value={editorStore[activeKey].smartContent}\n                                                    isOpenSearchMatch\n                                                    onChange={(value: any) => {\n                                                        setEditorState({\n                                                            smartContent: value,\n                                                        })\n                                                    }}\n                                                    onSearch={(value) => {\n                                                        setEditorState({\n                                                            smartCache: sqlMap[value],\n                                                            loading: true,\n                                                        })\n\n                                                        const timer = setInterval(() => {\n                                                            const currentContent = editorStoreRef.current[activeKey].content || ''\n                                                            if (editorStoreRef.current[activeKey].smartCache) {\n                                                                let smartCache = editorStoreRef.current[activeKey].smartCache || ''\n                                                                const tarStr = smartCache.substr(0, 20)\n                                                                smartCache = smartCache.replace(tarStr, '')\n\n                                                                setEditorState({\n                                                                    smartCache,\n                                                                    content: currentContent + tarStr\n                                                                })\n                                                            } else {\n                                                                clearInterval(editorStoreRef.current[activeKey].smartTimer)\n                                                                setEditorState({\n                                                                    smartCache: '',\n                                                                    smartTimer: undefined,\n                                                                    loading: false,\n                                                                })\n                                                            }\n                                                        }, 800)\n\n                                                        setEditorState({\n                                                            smartTimer: timer,\n                                                        })\n                                                    }}\n                                                    options={[\n                                                        'test',\n                                                    ]} placeholder={t('AI智能生成')} width={'240px'} /> : null\n                                            }\n                                        </div>\n                                        <div className=\"d-f ac\">\n                                            <ConfigFormData\n                                                ref={configDataComponentRefs}\n                                                dataValue={editorStore[activeKey]}\n                                                onChange={(dataValue) => {\n                                                    setEditorState(dataValue)\n                                                }}\n                                                onConfigChange={(option) => {\n                                                    setConfigOption(option)\n                                                    setEditorState({\n                                                        database: 'db'\n                                                    })\n                                                }}\n                                                option={configOptionRef.current} />\n                                            <Button className=\"ml16\" type=\"primary\" loading={editorStore[activeKey].status === 'running'} onClick={() => {\n                                                configDataComponentRefs.current.onSubmit().then((res: any) => {\n                                                    runTask()\n                                                })\n                                            }}>{t('运行')}<RightCircleOutlined /></Button>\n                                        </div>\n                                    </div>\n                                    <div className=\"flex1 bor b-side s0 bg-w p-r ov-a\" style={{ height: 'calc(100% - 80px)' }}>\n                                        <div className=\"pt8\">\n                                            <div className=\"tag-result bg-theme c-text-w mr16\">\n                                                {t('结果')}\n                                            </div>\n                                        </div>\n                                        <div className=\"plr16 pt8\">\n                                            <TaskList\n                                                option={editorStore[activeKey].taskMap}\n                                                onDelete={(id) => {\n                                                    Modal.confirm({\n                                                        title: t('删除'),\n                                                        icon: <ExclamationCircleOutlined />,\n                                                        content: `${t('确定删除')}?`,\n                                                        okText: t('确认删除'),\n                                                        cancelText: t('取消'),\n                                                        okButtonProps: { danger: true },\n                                                        onOk() {\n                                                            let taskMap = editorStore[activeKey].taskMap\n                                                            clearTaskTimer(activeKey, id)\n                                                            delete taskMap[id]\n                                                            setEditorState({\n                                                                taskMap\n                                                            })\n                                                        },\n                                                        onCancel() { },\n                                                    });\n                                                }}\n                                                onRetry={(id) => {\n                                                    pollGetRes(id)\n                                                }}\n                                            />\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </Tabs.TabPane>\n                    ))}\n                </Tabs>\n            </div>\n        </div>\n    )\n}\n"], "names": ["InputSearch", "props", "inputRef", "id", "Math", "random", "toString", "substring", "useState", "options", "dataCache", "setDataCache", "value", "setValue", "useEffect", "dataFilter", "isOpenSearchMatch", "filter", "item", "indexOf", "handleChange", "onChange", "highlight<PERSON>ey<PERSON>ord", "key<PERSON>ord", "index", "preStr", "nextStr", "length", "className", "debounceScroll", "fun", "timer", "time", "args", "clearTimeout", "setTimeout", "apply", "debounce", "onScrollButtom", "labelName", "htmlFor", "style", "width", "disabled", "placeholder", "max<PERSON><PERSON><PERSON>", "e", "target", "onKeyPress", "nativeEvent", "keyCode", "blur", "onSearch", "currentTarget", "ref", "element", "SearchOutlined", "onScroll", "stopPropagation", "scrollTop", "clientHeight", "scrollHeight", "maxHeight", "loading", "right", "top", "map", "onMouseDown", "onClick", "handleClick", "CopyToClipboard", "require", "ResizableTitle", "onResize", "restProps", "Resizable", "height", "handle", "draggableOpts", "enableUserSelectHack", "userSelect", "exportDataVisible", "setExportDataVisible", "header", "data", "dataFormat", "setDataFormat", "filterValue", "setFilterValue", "columns", "cols", "setCols", "handleResize", "_", "size", "temp", "tableWidth", "reduce", "pre", "next", "localStorage", "setItem", "table<PERSON><PERSON>", "JSON", "stringify", "setCurrentTableScroll", "currentTableScroll", "x", "customColumns", "col", "onHeaderCell", "column", "scroll", "useTranslation", "t", "i18n", "dataSource", "dataIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataColumns", "sheetHeader", "title", "tarData", "for<PERSON>ach", "dataRow", "row", "colName", "res", "push", "handleExportJira", "str", "join", "<PERSON><PERSON><PERSON>", "Object", "values", "handleExportText", "direction", "maskClosable", "centered", "bodyStyle", "overflow", "visible", "onCancel", "footer", "position", "label", "defaultValue", "bottom", "type", "tab", "text", "onCopy", "message", "cursor", "minHeight", "titleNode", "buttonNode", "cancelExportData", "justify", "align", "marginLeft", "renderEmpty", "flexDirection", "src", "emptyImg", "alt", "components", "cell", "pagination", "rowSelection", "actionRun", "params", "axios", "actionGetDataSearchRes", "task_id", "getIdexTaskDownloadUrl", "separator", "getIdexTaskResult", "stopIndxTask", "getIdexFormConfig", "CodeEdit", "theme", "bbedit", "extensions", "sql", "zebraStripes", "step", "lightColor", "darkColor", "stepMap", "statusIconMap", "status", "LoadingOutlined", "CloseCircleOutlined", "color", "QuestionCircleOutlined", "TaskList", "visibleDownload", "setVisibleDownload", "setVisibleResult", "setLoadingResult", "_setSeparator", "separatorRef", "useRef", "setSeparator", "current", "currentReqId", "_setCurrentReqId", "currentReqIdRef", "handleResultData", "result", "targetData", "slice", "config", "key", "destroyOnClose", "then", "window", "open", "download_url", "catch", "err", "console", "log", "defaultActiveKey", "entries", "option", "reverse", "reqId", "extra", "onDelete", "DeleteOutlined", "onRetry", "RedoOutlined", "Step", "icon", "TableBox", "record", "render", "placement", "Modal", "okText", "content", "span", "startTime", "duration", "onOk", "StopOutlined", "cancelText", "Promise", "resolve", "reject", "handleData", "finally", "database", "table", "startime", "ConfigFormData", "React", "Form", "form", "useImperativeHandle", "onSubmit", "validateFields", "setData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataValue", "dispatchRenderFormItem", "itemProps", "name", "rules", "required", "initialValue", "marginBottom", "marginRight", "mode", "multiple", "undefined", "showSearch", "disable", "optionFilterProp", "placeHolder", "rowOption", "relate", "relateId", "relateOption", "currentOption", "i", "onConfigChange", "getFieldsValue", "renderSelect", "inputSelectOption", "renderInputSelect", "component", "createId", "cookies", "sqlMap", "DataSearch", "initCurrentEditorData", "tabId", "smartShow", "smartContent", "smartCache", "smartTimer", "taskMap", "initEditorData", "parse", "getItem", "initEditorDataList", "active<PERSON><PERSON>", "_setActiveKey", "activeKeyRef", "setActiveKey", "editorStore", "_seteditorStore", "editorStoreRef", "seteditorStore", "configOption", "_setConfigOption", "configOptionRef", "setConfigOption", "initialPanes", "panes", "set<PERSON>anes", "newTabIndex", "setColumnConfig", "setDataList", "configDataComponentRefs", "setEditorState", "currentState", "targetRes", "currentTaskMap", "obj", "targetDom", "document", "getElementById", "onmousedown", "dir", "firstX", "clientX", "firstY", "clientY", "offsetWidth", "offsetHeight", "Left", "offsetLeft", "Top", "offsetTop", "<PERSON><PERSON><PERSON><PERSON>", "onmouseup", "clearEditorTaskTimerByKey", "tag", "clearInterval", "task", "clearTaskTimer", "taskId", "currentEditorStore", "pollGetRes", "fetchData", "state", "err_msg", "result_url", "spark_log_url", "stage", "downloadUrl", "starTime", "Date", "valueOf", "nowTime", "data2Time", "setInterval", "moment", "format", "newActiveKey", "onEdit", "<PERSON><PERSON><PERSON>", "action", "currentIndex", "newPanes", "initState", "add", "lastIndex", "pane", "remove", "closable", "LoadingStar", "axis", "onStart", "onDrag", "showBoxDom", "body", "y", "onStop", "MenuOutlined", "checked", "unChecked<PERSON><PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentC<PERSON>nt", "tarStr", "substr", "replace", "customParams", "ExclamationCircleOutlined", "runTask", "RightCircleOutlined", "okButtonProps", "danger"], "sourceRoot": ""}