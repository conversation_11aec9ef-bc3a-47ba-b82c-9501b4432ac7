{"name": "vite-ml-platform", "version": "0.1.0", "license": "UNLICENSED", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@antv/g6": "^4.6.4", "@fluentui/react": "^8.5.1", "@reduxjs/toolkit": "^1.5.1", "@typescript-eslint/parser": "^5.10.0", "antd": "4.17.4", "array-move": "^4.0.0", "axios": "^0.21.1", "babel-plugin-import": "^1.13.3", "cookie": "^0.4.1", "customize-cra": "^1.0.0", "eslint-plugin-react": "^7.28.0", "http-proxy-middleware": "^2.0.6", "i18next": "^23.6.0", "i18next-browser-languagedetector": "^7.1.0", "less": "^4.1.2", "less-loader": "^10.2.0", "lodash": "^4.17.21", "marked": "^15.0.3", "moment": "^2.29.1", "rc-resize-observer": "^1.4.0", "react": "^17.0.0", "react-copy-to-clipboard": "^5.0.4", "react-dom": "^17.0.0", "react-flow-renderer": "^9.4.0", "react-i18next": "^13.3.1", "react-json-view": "^1.21.3", "react-monaco-editor": "^0.44.0", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "react-sortable-hoc": "^2.0.0", "react-window": "^1.8.7", "xgplayer": "^2.28.0"}, "scripts": {"dev": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "prettier": "prettier --write '**/*.{tsx,ts,less}'"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/cookie": "^0.4.0", "@types/react": "^17.0.0", "@types/react-copy-to-clipboard": "^5.0.2", "@types/react-dom": "^17.0.0", "@types/react-redux": "^7.1.16", "@types/react-router-dom": "^5.1.7", "@types/react-window": "^1.8.5", "@typescript-eslint/eslint-plugin": "^5.40.0", "eslint": "^8.25.0", "monaco-editor-webpack-plugin": "^4.1.1", "prettier": "^2.2.1", "react-app-rewired": "^2.1.9", "react-scripts": "^5.0.0", "typescript": "^4.5.4", "web-vitals": "^1.0.1", "webpack-bundle-analyzer": "^4.4.2"}, "homepage": "./"}