{"数据接入": {"project_name": "数据接入", "image_name": "hub.tiduyun.com:5000/cube-studio/importer:v1", "gitpath": "", "image_describe": "hub.tiduyun.com:5000/cube-studio/importer:v1", "job_template_name": "数据接入", "job_template_describe": "接入数据平台数据", "job_template_workdir": "/home", "job_template_command": "python importer.py", "job_template_expand": {"index": 1, "rec_job_template": "数据接入"}, "job_template_args": {"数据接入": {"--dataset_url": {"type": "str", "item_type": "str", "label": "数据原始路径", "require": 1, "choice": [], "range": "", "default": "public/mnist.pkl.gz", "placeholder": "", "describe": "数据原始路径", "editable": 1}, "--dataset_id": {"type": "str", "item_type": "str", "label": "数据集ID", "require": 1, "choice": [], "range": "", "default": "11111111111111111", "placeholder": "", "describe": "数据集ID", "editable": 1}, "--dataset_version": {"type": "str", "item_type": "str", "label": "数据集版本", "require": 1, "choice": [], "range": "", "default": "1.1.1", "placeholder": "", "describe": "数据集版本", "editable": 1}, "--s3_endpoint": {"type": "str", "item_type": "str", "label": "数据集来源", "require": 1, "choice": [], "range": "", "default": "http://hub.tiduyun.com:8999", "placeholder": "", "describe": "数据集来源", "editable": 1}, "--s3_access_key": {"type": "str", "item_type": "str", "label": "s3 access key", "require": 1, "choice": [], "range": "", "default": "juicefs", "placeholder": "", "describe": "3 access key", "editable": 1}, "--s3_secret_key": {"type": "str", "item_type": "str", "label": "s3 secret key", "require": 1, "choice": [], "range": "", "default": "tiduJuicefs123", "placeholder": "", "describe": "s3 secret key", "editable": 1}, "--s3_bucket": {"type": "str", "item_type": "str", "label": "s3 bucket", "require": 1, "choice": [], "range": "", "default": "juice", "placeholder": "", "describe": "s3 bucket", "editable": 1}}}}, "数据生成": {"project_name": "数据生成", "image_name": "python:3.9", "gitpath": "", "image_describe": "python3.9", "job_template_name": "数据处理", "job_template_describe": "处理数据平台数据", "job_template_expand": {"index": 1, "rec_job_template": "数据处理"}, "job_template_args": {"数据生成": {"--dataset_url": {"type": "str", "item_type": "str", "label": "数据原始路径", "require": 1, "choice": [], "range": "", "default": "value1", "placeholder": "", "describe": "数据原始路径", "editable": 1}, "--dataset_id": {"type": "str", "item_type": "str", "label": "数据集ID", "require": 1, "choice": [], "range": "", "default": "value1", "placeholder": "", "describe": "数据集ID", "editable": 1}, "--dataset_version": {"type": "str", "item_type": "str", "label": "数据集版本", "require": 1, "choice": [], "range": "", "default": "value1", "placeholder": "", "describe": "数据集版本", "editable": 1}}}}, "算法训练": {"project_name": "算法训练", "image_name": "hub.tiduyun.com:5000/cube-studio/train:v2", "gitpath": "", "image_describe": "hub.tiduyun.com:5000/cube-studio/train:v2", "job_template_name": "算法训练", "job_template_describe": "算法训练", "job_template_workdir": "/home", "job_template_command": "python train.py", "job_template_env": "CLEARML_API_HOST=http://hub.tiduyun.com:30008\nCLEARML_WEB_HOST=http://hub.tiduyun.com:38080\nPYTHONUNBUFFERED=\"1\"\nCLEARML_QUEUE=default\nCLEARML_API_ACCESS_KEY=EGRTCO8JMSIGI6S39GTP43NFWXDQOW\nCLEARML_API_SECRET_KEY=x!XTov_G-#vspE*Y(h$Anm&DIc5Ou-F)jsl$PdOyj5wG1&E!Z8", "job_template_expand": {"index": 1, "rec_job_template": "算法训练"}, "job_template_args": {"算法训练": {"--algo_id": {"type": "str", "item_type": "str", "label": "算法ID", "require": 1, "choice": [], "range": "", "default": "0000000011111111", "placeholder": "", "describe": "选用算法ID", "editable": 1}, "--algo_version": {"type": "str", "item_type": "str", "label": "算法版本", "require": 1, "choice": [], "range": "", "default": "1.1.1", "placeholder": "", "describe": "选用算法版本", "editable": 1}, "--cmd": {"type": "str", "item_type": "str", "label": "运行命令行", "require": 1, "choice": [], "range": "", "default": "python keras_simple.py", "placeholder": "", "describe": "运行命令行", "editable": 1}, "--algo_param": {"type": "str", "item_type": "str", "label": "算法参数", "require": 1, "choice": [], "range": "", "default": "[{\"name\":\"layer_1\",  \"min\":128, \"max\":512, \"step\":256, \"value\":128 },{\"name\":\"layer_2\",  \"min\":128, \"max\":512, \"step\":256, \"value\":128 }]", "placeholder": "", "describe": "算法参数", "editable": 1}, "--algo_object_metric_name": {"type": "str", "item_type": "str", "label": "超参目标指标", "require": 1, "choice": [], "range": "", "default": "accuracy", "placeholder": "", "describe": "超参目标指标", "editable": 1}, "--dataset_id": {"type": "str", "item_type": "str", "label": "数据集ID", "require": 1, "choice": [], "range": "", "default": "11111111111111111", "placeholder": "", "describe": "选用数据集ID", "editable": 1}, "--dataset_version": {"type": "str", "item_type": "str", "label": "数据集版本", "require": 1, "choice": [], "range": "", "default": "1.1.1", "placeholder": "", "describe": "选用数据集版本", "editable": 1}, "--image": {"type": "str", "item_type": "str", "label": "算法镜像", "require": 1, "choice": [], "range": "", "default": "hub.tiduyun.com:5000/clearml/executor:1.4.1-keras", "placeholder": "", "describe": "算法镜像", "editable": 1}}}}, "算法评估": {"project_name": "算法训练", "image_name": "hub.tiduyun.com:5000/cube-studio/evaluator:v1", "gitpath": "", "image_describe": "hub.tiduyun.com:5000/cube-studio/evaluator:v1", "job_template_name": "算法评估", "job_template_describe": "算法评估", "job_template_workdir": "/home", "job_template_command": "python evaluator.py", "job_template_env": "CLEARML_API_HOST=http://hub.tiduyun.com:30008", "job_template_expand": {"index": 1, "rec_job_template": "算法评估"}, "job_template_args": {"算法评估": {"--metric_name": {"type": "str", "item_type": "str", "label": "评估指标名", "require": 1, "choice": [], "range": "", "default": "epoch_accuracy", "placeholder": "", "describe": "评估指标名", "editable": 1}}}}}