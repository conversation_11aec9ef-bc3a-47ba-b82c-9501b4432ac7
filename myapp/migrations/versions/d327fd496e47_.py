"""empty message

Revision ID: d327fd496e47
Revises: 109a8e8dca95
Create Date: 2022-02-26 13:03:54.286807

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'd327fd496e47'
down_revision = '109a8e8dca95'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('inferenceservice',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=True),
    sa.Column('label', sa.String(length=100), nullable=False),
    sa.Column('service_type', sa.String(length=100), nullable=True),
    sa.Column('model_name', sa.String(length=200), nullable=True),
    sa.Column('model_version', sa.String(length=200), nullable=True),
    sa.Column('model_path', sa.String(length=200), nullable=True),
    sa.Column('model_type', sa.String(length=200), nullable=True),
    sa.Column('model_input', sa.Text(length=65536), nullable=True),
    sa.Column('model_output', sa.Text(length=65536), nullable=True),
    sa.Column('model_status', sa.String(length=200), nullable=True),
    sa.Column('transformer', sa.String(length=200), nullable=True),
    sa.Column('images', sa.String(length=200), nullable=False),
    sa.Column('working_dir', sa.String(length=100), nullable=True),
    sa.Column('command', sa.String(length=1000), nullable=True),
    sa.Column('args', sa.Text(), nullable=True),
    sa.Column('env', sa.Text(), nullable=True),
    sa.Column('volume_mount', sa.String(length=200), nullable=True),
    sa.Column('node_selector', sa.String(length=100), nullable=True),
    sa.Column('min_replicas', sa.Integer(), nullable=True),
    sa.Column('max_replicas', sa.Integer(), nullable=True),
    sa.Column('hpa', sa.String(length=400), nullable=True),
    sa.Column('metrics', sa.Text(length=65536), nullable=True),
    sa.Column('health', sa.String(length=400), nullable=True),
    sa.Column('sidecar', sa.String(length=400), nullable=True),
    sa.Column('ports', sa.String(length=100), nullable=True),
    sa.Column('resource_memory', sa.String(length=100), nullable=True),
    sa.Column('resource_cpu', sa.String(length=100), nullable=True),
    sa.Column('resource_gpu', sa.String(length=100), nullable=True),
    sa.Column('deploy_time', sa.String(length=100), nullable=True),
    sa.Column('host', sa.String(length=200), nullable=True),
    sa.Column('expand', sa.Text(length=65536), nullable=True),
    sa.Column('canary', sa.String(length=400), nullable=True),
    sa.Column('shadow', sa.String(length=400), nullable=True),
    sa.Column('run_id', sa.String(length=100), nullable=True),
    sa.Column('run_time', sa.String(length=100), nullable=True),
    sa.Column('deploy_history', sa.Text(length=65536), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('service_pipeline',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('describe', sa.String(length=200), nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('dag_json', sa.Text(), nullable=False),
    sa.Column('namespace', sa.String(length=100), nullable=True),
    sa.Column('env', sa.String(length=500), nullable=True),
    sa.Column('run_id', sa.String(length=100), nullable=True),
    sa.Column('node_selector', sa.String(length=100), nullable=True),
    sa.Column('images', sa.String(length=200), nullable=False),
    sa.Column('working_dir', sa.String(length=100), nullable=True),
    sa.Column('command', sa.String(length=1000), nullable=True),
    sa.Column('volume_mount', sa.String(length=200), nullable=True),
    sa.Column('image_pull_policy', sa.Enum('Always', 'IfNotPresent'), nullable=False),
    sa.Column('replicas', sa.Integer(), nullable=True),
    sa.Column('resource_memory', sa.String(length=100), nullable=True),
    sa.Column('resource_cpu', sa.String(length=100), nullable=True),
    sa.Column('resource_gpu', sa.String(length=100), nullable=True),
    sa.Column('parallelism', sa.Integer(), nullable=False),
    sa.Column('alert_status', sa.String(length=100), nullable=True),
    sa.Column('alert_user', sa.String(length=300), nullable=True),
    sa.Column('expand', sa.Text(length=65536), nullable=True),
    sa.Column('parameter', sa.Text(length=65536), nullable=True),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.add_column('service', sa.Column('replicas', sa.Integer(), nullable=True))
    op.drop_column('service', 'min_replicas')
    op.drop_column('service', 'max_replicas')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service', sa.Column('max_replicas', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('service', sa.Column('min_replicas', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.drop_column('service', 'replicas')
    op.drop_table('service_pipeline')
    op.drop_table('inferenceservice')
    # ### end Alembic commands ###
