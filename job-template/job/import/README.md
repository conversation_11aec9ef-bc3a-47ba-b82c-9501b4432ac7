## 数据接入组件

### 1、构建镜像：
docker build -f import/Dockerfile -t hub.tiduyun.com:5000/cube-studio/importer:v2

### 2、组件配置
#### 1）启动参数
{
    "数据接入": {
        "--dataset_url": {
            "type": "str",
            "item_type": "str",
            "label": "数据原始路径",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "public/mnist.pkl.gz",
            "placeholder": "",
            "describe": "数据原始路径",
            "editable": 1
        },
        "--dataset_id": {
            "type": "str",
            "item_type": "str",
            "label": "数据集ID",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "11111111111111111",
            "placeholder": "",
            "describe": "数据集ID",
            "editable": 1
        },
        "--dataset_version": {
            "type": "str",
            "item_type": "str",
            "label": "数据集版本",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "1.1.1",
            "placeholder": "",
            "describe": "数据集版本",
            "editable": 1
        },
        "--s3_endpoint": {
            "type": "str",
            "item_type": "str",
            "label": "数据集来源",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "http://hub.tiduyun.com:8999",
            "placeholder": "",
            "describe": "数据集来源",
            "editable": 1
        },
        "--s3_access_key": {
            "type": "str",
            "item_type": "str",
            "label": "s3 access key",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "juicefs",
            "placeholder": "",
            "describe": "3 access key",
            "editable": 1
        },
        "--s3_secret_key": {
            "type": "str",
            "item_type": "str",
            "label": "s3 secret key",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "tiduJuicefs123",
            "placeholder": "",
            "describe": "s3 secret key",
            "editable": 1
        },
        "--s3_bucket": {
            "type": "str",
            "item_type": "str",
            "label": "s3 bucket",
            "require": 1,
            "choice": [],
            "range": "",
            "default": "juice",
            "placeholder": "",
            "describe": "s3 bucket",
            "editable": 1
        }
    }
}

#### 2）启动参数
python importer.py

#### 3）环境变量
无