import requests
import shutil
import argparse
from urllib.parse import urlparse
import os
import boto3
from botocore.client import Config

# ---------- 第一步：下载文件 TODO:适配客户的HDFS ----------
def download_file(url, local_filename):
    with requests.get(url, stream=True) as response:
        response.raise_for_status()
        with open(local_filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
    print(f"✅ 文件已下载: {local_filename}")

# ---------- 第二步：上传文件 到数据集PVC(后续走PVC的方式直接挂载即可) ----------
def upload_to_pvc(file_path, dataset_id, dataset_version):
    target_dir = f"dataset/{dataset_id}/{dataset_version}/"
    os.makedirs(target_dir, exist_ok=True)
    target_path = os.path.join(target_dir, os.path.basename(file_path))
    shutil.move(file_path, target_path)

def upload_to_s3(endpoint_url, access_key, secret_key, bucket_name, object_name, file_path):
    # 创建 S3 客户端，使用自定义 endpoint
    s3 = boto3.client(
        's3',
        endpoint_url=endpoint_url,
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        config=Config(signature_version='s3v4'),
        region_name='us-east-1'  # 可自定义，MinIO 可接受任意值
    )

    try:
        s3.upload_file(file_path, bucket_name, object_name)
        print(f"✅ 文件已上传至 {bucket_name}/{object_name}")
    except Exception as e:
        print(f"❌ 上传失败: {e}")


# ---------- 主程序 ----------
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="算法训练配置")
    parser.add_argument('--dataset_url', type=str, required=True, help="数据来源")
    parser.add_argument('--dataset_id', type=str, required=True, help="数据集ID")
    parser.add_argument('--dataset_version', type=str, required=True, help="数据集版本")

    args = parser.parse_args()

    parsed_url = urlparse(args.dataset_url)
    local_file = os.path.basename(parsed_url.path)

    # 下载文件
    download_file(args.dataset_url, local_file)

    # 上传到PVC(已挂载到 /data路径)  cubestudio和clearml不在一个集群，先用s3
    # upload_to_pvc(local_file, )

    s3_config = {
        "endpoint_url": "http://************:8999",
        "access_key": "juicefs",
        "secret_key": "tiduJuicefs123",
        "bucket_name": "juice",
        "object_name": f"tiduai-tiduai-dataset-pvc/{args.dataset_id}/{args.dataset_version}/mnist.pkl.gz",  # 上传后对象名称
        "file_path": local_file
    }
    upload_to_s3(**s3_config)