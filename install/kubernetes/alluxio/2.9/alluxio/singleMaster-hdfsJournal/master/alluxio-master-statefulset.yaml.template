---
# Source: alluxio/templates/master/statefulset.yaml
#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: alluxio-master
  labels:
    name: alluxio-master
    app: alluxio
    chart: alluxio-0.6.53
    release: alluxio
    heritage: Helm
    role: alluxio-master
spec:
  selector:
    matchLabels:
      app: alluxio
      role: alluxio-master
      name: alluxio-master
  serviceName: alluxio-master
  podManagementPolicy: Parallel
  replicas: 1
  template:
    metadata:
      labels:
        name: alluxio-master
        app: alluxio
        chart: alluxio-0.6.53
        release: alluxio
        heritage: Helm
        role: alluxio-master
    spec:
      
      hostNetwork: false
      dnsPolicy: ClusterFirst
      nodeSelector:
      tolerations:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      initContainers:
      shareProcessNamespace: false
      containers:
        - name: alluxio-master
          image: alluxio/alluxio:2.9.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 4
              memory: 8Gi
            requests:
              cpu: 1
              memory: 1Gi
          command: ["tini", "--", "/entrypoint.sh"]
          args:
            - master-only
            - --no-format
          env:
          - name: ALLUXIO_MASTER_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          envFrom:
          - configMapRef:
              name: alluxio-config
          readinessProbe:
            tcpSocket:
              port: rpc
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 3
            successThreshold: 1
          livenessProbe:
            tcpSocket:
              port: rpc
            initialDelaySeconds: 15
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 2
          ports:
          - containerPort: 19998
            name: rpc
          - containerPort: 19999
            name: web
          volumeMounts:
            - name: secret-alluxio-hdfs-config-volume
              mountPath: /secrets/hdfsConfig
              readOnly: true
        - name: alluxio-job-master
          image: alluxio/alluxio:2.9.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 4
              memory: 8Gi
            requests:
              cpu: 1
              memory: 1Gi
          command: ["tini", "--", "/entrypoint.sh"]
          args:
            - job-master
          env:
          - name: ALLUXIO_MASTER_HOSTNAME
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          envFrom:
          - configMapRef:
              name: alluxio-config
          readinessProbe:
            tcpSocket:
              port: job-rpc
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 3
            successThreshold: 1
          livenessProbe:
            tcpSocket:
              port: job-rpc
            initialDelaySeconds: 15
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 2
          ports:
          - containerPort: 20001
            name: job-rpc
          - containerPort: 20002
            name: job-web
          volumeMounts:
            - name: secret-alluxio-hdfs-config-volume
              mountPath: /secrets/hdfsConfig
              readOnly: true
      restartPolicy: Always
      volumes:
        - name: secret-alluxio-hdfs-config-volume
          secret:
            secretName: alluxio-hdfs-config
            defaultMode: 256
  volumeClaimTemplates:
