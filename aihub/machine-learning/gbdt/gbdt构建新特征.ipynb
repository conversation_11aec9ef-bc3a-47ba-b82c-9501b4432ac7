{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Gradient Boosting是一种Boosting的方法，它主要的思想是，每一次建立模型是在之前建立模型损失函数的梯度下降方向。\n", "\n", "损失函数是评价模型性能（一般为拟合程度+正则项），认为损失函数越小，性能越好。\n", "\n", "而让损失函数持续下降，就能使得模型不断改性提升性能，其最好的方法就是使损失函数沿着梯度方向下降（讲道理梯度方向上下降最快）。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GBDT构建新的特征思想\n", "------------\n", "\n", "特征决定模型性能上界，例如深度学习方法也是将数据如何更好的表达为特征。\n", "\n", "如果能够将数据表达成为线性可分的数据，那么使用简单的线性模型就可以取得很好的效果。GBDT构建新的特征也是使特征更好地表达数据。\n", "\n", "> 主要思想：GBDT每棵树的路径所代表的特征组合直接作为LR的输入特征使用。\n", "> \n", "> 用已有特征训练GBDT模型，然后利用GBDT模型学习到的树来构造新特征，最后把这些新特征加入原有特征一起训练模型。\n", "\n", "> 构造的新特征向量是取值0/1的，向量的每个元素对应于GBDT模型中树的叶子结点。\n", ">\n", "> 当一个样本点通过某棵树最终落在这棵树的一个叶子结点上，那么在新特征向量中这个叶子结点对应的元素值为1，而这棵树的其他叶子结点对应的元素值为0。新特征向量的长度等于GBDT模型里所有树包含的叶子结点数之和。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![x](https://img-blog.csdnimg.cn/2e94dc27e3de4cfe94aef84985961ac3.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如上图，假设GBDT使用了2个决策树作为弱学习器。\n", "\n", "其中第一颗树有3个叶子节点$l^1_1,l^2_1,l^3_1$，第二颗树有2个叶子节点$l^1_2,l^2_2$，那么我们就为样本生成一个5维的新特征。\n", "\n", "若样本在第一颗树中属于叶子1，在第二颗树中属于叶子2，则该样本在新特征中的值为[1,0,0,0,1]，在第1个维度和第5个维度上有值，其他维度没有值。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**含义：决策树中的叶子节点代表了样本具有某种属性组合，这种属性组合由于很有意义所以才会存在决策树中（不然就会被剪支掉）。**\n", "\n", "**比如广告点击中广告属性（国家、节日）这种特征，（中国-春节）和（美国-感恩节）是有意义的特征组合。**\n", "\n", "**我们将所有有意义的特征组合提取出来作为一个新的特征，这就是gbdt构建新特征的目的。**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["因为每种有意义的特征组合，这种组合的意义有多大是不知道的，所以需要重新将特征组合作为新特征来训练模型。\n", "\n", "训练出每种特征组合的意义权重，进而对样本包含的多重有效组合进行计算结果。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["用下面的例子更好理解一些，比如现在知道一批有意义的特征组合。其中$x^i_j$表示第j个特征取第i个该特征的可取值。\n", "\n", "| 特征取值组合 | 组合有效性|\n", "|--|--|\n", "| $x^1_1$、$x^1_2$、$x^1_3$ | $w_1$ |\n", "| $x^1_1$、$x^3_2$ | $w_2$ |\n", "| $x^2_1$、$x^3_3$ | $w_3$ |\n", "| $x^4_1$、$x^6_2$ | $w_4$ |\n", "\n", "GBDT的叶子节点就代表有效的特征组合。我们需要求的就是$w_1$、$w_2$、$w_3$、$w_4$。这个可以用lr或者fm求解。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GBDT与LR融合案例\n", "--\n", "\n", "所以这个过程我们一般需要将数据划分为3个。gbdt特征组合提取数据集，lr特征组合有效性系数训练数据集，测试集。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每个样本在每个树中所属的叶子索引\n", " [[ 4.  4.  4. ...  4.  3.  4.]\n", " [ 6.  6.  6. ...  6.  6.  6.]\n", " [ 6.  6.  6. ...  6.  6.  6.]\n", " ...\n", " [14. 14. 14. ... 14. 14. 14.]\n", " [ 4.  4.  4. ...  4.  4. 13.]\n", " [ 6.  6.  6. ...  6.  6.  6.]]\n", "使用逻辑回归训练GBDT组合特征的结果\n", "auc值为\n", " 0.9592938439659342\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.6/dist-packages/sklearn/linear_model/_logistic.py:764: ConvergenceWarning: lbfgs failed to converge (status=1):\n", "STOP: TOTAL NO. of ITERATIONS REACHED LIMIT.\n", "\n", "Increase the number of iterations (max_iter) or scale the data as shown in:\n", "    https://scikit-learn.org/stable/modules/preprocessing.html\n", "Please also refer to the documentation for alternative solver options:\n", "    https://scikit-learn.org/stable/modules/linear_model.html#logistic-regression\n", "  extra_warning_msg=_LOGISTIC_SOLVER_CONVERGENCE_MSG)\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["使用逻辑回归训练原始数据集的结果\n", "auc值为\n", " 0.9239646884707894\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np # 快速操作结构数组的工具\n", "import matplotlib.pyplot as plt  # 可视化绘制\n", "from sklearn.linear_model import LinearRegression  # 线性回归\n", "from sklearn.datasets import make_classification\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import GradientBoostingClassifier,RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import roc_auc_score,roc_curve,auc\n", "from sklearn.preprocessing import OneHotEncoder\n", "\n", "\n", "# 弱分类器的数目\n", "n_estimator = 10\n", "# 随机生成分类数据。\n", "X, y = make_classification(n_samples=80000,n_features=20,n_classes=2)\n", "\n", "# 切分为测试集和训练集，比例0.5\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.5)\n", "# 将训练集切分为两部分，一部分用于训练GBDT模型，另一部分输入到训练好的GBDT模型生成GBDT特征，然后作为LR的特征。这样分成两部分是为了防止过拟合。\n", "X_train_gbdt, X_train_lr, y_train_gbdt, y_train_lr = train_test_split(X_train, y_train, test_size=0.5)\n", "# 调用GBDT分类模型。\n", "gbdt = GradientBoostingClassifier(n_estimators=n_estimator)\n", "# 调用one-hot编码。\n", "one_hot = OneHotEncoder()\n", "# 调用LR分类模型。\n", "lr = LogisticRegression()\n", "\n", "\n", "'''使用X_train训练GBDT模型，后面用此模型构造特征'''\n", "gbdt.fit(X_train_gbdt, y_train_gbdt)\n", "\n", "X_leaf_index = gbdt.apply(X_train_gbdt)[:, :, 0]  # apply返回每个样本在每科树中所属的叶子节点索引。行数为样本数，列数为树数目。值为在每个数的叶子索引\n", "X_lr_leaf_index = gbdt.apply(X_train_lr)[:, :, 0] # apply返回每个样本在每科树中所属的叶子节点索引。行数为样本数，列数为树数目。值为在每个数的叶子索引\n", "print('每个样本在每个树中所属的叶子索引\\n',X_leaf_index)\n", "# fit one-hot编码器\n", "one_hot.fit(X_leaf_index)  # 训练one-hot编码，就是识别每列有多少可取值\n", "X_lr_one_hot = one_hot.transform(X_lr_leaf_index)  # 将训练数据，通过gbdt树，形成的叶子节点（每个叶子代表了原始特征的一种组合）索引，编码成one0-hot特征。\n", "# 编码后的每个特征代表原来的一批特征的组合。\n", "\n", "''' \n", "使用训练好的GBDT模型构建特征，然后将特征经过one-hot编码作为新的特征输入到LR模型训练。\n", "'''\n", "\n", "# 使用lr训练gbdt的特征组合\n", "print('使用逻辑回归训练GBDT组合特征的结果')\n", "lr.fit(X_lr_one_hot, y_train_lr)\n", "# 用训练好的LR模型多X_test做预测\n", "y_pred_grd_lm = lr.predict_proba(one_hot.transform(gbdt.apply(X_test)[:, :, 0]))[:, 1]  # 获取测试集正样本的概率\n", "# 根据预测结果输出\n", "fpr, tpr, thresholds = roc_curve(y_test, y_pred_grd_lm)  # 获取真正率和假正率以及门限\n", "roc_auc = auc(fpr, tpr)\n", "print('auc值为\\n',roc_auc)\n", "#画图，只需要plt.plot(fpr,tpr),变量roc_auc只是记录auc的值，通过auc()函数能计算出来\n", "plt.plot(fpr, tpr, lw=1, label='area = %0.2f' %  roc_auc)\n", "plt.show()\n", "\n", "\n", "\n", "# 使用lr直接训练原始数据\n", "print('使用逻辑回归训练原始数据集的结果')\n", "lr.fit(X_train_lr, y_train_lr)\n", "# 用训练好的LR模型多X_test做预测\n", "y_pred_grd_lm = lr.predict_proba(X_test)[:, 1]  # 获取测试集正样本的概率\n", "# 根据预测结果输出\n", "fpr, tpr, thresholds = roc_curve(y_test, y_pred_grd_lm)  # 获取真正率和假正率以及门限\n", "roc_auc = auc(fpr, tpr)\n", "print('auc值为\\n',roc_auc)\n", "#画图，只需要plt.plot(fpr,tpr),变量roc_auc只是记录auc的值，通过auc()函数能计算出来\n", "plt.plot(fpr, tpr, lw=1, label='area = %0.2f' %  roc_auc)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们可以看到结果\n", "每个样本在每个树中所属的叶子索引\n", "\n", " [[  3.   3.   3. ...,   3.   3.   3.]\n", " \n", " [  7.   7.   7. ...,   4.   7.   4.]\n", " \n", " [ 14.  14.  14. ...,  14.  14.  14.]\n", " \n", " ..., \n", " [ 14.  14.  14. ...,  13.  13.  14.]\n", " \n", " [ 14.  14.  14. ...,  14.  14.  14.]\n", " \n", " [ 14.  14.  13. ...,  13.  13.  14.]]\n", " \n", "使用逻辑回归训练GBDT组合特征的结果\n", "\n", "auc值为\n", "\n", " 0.976446818947\n", " \n", "使用逻辑回归训练原始数据集的结果\n", "\n", "auc值为\n", "\n", " 0.960063847564\n", "\n", "效果提取了1.5%。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["CTR预估中GBDT和LR的融合\n", "----------------\n", "\n", "在CTR预估中，如何利用ad_id是一个问题。\n", "\n", "直接将ad_id作为特征建树不可行，而one-hot编码过于稀疏，为每个ad_id建GBDT树，相当于发掘出区分每个广告的特征。\n", "\n", "而对于曝光不充分的样本即长尾部分，无法单独建树。\n", "\n", "综合方案为：使用GBDT对非ID和ID分别建一类树。\n", "\n", "非ID类树：\n", "\n", "不以细粒度的ID建树，此类树作为base，即这些ID一起构建GBDT。\n", "\n", "即便曝光少的广告、广告主，仍可以通过此类树得到有区分性的特征、特征组合。\n", "\n", "ID类树：\n", "\n", "以细粒度 的ID建一类树（每个ID构建GBDT），用于发现曝光充分的ID对应有区分性的特征、特征组合。\n", "\n", "如何根据GBDT建的两类树，对原始特征进行映射？\n", "\n", "以如下图为例，当一条样本x进来之后，遍历两类树到叶子节点，得到的特征作为LR的输入。当AD曝光不充分不足以训练树时，其它树恰好作为补充。\n", "\n", "方案如图：\n", "\n", "![x](https://img-blog.csdnimg.cn/d5ac576e44fe47358cc8e881157c82ed.png)\n", "\n", "其中kaggle竞赛一般树的数目最多为30，通过GBDT转换得到特征空间相比于原始ID低了很多。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["总结\n", "--\n", "\n", "对于样本量大的数据，线性模型具有训练速度快的特点，但线性模型学习能力限于线性可分数据，所以就需要特征工程将数据尽可能地从输入空间转换到线性可分的特征空间。\n", "\n", "GBDT与LR的融合模型，其实使用GBDT来发掘有区分度的特征以及组合特征，来替代人工组合特征。工业种GBDT+LR、GBDT+FM都是应用比较广泛。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 4}