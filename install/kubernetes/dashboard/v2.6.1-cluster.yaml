# kubectl create secret generic kubernetes-dashboard-certs --from-file=./ -n kubernetes-dashboard

apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    k8s-app: kubernetes-dashboard-cluster
  name: kubernetes-dashboard-cluster
  namespace: kube-system

---

kind: Service
apiVersion: v1
metadata:
  labels:
    k8s-app: kubernetes-dashboard-cluster
  name: kubernetes-dashboard-cluster
  namespace: kube-system
spec:
  ports:
    - port: 9090
      targetPort: 9090
  selector:
    k8s-app: kubernetes-dashboard-cluster
    
# kubectl create secret generic kubernetes-dashboard-certs --from-file=certs -n kube-system
---
apiVersion: v1
kind: Secret
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard-certs
  namespace: kube-system
type: Opaque

---

apiVersion: v1
kind: Secret
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard-csrf
  namespace: kube-system
type: Opaque
data:
  csrf: ""

---

apiVersion: v1
kind: Secret
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard-key-holder
  namespace: kube-system
type: Opaque

---

kind: ConfigMap
apiVersion: v1
metadata:
  labels:
    k8s-app: kubernetes-dashboard
  name: kubernetes-dashboard-settings
  namespace: kube-system

---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels:
    k8s-app: kubernetes-dashboard-cluster
  name: kubernetes-dashboard-cluster
rules:
  # Allow Metrics Scraper to get metrics from the Metrics server
  - apiGroups: ["*"]
    resources: ["*"]
    verbs: ["*"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kubernetes-dashboard-cluster
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kubernetes-dashboard-cluster
subjects:
  - kind: ServiceAccount
    name: kubernetes-dashboard-cluster
    namespace: kube-system

---

kind: Deployment
apiVersion: apps/v1
metadata:
  labels:
    k8s-app: kubernetes-dashboard-cluster
  name: kubernetes-dashboard-cluster
  namespace: kube-system
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: kubernetes-dashboard-cluster
  template:
    metadata:
      labels:
        k8s-app: kubernetes-dashboard-cluster
    spec:
      containers:
        - name: kubernetes-dashboard-cluster
          image: kubernetesui/dashboard:v2.6.1
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9090
              protocol: TCP
          args:
            - --namespace=kube-system
            - --enable-skip-login
            - --sidecar-host=http://dashboard-cluster-metrics-scraper:8000   # 可以多个dashboard共用一个metrics-scraper
            
            
            # 通过 token 的方式登录 dashboard，又不想 token 很快过期
            # - --auto-generate-certificates
            # - --namespace=kubernetes-dashboard
            # - --token-ttl=604800
            # 查看token
            # kubectl -n kube-system describe secret $(kubectl -n kube-system get secret | grep admin-user | awk '{print $1}')

            # - --tls-cert-file=/certs/dashboard.crt
            # - --tls-key-file=/certs/dashboard.key
            # Uncomment the following line to manually specify Kubernetes API server Host
            # If not specified, Dashboard will attempt to auto discover the API server and connect
            # to it. Uncomment only if the default does not work.
            # - --apiserver-host=http://my-address:port
          volumeMounts:
            - mountPath: /tmp
              name: tmp-volume
          livenessProbe:
            httpGet:
              scheme: HTTP
              path: /
              port: 9090
            initialDelaySeconds: 30
            timeoutSeconds: 30
      nodeSelector:
        kubeflow: 'true'
      volumes:
        - name: tmp-volume
          emptyDir: {}
      serviceAccountName: kubernetes-dashboard-cluster

---
kind: Service
apiVersion: v1
metadata:
  labels:
    k8s-app: dashboard-cluster-metrics-scraper
  name: dashboard-cluster-metrics-scraper
  namespace: kube-system
spec:
  ports:
    - port: 8000
      targetPort: 8000
  selector:
    k8s-app: dashboard-cluster-metrics-scraper

---

kind: Deployment
apiVersion: apps/v1
metadata:
  labels:
    k8s-app: dashboard-cluster-metrics-scraper
  name: dashboard-cluster-metrics-scraper
  namespace: kube-system
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: dashboard-cluster-metrics-scraper
  template:
    metadata:
      labels:
        k8s-app: dashboard-cluster-metrics-scraper
      annotations:
        seccomp.security.alpha.kubernetes.io/pod: 'runtime/default'
    spec:
      containers:
        - name: dashboard-cluster-metrics-scraper
          securityContext:
            runAsUser: 0
          image: kubernetesui/metrics-scraper:v1.0.8
          ports:
            - containerPort: 8000
              protocol: TCP
          livenessProbe:
            httpGet:
              scheme: HTTP
              path: /
              port: 8000
            initialDelaySeconds: 30
            timeoutSeconds: 30
          volumeMounts:
          - mountPath: /tmp
            name: tmp-volume
      serviceAccountName: kubernetes-dashboard-cluster
      nodeSelector:
        kubeflow: 'true'
      volumes:
        - name: tmp-volume
          emptyDir: {}
