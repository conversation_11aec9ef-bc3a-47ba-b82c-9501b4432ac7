{"mysql-ui": {"project_name": "public", "service_name": "mysql-ui", "service_describe": "可视化编辑mysql数据库", "image_name": "phpmyadmin:5.2.1", "command": "", "env": "PMA_HOST=mysql-service.infra\nPMA_PORT=3306\nPMA_USER=root\nPMA_PASSWORD=admin", "ports": "80", "expand": {"help_url": "/docs/example/service.md"}}, "redis-ui": {"project_name": "public", "service_name": "redis-ui", "service_describe": "可视化编辑redis数据库", "image_name": "ccr.ccs.tencentyun.com/cube-studio/patrikx3:latest", "command": "", "env": "REDIS_NAME=default\nREDIS_HOST=redis-master.infra\nREDIS_PORT=6379\nREDIS_PASSWORD=admin", "ports": "7843", "expand": {"help_url": "/docs/example/service.md"}}, "mongo-express": {"project_name": "public", "service_name": "mongo-express", "service_describe": "可视化编辑mongo数据库", "image_name": "mongo-express:0.54.0", "command": "", "env": "ME_CONFIG_MONGODB_SERVER=xx.xx.xx.xx\nME_CONFIG_MONGODB_PORT=xx\nME_CONFIG_MONGODB_ENABLE_ADMIN=true\nME_CONFIG_MONGODB_ADMINUSERNAME=xx\nME_CONFIG_MONGODB_ADMINPASSWORD=xx\nME_CONFIG_MONGODB_AUTH_DATABASE=xx\nME_CONFIG_MONGODB_AUTH_USERNAME=xx\nME_CONFIG_MONGODB_AUTH_PASSWORD=xx\nVCAP_APP_HOST=0.0.0.0\nVCAP_APP_PORT=8081\nME_CONFIG_OPTIONS_EDITORTHEME=ambiance", "ports": "8081", "expand": {"help_url": "/docs/example/service.md"}}, "neo4j": {"project_name": "public", "service_name": "neo4j", "service_describe": "可视化编辑图数据库neo4j", "image_name": "ccr.ccs.tencentyun.com/cube-studio/neo4j:4.4", "command": "", "env": "NEO4J_AUTH=neo4j/admin", "ports": "7474,7687", "volume_mount": "kubeflow-user-workspace(pvc):/mnt,/data/k8s/kubeflow/pipeline/workspace/admin/neo4j(hostpath):/var/lib/neo4j/data", "expand": {"help_url": "/docs/example/service.md"}}, "jaeger": {"project_name": "public", "service_name": "jaeger", "service_describe": "jaeger链路追踪", "image_name": "jaegertracing/all-in-one:1.29", "command": "", "env": "", "ports": "16686,5775", "expand": {"help_url": "/docs/example/service.md"}}, "postgresql-ui": {"project_name": "public", "service_name": "postgresql-ui", "service_describe": "可视化编辑postgresql数据库", "image_name": "dpage/pgadmin4", "command": "", "env": "PGADMIN_DEFAULT_EMAIL=<EMAIL>\nPGADMIN_DEFAULT_PASSWORD=admin", "ports": "80", "expand": {"help_url": "/docs/example/service.md"}}, "es": {"project_name": "public", "service_name": "es", "service_describe": "elasticsearch", "image_name": "elasticsearch:7.12.1", "command": "", "env": "discovery.type=single-node\ncluster.name=es-docker-cluster\nTAKE_FILE_OWNERSHIP=111", "ports": "8080,9200", "expand": {"help_url": "https://github.com/tencentmusic/cube-studio/wiki"}}, "chrome": {"project_name": "public", "service_name": "chrome", "service_describe": "无头浏览器", "image_name": "selenium/standalone-chrome", "command": "", "env": "shm-size=2g\nSE_NODE_OVERRIDE_MAX_SESSIONS=true\nSE_NODE_MAX_SESSIONS=20", "ports": "4444", "expand": {"help_url": "https://github.com/tencentmusic/cube-studio/wiki"}}, "sd-webui": {"project_name": "public", "service_name": "sd-webui", "service_describe": "sd的webui", "image_name": "ccr.ccs.tencentyun.com/cube-studio/aihub:sd-webui", "command": "python /app/stable-diffusion-webui/webui.py --port 80 --listen --no-half", "env": "", "ports": "80", "resource_gpu": "1", "resource_cpu": "5", "resource_memory": "10G", "volume_mount": "/data/k8s/kubeflow/pipeline/workspace/admin/pipeline/example/sd(hostpath):/app/stable-diffusion-webui/models/Stable-diffusion/", "expand": {"help_url": "https://github.com/data-infra/cube-studio/wiki"}}, "xinference": {"project_name": "public", "service_name": "xinference", "service_describe": "xinference大模型推理", "image_name": "xprobe/xinference:v1.0.1", "command": "xinference-local -H 0.0.0.0", "env": "XINFERENCE_HOME=/mnt/{{creator}}/pipeline/example/xinference\nHF_HUB_ENABLE_HF_TRANSFER=1\nHF_ENDPOINT=https://hf-mirror.com\nXINFERENCE_MODEL_SRC=modelscope", "ports": "9997", "resource_gpu": "1", "resource_cpu": "20", "resource_memory": "20G", "expand": {"help_url": "https://github.com/xorbitsai/inference"}}, "ollama": {"project_name": "public", "service_name": "ollama", "service_describe": "ollama大模型推理", "image_name": "ghcr.nju.edu.cn/open-webui/open-webui:ollama", "command": "", "env": "", "ports": "8080", "resource_gpu": "1", "resource_cpu": "20", "resource_memory": "20G", "volume_mount": "kubeflow-user-workspace(pvc):/mnt,/data/k8s/kubeflow/pipeline/workspace/{{creator}}/pipeline/example/ollama/model(hostpath):/root/.ollama", "expand": {"help_url": "https://github.com/ollama/ollama"}}}