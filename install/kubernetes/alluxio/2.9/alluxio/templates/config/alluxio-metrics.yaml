#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{- $name := include "alluxio.name" . }}
{{- $fullName := include "alluxio.fullname" . }}
{{- $chart := include "alluxio.chart" . }}

{{ if .Values.metrics.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullName }}-metrics
  labels:
    name: {{ $fullName }}-metrics
    app: {{ $name }}
    chart: {{ $chart }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  metrics.properties: |-
    {{- if .Values.metrics.ConsoleSink.enabled }}
    sink.console.class=alluxio.metrics.sink.ConsoleSink
    sink.console.period={{ .Values.metrics.ConsoleSink.period }}
    sink.console.unit={{ .Values.metrics.ConsoleSink.unit }}
    {{- end }}

    {{- if .Values.metrics.CsvSink.enabled }}
    sink.csv.class=alluxio.metrics.sink.CsvSink
    sink.csv.period={{ .Values.metrics.CsvSink.period }}
    sink.csv.unit={{ .Values.metrics.CsvSink.unit }}
    sink.csv.directory={{ .Values.metrics.CsvSink.directory }}
    {{- end }}

    {{- if .Values.metrics.JmxSink.enabled }}
    sink.jmx.class=alluxio.metrics.sink.JmxSink
    sink.jmx.domain={{ .Values.metrics.JmxSink.domain }}
    {{- end }}

    {{- if .Values.metrics.GraphiteSink.enabled }}
    sink.graphite.class=alluxio.metrics.sink.GraphiteSink
    sink.graphite.host={{ .Values.metrics.GraphiteSink.host }}
    sink.graphite.port={{ .Values.metrics.GraphiteSink.port }}
    sink.graphite.period={{ .Values.metrics.GraphiteSink.period }}
    sink.graphite.unit={{ .Values.metrics.GraphiteSink.unit }}
    sink.graphite.prefix={{ .Values.metrics.GraphiteSink.prefix }}
    {{- end }}

    {{- if .Values.metrics.Slf4jSink.enabled }}
    sink.slf4j.class=alluxio.metrics.sink.Slf4jSink
    sink.slf4j.period={{ .Values.metrics.Slf4jSink.period }}
    sink.slf4j.unit={{ .Values.metrics.Slf4jSink.unit }}
    sink.slf4j.filter-class={{ .Values.metrics.Slf4jSink.filterClass }}
    sink.slf4j.filter-regex={{ .Values.metrics.Slf4jSink.filterRegex }}
    {{- end }}

    {{- if .Values.metrics.PrometheusMetricsServlet.enabled }}
    sink.prometheus.class=alluxio.metrics.sink.PrometheusMetricsServlet
    {{- end }}

{{- end }}
