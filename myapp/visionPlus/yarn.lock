# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  version "1.2.6"
  resolved "https://mirrors.tencent.com/npm/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz"
  integrity sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==

"@ampproject/remapping@^2.1.0":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.1.2.tgz"
  integrity sha512-hoyByceqwKirw7w3Z7gnIIZC3Wx3J484Y3L/cMpXFbr7d9ZQj2mODrirNzcJa+SM3UlpWXYvKV4RlRpFXlWgXg==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.0"

"@ant-design/colors@^6.0.0":
  version "6.0.0"
  resolved "https://registry.npmjs.org/@ant-design/colors/-/colors-6.0.0.tgz"
  integrity sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"

"@ant-design/icons-svg@^4.2.1":
  version "4.2.1"
  resolved "https://registry.npmjs.org/@ant-design/icons-svg/-/icons-svg-4.2.1.tgz"
  integrity sha512-EB0iwlKDGpG93hW8f85CTJTs4SvMX7tt5ceupvhALp1IF44SeUFOMhKUOYqpsoYWQKAOuTRDMqn75rEaKDp0Xw==

"@ant-design/icons@^4.7.0":
  version "4.7.0"
  resolved "https://registry.npmjs.org/@ant-design/icons/-/icons-4.7.0.tgz"
  integrity sha512-aoB4Z7JA431rt6d4u+8xcNPPCrdufSRMUOpxa1ab6mz1JCQZOEVolj2WVs/tDFmN62zzK30mNelEsprLYsSF3g==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.2.1"
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    rc-util "^5.9.4"

"@ant-design/react-slick@~0.28.1":
  version "0.28.4"
  resolved "https://registry.npmjs.org/@ant-design/react-slick/-/react-slick-0.28.4.tgz"
  integrity sha512-j9eAHTn7GxbXUFNknJoHS2ceAsqrQi2j8XykjZE1IXCD8kJF+t28EvhBLniDpbOsBk/3kjalnhriTfZcjBHNqg==
  dependencies:
    "@babel/runtime" "^7.10.4"
    classnames "^2.2.5"
    json2mq "^0.2.0"
    lodash "^4.17.21"
    resize-observer-polyfill "^1.5.0"

"@apideck/better-ajv-errors@^0.3.1":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@apideck/better-ajv-errors/-/better-ajv-errors-0.3.3.tgz"
  integrity sha512-9o+HO2MbJhJHjDYZaDxJmSDckvDpiuItEsrIShV0DXeCshXWRHhqYyU/PKHMkuClOmFnZhRd6wzv4vpDu/dRKg==
  dependencies:
    json-schema "^0.4.0"
    jsonpointer "^5.0.0"
    leven "^3.1.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4":
  version "7.12.11"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.12.11.tgz"
  integrity sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.12.13", "@babel/code-frame@^7.16.0", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.16.7.tgz"
  integrity sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==
  dependencies:
    "@babel/highlight" "^7.16.7"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.16.4", "@babel/compat-data@^7.16.8", "@babel/compat-data@^7.17.0":
  version "7.17.0"
  resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.17.0.tgz"
  integrity sha512-392byTlpGWXMv4FbyWw3sAZ/FrW/DrwqLGXpy0mbyNe9Taqv1mg9yON5/o0cnr8XYCkFTZbC1eV+c+LAROgrng==

"@babel/core@^7.1.0", "@babel/core@^7.11.1", "@babel/core@^7.12.3", "@babel/core@^7.16.0", "@babel/core@^7.7.2", "@babel/core@^7.8.0":
  version "7.17.5"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.17.5.tgz"
  integrity sha512-/BBMw4EvjmyquN5O+t5eh0+YqB3XXJkYD2cjKpYtWOfFy4lQ4UozNSmxAcWT8r2XtZs0ewG+zrfsqeR15i1ajA==
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.17.3"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helpers" "^7.17.2"
    "@babel/parser" "^7.17.3"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.3"
    "@babel/types" "^7.17.0"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.1.2"
    semver "^6.3.0"

"@babel/eslint-parser@^7.16.3":
  version "7.17.0"
  resolved "https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.17.0.tgz"
  integrity sha512-PUEJ7ZBXbRkbq3qqM/jZ2nIuakUBqCYc7Qf52Lj7dlZ6zERnqisdHioL0l4wwQZnmskMeasqUNzLBFKs3nylXA==
  dependencies:
    eslint-scope "^5.1.1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.0"

"@babel/generator@^7.17.3", "@babel/generator@^7.7.2":
  version "7.17.3"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.17.3.tgz"
  integrity sha512-+R6Dctil/MgUsZsZAkYgK+ADNSZzJRRy0TvY65T71z/CR854xHQ1EweBYXdfT+HNeN7w0cSJJEzgxZMv40pxsg==
  dependencies:
    "@babel/types" "^7.17.0"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.16.7.tgz"
  integrity sha512-s6t2w/IPQVTAET1HitoowRGXooX8mCgtuP5195wD/QJPV6wYjpujCGF7JuMODVX2ZAJOf1GT6DT9MHEZvLOFSw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.16.7.tgz"
  integrity sha512-C6FdbRaxYjwVu/geKW4ZeQ0Q31AftgRcdSnZ5/jsH6BzCJbtvXvhpfkbkThYSuutZA7nCXpPR6AD9zd1dprMkA==
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.16.7.tgz"
  integrity sha512-mGojBwIWcwGD6rfqgRXVlVYmPAv7eOpIemUG3dGnDdCY4Pae70ROij3XmfrH6Fa1h1aiDylpglbZyktfzyo/hA==
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-validator-option" "^7.16.7"
    browserslist "^4.17.5"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.16.10", "@babel/helper-create-class-features-plugin@^7.16.7", "@babel/helper-create-class-features-plugin@^7.17.1", "@babel/helper-create-class-features-plugin@^7.17.6":
  version "7.17.6"
  resolved "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.6.tgz"
  integrity sha512-SogLLSxXm2OkBbSsHZMM4tUi8fUzjs63AT/d0YQIzr6GSd8Hxsbk2KYDX0k0DweAzGMj/YWeiCsorIdtdcW8Eg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"

"@babel/helper-create-regexp-features-plugin@^7.16.7":
  version "7.17.0"
  resolved "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.17.0.tgz"
  integrity sha512-awO2So99wG6KnlE+TPs6rn83gCz5WlEePJDTnLEqbchMVrBeAujURVphRdigsk094VhvZehFoNOihSlcBjwsXA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    regexpu-core "^5.0.1"

"@babel/helper-define-polyfill-provider@^0.3.1":
  version "0.3.1"
  resolved "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.1.tgz"
  integrity sha512-J9hGMpJQmtWmj46B3kBHmL38UhJGhYX7eqkcq+2gsstyYt341HmPeWspihX43yVRA0mS+8GGk2Gckc7bY/HCmA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.16.7.tgz"
  integrity sha512-SLLb0AAn6PkUeAfKJCCOl9e1R53pQlGAfc4y4XuMRZfqeMYLE0dM1LMhqbGAlGQY0lfw5/ohoYWAe9V1yibRag==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-explode-assignable-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.16.7.tgz"
  integrity sha512-KyUenhWMC8VrxzkGP0Jizjo4/Zx+1nNZhgocs+gLzyZyB8SHidhoq9KK/8Ato4anhwsivfkBLftky7gvzbZMtQ==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-function-name@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.16.7.tgz"
  integrity sha512-QfDfEnIUyyBSR3HtrtGECuZ6DAyCkYFp7GHl75vFtTnn6pjKeK0T1DB5lLkFvBea8MdaiUABx3osbgLyInoejA==
  dependencies:
    "@babel/helper-get-function-arity" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-get-function-arity@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-get-function-arity/-/helper-get-function-arity-7.16.7.tgz"
  integrity sha512-flc+RLSOBXzNzVhcLu6ujeHUrD6tANAOU5ojrRx/as+tbzf8+stUCj7+IfRRoAbEZqj/ahXEMsjhOhgeZsrnTw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-hoist-variables@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.16.7.tgz"
  integrity sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-member-expression-to-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.16.7.tgz"
  integrity sha512-VtJ/65tYiU/6AbMTDwyoXGPKHgTsfRarivm+YbB5uAzKUyuPjgZSgAFeG87FCigc7KNHu2Pegh1XIT3lXjvz3Q==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.10.4", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.16.7.tgz"
  integrity sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-transforms@^7.16.7":
  version "7.17.6"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.17.6.tgz"
  integrity sha512-2ULmRdqoOMpdvkbT8jONrZML/XALfzxlb052bldftkicAUy8AxSCkD5trDPQcwHNmolcl7wP6ehNqMlyUw6AaA==
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.3"
    "@babel/types" "^7.17.0"

"@babel/helper-optimise-call-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.7.tgz"
  integrity sha512-EtgBhg7rd/JcnpZFXpBy0ze1YRfdm7BnBX4uKMBd3ixa3RGAE002JZB66FJyNH7g0F38U05pXmA5P8cBh7z+1w==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz"
  integrity sha512-Qg3Nk7ZxpgMrsox6HreY1ZNKdBq7K72tDSliA6dCl5f007jR4ne8iD5UzuNnCJH2xBf2BEEVGr+/OL6Gdp7RxA==

"@babel/helper-remap-async-to-generator@^7.16.8":
  version "7.16.8"
  resolved "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.8.tgz"
  integrity sha512-fm0gH7Flb8H51LqJHy3HJ3wnE1+qtYR2A99K06ahwrawLdOFsCEWjZOrYricXJHoPSudNKxrMBUPEIPxiIIvBw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-wrap-function" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helper-replace-supers@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.16.7.tgz"
  integrity sha512-y9vsWilTNaVnVh6xiJfABzsNpgDPKev9HnAgz6Gb1p6UUwf9NepdlsV7VXGCftJM+jqD5f7JIEubcpLjZj5dBw==
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-simple-access@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.16.7.tgz"
  integrity sha512-ZIzHVyoeLMvXMN/vok/a4LWRy8G2v205mNP0XOuf9XRLyX5/u9CnVulUtDgUTama3lT+bf/UqucuZjqiGuTS1g==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.16.0.tgz"
  integrity sha512-+il1gTy0oHwUsBQZyJvukbB4vPMdcYBrFHa0Uc4AizLxbq6BOYC51Rv4tWocX9BLBDLZ4kc6qUFpQ6HRgL+3zw==
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.16.7.tgz"
  integrity sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-validator-identifier@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz"
  integrity sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==

"@babel/helper-validator-option@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.16.7.tgz"
  integrity sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ==

"@babel/helper-wrap-function@^7.16.8":
  version "7.16.8"
  resolved "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.16.8.tgz"
  integrity sha512-8RpyRVIAW1RcDDGTA+GpPAwV22wXCfKOoM9bet6TLkGIFTkRQSkH1nMQ5Yet4MpoXe1ZwHPVtNasc2w0uZMqnw==
  dependencies:
    "@babel/helper-function-name" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helpers@^7.17.2":
  version "7.17.2"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.17.2.tgz"
  integrity sha512-0Qu7RLR1dILozr/6M0xgj+DFPmi6Bnulgm9M8BVa9ZCWxDqlSnqt3cf8IDPB5m45sVXUZ0kuQAgUrdSFFH79fQ==
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.0"
    "@babel/types" "^7.17.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.16.7":
  version "7.16.10"
  resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.16.10.tgz"
  integrity sha512-5FnTQLSLswEj6IkgVw5KusNUUFY9ZGqe/TRFnP/BKYHYgfh7tc+C7mwiy95/yNP7Dh9x580Vv8r7u7ZfTBFxdw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.16.7", "@babel/parser@^7.17.3":
  version "7.17.3"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.17.3.tgz"
  integrity sha512-7yJPvPV+ESz2IUTPbOL+YkIGyCqOyNIzdguKQuJGnH7bg1WTIifuM21YqokFt/THWh1AkCRn9IgoykTRCBVpzA==

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.7.tgz"
  integrity sha512-anv/DObl7waiGEnC24O9zqL0pSuI9hljihqiDuFHC8d7/bjr/4RLGPWuc8rYOff/QPzbEPSkzG8wGG9aDuhHRg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.16.7.tgz"
  integrity sha512-di8vUHRdf+4aJ7ltXhaDbPoszdkh59AQtJM5soLsuHpQJdFQZOA4uGj0V2u/CZ8bJ/u8ULDL5yq6FO/bCXnKHw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"

"@babel/plugin-proposal-async-generator-functions@^7.16.8":
  version "7.16.8"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.16.8.tgz"
  integrity sha512-71YHIvMuiuqWJQkebWJtdhQTfd4Q4mF76q2IX37uZPkG9+olBxsX+rH1vkhFto4UeJZ9dPY2s+mDvhDm1u2BGQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.16.0", "@babel/plugin-proposal-class-properties@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.7.tgz"
  integrity sha512-IobU0Xme31ewjYOShSIqd/ZGM/r/cuOz2z0MDbNrhF5FW+ZVgi0f2lyeoj9KFPDOAqsYxmLWZte1WOwlvY9aww==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-class-static-block@^7.16.7":
  version "7.17.6"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.17.6.tgz"
  integrity sha512-X/tididvL2zbs7jZCeeRJ8167U/+Ac135AM6jCAx6gYXDUviZV5Ku9UDvWS2NCuWlFjIRXklYhwo6HhAC7ETnA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.17.6"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.16.4":
  version "7.17.2"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.17.2.tgz"
  integrity sha512-WH8Z95CwTq/W8rFbMqb9p3hicpt4RX4f0K659ax2VHxgOyT6qQmUaEVEjIh4WR9Eh9NymkVn5vwsrE68fAQNUw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.17.1"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/plugin-syntax-decorators" "^7.17.0"
    charcodes "^0.2.0"

"@babel/plugin-proposal-dynamic-import@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.16.7.tgz"
  integrity sha512-I8SW9Ho3/8DRSdmDdH3gORdyUuYnk1m4cMxUAdu5oy4n3OfN8flDEH+d60iG7dUfi0KkYwSvoalHzzdRzpWHTg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.16.7.tgz"
  integrity sha512-ZxdtqDXLRGBL64ocZcs7ovt71L3jhC1RGSyR996svrCi3PYqHNkb3SwPJCs8RIzD86s+WPpt2S73+EHCGO+NUA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.16.7.tgz"
  integrity sha512-lNZ3EEggsGY78JavgbHsK9u5P3pQaW7k4axlgFLYkMd7UBsiNahCITShLjNQschPyjtO6dADrL24757IdhBrsQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.16.7.tgz"
  integrity sha512-K3XzyZJGQCr00+EtYtrDjmwX7o7PLK6U9bi1nCwkQioRFVUv6dJoxbQjtWVtP+bCPy82bONBKG8NPyQ4+i6yjg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.16.7.tgz"
  integrity sha512-aUOrYU3EVtjf62jQrCj63pYZ7k6vns2h/DQvHPWGmsJRYzWXZ6/AsfgpiRy6XiuIDADhJzP2Q9MwSMKauBQ+UQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.0", "@babel/plugin-proposal-numeric-separator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.16.7.tgz"
  integrity sha512-vQgPMknOIgiuVqbokToyXbkY/OmmjAzr/0lhSIbG/KmnzXPGwW/AdhdKpi+O4X/VkWiWjnkKOBiqJrTaC98VKw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.16.7":
  version "7.17.3"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.17.3.tgz"
  integrity sha512-yuL5iQA/TbZn+RGAfxQXfi7CNLmKi1f8zInn4IgobuCWcAb7i+zj4TYzQ9l8cEzVyJ89PDGuqxK1xZpUDISesw==
  dependencies:
    "@babel/compat-data" "^7.17.0"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.16.7"

"@babel/plugin-proposal-optional-catch-binding@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.16.7.tgz"
  integrity sha512-eMOH/L4OvWSZAE1VkHbr1vckLG1WUcHGJSLqqQwl2GaUqG6QjddvrOaTUMNYiv77H5IKPMZ9U9P7EaHwvAShfA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.0", "@babel/plugin-proposal-optional-chaining@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.16.7.tgz"
  integrity sha512-eC3xy+ZrUcBtP7x+sq62Q/HYd674pPTb/77XZMb5wbDPGWIdUbSr4Agr052+zaUPSb+gGRnjxXfKFvx5iMJ+DA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.0", "@babel/plugin-proposal-private-methods@^7.16.11":
  version "7.16.11"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.16.11.tgz"
  integrity sha512-F/2uAkPlXDr8+BHpZvo19w3hLFKge+k75XUprE6jaqKxjGkSYcK+4c+bup5PdW/7W/Rpjwql7FTVEDW+fRAQsw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.10"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-private-property-in-object@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.7.tgz"
  integrity sha512-rMQkjcOFbm+ufe3bTZLyOfsOUOxyvLXZJCTARhJr+8UMSoZmqTe1K1BgkFcrW37rAchWg57yI69ORxiWvUINuQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.16.7", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.16.7.tgz"
  integrity sha512-QRK0YI/40VLhNVGIjRNAAQkEHws0cswSdFFjpFyt943YmJIU1da9uW63Iu6NFV6CxTZW5eTDCrwZUstBWgp/Rg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13", "@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.17.0":
  version "7.17.0"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.17.0.tgz"
  integrity sha512-qWe85yCXsvDEluNP0OyeQjH63DlhAR3W7K9BxxU1MvbDb48tgBG+Ao6IJJ6smPDrrVzSQZrbF6donpkFBMcs3A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  integrity sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-flow@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.16.7.tgz"
  integrity sha512-UDo3YGQO0jH6ytzVwgSLv9i/CzMcUjbKenL67dTrAZPPv6GFAtDhe6jqnvmoKzC/7htNTohhos+onPtDMqJwaQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.16.7.tgz"
  integrity sha512-Esxmk7YjA8QysKeT3VhTXvF6y77f/a91SIs4pWb4H2eWGQkCKFgQaG6hdoEVZtGsrAcb2K5BW66XsOErD4WU3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4", "@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5", "@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.16.7", "@babel/plugin-syntax-typescript@^7.7.2":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.7.tgz"
  integrity sha512-YhUIJHHGkqPgEcMYkPCKTyGUdoGKWtopIycQyjJH8OjvRgOYsXsaKehLVPScKJWAULPxMa4N1vCe6szREFlZ7A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-arrow-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.7.tgz"
  integrity sha512-9ffkFFMbvzTvv+7dTp/66xvZAWASuPD5Tl9LK3Z9vhOmANo6j94rik+5YMBt4CwHVMWLWpMsriIc2zsa3WW3xQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-async-to-generator@^7.16.8":
  version "7.16.8"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.8.tgz"
  integrity sha512-MtmUmTJQHCnyJVrScNzNlofQJ3dLFuobYn3mwOTKHnSCMtbNsqvF71GQmJfFjdrXSsAA7iysFmYWw4bXZ20hOg==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"

"@babel/plugin-transform-block-scoped-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.16.7.tgz"
  integrity sha512-JUuzlzmF40Z9cXyytcbZEZKckgrQzChbQJw/5PuEHYeqzCsvebDx0K0jWnIIVcmmDOAVctCgnYs0pMcrYj2zJg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-block-scoping@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.7.tgz"
  integrity sha512-ObZev2nxVAYA4bhyusELdo9hb3H+A56bxH3FZMbEImZFiEDYVHXQSJ1hQKFlDnlt8G9bBrCZ5ZpURZUrV4G5qQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-classes@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.7.tgz"
  integrity sha512-WY7og38SFAGYRe64BrjKf8OrE6ulEHtr5jEYaZMwox9KebgqPi67Zqz8K53EKk1fFEJgm96r32rkKZ3qA2nCWQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.16.7.tgz"
  integrity sha512-gN72G9bcmenVILj//sv1zLNaPyYcOzUho2lIJBMh/iakJ9ygCo/hEF9cpGb61SCMEDxbbyBoVQxrt+bWKu5KGw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-destructuring@^7.16.7":
  version "7.17.3"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.17.3.tgz"
  integrity sha512-dDFzegDYKlPqa72xIlbmSkly5MluLoaC1JswABGktyt6NTXSBcUuse/kWE/wvKFWJHPETpi158qJZFS3JmykJg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-dotall-regex@^7.16.7", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.7.tgz"
  integrity sha512-Lyttaao2SjZF6Pf4vk1dVKv8YypMpomAbygW+mU5cYP3S5cWTfCJjG8xV6CFdzGFlfWK81IjL9viiTvpb6G7gQ==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-duplicate-keys@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.7.tgz"
  integrity sha512-03DvpbRfvWIXyK0/6QiR1KMTWeT6OcQ7tbhjrXyFS02kjuX/mu5Bvnh5SDSWHxyawit2g5aWhKwI86EE7GUnTw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-exponentiation-operator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.7.tgz"
  integrity sha512-8UYLSlyLgRixQvlYH3J2ekXFHDFLQutdy7FfFAMm3CPZ6q9wHCwnUyiXpQCe3gVVnQlHc5nsuiEVziteRNTXEA==
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-flow-strip-types@^7.16.0":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.16.7.tgz"
  integrity sha512-mzmCq3cNsDpZZu9FADYYyfZJIOrSONmHcop2XEKPdBNMa4PDC4eEvcOvzZaCNcjKu72v0XQlA5y1g58aLRXdYg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-flow" "^7.16.7"

"@babel/plugin-transform-for-of@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.7.tgz"
  integrity sha512-/QZm9W92Ptpw7sjI9Nx1mbcsWz33+l8kuMIQnDwgQBG5s3fAfQvkRjQ7NqXhtNcKOnPkdICmUHyCaWW06HCsqg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-function-name@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.7.tgz"
  integrity sha512-SU/C68YVwTRxqWj5kgsbKINakGag0KTgq9f2iZEXdStoAbOzLHEBRYzImmA6yFo8YZhJVflvXmIHUO7GWHmxxA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.7.tgz"
  integrity sha512-6tH8RTpTWI0s2sV6uq3e/C9wPo4PTqqZps4uF0kzQ9/xPLFQtipynvmT1g/dOfEJ+0EQsHhkQ/zyRId8J2b8zQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-member-expression-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.7.tgz"
  integrity sha512-mBruRMbktKQwbxaJof32LT9KLy2f3gH+27a5XSuXo6h7R3vqltl0PgZ80C8ZMKw98Bf8bqt6BEVi3svOh2PzMw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-modules-amd@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.7.tgz"
  integrity sha512-KaaEtgBL7FKYwjJ/teH63oAmE3lP34N3kshz8mm4VMAw7U3PxjVwwUmxEFksbgsNUaO3wId9R2AVQYSEGRa2+g==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.16.8":
  version "7.16.8"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.16.8.tgz"
  integrity sha512-oflKPvsLT2+uKQopesJt3ApiaIS2HW+hzHFcwRNtyDGieAeC/dIHZX8buJQ2J2X1rxGPy4eRcUijm3qcSPjYcA==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.16.7.tgz"
  integrity sha512-DuK5E3k+QQmnOqBR9UkusByy5WZWGRxfzV529s9nPra1GE7olmxfqO2FHobEOYSPIjPBTr4p66YDcjQnt8cBmw==
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.7.tgz"
  integrity sha512-EMh7uolsC8O4xhudF2F6wedbSHm1HHZ0C6aJ7K67zcDNidMzVcxWdGr+htW9n21klm+bOn+Rx4CBsAntZd3rEQ==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-named-capturing-groups-regex@^7.16.8":
  version "7.16.8"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.8.tgz"
  integrity sha512-j3Jw+n5PvpmhRR+mrgIh04puSANCk/T/UA3m3P1MjJkhlK906+ApHhDIqBQDdOgL/r1UYpz4GNclTXxyZrYGSw==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"

"@babel/plugin-transform-new-target@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.7.tgz"
  integrity sha512-xiLDzWNMfKoGOpc6t3U+etCE2yRnn3SM09BXqWPIZOBpL2gvVrBWUKnsJx0K/ADi5F5YC5f8APFfWrz25TdlGg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-object-super@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.7.tgz"
  integrity sha512-14J1feiQVWaGvRxj2WjyMuXS2jsBkgB3MdSN5HuC2G5nRspa5RK9COcs82Pwy5BuGcjb+fYaUj94mYcOj7rCvw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"

"@babel/plugin-transform-parameters@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.7.tgz"
  integrity sha512-AT3MufQ7zZEhU2hwOA11axBnExW0Lszu4RL/tAlUJBuNoRak+wehQW8h6KcXOcgjY42fHtDxswuMhMjFEuv/aw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-property-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.7.tgz"
  integrity sha512-z4FGr9NMGdoIl1RqavCqGG+ZuYjfZ/hkCIeuH6Do7tXmSm0ls11nYVSJqFEUOSJbDab5wC6lRE/w6YjVcr6Hqw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-constant-elements@^7.12.1":
  version "7.17.6"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-constant-elements/-/plugin-transform-react-constant-elements-7.17.6.tgz"
  integrity sha512-OBv9VkyyKtsHZiHLoSfCn+h6yU7YKX8nrs32xUmOa1SRSk+t03FosB6fBZ0Yz4BpD1WV7l73Nsad+2Tz7APpqw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-display-name@^7.16.0", "@babel/plugin-transform-react-display-name@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.16.7.tgz"
  integrity sha512-qgIg8BcZgd0G/Cz916D5+9kqX0c7nPZyXaP8R2tLNN5tkyIZdG5fEwBrxwplzSnjC1jvQmyMNVwUCZPcbGY7Pg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-jsx-development@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.16.7.tgz"
  integrity sha512-RMvQWvpla+xy6MlBpPlrKZCMRs2AGiHOGHY3xRwl0pEeim348dDyxeH4xBsMPbIMhujeq7ihE702eM2Ew0Wo+A==
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.16.7"

"@babel/plugin-transform-react-jsx@^7.16.7":
  version "7.17.3"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.17.3.tgz"
  integrity sha512-9tjBm4O07f7mzKSIlEmPdiE6ub7kfIe6Cd+w+oQebpATfTQMAgW+YOuWxogbKVTulA+MEO7byMeIUtQ1z+z+ZQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-jsx" "^7.16.7"
    "@babel/types" "^7.17.0"

"@babel/plugin-transform-react-pure-annotations@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.7.tgz"
  integrity sha512-hs71ToC97k3QWxswh2ElzMFABXHvGiJ01IB1TbYQDGeWRKWz/MPUTh5jGExdHvosYKpnJW5Pm3S4+TA3FyX+GA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-regenerator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.7.tgz"
  integrity sha512-mF7jOgGYCkSJagJ6XCujSQg+6xC1M77/03K2oBmVJWoFGNUtnVJO4WHKJk3dnPC8HCcj4xBQP1Egm8DWh3Pb3Q==
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.7.tgz"
  integrity sha512-KQzzDnZ9hWQBjwi5lpY5v9shmm6IVG0U9pB18zvMu2i4H90xpT4gmqwPYsn8rObiadYe2M0gmgsiOIF5A/2rtg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-runtime@^7.16.4":
  version "7.17.0"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.17.0.tgz"
  integrity sha512-fr7zPWnKXNc1xoHfrIU9mN/4XKX4VLZ45Q+oMhfsYIaHvg7mHgmhfOy/ckRWqDK7XF3QDigRpkh5DKq6+clE8A==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    babel-plugin-polyfill-corejs2 "^0.3.0"
    babel-plugin-polyfill-corejs3 "^0.5.0"
    babel-plugin-polyfill-regenerator "^0.3.0"
    semver "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.7.tgz"
  integrity sha512-hah2+FEnoRoATdIb05IOXf+4GzXYTq75TVhIn1PewihbpyrNWUt2JbudKQOETWw6QpLe+AIUpJ5MVLYTQbeeUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-spread@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.7.tgz"
  integrity sha512-+pjJpgAngb53L0iaA5gU/1MLXJIfXcYepLgXB3esVRf4fqmj8f2cxM3/FKaHsZms08hFQJkFccEWuIpm429TXg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.7.tgz"
  integrity sha512-NJa0Bd/87QV5NZZzTuZG5BPJjLYadeSZ9fO6oOUoL4iQx+9EEuw/eEM92SrsT19Yc2jgB1u1hsjqDtH02c3Drw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-template-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.16.7.tgz"
  integrity sha512-VwbkDDUeenlIjmfNeDX/V0aWrQH2QiVyJtwymVQSzItFDTpxfyJh3EVaQiS0rIN/CqbLGr0VcGmuwyTdZtdIsA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typeof-symbol@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.7.tgz"
  integrity sha512-p2rOixCKRJzpg9JB4gjnG4gjWkWa89ZoYUnl9snJ1cWIcTH/hvxZqfO+WjG6T8DRBpctEol5jw1O5rA8gkCokQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typescript@^7.16.7":
  version "7.16.8"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.8.tgz"
  integrity sha512-bHdQ9k7YpBDO2d0NVfkj51DpQcvwIzIusJ7mEUaMlbZq3Kt/U47j24inXZHQ5MDiYpCs+oZiwnXyKedE8+q7AQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-typescript" "^7.16.7"

"@babel/plugin-transform-unicode-escapes@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.7.tgz"
  integrity sha512-TAV5IGahIz3yZ9/Hfv35TV2xEm+kaBDaZQCn2S/hG9/CZ0DktxJv9eKfPc7yYCvOYR4JGx1h8C+jcSOvgaaI/Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-unicode-regex@^7.16.7":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.7.tgz"
  integrity sha512-oC5tYYKw56HO75KZVLQ+R/Nl3Hro9kf8iG0hXoaHP7tjAyCpvqBiSNe6vGrZni1Z6MggmUOC6A7VP7AVmw225Q==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/preset-env@^7.11.0", "@babel/preset-env@^7.12.1", "@babel/preset-env@^7.16.4":
  version "7.16.11"
  resolved "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.16.11.tgz"
  integrity sha512-qcmWG8R7ZW6WBRPZK//y+E3Cli151B20W1Rv7ln27vuPaXU/8TKms6jFdiJtF7UDTxcrb7mZd88tAeK9LjdT8g==
  dependencies:
    "@babel/compat-data" "^7.16.8"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.16.7"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-async-generator-functions" "^7.16.8"
    "@babel/plugin-proposal-class-properties" "^7.16.7"
    "@babel/plugin-proposal-class-static-block" "^7.16.7"
    "@babel/plugin-proposal-dynamic-import" "^7.16.7"
    "@babel/plugin-proposal-export-namespace-from" "^7.16.7"
    "@babel/plugin-proposal-json-strings" "^7.16.7"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.16.7"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.7"
    "@babel/plugin-proposal-numeric-separator" "^7.16.7"
    "@babel/plugin-proposal-object-rest-spread" "^7.16.7"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.7"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-private-methods" "^7.16.11"
    "@babel/plugin-proposal-private-property-in-object" "^7.16.7"
    "@babel/plugin-proposal-unicode-property-regex" "^7.16.7"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.16.7"
    "@babel/plugin-transform-async-to-generator" "^7.16.8"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.7"
    "@babel/plugin-transform-block-scoping" "^7.16.7"
    "@babel/plugin-transform-classes" "^7.16.7"
    "@babel/plugin-transform-computed-properties" "^7.16.7"
    "@babel/plugin-transform-destructuring" "^7.16.7"
    "@babel/plugin-transform-dotall-regex" "^7.16.7"
    "@babel/plugin-transform-duplicate-keys" "^7.16.7"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.7"
    "@babel/plugin-transform-for-of" "^7.16.7"
    "@babel/plugin-transform-function-name" "^7.16.7"
    "@babel/plugin-transform-literals" "^7.16.7"
    "@babel/plugin-transform-member-expression-literals" "^7.16.7"
    "@babel/plugin-transform-modules-amd" "^7.16.7"
    "@babel/plugin-transform-modules-commonjs" "^7.16.8"
    "@babel/plugin-transform-modules-systemjs" "^7.16.7"
    "@babel/plugin-transform-modules-umd" "^7.16.7"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.16.8"
    "@babel/plugin-transform-new-target" "^7.16.7"
    "@babel/plugin-transform-object-super" "^7.16.7"
    "@babel/plugin-transform-parameters" "^7.16.7"
    "@babel/plugin-transform-property-literals" "^7.16.7"
    "@babel/plugin-transform-regenerator" "^7.16.7"
    "@babel/plugin-transform-reserved-words" "^7.16.7"
    "@babel/plugin-transform-shorthand-properties" "^7.16.7"
    "@babel/plugin-transform-spread" "^7.16.7"
    "@babel/plugin-transform-sticky-regex" "^7.16.7"
    "@babel/plugin-transform-template-literals" "^7.16.7"
    "@babel/plugin-transform-typeof-symbol" "^7.16.7"
    "@babel/plugin-transform-unicode-escapes" "^7.16.7"
    "@babel/plugin-transform-unicode-regex" "^7.16.7"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.16.8"
    babel-plugin-polyfill-corejs2 "^0.3.0"
    babel-plugin-polyfill-corejs3 "^0.5.0"
    babel-plugin-polyfill-regenerator "^0.3.0"
    core-js-compat "^3.20.2"
    semver "^6.3.0"

"@babel/preset-modules@^0.1.5":
  version "0.1.5"
  resolved "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
  integrity sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.12.5", "@babel/preset-react@^7.16.0":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.16.7.tgz"
  integrity sha512-fWpyI8UM/HE6DfPBzD8LnhQ/OcH8AgTaqcqP2nGOXEUV+VKBR5JRN9hCk9ai+zQQ57vtm9oWeXguBCPNUjytgA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-transform-react-display-name" "^7.16.7"
    "@babel/plugin-transform-react-jsx" "^7.16.7"
    "@babel/plugin-transform-react-jsx-development" "^7.16.7"
    "@babel/plugin-transform-react-pure-annotations" "^7.16.7"

"@babel/preset-typescript@^7.16.0":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.16.7.tgz"
  integrity sha512-WbVEmgXdIyvzB77AQjGBEyYPZx+8tTsO50XtfozQrkW8QB2rLJpH2lgx0TRw5EJrBxOZQ+wCcyPVQvS8tjEHpQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-transform-typescript" "^7.16.7"

"@babel/runtime-corejs3@^7.10.2":
  version "7.17.2"
  resolved "https://registry.npmjs.org/@babel/runtime-corejs3/-/runtime-corejs3-7.17.2.tgz"
  integrity sha512-NcKtr2epxfIrNM4VOmPKO46TvDMCBhgi2CrSHaEarrz+Plk2K5r9QemmOFTGpZaoKnWoGH5MO+CzeRsih/Fcgg==
  dependencies:
    core-js-pure "^3.20.2"
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.10.1", "@babel/runtime@^7.10.2", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.1", "@babel/runtime@^7.12.13", "@babel/runtime@^7.12.5", "@babel/runtime@^7.15.4", "@babel/runtime@^7.16.3", "@babel/runtime@^7.16.7", "@babel/runtime@^7.19.4", "@babel/runtime@^7.22.5", "@babel/runtime@^7.8.4", "@babel/runtime@^7.9.2":
  version "7.23.2"
  resolved "https://mirrors.tencent.com/npm/@babel/runtime/-/runtime-7.23.2.tgz"
  integrity sha512-mM8eg4yl5D6i3lu2QKPuPH4FArvJ8KhTofbE7jwMUv9KX5mBvwPAqnV3MlyBNqdp9RyRKP6Yck8TrfYrPvX3bg==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.16.7", "@babel/template@^7.3.3":
  version "7.16.7"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.16.7.tgz"
  integrity sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/traverse@^7.13.0", "@babel/traverse@^7.16.7", "@babel/traverse@^7.16.8", "@babel/traverse@^7.17.0", "@babel/traverse@^7.17.3", "@babel/traverse@^7.7.2":
  version "7.17.3"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.17.3.tgz"
  integrity sha512-5irClVky7TxRWIRtxlh2WPUUOLhcPN06AGgaQSB8AEwuyEBgJVuJ5imdHm5zxk8w0QS5T+tDfnDxAlhWjpb7cw==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.17.3"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/parser" "^7.17.3"
    "@babel/types" "^7.17.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.12.6", "@babel/types@^7.16.0", "@babel/types@^7.16.7", "@babel/types@^7.16.8", "@babel/types@^7.17.0", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.4.4":
  version "7.17.0"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.17.0.tgz"
  integrity sha512-TmKSNO4D5rzhL5bjWFcVHHLETzfQ/AmbKpKPOSjlP0WoHZ6L911fgoOKY4Alp/emzG4cHJdyN49zpgkbXFEHHw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  integrity sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==

"@csstools/normalize.css@*":
  version "12.0.0"
  resolved "https://registry.npmjs.org/@csstools/normalize.css/-/normalize.css-12.0.0.tgz"
  integrity sha512-M0qqxAcwCsIVfpFQSlGN5XjXWu8l5JDZN+fPt1LeW5SZexQTgnaEvgXAY+CeygRw0EeppWHi12JxESWiWrB0Sg==

"@csstools/postcss-color-function@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@csstools/postcss-color-function/-/postcss-color-function-1.0.2.tgz"
  integrity sha512-uayvFqfa0hITPwVduxRYNL9YBD/anTqula0tu2llalaxblEd7QPuETSN3gB5PvTYxSfd0d8kS4Fypgo5JaUJ6A==
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-font-format-keywords@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/@csstools/postcss-font-format-keywords/-/postcss-font-format-keywords-1.0.0.tgz"
  integrity sha512-oO0cZt8do8FdVBX8INftvIA4lUrKUSCcWUf9IwH9IPWOgKT22oAZFXeHLoDK7nhB2SmkNycp5brxfNMRLIhd6Q==
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-hwb-function@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/@csstools/postcss-hwb-function/-/postcss-hwb-function-1.0.0.tgz"
  integrity sha512-VSTd7hGjmde4rTj1rR30sokY3ONJph1reCBTUXqeW1fKwETPy1x4t/XIeaaqbMbC5Xg4SM/lyXZ2S8NELT2TaA==
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-ic-unit@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/@csstools/postcss-ic-unit/-/postcss-ic-unit-1.0.0.tgz"
  integrity sha512-i4yps1mBp2ijrx7E96RXrQXQQHm6F4ym1TOD0D69/sjDjZvQ22tqiEvaNw7pFZTUO5b9vWRHzbHzP9+UKuw+bA==
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-is-pseudo-class@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@csstools/postcss-is-pseudo-class/-/postcss-is-pseudo-class-2.0.0.tgz"
  integrity sha512-WnfZlyuh/CW4oS530HBbrKq0G8BKl/bsNr5NMFoubBFzJfvFRGJhplCgIJYWUidLuL3WJ/zhMtDIyNFTqhx63Q==
  dependencies:
    postcss-selector-parser "^6.0.9"

"@csstools/postcss-normalize-display-values@^1.0.0":
  version "1.0.0"
  resolved "https://registry.npmjs.org/@csstools/postcss-normalize-display-values/-/postcss-normalize-display-values-1.0.0.tgz"
  integrity sha512-bX+nx5V8XTJEmGtpWTO6kywdS725t71YSLlxWt78XoHUbELWgoCXeOFymRJmL3SU1TLlKSIi7v52EWqe60vJTQ==
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-oklab-function@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@csstools/postcss-oklab-function/-/postcss-oklab-function-1.0.1.tgz"
  integrity sha512-Bnly2FWWSTZX20hDJLYHpurhp1ot+ZGvojLOsrHa9frzOVruOv4oPYMZ6wQomi9KsbZZ+Af/CuRYaGReTyGtEg==
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-progressive-custom-properties@^1.1.0", "@csstools/postcss-progressive-custom-properties@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@csstools/postcss-progressive-custom-properties/-/postcss-progressive-custom-properties-1.2.0.tgz"
  integrity sha512-YLpFPK5OaLIRKZhUfnrZPT9s9cmtqltIOg7W6jPcxmiDpnZ4lk+odfufZttOAgcg6IHWvNLgcITSLpJxIQB/qQ==
  dependencies:
    postcss-value-parser "^4.2.0"

"@ctrl/tinycolor@^3.4.0":
  version "3.4.0"
  resolved "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.4.0.tgz"
  integrity sha512-JZButFdZ1+/xAfpguQHoabIXkcqRRKpMrWKBkpEZZyxfY9C1DpADFB8PEqGSTeFr135SaTRfKqGKx5xSCLI7ZQ==

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  resolved "https://mirrors.tencent.com/npm/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
  integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  version "4.10.0"
  resolved "https://mirrors.tencent.com/npm/@eslint-community/regexpp/-/regexpp-4.10.0.tgz"
  integrity sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==

"@eslint/eslintrc@^2.1.3":
  version "2.1.3"
  resolved "https://mirrors.tencent.com/npm/@eslint/eslintrc/-/eslintrc-2.1.3.tgz"
  integrity sha512-yZzuIG+jnVu6hNSzFEN07e8BxF3uAzYtQb6uDkaYZLo6oYZDCq454c5kB8zxnzfCYyP4MIuyBn10L0DqwujTmA==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.53.0":
  version "8.53.0"
  resolved "https://mirrors.tencent.com/npm/@eslint/js/-/js-8.53.0.tgz"
  integrity sha512-Kn7K8dx/5U6+cT1yEhpX1w4PCSg0M+XyRILPgvwcEBjerFWCwQj5sbr3/VmxqV0JGHCBCzyd6LxypEuehypY1w==

"@fluentui/date-time-utilities@^8.3.0":
  version "8.3.0"
  resolved "https://registry.npmjs.org/@fluentui/date-time-utilities/-/date-time-utilities-8.3.0.tgz"
  integrity sha512-shSWwarh+ueDF22hIRL1T9bGA4epsP8QAIfYhpoK5ySr0Zg5RjqdNMM5IXzlTiXqE/LGe16CVrvyW2ksqfvQPg==
  dependencies:
    "@fluentui/set-version" "^8.1.5"
    tslib "^2.1.0"

"@fluentui/dom-utilities@^2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@fluentui/dom-utilities/-/dom-utilities-2.1.5.tgz"
  integrity sha512-OLDYV5ZGIiK/JXx/DFFib4vSa7PELvznbdAujDcX2wjt3V3Lt2N5ucv59JsVxk5LlwXjasUHJI2NZadagmnM6A==
  dependencies:
    "@fluentui/set-version" "^8.1.5"
    tslib "^2.1.0"

"@fluentui/font-icons-mdl2@^8.1.25":
  version "8.1.25"
  resolved "https://registry.npmjs.org/@fluentui/font-icons-mdl2/-/font-icons-mdl2-8.1.25.tgz"
  integrity sha512-HeKTiEqBzP1vWlJeZzeVHCKvd81JwqgFv8Sv3KLZEewqZ/JkKBVwRbCFAI5FCx0G3nwX0C1hsLHxVqST9IlEyA==
  dependencies:
    "@fluentui/set-version" "^8.1.5"
    "@fluentui/style-utilities" "^8.5.7"
    tslib "^2.1.0"

"@fluentui/foundation-legacy@^8.1.24":
  version "8.1.24"
  resolved "https://registry.npmjs.org/@fluentui/foundation-legacy/-/foundation-legacy-8.1.24.tgz"
  integrity sha512-Buj0Czbs/+VrPV8UdO7ycFGaHw5nmmVPLxzdQauLJhOBmgCFz4GpA0XMBoWTEMQVRC5ttXkHxdUSwuQzF7HDbA==
  dependencies:
    "@fluentui/merge-styles" "^8.3.0"
    "@fluentui/set-version" "^8.1.5"
    "@fluentui/style-utilities" "^8.5.7"
    "@fluentui/utilities" "^8.4.3"
    tslib "^2.1.0"

"@fluentui/keyboard-key@^0.3.5":
  version "0.3.5"
  resolved "https://registry.npmjs.org/@fluentui/keyboard-key/-/keyboard-key-0.3.5.tgz"
  integrity sha512-qPNPnRtkC92b8Zjx3mJ6+vRX+pdmbDYcXP8zXb2NJ/briAQXYmyqdjJLUl2riVBcAC4H3cL6dTKLR9VAyqhdYQ==
  dependencies:
    tslib "^2.1.0"

"@fluentui/merge-styles@^8.3.0":
  version "8.3.0"
  resolved "https://registry.npmjs.org/@fluentui/merge-styles/-/merge-styles-8.3.0.tgz"
  integrity sha512-qI61vWnmSuZYopMfWbEkTuDmnedmOYmsphTwflG6zif1EPV6h5u3dDUfHnmHMahywNOLOv5MOa+zVU0SCEHSAg==
  dependencies:
    "@fluentui/set-version" "^8.1.5"
    tslib "^2.1.0"

"@fluentui/react-focus@^8.3.20":
  version "8.3.20"
  resolved "https://registry.npmjs.org/@fluentui/react-focus/-/react-focus-8.3.20.tgz"
  integrity sha512-wtNf0N8tuXSKefWsSGWDah6usZ+Em4l61kkH8IsV9r0AafVjafoTeTQkbtvIHYfh6CcMnpcuGc3D0XpVzolUmg==
  dependencies:
    "@fluentui/keyboard-key" "^0.3.5"
    "@fluentui/merge-styles" "^8.3.0"
    "@fluentui/set-version" "^8.1.5"
    "@fluentui/style-utilities" "^8.5.7"
    "@fluentui/utilities" "^8.4.3"
    tslib "^2.1.0"

"@fluentui/react-hooks@^8.3.14":
  version "8.3.14"
  resolved "https://registry.npmjs.org/@fluentui/react-hooks/-/react-hooks-8.3.14.tgz"
  integrity sha512-dH2WQPfnegHrspoM/6mpyru3wbvT5bn3zwuHvJhkOrmOZh+tyg0VQlJz8NI+HSED9yFKkccgbx1genbgX0W/MQ==
  dependencies:
    "@fluentui/react-window-provider" "^2.1.6"
    "@fluentui/set-version" "^8.1.5"
    "@fluentui/utilities" "^8.4.3"
    tslib "^2.1.0"

"@fluentui/react-window-provider@^2.1.6":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@fluentui/react-window-provider/-/react-window-provider-2.1.6.tgz"
  integrity sha512-Fr1THmtn/Kx95/WVXI1qIELX2VHpuGYVKWSpdgbpcmKukvk30h4JG6kKMGYPz3r7UeaTzpaeIw537A9CJ2hVMg==
  dependencies:
    "@fluentui/set-version" "^8.1.5"
    tslib "^2.1.0"

"@fluentui/react@^8.5.1":
  version "8.56.1"
  resolved "https://registry.npmjs.org/@fluentui/react/-/react-8.56.1.tgz"
  integrity sha512-rjYWRWfWH4i9QPpiaogrP3uLPCHE0CkiRGIdxIZNEFTww38apaDh7t2B65IBbPJMsxyJK2WdtcjSSfW4HL7J+w==
  dependencies:
    "@fluentui/date-time-utilities" "^8.3.0"
    "@fluentui/font-icons-mdl2" "^8.1.25"
    "@fluentui/foundation-legacy" "^8.1.24"
    "@fluentui/merge-styles" "^8.3.0"
    "@fluentui/react-focus" "^8.3.20"
    "@fluentui/react-hooks" "^8.3.14"
    "@fluentui/react-window-provider" "^2.1.6"
    "@fluentui/set-version" "^8.1.5"
    "@fluentui/style-utilities" "^8.5.7"
    "@fluentui/theme" "^2.4.11"
    "@fluentui/utilities" "^8.4.3"
    "@microsoft/load-themed-styles" "^1.10.26"
    tslib "^2.1.0"

"@fluentui/set-version@^8.1.5":
  version "8.1.5"
  resolved "https://registry.npmjs.org/@fluentui/set-version/-/set-version-8.1.5.tgz"
  integrity sha512-AfaycaduWd/aErqEmrAUWpr2gpZrkaSe6D9noXhtVH3JlreRuFM78Ji1oE4f8cpWxSA/K5qb7BT6x4z4I2Bs+A==
  dependencies:
    tslib "^2.1.0"

"@fluentui/style-utilities@^8.5.7":
  version "8.5.7"
  resolved "https://registry.npmjs.org/@fluentui/style-utilities/-/style-utilities-8.5.7.tgz"
  integrity sha512-MyUjiWzilNbBRVSMw5Kd2Nh1feEcp/VFzYXmKabxg6vOo2qZkxgmpDU3dSxssvuxsrJCD/ADytiA/qgGzzeiLQ==
  dependencies:
    "@fluentui/merge-styles" "^8.3.0"
    "@fluentui/set-version" "^8.1.5"
    "@fluentui/theme" "^2.4.11"
    "@fluentui/utilities" "^8.4.3"
    "@microsoft/load-themed-styles" "^1.10.26"
    tslib "^2.1.0"

"@fluentui/theme@^2.4.11":
  version "2.4.11"
  resolved "https://registry.npmjs.org/@fluentui/theme/-/theme-2.4.11.tgz"
  integrity sha512-+g/4Z2OslFsMylsobhJkwY8pTp2He3ox+HDGmQVP5Mc0MeC5JP28XIp14rVNrjguczsJt0Jmh+ollfEXojVpcA==
  dependencies:
    "@fluentui/merge-styles" "^8.3.0"
    "@fluentui/set-version" "^8.1.5"
    "@fluentui/utilities" "^8.4.3"
    tslib "^2.1.0"

"@fluentui/utilities@^8.4.3":
  version "8.4.3"
  resolved "https://registry.npmjs.org/@fluentui/utilities/-/utilities-8.4.3.tgz"
  integrity sha512-V4B+jIfA/pqNAergudOM//UMlxHas2DSrSgmsCL/8drfNcbiR5JzZDoD2sZtzdBXbo9xdCYuz+PpQuGRYVmpUg==
  dependencies:
    "@fluentui/dom-utilities" "^2.1.5"
    "@fluentui/merge-styles" "^8.3.0"
    "@fluentui/set-version" "^8.1.5"
    tslib "^2.1.0"

"@humanwhocodes/config-array@^0.11.13":
  version "0.11.13"
  resolved "https://mirrors.tencent.com/npm/@humanwhocodes/config-array/-/config-array-0.11.13.tgz"
  integrity sha512-JSBDMiDKSzQVngfRjOdFXgFfklaXI4K9nLF49Auh21lmBWRLIK3+xTErTWD4KU54pb6coM6ESE7Awz/FNU3zgQ==
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.1"
    debug "^4.1.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://mirrors.tencent.com/npm/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/object-schema@^2.0.1":
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/@humanwhocodes/object-schema/-/object-schema-2.0.1.tgz"
  integrity sha512-dvuCeX5fC9dXgJn9t+X5atfmgQAzUOWqS1254Gh0m6i8wKd10ebXkfNKiRK+1GWi/yTvvLDHpoxLr0xxxeslWw==

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  integrity sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  integrity sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==

"@jest/console@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/console/-/console-27.5.1.tgz"
  integrity sha512-kZ/tNpS3NXn0mlXXXPNuDZnb4c0oZ20r4K5eemM2k30ZC3G0T02nXUvyhf5YdbXWHPEJLc9qGLxEZ216MdL+Zg==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^27.5.1"
    jest-util "^27.5.1"
    slash "^3.0.0"

"@jest/core@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/core/-/core-27.5.1.tgz"
  integrity sha512-AK6/UTrvQD0Cd24NSqmIA6rKsu0tKIxfiCducZvqxYdmMisOYAsdItspT+fQDQYARPf8XgjAFZi0ogW2agH5nQ==
  dependencies:
    "@jest/console" "^27.5.1"
    "@jest/reporters" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.8.1"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^27.5.1"
    jest-config "^27.5.1"
    jest-haste-map "^27.5.1"
    jest-message-util "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-resolve-dependencies "^27.5.1"
    jest-runner "^27.5.1"
    jest-runtime "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    jest-watcher "^27.5.1"
    micromatch "^4.0.4"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/environment/-/environment-27.5.1.tgz"
  integrity sha512-/WQjhPJe3/ghaol/4Bq480JKXV/Rfw8nQdN7f41fM8VDHLcxKXou6QyXAh3EFr9/bVG3x74z1NWDkP87EiY8gA==
  dependencies:
    "@jest/fake-timers" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    jest-mock "^27.5.1"

"@jest/fake-timers@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.5.1.tgz"
  integrity sha512-/aPowoolwa07k7/oM3aASneNeBGCmGQsc3ugN4u6s4C/+s5M64MFo/+djTdiwcbQlRfFElGuDXWzaWj6QgKObQ==
  dependencies:
    "@jest/types" "^27.5.1"
    "@sinonjs/fake-timers" "^8.0.1"
    "@types/node" "*"
    jest-message-util "^27.5.1"
    jest-mock "^27.5.1"
    jest-util "^27.5.1"

"@jest/globals@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/globals/-/globals-27.5.1.tgz"
  integrity sha512-ZEJNB41OBQQgGzgyInAv0UUfDDj3upmHydjieSxFvTRuZElrx7tXg/uVQ5hYVEwiXs3+aMsAeEc9X7xiSKCm4Q==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/types" "^27.5.1"
    expect "^27.5.1"

"@jest/reporters@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/reporters/-/reporters-27.5.1.tgz"
  integrity sha512-cPXh9hWIlVJMQkVk84aIvXuBB4uQQmFqZiacloFuGiP3ah1sbCxCosidXFDfqG8+6fO1oR2dTJTlsOy4VFmUfw==
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^5.1.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-haste-map "^27.5.1"
    jest-resolve "^27.5.1"
    jest-util "^27.5.1"
    jest-worker "^27.5.1"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^4.0.1"
    terminal-link "^2.0.0"
    v8-to-istanbul "^8.1.0"

"@jest/source-map@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/source-map/-/source-map-27.5.1.tgz"
  integrity sha512-y9NIHUYF3PJRlHk98NdC/N1gl88BL08aQQgu4k4ZopQkCw9t9cV8mtl3TV8b/YCB8XaVTFrmUTAJvjsntDireg==
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.9"
    source-map "^0.6.0"

"@jest/test-result@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/test-result/-/test-result-27.5.1.tgz"
  integrity sha512-EW35l2RYFUcUQxFJz5Cv5MTOxlJIQs4I7gxzi2zVU7PJhOwfYq1MdC5nhSmYjX1gmMmLPvB3sIaC+BkcHRBfag==
  dependencies:
    "@jest/console" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.5.1.tgz"
  integrity sha512-LCheJF7WB2+9JuCS7VB/EmGIdQuhtqjRNI9A43idHv3E4KltCTsPsLxvdaubFHSYwY/fNjMWjl6vNRhDiN7vpQ==
  dependencies:
    "@jest/test-result" "^27.5.1"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-runtime "^27.5.1"

"@jest/transform@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/transform/-/transform-27.5.1.tgz"
  integrity sha512-ipON6WtYgl/1329g5AIJVbUuEh0wZVbdpGwC99Jw4LwuoBNS95MVphU6zOeD9pDkon+LLbFL7lOQRapbB8SCHw==
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^27.5.1"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-util "^27.5.1"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/types@^27.5.1":
  version "27.5.1"
  resolved "https://registry.npmjs.org/@jest/types/-/types-27.5.1.tgz"
  integrity sha512-Cx46iJ9QpwQTjIdq5VJu2QTMMs3QlEjI0x1QbBP5W1+nMzyc2XmimiRR/CbX9TO0cPTeUlxWMOu8mslYsJ8DEw==
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^16.0.0"
    chalk "^4.0.0"

"@jridgewell/resolve-uri@^3.0.3":
  version "3.0.5"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.5.tgz"
  integrity sha512-VPeQ7+wH0itvQxnG+lIzWgkysKIr3L9sslimFW55rHMdGu/qCQ5z5h9zq4gI8uBtqkpHhsF4Z/OwExufUCThew==

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.11"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.11.tgz"
  integrity sha512-Fg32GrJo61m+VqYSdRSjRXMjQ06j8YIYfcTqndLYVAaHmroZHLJZCydsWBOTDqXS2v+mjxohBWEMfg97GXmYQg==

"@jridgewell/trace-mapping@^0.3.0":
  version "0.3.4"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.4.tgz"
  integrity sha512-vFv9ttIedivx0ux3QSjhgtCVjPZd5l46ZOMDSCwnH1yUO2e964gO8LZGyv2QkqcgR6TnBU1v+1IFqmeoG+0UJQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@microsoft/load-themed-styles@^1.10.26":
  version "1.10.247"
  resolved "https://registry.npmjs.org/@microsoft/load-themed-styles/-/load-themed-styles-1.10.247.tgz"
  integrity sha512-vKbuG3Mcbc4kkNAcIE13aIv5KoI2g+tHFFIZnFhtUilpYHc0VsMd4Fw7Jz81A8AB7L3wWu3OZB2CNiRnr1a3ew==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pmmmwh/react-refresh-webpack-plugin@^0.5.3":
  version "0.5.4"
  resolved "https://registry.npmjs.org/@pmmmwh/react-refresh-webpack-plugin/-/react-refresh-webpack-plugin-0.5.4.tgz"
  integrity sha512-zZbZeHQDnoTlt2AF+diQT0wsSXpvWiaIOZwBRdltNFhG1+I3ozyaw7U/nBiUwyJ0D+zwdXp0E3bWOl38Ag2BMw==
  dependencies:
    ansi-html-community "^0.0.8"
    common-path-prefix "^3.0.0"
    core-js-pure "^3.8.1"
    error-stack-parser "^2.0.6"
    find-up "^5.0.0"
    html-entities "^2.1.0"
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"
    source-map "^0.7.3"

"@polka/url@^1.0.0-next.20":
  version "1.0.0-next.21"
  resolved "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.21.tgz"
  integrity sha512-a5Sab1C4/icpTZVzZc5Ghpz88yQtGOyNqYXcZgOssB2uuAr+wF/MvN6bgtW32q7HHrvBki+BsZ0OuNv6EV3K9g==

"@reduxjs/toolkit@^1.5.1":
  version "1.8.0"
  resolved "https://registry.npmjs.org/@reduxjs/toolkit/-/toolkit-1.8.0.tgz"
  integrity sha512-cdfHWfcvLyhBUDicoFwG1u32JqvwKDxLxDd7zSmSoFw/RhYLOygIRtmaMjPRUUHmVmmAGAvquLLsKKU/677kSQ==
  dependencies:
    immer "^9.0.7"
    redux "^4.1.2"
    redux-thunk "^2.4.1"
    reselect "^4.1.5"

"@rollup/plugin-babel@^5.2.0":
  version "5.3.1"
  resolved "https://registry.npmjs.org/@rollup/plugin-babel/-/plugin-babel-5.3.1.tgz"
  integrity sha512-WFfdLWU/xVWKeRQnKmIAQULUI7Il0gZnBIH/ZFO069wYIfPu+8zrfp/KMW0atmELoRDq8FbiP3VCss9MhCut7Q==
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@rollup/pluginutils" "^3.1.0"

"@rollup/plugin-node-resolve@^11.2.1":
  version "11.2.1"
  resolved "https://registry.npmjs.org/@rollup/plugin-node-resolve/-/plugin-node-resolve-11.2.1.tgz"
  integrity sha512-yc2n43jcqVyGE2sqV5/YCmocy9ArjVAP/BeXyTtADTBBX6V0e5UMqwO8CdQ0kzjb6zu5P1qMzsScCMRvE9OlVg==
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    "@types/resolve" "1.17.1"
    builtin-modules "^3.1.0"
    deepmerge "^4.2.2"
    is-module "^1.0.0"
    resolve "^1.19.0"

"@rollup/plugin-replace@^2.4.1":
  version "2.4.2"
  resolved "https://registry.npmjs.org/@rollup/plugin-replace/-/plugin-replace-2.4.2.tgz"
  integrity sha512-IGcu+cydlUMZ5En85jxHH4qj2hta/11BHq95iHEyb2sbgiN0eCdzvUcHw5gt9pBL5lTi4JDYJ1acCoMGpTvEZg==
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    magic-string "^0.25.7"

"@rollup/pluginutils@^3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-3.1.0.tgz"
  integrity sha512-GksZ6pr6TpIjHm8h9lSQ8pi8BE9VeubNT0OMJ3B5uZJ8pz73NPiqOtCog/x2/QzM1ENChPKxMDhiQuRHsqc+lg==
  dependencies:
    "@types/estree" "0.0.39"
    estree-walker "^1.0.1"
    picomatch "^2.2.2"

"@rushstack/eslint-patch@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.1.0.tgz"
  integrity sha512-JLo+Y592QzIE+q7Dl2pMUtt4q8SKYI5jDrZxrozEQxnGVOyYE+GWK9eLkwTaeN9DDctlaRAQ3TBmzZ1qdLE30A==

"@sinonjs/commons@^1.7.0":
  version "1.8.3"
  resolved "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.3.tgz"
  integrity sha512-xkNcLAn/wZaX14RPlwizcKicDk9G3F8m2nU3L7Ukm5zBgTwiT0wsoFAHx9Jq56fJA1z/7uKGtCRu16sOUCLIHQ==
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^8.0.1":
  version "8.1.0"
  resolved "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-8.1.0.tgz"
  integrity sha512-OAPJUAtgeINhh/TAlUID4QTs53Njm7xzddaVlEs/SXwgtiD1tW22zAB/W1wdqfrpmikgaWQ9Fw6Ws+hsiRm5Vg==
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@surma/rollup-plugin-off-main-thread@^2.2.3":
  version "2.2.3"
  resolved "https://registry.npmjs.org/@surma/rollup-plugin-off-main-thread/-/rollup-plugin-off-main-thread-2.2.3.tgz"
  integrity sha512-lR8q/9W7hZpMWweNiAKU7NQerBnzQQLvi8qnTDU/fxItPhtZVMbPV3lbCwjhIlNBe9Bbr5V+KHshvWmVSG9cxQ==
  dependencies:
    ejs "^3.1.6"
    json5 "^2.2.0"
    magic-string "^0.25.0"
    string.prototype.matchall "^4.0.6"

"@svgr/babel-plugin-add-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-5.4.0.tgz"
  integrity sha512-ZFf2gs/8/6B8PnSofI0inYXr2SDNTDScPXhN7k5EqD4aZ3gi6u+rbmZHVB8IM3wDyx8ntKACZbtXSm7oZGRqVg==

"@svgr/babel-plugin-remove-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-5.4.0.tgz"
  integrity sha512-yaS4o2PgUtwLFGTKbsiAy6D0o3ugcUhWK0Z45umJ66EPWunAz9fuFw2gJuje6wqQvQWOTJvIahUwndOXb7QCPg==

"@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1":
  version "5.0.1"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz"
  integrity sha512-LA72+88A11ND/yFIMzyuLRSMJ+tRKeYKeQ+mR3DcAZ5I4h5CPWN9AHyUzJbWSYp/u2u0xhmgOe0+E41+GjEueA==

"@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1":
  version "5.0.1"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz"
  integrity sha512-PoiE6ZD2Eiy5mK+fjHqwGOS+IXX0wq/YDtNyIgOrc6ejFnxN4b13pRpiIPbtPwHEc+NT2KCjteAcq33/F1Y9KQ==

"@svgr/babel-plugin-svg-dynamic-title@^5.4.0":
  version "5.4.0"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-5.4.0.tgz"
  integrity sha512-zSOZH8PdZOpuG1ZVx/cLVePB2ibo3WPpqo7gFIjLV9a0QsuQAzJiwwqmuEdTaW2pegyBE17Uu15mOgOcgabQZg==

"@svgr/babel-plugin-svg-em-dimensions@^5.4.0":
  version "5.4.0"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-5.4.0.tgz"
  integrity sha512-cPzDbDA5oT/sPXDCUYoVXEmm3VIoAWAPT6mSPTJNbQaBNUuEKVKyGH93oDY4e42PYHRW67N5alJx/eEol20abw==

"@svgr/babel-plugin-transform-react-native-svg@^5.4.0":
  version "5.4.0"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-5.4.0.tgz"
  integrity sha512-3eYP/SaopZ41GHwXma7Rmxcv9uRslRDTY1estspeB1w1ueZWd/tPlMfEOoccYpEMZU3jD4OU7YitnXcF5hLW2Q==

"@svgr/babel-plugin-transform-svg-component@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-5.5.0.tgz"
  integrity sha512-q4jSH1UUvbrsOtlo/tKcgSeiCHRSBdXoIoqX1pgcKK/aU3JD27wmMKwGtpB8qRYUYoyXvfGxUVKchLuR5pB3rQ==

"@svgr/babel-preset@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-5.5.0.tgz"
  integrity sha512-4FiXBjvQ+z2j7yASeGPEi8VD/5rrGQk4Xrq3EdJmoZgz/tpqChpo5hgXDvmEauwtvOc52q8ghhZK4Oy7qph4ig==
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^5.0.1"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^5.0.1"
    "@svgr/babel-plugin-svg-dynamic-title" "^5.4.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^5.4.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^5.4.0"
    "@svgr/babel-plugin-transform-svg-component" "^5.5.0"

"@svgr/core@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@svgr/core/-/core-5.5.0.tgz"
  integrity sha512-q52VOcsJPvV3jO1wkPtzTuKlvX7Y3xIcWRpCMtBF3MrteZJtBfQw/+u0B1BHy5ColpQc1/YVTrPEtSYIMNZlrQ==
  dependencies:
    "@svgr/plugin-jsx" "^5.5.0"
    camelcase "^6.2.0"
    cosmiconfig "^7.0.0"

"@svgr/hast-util-to-babel-ast@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-5.5.0.tgz"
  integrity sha512-cAaR/CAiZRB8GP32N+1jocovUtvlj0+e65TB50/6Lcime+EA49m/8l+P2ko+XPJ4dw3xaPS3jOL4F2X4KWxoeQ==
  dependencies:
    "@babel/types" "^7.12.6"

"@svgr/plugin-jsx@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-5.5.0.tgz"
  integrity sha512-V/wVh33j12hGh05IDg8GpIUXbjAPnTdPTKuP4VNLggnwaHMPNQNae2pRnyTAILWCQdz5GyMqtO488g7CKM8CBA==
  dependencies:
    "@babel/core" "^7.12.3"
    "@svgr/babel-preset" "^5.5.0"
    "@svgr/hast-util-to-babel-ast" "^5.5.0"
    svg-parser "^2.0.2"

"@svgr/plugin-svgo@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@svgr/plugin-svgo/-/plugin-svgo-5.5.0.tgz"
  integrity sha512-r5swKk46GuQl4RrVejVwpeeJaydoxkdwkM1mBKOgJLBUJPGaLci6ylg/IjhrRsREKDkr4kbMWdgOtbXEh0fyLQ==
  dependencies:
    cosmiconfig "^7.0.0"
    deepmerge "^4.2.2"
    svgo "^1.2.2"

"@svgr/webpack@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@svgr/webpack/-/webpack-5.5.0.tgz"
  integrity sha512-DOBOK255wfQxguUta2INKkzPj6AIS6iafZYiYmHn6W3pHlycSRRlvWKCfLDG10fXfLWqE3DJHgRUOyJYmARa7g==
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/plugin-transform-react-constant-elements" "^7.12.1"
    "@babel/preset-env" "^7.12.1"
    "@babel/preset-react" "^7.12.5"
    "@svgr/core" "^5.5.0"
    "@svgr/plugin-jsx" "^5.5.0"
    "@svgr/plugin-svgo" "^5.5.0"
    loader-utils "^2.0.0"

"@tootallnate/once@1":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz"
  integrity sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==

"@trysound/sax@0.2.0":
  version "0.2.0"
  resolved "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz"
  integrity sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.14":
  version "7.1.18"
  resolved "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.18.tgz"
  integrity sha512-S7unDjm/C7z2A2R9NzfKCK1I+BAALDtxEmsJBwlB3EzNfb929ykjL++1CK9LO++EIp2fQrC8O+BwjKvz6UeDyQ==
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.4"
  resolved "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.4.tgz"
  integrity sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.1"
  resolved "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz"
  integrity sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  version "7.14.2"
  resolved "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.14.2.tgz"
  integrity sha512-K2waXdXBi2302XUdcHcR1jCeU0LL4TD9HRs/gk0N2Xvrht+G/BfJa4QObBQZfhMdxiCpV3COl5Nfq4uKTeTnJA==
  dependencies:
    "@babel/types" "^7.3.0"

"@types/body-parser@*":
  version "1.19.2"
  resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz"
  integrity sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  version "3.5.10"
  resolved "https://registry.npmjs.org/@types/bonjour/-/bonjour-3.5.10.tgz"
  integrity sha512-p7ienRMiS41Nu2/igbJxxLDWrSZ0WxM8UQgCeO9KhoVF7cOVFkrKsiDr1EsJIla8vV3oEEjGcz11jc5yimhzZw==
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.3.5":
  version "1.3.5"
  resolved "https://registry.npmjs.org/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz"
  integrity sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw==
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.35"
  resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz"
  integrity sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==
  dependencies:
    "@types/node" "*"

"@types/cookie@^0.4.0":
  version "0.4.1"
  resolved "https://registry.npmjs.org/@types/cookie/-/cookie-0.4.1.tgz"
  integrity sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==

"@types/eslint-scope@^3.7.3":
  version "3.7.3"
  resolved "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.3.tgz"
  integrity sha512-PB3ldyrcnAicT35TWPs5IcwKD8S333HMaa2VVv4+wdvebJkjWuW/xESoB8IwRcog8HYVYamb1g/R31Qv5Bx03g==
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.4.1"
  resolved "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.1.tgz"
  integrity sha512-GE44+DNEyxxh2Kc6ro/VkIj+9ma0pO0bwv9+uHSyBrikYOHr8zYcdPvnBOp1aw8s+CjRvuSx7CyWqRrNFQ59mA==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/eslint@^7.28.2":
  version "7.29.0"
  resolved "https://registry.npmjs.org/@types/eslint/-/eslint-7.29.0.tgz"
  integrity sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^0.0.51":
  version "0.0.51"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-0.0.51.tgz"
  integrity sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ==

"@types/estree@0.0.39":
  version "0.0.39"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-0.0.39.tgz"
  integrity sha512-EYNwp3bU+98cpU4lAWYYL7Zz+2gryWH1qbdDTidVd6hkiR6weksdbMadyXKXNPEkQFhXM+hVO9ZygomHXp+AIw==

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  version "4.17.28"
  resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.28.tgz"
  integrity sha512-P1BJAEAW3E2DJUlkgq4tOL3RyMunoWXqbSCygWo5ZIWTjUgN1YnaXWW4VWl/oc8vs/XoYibEGBKP0uZyF4AHig==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*", "@types/express@^4.17.13":
  version "4.17.13"
  resolved "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz"
  integrity sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/graceful-fs@^4.1.2":
  version "4.1.5"
  resolved "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.5.tgz"
  integrity sha512-anKkLmZZ+xm4p8JWBf4hElkM4XR+EZeA2M9BAkkTldmcyDY4mbdIJnRghDJH3Ov5ooY7/UAoENtmdMSkaAd7Cw==
  dependencies:
    "@types/node" "*"

"@types/history@^4.7.11":
  version "4.7.11"
  resolved "https://registry.npmjs.org/@types/history/-/history-4.7.11.tgz"
  integrity sha512-qjDJRrmvBMiTx+jyLxvLfJU7UznFuokDv4f3WRuriHKERccVpFU+8XMQUAbDzoiJCsmexxRExQeMwwCdamSKDA==

"@types/hoist-non-react-statics@^3.3.0":
  version "3.3.1"
  resolved "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz"
  integrity sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA==
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/html-minifier-terser@^6.0.0":
  version "6.1.0"
  resolved "https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  integrity sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==

"@types/http-proxy@^1.17.8":
  version "1.17.8"
  resolved "https://registry.npmjs.org/@types/http-proxy/-/http-proxy-1.17.8.tgz"
  integrity sha512-5kPLG5BKpWYkw/LVOGWpiq3nEVqxiN32rTgI53Sk12/xHFQ2rG3ehI9IO+O3W2QoKeyB92dJkoka8SUm6BX1pA==
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz"
  integrity sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  integrity sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz"
  integrity sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw==
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.4", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.9"
  resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz"
  integrity sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/mime@^1":
  version "1.3.2"
  resolved "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz"
  integrity sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw==

"@types/node@*":
  version "17.0.21"
  resolved "https://registry.npmjs.org/@types/node/-/node-17.0.21.tgz"
  integrity sha512-DBZCJbhII3r90XbQxI8Y9IjjiiOGlZ0Hr32omXIZvwwZ7p4DMMXGrKXVyPfuoBOri9XNtL0UK69jYIBIsRX3QQ==

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.0.tgz"
  integrity sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==

"@types/prettier@^2.1.5":
  version "2.4.4"
  resolved "https://registry.npmjs.org/@types/prettier/-/prettier-2.4.4.tgz"
  integrity sha512-ReVR2rLTV1kvtlWFyuot+d1pkpG2Fw/XKE3PDAdj57rbM97ttSp9JZ2UsP+2EHTylra9cUf6JA7tGwW1INzUrA==

"@types/prop-types@*":
  version "15.7.4"
  resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.4.tgz"
  integrity sha512-rZ5drC/jWjrArrS8BR6SIr4cWpW09RNTYt9AMZo3Jwwif+iacXAqgVjm0B0Bv/S1jhDXKHqRVNCbACkJ89RAnQ==

"@types/q@^1.5.1":
  version "1.5.5"
  resolved "https://registry.npmjs.org/@types/q/-/q-1.5.5.tgz"
  integrity sha512-L28j2FcJfSZOnL1WBjDYp2vUHCeIFlyYI/53EwD/rKUBQ7MtUUfbQWiyKJGpcnv4/WgrhWsFKrcPstcAt/J0tQ==

"@types/qs@*":
  version "6.9.7"
  resolved "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz"
  integrity sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==

"@types/range-parser@*":
  version "1.2.4"
  resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz"
  integrity sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==

"@types/react-dom@^17.0.0":
  version "17.0.11"
  resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-17.0.11.tgz"
  integrity sha512-f96K3k+24RaLGVu/Y2Ng3e1EbZ8/cVJvypZWd7cy0ofCBaf2lcM46xNhycMZ2xGwbBjRql7hOlZ+e2WlJ5MH3Q==
  dependencies:
    "@types/react" "*"

"@types/react-redux@^7.1.16", "@types/react-redux@^7.1.20":
  version "7.1.22"
  resolved "https://registry.npmjs.org/@types/react-redux/-/react-redux-7.1.22.tgz"
  integrity sha512-GxIA1kM7ClU73I6wg9IRTVwSO9GS+SAKZKe0Enj+82HMU6aoESFU2HNAdNi3+J53IaOHPiUfT3kSG4L828joDQ==
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"
    redux "^4.0.0"

"@types/react-router-dom@^5.1.7":
  version "5.3.3"
  resolved "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.3.3.tgz"
  integrity sha512-kpqnYK4wcdm5UaWI3fLcELopqLrHgLqNsdpHauzlQktfkHL3npOSwtj1Uz9oKBAzs7lFtVkV8j83voAz2D8fhw==
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router@*":
  version "5.1.18"
  resolved "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.18.tgz"
  integrity sha512-YYknwy0D0iOwKQgz9v8nOzt2J6l4gouBmDnWqUUznltOTaon+r8US8ky8HvN0tXvc38U9m6z/t2RsVsnd1zM0g==
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"

"@types/react@*", "@types/react@^17.0.0":
  version "17.0.39"
  resolved "https://registry.npmjs.org/@types/react/-/react-17.0.39.tgz"
  integrity sha512-UVavlfAxDd/AgAacMa60Azl7ygyQNRwC/DsHZmKgNvPmRR5p70AJ5Q9EAmL2NWOJmeV+vVUI4IAP7GZrN8h8Ug==
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/resolve@1.17.1":
  version "1.17.1"
  resolved "https://registry.npmjs.org/@types/resolve/-/resolve-1.17.1.tgz"
  integrity sha512-yy7HuzQhj0dhGpD8RLXSZWEkLsV9ibvxvi6EiJ3bkqLAO1RGo0WbkWQiwpRlSFymTJRz0d3k5LM3kkx8ArDbLw==
  dependencies:
    "@types/node" "*"

"@types/retry@^0.12.0":
  version "0.12.1"
  resolved "https://registry.npmjs.org/@types/retry/-/retry-0.12.1.tgz"
  integrity sha512-xoDlM2S4ortawSWORYqsdU+2rxdh4LRW9ytc3zmT37RIKQh6IHyKwwtKhKis9ah8ol07DCkZxPt8BBvPjC6v4g==

"@types/scheduler@*":
  version "0.16.2"
  resolved "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz"
  integrity sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew==

"@types/semver@^7.3.12":
  version "7.5.4"
  resolved "https://mirrors.tencent.com/npm/@types/semver/-/semver-7.5.4.tgz"
  integrity sha512-MMzuxN3GdFwskAnb6fz0orFvhfqi752yjaXylr0Rp4oDg5H0Zn1IuyRhDVvYOwAXoJirx2xuS16I3WjxnAIHiQ==

"@types/serve-index@^1.9.1":
  version "1.9.1"
  resolved "https://registry.npmjs.org/@types/serve-index/-/serve-index-1.9.1.tgz"
  integrity sha512-d/Hs3nWDxNL2xAczmOVZNj92YZCS6RGxfBPjKzuu/XirCgXdpKEb88dYNbrYGint6IVWLNP+yonwVAuRC0T2Dg==
  dependencies:
    "@types/express" "*"

"@types/serve-static@*":
  version "1.13.10"
  resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.10.tgz"
  integrity sha512-nCkHGI4w7ZgAdNkrEu0bv+4xNV/XDqW+DydknebMOQwkpDGx8G+HTlj7R7ABI8i8nKxVw0wtKPi1D+lPOkh4YQ==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/sockjs@^0.3.33":
  version "0.3.33"
  resolved "https://registry.npmjs.org/@types/sockjs/-/sockjs-0.3.33.tgz"
  integrity sha512-f0KEEe05NvUnat+boPTZ0dgaLZ4SfSouXUgv5noUiefG2ajgKjmETo9ZJyuqsl7dfl2aHlLJUiki6B4ZYldiiw==
  dependencies:
    "@types/node" "*"

"@types/stack-utils@^2.0.0":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.1.tgz"
  integrity sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw==

"@types/trusted-types@^2.0.2":
  version "2.0.2"
  resolved "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.2.tgz"
  integrity sha512-F5DIZ36YVLE+PN+Zwws4kJogq47hNgX3Nx6WyDJ3kcplxyke3XIzB8uK5n/Lpm1HBsbGzd6nmGehL8cPekP+Tg==

"@types/ws@^8.2.2":
  version "8.5.1"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-8.5.1.tgz"
  integrity sha512-UxlLOfkuQnT2YSBCNq0x86SGOUxas6gAySFeDe2DcnEnA8655UIPoCDorWZCugcvKIL8IUI4oueUfJ1hhZSE2A==
  dependencies:
    "@types/node" "*"

"@types/yargs-parser@*":
  version "20.2.1"
  resolved "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-20.2.1.tgz"
  integrity sha512-7tFImggNeNBVMsn0vLrpn1H1uPrUBdnARPTpZoitY37ZrdJREzf7I16tMrlK3hen349gr1NYh8CmZQa7CTG6Aw==

"@types/yargs@^16.0.0":
  version "16.0.4"
  resolved "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.4.tgz"
  integrity sha512-T8Yc9wt/5LbJyCaLiHPReJa0kApcIgJ7Bn735GjItUfh08Z1pJvu8QZqb9s+mMvKV6WUQRV7K2R46YbjMXTTJw==
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^5.40.0", "@typescript-eslint/eslint-plugin@^5.5.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.62.0.tgz"
  integrity sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@^5.0.0":
  version "5.12.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-5.12.1.tgz"
  integrity sha512-4bEa8WrS5DdzJq43smPH12ys4AOoCxVu2xjYGXQR4DnNyM8pqNzCr28zodf38Jc4bxWdniSEKKC1bQaccXGq5Q==
  dependencies:
    "@typescript-eslint/utils" "5.12.1"

"@typescript-eslint/parser@^4.29.1":
  version "4.33.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-4.33.0.tgz"
  integrity sha512-ZohdsbXadjGBSK0/r+d87X0SBmKzOq4/S5nzK6SBgJspFo9/CUDJ7hjayuze+JK7CZQLDMroqytp7pOcFKTxZA==
  dependencies:
    "@typescript-eslint/scope-manager" "4.33.0"
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/typescript-estree" "4.33.0"
    debug "^4.3.1"

"@typescript-eslint/parser@^5.5.0":
  version "5.12.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.12.1.tgz"
  integrity sha512-6LuVUbe7oSdHxUWoX/m40Ni8gsZMKCi31rlawBHt7VtW15iHzjbpj2WLiToG2758KjtCCiLRKZqfrOdl3cNKuw==
  dependencies:
    "@typescript-eslint/scope-manager" "5.12.1"
    "@typescript-eslint/types" "5.12.1"
    "@typescript-eslint/typescript-estree" "5.12.1"
    debug "^4.3.2"

"@typescript-eslint/scope-manager@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-4.33.0.tgz"
  integrity sha512-5IfJHpgTsTZuONKbODctL4kKuQje/bzBRkwHE8UOZ4f89Zeddg+EGZs8PD8NcN4LdM3ygHWYB3ukPAYjvl/qbQ==
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"

"@typescript-eslint/scope-manager@5.12.1":
  version "5.12.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.12.1.tgz"
  integrity sha512-J0Wrh5xS6XNkd4TkOosxdpObzlYfXjAFIm9QxYLCPOcHVv1FyyFCPom66uIh8uBr0sZCrtS+n19tzufhwab8ZQ==
  dependencies:
    "@typescript-eslint/types" "5.12.1"
    "@typescript-eslint/visitor-keys" "5.12.1"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz"
  integrity sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/type-utils/-/type-utils-5.62.0.tgz"
  integrity sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-4.33.0.tgz"
  integrity sha512-zKp7CjQzLQImXEpLt2BUw1tvOMPfNoTAfb8l51evhYbOEEzdWyQNmHWWGPR6hwKJDAi+1VXSBmnhL9kyVTTOuQ==

"@typescript-eslint/types@5.12.1":
  version "5.12.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.12.1.tgz"
  integrity sha512-hfcbq4qVOHV1YRdhkDldhV9NpmmAu2vp6wuFODL71Y0Ixak+FLeEU4rnPxgmZMnGreGEghlEucs9UZn5KOfHJA==

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/types/-/types-5.62.0.tgz"
  integrity sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==

"@typescript-eslint/typescript-estree@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-4.33.0.tgz"
  integrity sha512-rkWRY1MPFzjwnEVHsxGemDzqqddw2QbTJlICPD9p9I9LfsO8fdmfQPOX3uKfUaGRDFJbfrtm/sXhVXN4E+bzCA==
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"
    debug "^4.3.1"
    globby "^11.0.3"
    is-glob "^4.0.1"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/typescript-estree@5.12.1":
  version "5.12.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.12.1.tgz"
  integrity sha512-ahOdkIY9Mgbza7L9sIi205Pe1inCkZWAHE1TV1bpxlU4RZNPtXaDZfiiFWcL9jdxvW1hDYZJXrFm+vlMkXRbBw==
  dependencies:
    "@typescript-eslint/types" "5.12.1"
    "@typescript-eslint/visitor-keys" "5.12.1"
    debug "^4.3.2"
    globby "^11.0.4"
    is-glob "^4.0.3"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz"
  integrity sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.12.1", "@typescript-eslint/utils@^5.10.2":
  version "5.12.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.12.1.tgz"
  integrity sha512-Qq9FIuU0EVEsi8fS6pG+uurbhNTtoYr4fq8tKjBupsK5Bgbk2I32UGm0Sh+WOyjOPgo/5URbxxSNV6HYsxV4MQ==
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@typescript-eslint/scope-manager" "5.12.1"
    "@typescript-eslint/types" "5.12.1"
    "@typescript-eslint/typescript-estree" "5.12.1"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/utils/-/utils-5.62.0.tgz"
  integrity sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-4.33.0.tgz"
  integrity sha512-uqi/2aSz9g2ftcHWf8uLPJA70rUv6yuMW5Bohw+bwcuzaxQIHaKFZCKGoGXIrc9vkTJ3+0txM73K0Hq3d5wgIg==
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    eslint-visitor-keys "^2.0.0"

"@typescript-eslint/visitor-keys@5.12.1":
  version "5.12.1"
  resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.12.1.tgz"
  integrity sha512-l1KSLfupuwrXx6wc0AuOmC7Ko5g14ZOQ86wJJqRbdLbXLK02pK/DPiDDqCc7BqqiiA04/eAA6ayL0bgOrAkH7A==
  dependencies:
    "@typescript-eslint/types" "5.12.1"
    eslint-visitor-keys "^3.0.0"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "https://mirrors.tencent.com/npm/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz"
  integrity sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@ungap/structured-clone@^1.2.0":
  version "1.2.0"
  resolved "https://mirrors.tencent.com/npm/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
  integrity sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==

"@webassemblyjs/ast@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.1.tgz"
  integrity sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw==
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.1.tgz"
  integrity sha512-iGRfyc5Bq+NnNuX8b5hwBrRjzf0ocrJPI6GWFodBFzmFnyvrQ83SHKhmilCU/8Jv67i4GJZBMhEzltxzcNagtQ==

"@webassemblyjs/helper-api-error@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.1.tgz"
  integrity sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg==

"@webassemblyjs/helper-buffer@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.1.tgz"
  integrity sha512-gwikF65aDNeeXa8JxXa2BAk+REjSyhrNC9ZwdT0f8jc4dQQeDQ7G4m0f2QCLPJiMTTO6wfDmRmj/pW0PsUvIcA==

"@webassemblyjs/helper-numbers@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.1.tgz"
  integrity sha512-vDkbxiB8zfnPdNK9Rajcey5C0w+QJugEglN0of+kmO8l7lDb77AnlKYQF7aarZuCrv+l0UvqL+68gSDr3k9LPQ==
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.1.tgz"
  integrity sha512-PvpoOGiJwXeTrSf/qfudJhwlvDQxFgelbMqtq52WWiXC6Xgg1IREdngmPN3bs4RoO83PnL/nFrxucXj1+BX62Q==

"@webassemblyjs/helper-wasm-section@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.1.tgz"
  integrity sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.1.tgz"
  integrity sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.11.1.tgz"
  integrity sha512-BJ2P0hNZ0u+Th1YZXJpzW6miwqQUGcIHT1G/sf72gLVD9DZ5AdYTqPNbHZh6K1M5VmKvFXwGSWZADz+qBWxeRw==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.1.tgz"
  integrity sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ==

"@webassemblyjs/wasm-edit@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.1.tgz"
  integrity sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.1.tgz"
  integrity sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.1.tgz"
  integrity sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.1.tgz"
  integrity sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  version "1.11.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.1.tgz"
  integrity sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg==
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

abab@^2.0.3, abab@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/abab/-/abab-2.0.5.tgz"
  integrity sha512-9IK9EadsbHo6jLWIpxpR6pL0sazTXV6+SQv25ZB+F7Bj9mJNaOc4nCRabwd5M/JwmUa8idz6Eci6eKfJryPs6Q==

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-globals@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/acorn-globals/-/acorn-globals-6.0.0.tgz"
  integrity sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg==
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-import-assertions@^1.7.6:
  version "1.8.0"
  resolved "https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz"
  integrity sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw==

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://mirrors.tencent.com/npm/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn-node@^1.6.1:
  version "1.8.2"
  resolved "https://registry.npmjs.org/acorn-node/-/acorn-node-1.8.2.tgz"
  integrity sha512-8mt+fslDufLYntIoPAaIMUe/lrbrehIiwmR3t2k9LljIzoigEPF27eLk2hy8zSGzmR/ogr7zbRKINMo1u0yh5A==
  dependencies:
    acorn "^7.0.0"
    acorn-walk "^7.0.0"
    xtend "^4.0.2"

acorn-walk@^7.0.0, acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz"
  integrity sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA==

acorn-walk@^8.0.0:
  version "8.2.0"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.2.0.tgz"
  integrity sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==

acorn@^7.0.0, acorn@^7.1.1:
  version "7.4.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz"
  integrity sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==

acorn@^8.0.4, acorn@^8.2.4, acorn@^8.4.1, acorn@^8.5.0:
  version "8.7.0"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.7.0.tgz"
  integrity sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ==

acorn@^8.9.0:
  version "8.11.2"
  resolved "https://mirrors.tencent.com/npm/acorn/-/acorn-8.11.2.tgz"
  integrity sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==

address@^1.0.1, address@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/address/-/address-1.1.2.tgz"
  integrity sha512-aT6camzM4xEA54YVJYSqxz1kv4IHnQZRtThJJHhUMRExaU5spC7jX5ugSwTaTgJliIgs4VhZOk7htClvQ/LmRA==

adjust-sourcemap-loader@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz"
  integrity sha512-OXwN5b9pCUXNQHJpwwD2qP40byEmSgzj8B4ydSN0uMNYWiFmJ6x6KwUllMmfk8Rwu/HJDFR7U8ubsWBoN0Xp0A==
  dependencies:
    loader-utils "^2.0.0"
    regex-parser "^2.2.11"

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==

ajv-keywords@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  integrity sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.12.2, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.6.0, ajv@^8.8.0:
  version "8.10.0"
  resolved "https://registry.npmjs.org/ajv/-/ajv-8.10.0.tgz"
  integrity sha512-bzqAEZOjkrUMl2afH8dknrq5KEk2SrwdBROR+vH1EKVQTqaUbJVPdc/gEdggTMM0Se+s+Ja4ju4TlNcStKl2Hw==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-escapes@^4.2.1, ansi-escapes@^4.3.1:
  version "4.3.2"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://registry.npmjs.org/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  integrity sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz"
  integrity sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==

antd@4.17.4:
  version "4.17.4"
  resolved "https://registry.npmjs.org/antd/-/antd-4.17.4.tgz"
  integrity sha512-aiWi7TxAc7qAxbL412GSKpkWkL/wIhQe6ABuLLCiE1vqXnGTvav2Z0PiOUdFclZcfz2M2IofsUl2pLVN9I8iCg==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons" "^4.7.0"
    "@ant-design/react-slick" "~0.28.1"
    "@babel/runtime" "^7.12.5"
    "@ctrl/tinycolor" "^3.4.0"
    array-tree-filter "^2.1.0"
    classnames "^2.2.6"
    copy-to-clipboard "^3.2.0"
    lodash "^4.17.21"
    memoize-one "^6.0.0"
    moment "^2.25.3"
    rc-cascader "~2.3.0"
    rc-checkbox "~2.3.0"
    rc-collapse "~3.1.0"
    rc-dialog "~8.6.0"
    rc-drawer "~4.4.2"
    rc-dropdown "~3.2.0"
    rc-field-form "~1.21.0"
    rc-image "~5.2.5"
    rc-input-number "~7.3.0"
    rc-mentions "~1.6.1"
    rc-menu "~9.0.12"
    rc-motion "^2.4.4"
    rc-notification "~4.5.7"
    rc-pagination "~3.1.9"
    rc-picker "~2.5.17"
    rc-progress "~3.1.0"
    rc-rate "~2.9.0"
    rc-resize-observer "^1.1.2"
    rc-select "~13.2.1"
    rc-slider "~9.7.4"
    rc-steps "~4.1.0"
    rc-switch "~3.2.0"
    rc-table "~7.19.0"
    rc-tabs "~11.10.0"
    rc-textarea "~0.3.0"
    rc-tooltip "~5.1.1"
    rc-tree "~5.3.0"
    rc-tree-select "~4.8.0"
    rc-trigger "^5.2.10"
    rc-upload "~4.3.0"
    rc-util "^5.14.0"
    scroll-into-view-if-needed "^2.2.25"

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz"
  integrity sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.1.tgz"
  integrity sha512-e0hDa9H2Z9AwFkk2qDlwhoMYE4eToKarchkQHovNdLTCYMHZHeRjI71crOh+dio4K6u1IcwubQqo79Ga4CyAQA==

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://mirrors.tencent.com/npm/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

aria-query@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmjs.org/aria-query/-/aria-query-4.2.2.tgz"
  integrity sha512-o/HelwhuKpTj/frsOsbNLNgnNGVIFsVP/SW2BSF14gVl7kAfMOJ6/8wUAUvG1R1NHKrfG+2sHZTu0yauT1qBrA==
  dependencies:
    "@babel/runtime" "^7.10.2"
    "@babel/runtime-corejs3" "^7.10.2"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz"
  integrity sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==

array-includes@^3.1.3, array-includes@^3.1.4:
  version "3.1.4"
  resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.4.tgz"
  integrity sha512-ZTNSQkmWumEbiHO2GF4GmWxYVTiQyJy2XOTa15sdQSrvKn7l+180egQMqlrMOUMCyLMD7pmyQe4mMDUT6Behrw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    is-string "^1.0.7"

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-tree-filter/-/array-tree-filter-2.1.0.tgz"
  integrity sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

array.prototype.flat@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.2.5.tgz"
  integrity sha512-KaYU+S+ndVqyUnignHftkwc58o3uVU1jzczILJ1tN2YaIZpFIKBiP/x/j97E5MVPsaCloPbqWLB/8qCTVvT2qg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"

array.prototype.flatmap@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.2.5.tgz"
  integrity sha512-08u6rVyi1Lj7oqWbS9nUxliETrtIROT4XGTA4D/LWGten6E3ocm7cy9SIrmNHOL5XVbVuckUp3X6Xyg8/zpvHA==
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"

asap@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.7.tgz"
  integrity sha1-9wtzXGvKGlycItmCw+Oef+ujva0=

async-validator@^4.0.2:
  version "4.0.7"
  resolved "https://registry.npmjs.org/async-validator/-/async-validator-4.0.7.tgz"
  integrity sha512-Pj2IR7u8hmUEDOwB++su6baaRi+QvsgajuFB9j95foM1N2gy5HM4z60hfusIO0fBPG5uLAEl6yCJr1jNSVugEQ==

async@0.9.x:
  version "0.9.2"
  resolved "https://registry.npmjs.org/async/-/async-0.9.2.tgz"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

async@^2.6.2:
  version "2.6.3"
  resolved "https://registry.npmjs.org/async/-/async-2.6.3.tgz"
  integrity sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz"
  integrity sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==

autoprefixer@^10.4.2:
  version "10.4.2"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.2.tgz"
  integrity sha512-9fOPpHKuDW1w/0EKfRmVnxTDt8166MAnLI3mgZ1JCnhNtYWxcJ6Ud5CO/AVOZi/AvFa8DY9RTy3h3+tFBlrrdQ==
  dependencies:
    browserslist "^4.19.1"
    caniuse-lite "^1.0.30001297"
    fraction.js "^4.1.2"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

axe-core@^4.3.5:
  version "4.4.1"
  resolved "https://registry.npmjs.org/axe-core/-/axe-core-4.4.1.tgz"
  integrity sha512-gd1kmb21kwNuWr6BQz8fv6GNECPBnUasepcoLbekws23NVBLODdsClRZ+bQ8+9Uomf3Sm3+Vwn0oYG9NvwnJCw==

axios@^0.21.1:
  version "0.21.4"
  resolved "https://registry.npmjs.org/axios/-/axios-0.21.4.tgz"
  integrity sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==
  dependencies:
    follow-redirects "^1.14.0"

axobject-query@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/axobject-query/-/axobject-query-2.2.0.tgz"
  integrity sha512-Td525n+iPOOyUQIeBfcASuG6uJsDOITl7Mds5gFyerkWiX7qhUTdYUBlSgNMyVqtSJqwpt1kXGLdUt6SykLMRA==

babel-jest@^27.4.2, babel-jest@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/babel-jest/-/babel-jest-27.5.1.tgz"
  integrity sha512-cdQ5dXjGRd0IBRATiQ4mZGlGlRE8kJpjPOixdNRdT+m3UcNqmYWN6rK6nvtXYfY3D76cb8s/O1Ss8ea24PIwcg==
  dependencies:
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^27.5.1"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-loader@^8.2.3:
  version "8.2.3"
  resolved "https://registry.npmjs.org/babel-loader/-/babel-loader-8.2.3.tgz"
  integrity sha512-n4Zeta8NC3QAsuyiizu0GkmRcQ6clkV9WFUnUf1iXP//IeSKbWjofW3UHyZVwlOB4y039YQKefawyTn64Zwbuw==
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^1.4.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ==
  dependencies:
    object.assign "^4.1.0"

babel-plugin-import@^1.13.3:
  version "1.13.3"
  resolved "https://registry.npmjs.org/babel-plugin-import/-/babel-plugin-import-1.13.3.tgz"
  integrity sha512-1qCWdljJOrDRH/ybaCZuDgySii4yYrtQ8OJQwrcDqdt0y67N30ng3X3nABg6j7gR7qUJgcMa9OMhc4AGViDwWw==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  integrity sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.5.1.tgz"
  integrity sha512-50wCwD5EMNW4aRpOwtqzyZHIewTYNxLA4nhB+09d8BIssfNfzBRhkBIHiaPv1Si226TQSvp8gxAJm2iY2qs2hQ==
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  integrity sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

babel-plugin-named-asset-import@^0.3.8:
  version "0.3.8"
  resolved "https://registry.npmjs.org/babel-plugin-named-asset-import/-/babel-plugin-named-asset-import-0.3.8.tgz"
  integrity sha512-WXiAc++qo7XcJ1ZnTYGtLxmBCVbddAml3CEXgWaBzNzLNoxtQ8AiGEFDMOhot9XjTCQbvP5E77Fj9Gk924f00Q==

babel-plugin-polyfill-corejs2@^0.3.0:
  version "0.3.1"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.1.tgz"
  integrity sha512-v7/T6EQcNfVLfcN2X8Lulb7DjprieyLWJK/zOWH5DUYcAgex9sP3h25Q+DLsX9TloXe3y1O8l2q2Jv9q8UVB9w==
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.5.0:
  version "0.5.2"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.2.tgz"
  integrity sha512-G3uJih0XWiID451fpeFaYGVuxHEjzKTHtc9uGFEjR6hHrvNzeS/PX+LLLcetJcytsB5m4j+K3o/EpXJNb/5IEQ==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    core-js-compat "^3.21.0"

babel-plugin-polyfill-regenerator@^0.3.0:
  version "0.3.1"
  resolved "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.1.tgz"
  integrity sha512-Y2B06tvgHYt1x0yz17jGkGeeMr5FeKUu+ASJ+N6nB5lQ8Dapfg42i0OVrf8PNGJ3zKL4A23snMi1IRwrqqND7A==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"

babel-plugin-transform-react-remove-prop-types@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz"
  integrity sha512-eqj0hVcJUR57/Ug2zE1Yswsw4LhuqqHhD+8v120T1cl3kjg76QwtyBrdIk4WVwK+lAhBJVYCd/v+4nc4y+8JsA==

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz"
  integrity sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ==
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-jest@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.5.1.tgz"
  integrity sha512-Nptf2FzlPCWYuJg41HBqXVT8ym6bXOevuCTbhxlUpjwtysGaIWFvDEjp4y+G7fl13FgOdjs7P/DmErqH7da0Ag==
  dependencies:
    babel-plugin-jest-hoist "^27.5.1"
    babel-preset-current-node-syntax "^1.0.0"

babel-preset-react-app@^10.0.1:
  version "10.0.1"
  resolved "https://registry.npmjs.org/babel-preset-react-app/-/babel-preset-react-app-10.0.1.tgz"
  integrity sha512-b0D9IZ1WhhCWkrTXyFuIIgqGzSkRIH5D5AmB0bXbzYAB1OBAwHcUeyWW2LorutLWF5btNo/N7r/cIdmvvKJlYg==
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/plugin-proposal-class-properties" "^7.16.0"
    "@babel/plugin-proposal-decorators" "^7.16.4"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.0"
    "@babel/plugin-proposal-numeric-separator" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-private-methods" "^7.16.0"
    "@babel/plugin-transform-flow-strip-types" "^7.16.0"
    "@babel/plugin-transform-react-display-name" "^7.16.0"
    "@babel/plugin-transform-runtime" "^7.16.4"
    "@babel/preset-env" "^7.16.4"
    "@babel/preset-react" "^7.16.0"
    "@babel/preset-typescript" "^7.16.0"
    "@babel/runtime" "^7.16.3"
    babel-plugin-macros "^3.1.0"
    babel-plugin-transform-react-remove-prop-types "^0.4.24"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bfj@^7.0.2:
  version "7.0.2"
  resolved "https://registry.npmjs.org/bfj/-/bfj-7.0.2.tgz"
  integrity sha512-+e/UqUzwmzJamNF50tBV6tZPTORow7gQ96iFow+8b562OdMpEK0BcJEq2OSPEDmAbSMBQ7PKZ87ubFkgxpYWgw==
  dependencies:
    bluebird "^3.5.5"
    check-types "^11.1.1"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==

bluebird@^3.5.5:
  version "3.7.2"
  resolved "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==

body-parser@1.19.2:
  version "1.19.2"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.19.2.tgz"
  integrity sha512-SAAwOxgoCKMGs9uUAUFHygfLAyaniaoun6I8mFY9pRAJL9+Kec34aU+oIjDhTycub1jozEfEwx1W1IuOYxVSFw==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.8.1"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.9.7"
    raw-body "2.4.3"
    type-is "~1.6.18"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.npmjs.org/bonjour/-/bonjour-3.5.0.tgz"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^3.0.1, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz"
  integrity sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==

browserslist@^4.0.0, browserslist@^4.14.5, browserslist@^4.16.6, browserslist@^4.17.5, browserslist@^4.18.1, browserslist@^4.19.1:
  version "4.19.3"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.19.3.tgz"
  integrity sha512-XK3X4xtKJ+Txj8G5c30B4gsm71s69lqXlkYui4s6EkKxuv49qjYlY6oVd+IFJ73d4YymtM3+djvvt/R/iJwwDg==
  dependencies:
    caniuse-lite "^1.0.30001312"
    electron-to-chromium "^1.4.71"
    escalade "^3.1.1"
    node-releases "^2.0.2"
    picocolors "^1.0.0"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  integrity sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==
  dependencies:
    node-int64 "^0.4.0"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/buffer-indexof/-/buffer-indexof-1.1.1.tgz"
  integrity sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g==

builtin-modules@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/builtin-modules/-/builtin-modules-3.2.0.tgz"
  integrity sha512-lGzLKcioL90C7wMczpkY0n/oART3MbBa8R9OFGE1rJxoVI86u4WAGfEk8Wjv10eKSyTHVGkSo3bvBylCEtk7LA==

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
  integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz"
  integrity sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

camelcase@^6.2.0, camelcase@^6.2.1:
  version "6.3.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz"
  integrity sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001297, caniuse-lite@^1.0.30001312:
  version "1.0.30001312"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001312.tgz"
  integrity sha512-Wiz1Psk2MEK0pX3rUzWaunLTZzqS2JYZFzNKqAiJGiuxIjRPLgV6+VDPOg6lQOUxmDwhTlh198JsTTi8Hzw6aQ==

case-sensitive-paths-webpack-plugin@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  integrity sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw==

chalk@^2.0.0, chalk@^2.3.2, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz"
  integrity sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==

char-regex@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/char-regex/-/char-regex-2.0.1.tgz"
  integrity sha512-oSvEeo6ZUD7NepqAat3RqoucZ5SeqLJgOvVIwkafu6IP3V0pO38s/ypdVUmDDK6qIIHNlYHJAKX9E7R7HoKElw==

charcodes@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/charcodes/-/charcodes-0.2.0.tgz"
  integrity sha512-Y4kiDb+AM4Ecy58YkuZrrSRJBDQdQ2L+NyS1vHHFtNtUjgutcZfx3yp1dAONI/oPaPmyGfCLx5CxL+zauIMyKQ==

check-types@^11.1.1:
  version "11.1.2"
  resolved "https://registry.npmjs.org/check-types/-/check-types-11.1.2.tgz"
  integrity sha512-tzWzvgePgLORb9/3a0YenggReLKAIb2owL03H2Xdoe5pKcUyWRSEQ8xfCar8t2SIAuEDwtmx2da1YB52YuHQMQ==

chokidar@^3.4.2, chokidar@^3.5.3:
  version "3.5.3"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  integrity sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==

ci-info@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/ci-info/-/ci-info-3.3.0.tgz"
  integrity sha512-riT/3vI5YpVH6/qomlDnJow6TBee2PBKSEpx3O32EGPYbWGIRsIlGRms3Sm74wYE1JMo8RnO04Hb12+v1J5ICw==

cjs-module-lexer@^1.0.0:
  version "1.2.2"
  resolved "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.2.tgz"
  integrity sha512-cOU9usZw8/dXIXKtwa8pM0OTJQuJkxMN6w30csNRUerHfeQ5R6U3kkU/FtJeIf3M202OHfY2U8ccInBG7/xogA==

classcat@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/classcat/-/classcat-5.0.3.tgz"
  integrity sha512-6dK2ke4VEJZOFx2ZfdDAl5OhEL8lvkl6EHF92IfRePfHxQTqir5NlcNVUv+2idjDqCX2NDc8m8YSAI5NI975ZQ==

classnames@2.x, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.5, classnames@^2.2.6, classnames@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/classnames/-/classnames-2.3.1.tgz"
  integrity sha512-OlQdbZ7gLfGarSqxesMesDa5uz7KFbID8Kpq/SxIoNGDqY8lSYs0D+hhtBXhcdB3rcbXArFr7vlHheLk1voeNA==

clean-css@^5.2.2:
  version "5.2.4"
  resolved "https://registry.npmjs.org/clean-css/-/clean-css-5.2.4.tgz"
  integrity sha512-nKseG8wCzEuji/4yrgM/5cthL9oTDc5UOQyFMvW/Q53oP6gLH690o1NbuTh6Y18nujr7BxlsFuS7gXLnLzKJGg==
  dependencies:
    source-map "~0.6.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz"
  integrity sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clsx@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.1.1.tgz"
  integrity sha512-6/bPho624p3S2pMyvP5kKBPXnI3ufHLObBFCfgx+LkeR5lg2XYy2hqZqUf45ypD8COn2bhgGJSUE+l5dhNBieA==

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

coa@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/coa/-/coa-2.0.2.tgz"
  integrity sha512-q5/jG+YQnSy4nRTV4F7lPepBJZ8qBNJJDBuJdoejDyLXgmL7IEo+Le2JDZudFTFt7mrCqIRaSjws4ygRCTCAXA==
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

codemirror@^5.65.2:
  version "5.65.15"
  resolved "https://mirrors.tencent.com/npm/codemirror/-/codemirror-5.65.15.tgz"
  integrity sha512-YC4EHbbwQeubZzxLl5G4nlbLc1T21QTrKGaOal/Pkm9dVDMZXMH7+ieSPEOZCtO9I68i8/oteJKOxzHC2zR+0g==

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.1.tgz"
  integrity sha512-iBPtljfCNcTKNAto0KEtDfZ3qzjJvqE3aTGZsbhjSBlorqpXJlaWWtPO35D+ZImoC3KWejX64o+yPGxhWSTzfg==

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.1.4, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

colord@^2.9.1:
  version "2.9.2"
  resolved "https://registry.npmjs.org/colord/-/colord-2.9.2.tgz"
  integrity sha512-Uqbg+J445nc1TKn4FoDPS6ZZqAvEDnwrH42yo8B40JSOgSLxMZ/gt3h4nmCtPLQeXhjJJkqBx7SCY35WnIixaQ==

colorette@^2.0.10:
  version "2.0.16"
  resolved "https://registry.npmjs.org/colorette/-/colorette-2.0.16.tgz"
  integrity sha512-hUewv7oMjCp+wkBv5Rm0v87eJhq4woh5rSR+42YSQJKecCqgIqNkZ6lAlQms/BwHPJA5NKMRlpxPRv0n8HQW6g==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.15.1, commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@^7.2.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz"
  integrity sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==

commander@^8.3.0:
  version "8.3.0"
  resolved "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz"
  integrity sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==

common-path-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/common-path-prefix/-/common-path-prefix-3.0.0.tgz"
  integrity sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w==

common-tags@^1.8.0:
  version "1.8.2"
  resolved "https://registry.npmjs.org/common-tags/-/common-tags-1.8.2.tgz"
  integrity sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA==

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz"
  integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npmjs.org/compression/-/compression-1.7.4.tgz"
  integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

compute-scroll-into-view@^1.0.17:
  version "1.0.17"
  resolved "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-1.0.17.tgz"
  integrity sha512-j4dx+Fb0URmzbwwMUrhqWM2BEWHdFGx+qZ9qqASHRPqvTYdqvWnHg0H1hIbcyLnvgnoNAVMlwkepyqM3DaIFUg==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

confusing-browser-globals@^1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"
  integrity sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz"
  integrity sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz"
  integrity sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.8.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz"
  integrity sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA==
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.2, cookie@^0.4.1:
  version "0.4.2"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.2.tgz"
  integrity sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==

copy-anything@^2.0.1:
  version "2.0.6"
  resolved "https://registry.npmjs.org/copy-anything/-/copy-anything-2.0.6.tgz"
  integrity sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==
  dependencies:
    is-what "^3.14.1"

copy-to-clipboard@^3.2.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.1.tgz"
  integrity sha512-i13qo6kIHTTpCm8/Wup+0b1mVWETvu2kIMzKoK8FpkLkFxlt0znUAHcMzox+T8sPlqtZXq3CulEjQHsYiGFJUw==
  dependencies:
    toggle-selection "^1.0.6"

core-js-compat@^3.20.2, core-js-compat@^3.21.0:
  version "3.21.1"
  resolved "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.21.1.tgz"
  integrity sha512-gbgX5AUvMb8gwxC7FLVWYT7Kkgu/y7+h/h1X43yJkNqhlK2fuYyQimqvKGNZFAY6CKii/GFKJ2cp/1/42TN36g==
  dependencies:
    browserslist "^4.19.1"
    semver "7.0.0"

core-js-pure@^3.20.2, core-js-pure@^3.8.1:
  version "3.21.1"
  resolved "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.21.1.tgz"
  integrity sha512-12VZfFIu+wyVbBebyHmRTuEE/tZrB4tJToWcwAMcsp3h4+sHR+fMJWbKpYiCRWlhFBq+KNyO8rIV9rTkeVmznQ==

core-js@^3.19.2:
  version "3.21.1"
  resolved "https://registry.npmjs.org/core-js/-/core-js-3.21.1.tgz"
  integrity sha512-FRq5b/VMrWlrmCzwRrpDYNxyHP9BcAZC+xHJaqTgIE5091ZV1NTmyh0sGOg5XqpnHvR0svdy0sv1gWA1zmhxig==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-6.0.0.tgz"
  integrity sha512-xb3ZL6+L8b9JLLCx3ZdoZy4+2ECphCMo2PwqgP1tlfVq6M6YReyzBJtvWWtbDSpNr9hn96pkCiZqUcFEc+54Qg==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

cosmiconfig@^7.0.0, cosmiconfig@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.1.tgz"
  integrity sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz"
  integrity sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA==

css-blank-pseudo@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/css-blank-pseudo/-/css-blank-pseudo-3.0.3.tgz"
  integrity sha512-VS90XWtsHGqoM0t4KpH053c4ehxZ2E6HtGI7x68YFV0pTo/QmkV/YFA+NnlvK8guxZVNWGQhVNJGC39Q8XF4OQ==
  dependencies:
    postcss-selector-parser "^6.0.9"

css-declaration-sorter@^6.0.3:
  version "6.1.4"
  resolved "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-6.1.4.tgz"
  integrity sha512-lpfkqS0fctcmZotJGhnxkIyJWvBXgpyi2wsFd4J8VB7wzyrT6Ch/3Q+FMNJpjK4gu1+GN5khOnpU2ZVKrLbhCw==
  dependencies:
    timsort "^0.3.0"

css-has-pseudo@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/css-has-pseudo/-/css-has-pseudo-3.0.4.tgz"
  integrity sha512-Vse0xpR1K9MNlp2j5w1pgWIJtm1a8qS0JwS9goFYcImjlHEmywP9VUF05aGBXzGpDJF86QXk4L0ypBmwPhGArw==
  dependencies:
    postcss-selector-parser "^6.0.9"

css-loader@^6.5.1:
  version "6.6.0"
  resolved "https://registry.npmjs.org/css-loader/-/css-loader-6.6.0.tgz"
  integrity sha512-FK7H2lisOixPT406s5gZM1S3l8GrfhEBT3ZiL2UX1Ng1XWs0y2GPllz/OTyvbaHe12VgQrIXIzuEGVlbUhodqg==
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.5"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.0"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.3.5"

css-minimizer-webpack-plugin@^3.2.0:
  version "3.4.1"
  resolved "https://registry.npmjs.org/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.4.1.tgz"
  integrity sha512-1u6D71zeIfgngN2XNRJefc/hY7Ybsxd74Jm4qngIXyUEk7fss3VUzuHxLAq/R8NAba4QU9OUSaMZlbpRc7bM4Q==
  dependencies:
    cssnano "^5.0.6"
    jest-worker "^27.0.2"
    postcss "^8.3.5"
    schema-utils "^4.0.0"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"

css-prefers-color-scheme@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/css-prefers-color-scheme/-/css-prefers-color-scheme-6.0.3.tgz"
  integrity sha512-4BqMbZksRkJQx2zAjrokiGMd07RqOa2IxIrrN10lyBe9xhn9DEvjUK79J6jkeiv9D9hQFXKb6g1jwU62jziJZA==

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"
  integrity sha512-jQVeeRG70QI08vSTwf1jHxp74JoZsr2XSgETae8/xC8ovSnL2WF87GTLO86Sbwdt2lK4Umg4HnnwMO4YF3Ce7w==

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz"
  integrity sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-select@^4.1.3:
  version "4.2.1"
  resolved "https://registry.npmjs.org/css-select/-/css-select-4.2.1.tgz"
  integrity sha512-/aUslKhzkTNCQUB2qTX84lVmfia9NyjP3WpDGtj/WxhwBzWBYUV3DgUpurHTme8UTPcPlAD1DJ+b0nN/t50zDQ==
  dependencies:
    boolbase "^1.0.0"
    css-what "^5.1.0"
    domhandler "^4.3.0"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  integrity sha512-DMxWJg0rnz7UgxKT0Q1HU/L9BeJI0M6ksor0OgqOnF+aRCDWg/N2641HmVyU9KVIu0OVVWOb2IpC9A+BJRnejg==
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-tree@^1.1.2, css-tree@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz"
  integrity sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://registry.npmjs.org/css-what/-/css-what-3.4.2.tgz"
  integrity sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ==

css-what@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/css-what/-/css-what-5.1.0.tgz"
  integrity sha512-arSMRWIIFY0hV8pIxZMEfmMI47Wj3R/aWpZDDxWYCPEiOMv6tfOrnpDtgxBYPEQD4V0Y/958+1TdC3iWTFcUPw==

cssdb@^6.3.1:
  version "6.4.0"
  resolved "https://registry.npmjs.org/cssdb/-/cssdb-6.4.0.tgz"
  integrity sha512-8NMWrur/ewSNrRNZndbtOTXc2Xb2b+NCTPHj8VErFYvJUlgsMAiBGaFaxG6hjy9zbCjj2ZLwSQrMM+tormO8qA==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

cssnano-preset-default@^5.1.12:
  version "5.1.12"
  resolved "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-5.1.12.tgz"
  integrity sha512-rO/JZYyjW1QNkWBxMGV28DW7d98UDLaF759frhli58QFehZ+D/LSmwQ2z/ylBAe2hUlsIWTq6NYGfQPq65EF9w==
  dependencies:
    css-declaration-sorter "^6.0.3"
    cssnano-utils "^3.0.2"
    postcss-calc "^8.2.0"
    postcss-colormin "^5.2.5"
    postcss-convert-values "^5.0.4"
    postcss-discard-comments "^5.0.3"
    postcss-discard-duplicates "^5.0.3"
    postcss-discard-empty "^5.0.3"
    postcss-discard-overridden "^5.0.4"
    postcss-merge-longhand "^5.0.6"
    postcss-merge-rules "^5.0.6"
    postcss-minify-font-values "^5.0.4"
    postcss-minify-gradients "^5.0.6"
    postcss-minify-params "^5.0.5"
    postcss-minify-selectors "^5.1.3"
    postcss-normalize-charset "^5.0.3"
    postcss-normalize-display-values "^5.0.3"
    postcss-normalize-positions "^5.0.4"
    postcss-normalize-repeat-style "^5.0.4"
    postcss-normalize-string "^5.0.4"
    postcss-normalize-timing-functions "^5.0.3"
    postcss-normalize-unicode "^5.0.4"
    postcss-normalize-url "^5.0.5"
    postcss-normalize-whitespace "^5.0.4"
    postcss-ordered-values "^5.0.5"
    postcss-reduce-initial "^5.0.3"
    postcss-reduce-transforms "^5.0.4"
    postcss-svgo "^5.0.4"
    postcss-unique-selectors "^5.0.4"

cssnano-utils@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/cssnano-utils/-/cssnano-utils-3.0.2.tgz"
  integrity sha512-KhprijuQv2sP4kT92sSQwhlK3SJTbDIsxcfIEySB0O+3m9esFOai7dP9bMx5enHAh2MwarVIcnwiWoOm01RIbQ==

cssnano@^5.0.6:
  version "5.0.17"
  resolved "https://registry.npmjs.org/cssnano/-/cssnano-5.0.17.tgz"
  integrity sha512-fmjLP7k8kL18xSspeXTzRhaFtRI7DL9b8IcXR80JgtnWBpvAzHT7sCR/6qdn0tnxIaINUN6OEQu83wF57Gs3Xw==
  dependencies:
    cssnano-preset-default "^5.1.12"
    lilconfig "^2.0.3"
    yaml "^1.10.2"

csso@^4.0.2, csso@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz"
  integrity sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==
  dependencies:
    css-tree "^1.1.2"

cssom@^0.4.4:
  version "0.4.4"
  resolved "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz"
  integrity sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==

cssom@~0.3.6:
  version "0.3.8"
  resolved "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz"
  integrity sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz"
  integrity sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==
  dependencies:
    cssom "~0.3.6"

csstype@^3.0.2:
  version "3.0.10"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.0.10.tgz"
  integrity sha512-2u44ZG2OcNUO9HDp/Jl8C07x6pU/eTR3ncV91SiK3dhG9TWvRVsCoJw14Ckx5DgWkzGA3waZWO3d7pgqpUI/XA==

customize-cra@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/customize-cra/-/customize-cra-1.0.0.tgz"
  integrity sha512-DbtaLuy59224U+xCiukkxSq8clq++MOtJ1Et7LED1fLszWe88EoblEYFBJ895sB1mC6B4uu3xPT/IjClELhMbA==
  dependencies:
    lodash.flow "^3.5.0"

"d3-color@1 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-color/-/d3-color-3.0.1.tgz"
  integrity sha512-6/SlHkDOBLyQSJ1j1Ghs82OIUXpKWlR0hCsw0XrLSQhuUPuCSmLQ1QPH98vpnQxMUQM2/gfAkUEWsupVpd9JGw==

"d3-dispatch@1 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-dispatch/-/d3-dispatch-3.0.1.tgz"
  integrity sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==

"d3-drag@2 - 3":
  version "3.0.0"
  resolved "https://registry.npmjs.org/d3-drag/-/d3-drag-3.0.0.tgz"
  integrity sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==
  dependencies:
    d3-dispatch "1 - 3"
    d3-selection "3"

"d3-ease@1 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-ease/-/d3-ease-3.0.1.tgz"
  integrity sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==

"d3-interpolate@1 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz"
  integrity sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==
  dependencies:
    d3-color "1 - 3"

"d3-selection@2 - 3", d3-selection@3, d3-selection@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/d3-selection/-/d3-selection-3.0.0.tgz"
  integrity sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==

"d3-timer@1 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-timer/-/d3-timer-3.0.1.tgz"
  integrity sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==

"d3-transition@2 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-transition/-/d3-transition-3.0.1.tgz"
  integrity sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==
  dependencies:
    d3-color "1 - 3"
    d3-dispatch "1 - 3"
    d3-ease "1 - 3"
    d3-interpolate "1 - 3"
    d3-timer "1 - 3"

d3-zoom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/d3-zoom/-/d3-zoom-3.0.0.tgz"
  integrity sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==
  dependencies:
    d3-dispatch "1 - 3"
    d3-drag "2 - 3"
    d3-interpolate "1 - 3"
    d3-selection "2 - 3"
    d3-transition "2 - 3"

d@1, d@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/d/-/d-1.0.1.tgz"
  integrity sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

damerau-levenshtein@^1.0.7:
  version "1.0.8"
  resolved "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
  integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==

danmu.js@^0.5.9:
  version "0.5.10"
  resolved "https://registry.npmjs.org/danmu.js/-/danmu.js-0.5.10.tgz"
  integrity sha512-zLpSd5Yi+fxVVd0GHjrf5lZA45KKpbvc4Q8BbSVO3OdmCBkoB2vlDCGnVn18cnKpTNqi8Soy7AnzUw1YcqtzUA==
  dependencies:
    event-emitter "^0.3.5"

data-urls@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/data-urls/-/data-urls-2.0.0.tgz"
  integrity sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ==
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

date-fns@2.x:
  version "2.28.0"
  resolved "https://registry.npmjs.org/date-fns/-/date-fns-2.28.0.tgz"
  integrity sha512-8d35hViGYx/QH0icHYCeLmsLmMUheMmTyV9Fcm6gvNwdw31yXXH+O85sOBJ+OLnLQMKZowvpKb6FgMIQjcpvQw==

dayjs@1.x:
  version "1.10.7"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.10.7.tgz"
  integrity sha512-P6twpd70BcPK34K26uJ1KT3wlhpuOAPoMwJzpsIWUxHZ7wpmbdZL/hQqBDfz7hGurYSa5PhzdhDHtt319hL3ig==

debug@2.6.9, debug@^2.6.0, debug@^2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@4, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.4"
  resolved "https://mirrors.tencent.com/npm/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@^3.1.1, debug@^3.2.6, debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

decimal.js@^10.2.1:
  version "10.3.1"
  resolved "https://registry.npmjs.org/decimal.js/-/decimal.js-10.3.1.tgz"
  integrity sha512-V0pfhfr8suzyPGOx3nmq4aHqabehUZn6Ch9kyFpV79TGDTWFmHqUqXdabR7QHqxzrYolF4+tVmJhUG4OURg5dQ==

dedent@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz"
  integrity sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@^0.1.3, deep-is@~0.1.3:
  version "0.1.4"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.2.2.tgz"
  integrity sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/default-gateway/-/default-gateway-6.0.3.tgz"
  integrity sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==
  dependencies:
    execa "^5.0.0"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  integrity sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==

define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz"
  integrity sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==
  dependencies:
    object-keys "^1.0.12"

defined@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/defined/-/defined-1.0.0.tgz"
  integrity sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM=

del@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/del/-/del-6.0.0.tgz"
  integrity sha512-1shh9DQ23L16oXSZKB2JxpL7iMy2E0S9d517ptA1P8iw0alkPtQcrKH7ru31rYtKwF499HkTu+DRzq3TCKDFRQ==
  dependencies:
    globby "^11.0.1"
    graceful-fs "^4.2.4"
    is-glob "^4.0.1"
    is-path-cwd "^2.2.0"
    is-path-inside "^3.0.2"
    p-map "^4.0.0"
    rimraf "^3.0.2"
    slash "^3.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz"
  integrity sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz"
  integrity sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==

detect-port-alt@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/detect-port-alt/-/detect-port-alt-1.1.6.tgz"
  integrity sha512-5tQykt+LqfJFBEYaDITx7S7cR7mJ/zQmLXZ2qt5w04ainYZw6tBf9dBunMjVeVOdYVRUzUOE4HkY5J7+uttb5Q==
  dependencies:
    address "^1.0.1"
    debug "^2.6.0"

detective@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/detective/-/detective-5.2.0.tgz"
  integrity sha512-6SsIx+nUUbuK0EthKjv0zrdnajCCXVYGmbYYiYjFVpzcjwEs/JMDZ8tPRG29J/HhN56t3GJp2cGSWDRjjot8Pg==
  dependencies:
    acorn-node "^1.6.1"
    defined "^1.0.0"
    minimist "^1.1.1"

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

diff-sequences@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.5.1.tgz"
  integrity sha512-k1gCAXAsNgLwEL+Y8Wvl+M6oEFj5bgazfZULpS5CneoPPXRaCCW7dm+q21Ky2VEE5X+VeRDBVg1Pcvvsr4TtNQ==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.4.tgz"
  integrity sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA==
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dom-align@^1.7.0:
  version "1.12.2"
  resolved "https://registry.npmjs.org/dom-align/-/dom-align-1.12.2.tgz"
  integrity sha512-pHuazgqrsTFrGU2WLDdXxCFabkdQDx72ddkraZNih1KsMcN5qsRSTR9O4VJRlwTPCPb5COYg3LOfiMHHcPInHg==

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz"
  integrity sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==
  dependencies:
    utila "~0.4"

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz"
  integrity sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-serializer@^1.0.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.3.2.tgz"
  integrity sha512-5c54Bk5Dw4qAxNOI1pFEizPSjVsx5+bpJKmL2kPn8JhBUq2q09tTCa3mjijun2NfK78NMouDYNMBkOrPZiS+ig==
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz"
  integrity sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/domelementtype/-/domelementtype-2.2.0.tgz"
  integrity sha512-DtBMo82pv1dFtUmHyr48beiuq792Sxohr+8Hm9zoxklYPfa6n0Z3Byjj2IV7bmr2IyqClnqEQhfgHJJ5QF0R5A==

domexception@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/domexception/-/domexception-2.0.1.tgz"
  integrity sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg==
  dependencies:
    webidl-conversions "^5.0.0"

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/domhandler/-/domhandler-4.3.0.tgz"
  integrity sha512-fC0aXNQXqKSFTr2wDNZDhsEYjCiYsDWl3D01kwt25hm1YIPyDGHvvi3rw+PLqHAl/m71MaiF7d5zvBr0p5UB2g==
  dependencies:
    domelementtype "^2.2.0"

domutils@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz"
  integrity sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz"
  integrity sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz"
  integrity sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  integrity sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==

dotenv@^10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz"
  integrity sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==

downloadjs@1.4.7:
  version "1.4.7"
  resolved "https://registry.npmjs.org/downloadjs/-/downloadjs-1.4.7.tgz"
  integrity sha1-9p+W+UDg0FU9rCkROYZaPNAQHjw=

draggabilly@^2.2.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/draggabilly/-/draggabilly-2.4.1.tgz"
  integrity sha512-HHHLPEPZqRXIDQDFRFdK7RONZausNlJ4WkA73ST7Z6O2HPWttxFHVwHo8nccuDLzXWwiVKRVuc6fTkW+CQA++A==
  dependencies:
    get-size "^2.0.2"
    unidragger "^2.4.0"

duplexer@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz"
  integrity sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^3.1.6:
  version "3.1.6"
  resolved "https://registry.npmjs.org/ejs/-/ejs-3.1.6.tgz"
  integrity sha512-9lt9Zse4hPucPkoP7FHDF0LQAlGyF9JVpnClFLFH3aSSbxmyoqINRpp/9wePWJTUl4KOQwRL72Iw3InHPDkoGw==
  dependencies:
    jake "^10.6.1"

electron-to-chromium@^1.4.71:
  version "1.4.73"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.73.tgz"
  integrity sha512-RlCffXkE/LliqfA5m29+dVDPB2r72y2D2egMMfIy3Le8ODrxjuZNVo4NIC2yPL01N4xb4nZQLwzi6Z5tGIGLnA==

emittery@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npmjs.org/emittery/-/emittery-0.8.1.tgz"
  integrity sha512-uDfvUjVrfGJJhymx/kz6prltenw1u7WrCg1oa94zYY8xxVpLLUu045LAT0dhDZdXG58/EpPL/5kA180fQ/qudg==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

enhanced-resolve@^5.8.3:
  version "5.9.1"
  resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.9.1.tgz"
  integrity sha512-jdyZMwCQ5Oj4c5+BTnkxPgDZO/BJzh/ADDmKebayyzNwjVX1AFCeGkOfxNx0mHi2+8BKC5VxUYiw3TIvoT7vhw==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz"
  integrity sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==

errno@^0.1.1:
  version "0.1.8"
  resolved "https://registry.npmjs.org/errno/-/errno-0.1.8.tgz"
  integrity sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.0.7"
  resolved "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.0.7.tgz"
  integrity sha512-chLOW0ZGRf4s8raLrDxa5sdkvPec5YdvwbFnqJme4rk0rFajP8mPtrDL1+I+CwrQDCjswDA5sREX7jYQDQs9vA==
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.17.2, es-abstract@^1.19.0, es-abstract@^1.19.1:
  version "1.19.1"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.19.1.tgz"
  integrity sha512-2vJ6tjA/UfqLm2MPs7jxVybLoB8i1t1Jd9R3kISld20sIxPcTbLuggQOUxeWeAvIUkduv/CfMjuh4WmiXr2v9w==
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    get-intrinsic "^1.1.1"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-symbols "^1.0.2"
    internal-slot "^1.0.3"
    is-callable "^1.2.4"
    is-negative-zero "^2.0.1"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.1"
    is-string "^1.0.7"
    is-weakref "^1.0.1"
    object-inspect "^1.11.0"
    object-keys "^1.1.1"
    object.assign "^4.1.2"
    string.prototype.trimend "^1.0.4"
    string.prototype.trimstart "^1.0.4"
    unbox-primitive "^1.0.1"

es-module-lexer@^0.9.0:
  version "0.9.3"
  resolved "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-0.9.3.tgz"
  integrity sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es5-ext@^0.10.35, es5-ext@^0.10.50, es5-ext@~0.10.14:
  version "0.10.53"
  resolved "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.53.tgz"
  integrity sha512-Xs2Stw6NiNHWypzRTY1MtaG/uJlwCk8kH81920ma8mvN8Xq1gsfhZvpkImLQArw8AHnv8MT2I45J3c0R8slE+Q==
  dependencies:
    es6-iterator "~2.0.3"
    es6-symbol "~3.1.3"
    next-tick "~1.0.0"

es6-iterator@~2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-symbol@^3.1.1, es6-symbol@~3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.3.tgz"
  integrity sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA==
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  integrity sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

escodegen@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/escodegen/-/escodegen-2.0.0.tgz"
  integrity sha512-mmHKys/C8BFUGI+MAWNcSYoORYLMdPzjrknd2Vc+bUsjN5bXcr8EhrNB+UTqfL1y3I9c4fw2ihgtMPQLBRiQxw==
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-react-app@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/eslint-config-react-app/-/eslint-config-react-app-7.0.0.tgz"
  integrity sha512-xyymoxtIt1EOsSaGag+/jmcywRuieQoA2JbPCjnw9HukFj9/97aGPoZVFioaotzk1K5Qt9sHO5EutZbkrAXS0g==
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/eslint-parser" "^7.16.3"
    "@rushstack/eslint-patch" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^5.5.0"
    "@typescript-eslint/parser" "^5.5.0"
    babel-preset-react-app "^10.0.1"
    confusing-browser-globals "^1.0.11"
    eslint-plugin-flowtype "^8.0.3"
    eslint-plugin-import "^2.25.3"
    eslint-plugin-jest "^25.3.0"
    eslint-plugin-jsx-a11y "^6.5.1"
    eslint-plugin-react "^7.27.1"
    eslint-plugin-react-hooks "^4.3.0"
    eslint-plugin-testing-library "^5.0.1"

eslint-import-resolver-node@^0.3.6:
  version "0.3.6"
  resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.6.tgz"
  integrity sha512-0En0w03NRVMn9Uiyn8YRPDKvWjxCWkslUEhGNTdGx15RvPJYQ+lbOlqrlNI2vEAs4pDYK4f/HN2TbDmk5TP0iw==
  dependencies:
    debug "^3.2.7"
    resolve "^1.20.0"

eslint-module-utils@^2.7.2:
  version "2.7.3"
  resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.7.3.tgz"
  integrity sha512-088JEC7O3lDZM9xGe0RerkOMd0EjFl+Yvd1jPWIkMT5u3H9+HC34mWWPnqPrN13gieT9pBOO+Qt07Nb/6TresQ==
  dependencies:
    debug "^3.2.7"
    find-up "^2.1.0"

eslint-plugin-flowtype@^8.0.3:
  version "8.0.3"
  resolved "https://registry.npmjs.org/eslint-plugin-flowtype/-/eslint-plugin-flowtype-8.0.3.tgz"
  integrity sha512-dX8l6qUL6O+fYPtpNRideCFSpmWOUVx5QcaGLVqe/vlDiBSe4vYljDWDETwnyFzpl7By/WVIu6rcrniCgH9BqQ==
  dependencies:
    lodash "^4.17.21"
    string-natural-compare "^3.0.1"

eslint-plugin-import@^2.25.3:
  version "2.25.4"
  resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.25.4.tgz"
  integrity sha512-/KJBASVFxpu0xg1kIBn9AUa8hQVnszpwgE7Ld0lKAlx7Ie87yzEzCgSkekt+le/YVhiaosO4Y14GDAOc41nfxA==
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flat "^1.2.5"
    debug "^2.6.9"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-module-utils "^2.7.2"
    has "^1.0.3"
    is-core-module "^2.8.0"
    is-glob "^4.0.3"
    minimatch "^3.0.4"
    object.values "^1.1.5"
    resolve "^1.20.0"
    tsconfig-paths "^3.12.0"

eslint-plugin-jest@^25.3.0:
  version "25.7.0"
  resolved "https://registry.npmjs.org/eslint-plugin-jest/-/eslint-plugin-jest-25.7.0.tgz"
  integrity sha512-PWLUEXeeF7C9QGKqvdSbzLOiLTx+bno7/HC9eefePfEb257QFHg7ye3dh80AZVkaa/RQsBB1Q/ORQvg2X7F0NQ==
  dependencies:
    "@typescript-eslint/experimental-utils" "^5.0.0"

eslint-plugin-jsx-a11y@^6.5.1:
  version "6.5.1"
  resolved "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.5.1.tgz"
  integrity sha512-sVCFKX9fllURnXT2JwLN5Qgo24Ug5NF6dxhkmxsMEUZhXRcGg+X3e1JbJ84YePQKBl5E0ZjAH5Q4rkdcGY99+g==
  dependencies:
    "@babel/runtime" "^7.16.3"
    aria-query "^4.2.2"
    array-includes "^3.1.4"
    ast-types-flow "^0.0.7"
    axe-core "^4.3.5"
    axobject-query "^2.2.0"
    damerau-levenshtein "^1.0.7"
    emoji-regex "^9.2.2"
    has "^1.0.3"
    jsx-ast-utils "^3.2.1"
    language-tags "^1.0.5"
    minimatch "^3.0.4"

eslint-plugin-react-hooks@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.3.0.tgz"
  integrity sha512-XslZy0LnMn+84NEG9jSGR6eGqaZB3133L8xewQo3fQagbQuGt7a63gf+P1NGKZavEYEC3UXaWEAA/AqDkuN6xA==

eslint-plugin-react@^7.27.1, eslint-plugin-react@^7.28.0:
  version "7.29.2"
  resolved "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.29.2.tgz"
  integrity sha512-ypEBTKOy5liFQXZWMchJ3LN0JX1uPI6n7MN7OPHKacqXAxq5gYC30TdO7wqGYQyxD1OrzpobdHC3hDmlRWDg9w==
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flatmap "^1.2.5"
    doctrine "^2.1.0"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.5"
    object.fromentries "^2.0.5"
    object.hasown "^1.1.0"
    object.values "^1.1.5"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.3"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.6"

eslint-plugin-testing-library@^5.0.1:
  version "5.0.5"
  resolved "https://registry.npmjs.org/eslint-plugin-testing-library/-/eslint-plugin-testing-library-5.0.5.tgz"
  integrity sha512-0j355vJpJCE/2g+aayIgJRUB6jBVqpD5ztMLGcadR1PgrgGPnPxN1HJuOAsAAwiMo27GwRnpJB8KOQzyNuNZrw==
  dependencies:
    "@typescript-eslint/utils" "^5.10.2"

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://mirrors.tencent.com/npm/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz"
  integrity sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^2.0.0, eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==

eslint-visitor-keys@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz"
  integrity sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://mirrors.tencent.com/npm/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint-webpack-plugin@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/eslint-webpack-plugin/-/eslint-webpack-plugin-3.1.1.tgz"
  integrity sha512-xSucskTN9tOkfW7so4EaiFIkulWLXwCB/15H917lR6pTv0Zot6/fetFucmENRb7J5whVSFKIvwnrnsa78SG2yg==
  dependencies:
    "@types/eslint" "^7.28.2"
    jest-worker "^27.3.1"
    micromatch "^4.0.4"
    normalize-path "^3.0.0"
    schema-utils "^3.1.1"

eslint@^8.25.0, eslint@^8.3.0:
  version "8.53.0"
  resolved "https://mirrors.tencent.com/npm/eslint/-/eslint-8.53.0.tgz"
  integrity sha512-N4VuiPjXDUa4xVeV/GC/RV3hQW9Nw+Y463lkWaKKXKYMvmRiRDAtfpuPFLN+E1/6ZhyR8J2ig+eVREnYgUsiag==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.3"
    "@eslint/js" "8.53.0"
    "@humanwhocodes/config-array" "^0.11.13"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://mirrors.tencent.com/npm/espree/-/espree-9.6.1.tgz"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esprima@~1.2.2:
  version "1.2.5"
  resolved "https://registry.npmjs.org/esprima/-/esprima-1.2.5.tgz"
  integrity sha1-CZNQL+r2aBODJXVvMPmlH+7sEek=

esquery@^1.4.2:
  version "1.5.0"
  resolved "https://mirrors.tencent.com/npm/esquery/-/esquery-1.5.0.tgz"
  integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-1.0.1.tgz"
  integrity sha512-1fMXF3YP4pZZVozF8j/ZLfvnR8NSIljt56UhbZ5PeeDmmGHpgpdwQt7ITlGvYaQukCvuBRMLEiKiYC+oeIg4cg==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

ev-emitter@^1.0.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ev-emitter/-/ev-emitter-1.1.1.tgz"
  integrity sha512-ipiDYhdQSCZ4hSbX4rMW+XzNKMD1prg/sTvoVmSLkuQ1MVlwjJQQA+sW8tMYR3BLUr9KjodFV4pvzunvRhd33Q==

event-emitter@^0.3.5:
  version "0.3.5"
  resolved "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter3@^4.0.0, eventemitter3@^4.0.7:
  version "4.0.7"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

events@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expect@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/expect/-/expect-27.5.1.tgz"
  integrity sha512-E1q5hSUG2AmYQwQJ041nvgpkODHQvB+RKlB4IYdru6uJsyFTRyZAP463M+1lINorwbqAmUggi6+WwkD8lCS/Dw==
  dependencies:
    "@jest/types" "^27.5.1"
    jest-get-type "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"

express@^4.17.1:
  version "4.17.3"
  resolved "https://registry.npmjs.org/express/-/express-4.17.3.tgz"
  integrity sha512-yuSQpz5I+Ch7gFrPCk4/c+dIBKlQUxtgwqzph132bsT6qhuzss6I8cLJQz7B3rFblzd6wtcI0ZbGltH/C4LjUg==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.19.2"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.4.2"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.9.7"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.17.2"
    serve-static "1.14.2"
    setprototypeof "1.2.0"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

ext@^1.1.2:
  version "1.6.0"
  resolved "https://registry.npmjs.org/ext/-/ext-1.6.0.tgz"
  integrity sha512-sdBImtzkq2HpkdRLtlLWDa6w4DX22ijZLKx8BMPUuKe1c5lbN6xwQDQCxSfxBQnHZ13ls/FH0MQZx/q/gr6FQg==
  dependencies:
    type "^2.5.0"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^3.2.11, fast-glob@^3.2.9:
  version "3.2.11"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.11.tgz"
  integrity sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz"
  integrity sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz"
  integrity sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==
  dependencies:
    websocket-driver ">=0.5.1"

fb-watchman@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.1.tgz"
  integrity sha512-DkPJKQeY6kKwmuMretBhr7G6Vodr7bFwDYTXIkfG1gjvNpaxBTQV3PbXg6bR1c1UP4jPOX0jHUbbHANL9vRjVg==
  dependencies:
    bser "2.1.1"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

file-loader@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/file-loader/-/file-loader-6.2.0.tgz"
  integrity sha512-qo3glqyTa61Ytg4u73GultjHGjdRyig3tG6lPtyX/jOEJvHif9uB0/OCI2Kif6ctF3caQTW2G5gym21oAsI4pw==
  dependencies:
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"

filelist@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/filelist/-/filelist-1.0.2.tgz"
  integrity sha512-z7O0IS8Plc39rTCq6i6iHxk43duYOn8uFJiWSewIq0Bww1RNybVHSCjahmcC87ZqAm4OTvFzlzeGu3XAzG1ctQ==
  dependencies:
    minimatch "^3.0.4"

filesize@^8.0.6:
  version "8.0.7"
  resolved "https://registry.npmjs.org/filesize/-/filesize-8.0.7.tgz"
  integrity sha512-pjmC+bkIF8XI7fWaH8KxHcZL3DPybs1roSKP4rKDvy20tAWwIObE4+JIseG2byfGKhud5ZnM4YSGKBz7Sh0ndQ==

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  integrity sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  integrity sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz"
  integrity sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.0.4.tgz"
  integrity sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.5"
  resolved "https://registry.npmjs.org/flatted/-/flatted-3.2.5.tgz"
  integrity sha512-WIWGi2L3DyTUvUrwRKgGi9TwxQMUEqPOPQBVi71R96jZXJdFskXEmf54BoZaS1kknGODoIGASGEzBUYdyMCBJg==

follow-redirects@^1.0.0, follow-redirects@^1.14.0:
  version "1.14.9"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.9.tgz"
  integrity sha512-MQDfihBQYMcyy5dhRDJUHcw7lb2Pv/TuE6xP1vyraLukNDHKbDxDNaOE3NbCAdKQApno+GPRyo1YAp89yCjK4w==

fork-ts-checker-webpack-plugin@^6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-6.5.0.tgz"
  integrity sha512-cS178Y+xxtIjEUorcHddKS7yCMlrDPV31mt47blKKRfMd70Kxu5xruAFE2o9sDY6wVC5deuob/u/alD04YYHnw==
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@types/json-schema" "^7.0.5"
    chalk "^4.1.0"
    chokidar "^3.4.2"
    cosmiconfig "^6.0.0"
    deepmerge "^4.2.2"
    fs-extra "^9.0.0"
    glob "^7.1.6"
    memfs "^3.1.2"
    minimatch "^3.0.4"
    schema-utils "2.7.0"
    semver "^7.3.2"
    tapable "^1.0.0"

form-data@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/form-data/-/form-data-3.0.1.tgz"
  integrity sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

fraction.js@^4.1.2:
  version "4.1.3"
  resolved "https://registry.npmjs.org/fraction.js/-/fraction.js-4.1.3.tgz"
  integrity sha512-pUHWWt6vHzZZiQJcM6S/0PXfS+g6FM4BF5rj9wZyreivhQPdsh5PpE25VtSNxq80wHS5RfY51Ii+8Z0Zl/pmzg==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^10.0.0:
  version "10.0.1"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-10.0.1.tgz"
  integrity sha512-NbdoVMZso2Lsrn/QwLXOy6rm0ufY2zEOKCDzJR/0kBsb0E6qed0P3iYK+Ath3BfvXEeu4JhEtXLgILx5psUfag==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-5.0.0.tgz"
  integrity sha512-66Pm4RYbjzdyeuqudYqhFiNBbCIuI9kgRqLPSHIlXHidW8NIQtVdkM1yeZ4lXwuhbTETv3EUGMNHAAw6hiundQ==
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^9.0.0, fs-extra@^9.0.1:
  version "9.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz"
  integrity sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.3.tgz"
  integrity sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q==

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.3.2, fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz"
  integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

generate-source-map@0.0.5:
  version "0.0.5"
  resolved "https://registry.npmjs.org/generate-source-map/-/generate-source-map-0.0.5.tgz"
  integrity sha1-8SVfMWU8sCMeZxOn3IN1r08zpQk=
  dependencies:
    esprima "~1.2.2"
    source-map "~0.1.34"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz"
  integrity sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q==
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"
  integrity sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g==

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  integrity sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==

get-size@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/get-size/-/get-size-2.0.3.tgz"
  integrity sha512-lXNzT/h/dTjTxRbm9BXb+SGxxzkm97h/PCIKtlN/CBCxxmkkIVV21udumMS93MuVTDX583gqc94v3RjuHmI+2Q==

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  integrity sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==

glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.2.0"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.0.tgz"
  integrity sha512-lmLf6gtyrPq8tTjSmrO94wBeQbFR3HbLHbuyD69wuyQkImp2hWqMGB47OX65FBkPffO641IP9jWa1z4ivqG26Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz"
  integrity sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A==
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz"
  integrity sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg==
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.19.0:
  version "13.23.0"
  resolved "https://mirrors.tencent.com/npm/globals/-/globals-13.23.0.tgz"
  integrity sha512-XAmF0RjlrjY23MA51q3HltdlGxUpXPvg0GioKiD9X6HD28iMjo2dKC8Vqwm7lne4GNr78+RHTfliktR6ZH09wA==
  dependencies:
    type-fest "^0.20.2"

globby@^11.0.1, globby@^11.0.3, globby@^11.0.4, globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
  version "4.2.9"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.9.tgz"
  integrity sha512-NtNxqUcXgpW2iMrfqSfR73Glt39K+BLwWsPs94yR63v45T0Wbej7eRmL5cWfwEgqXnmjQp3zaJTshdRW/qC2ZQ==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/gzip-size/-/gzip-size-6.0.0.tgz"
  integrity sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==
  dependencies:
    duplexer "^0.1.2"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz"
  integrity sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==

harmony-reflect@^1.4.6:
  version "1.6.2"
  resolved "https://registry.npmjs.org/harmony-reflect/-/harmony-reflect-1.6.2.tgz"
  integrity sha512-HIp/n38R9kQjDEziXyDTuW3vvoxxyxjxFzXLrBr18uB47GnSt+G9D29fqrpM5ZkspMcPICud3XsBJQ4Y2URg8g==

has-bigints@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.1.tgz"
  integrity sha512-LSBS2LjbNBTf6287JEbEzvJgftkF5qFkmCo9hDRpAzKhUOlJ+hx8dd4USs00SgsUNwc4617J9ki5YtEClM2ffA==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-symbols@^1.0.1, has-symbols@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.2.tgz"
  integrity sha512-chXa79rL/UC2KlX17jo3vRGz0azaWEx5tGqZg5pO3NUyEJVB17dMruQlzCCOfUvElghKcm5194+BCRvi2Rv/Gw==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
  dependencies:
    has-symbols "^1.0.2"

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

he@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/he/-/he-1.2.0.tgz"
  integrity sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==

history@^4.9.0:
  version "4.10.1"
  resolved "https://registry.npmjs.org/history/-/history-4.10.1.tgz"
  integrity sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew==
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^3.0.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^1.0.1"

hoist-non-react-statics@^3.1.0, hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/hoopy/-/hoopy-0.1.4.tgz"
  integrity sha512-HRcs+2mr52W0K+x8RzcLzuPPmVIKMSv97RGHy0Ea9y/mpcaK+xTrjICA04KAHi4GRzxliNqNJEFYWHghy3rSfQ==

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz"
  integrity sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ==
  dependencies:
    whatwg-encoding "^1.0.5"

html-entities@^2.1.0, html-entities@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npmjs.org/html-entities/-/html-entities-2.3.2.tgz"
  integrity sha512-c3Ab/url5ksaT0WyleslpBEthOzWhrjQbg75y7XUsfSzi3Dgzt0l8w5e7DylRn15MTlMMD58dTfzddNS2kcAjQ==

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==

html-minifier-terser@^6.0.2:
  version "6.1.0"
  resolved "https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  integrity sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw==
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.2.2"
    commander "^8.3.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.10.0"

html-parse-stringify@^3.0.1:
  version "3.0.1"
  resolved "https://mirrors.tencent.com/npm/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz"
  integrity sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==
  dependencies:
    void-elements "3.1.0"

html-webpack-plugin@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-5.5.0.tgz"
  integrity sha512-sy88PC2cRTVxvETRgUHFrL4No3UxvcH8G1NepGhqaTT+GXN2kTamqasot0inS5hXeg1cMbFDt27zzo9p35lZVw==
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/htmlparser2/-/htmlparser2-6.1.0.tgz"
  integrity sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.8.1.tgz"
  integrity sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.5"
  resolved "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.5.tgz"
  integrity sha512-x+JVEkO2PoM8qqpbPbOL3cqHPwerep7OwzK7Ay+sMQjKzaKCqWvjoXm5tqMP9tXWWTnTzAjIhXg+J99XYuPhPA==

http-proxy-agent@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz"
  integrity sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

http-proxy-middleware@^2.0.0, http-proxy-middleware@^2.0.6:
  version "2.0.6"
  resolved "https://mirrors.tencent.com/npm/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz"
  integrity sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz"
  integrity sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

https-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.0.tgz"
  integrity sha512-EkYm5BcKUGiduxzSt3Eppko+PiNWNEpa4ySk9vTC6wDsQJW9rHSa+UhGNJoRYp7bz6Ht1eaRIa6QaJqO5rCFbA==
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

i18next-browser-languagedetector@^7.1.0:
  version "7.1.0"
  resolved "https://mirrors.tencent.com/npm/i18next-browser-languagedetector/-/i18next-browser-languagedetector-7.1.0.tgz"
  integrity sha512-cr2k7u1XJJ4HTOjM9GyOMtbOA47RtUoWRAtt52z43r3AoMs2StYKyjS3URPhzHaf+mn10hY9dZWamga5WPQjhA==
  dependencies:
    "@babel/runtime" "^7.19.4"

i18next@^23.6.0:
  version "23.6.0"
  resolved "https://mirrors.tencent.com/npm/i18next/-/i18next-23.6.0.tgz"
  integrity sha512-z0Cxr0MGkt+kli306WS4nNNM++9cgt2b2VCMprY92j+AIab/oclgPxdwtTZVLP1zn5t5uo8M6uLsZmYrcjr3HA==
  dependencies:
    "@babel/runtime" "^7.22.5"

iconv-lite@0.4.24, iconv-lite@^0.4.4:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz"
  integrity sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==

idb@^6.1.4:
  version "6.1.5"
  resolved "https://registry.npmjs.org/idb/-/idb-6.1.5.tgz"
  integrity sha512-IJtugpKkiVXQn5Y+LteyBCNk1N8xpGV3wWZk9EVtZWH8DYkjBn0bX1XnGP9RkyZF0sAcywa6unHqSWKe7q4LGw==

identity-obj-proxy@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz"
  integrity sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ=
  dependencies:
    harmony-reflect "^1.4.6"

ignore@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.2.0.tgz"
  integrity sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==

image-size@~0.5.0:
  version "0.5.5"
  resolved "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

immer@^9.0.7:
  version "9.0.12"
  resolved "https://registry.npmjs.org/immer/-/immer-9.0.12.tgz"
  integrity sha512-lk7UNmSbAukB5B6dh9fnh5D0bJTOFKxVg2cyJWTYrWRfhLrLMBquONcUs3aFq507hNoIZEDDh8lb8UtOizSMhA==

import-fresh@^3.1.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/import-local/-/import-local-3.1.0.tgz"
  integrity sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.5:
  version "1.3.8"
  resolved "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz"
  integrity sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.3.tgz"
  integrity sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA==
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

ip@^1.1.0:
  version "1.1.5"
  resolved "https://registry.npmjs.org/ip/-/ip-1.1.5.tgz"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

ipaddr.js@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.0.1.tgz"
  integrity sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng==

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
  integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.4, is-callable@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.4.tgz"
  integrity sha512-nsuwtxZfMX67Oryl9LCQ+upnC0Z0BgpwntpS89m1H/TLF0zNfzfLMV/9Wa/6MZsj0acpEjAO0KF1xT6ZdLl95w==

is-core-module@^2.2.0, is-core-module@^2.8.0, is-core-module@^2.8.1:
  version "2.8.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.8.1.tgz"
  integrity sha512-SdNCUs284hr40hFTFP6l0IfZ/RSrMXF3qgoRHd3/79unUTvrFO/JoXwkGm+5J/Oe3E/b5GsnG330uUNgRpu1PA==
  dependencies:
    has "^1.0.3"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
  integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  integrity sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-module/-/is-module-1.0.0.tgz"
  integrity sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=

is-negative-zero@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==

is-number-object@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.6.tgz"
  integrity sha512-bEVOqiRcvo3zO1+G2lVMy+gkkEm9Yh7cDMRusKKu5ZJKPUYSJwICTKZrNKHA2EbSP0Tu0+6B/emsYNHZyn6K8g==
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-path-cwd@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
  integrity sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==

is-path-inside@^3.0.2, is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  integrity sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==

is-regex@^1.0.4, is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-regexp/-/is-regexp-1.0.0.tgz"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-root@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-root/-/is-root-2.1.0.tgz"
  integrity sha512-AGOriNp96vNBd3HtU+RzFEc75FfR5ymiYv8E553I71SCeXBiMsVDUtdio1OEFvrPyLIQ9tVR5RxXIFe5PUFjMg==

is-shared-array-buffer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.1.tgz"
  integrity sha512-IU0NmyknYZN0rChcKhRO1X8LYz5Isj/Fsqh8NJOSf+N/hCOTwy29F32Ik7a+QszE63IdvmwdTPDd6cZ5pg4cwA==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
  integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
  integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
  dependencies:
    has-symbols "^1.0.2"

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-weakref@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

is-what@^3.14.1:
  version "3.14.1"
  resolved "https://registry.npmjs.org/is-what/-/is-what-3.14.1.tgz"
  integrity sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz"
  integrity sha512-eOeJ5BHCmHYvQK7xt9GkdHuzuCGS1Y6g9Gvnx3Ym33fz/HpLRYxiS0wHNr+m/MBC8B647Xt608vCDEvhl9c6Mw==

istanbul-lib-instrument@^5.0.4, istanbul-lib-instrument@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.1.0.tgz"
  integrity sha512-czwUz525rkOFDJxfKK6mYfIs9zBKILyrZQxjz3ABhjQXhbhFsSbo1HW/BFcsDnfJYJWA6thRR5/TUY2qs5W99Q==
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz"
  integrity sha512-wcdi+uAKzfiGT2abPpKZ0hSU1rGQjUQnLvtY5MpQ7QCTahD3VODhcu4wcfY1YtkGaDD5yuydOLINXsfbus9ROw==
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  integrity sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.4"
  resolved "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.4.tgz"
  integrity sha512-r1/DshN4KSE7xWEknZLLLLDn5CJybV3nw01VTkp6D5jzLuELlcbudfj/eSQFvrKsJuTVCGnePO7ho82Nw9zzfw==
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jake@^10.6.1:
  version "10.8.2"
  resolved "https://registry.npmjs.org/jake/-/jake-10.8.2.tgz"
  integrity sha512-eLpKyrfG3mzvGE2Du8VoPbeSkRry093+tyNjdYaBbJS9v17knImYGNXQCUV0gLxQtF82m3E8iRb/wdSQZLoq7A==
  dependencies:
    async "0.9.x"
    chalk "^2.4.2"
    filelist "^1.0.1"
    minimatch "^3.0.4"

jest-changed-files@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.5.1.tgz"
  integrity sha512-buBLMiByfWGCoMsLLzGUUSpAmIAGnbR2KJoMN10ziLhOLvP4e0SlypHnAel8iqQXTrcbmfEY9sSqae5sgUsTvw==
  dependencies:
    "@jest/types" "^27.5.1"
    execa "^5.0.0"
    throat "^6.0.1"

jest-circus@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-circus/-/jest-circus-27.5.1.tgz"
  integrity sha512-D95R7x5UtlMA5iBYsOHFFbMD/GVA4R/Kdq15f7xYWUfWHBto9NYRsOvnSauTgdF+ogCpJ4tyKOXhUifxS65gdw==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^0.7.0"
    expect "^27.5.1"
    is-generator-fn "^2.0.0"
    jest-each "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"
    jest-runtime "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    pretty-format "^27.5.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"
    throat "^6.0.1"

jest-cli@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-cli/-/jest-cli-27.5.1.tgz"
  integrity sha512-Hc6HOOwYq4/74/c62dEE3r5elx8wjYqxY0r0G/nFrLDPMFRu6RA/u8qINOIkvhxG7mMQ5EJsOGfRpI8L6eFUVw==
  dependencies:
    "@jest/core" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    import-local "^3.0.2"
    jest-config "^27.5.1"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    prompts "^2.0.1"
    yargs "^16.2.0"

jest-config@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-config/-/jest-config-27.5.1.tgz"
  integrity sha512-5sAsjm6tGdsVbW9ahcChPAFCk4IlkQUknH5AvKjuLTSlcO/wCZKyFdn7Rg0EkC+OGgWODEy2hDpWB1PgzH0JNA==
  dependencies:
    "@babel/core" "^7.8.0"
    "@jest/test-sequencer" "^27.5.1"
    "@jest/types" "^27.5.1"
    babel-jest "^27.5.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.1"
    graceful-fs "^4.2.9"
    jest-circus "^27.5.1"
    jest-environment-jsdom "^27.5.1"
    jest-environment-node "^27.5.1"
    jest-get-type "^27.5.1"
    jest-jasmine2 "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-runner "^27.5.1"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^27.5.1"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-diff/-/jest-diff-27.5.1.tgz"
  integrity sha512-m0NvkX55LDt9T4mctTEgnZk3fmEg3NRYutvMPWM/0iPnkFj2wIeF45O1718cMSOFO1vINkqmxqD8vE37uTEbqw==
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^27.5.1"
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-docblock@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.5.1.tgz"
  integrity sha512-rl7hlABeTsRYxKiUfpHrQrG4e2obOiTQWfMEH3PxPjOtdsfLQO4ReWSZaQ7DETm4xu07rl4q/h4zcKXyU0/OzQ==
  dependencies:
    detect-newline "^3.0.0"

jest-each@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-each/-/jest-each-27.5.1.tgz"
  integrity sha512-1Ff6p+FbhT/bXQnEouYy00bkNSY7OUpfIcmdl8vZ31A1UUaurOLPA8a8BbJOF2RDUElwJhmeaV7LnagI+5UwNQ==
  dependencies:
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    jest-get-type "^27.5.1"
    jest-util "^27.5.1"
    pretty-format "^27.5.1"

jest-environment-jsdom@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-27.5.1.tgz"
  integrity sha512-TFBvkTC1Hnnnrka/fUb56atfDtJ9VMZ94JkjTbggl1PEpwrYtUBKMezB3inLmWqQsXYLcMwNoDQwoBTAvFfsfw==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/fake-timers" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    jest-mock "^27.5.1"
    jest-util "^27.5.1"
    jsdom "^16.6.0"

jest-environment-node@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-27.5.1.tgz"
  integrity sha512-Jt4ZUnxdOsTGwSRAfKEnE6BcwsSPNOijjwifq5sDFSA2kesnXTvNqKHYgM0hDq3549Uf/KzdXNYn4wMZJPlFLw==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/fake-timers" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    jest-mock "^27.5.1"
    jest-util "^27.5.1"

jest-get-type@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.5.1.tgz"
  integrity sha512-2KY95ksYSaK7DMBWQn6dQz3kqAf3BB64y2udeG+hv4KfSOb9qwcYQstTJc1KCbsix+wLZWZYN8t7nwX3GOBLRw==

jest-haste-map@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-27.5.1.tgz"
  integrity sha512-7GgkZ4Fw4NFbMSDSpZwXeBiIbx+t/46nJ2QitkOjvwPYyZmqttu2TDSimMHP1EkPOi4xUZAN1doE5Vd25H4Jng==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^27.5.1"
    jest-serializer "^27.5.1"
    jest-util "^27.5.1"
    jest-worker "^27.5.1"
    micromatch "^4.0.4"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.3.2"

jest-jasmine2@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-jasmine2/-/jest-jasmine2-27.5.1.tgz"
  integrity sha512-jtq7VVyG8SqAorDpApwiJJImd0V2wv1xzdheGHRGyuT7gZm6gG47QEskOlzsN1PG/6WNaCo5pmwMHDf3AkG2pQ==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/source-map" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    expect "^27.5.1"
    is-generator-fn "^2.0.0"
    jest-each "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"
    jest-runtime "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    pretty-format "^27.5.1"
    throat "^6.0.1"

jest-leak-detector@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.5.1.tgz"
  integrity sha512-POXfWAMvfU6WMUXftV4HolnJfnPOGEu10fscNCA76KBpRRhcMN2c8d3iT2pxQS3HLbA+5X4sOUPzYO2NUyIlHQ==
  dependencies:
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-matcher-utils@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-27.5.1.tgz"
  integrity sha512-z2uTx/T6LBaCoNWNFWwChLBKYxTMcGBRjAt+2SbP929/Fflb9aa5LGma654Rz8z9HLxsrUaYzxE9T/EFIL/PAw==
  dependencies:
    chalk "^4.0.0"
    jest-diff "^27.5.1"
    jest-get-type "^27.5.1"
    pretty-format "^27.5.1"

jest-message-util@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-message-util/-/jest-message-util-27.5.1.tgz"
  integrity sha512-rMyFe1+jnyAAf+NHwTclDz0eAaLkVDdKVHHBFWsBWHnnh5YeJMNWWsv7AbFYXfK3oTqvL7VTWkhNLu1jX24D+g==
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^27.5.1"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^27.5.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-mock/-/jest-mock-27.5.1.tgz"
  integrity sha512-K4jKbY1d4ENhbrG2zuPWaQBvDly+iZ2yAW+T1fATN78hc0sInwn7wZB8XtlNnvHug5RMwV897Xm4LqmPM4e2Og==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.2.tgz"
  integrity sha512-olV41bKSMm8BdnuMsewT4jqlZ8+3TCARAXjZGT9jcoSnrfUnRCqnMoF9XEeoWjbzObpqF9dRhHQj0Xb9QdF6/w==

jest-regex-util@^27.0.0, jest-regex-util@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.5.1.tgz"
  integrity sha512-4bfKq2zie+x16okqDXjXn9ql2B0dScQu+vcwe4TvFVhkVyuWLqpZrZtXxLLWoXYgn0E87I6r6GRYHF7wFZBUvg==

jest-resolve-dependencies@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-27.5.1.tgz"
  integrity sha512-QQOOdY4PE39iawDn5rzbIePNigfe5B9Z91GDD1ae/xNDlu9kaat8QQ5EKnNmVWPV54hUdxCVwwj6YMgR2O7IOg==
  dependencies:
    "@jest/types" "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-snapshot "^27.5.1"

jest-resolve@^27.4.2, jest-resolve@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-resolve/-/jest-resolve-27.5.1.tgz"
  integrity sha512-FFDy8/9E6CV83IMbDpcjOhumAQPDyETnU2KZ1O98DwTnz8AOBsW/Xv3GySr1mOZdItLR+zDZ7I/UdTFbgSOVCw==
  dependencies:
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-pnp-resolver "^1.2.2"
    jest-util "^27.5.1"
    jest-validate "^27.5.1"
    resolve "^1.20.0"
    resolve.exports "^1.1.0"
    slash "^3.0.0"

jest-runner@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-runner/-/jest-runner-27.5.1.tgz"
  integrity sha512-g4NPsM4mFCOwFKXO4p/H/kWGdJp9V8kURY2lX8Me2drgXqG7rrZAx5kv+5H7wtt/cdFIjhqYx1HrlqWHaOvDaQ==
  dependencies:
    "@jest/console" "^27.5.1"
    "@jest/environment" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.8.1"
    graceful-fs "^4.2.9"
    jest-docblock "^27.5.1"
    jest-environment-jsdom "^27.5.1"
    jest-environment-node "^27.5.1"
    jest-haste-map "^27.5.1"
    jest-leak-detector "^27.5.1"
    jest-message-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-runtime "^27.5.1"
    jest-util "^27.5.1"
    jest-worker "^27.5.1"
    source-map-support "^0.5.6"
    throat "^6.0.1"

jest-runtime@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-runtime/-/jest-runtime-27.5.1.tgz"
  integrity sha512-o7gxw3Gf+H2IGt8fv0RiyE1+r83FJBRruoA+FXrlHw6xEyBsU8ugA6IPfTdVyA0w8HClpbK+DGJxH59UrNMx8A==
  dependencies:
    "@jest/environment" "^27.5.1"
    "@jest/fake-timers" "^27.5.1"
    "@jest/globals" "^27.5.1"
    "@jest/source-map" "^27.5.1"
    "@jest/test-result" "^27.5.1"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    execa "^5.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^27.5.1"
    jest-message-util "^27.5.1"
    jest-mock "^27.5.1"
    jest-regex-util "^27.5.1"
    jest-resolve "^27.5.1"
    jest-snapshot "^27.5.1"
    jest-util "^27.5.1"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-serializer@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-serializer/-/jest-serializer-27.5.1.tgz"
  integrity sha512-jZCyo6iIxO1aqUxpuBlwTDMkzOAJS4a3eYz3YzgxxVQFwLeSA7Jfq5cbqCY+JLvTDrWirgusI/0KwxKMgrdf7w==
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.9"

jest-snapshot@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-27.5.1.tgz"
  integrity sha512-yYykXI5a0I31xX67mgeLw1DZ0bJB+gpq5IpSuCAoyDi0+BhgU/RIrL+RTzDmkNTchvDFWKP8lp+w/42Z3us5sA==
  dependencies:
    "@babel/core" "^7.7.2"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.0.0"
    "@jest/transform" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.1.5"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^27.5.1"
    graceful-fs "^4.2.9"
    jest-diff "^27.5.1"
    jest-get-type "^27.5.1"
    jest-haste-map "^27.5.1"
    jest-matcher-utils "^27.5.1"
    jest-message-util "^27.5.1"
    jest-util "^27.5.1"
    natural-compare "^1.4.0"
    pretty-format "^27.5.1"
    semver "^7.3.2"

jest-util@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-util/-/jest-util-27.5.1.tgz"
  integrity sha512-Kv2o/8jNvX1MQ0KGtw480E/w4fBCDOnH6+6DmeKi6LZUIlKA5kwY0YNdlzaWTiVgxqAqik11QyxDOKk543aKXw==
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-validate/-/jest-validate-27.5.1.tgz"
  integrity sha512-thkNli0LYTmOI1tDB3FI1S1RTp/Bqyd9pTarJwL87OIBFuqEb5Apv5EaApEudYg4g86e3CT6kM0RowkhtEnCBQ==
  dependencies:
    "@jest/types" "^27.5.1"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^27.5.1"
    leven "^3.1.0"
    pretty-format "^27.5.1"

jest-watch-typeahead@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/jest-watch-typeahead/-/jest-watch-typeahead-1.0.0.tgz"
  integrity sha512-jxoszalAb394WElmiJTFBMzie/RDCF+W7Q29n5LzOPtcoQoHWfdUtHFkbhgf5NwWe8uMOxvKb/g7ea7CshfkTw==
  dependencies:
    ansi-escapes "^4.3.1"
    chalk "^4.0.0"
    jest-regex-util "^27.0.0"
    jest-watcher "^27.0.0"
    slash "^4.0.0"
    string-length "^5.0.1"
    strip-ansi "^7.0.1"

jest-watcher@^27.0.0, jest-watcher@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.5.1.tgz"
  integrity sha512-z676SuD6Z8o8qbmEGhoEUFOM1+jfEiL3DXHK/xgEiG2EyNYfFG60jluWcupY6dATjfEsKQuibReS1djInQnoVw==
  dependencies:
    "@jest/test-result" "^27.5.1"
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    jest-util "^27.5.1"
    string-length "^4.0.1"

jest-worker@^26.2.1:
  version "26.6.2"
  resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz"
  integrity sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest-worker@^27.0.2, jest-worker@^27.3.1, jest-worker@^27.4.5, jest-worker@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^27.4.3:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest/-/jest-27.5.1.tgz"
  integrity sha512-Yn0mADZB89zTtjkPJEXwrac3LHudkQMR+Paqa8uxJHCBr9agxztUifWCyiYrjhMPBoUVBjyny0I7XH6ozDr7QQ==
  dependencies:
    "@jest/core" "^27.5.1"
    import-local "^3.0.2"
    jest-cli "^27.5.1"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://mirrors.tencent.com/npm/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsdom@^16.6.0:
  version "16.7.0"
  resolved "https://registry.npmjs.org/jsdom/-/jsdom-16.7.0.tgz"
  integrity sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw==
  dependencies:
    abab "^2.0.5"
    acorn "^8.2.4"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.3.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.1"
    domexception "^2.0.1"
    escodegen "^2.0.0"
    form-data "^3.0.0"
    html-encoding-sniffer "^2.0.1"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "^5.0.0"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.0"
    parse5 "6.0.1"
    saxes "^5.0.1"
    symbol-tree "^3.2.4"
    tough-cookie "^4.0.0"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.5.0"
    ws "^7.4.6"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  integrity sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json-schema@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz"
  integrity sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json2mq@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/json2mq/-/json2mq-0.2.0.tgz"
  integrity sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=
  dependencies:
    string-convert "^0.2.0"

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz"
  integrity sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2, json5@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.0.tgz"
  integrity sha512-f+8cldu7X/y7RAJurMEJmdoKXGB/X550w2Nr3tTbezL6RwEE/iMcm+tZnXeoZtKuOq6ft8+CqzEkrIgx1fPoQA==
  dependencies:
    minimist "^1.2.5"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonpointer@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/jsonpointer/-/jsonpointer-5.0.0.tgz"
  integrity sha512-PNYZIdMjVIvVgDSYKTT63Y+KZ6IZvGRNNWcxwD+GNnUz1MKPfv30J8ueCjdwcN0nDx2SlshgyB7Oy0epAzVRRg==

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.2.1.tgz"
  integrity sha512-uP5vu8xfy2F9A6LGC22KO7e2/vGTS1MhP+18f++ZNlf0Ohaxbc9nIEwHAsejlJKyzfZzU5UIhe5ItYkitcZnZA==
  dependencies:
    array-includes "^3.1.3"
    object.assign "^4.1.2"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==

klona@^2.0.4, klona@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/klona/-/klona-2.0.5.tgz"
  integrity sha512-pJiBpiXMbt7dkzXe8Ghj/u4FfXOOa98fPW+bihOJ4SjnoijweJrNThJfd3ifXpXhREjpoF2mZVH1GfS9LV3kHQ==

language-subtag-registry@~0.3.2:
  version "0.3.21"
  resolved "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.21.tgz"
  integrity sha512-L0IqwlIXjilBVVYKFT37X9Ih11Um5NEl9cbJIuU/SwP/zEEAbBPOnEeeuxVMf45ydWQRDQN3Nqc96OgbH1K+Pg==

language-tags@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/language-tags/-/language-tags-1.0.5.tgz"
  integrity sha1-0yHbxNowuovzAk4ED6XBRmH5GTo=
  dependencies:
    language-subtag-registry "~0.3.2"

less-loader@^10.2.0:
  version "10.2.0"
  resolved "https://registry.npmjs.org/less-loader/-/less-loader-10.2.0.tgz"
  integrity sha512-AV5KHWvCezW27GT90WATaDnfXBv99llDbtaj4bshq6DvAihMdNjaPDcUMa6EXKLRF+P2opFenJp89BXg91XLYg==
  dependencies:
    klona "^2.0.4"

less@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/less/-/less-4.1.2.tgz"
  integrity sha512-EoQp/Et7OSOVu0aJknJOtlXZsnr8XE8KwuzTHOLeVSEx8pVWUICc8Q0VYRHgzyjX78nMEyC/oztWFbgyhtNfDA==
  dependencies:
    copy-anything "^2.0.1"
    parse-node-version "^1.0.1"
    tslib "^2.3.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    needle "^2.5.2"
    source-map "~0.6.0"

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  integrity sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lilconfig@^2.0.3, lilconfig@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.4.tgz"
  integrity sha512-bfTIN7lEsiooCocSISTWXkiWJkRqtL9wYtYy+8EK3Y41qh3mpwPU0ycTOgjdY9ErwXCc8QyrQp82bdL0Xkm9yA==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

loader-runner@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/loader-runner/-/loader-runner-4.2.0.tgz"
  integrity sha512-92+huvxMvYlMzMt0iIOukcwYBFpkYJdpl2xsZ7LrlayO7E8SOv+JJUEK17B/dJIHAOLMfh2dZZ/Y18WgmGtYNw==

loader-utils@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.0.tgz"
  integrity sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.2.tgz"
  integrity sha512-TM57VeHptv569d/GKh6TAYdzKblwDNiumOdkFnejjD0XwTH87K90w3O7AiJRqdQoXygvi1VQTJTLGhJl7WqA7A==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

loader-utils@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-3.2.0.tgz"
  integrity sha512-HVl9ZqccQihZ7JM85dco1MvO9G+ONvxoGa9rkhzFsneGLKSUg1gJf9bWzhRhcvm2qChhWpebQhP44qxjKIUCaQ==

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz"
  integrity sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.flow@^3.5.0:
  version "3.5.0"
  resolved "https://registry.npmjs.org/lodash.flow/-/lodash.flow-3.5.0.tgz"
  integrity sha1-h79AKSuM+D5OjOGjrkIJ4gBxZ1o=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.14, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.7.0:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz"
  integrity sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==
  dependencies:
    tslib "^2.0.3"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

magic-string@^0.25.0, magic-string@^0.25.7:
  version "0.25.7"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.25.7.tgz"
  integrity sha512-4CrMT5DOHTDk4HYDlzmwu4FVCcIYI8gauveasrdCu2IKIFOJ3f0v/8MDGJCDL9oD2ppz/Av1b0Nj345H9M+XIA==
  dependencies:
    sourcemap-codec "^1.4.4"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-2.1.0.tgz"
  integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0, make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz"
  integrity sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==
  dependencies:
    tmpl "1.0.5"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz"
  integrity sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz"
  integrity sha512-iV3XNKw06j5Q7mi6h+9vbx23Tv7JkjEVgKHW4pimwyDGWm0OIQntJJ+u1C6mg6mK1EaTv42XQ7w76yuzH7M2cA==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.1.2, memfs@^3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/memfs/-/memfs-3.4.1.tgz"
  integrity sha512-1c9VPVvW5P7I85c35zAdEr1TD5+F11IToIHIlrVIcflfnzPkJa0ZoYEoEdYDP8KgPFoSZ/opDrUsAoZWym3mtw==
  dependencies:
    fs-monkey "1.0.3"

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz"
  integrity sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg==
  dependencies:
    braces "^3.0.1"
    picomatch "^2.2.3"

mime-db@1.51.0, "mime-db@>= 1.43.0 < 2":
  version "1.51.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.51.0.tgz"
  integrity sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g==

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.34"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.34.tgz"
  integrity sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A==
  dependencies:
    mime-db "1.51.0"

mime@1.6.0, mime@^1.4.1:
  version "1.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mini-create-react-context@^0.4.0:
  version "0.4.1"
  resolved "https://registry.npmjs.org/mini-create-react-context/-/mini-create-react-context-0.4.1.tgz"
  integrity sha512-YWCYEmd5CQeHGSAKrYvXgmzzkrvssZcuuQDDeqkT+PziKGMgE+0MCCtcKbROzocGBG1meBLl2FotlRwf4gAzbQ==
  dependencies:
    "@babel/runtime" "^7.12.1"
    tiny-warning "^1.0.3"

mini-css-extract-plugin@^2.4.5:
  version "2.5.3"
  resolved "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-2.5.3.tgz"
  integrity sha512-YseMB8cs8U/KCaAGQoqYmfUuhhGW0a9p9XvWXrxVOkE3/IiISTLw4ALNt7JR5B2eYauFM+PQGSbXMDmVbR7Tfw==
  dependencies:
    schema-utils "^4.0.0"

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  integrity sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==

minimatch@3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz"
  integrity sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz"
  integrity sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==

mkdirp@^0.5.5, mkdirp@~0.5.1:
  version "0.5.5"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.5.tgz"
  integrity sha512-NKmAlESf6jMGym1++R0Ra7wvhV+wFW63FaSOFPwRahvea0gMUcGUhVeAg/0BC0wiv9ih5NYPB1Wn1UEI1/L+xQ==
  dependencies:
    minimist "^1.2.5"

moment@^2.24.0, moment@^2.25.3, moment@^2.29.1:
  version "2.29.1"
  resolved "https://registry.npmjs.org/moment/-/moment-2.29.1.tgz"
  integrity sha512-kHmoybcPV8Sqy59DwNDY3Jefr64lK/by/da0ViFcuA4DH0vQg5Q6Ze5VimxkfQNSC+Mls/Kx53s7TjP1RhFEDQ==

monaco-editor-webpack-plugin@^4.1.1:
  version "4.2.0"
  resolved "https://registry.npmjs.org/monaco-editor-webpack-plugin/-/monaco-editor-webpack-plugin-4.2.0.tgz"
  integrity sha512-/P3sFiEgBl+Y50he4mbknMhbLJVop5gBUZiPS86SuHUDOOnQiQ5rL1jU5lwt1XKAwMEkhwZbUwqaHxTPkb1Utw==
  dependencies:
    loader-utils "^2.0.0"

monaco-editor@^0.26.1:
  version "0.26.1"
  resolved "https://registry.npmjs.org/monaco-editor/-/monaco-editor-0.26.1.tgz"
  integrity sha512-mm45nUrBDk0DgZKgbD7+bhDOtcAFNGPJJRAdS6Su1kTGl6XEgC7U3xOmDUW/0RrLf+jlvCGaqLvD4p2VjwuwwQ==

mrmime@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/mrmime/-/mrmime-1.0.0.tgz"
  integrity sha512-a70zx7zFfVO7XpnQ2IX1Myh9yY4UYvfld/dikWRnsXxbyvMcfz+u6UfgNAtH+k2QqtJuzVpv6eLTx1G2+WKZbQ==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2, ms@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npmjs.org/multicast-dns/-/multicast-dns-6.2.3.tgz"
  integrity sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g==
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

nanoid@^3.3.1:
  version "3.3.1"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.1.tgz"
  integrity sha512-n6Vs/3KGyxPQd6uO0eH4Bv0ojGSUvuLlIHtC3Y0kEO23YRge8H9x1GCzLn28YX0H66pMkxuaeESFq4tKISKwdw==

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "https://mirrors.tencent.com/npm/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

needle@^2.5.2:
  version "2.9.1"
  resolved "https://registry.npmjs.org/needle/-/needle-2.9.1.tgz"
  integrity sha512-6R9fqJ5Zcmf+uYaFgdIHmLwNldn5HbK8L5ybn7Uz+ylX/rnOsSp1AHcvQSrCaFN+qNM1wpymHqD7mVasEOlHGQ==
  dependencies:
    debug "^3.2.6"
    iconv-lite "^0.4.4"
    sax "^1.2.4"

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

next-tick@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/next-tick/-/next-tick-1.0.0.tgz"
  integrity sha1-yobR/ogoFpsBICCOPchCS524NCw=

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz"
  integrity sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-forge@^1.2.0:
  version "1.2.1"
  resolved "https://registry.npmjs.org/node-forge/-/node-forge-1.2.1.tgz"
  integrity sha512-Fcvtbb+zBcZXbTTVwqGA5W+MKBj56UjVRevvchv5XrcyXbmNdesfZL37nlcWOfpgHhgmxApw3tQbTr4CqNmX4w==

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.2.tgz"
  integrity sha512-XxYDdcQ6eKqp/YjI+tb2C5WM2LgjnZrfYg4vgQt49EK268b6gYCHsBLrK2qvJo4FmCtqmKezb0WZFK4fkrZNsg==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz"
  integrity sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz"
  integrity sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==
  dependencies:
    boolbase "~1.0.0"

nth-check@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/nth-check/-/nth-check-2.0.1.tgz"
  integrity sha512-it1vE95zF6dTT9lBsYbxvqh0Soy4SPowchj0UBGj/V6cTPnXXtQOPUbhZ6CmGzAD/rW22LQK6E96pcdJXk4A4w==
  dependencies:
    boolbase "^1.0.0"

nwsapi@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.0.tgz"
  integrity sha512-h2AatdwYH+JHiZpv7pt/gSX1XoRGb7L/qSIeuqA6GwYoF9w1vP1cw42TO0aI2pNyshRK5893hNSl+1//vHK7hQ==

object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-2.2.0.tgz"
  integrity sha512-gScRMn0bS5fH+IuwyIFgnh9zBdo4DV+6GhygmWM9HyNJSgS0hScp1f5vjtm7oIIOiT9trXrShAkLFSc2IqKNgw==

object-inspect@^1.11.0, object-inspect@^1.9.0:
  version "1.12.0"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.0.tgz"
  integrity sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g==

object-is@^1.0.1:
  version "1.1.5"
  resolved "https://registry.npmjs.org/object-is/-/object-is-1.1.5.tgz"
  integrity sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.0, object.assign@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.2.tgz"
  integrity sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ==
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.entries@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.5.tgz"
  integrity sha512-TyxmjUoZggd4OrrU1W66FMDG6CuqJxsFvymeyXI51+vQLN67zYfZseptRge703kKQdo4uccgAKebXFcRCzk4+g==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.fromentries@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.5.tgz"
  integrity sha512-CAyG5mWQRRiBU57Re4FKoTBjXfDoNwdFVH2Y1tS9PqCsfUTymAohOkEMSG3aRNKmv4lV3O7p1et7c187q6bynw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.getownpropertydescriptors@^2.1.0:
  version "2.1.3"
  resolved "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.3.tgz"
  integrity sha512-VdDoCwvJI4QdC6ndjpqFmoL3/+HxffFBbcJzKi5hwLLqqx3mdbedRpfZDdK0SrOSauj8X4GzBvnDZl4vTN7dOw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.hasown@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/object.hasown/-/object.hasown-1.1.0.tgz"
  integrity sha512-MhjYRfj3GBlhSkDHo6QmvgjRLXQ2zndabdf3nX0yTyZK9rPfxb6uRpAac8HXNLy1GpqWtZ81Qh4v3uOls2sRAg==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.values@^1.1.0, object.values@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmjs.org/object.values/-/object.values-1.1.5.tgz"
  integrity sha512-QUZRW0ilQ3PnPpbNtgdNV1PDbEqLIiSFB3l+EnGtBQ/8SUTLj1PZwtQHABZtLgwpJZTSZhuGLOGk57Drx2IvYg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.9, open@^8.4.0:
  version "8.4.0"
  resolved "https://registry.npmjs.org/open/-/open-8.4.0.tgz"
  integrity sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q==
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

opener@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz"
  integrity sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==

optionator@^0.8.1:
  version "0.8.3"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz"
  integrity sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

optionator@^0.9.3:
  version "0.9.3"
  resolved "https://mirrors.tencent.com/npm/optionator/-/optionator-0.9.3.tgz"
  integrity sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"

p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz"
  integrity sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz"
  integrity sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
  integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^4.5.0:
  version "4.6.1"
  resolved "https://registry.npmjs.org/p-retry/-/p-retry-4.6.1.tgz"
  integrity sha512-e2xXGNhZOZ0lfgR9kL34iGlU8N/KO0xZnQxVEwdeOvpqNDQfdnxIYizvWtK8RglUa3bGqI8g0R/BdfzLMxRkiA==
  dependencies:
    "@types/retry" "^0.12.0"
    retry "^0.13.1"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz"
  integrity sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-node-version@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parse-node-version/-/parse-node-version-1.0.1.tgz"
  integrity sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==

parse5@6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz"
  integrity sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz"
  integrity sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.6, path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@^1.7.0:
  version "1.8.0"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.8.0.tgz"
  integrity sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==
  dependencies:
    isarray "0.0.1"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz"
  integrity sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.2.3:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/pify/-/pify-4.0.1.tgz"
  integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==

pirates@^4.0.4:
  version "4.0.5"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.5.tgz"
  integrity sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==

pkg-dir@^4.1.0, pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

pkg-up@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/pkg-up/-/pkg-up-3.1.0.tgz"
  integrity sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==
  dependencies:
    find-up "^3.0.0"

portfinder@^1.0.28:
  version "1.0.28"
  resolved "https://registry.npmjs.org/portfinder/-/portfinder-1.0.28.tgz"
  integrity sha512-Se+2isanIcEqf2XMHjyUKskczxbPH7dQnlMjXX6+dybayyHvAf/TCgyMRlzf/B6QDhAEFOGes0pzRo3by4AbMA==
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.5"

postcss-attribute-case-insensitive@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-5.0.0.tgz"
  integrity sha512-b4g9eagFGq9T5SWX4+USfVyjIb3liPnjhHHRMP7FMB2kFVpYyfEscV0wP3eaXhKlcHKUut8lt5BGoeylWA/dBQ==
  dependencies:
    postcss-selector-parser "^6.0.2"

postcss-browser-comments@^4:
  version "4.0.0"
  resolved "https://registry.npmjs.org/postcss-browser-comments/-/postcss-browser-comments-4.0.0.tgz"
  integrity sha512-X9X9/WN3KIvY9+hNERUqX9gncsgBA25XaeR+jshHz2j8+sYyHktHw1JdKuMjeLpGktXidqDhA7b/qm1mrBDmgg==

postcss-calc@^8.2.0:
  version "8.2.4"
  resolved "https://registry.npmjs.org/postcss-calc/-/postcss-calc-8.2.4.tgz"
  integrity sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==
  dependencies:
    postcss-selector-parser "^6.0.9"
    postcss-value-parser "^4.2.0"

postcss-clamp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/postcss-clamp/-/postcss-clamp-4.0.0.tgz"
  integrity sha512-FsMmeBZtymFN7Jtlnw9is8I4nB+qEEb/qS0ZLTIqcKiwZyHBq44Yhv29Q+VQsTGHYFqIr/s/9tqvNM7j+j1d+g==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-functional-notation@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmjs.org/postcss-color-functional-notation/-/postcss-color-functional-notation-4.2.2.tgz"
  integrity sha512-DXVtwUhIk4f49KK5EGuEdgx4Gnyj6+t2jBSEmxvpIK9QI40tWrpS2Pua8Q7iIZWBrki2QOaeUdEaLPPa91K0RQ==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-hex-alpha@^8.0.3:
  version "8.0.3"
  resolved "https://registry.npmjs.org/postcss-color-hex-alpha/-/postcss-color-hex-alpha-8.0.3.tgz"
  integrity sha512-fESawWJCrBV035DcbKRPAVmy21LpoyiXdPTuHUfWJ14ZRjY7Y7PA6P4g8z6LQGYhU1WAxkTxjIjurXzoe68Glw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-rebeccapurple@^7.0.2:
  version "7.0.2"
  resolved "https://registry.npmjs.org/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-7.0.2.tgz"
  integrity sha512-SFc3MaocHaQ6k3oZaFwH8io6MdypkUtEy/eXzXEB1vEQlO3S3oDc/FSZA8AsS04Z25RirQhlDlHLh3dn7XewWw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-colormin@^5.2.5:
  version "5.2.5"
  resolved "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-5.2.5.tgz"
  integrity sha512-+X30aDaGYq81mFqwyPpnYInsZQnNpdxMX0ajlY7AExCexEFkPVV+KrO7kXwayqEWL2xwEbNQ4nUO0ZsRWGnevg==
  dependencies:
    browserslist "^4.16.6"
    caniuse-api "^3.0.0"
    colord "^2.9.1"
    postcss-value-parser "^4.2.0"

postcss-convert-values@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-5.0.4.tgz"
  integrity sha512-bugzSAyjIexdObovsPZu/sBCTHccImJxLyFgeV0MmNBm/Lw5h5XnjfML6gzEmJ3A6nyfCW7hb1JXzcsA4Zfbdw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-custom-media@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/postcss-custom-media/-/postcss-custom-media-8.0.0.tgz"
  integrity sha512-FvO2GzMUaTN0t1fBULDeIvxr5IvbDXcIatt6pnJghc736nqNgsGao5NT+5+WVLAQiTt6Cb3YUms0jiPaXhL//g==

postcss-custom-properties@^12.1.4:
  version "12.1.4"
  resolved "https://registry.npmjs.org/postcss-custom-properties/-/postcss-custom-properties-12.1.4.tgz"
  integrity sha512-i6AytuTCoDLJkWN/MtAIGriJz3j7UX6bV7Z5t+KgFz+dwZS15/mlTJY1S0kRizlk6ba0V8u8hN50Fz5Nm7tdZw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-custom-selectors@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/postcss-custom-selectors/-/postcss-custom-selectors-6.0.0.tgz"
  integrity sha512-/1iyBhz/W8jUepjGyu7V1OPcGbc636snN1yXEQCinb6Bwt7KxsiU7/bLQlp8GwAXzCh7cobBU5odNn/2zQWR8Q==
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-dir-pseudo-class@^6.0.4:
  version "6.0.4"
  resolved "https://registry.npmjs.org/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-6.0.4.tgz"
  integrity sha512-I8epwGy5ftdzNWEYok9VjW9whC4xnelAtbajGv4adql4FIF09rnrxnA9Y8xSHN47y7gqFIv10C5+ImsLeJpKBw==
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-discard-comments@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-5.0.3.tgz"
  integrity sha512-6W5BemziRoqIdAKT+1QjM4bNcJAQ7z7zk073730NHg4cUXh3/rQHHj7pmYxUB9aGhuRhBiUf0pXvIHkRwhQP0Q==

postcss-discard-duplicates@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-5.0.3.tgz"
  integrity sha512-vPtm1Mf+kp7iAENTG7jI1MN1lk+fBqL5y+qxyi4v3H+lzsXEdfS3dwUZD45KVhgzDEgduur8ycB4hMegyMTeRw==

postcss-discard-empty@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-5.0.3.tgz"
  integrity sha512-xGJugpaXKakwKI7sSdZjUuN4V3zSzb2Y0LOlmTajFbNinEjTfVs9PFW2lmKBaC/E64WwYppfqLD03P8l9BuueA==

postcss-discard-overridden@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-5.0.4.tgz"
  integrity sha512-3j9QH0Qh1KkdxwiZOW82cId7zdwXVQv/gRXYDnwx5pBtR1sTkU4cXRK9lp5dSdiM0r0OICO/L8J6sV1/7m0kHg==

postcss-double-position-gradients@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/postcss-double-position-gradients/-/postcss-double-position-gradients-3.1.0.tgz"
  integrity sha512-oz73I08yMN3oxjj0s8mED1rG+uOYoK3H8N9RjQofyg52KBRNmePJKg3fVwTpL2U5ZFbCzXoZBsUD/CvZdlqE4Q==
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

postcss-env-function@^4.0.5:
  version "4.0.5"
  resolved "https://registry.npmjs.org/postcss-env-function/-/postcss-env-function-4.0.5.tgz"
  integrity sha512-gPUJc71ji9XKyl0WSzAalBeEA/89kU+XpffpPxSaaaZ1c48OL36r1Ep5R6+9XAPkIiDlSvVAwP4io12q/vTcvA==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-flexbugs-fixes@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-5.0.2.tgz"
  integrity sha512-18f9voByak7bTktR2QgDveglpn9DTbBWPUzSOe9g0N4WR/2eSt6Vrcbf0hmspvMI6YWGywz6B9f7jzpFNJJgnQ==

postcss-focus-visible@^6.0.4:
  version "6.0.4"
  resolved "https://registry.npmjs.org/postcss-focus-visible/-/postcss-focus-visible-6.0.4.tgz"
  integrity sha512-QcKuUU/dgNsstIK6HELFRT5Y3lbrMLEOwG+A4s5cA+fx3A3y/JTq3X9LaOj3OC3ALH0XqyrgQIgey/MIZ8Wczw==
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-focus-within@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-focus-within/-/postcss-focus-within-5.0.4.tgz"
  integrity sha512-vvjDN++C0mu8jz4af5d52CB184ogg/sSxAFS+oUJQq2SuCe7T5U2iIsVJtsCp2d6R4j0jr5+q3rPkBVZkXD9fQ==
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-font-variant@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/postcss-font-variant/-/postcss-font-variant-5.0.0.tgz"
  integrity sha512-1fmkBaCALD72CK2a9i468mA/+tr9/1cBxRRMXOUaZqO43oWPR5imcyPjXwuv7PXbCid4ndlP5zWhidQVVa3hmA==

postcss-gap-properties@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/postcss-gap-properties/-/postcss-gap-properties-3.0.3.tgz"
  integrity sha512-rPPZRLPmEKgLk/KlXMqRaNkYTUpE7YC+bOIQFN5xcu1Vp11Y4faIXv6/Jpft6FMnl6YRxZqDZG0qQOW80stzxQ==

postcss-image-set-function@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/postcss-image-set-function/-/postcss-image-set-function-4.0.6.tgz"
  integrity sha512-KfdC6vg53GC+vPd2+HYzsZ6obmPqOk6HY09kttU19+Gj1nC3S3XBVEXDHxkhxTohgZqzbUb94bKXvKDnYWBm/A==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-initial@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-initial/-/postcss-initial-4.0.1.tgz"
  integrity sha512-0ueD7rPqX8Pn1xJIjay0AZeIuDoF+V+VvMt/uOnn+4ezUKhZM/NokDeP6DwMNyIoYByuN/94IQnt5FEkaN59xQ==

postcss-js@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.0.tgz"
  integrity sha512-77QESFBwgX4irogGVPgQ5s07vLvFqWr228qZY+w6lW599cRlK/HmnlivnnVUxkjHnCu4J16PDMHcH+e+2HbvTQ==
  dependencies:
    camelcase-css "^2.0.1"

postcss-lab-function@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/postcss-lab-function/-/postcss-lab-function-4.1.1.tgz"
  integrity sha512-j3Z0WQCimY2tMle++YcmygnnVbt6XdnrCV1FO2IpzaCSmtTF2oO8h4ZYUA1Q+QHYroIiaWPvNHt9uBR4riCksQ==
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

postcss-load-config@^3.1.0:
  version "3.1.3"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.3.tgz"
  integrity sha512-5EYgaM9auHGtO//ljHH+v/aC/TQ5LHXtL7bQajNAUBKUVKiYE8rYpFms7+V26D9FncaGe2zwCoPQsFKb5zF/Hw==
  dependencies:
    lilconfig "^2.0.4"
    yaml "^1.10.2"

postcss-loader@^6.2.1:
  version "6.2.1"
  resolved "https://registry.npmjs.org/postcss-loader/-/postcss-loader-6.2.1.tgz"
  integrity sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q==
  dependencies:
    cosmiconfig "^7.0.0"
    klona "^2.0.5"
    semver "^7.3.5"

postcss-logical@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-logical/-/postcss-logical-5.0.4.tgz"
  integrity sha512-RHXxplCeLh9VjinvMrZONq7im4wjWGlRJAqmAVLXyZaXwfDWP73/oq4NdIp+OZwhQUMj0zjqDfM5Fj7qby+B4g==

postcss-media-minmax@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/postcss-media-minmax/-/postcss-media-minmax-5.0.0.tgz"
  integrity sha512-yDUvFf9QdFZTuCUg0g0uNSHVlJ5X1lSzDZjPSFaiCWvjgsvu8vEVxtahPrLMinIDEEGnx6cBe6iqdx5YWz08wQ==

postcss-merge-longhand@^5.0.6:
  version "5.0.6"
  resolved "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-5.0.6.tgz"
  integrity sha512-rkmoPwQO6ymJSmWsX6l2hHeEBQa7C4kJb9jyi5fZB1sE8nSCv7sqchoYPixRwX/yvLoZP2y6FA5kcjiByeJqDg==
  dependencies:
    postcss-value-parser "^4.2.0"
    stylehacks "^5.0.3"

postcss-merge-rules@^5.0.6:
  version "5.0.6"
  resolved "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-5.0.6.tgz"
  integrity sha512-nzJWJ9yXWp8AOEpn/HFAW72WKVGD2bsLiAmgw4hDchSij27bt6TF+sIK0cJUBAYT3SGcjtGGsOR89bwkkMuMgQ==
  dependencies:
    browserslist "^4.16.6"
    caniuse-api "^3.0.0"
    cssnano-utils "^3.0.2"
    postcss-selector-parser "^6.0.5"

postcss-minify-font-values@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-5.0.4.tgz"
  integrity sha512-RN6q3tyuEesvyCYYFCRGJ41J1XFvgV+dvYGHr0CeHv8F00yILlN8Slf4t8XW4IghlfZYCeyRrANO6HpJ948ieA==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-minify-gradients@^5.0.6:
  version "5.0.6"
  resolved "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-5.0.6.tgz"
  integrity sha512-E/dT6oVxB9nLGUTiY/rG5dX9taugv9cbLNTFad3dKxOO+BQg25Q/xo2z2ddG+ZB1CbkZYaVwx5blY8VC7R/43A==
  dependencies:
    colord "^2.9.1"
    cssnano-utils "^3.0.2"
    postcss-value-parser "^4.2.0"

postcss-minify-params@^5.0.5:
  version "5.0.5"
  resolved "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-5.0.5.tgz"
  integrity sha512-YBNuq3Rz5LfLFNHb9wrvm6t859b8qIqfXsWeK7wROm3jSKNpO1Y5e8cOyBv6Acji15TgSrAwb3JkVNCqNyLvBg==
  dependencies:
    browserslist "^4.16.6"
    cssnano-utils "^3.0.2"
    postcss-value-parser "^4.2.0"

postcss-minify-selectors@^5.1.3:
  version "5.1.3"
  resolved "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-5.1.3.tgz"
  integrity sha512-9RJfTiQEKA/kZhMaEXND893nBqmYQ8qYa/G+uPdVnXF6D/FzpfI6kwBtWEcHx5FqDbA79O9n6fQJfrIj6M8jvQ==
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-modules-extract-imports@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz"
  integrity sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==

postcss-modules-local-by-default@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.0.tgz"
  integrity sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ==
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz"
  integrity sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg==
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  integrity sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==
  dependencies:
    icss-utils "^5.0.0"

postcss-nested@5.0.6:
  version "5.0.6"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.6.tgz"
  integrity sha512-rKqm2Fk0KbA8Vt3AdGN0FB9OBOMDVajMG6ZCf/GoHgdxUJ4sBFp0A/uMIRm+MJUdo33YXEtjqIz8u7DAp8B7DA==
  dependencies:
    postcss-selector-parser "^6.0.6"

postcss-nesting@^10.1.2:
  version "10.1.2"
  resolved "https://registry.npmjs.org/postcss-nesting/-/postcss-nesting-10.1.2.tgz"
  integrity sha512-dJGmgmsvpzKoVMtDMQQG/T6FSqs6kDtUDirIfl4KnjMCiY9/ETX8jdKyCd20swSRAbUYkaBKV20pxkzxoOXLqQ==
  dependencies:
    postcss-selector-parser "^6.0.8"

postcss-normalize-charset@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-5.0.3.tgz"
  integrity sha512-iKEplDBco9EfH7sx4ut7R2r/dwTnUqyfACf62Unc9UiyFuI7uUqZZtY+u+qp7g8Qszl/U28HIfcsI3pEABWFfA==

postcss-normalize-display-values@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-5.0.3.tgz"
  integrity sha512-FIV5FY/qs4Ja32jiDb5mVj5iWBlS3N8tFcw2yg98+8MkRgyhtnBgSC0lxU+16AMHbjX5fbSJgw5AXLMolonuRQ==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-positions@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-5.0.4.tgz"
  integrity sha512-qynirjBX0Lc73ROomZE3lzzmXXTu48/QiEzKgMeqh28+MfuHLsuqC9po4kj84igZqqFGovz8F8hf44hA3dPYmQ==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-repeat-style@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.0.4.tgz"
  integrity sha512-Innt+wctD7YpfeDR7r5Ik6krdyppyAg2HBRpX88fo5AYzC1Ut/l3xaxACG0KsbX49cO2n5EB13clPwuYVt8cMA==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-string@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-5.0.4.tgz"
  integrity sha512-Dfk42l0+A1CDnVpgE606ENvdmksttLynEqTQf5FL3XGQOyqxjbo25+pglCUvziicTxjtI2NLUR6KkxyUWEVubQ==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-timing-functions@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.0.3.tgz"
  integrity sha512-QRfjvFh11moN4PYnJ7hia4uJXeFotyK3t2jjg8lM9mswleGsNw2Lm3I5wO+l4k1FzK96EFwEVn8X8Ojrp2gP4g==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-unicode@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-5.0.4.tgz"
  integrity sha512-W79Regn+a+eXTzB+oV/8XJ33s3pDyFTND2yDuUCo0Xa3QSy1HtNIfRVPXNubHxjhlqmMFADr3FSCHT84ITW3ig==
  dependencies:
    browserslist "^4.16.6"
    postcss-value-parser "^4.2.0"

postcss-normalize-url@^5.0.5:
  version "5.0.5"
  resolved "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-5.0.5.tgz"
  integrity sha512-Ws3tX+PcekYlXh+ycAt0wyzqGthkvVtZ9SZLutMVvHARxcpu4o7vvXcNoiNKyjKuWecnjS6HDI3fjBuDr5MQxQ==
  dependencies:
    normalize-url "^6.0.1"
    postcss-value-parser "^4.2.0"

postcss-normalize-whitespace@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.0.4.tgz"
  integrity sha512-wsnuHolYZjMwWZJoTC9jeI2AcjA67v4UuidDrPN9RnX8KIZfE+r2Nd6XZRwHVwUiHmRvKQtxiqo64K+h8/imaw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize@^10.0.1:
  version "10.0.1"
  resolved "https://registry.npmjs.org/postcss-normalize/-/postcss-normalize-10.0.1.tgz"
  integrity sha512-+5w18/rDev5mqERcG3W5GZNMJa1eoYYNGo8gB7tEwaos0ajk3ZXAI4mHGcNT47NE+ZnZD1pEpUOFLvltIwmeJA==
  dependencies:
    "@csstools/normalize.css" "*"
    postcss-browser-comments "^4"
    sanitize.css "*"

postcss-opacity-percentage@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/postcss-opacity-percentage/-/postcss-opacity-percentage-1.1.2.tgz"
  integrity sha512-lyUfF7miG+yewZ8EAk9XUBIlrHyUE6fijnesuz+Mj5zrIHIEw6KcIZSOk/elVMqzLvREmXB83Zi/5QpNRYd47w==

postcss-ordered-values@^5.0.5:
  version "5.0.5"
  resolved "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-5.0.5.tgz"
  integrity sha512-mfY7lXpq+8bDEHfP+muqibDPhZ5eP9zgBEF9XRvoQgXcQe2Db3G1wcvjbnfjXG6wYsl+0UIjikqq4ym1V2jGMQ==
  dependencies:
    cssnano-utils "^3.0.2"
    postcss-value-parser "^4.2.0"

postcss-overflow-shorthand@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/postcss-overflow-shorthand/-/postcss-overflow-shorthand-3.0.3.tgz"
  integrity sha512-CxZwoWup9KXzQeeIxtgOciQ00tDtnylYIlJBBODqkgS/PU2jISuWOL/mYLHmZb9ZhZiCaNKsCRiLp22dZUtNsg==

postcss-page-break@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/postcss-page-break/-/postcss-page-break-3.0.4.tgz"
  integrity sha512-1JGu8oCjVXLa9q9rFTo4MbeeA5FMe00/9C7lN4va606Rdb+HkxXtXsmEDrIraQ11fGz/WvKWa8gMuCKkrXpTsQ==

postcss-place@^7.0.4:
  version "7.0.4"
  resolved "https://registry.npmjs.org/postcss-place/-/postcss-place-7.0.4.tgz"
  integrity sha512-MrgKeiiu5OC/TETQO45kV3npRjOFxEHthsqGtkh3I1rPbZSbXGD/lZVi9j13cYh+NA8PIAPyk6sGjT9QbRyvSg==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-preset-env@^7.0.1:
  version "7.4.1"
  resolved "https://registry.npmjs.org/postcss-preset-env/-/postcss-preset-env-7.4.1.tgz"
  integrity sha512-UvBVvPJ2vb4odAtckSbryndyBz+Me1q8wawqq0qznpDXy188I+8W5Sa929sCPqw2/NSYnqpHJbo41BKso3+I9A==
  dependencies:
    "@csstools/postcss-color-function" "^1.0.2"
    "@csstools/postcss-font-format-keywords" "^1.0.0"
    "@csstools/postcss-hwb-function" "^1.0.0"
    "@csstools/postcss-ic-unit" "^1.0.0"
    "@csstools/postcss-is-pseudo-class" "^2.0.0"
    "@csstools/postcss-normalize-display-values" "^1.0.0"
    "@csstools/postcss-oklab-function" "^1.0.1"
    "@csstools/postcss-progressive-custom-properties" "^1.2.0"
    autoprefixer "^10.4.2"
    browserslist "^4.19.1"
    css-blank-pseudo "^3.0.3"
    css-has-pseudo "^3.0.4"
    css-prefers-color-scheme "^6.0.3"
    cssdb "^6.3.1"
    postcss-attribute-case-insensitive "^5.0.0"
    postcss-clamp "^4.0.0"
    postcss-color-functional-notation "^4.2.2"
    postcss-color-hex-alpha "^8.0.3"
    postcss-color-rebeccapurple "^7.0.2"
    postcss-custom-media "^8.0.0"
    postcss-custom-properties "^12.1.4"
    postcss-custom-selectors "^6.0.0"
    postcss-dir-pseudo-class "^6.0.4"
    postcss-double-position-gradients "^3.1.0"
    postcss-env-function "^4.0.5"
    postcss-focus-visible "^6.0.4"
    postcss-focus-within "^5.0.4"
    postcss-font-variant "^5.0.0"
    postcss-gap-properties "^3.0.3"
    postcss-image-set-function "^4.0.6"
    postcss-initial "^4.0.1"
    postcss-lab-function "^4.1.1"
    postcss-logical "^5.0.4"
    postcss-media-minmax "^5.0.0"
    postcss-nesting "^10.1.2"
    postcss-opacity-percentage "^1.1.2"
    postcss-overflow-shorthand "^3.0.3"
    postcss-page-break "^3.0.4"
    postcss-place "^7.0.4"
    postcss-pseudo-class-any-link "^7.1.1"
    postcss-replace-overflow-wrap "^4.0.0"
    postcss-selector-not "^5.0.0"

postcss-pseudo-class-any-link@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-7.1.1.tgz"
  integrity sha512-JRoLFvPEX/1YTPxRxp1JO4WxBVXJYrSY7NHeak5LImwJ+VobFMwYDQHvfTXEpcn+7fYIeGkC29zYFhFWIZD8fg==
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-reduce-initial@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-5.0.3.tgz"
  integrity sha512-c88TkSnQ/Dnwgb4OZbKPOBbCaauwEjbECP5uAuFPOzQ+XdjNjRH7SG0dteXrpp1LlIFEKK76iUGgmw2V0xeieA==
  dependencies:
    browserslist "^4.16.6"
    caniuse-api "^3.0.0"

postcss-reduce-transforms@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-5.0.4.tgz"
  integrity sha512-VIJB9SFSaL8B/B7AXb7KHL6/GNNbbCHslgdzS9UDfBZYIA2nx8NLY7iD/BXFSO/1sRUILzBTfHCoW5inP37C5g==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-replace-overflow-wrap@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-4.0.0.tgz"
  integrity sha512-KmF7SBPphT4gPPcKZc7aDkweHiKEEO8cla/GjcBK+ckKxiZslIu3C4GCRW3DNfL0o7yW7kMQu9xlZ1kXRXLXtw==

postcss-selector-not@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/postcss-selector-not/-/postcss-selector-not-5.0.0.tgz"
  integrity sha512-/2K3A4TCP9orP4TNS7u3tGdRFVKqz/E6pX3aGnriPG0jU78of8wsUcqE4QAhWEU0d+WnMSF93Ah3F//vUtK+iQ==
  dependencies:
    balanced-match "^1.0.0"

postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.5, postcss-selector-parser@^6.0.6, postcss-selector-parser@^6.0.8, postcss-selector-parser@^6.0.9:
  version "6.0.9"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.9.tgz"
  integrity sha512-UO3SgnZOVTwu4kyLR22UQ1xZh086RyNZppb7lLAKBFK8a32ttG5i87Y/P3+2bRSjZNyJ1B7hfFNo273tKe9YxQ==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-5.0.4.tgz"
  integrity sha512-yDKHvULbnZtIrRqhZoA+rxreWpee28JSRH/gy9727u0UCgtpv1M/9WEWY3xySlFa0zQJcqf6oCBJPR5NwkmYpg==
  dependencies:
    postcss-value-parser "^4.2.0"
    svgo "^2.7.0"

postcss-unique-selectors@^5.0.4:
  version "5.0.4"
  resolved "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-5.0.4.tgz"
  integrity sha512-5ampwoSDJCxDPoANBIlMgoBcYUHnhaiuLYJR5pj1DLnYQvMRVyFuTA5C3Bvt+aHtiqWpJkD/lXT50Vo1D0ZsAQ==
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^7.0.35:
  version "7.0.39"
  resolved "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

postcss@^8.3.5, postcss@^8.4.4, postcss@^8.4.5, postcss@^8.4.6:
  version "8.4.7"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.7.tgz"
  integrity sha512-L9Ye3r6hkkCeOETQX6iOaWZgjp3LL6Lpqm6EtgbKrgqGGteRMNb9vzBfRL96YOSu8o7x3MfIH9Mo5cPJFGrW6A==
  dependencies:
    nanoid "^3.3.1"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prettier@^2.2.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/prettier/-/prettier-2.5.1.tgz"
  integrity sha512-vBZcPRUR5MZJwoyi3ZoyQlc1rXeEck8KgeC9AwwOn+exuxLxq5toTRDTSaVrXHxelDMHy9zlicw8u66yxoSUFg==

pretty-bytes@^5.3.0, pretty-bytes@^5.4.1:
  version "5.6.0"
  resolved "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz"
  integrity sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg==

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/pretty-error/-/pretty-error-4.0.0.tgz"
  integrity sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw==
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

pretty-format@^27.5.1:
  version "27.5.1"
  resolved "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz"
  integrity sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

promise@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/promise/-/promise-8.1.0.tgz"
  integrity sha512-W04AqnILOL/sPRXziNicCjSNRruLAuIHEOVBazepu0545DDNGYHz7ar9ZgZ1fMU8/MA4mVxp5rkBWRi6OXIy3Q==
  dependencies:
    asap "~2.0.6"

prompts@^2.0.1, prompts@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  integrity sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.6.0, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

psl@^1.1.33:
  version "1.8.0"
  resolved "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz"
  integrity sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ==

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz"
  integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==

q@^1.1.2:
  version "1.5.1"
  resolved "https://registry.npmjs.org/q/-/q-1.5.1.tgz"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@6.9.7:
  version "6.9.7"
  resolved "https://registry.npmjs.org/qs/-/qs-6.9.7.tgz"
  integrity sha512-IhMFgUmuNpyRfxA90umL7ByLlgRXu6tIfKPpF5TmcfRLlLCckfP/g3IQmju6jjpu+Hh8rA+2p6A27ZSPOOHdKw==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz"
  integrity sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==

raf@^3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  integrity sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==
  dependencies:
    performance-now "^2.1.0"

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.4.3:
  version "2.4.3"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.4.3.tgz"
  integrity sha512-UlTNLIcu0uzb4D2f4WltY6cVjLi+/jEN4lgEUj3E04tpMDpUlkBo/eSn6zou9hum2VMNpCCUone0O0WeJim07g==
  dependencies:
    bytes "3.1.2"
    http-errors "1.8.1"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc-align@^4.0.0:
  version "4.0.11"
  resolved "https://registry.npmjs.org/rc-align/-/rc-align-4.0.11.tgz"
  integrity sha512-n9mQfIYQbbNTbefyQnRHZPWuTEwG1rY4a9yKlIWHSTbgwI+XUMGRYd0uJ5pE2UbrNX0WvnMBA1zJ3Lrecpra/A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    dom-align "^1.7.0"
    lodash "^4.17.21"
    rc-util "^5.3.0"
    resize-observer-polyfill "^1.5.1"

rc-cascader@~2.3.0:
  version "2.3.3"
  resolved "https://registry.npmjs.org/rc-cascader/-/rc-cascader-2.3.3.tgz"
  integrity sha512-ckD8rKJjS8mdXxylWh1PtIHSFhbj/yf1NimyooqeJlvtLhzRZXIHCj4IuOjwYZ6J1DqoOCCdJfVtc7UeZia38w==
  dependencies:
    "@babel/runtime" "^7.12.5"
    array-tree-filter "^2.1.0"
    classnames "^2.3.1"
    rc-tree-select "~4.8.0"
    rc-trigger "^5.0.4"
    rc-util "^5.6.1"
    warning "^4.0.1"

rc-checkbox@~2.3.0:
  version "2.3.2"
  resolved "https://registry.npmjs.org/rc-checkbox/-/rc-checkbox-2.3.2.tgz"
  integrity sha512-afVi1FYiGv1U0JlpNH/UaEXdh6WUJjcWokj/nUN2TgG80bfG+MDdbfHKlLcNNba94mbjy2/SXJ1HDgrOkXGAjg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"

rc-collapse@~3.1.0:
  version "3.1.2"
  resolved "https://registry.npmjs.org/rc-collapse/-/rc-collapse-3.1.2.tgz"
  integrity sha512-HujcKq7mghk/gVKeI6EjzTbb8e19XUZpakrYazu1MblEZ3Hu3WBMSN4A3QmvbF6n1g7x6lUlZvsHZ5shABWYOQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.3.4"
    rc-util "^5.2.1"
    shallowequal "^1.1.0"

rc-dialog@~8.6.0:
  version "8.6.0"
  resolved "https://registry.npmjs.org/rc-dialog/-/rc-dialog-8.6.0.tgz"
  integrity sha512-GSbkfqjqxpZC5/zc+8H332+q5l/DKUhpQr0vdX2uDsxo5K0PhvaMEVjyoJUTkZ3+JstEADQji1PVLVb/2bJeOQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-motion "^2.3.0"
    rc-util "^5.6.1"

rc-drawer@~4.4.2:
  version "4.4.3"
  resolved "https://registry.npmjs.org/rc-drawer/-/rc-drawer-4.4.3.tgz"
  integrity sha512-FYztwRs3uXnFOIf1hLvFxIQP9MiZJA+0w+Os8dfDh/90X7z/HqP/Yg+noLCIeHEbKln1Tqelv8ymCAN24zPcfQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.7.0"

rc-dropdown@^3.2.0, rc-dropdown@~3.2.0:
  version "3.2.5"
  resolved "https://registry.npmjs.org/rc-dropdown/-/rc-dropdown-3.2.5.tgz"
  integrity sha512-dVO2eulOSbEf+F4OyhCY5iGiMVhUYY/qeXxL7Ex2jDBt/xc89jU07mNoowV6aWxwVOc70pxEINff0oM2ogjluA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-trigger "^5.0.4"

rc-field-form@~1.21.0:
  version "1.21.2"
  resolved "https://registry.npmjs.org/rc-field-form/-/rc-field-form-1.21.2.tgz"
  integrity sha512-LR/bURt/Tf5g39mb0wtMtQuWn42d/7kEzpzlC5fNC7yaRVmLTtlPP4sBBlaViETM9uZQKLoaB0Pt9Mubhm9gow==
  dependencies:
    "@babel/runtime" "^7.8.4"
    async-validator "^4.0.2"
    rc-util "^5.8.0"

rc-image@~5.2.5:
  version "5.2.5"
  resolved "https://registry.npmjs.org/rc-image/-/rc-image-5.2.5.tgz"
  integrity sha512-qUfZjYIODxO0c8a8P5GeuclYXZjzW4hV/5hyo27XqSFo1DmTCs2HkVeQObkcIk5kNsJtgsj1KoPThVsSc/PXOw==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    rc-dialog "~8.6.0"
    rc-util "^5.0.6"

rc-input-number@~7.3.0:
  version "7.3.4"
  resolved "https://registry.npmjs.org/rc-input-number/-/rc-input-number-7.3.4.tgz"
  integrity sha512-W9uqSzuvJUnz8H8vsVY4kx+yK51SsAxNTwr8SNH4G3XqQNocLVmKIibKFRjocnYX1RDHMND9FFbgj2h7E7nvGA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.9.8"

rc-mentions@~1.6.1:
  version "1.6.2"
  resolved "https://registry.npmjs.org/rc-mentions/-/rc-mentions-1.6.2.tgz"
  integrity sha512-cntfJkNMq8B910rXuvnsnOV88DfmoUidnQnSIeXzWiYiUX4RL5oWUfSZzs+HAXYRU4SL1l8Mwjx95wHETiZ/fQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-menu "^9.0.0"
    rc-textarea "^0.3.0"
    rc-trigger "^5.0.4"
    rc-util "^5.0.1"

rc-menu@^9.0.0, rc-menu@~9.0.12:
  version "9.0.14"
  resolved "https://registry.npmjs.org/rc-menu/-/rc-menu-9.0.14.tgz"
  integrity sha512-CIox5mZeLDAi32SlHrV7UeSjv7tmJJhwRyxQtZCKt351w3q59XlL4WMFOmtT9gwIfP9h0XoxdBZUMe/xzkp78A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.2.0"
    rc-trigger "^5.1.2"
    rc-util "^5.12.0"
    shallowequal "^1.1.0"

rc-motion@^2.0.0, rc-motion@^2.0.1, rc-motion@^2.2.0, rc-motion@^2.3.0, rc-motion@^2.3.4, rc-motion@^2.4.3, rc-motion@^2.4.4:
  version "2.4.5"
  resolved "https://registry.npmjs.org/rc-motion/-/rc-motion-2.4.5.tgz"
  integrity sha512-f3uJHR4gcpeZS/s8/nYFSOrXt2Wu/h9GrEcbJmC0qmKrVNgwL1pTgrT5kW7lgG6PFeoL4yHDmpQoEKkrPtKIzQ==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.18.1"

rc-notification@~4.5.7:
  version "4.5.7"
  resolved "https://registry.npmjs.org/rc-notification/-/rc-notification-4.5.7.tgz"
  integrity sha512-zhTGUjBIItbx96SiRu3KVURcLOydLUHZCPpYEn1zvh+re//Tnq/wSxN4FKgp38n4HOgHSVxcLEeSxBMTeBBDdw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.2.0"
    rc-util "^5.0.1"

rc-overflow@^1.0.0, rc-overflow@^1.2.0:
  version "1.2.3"
  resolved "https://registry.npmjs.org/rc-overflow/-/rc-overflow-1.2.3.tgz"
  integrity sha512-Bz6dXTn/ww8nmu70tUQfRV0wT3BkfXY6j1lB1O38OVkDPz4xwfAcGK+LJ2zewUR5cTXkJ8hAN7YULohG8z4M7Q==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.15.0"

rc-pagination@~3.1.9:
  version "3.1.15"
  resolved "https://registry.npmjs.org/rc-pagination/-/rc-pagination-3.1.15.tgz"
  integrity sha512-4L3fot8g4E+PjWEgoVGX0noFCg+8ZFZmeLH4vsnZpB3O2T2zThtakjNxG+YvSaYtyMVT4B+GLayjKrKbXQpdAg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"

rc-picker@~2.5.17:
  version "2.5.19"
  resolved "https://registry.npmjs.org/rc-picker/-/rc-picker-2.5.19.tgz"
  integrity sha512-u6myoCu/qiQ0vLbNzSzNrzTQhs7mldArCpPHrEI6OUiifs+IPXmbesqSm0zilJjfzrZJLgYeyyOMSznSlh0GKA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    date-fns "2.x"
    dayjs "1.x"
    moment "^2.24.0"
    rc-trigger "^5.0.4"
    rc-util "^5.4.0"
    shallowequal "^1.1.0"

rc-progress@~3.1.0:
  version "3.1.4"
  resolved "https://registry.npmjs.org/rc-progress/-/rc-progress-3.1.4.tgz"
  integrity sha512-XBAif08eunHssGeIdxMXOmRQRULdHaDdIFENQ578CMb4dyewahmmfJRyab+hw4KH4XssEzzYOkAInTLS7JJG+Q==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"

rc-rate@~2.9.0:
  version "2.9.1"
  resolved "https://registry.npmjs.org/rc-rate/-/rc-rate-2.9.1.tgz"
  integrity sha512-MmIU7FT8W4LYRRHJD1sgG366qKtSaKb67D0/vVvJYR0lrCuRrCiVQ5qhfT5ghVO4wuVIORGpZs7ZKaYu+KMUzA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.0.1"

rc-resize-observer@^1.0.0, rc-resize-observer@^1.1.2:
  version "1.2.0"
  resolved "https://registry.npmjs.org/rc-resize-observer/-/rc-resize-observer-1.2.0.tgz"
  integrity sha512-6W+UzT3PyDM0wVCEHfoW3qTHPTvbdSgiA43buiy8PzmeMnfgnDeb9NjdimMXMl3/TcrvvWl5RRVdp+NqcR47pQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-util "^5.15.0"
    resize-observer-polyfill "^1.5.1"

rc-select@~13.2.1:
  version "13.2.1"
  resolved "https://registry.npmjs.org/rc-select/-/rc-select-13.2.1.tgz"
  integrity sha512-L2cJFAjVEeDiNVa/dlOVKE79OUb0J7sUBvWN3Viav3XHcjvv9Ovn4D8J9QhBSlDXeGuczZ81CZI3BbdHD25+Gg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.0.0"
    rc-trigger "^5.0.4"
    rc-util "^5.9.8"
    rc-virtual-list "^3.2.0"

rc-slider@~9.7.4:
  version "9.7.5"
  resolved "https://registry.npmjs.org/rc-slider/-/rc-slider-9.7.5.tgz"
  integrity sha512-LV/MWcXFjco1epPbdw1JlLXlTgmWpB9/Y/P2yinf8Pg3wElHxA9uajN21lJiWtZjf5SCUekfSP6QMJfDo4t1hg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-tooltip "^5.0.1"
    rc-util "^5.16.1"
    shallowequal "^1.1.0"

rc-steps@~4.1.0:
  version "4.1.4"
  resolved "https://registry.npmjs.org/rc-steps/-/rc-steps-4.1.4.tgz"
  integrity sha512-qoCqKZWSpkh/b03ASGx1WhpKnuZcRWmvuW+ZUu4mvMdfvFzVxblTwUM+9aBd0mlEUFmt6GW8FXhMpHkK3Uzp3w==
  dependencies:
    "@babel/runtime" "^7.10.2"
    classnames "^2.2.3"
    rc-util "^5.0.1"

rc-switch@~3.2.0:
  version "3.2.2"
  resolved "https://registry.npmjs.org/rc-switch/-/rc-switch-3.2.2.tgz"
  integrity sha512-+gUJClsZZzvAHGy1vZfnwySxj+MjLlGRyXKXScrtCTcmiYNPzxDFOxdQ/3pK1Kt/0POvwJ/6ALOR8gwdXGhs+A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-util "^5.0.1"

rc-table@~7.19.0:
  version "7.19.2"
  resolved "https://registry.npmjs.org/rc-table/-/rc-table-7.19.2.tgz"
  integrity sha512-NdpnoM50MK02H5/hGOsObfxCvGFUG5cHB9turE5BKJ81T5Ycbq193w5tLhnpILXe//Oanzr47MdMxkUnVGP+qg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-resize-observer "^1.0.0"
    rc-util "^5.14.0"
    shallowequal "^1.1.0"

rc-tabs@~11.10.0:
  version "11.10.5"
  resolved "https://registry.npmjs.org/rc-tabs/-/rc-tabs-11.10.5.tgz"
  integrity sha512-DDuUdV6b9zGRYLtjI5hyejWLKoz1QiLWNgMeBzc3aMeQylZFhTYnFGdDc6HRqj5IYearNTsFPVSA+6VIT8g5cg==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "2.x"
    rc-dropdown "^3.2.0"
    rc-menu "^9.0.0"
    rc-resize-observer "^1.0.0"
    rc-util "^5.5.0"

rc-textarea@^0.3.0, rc-textarea@~0.3.0:
  version "0.3.7"
  resolved "https://registry.npmjs.org/rc-textarea/-/rc-textarea-0.3.7.tgz"
  integrity sha512-yCdZ6binKmAQB13hc/oehh0E/QRwoPP1pjF21aHBxlgXO3RzPF6dUu4LG2R4FZ1zx/fQd2L1faktulrXOM/2rw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.7.0"
    shallowequal "^1.1.0"

rc-tooltip@^5.0.1, rc-tooltip@~5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/rc-tooltip/-/rc-tooltip-5.1.1.tgz"
  integrity sha512-alt8eGMJulio6+4/uDm7nvV+rJq9bsfxFDCI0ljPdbuoygUscbsMYb6EQgwib/uqsXQUvzk+S7A59uYHmEgmDA==
  dependencies:
    "@babel/runtime" "^7.11.2"
    rc-trigger "^5.0.0"

rc-tree-select@~4.8.0:
  version "4.8.0"
  resolved "https://registry.npmjs.org/rc-tree-select/-/rc-tree-select-4.8.0.tgz"
  integrity sha512-evuVIF7GHCGDdvISdBWl4ZYmG/8foof/RDtzCu/WFLA1tFKZD77RRC3khEsjh4WgsB0vllLe7j+ODJ7jHRcDRQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-select "~13.2.1"
    rc-tree "~5.3.0"
    rc-util "^5.7.0"

rc-tree@~5.3.0:
  version "5.3.8"
  resolved "https://registry.npmjs.org/rc-tree/-/rc-tree-5.3.8.tgz"
  integrity sha512-YuobEryPymqPmHFUOvsoOrYdm24psaj0CrGEUuDUQUeG/nNcTGw6FA2YmF4NsEaNBvNSJUSzwfZnFHrKa/xv0A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.4.1"

rc-trigger@^5.0.0, rc-trigger@^5.0.4, rc-trigger@^5.1.2, rc-trigger@^5.2.10:
  version "5.2.10"
  resolved "https://registry.npmjs.org/rc-trigger/-/rc-trigger-5.2.10.tgz"
  integrity sha512-FkUf4H9BOFDaIwu42fvRycXMAvkttph9AlbCZXssZDVzz2L+QZ0ERvfB/4nX3ZFPh1Zd+uVGr1DEDeXxq4J1TA==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    rc-align "^4.0.0"
    rc-motion "^2.0.0"
    rc-util "^5.5.0"

rc-upload@~4.3.0:
  version "4.3.3"
  resolved "https://registry.npmjs.org/rc-upload/-/rc-upload-4.3.3.tgz"
  integrity sha512-YoJ0phCRenMj1nzwalXzciKZ9/FAaCrFu84dS5pphwucTC8GUWClcDID/WWNGsLFcM97NqIboDqrV82rVRhW/w==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.2.0"

rc-util@^5.0.1, rc-util@^5.0.6, rc-util@^5.0.7, rc-util@^5.12.0, rc-util@^5.14.0, rc-util@^5.15.0, rc-util@^5.16.1, rc-util@^5.18.1, rc-util@^5.2.0, rc-util@^5.2.1, rc-util@^5.3.0, rc-util@^5.4.0, rc-util@^5.5.0, rc-util@^5.6.1, rc-util@^5.7.0, rc-util@^5.8.0, rc-util@^5.9.4, rc-util@^5.9.8:
  version "5.18.1"
  resolved "https://registry.npmjs.org/rc-util/-/rc-util-5.18.1.tgz"
  integrity sha512-24xaSrMZUEKh1+suDOtJWfPe9E6YrwryViZcoPO0miJTKzP4qhUlV5AAlKQ82AJilz/AOHfi3l6HoX8qa1ye8w==
  dependencies:
    "@babel/runtime" "^7.12.5"
    react-is "^16.12.0"
    shallowequal "^1.1.0"

rc-virtual-list@^3.2.0, rc-virtual-list@^3.4.1:
  version "3.4.2"
  resolved "https://registry.npmjs.org/rc-virtual-list/-/rc-virtual-list-3.4.2.tgz"
  integrity sha512-OyVrrPvvFcHvV0ssz5EDZ+7Rf5qLat/+mmujjchNw5FfbJWNDwkpQ99EcVE6+FtNRmX9wFa1LGNpZLUTvp/4GQ==
  dependencies:
    classnames "^2.2.6"
    rc-resize-observer "^1.0.0"
    rc-util "^5.0.7"

react-app-polyfill@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/react-app-polyfill/-/react-app-polyfill-3.0.0.tgz"
  integrity sha512-sZ41cxiU5llIB003yxxQBYrARBqe0repqPTTYBTmMqTz9szeBbE37BehCE891NZsmdZqqP+xWKdT3eo3vOzN8w==
  dependencies:
    core-js "^3.19.2"
    object-assign "^4.1.1"
    promise "^8.1.0"
    raf "^3.4.1"
    regenerator-runtime "^0.13.9"
    whatwg-fetch "^3.6.2"

react-app-rewired@^2.1.9:
  version "2.2.1"
  resolved "https://registry.npmjs.org/react-app-rewired/-/react-app-rewired-2.2.1.tgz"
  integrity sha512-uFQWTErXeLDrMzOJHKp0h8P1z0LV9HzPGsJ6adOtGlA/B9WfT6Shh4j2tLTTGlXOfiVx6w6iWpp7SOC5pvk+gA==
  dependencies:
    semver "^5.6.0"

react-codemirror2@^7.2.1:
  version "7.3.0"
  resolved "https://mirrors.tencent.com/npm/react-codemirror2/-/react-codemirror2-7.3.0.tgz"
  integrity sha512-gCgJPXDX+5iaPolkHAu1YbJ92a2yL7Je4TuyO3QEqOtI/d6mbEk08l0oIm18R4ctuT/Sl87X63xIMBnRQBXYXA==

react-dev-utils@^12.0.0:
  version "12.0.0"
  resolved "https://registry.npmjs.org/react-dev-utils/-/react-dev-utils-12.0.0.tgz"
  integrity sha512-xBQkitdxozPxt1YZ9O1097EJiVpwHr9FoAuEVURCKV0Av8NBERovJauzP7bo1ThvuhZ4shsQ1AJiu4vQpoT1AQ==
  dependencies:
    "@babel/code-frame" "^7.16.0"
    address "^1.1.2"
    browserslist "^4.18.1"
    chalk "^4.1.2"
    cross-spawn "^7.0.3"
    detect-port-alt "^1.1.6"
    escape-string-regexp "^4.0.0"
    filesize "^8.0.6"
    find-up "^5.0.0"
    fork-ts-checker-webpack-plugin "^6.5.0"
    global-modules "^2.0.0"
    globby "^11.0.4"
    gzip-size "^6.0.0"
    immer "^9.0.7"
    is-root "^2.1.0"
    loader-utils "^3.2.0"
    open "^8.4.0"
    pkg-up "^3.1.0"
    prompts "^2.4.2"
    react-error-overlay "^6.0.10"
    recursive-readdir "^2.2.2"
    shell-quote "^1.7.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

react-dom@^17.0.0:
  version "17.0.2"
  resolved "https://registry.npmjs.org/react-dom/-/react-dom-17.0.2.tgz"
  integrity sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    scheduler "^0.20.2"

react-draggable@^4.4.4:
  version "4.4.4"
  resolved "https://registry.npmjs.org/react-draggable/-/react-draggable-4.4.4.tgz"
  integrity sha512-6e0WdcNLwpBx/YIDpoyd2Xb04PB0elrDrulKUgdrIlwuYvxh5Ok9M+F8cljm8kPXXs43PmMzek9RrB1b7mLMqA==
  dependencies:
    clsx "^1.1.1"
    prop-types "^15.6.0"

react-error-overlay@^6.0.10:
  version "6.0.10"
  resolved "https://registry.npmjs.org/react-error-overlay/-/react-error-overlay-6.0.10.tgz"
  integrity sha512-mKR90fX7Pm5seCOfz8q9F+66VCc1PGsWSBxKbITjfKVQHMNF2zudxHnMdJiB1fRCb+XsbQV9sO9DCkgsMQgBIA==

react-flow-renderer@^9.4.0:
  version "9.7.4"
  resolved "https://registry.npmjs.org/react-flow-renderer/-/react-flow-renderer-9.7.4.tgz"
  integrity sha512-GxHBXzkn8Y+TEG8pul7h6Fjo4cKrT0kW9UQ34OAGZqAnSBLbBsx9W++TF8GiULBbTn3O8o7HtHxux685Op10mQ==
  dependencies:
    "@babel/runtime" "^7.16.7"
    classcat "^5.0.3"
    d3-selection "^3.0.0"
    d3-zoom "^3.0.0"
    fast-deep-equal "^3.1.3"
    react-draggable "^4.4.4"
    react-redux "^7.2.6"
    redux "^4.1.2"

react-i18next@^13.3.1:
  version "13.3.1"
  resolved "https://mirrors.tencent.com/npm/react-i18next/-/react-i18next-13.3.1.tgz"
  integrity sha512-JAtYREK879JXaN9GdzfBI4yJeo/XyLeXWUsRABvYXiFUakhZJ40l+kaTo+i+A/3cKIED41kS/HAbZ5BzFtq/Og==
  dependencies:
    "@babel/runtime" "^7.22.5"
    html-parse-stringify "^3.0.1"

react-is@^16.12.0, react-is@^16.13.1, react-is@^16.6.0, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^17.0.1, react-is@^17.0.2:
  version "17.0.2"
  resolved "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  integrity sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==

react-monaco-editor@^0.44.0:
  version "0.44.0"
  resolved "https://registry.npmjs.org/react-monaco-editor/-/react-monaco-editor-0.44.0.tgz"
  integrity sha512-GPheXTIpBXpwv857H7/jA8HX5yae4TJ7vFwDJ5iTvy05LxIQTsD3oofXznXGi66lVA93ST/G7wRptEf4CJ9dOg==
  dependencies:
    monaco-editor "^0.26.1"
    prop-types "^15.7.2"

react-redux@^7.2.4, react-redux@^7.2.6:
  version "7.2.6"
  resolved "https://registry.npmjs.org/react-redux/-/react-redux-7.2.6.tgz"
  integrity sha512-10RPdsz0UUrRL1NZE0ejTkucnclYSgXp5q+tB5SWx2qeG2ZJQJyymgAhwKy73yiL/13btfB6fPr+rgbMAaZIAQ==
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    hoist-non-react-statics "^3.3.2"
    loose-envify "^1.4.0"
    prop-types "^15.7.2"
    react-is "^17.0.2"

react-refresh@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npmjs.org/react-refresh/-/react-refresh-0.11.0.tgz"
  integrity sha512-F27qZr8uUqwhWZboondsPx8tnC3Ct3SxZA3V5WyEvujRyyNv0VYPhoBg1gZ8/MV5tubQp76Trw8lTv9hzRBa+A==

react-router-dom@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/react-router-dom/-/react-router-dom-5.3.0.tgz"
  integrity sha512-ObVBLjUZsphUUMVycibxgMdh5jJ1e3o+KpAZBVeHcNQZ4W+uUGGWsokurzlF4YOldQYRQL4y6yFRWM4m3svmuQ==
  dependencies:
    "@babel/runtime" "^7.12.13"
    history "^4.9.0"
    loose-envify "^1.3.1"
    prop-types "^15.6.2"
    react-router "5.2.1"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"

react-router@5.2.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/react-router/-/react-router-5.2.1.tgz"
  integrity sha512-lIboRiOtDLFdg1VTemMwud9vRVuOCZmUIT/7lUoZiSpPODiiH1UQlfXy+vPLC/7IWdFYnhRwAyNqA/+I7wnvKQ==
  dependencies:
    "@babel/runtime" "^7.12.13"
    history "^4.9.0"
    hoist-non-react-statics "^3.1.0"
    loose-envify "^1.3.1"
    mini-create-react-context "^0.4.0"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.2"
    react-is "^16.6.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"

react-scripts@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/react-scripts/-/react-scripts-5.0.0.tgz"
  integrity sha512-3i0L2CyIlROz7mxETEdfif6Sfhh9Lfpzi10CtcGs1emDQStmZfWjJbAIMtRD0opVUjQuFWqHZyRZ9PPzKCFxWg==
  dependencies:
    "@babel/core" "^7.16.0"
    "@pmmmwh/react-refresh-webpack-plugin" "^0.5.3"
    "@svgr/webpack" "^5.5.0"
    babel-jest "^27.4.2"
    babel-loader "^8.2.3"
    babel-plugin-named-asset-import "^0.3.8"
    babel-preset-react-app "^10.0.1"
    bfj "^7.0.2"
    browserslist "^4.18.1"
    camelcase "^6.2.1"
    case-sensitive-paths-webpack-plugin "^2.4.0"
    css-loader "^6.5.1"
    css-minimizer-webpack-plugin "^3.2.0"
    dotenv "^10.0.0"
    dotenv-expand "^5.1.0"
    eslint "^8.3.0"
    eslint-config-react-app "^7.0.0"
    eslint-webpack-plugin "^3.1.1"
    file-loader "^6.2.0"
    fs-extra "^10.0.0"
    html-webpack-plugin "^5.5.0"
    identity-obj-proxy "^3.0.0"
    jest "^27.4.3"
    jest-resolve "^27.4.2"
    jest-watch-typeahead "^1.0.0"
    mini-css-extract-plugin "^2.4.5"
    postcss "^8.4.4"
    postcss-flexbugs-fixes "^5.0.2"
    postcss-loader "^6.2.1"
    postcss-normalize "^10.0.1"
    postcss-preset-env "^7.0.1"
    prompts "^2.4.2"
    react-app-polyfill "^3.0.0"
    react-dev-utils "^12.0.0"
    react-refresh "^0.11.0"
    resolve "^1.20.0"
    resolve-url-loader "^4.0.0"
    sass-loader "^12.3.0"
    semver "^7.3.5"
    source-map-loader "^3.0.0"
    style-loader "^3.3.1"
    tailwindcss "^3.0.2"
    terser-webpack-plugin "^5.2.5"
    webpack "^5.64.4"
    webpack-dev-server "^4.6.0"
    webpack-manifest-plugin "^4.0.2"
    workbox-webpack-plugin "^6.4.1"
  optionalDependencies:
    fsevents "^2.3.2"

react@^17.0.0:
  version "17.0.2"
  resolved "https://registry.npmjs.org/react/-/react-17.0.2.tgz"
  integrity sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

readable-stream@^2.0.1:
  version "2.3.7"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz"
  integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

recursive-readdir@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmjs.org/recursive-readdir/-/recursive-readdir-2.2.2.tgz"
  integrity sha512-nRCcW9Sj7NuZwa2XvH9co8NPeXUBhZP7CRKJtU+cS6PW9FpCIFoI5ib0NT1ZrbNuPoRy0ylyCaUL8Gih4LSyFg==
  dependencies:
    minimatch "3.0.4"

redux-thunk@^2.4.1:
  version "2.4.1"
  resolved "https://registry.npmjs.org/redux-thunk/-/redux-thunk-2.4.1.tgz"
  integrity sha512-OOYGNY5Jy2TWvTL1KgAlVy6dcx3siPJ1wTq741EPyUKfn6W6nChdICjZwCd0p8AZBs5kWpZlbkXW2nE/zjUa+Q==

redux@^4.0.0, redux@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/redux/-/redux-4.1.2.tgz"
  integrity sha512-SH8PglcebESbd/shgf6mii6EIoRM0zrQyjcuQ+ojmfxjTtE0z9Y8pa62iA/OJ58qjP6j27uyW4kUF4jl/jd6sw==
  dependencies:
    "@babel/runtime" "^7.9.2"

regenerate-unicode-properties@^10.0.1:
  version "10.0.1"
  resolved "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.0.1.tgz"
  integrity sha512-vn5DU6yg6h8hP/2OkQo3K7uVILvY4iu0oI4t3HFa81UPkhGJwkRwM10JEc3upjdhHjs/k8GJY1sRBhk5sr69Bw==
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz"
  integrity sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==

regenerator-runtime@^0.13.4, regenerator-runtime@^0.13.9:
  version "0.13.9"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  integrity sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==

regenerator-runtime@^0.14.0:
  version "0.14.0"
  resolved "https://mirrors.tencent.com/npm/regenerator-runtime/-/regenerator-runtime-0.14.0.tgz"
  integrity sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.14.5.tgz"
  integrity sha512-eOf6vka5IO151Jfsw2NO9WpGX58W6wWmefK3I1zEGr0lOD0u8rwPaNqQL1aRxUaxLeKO3ArNh3VYg1KbaD+FFw==
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-parser@^2.2.11:
  version "2.2.11"
  resolved "https://registry.npmjs.org/regex-parser/-/regex-parser-2.2.11.tgz"
  integrity sha512-jbD/FT0+9MBU2XAZluI7w2OBs1RBi6p9M83nkoZayQXXU9e8Robt69FcZc7wU4eJD/YFTjn1JdCk3rbMJajz8Q==

regexp.prototype.flags@^1.2.0, regexp.prototype.flags@^1.3.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.4.1.tgz"
  integrity sha512-pMR7hBVUUGI7PMA37m2ofIdQCsomVnas+Jn5UPGAHQ+/LlwKm/aTLJHdasmHRzlfeZwHiAOaRSo2rbBDm3nNUQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

regexpu-core@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.0.1.tgz"
  integrity sha512-CriEZlrKK9VJw/xQGJpQM5rY88BtuL8DM+AEwvcThHilbxiTAy8vq4iJnd2tqq8wLmjbGZzP7ZcKFjbGkmEFrw==
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.0.1"
    regjsgen "^0.6.0"
    regjsparser "^0.8.2"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.0.0"

regjsgen@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/regjsgen/-/regjsgen-0.6.0.tgz"
  integrity sha512-ozE883Uigtqj3bx7OhL1KNbCzGyW2NQZPl6Hs09WTvCuZD5sTI4JY58bkbQWa/Y9hxIsvJ3M8Nbf7j54IqeZbA==

regjsparser@^0.8.2:
  version "0.8.4"
  resolved "https://registry.npmjs.org/regjsparser/-/regjsparser-0.8.4.tgz"
  integrity sha512-J3LABycON/VNEu3abOviqGHuB/LOtOQj8SKmfP9anY5GfAVw/SPjwzSjxGjbZXIxbGfqTHtJw58C2Li/WkStmA==
  dependencies:
    jsesc "~0.5.0"

relateurl@^0.2.7:
  version "0.2.7"
  resolved "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

renderkid@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/renderkid/-/renderkid-3.0.0.tgz"
  integrity sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg==
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

reselect@^4.1.5:
  version "4.1.5"
  resolved "https://registry.npmjs.org/reselect/-/reselect-4.1.5.tgz"
  integrity sha512-uVdlz8J7OO+ASpBYoz1Zypgx0KasCY20H+N8JD13oUMtPvSHQuscrHop4KbXrbsBcdB9Ds7lVK7eRkBIfO43vQ==

resize-observer-polyfill@^1.5.0, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve-pathname@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/resolve-pathname/-/resolve-pathname-3.0.0.tgz"
  integrity sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng==

resolve-url-loader@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-4.0.0.tgz"
  integrity sha512-05VEMczVREcbtT7Bz+C+96eUO5HDNvdthIiMB34t7FcF8ehcu4wC0sSgPUubs3XW2Q3CNLJk/BJrCU9wVRymiA==
  dependencies:
    adjust-sourcemap-loader "^4.0.0"
    convert-source-map "^1.7.0"
    loader-utils "^2.0.0"
    postcss "^7.0.35"
    source-map "0.6.1"

resolve.exports@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.1.0.tgz"
  integrity sha512-J1l+Zxxp4XK3LUDZ9m60LRJF/mAe4z6a4xyabPHk7pvK5t35dACV32iIjJDFeWZFfZlO29w6SZ67knR0tHzJtQ==

resolve@^1.14.2, resolve@^1.19.0, resolve@^1.20.0, resolve@^1.22.0:
  version "1.22.0"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.0.tgz"
  integrity sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw==
  dependencies:
    is-core-module "^2.8.1"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.3:
  version "2.0.0-next.3"
  resolved "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.3.tgz"
  integrity sha512-W8LucSynKUIDu9ylraa7ueVZ7hc0uAgJBxVsQSKOXOyle8a93qXhcz+XAXZ8bIq2d6i4Ehddn6Evt+0/UwKk6Q==
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

retry@^0.13.1:
  version "0.13.1"
  resolved "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rollup-plugin-terser@^7.0.0:
  version "7.0.2"
  resolved "https://registry.npmjs.org/rollup-plugin-terser/-/rollup-plugin-terser-7.0.2.tgz"
  integrity sha512-w3iIaU4OxcF52UUXiZNsNeuXIMDvFrr+ZXK6bFZ0Q60qyVfq4uLptoS4bbq3paG3x216eQllFZX7zt6TIImguQ==
  dependencies:
    "@babel/code-frame" "^7.10.4"
    jest-worker "^26.2.1"
    serialize-javascript "^4.0.0"
    terser "^5.0.0"

rollup@^2.43.1:
  version "2.68.0"
  resolved "https://registry.npmjs.org/rollup/-/rollup-2.68.0.tgz"
  integrity sha512-XrMKOYK7oQcTio4wyTz466mucnd8LzkiZLozZ4Rz0zQD+HeX4nUK4B8GrTX/2EvN2/vBF/i2WnaXboPxo0JylA==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@5.1.2, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-buffer@5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sanitize.css@*:
  version "13.0.0"
  resolved "https://registry.npmjs.org/sanitize.css/-/sanitize.css-13.0.0.tgz"
  integrity sha512-ZRwKbh/eQ6w9vmTjkuG0Ioi3HBwPFce0O+v//ve+aOq1oeCy7jMV2qzzAlpsNuqpqCBjjriM1lbtZbF/Q8jVyA==

sass-loader@^12.3.0:
  version "12.6.0"
  resolved "https://registry.npmjs.org/sass-loader/-/sass-loader-12.6.0.tgz"
  integrity sha512-oLTaH0YCtX4cfnJZxKSLAyglED0naiYfNG1iXfU5w1LNZ+ukoA5DtyDIN5zmKVZwYNJP4KRc5Y3hkWga+7tYfA==
  dependencies:
    klona "^2.0.4"
    neo-async "^2.6.2"

sax@^1.2.4, sax@~1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz"
  integrity sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==

saxes@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz"
  integrity sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw==
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.20.2.tgz"
  integrity sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

schema-utils@2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.0.tgz"
  integrity sha512-0ilKFI6QQF5nxDZLFn2dMjvc4hjg/Wkg7rHd3jK6/A4a1Hl9VFdQWvgB1UMGoU94pad1P/8N7fMcEnLnSiju8A==
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz"
  integrity sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.0.0, schema-utils@^3.1.0, schema-utils@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-3.1.1.tgz"
  integrity sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-4.0.0.tgz"
  integrity sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.8.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.0.0"

scroll-into-view-if-needed@^2.2.25:
  version "2.2.29"
  resolved "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.29.tgz"
  integrity sha512-hxpAR6AN+Gh53AdAimHM6C8oTN1ppwVZITihix+WqalywBeFcQ6LdQP5ABNl26nX8GTEL7VT+b8lKpdqq65wXg==
  dependencies:
    compute-scroll-into-view "^1.0.17"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/selfsigned/-/selfsigned-2.0.0.tgz"
  integrity sha512-cUdFiCbKoa1mZ6osuJs2uDHrs0k0oprsKveFiiaBKCNq3SYyb5gs2HxhQyDNLCmL51ZZThqi4YNDpCK6GOP1iQ==
  dependencies:
    node-forge "^1.2.0"

semver@7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/semver/-/semver-7.0.0.tgz"
  integrity sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A==

semver@^5.6.0:
  version "5.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz"
  integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^7.3.2, semver@^7.3.5, semver@^7.3.7:
  version "7.5.4"
  resolved "https://mirrors.tencent.com/npm/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

send@0.17.2:
  version "0.17.2"
  resolved "https://registry.npmjs.org/send/-/send-0.17.2.tgz"
  integrity sha512-UJYB6wFSJE3G00nEivR5rgWp8c2xXvJ3OPWPhmuteU0IKj8nKbG3DrjiOmLwpnHGYWAVwA69zmTm++YG0Hmwww==
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "1.8.1"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-4.0.0.tgz"
  integrity sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==
  dependencies:
    randombytes "^2.1.0"

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.0.tgz"
  integrity sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag==
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.2:
  version "1.14.2"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.14.2.tgz"
  integrity sha512-+TMNA9AFxUEGuC0z2mevogSnn9MXKb4fa7ngeRMJaaGv8vTwnIEkKi+QGvPt33HSnf8pRS+WGM0EbMtCJLKMBQ==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.2"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz"
  integrity sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shallowequal@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/shallowequal/-/shallowequal-1.1.0.tgz"
  integrity sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shell-quote@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.3.tgz"
  integrity sha512-Vpfqwm4EnqGdlsBFNmHhxhElJYrdfcxPThu+ryKS5J8L/fhAwLazFZtq+S+TWZ9ANj2piSQLGj6NQg+lKPmxrw==

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

sirv@^1.0.7:
  version "1.0.19"
  resolved "https://registry.npmjs.org/sirv/-/sirv-1.0.19.tgz"
  integrity sha512-JuLThK3TnZG1TAKDwNIqNq6QA2afLOCcm+iE8D1Kj3GA40pSPsxQjjJl0J8X3tsR7T+CP1GavpzLwYkgVLWrZQ==
  dependencies:
    "@polka/url" "^1.0.0-next.20"
    mrmime "^1.0.0"
    totalist "^1.0.0"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

slash@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-4.0.0.tgz"
  integrity sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew==

sockjs@^0.3.21:
  version "0.3.24"
  resolved "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz"
  integrity sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

source-list-map@^2.0.0, source-list-map@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz"
  integrity sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==

source-map-js@^1.0.1, source-map-js@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz"
  integrity sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==

source-map-loader@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/source-map-loader/-/source-map-loader-3.0.1.tgz"
  integrity sha512-Vp1UsfyPvgujKQzi4pyDiTOnE3E4H+yHvkVRN3c/9PJmQS4CQJExvcDvaX/D+RV+xQben9HJ56jMJS3CgUeWyA==
  dependencies:
    abab "^2.0.5"
    iconv-lite "^0.6.3"
    source-map-js "^1.0.1"

source-map-support@^0.5.6, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@0.6.1, source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.5.0:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.7.3, source-map@~0.7.2:
  version "0.7.3"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz"
  integrity sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ==

source-map@^0.8.0-beta.0:
  version "0.8.0-beta.0"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.8.0-beta.0.tgz"
  integrity sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA==
  dependencies:
    whatwg-url "^7.0.0"

source-map@~0.1.34:
  version "0.1.43"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz"
  integrity sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=
  dependencies:
    amdefine ">=0.0.4"

sourcemap-codec@^1.4.4:
  version "1.4.8"
  resolved "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  integrity sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz"
  integrity sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz"
  integrity sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stable@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz"
  integrity sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==

stack-utils@^2.0.3:
  version "2.0.5"
  resolved "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.5.tgz"
  integrity sha512-xrQcmYhOsn/1kX+Vraq+7j4oE2j/6BFscZ0etmYg81xuM8Gq0022Pxb8+IqgOFUIaxHs0KaSb7T1+OegiNrNFA==
  dependencies:
    escape-string-regexp "^2.0.0"

stackframe@^1.1.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/stackframe/-/stackframe-1.2.1.tgz"
  integrity sha512-h88QkzREN/hy8eRdyNhhsO7RSJ5oyTqxxmmn0dzBIMUclZsjpfmrsg81vp8mjjAs2vAZ72nyWxRUwSwmh0e4xg==

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

string-convert@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmjs.org/string-convert/-/string-convert-0.2.1.tgz"
  integrity sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz"
  integrity sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-length@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/string-length/-/string-length-5.0.1.tgz"
  integrity sha512-9Ep08KAMUn0OadnVaBuRdE2l615CQ508kr0XMadjClfYpdCyvrbFp6Taebo8yyxokQ4viUd/xPPUA4FGgUa0ow==
  dependencies:
    char-regex "^2.0.0"
    strip-ansi "^7.0.1"

string-natural-compare@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/string-natural-compare/-/string-natural-compare-3.0.1.tgz"
  integrity sha512-n3sPwynL1nwKi3WJ6AIsClwBMa0zTi54fn2oLU6ndfTSIO05xaznjSf15PcBZU6FNWbmN5Q6cxT4V5hGvB4taw==

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.matchall@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.6.tgz"
  integrity sha512-6WgDX8HmQqvEd7J+G6VtAahhsQIssiZ8zl7zKh1VDMFyL3hRTJP4FTNA3RbIp2TOQ9AYNDcc7e3fH0Qbup+DBg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    has-symbols "^1.0.2"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.3.1"
    side-channel "^1.0.4"

string.prototype.trimend@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.4.tgz"
  integrity sha512-y9xCjw1P23Awk8EvTpcyL2NIr1j7wJ39f+k6lvRnSMz+mz9CGz9NYPelDk42kOz6+ql8xjfK8oYzy3jAP5QU5A==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

string.prototype.trimstart@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.4.tgz"
  integrity sha512-jh6e984OBfvxS50tdY2nRZnoC5/mLFKOREQfw8t5yytkoUsJRNxvI/E39qu1sD0OtWI3OC0XgKSmcWwziwYuZw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz"
  integrity sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw==
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.0, strip-ansi@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.0.1.tgz"
  integrity sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw==
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz"
  integrity sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==

strip-comments@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/strip-comments/-/strip-comments-2.0.1.tgz"
  integrity sha512-ZprKx+bBLXv067WTCALv8SSz5l2+XhpYCsVtSqlMnkAXMWDq+/ekVbl1ghqP9rUHTzv6sm/DwCOiYutU/yp1fw==

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

style-loader@^3.3.1:
  version "3.3.1"
  resolved "https://registry.npmjs.org/style-loader/-/style-loader-3.3.1.tgz"
  integrity sha512-GPcQ+LDJbrcxHORTRes6Jy2sfvK2kS6hpSfI/fXhPt+spVzxF6LJ1dHLN9zIGmVaaP044YKaIatFaufENRiDoQ==

stylehacks@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/stylehacks/-/stylehacks-5.0.3.tgz"
  integrity sha512-ENcUdpf4yO0E1rubu8rkxI+JGQk4CgjchynZ4bDBJDfqdy+uhTRSWb8/F3Jtu+Bw5MW45Po3/aQGeIyyxgQtxg==
  dependencies:
    browserslist "^4.16.6"
    postcss-selector-parser "^6.0.4"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.2.0.tgz"
  integrity sha512-6sXEzV5+I5j8Bmq9/vUphGRM/RJNT9SCURJLjwfOg51heRtguGWDzcaBlgAzKhQa0EVNpPEKzQuBwZ8S8WaCeQ==
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-parser@^2.0.2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz"
  integrity sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ==

svgo@^1.2.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz"
  integrity sha512-yhy/sQYxR5BkC98CY7o31VGsg014AKLEPxdfhora76l36hD9Rdy5NZA/Ocn6yayNPgSamYdtX2rFJdcv07AYVw==
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

svgo@^2.7.0:
  version "2.8.0"
  resolved "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz"
  integrity sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^4.1.3"
    css-tree "^1.1.3"
    csso "^4.2.0"
    picocolors "^1.0.0"
    stable "^0.1.8"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
  integrity sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==

tailwindcss@^3.0.2:
  version "3.0.23"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.0.23.tgz"
  integrity sha512-+OZOV9ubyQ6oI2BXEhzw4HrqvgcARY38xv3zKcjnWtMIZstEsXdI9xftd1iB7+RbOnj2HOEzkA0OyB5BaSxPQA==
  dependencies:
    arg "^5.0.1"
    chalk "^4.1.2"
    chokidar "^3.5.3"
    color-name "^1.1.4"
    cosmiconfig "^7.0.1"
    detective "^5.2.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.2.11"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    normalize-path "^3.0.0"
    object-hash "^2.2.0"
    postcss "^8.4.6"
    postcss-js "^4.0.0"
    postcss-load-config "^3.1.0"
    postcss-nested "5.0.6"
    postcss-selector-parser "^6.0.9"
    postcss-value-parser "^4.2.0"
    quick-lru "^5.1.1"
    resolve "^1.22.0"

tapable@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz"
  integrity sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

temp-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/temp-dir/-/temp-dir-2.0.0.tgz"
  integrity sha512-aoBAniQmmwtcKp/7BzsH8Cxzv8OL736p7v1ihGb5e9DJ9kTwGWHrQrVB5+lfVDzfGrdRzXch+ig7LHaY1JTOrg==

tempy@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/tempy/-/tempy-0.6.0.tgz"
  integrity sha512-G13vtMYPT/J8A4X2SjdtBTphZlrp1gKv6hZiOjw14RCWg6GbHuQBGtjlx75xLbYV/wEc0D7G5K4rxKP/cXk8Bw==
  dependencies:
    is-stream "^2.0.0"
    temp-dir "^2.0.0"
    type-fest "^0.16.0"
    unique-string "^2.0.0"

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz"
  integrity sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

terser-webpack-plugin@^5.1.3, terser-webpack-plugin@^5.2.5:
  version "5.3.1"
  resolved "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.1.tgz"
  integrity sha512-GvlZdT6wPQKbDNW/GDQzZFg/j4vKU96yl2q6mcUkzKOgW4gwf1Z8cZToUCrz31XHlPWH8MVb1r2tFtdDtTGJ7g==
  dependencies:
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"
    terser "^5.7.2"

terser@^5.0.0, terser@^5.10.0, terser@^5.7.2:
  version "5.11.0"
  resolved "https://registry.npmjs.org/terser/-/terser-5.11.0.tgz"
  integrity sha512-uCA9DLanzzWSsN1UirKwylhhRz3aKPInlfmpGfw8VN6jHsAtu8HJtIpeeHHK23rxnE/cDc+yvmq5wqkIC6Kn0A==
  dependencies:
    acorn "^8.5.0"
    commander "^2.20.0"
    source-map "~0.7.2"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  integrity sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throat@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/throat/-/throat-6.0.1.tgz"
  integrity sha512-8hmiGIJMDlwjg7dlJ4yKGLK8EsYqKgPWbG3b4wjJddKNwc7N7Dpn08Df4szr/sZdMVeOstrdYSsqzX6BYbcB+w==

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz"
  integrity sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==

timsort@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tiny-invariant@^1.0.2:
  version "1.2.0"
  resolved "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.2.0.tgz"
  integrity sha512-1Uhn/aqw5C6RI4KejVeTg6mIS7IqxnLJ8Mv2tV5rTc0qWobay7pDUz6Wi392Cnc8ak1H0F2cjoRzb2/AW4+Fvg==

tiny-warning@^1.0.0, tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  integrity sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz"
  integrity sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

totalist@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/totalist/-/totalist-1.1.0.tgz"
  integrity sha512-gduQwd1rOdDMGxFG1gEvhV88Oirdo2p+KjoYFU7k2g+i7n6AFFbDQ5kMPUsW0pNbfQsB/cwXvT1i4Bue0s9g5g==

tough-cookie@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.0.0.tgz"
  integrity sha512-tHdtEpQCMrc1YLrMaqXXcj6AxhYi/xgit6mZu1+EDWUn+qhUf8wMQoFIy9NXuq23zAwtcB0t/MjACGR18pcRbg==
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.1.2"

tr46@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

tr46@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/tr46/-/tr46-2.1.0.tgz"
  integrity sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw==
  dependencies:
    punycode "^2.1.1"

tryer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz"
  integrity sha512-c3zayb8/kWWpycWYg87P71E1S1ZL6b6IJxfb5fvsUgsf0S2MVGaDhDXXjDMpdCpfWXqptc+4mXwmiy1ypXqRAA==

tsconfig-paths@^3.12.0:
  version "3.12.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.12.0.tgz"
  integrity sha512-e5adrnOYT6zqVnWqZu7i/BQ3BnhzvGbjEjejFXO20lKIKpwTaupkCPgEfv4GZK1IBciJUEhYs3J3p75FdaTFVg==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.3.1.tgz"
  integrity sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw==

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
  integrity sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  integrity sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==

type-fest@^0.16.0:
  version "0.16.0"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.16.0.tgz"
  integrity sha512-eaBzG6MxNzEn9kiwvtre90cXaNLkmadMWa1zQMs3XORCXNbsH/OewwbxC5ia9dCxIxnTAsSxXJaa/p5y8DlvJg==

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/type/-/type-1.2.0.tgz"
  integrity sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg==

type@^2.5.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/type/-/type-2.6.0.tgz"
  integrity sha512-eiDBDOmkih5pMbo9OqsqPRGMljLodLcwd5XD5JbtNB0o89xZAwynY9EdCDsJU7LtcVCClu9DvM7/0Ep1hYX3EQ==

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz"
  integrity sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==
  dependencies:
    is-typedarray "^1.0.0"

typescript@^4.5.4:
  version "4.5.5"
  resolved "https://registry.npmjs.org/typescript/-/typescript-4.5.5.tgz"
  integrity sha512-TCTIul70LyWe6IJWT8QSYeA54WQe8EjQFU4wY52Fasj5UKx88LNYKCgBEHcOMOrFF1rKGbD8v/xcNWVUq9SymA==

unbox-primitive@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.1.tgz"
  integrity sha512-tZU/3NqK3dA5gpE1KtyiJUrEB0lxnGkMFHptJ7q6ewdZ8s12QrODwNbhIJStmJkd1QDXa1NRA8aF2A1zk/Ypyw==
  dependencies:
    function-bind "^1.1.1"
    has-bigints "^1.0.1"
    has-symbols "^1.0.2"
    which-boxed-primitive "^1.0.2"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  integrity sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz"
  integrity sha512-7Yhkc0Ye+t4PNYzOGKedDhXbYIBe1XEQYQxOPyhcXNMJ0WCABqqj6ckydd6pWRZTHV4GuCPKdBAUiMc60tsKVw==

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz"
  integrity sha512-5Zfuy9q/DFr4tfO7ZPeVXb1aPoeQSdeFMLpYuFebehDAhbuevLs5yxSZmIFN1tP5F9Wl4IpJrYojg85/zgyZHQ==

unidragger@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/unidragger/-/unidragger-2.4.0.tgz"
  integrity sha512-MueZK2oXuGE6OAlGKIrSXK2zCq+8yb1QUZgqyTDCSJzvwYL0g2Llrad+TtoQTYxtFnNyxxSw0IMnKNIgEMia1w==
  dependencies:
    unipointer "^2.4.0"

unipointer@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npmjs.org/unipointer/-/unipointer-2.4.0.tgz"
  integrity sha512-VjzDLPjGK7aYpQKH7bnDZS8X4axF5AFU/LQi+NQe1oyEHfaz6lWKhaQ7n4o7vJ1iJ4i2T0quCIfrQM139p05Sw==
  dependencies:
    ev-emitter "^1.0.1"

unique-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz"
  integrity sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^0.1.0, universalify@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz"
  integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/universalify/-/universalify-2.0.0.tgz"
  integrity sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

upath@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz"
  integrity sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg==

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.1.tgz"
  integrity sha512-g9JpC/3He3bm38zsLupWryXHoEcS22YHthuPQSJdMy6KNrzIRzWqcsHzD/WUnqe45whVou4VIsPew37DoXWNrA==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

v8-to-istanbul@^8.1.0:
  version "8.1.1"
  resolved "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.1.1.tgz"
  integrity sha512-FGtKtv3xIpR6BYhvgH8MI/y78oT7d8Au3ww4QIxymrCtZEh5b8gCw2siywE+puhEmuWKDtmfrvF5UlB298ut3w==
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

value-equal@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/value-equal/-/value-equal-1.0.1.tgz"
  integrity sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw==

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

void-elements@3.1.0:
  version "3.1.0"
  resolved "https://mirrors.tencent.com/npm/void-elements/-/void-elements-3.1.0.tgz"
  integrity sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=

w3c-hr-time@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz"
  integrity sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz"
  integrity sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA==
  dependencies:
    xml-name-validator "^3.0.0"

walker@^1.0.7:
  version "1.0.8"
  resolved "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz"
  integrity sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==
  dependencies:
    makeerror "1.0.12"

warning@^4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

watchpack@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/watchpack/-/watchpack-2.3.1.tgz"
  integrity sha512-x0t0JuydIo8qCNctdDrn1OzH/qDzk2+rdCOC3YzumZ42fiMqmQ7T3xQurykYMhYfHaPHTp4ZxAx2NfUo1K6QaA==
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz"
  integrity sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==
  dependencies:
    minimalistic-assert "^1.0.0"

web-vitals@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/web-vitals/-/web-vitals-1.1.2.tgz"
  integrity sha512-PFMKIY+bRSXlMxVAQ+m2aw9c/ioUYfDgrYot0YUa+/xa0sakubWhSDyxAKwzymvXVdF4CZI71g06W+mqhzu6ig==

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz"
  integrity sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==

webidl-conversions@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz"
  integrity sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA==

webidl-conversions@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.1.0.tgz"
  integrity sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w==

webpack-bundle-analyzer@^4.4.2:
  version "4.5.0"
  resolved "https://registry.npmjs.org/webpack-bundle-analyzer/-/webpack-bundle-analyzer-4.5.0.tgz"
  integrity sha512-GUMZlM3SKwS8Z+CKeIFx7CVoHn3dXFcUAjT/dcZQQmfSZGvitPfMob2ipjai7ovFFqPvTqkEZ/leL4O0YOdAYQ==
  dependencies:
    acorn "^8.0.4"
    acorn-walk "^8.0.0"
    chalk "^4.1.0"
    commander "^7.2.0"
    gzip-size "^6.0.0"
    lodash "^4.17.20"
    opener "^1.5.2"
    sirv "^1.0.7"
    ws "^7.3.1"

webpack-dev-middleware@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-5.3.1.tgz"
  integrity sha512-81EujCKkyles2wphtdrnPg/QqegC/AtqNH//mQkBYSMqwFVCQrxM6ktB2O/SPlZy7LqeEfTbV3cZARGQz6umhg==
  dependencies:
    colorette "^2.0.10"
    memfs "^3.4.1"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.6.0:
  version "4.7.4"
  resolved "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-4.7.4.tgz"
  integrity sha512-nfdsb02Zi2qzkNmgtZjkrMOcXnYZ6FLKcQwpxT7MvmHKc+oTtDsBju8j+NMyAygZ9GW1jMEUpy3itHtqgEhe1A==
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.2.2"
    ansi-html-community "^0.0.8"
    bonjour "^3.5.0"
    chokidar "^3.5.3"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    default-gateway "^6.0.3"
    del "^6.0.0"
    express "^4.17.1"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.0"
    ipaddr.js "^2.0.1"
    open "^8.0.9"
    p-retry "^4.5.0"
    portfinder "^1.0.28"
    schema-utils "^4.0.0"
    selfsigned "^2.0.0"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    spdy "^4.0.2"
    strip-ansi "^7.0.0"
    webpack-dev-middleware "^5.3.1"
    ws "^8.4.2"

webpack-manifest-plugin@^4.0.2:
  version "4.1.1"
  resolved "https://registry.npmjs.org/webpack-manifest-plugin/-/webpack-manifest-plugin-4.1.1.tgz"
  integrity sha512-YXUAwxtfKIJIKkhg03MKuiFAD72PlrqCiwdwO4VEXdRO5V0ORCNwaOwAZawPZalCbmH9kBDmXnNeQOw+BIEiow==
  dependencies:
    tapable "^2.0.0"
    webpack-sources "^2.2.0"

webpack-sources@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz"
  integrity sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack-sources@^2.2.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.3.1.tgz"
  integrity sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA==
  dependencies:
    source-list-map "^2.0.1"
    source-map "^0.6.1"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack@^5.64.4:
  version "5.69.1"
  resolved "https://registry.npmjs.org/webpack/-/webpack-5.69.1.tgz"
  integrity sha512-+VyvOSJXZMT2V5vLzOnDuMz5GxEqLk7hKWQ56YxPW/PQRUuKimPqmEIJOx8jHYeyo65pKbapbW464mvsKbaj4A==
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^0.0.51"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    acorn "^8.4.1"
    acorn-import-assertions "^1.7.6"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.8.3"
    es-module-lexer "^0.9.0"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.9"
    json-parse-better-errors "^1.0.2"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.1.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.1.3"
    watchpack "^2.3.1"
    webpack-sources "^3.2.3"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz"
  integrity sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  integrity sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==

whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz"
  integrity sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==
  dependencies:
    iconv-lite "0.4.24"

whatwg-fetch@^3.6.2:
  version "3.6.2"
  resolved "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz"
  integrity sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA==

whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz"
  integrity sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz"
  integrity sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

whatwg-url@^8.0.0, whatwg-url@^8.5.0:
  version "8.7.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz"
  integrity sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg==
  dependencies:
    lodash "^4.7.0"
    tr46 "^2.1.0"
    webidl-conversions "^6.1.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz"
  integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==

workbox-background-sync@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-background-sync/-/workbox-background-sync-6.5.0.tgz"
  integrity sha512-rrekt/gt6qOIZsisj6QZfmAFPAnocq1Z603zAjt+qHmeXY8DLPOklVtvrXSaHoHH3qIjUq3SQY5s2x240iTIKw==
  dependencies:
    idb "^6.1.4"
    workbox-core "6.5.0"

workbox-broadcast-update@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-broadcast-update/-/workbox-broadcast-update-6.5.0.tgz"
  integrity sha512-JC97c7tYqoGWcCfbKO9KHG6lkU+WhXCnDB2j1oFWEiv53nUHy3yjPpzMmAGNLD9oV5lInO15n6V18HfwgkhISw==
  dependencies:
    workbox-core "6.5.0"

workbox-build@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-build/-/workbox-build-6.5.0.tgz"
  integrity sha512-da0/1b6//P9+ts7ofcIKcMVPyN6suJvjJASXokF7DsqvUmgRBPcCVV4KCy8QWjgfcz7mzuTpkSbdVHcPFJ/p0A==
  dependencies:
    "@apideck/better-ajv-errors" "^0.3.1"
    "@babel/core" "^7.11.1"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.2"
    "@rollup/plugin-babel" "^5.2.0"
    "@rollup/plugin-node-resolve" "^11.2.1"
    "@rollup/plugin-replace" "^2.4.1"
    "@surma/rollup-plugin-off-main-thread" "^2.2.3"
    ajv "^8.6.0"
    common-tags "^1.8.0"
    fast-json-stable-stringify "^2.1.0"
    fs-extra "^9.0.1"
    glob "^7.1.6"
    lodash "^4.17.20"
    pretty-bytes "^5.3.0"
    rollup "^2.43.1"
    rollup-plugin-terser "^7.0.0"
    source-map "^0.8.0-beta.0"
    stringify-object "^3.3.0"
    strip-comments "^2.0.1"
    tempy "^0.6.0"
    upath "^1.2.0"
    workbox-background-sync "6.5.0"
    workbox-broadcast-update "6.5.0"
    workbox-cacheable-response "6.5.0"
    workbox-core "6.5.0"
    workbox-expiration "6.5.0"
    workbox-google-analytics "6.5.0"
    workbox-navigation-preload "6.5.0"
    workbox-precaching "6.5.0"
    workbox-range-requests "6.5.0"
    workbox-recipes "6.5.0"
    workbox-routing "6.5.0"
    workbox-strategies "6.5.0"
    workbox-streams "6.5.0"
    workbox-sw "6.5.0"
    workbox-window "6.5.0"

workbox-cacheable-response@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-cacheable-response/-/workbox-cacheable-response-6.5.0.tgz"
  integrity sha512-sqAtWAiBwWvI8HG/2Do7BeKPhHuUczt22ORkAjkH9DfTq9LuWRFd6T4HAMqX5G8F1gM9XA2UPlxRrEeSpFIz/A==
  dependencies:
    workbox-core "6.5.0"

workbox-core@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-core/-/workbox-core-6.5.0.tgz"
  integrity sha512-5SPwNipUzYBhrneLVT02JFA0fw3LG82jFAN/G2NzxkIW10t4MVZuML2nU94bbkgjq25u0fkY8+4JXzMfHgxEWQ==

workbox-expiration@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-expiration/-/workbox-expiration-6.5.0.tgz"
  integrity sha512-y3WRkKRy/gMuZZNkrLFahjY0QZtLoq+QfhTbVAsOGHVg1CCtnNbeFAnEidQs7UisI2BK76VqQPvM7hEOFyZ92A==
  dependencies:
    idb "^6.1.4"
    workbox-core "6.5.0"

workbox-google-analytics@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-google-analytics/-/workbox-google-analytics-6.5.0.tgz"
  integrity sha512-CHHh55wMNCc/BV1URrzEM2Zjgf6g2CV6QpAAc1pBRqaLY5755PeQZbp3o8KbJEM7YsC9mIBeQVsOkSKkGS30bg==
  dependencies:
    workbox-background-sync "6.5.0"
    workbox-core "6.5.0"
    workbox-routing "6.5.0"
    workbox-strategies "6.5.0"

workbox-navigation-preload@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-navigation-preload/-/workbox-navigation-preload-6.5.0.tgz"
  integrity sha512-ktrRQzXJ0zFy0puOtCa49wE3BSBGUB8KRMot3tEieikCkSO0wMLmiCb9GwTVvNMJLl0THRlsdFoI93si04nTxA==
  dependencies:
    workbox-core "6.5.0"

workbox-precaching@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-precaching/-/workbox-precaching-6.5.0.tgz"
  integrity sha512-IVLzgHx38T6LphJyEOltd7XAvpDi73p85uCT2ZtT1HHg9FAYC49a+5iHUVOnqye73fLW20eiAMFcnehGxz9RWg==
  dependencies:
    workbox-core "6.5.0"
    workbox-routing "6.5.0"
    workbox-strategies "6.5.0"

workbox-range-requests@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-range-requests/-/workbox-range-requests-6.5.0.tgz"
  integrity sha512-+qTELdGZE5rOjuv+ifFrfRDN8Uvzpbm5Fal7qSUqB1V1DLCMxPwHCj6mWwQBRKBpW7G09kAwewH7zA3Asjkf/Q==
  dependencies:
    workbox-core "6.5.0"

workbox-recipes@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-recipes/-/workbox-recipes-6.5.0.tgz"
  integrity sha512-7hWZAIcXmvr31NwYSWaQIrnThCH/Dx9+eYv/YdkpUeWIXRiHRkYvP1FdiHItbLSjL4Y6K7cy2Y9y5lGCkgaE4w==
  dependencies:
    workbox-cacheable-response "6.5.0"
    workbox-core "6.5.0"
    workbox-expiration "6.5.0"
    workbox-precaching "6.5.0"
    workbox-routing "6.5.0"
    workbox-strategies "6.5.0"

workbox-routing@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-routing/-/workbox-routing-6.5.0.tgz"
  integrity sha512-w1A9OVa/yYStu9ds0Dj+TC6zOAoskKlczf+wZI5mrM9nFCt/KOMQiFp1/41DMFPrrN/8KlZTS3Cel/Ttutw93Q==
  dependencies:
    workbox-core "6.5.0"

workbox-strategies@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-strategies/-/workbox-strategies-6.5.0.tgz"
  integrity sha512-Ngnwo+tfGw4uKSlTz3h1fYKb/lCV7SDI/dtTb8VaJzRl0N9XssloDGYERBmF6BN/DV/x3bnRsshfobnKI/3z0g==
  dependencies:
    workbox-core "6.5.0"

workbox-streams@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-streams/-/workbox-streams-6.5.0.tgz"
  integrity sha512-ZbeaZINkju4x45P9DFyRbOYInE+dyNAJIelflz4f9AOAdm+zZUJCooU4MdfsedVhHiTIA6pCD/3jCmW1XbvlbA==
  dependencies:
    workbox-core "6.5.0"
    workbox-routing "6.5.0"

workbox-sw@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-sw/-/workbox-sw-6.5.0.tgz"
  integrity sha512-uPGJ9Yost4yabnCko/IuhouquoQKrWOEqLq7L/xVYtltWe4+J8Hw8iPCVtxvXQ26hffd7MaFWUAN83j2ZWbxRg==

workbox-webpack-plugin@^6.4.1:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-webpack-plugin/-/workbox-webpack-plugin-6.5.0.tgz"
  integrity sha512-wy4uCBJELNfJVf2b4Tg3mjJQySq/aReWv4Q1RxQweJkY9ihq7DOGA3wLlXvoauek+MX/SuQfS3it+eXIfHKjvg==
  dependencies:
    fast-json-stable-stringify "^2.1.0"
    pretty-bytes "^5.4.1"
    upath "^1.2.0"
    webpack-sources "^1.4.3"
    workbox-build "6.5.0"

workbox-window@6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/workbox-window/-/workbox-window-6.5.0.tgz"
  integrity sha512-DOrhiTnWup/CsNstO2uvfdKM4kdStgHd31xGGvBcoCE3Are3DRcy5s3zz3PedcAR1AKskQj3BXz0UhzQiOq8nA==
  dependencies:
    "@types/trusted-types" "^2.0.2"
    workbox-core "6.5.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz"
  integrity sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@^7.3.1, ws@^7.4.6:
  version "7.5.7"
  resolved "https://registry.npmjs.org/ws/-/ws-7.5.7.tgz"
  integrity sha512-KMvVuFzpKBuiIXW3E4u3mySRO2/mCHSyZDJQM5NQ9Q9KHWHWh0NHgfbRMLLrceUK5qAL4ytALJbpRMjixFZh8A==

ws@^8.4.2:
  version "8.5.0"
  resolved "https://registry.npmjs.org/ws/-/ws-8.5.0.tgz"
  integrity sha512-BWX0SWVgLPzYwF8lTzEy1egjhS4S4OEAHfsO8o65WOVsrnSRGaSiUaa9e0ggGlkMTtBlmOpEXiie9RUcBO86qg==

xgplayer-subtitles@^1.0.19:
  version "1.0.22"
  resolved "https://registry.npmjs.org/xgplayer-subtitles/-/xgplayer-subtitles-1.0.22.tgz"
  integrity sha512-+m1fo+FFynL+2ymO4UQBUSnJAz3WuIthpJFCrKBjd+ukcLRoy3vTcsylXEOo5AZN1xT28fpHmxvZQKEIEYA+6w==
  dependencies:
    eventemitter3 "^4.0.7"
    generate-source-map "0.0.5"

xgplayer@^2.28.0:
  version "2.31.4"
  resolved "https://registry.npmjs.org/xgplayer/-/xgplayer-2.31.4.tgz"
  integrity sha512-apuAucT8RMsC8t0lattoBI9g8o9Pf7ZP7G5Eh5T7CpYAC7WA8zWZiWSunidc3rAs9HZmROhRtd1RhPnIyXGJjg==
  dependencies:
    chalk "^2.3.2"
    commander "^2.15.1"
    danmu.js "^0.5.9"
    downloadjs "1.4.7"
    draggabilly "^2.2.0"
    event-emitter "^0.3.5"
    fs-extra "^5.0.0"
    xgplayer-subtitles "^1.0.19"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz"
  integrity sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
  integrity sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==

xtend@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@^1.10.0, yaml@^1.10.2, yaml@^1.7.2:
  version "1.10.2"
  resolved "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz"
  integrity sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==

yargs@^16.2.0:
  version "16.2.0"
  resolved "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz"
  integrity sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
