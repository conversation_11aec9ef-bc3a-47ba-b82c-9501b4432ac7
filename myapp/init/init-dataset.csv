name,version,label,describe,source_type,source,industry,field,usage,research,storage_class,file_type,status,years,url,path,download_url,storage_size,entries_num,duration,price,icon,owner,features
ml,2024,机器学习示例,机器学习：决策树pipeline示例数据集,自产,Cube-studio,风控,机器学习,传统机器学习和深度学习入门,决策树,未压缩,csv,正常,2024,,/mnt/admin/pipeline/example/ml/train.csv,,190K,4119,,0,/static/assets/images/pipeline/tree.jpg,"admin,*",
mnist,1998,手写数字数据集,"深度学习：包含一组60,000个示例的训练集和一组10,000个示例的测试集。数字已经过尺寸标准化，以适合 20x20 像素框，同时保持其纵横比，并在固定尺寸的图像中居中",开源,github,图像处理,视觉,传统机器学习和深度学习入门,svm、分类,压缩,gz,正常,2024,,,"https://docker-76009.sz.gfp.tencent-cloud.com/kubeflow/pytorch/example/data/train-images-idx3-ubyte.gz
https://docker-76009.sz.gfp.tencent-cloud.com/kubeflow/pytorch/example/data/train-labels-idx1-ubyte.gz
https://docker-76009.sz.gfp.tencent-cloud.com/kubeflow/pytorch/example/data/t10k-images-idx3-ubyte.gz
https://docker-76009.sz.gfp.tencent-cloud.com/kubeflow/pytorch/example/data/t10k-labels-idx1-ubyte.gz",11M,"60,000 个示例的训练集和 10,000 个示例的测试集",,0,/static/assets/images/dataset/mnist.png,"admin,*"
coco,2014,coco2014版本数据集,机器视觉：包含100张图片和图片中的目标对象类别，box边框,自产,Cube-studio,图像处理,视觉,传统机器学习和深度学习入门,目标识别,未压缩,jpg,正常,2014,,/mnt/admin/pipeline/example/dataset/vision/coco,,25M,102,,0,/static/assets/images/pipeline/yolo.jpg,"admin,*","{
    ""id"": {
        ""_type"": ""Value"",
        ""dtype"": ""string""
    },
    ""image"": {
        ""_type"": ""Image""
    },
    ""class_name"": {
        ""_type"": ""Value"",
        ""dtype"": ""string""
    },
    ""class_index"": {
        ""_type"": ""Value"",
        ""dtype"": ""int""
    },
    ""x"": {
        ""_type"": ""Value"",
        ""dtype"": ""float""
    },
    ""y"": {
        ""_type"": ""Value"",
        ""dtype"": ""float""
    },
    ""width"": {
        ""_type"": ""Value"",
        ""dtype"": ""float""
    },
    ""height"": {
        ""_type"": ""Value"",
        ""dtype"": ""float""
    }
}"
asr,2024,语音识别数据集,语音识别：包含语音和对应的语音文字内容,自产,Cube-studio,语音识别,语音,传统机器学习和深度学习入门,语音识别,未压缩,mp3,正常,2024,,/mnt/admin/pipeline/example/dataset/audio/asr,,,20,,0,"<svg t=""1719137019161"" class=""icon"" viewBox=""0 0 1024 1024"" version=""1.1"" xmlns=""http://www.w3.org/2000/svg"" p-id=""2324"" width=""50px"" height=""50px""><path d=""M217.28 218.16m78.86 0l448.4 0q78.86 0 78.86 78.86l0 448.4q0 78.86-78.86 78.86l-448.4 0q-78.86 0-78.86-78.86l0-448.4q0-78.86 78.86-78.86Z"" fill=""#EFEFEF"" p-id=""2325""></path><path d=""M431.82 283.82m81.66 0l-0.02 0q81.66 0 81.66 81.66l0 150.16q0 81.66-81.66 81.66l0.02 0q-81.66 0-81.66-81.66l0-150.16q0-81.66 81.66-81.66Z"" fill=""#99D1FF"" p-id=""2326""></path><path d=""M513.46 613.3a97.76 97.76 0 0 1-97.66-97.66v-150.16a97.66 97.66 0 0 1 195.3 0v150.16a97.76 97.76 0 0 1-97.64 97.66z m0-313.48a65.72 65.72 0 0 0-65.66 65.66v150.16a65.66 65.66 0 1 0 131.3 0v-150.16a65.72 65.72 0 0 0-65.64-65.66z"" fill=""#18659E"" p-id=""2327""></path><path d=""M513.46 671.5a143.28 143.28 0 0 1-143.12-143.12 16 16 0 0 1 32 0 111.12 111.12 0 0 0 222.22 0 16 16 0 0 1 32 0 143.28 143.28 0 0 1-143.1 143.12z"" fill=""#18659E"" p-id=""2328""></path><path d=""M513.46 741.52a16 16 0 0 1-16-16v-70a16 16 0 0 1 32 0v70a16 16 0 0 1-16 16z"" fill=""#18659E"" p-id=""2329""></path><path d=""M583.88 748.68h-140.84a16 16 0 0 1 0-32h140.84a16 16 0 0 1 0 32zM313.02 568.14a16 16 0 0 1-16-16v-172.86a16 16 0 0 1 32 0v172.84a16 16 0 0 1-16 16.02zM237.46 512.7a16 16 0 0 1-16-16v-63.4a16 16 0 0 1 32 0v63.4a16 16 0 0 1-16 16zM717.08 567.42a16 16 0 0 1-16-16v-172.84a16 16 0 0 1 32 0v172.84a16 16 0 0 1-16 16zM792.64 512a16 16 0 0 1-16-16v-63.4a16 16 0 0 1 32 0V496a16 16 0 0 1-16 16zM583.88 449.3h-144.94a16 16 0 0 1 0-32h144.92a16 16 0 0 1 0 32zM125.04 357.16a16 16 0 0 1-16-16v-222a16 16 0 0 1 16-16h245.82a16 16 0 0 1 0 32H141.04v206a16 16 0 0 1-16 16zM370.86 915.1H125.04a16 16 0 0 1-16-16V632a16 16 0 0 1 32 0v251.1h229.82a16 16 0 0 1 0 32zM905.04 915.1H661.62a16 16 0 0 1 0-32h227.42V632a16 16 0 0 1 32 0v267.1a16 16 0 0 1-16 16zM905.04 357.16a16 16 0 0 1-16-16v-206H661.62a16 16 0 1 1 0-32h243.42a16 16 0 0 1 16 16v222a16 16 0 0 1-16 16z"" fill=""#18659E"" p-id=""2330""></path></svg>","admin,*","{
    ""audio"": {
        ""_type"": ""Audio""
    },
    ""text"": {
        ""_type"": ""Value"",
        ""dtype"": ""string""
    }
}"
identity,2024,大模型训练问答对,大模型：用于训练大模型身份认证的问答对,自产,Cube-studio,自然语言,自然语言,传统机器学习和深度学习入门,大模型,未压缩,csv,正常,2024,,/mnt/admin/pipeline/example/dataset/gpt/identity,,,91,,0,"<svg t=""1723257485221"" class=""icon"" viewBox=""0 0 1024 1024"" version=""1.1"" xmlns=""http://www.w3.org/2000/svg"" p-id=""3487"" width=""45px"" height=""45px""><path d=""M969.485 337.562a248.27 248.27 0 0 1-10.956 73.472c-1.792 5.632-1.28 9.42 2.816 14.08a251.904 251.904 0 0 1 59.187 126.976c2.457 13.721 3.635 27.648 3.584 41.625A259.123 259.123 0 0 1 976.5 740.147a255.846 255.846 0 0 1-98.1 83.763 240.538 240.538 0 0 1-56.32 19.764 10.496 10.496 0 0 0-8.703 7.68c-28.058 75.315-79.872 127.488-155.648 156.518a248.115 248.115 0 0 1-77.108 15.77c-67.584 3.276-127.692-16.026-180.224-58.01a315.238 315.238 0 0 1-24.32-22.63 11.162 11.162 0 0 0-11.264-3.584c-61.952 10.905-121.036 2.457-175.872-28.365C117.415 870.86 74.356 809.83 58.484 730.522a239.974 239.974 0 0 1 6.707-117.556l1.126-3.584a7.168 7.168 0 0 0-1.74-8.09A255.334 255.334 0 0 1 0.73 448.615a249.19 249.19 0 0 1 28.672-134.912 255.283 255.283 0 0 1 118.016-114.585 242.79 242.79 0 0 1 54.477-18.842c4.25-0.563 7.68-3.584 8.755-7.68a250.368 250.368 0 0 1 68.557-103.577 266.138 266.138 0 0 1 302.336-38.605c24.371 12.697 46.285 29.337 64.922 49.305 4.352 4.66 8.448 5.632 14.643 4.608a261.018 261.018 0 0 1 148.685 16.282 253.931 253.931 0 0 1 88.422 61.03 257.229 257.229 0 0 1 71.27 175.924zM581.901 111.104c-1.69-1.638-2.816-2.867-4.096-3.891a194.1 194.1 0 0 0-140.492-39.936 187.392 187.392 0 0 0-107.828 46.592c-43.366 37.939-66.201 85.811-66.713 143.053-0.666 78.08-0.103 156.16-0.256 234.29-0.461 4.455 2.048 8.705 6.144 10.548 26.112 14.643 52.019 29.542 78.029 44.34 1.536 0.92 3.276 1.535 5.734 2.662V262.86c0-13.978 5.888-24.218 18.125-31.232L575.04 115.2l6.86-4.147zM195.188 251.75c-2.253 0.666-3.79 1.024-5.376 1.639a181.197 181.197 0 0 0-59.904 37.53c-47.104 44.8-68.25 99.379-60.519 163.532 7.68 62.874 39.885 111.104 95.079 143.309 68.147 39.68 137.01 78.234 205.465 117.402a9.677 9.677 0 0 0 11.11-0.103c25.754-14.848 51.56-29.491 77.415-44.185 1.895-1.024 3.533-2.202 5.735-3.687-1.895-1.331-3.277-2.355-4.71-3.174L281.715 562.637l-69.632-39.68a29.542 29.542 0 0 1-14.49-17.152 57.651 57.651 0 0 1-2.355-16.18 56377.81 56377.81 0 0 1 0-227.942v-9.933z m703.744 119.296a175.104 175.104 0 0 0-0.359-67.84c-13.824-65.894-51.558-113.97-114.278-140.697-60.877-25.959-120.832-21.043-178.432 11.776a114064.282 114064.282 0 0 1-201.728 114.893 8.14 8.14 0 0 0-4.864 7.987c0.205 30.515 0.102 60.928 0.102 91.443 0 1.434 0.256 2.816 0.512 5.12l11.11-6.298L589.89 285.542l60.723-34.61a36.25 36.25 0 0 1 21.504-5.12 40.346 40.346 0 0 1 16.793 5.99c35.072 20.07 70.144 40.09 105.268 60.057l100.3 57.14a41.318 41.318 0 0 0 4.455 2.047zM124.276 651.93c-0.82 10.854-1.792 20.07-2.048 29.337a177.203 177.203 0 0 0 25.088 98.304c41.216 68.199 102.912 100.25 182.937 97.28 31.335-1.075 60.416-11.366 87.501-27.033 29.389-16.999 58.983-33.741 88.525-50.535a40451.27 40451.27 0 0 1 113.152-64.358 8.96 8.96 0 0 0 5.12-9.063c-0.154-29.644-0.051-59.29-0.102-88.883 0-2.048-0.205-3.993-0.359-6.656-2.406 1.229-4.096 1.997-5.734 2.919a20989.282 20989.282 0 0 0-79.616 45.414l-113.664 64.768-52.941 30.157a32.82 32.82 0 0 1-34.202 0l-5.068-2.816L146.804 664.73c-7.015-4.096-14.03-7.988-22.528-12.8z m435.046-295.373c2.048 1.331 3.02 2.15 4.096 2.713 52.736 30.157 105.472 60.212 158.157 90.215l90.214 51.405c7.63 4.096 13.21 11.161 15.36 19.456a45.142 45.142 0 0 1 1.639 12.544c0.102 77.619 0.102 155.29 0 232.96v6.553c3.635-1.331 6.656-2.355 9.574-3.584 22.835-9.472 43.52-23.296 60.826-40.755 40.345-40.755 60.21-89.6 56.525-146.586-4.455-67.788-36.864-119.5-95.488-154.982-19.354-11.725-39.27-22.63-58.983-33.74L653.735 308.53a9.216 9.216 0 0 0-10.547 0.154c-17.562 10.24-35.277 20.173-52.89 30.157l-30.976 17.715z m112.23 118.528V759.91c0 14.183-5.324 24.986-17.92 32.052l-103.116 58.726-103.578 59.085c-1.536 0.87-2.97 2.048-4.966 3.43 2.355 1.895 4.096 3.482 5.939 4.864 56.832 40.807 118.784 50.842 185.088 27.7A190.822 190.822 0 0 0 761.1 768.46c0.41-78.95 0-157.901 0.154-236.8a9.114 9.114 0 0 0-5.222-9.165c-26.47-14.848-52.736-29.952-79.156-45.005-1.177-0.717-2.56-1.126-5.324-2.406z m-272.179 101.12l112.64 64.153 112.026-63.846a12.8 12.8 0 0 0 0.41-1.894c0-41.012 0-82.074 0.153-123.136 0-3.38-1.69-4.762-4.3-6.247a41552.384 41552.384 0 0 1-98.612-56.115l-9.728-5.376-3.942 2.15-104.243 59.188a7.936 7.936 0 0 0-4.506 8.09c0.154 28.978 0 57.958 0 86.988l0.051 36.045z"" p-id=""3488""></path></svg>","admin,*",