---
apiVersion: ceph.rook.io/v1
kind: CephClient
metadata:
  name: glance
  namespace: rook-ceph # namespace:cluster
spec:
  caps:
    mon: 'profile rbd'
    osd: 'profile rbd pool=images'
---
apiVersion: ceph.rook.io/v1
kind: CephClient
metadata:
  name: cinder
  namespace: rook-ceph # namespace:cluster
spec:
  caps:
    mon: 'profile rbd'
    osd: 'profile rbd pool=volumes, profile rbd pool=vms, profile rbd-read-only pool=images'
