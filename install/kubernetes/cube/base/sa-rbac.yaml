# encoding: utf-8
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kubeflow-dashboard
  namespace: infra
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: kubeflow-dashboard
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["create", "delete", "deletecollection", "patch", "update", "get", "list", "watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: kubeflow-dashboard
subjects:
- kind: ServiceAccount
  name: kubeflow-dashboard
  namespace: infra
roleRef:
  kind: ClusterRole
  name: kubeflow-dashboard
  apiGroup: rbac.authorization.k8s.io



