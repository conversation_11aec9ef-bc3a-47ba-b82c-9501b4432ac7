#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
算法保存算子 - Algorithm Saver
用于将训练完成的模型保存回算法仓并生成新的算法版本

功能特性:
1. 从ClearML任务中获取最佳模型
2. 生成新的算法版本号
3. 保存模型到算法仓
4. 支持超参数优化结果保存
5. 支持模型指标记录
"""

import requests
import argparse
import os
import sys
import json
import datetime
import uuid
import logging
from typing import Dict, Optional, Tuple, Any

# ClearML SDK 可选导入
try:
    from clearml import Task
    CLEARML_SDK_AVAILABLE = True
except ImportError:
    CLEARML_SDK_AVAILABLE = False
    Task = None

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AlgorithmSaver:
    """算法保存器类"""
    
    def __init__(self):
        # 获取环境变量
        self.project_name = os.environ.get("KFJ_TASK_PROJECT_NAME", "public")
        self.creator = os.environ.get("KFJ_CREATOR", "admin")
        self.run_id = os.environ.get("KFJ_RUN_ID", f"run_{uuid.uuid4().hex[:8]}")
        self.pipeline_id = os.environ.get("KFJ_PIPELINE_ID", "0")
        self.task_name = os.environ.get("KFJ_TASK_NAME", "algorithm_saver")
        
        # API配置
        self.host = os.environ.get("HOST", 
                                  os.environ.get("KFJ_MODEL_REPO_API_URL", 
                                               "http://kubeflow-dashboard.infra")).strip('/')
        self.clearml_api_host = os.environ.get("CLEARML_API_HOST", "")
        
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': self.creator
        }
        
        logger.info(f"算法保存器初始化完成 - 项目: {self.project_name}, 创建者: {self.creator}")

    def generate_algorithm_version(self, base_version: str, suffix: str = "trained") -> str:
        """
        生成新的算法版本号
        
        Args:
            base_version: 基础版本号
            suffix: 版本后缀
            
        Returns:
            str: 新的版本号
        """
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        new_version = f"{base_version}_{suffix}_{timestamp}"
        logger.info(f"生成新算法版本: {base_version} -> {new_version}")
        return new_version

    def get_project_info(self) -> Optional[Dict[str, Any]]:
        """
        获取项目组信息
        
        Returns:
            Dict: 项目信息，失败返回None
        """
        try:
            url = self.host + "/project_modelview/api/?form_data=" + json.dumps({
                "filters": [
                    {
                        "col": "name",
                        "opr": "eq",
                        "value": self.project_name
                    }
                ]
            })
            
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            projects = response.json().get('result', {}).get('data', [])
            if not projects:
                logger.error(f"项目组 {self.project_name} 不存在")
                return None
                
            project_info = projects[0]
            logger.info(f"获取项目信息成功: {project_info['name']} (ID: {project_info['id']})")
            return project_info
            
        except Exception as e:
            logger.error(f"获取项目组信息失败: {str(e)}")
            return None

    def get_clearml_task_info(self, task_id: str) -> Tuple[Optional[str], Optional[Dict]]:
        """
        从ClearML任务中获取模型信息

        Args:
            task_id: ClearML任务ID

        Returns:
            Tuple[str, Dict]: (模型路径, 指标信息)
        """
        # 优先使用 ClearML SDK
        if CLEARML_SDK_AVAILABLE:
            return self._get_task_info_via_sdk(task_id)
        else:
            return self._get_task_info_via_api(task_id)

    def _get_task_info_via_sdk(self, task_id: str) -> Tuple[Optional[str], Optional[Dict]]:
        """
        使用 ClearML SDK 获取任务信息

        Args:
            task_id: ClearML任务ID

        Returns:
            Tuple[str, Dict]: (模型路径, 指标信息)
        """
        try:
            # 通过任务ID获取任务对象
            task = Task.get_task(task_id=task_id)

            if not task:
                logger.error(f"未找到任务: {task_id}")
                return None, None

            logger.info(f"通过SDK获取任务: {task.name} (ID: {task_id})")

            # 获取模型路径
            model_path = None
            artifacts = task.artifacts

            # 查找模型文件
            for artifact_name, artifact_obj in artifacts.items():
                if ('model' in artifact_name.lower() or
                    artifact_name.lower().endswith(('.pt', '.pth', '.pkl', '.h5', '.pb', '.onnx'))):
                    model_path = artifact_obj.url
                    logger.info(f"找到模型文件: {artifact_name} -> {model_path}")
                    break

            # 如果没有找到artifacts中的模型，尝试从参数中获取
            if not model_path:
                params = task.get_parameters()
                for section_name, section_params in params.items():
                    for param_name, param_value in section_params.items():
                        if 'model_path' in param_name.lower() or 'save_path' in param_name.lower():
                            model_path = param_value
                            logger.info(f"从参数中找到模型路径: {param_name} -> {model_path}")
                            break
                    if model_path:
                        break

            # 获取指标信息
            metrics = {}
            try:
                # 获取标量指标
                scalars = task.get_last_scalar_metrics()
                for metric_title, metric_series in scalars.items():
                    for series_name, series_data in metric_series.items():
                        key = f"{metric_title}_{series_name}" if series_name != metric_title else metric_title
                        metrics[key] = series_data['value']

                logger.info(f"获取到 {len(metrics)} 个指标")

            except Exception as e:
                logger.warning(f"获取指标信息失败: {str(e)}")

            return model_path, metrics

        except Exception as e:
            logger.error(f"通过SDK获取ClearML任务信息失败: {str(e)}")
            # 降级到API方式
            return self._get_task_info_via_api(task_id)

    def _get_task_info_via_api(self, task_id: str) -> Tuple[Optional[str], Optional[Dict]]:
        """
        使用 HTTP API 获取任务信息（降级方案）

        Args:
            task_id: ClearML任务ID

        Returns:
            Tuple[str, Dict]: (模型路径, 指标信息)
        """
        if not self.clearml_api_host:
            logger.warning("未配置CLEARML_API_HOST，跳过ClearML任务信息获取")
            return None, None

        try:
            url = f"{self.clearml_api_host}/tasks.get_by_id"
            payload = {"task": task_id}

            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()

            task_data = response.json().get('data', {}).get('task', {})

            # 提取模型路径
            model_path = self._extract_model_path(task_data)

            # 提取指标信息
            metrics = self._extract_metrics(task_data)

            logger.info(f"通过API获取ClearML任务信息成功 - 模型路径: {model_path}, 指标数量: {len(metrics)}")
            return model_path, metrics

        except Exception as e:
            logger.error(f"通过API获取ClearML任务信息失败: {str(e)}")
            return None, None

    def _extract_model_path(self, task_data: Dict) -> Optional[str]:
        """从任务数据中提取模型路径"""
        try:
            # 从artifacts中查找模型文件
            artifacts = task_data.get('execution', {}).get('artifacts', {})
            for artifact_name, artifact_info in artifacts.items():
                if ('model' in artifact_name.lower() or 
                    artifact_name.lower().endswith(('.pt', '.pth', '.pkl', '.h5', '.pb', '.onnx'))):
                    return artifact_info.get('uri', '')
            
            # 从hyperparams中查找模型路径
            hyperparams = task_data.get('hyperparams', {})
            for section_name, section_data in hyperparams.items():
                for param_name, param_info in section_data.items():
                    if 'model_path' in param_name.lower() or 'save_path' in param_name.lower():
                        return param_info.get('value', '')
            
            return None
            
        except Exception as e:
            logger.error(f"提取模型路径失败: {str(e)}")
            return None

    def _extract_metrics(self, task_data: Dict) -> Dict[str, float]:
        """从任务数据中提取指标信息"""
        try:
            metrics = {}
            last_metrics = task_data.get('last_metrics', {})
            
            for metric_name, metric_data in last_metrics.items():
                if isinstance(metric_data, dict) and 'last_values' in metric_data:
                    for series_name, series_data in metric_data['last_values'].items():
                        key = f"{metric_name}_{series_name}" if series_name != metric_name else metric_name
                        metrics[key] = series_data.get('value', 0.0)
            
            return metrics
            
        except Exception as e:
            logger.error(f"提取指标信息失败: {str(e)}")
            return {}

    def get_best_clearml_task(self, project_name: str, metric_name: str) -> Optional[str]:
        """
        获取ClearML项目中的最佳任务ID

        Args:
            project_name: ClearML项目名称
            metric_name: 评估指标名称

        Returns:
            str: 最佳任务ID，失败返回None
        """
        # 优先使用 ClearML SDK
        if CLEARML_SDK_AVAILABLE:
            return self._get_best_task_via_sdk(project_name, metric_name)
        else:
            return self._get_best_task_via_api(project_name, metric_name)

    def _get_best_task_via_sdk(self, project_name: str, metric_name: str) -> Optional[str]:
        """
        使用 ClearML SDK 获取最佳任务

        Args:
            project_name: ClearML项目名称
            metric_name: 评估指标名称

        Returns:
            str: 最佳任务ID，失败返回None
        """
        try:
            # 获取项目中的所有任务
            tasks = Task.get_tasks(project_name=project_name, task_filter={'status': ['completed']})

            if not tasks:
                logger.warning(f"项目 {project_name} 中没有已完成的任务")
                return None

            logger.info(f"找到 {len(tasks)} 个已完成的任务")

            best_task = None
            best_value = float('-inf')

            # 遍历任务找到最佳指标
            for task in tasks:
                try:
                    scalars = task.get_last_scalar_metrics()

                    # 查找指定的指标
                    metric_value = None
                    for metric_title, metric_series in scalars.items():
                        if metric_name.lower() in metric_title.lower():
                            for series_name, series_data in metric_series.items():
                                if metric_name.lower() in series_name.lower() or series_name == metric_title:
                                    metric_value = series_data['value']
                                    break
                            if metric_value is not None:
                                break

                    if metric_value is not None and metric_value > best_value:
                        best_value = metric_value
                        best_task = task
                        logger.info(f"找到更好的任务: {task.name} ({task.id}) - {metric_name}: {metric_value}")

                except Exception as e:
                    logger.warning(f"处理任务 {task.id} 时出错: {str(e)}")
                    continue

            if best_task:
                logger.info(f"最佳任务: {best_task.name} (ID: {best_task.id}) - {metric_name}: {best_value}")
                return best_task.id
            else:
                logger.warning(f"未找到包含指标 {metric_name} 的任务")
                return None

        except Exception as e:
            logger.error(f"通过SDK获取最佳任务失败: {str(e)}")
            # 降级到API方式
            return self._get_best_task_via_api(project_name, metric_name)

    def _get_best_task_via_api(self, project_name: str, metric_name: str) -> Optional[str]:
        """
        使用 HTTP API 获取最佳任务（降级方案）

        Args:
            project_name: ClearML项目名称
            metric_name: 评估指标名称

        Returns:
            str: 最佳任务ID，失败返回None
        """
        if not self.clearml_api_host:
            logger.warning("未配置CLEARML_API_HOST，跳过最佳任务获取")
            return None

        try:
            # 获取项目ID
            project_url = f"{self.clearml_api_host}/projects.get_all"
            project_payload = {"name": project_name}

            response = requests.post(project_url, json=project_payload, timeout=30)
            response.raise_for_status()

            projects = response.json().get("data", {}).get("projects", [])
            if not projects:
                logger.error(f"ClearML项目 {project_name} 不存在")
                return None

            project_id = projects[0].get("id")

            # 获取最佳任务
            best_task_url = f"{self.clearml_api_host}/projects.get_best_task"
            best_task_payload = {"project": project_id, "metric_name": metric_name}

            response = requests.post(best_task_url, json=best_task_payload, timeout=30)
            response.raise_for_status()

            best_task = response.json().get('data', {}).get('task', {})
            if best_task:
                task_id = best_task.get('id')
                logger.info(f"通过API获取最佳任务成功: {task_id}")
                return task_id
            else:
                logger.warning("未找到最佳任务")
                return None

        except Exception as e:
            logger.error(f"通过API获取最佳ClearML任务失败: {str(e)}")
            return None

    def save_algorithm(self, 
                      algo_id: str,
                      algo_version: str,
                      model_path: str,
                      model_metrics: Dict[str, float],
                      framework: str = "pytorch",
                      inference_framework: str = "custom",
                      description: str = "",
                      clearml_task_id: str = None) -> Optional[str]:
        """
        保存算法到算法仓
        
        Args:
            algo_id: 原始算法ID
            algo_version: 原始算法版本
            model_path: 模型文件路径
            model_metrics: 模型评估指标
            framework: 训练框架
            inference_framework: 推理框架
            description: 模型描述
            clearml_task_id: ClearML任务ID
            
        Returns:
            str: 新生成的算法版本号，失败返回None
        """
        try:
            # 获取项目信息
            project_info = self.get_project_info()
            if not project_info:
                return None
            
            # 生成新版本号
            new_version = self.generate_algorithm_version(algo_version)
            
            # 构建模型名称
            model_name = f"algo_{algo_id}_v{new_version}"
            
            # 构建保存载荷
            payload = {
                'name': model_name,
                'version': new_version,
                'path': model_path,
                'describe': description or f"基于算法 {algo_id} v{algo_version} 训练生成的模型",
                'project': project_info['id'],
                'framework': framework,
                'run_id': self.run_id,
                'run_time': datetime.datetime.now().strftime('%Y.%m.%d %H:%M:%S'),
                'metrics': json.dumps(model_metrics) if model_metrics else "{}",
                'md5': '',
                'api_type': inference_framework,
                'pipeline_id': self.pipeline_id,
                'expand': json.dumps({
                    'source_algo_id': algo_id,
                    'source_algo_version': algo_version,
                    'clearml_task_id': clearml_task_id,
                    'saved_by': 'algorithm_saver',
                    'save_time': datetime.datetime.now().isoformat()
                })
            }
            
            # 检查是否已存在
            existing_model = self._check_existing_model(model_name)
            
            if existing_model:
                # 更新现有模型
                return self._update_model(existing_model['id'], payload, new_version)
            else:
                # 创建新模型
                return self._create_model(payload, new_version)
                
        except Exception as e:
            logger.error(f"保存算法失败: {str(e)}")
            return None

    def _check_existing_model(self, model_name: str) -> Optional[Dict[str, Any]]:
        """检查是否存在同名模型"""
        try:
            url = self.host + "/training_model_modelview/api/?form_data=" + json.dumps({
                "filters": [
                    {
                        "col": "name",
                        "opr": "eq",
                        "value": model_name
                    },
                    {
                        "col": "run_id",
                        "opr": "eq",
                        "value": self.run_id
                    }
                ]
            })

            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()

            models = response.json().get('result', {}).get('data', [])
            return models[0] if models else None

        except Exception as e:
            logger.error(f"检查现有模型失败: {str(e)}")
            return None

    def _create_model(self, payload: Dict[str, Any], version: str) -> Optional[str]:
        """创建新模型"""
        try:
            url = self.host + "/training_model_modelview/api/"
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            response.raise_for_status()

            logger.info(f"算法保存成功: {payload['name']} v{version}")
            return version

        except Exception as e:
            logger.error(f"创建模型失败: {str(e)}")
            return None

    def _update_model(self, model_id: str, payload: Dict[str, Any], version: str) -> Optional[str]:
        """更新现有模型"""
        try:
            url = self.host + f"/training_model_modelview/api/{model_id}"
            response = requests.put(url, headers=self.headers, json=payload, timeout=30)
            response.raise_for_status()

            logger.info(f"算法更新成功: {payload['name']} v{version}")
            return version

        except Exception as e:
            logger.error(f"更新模型失败: {str(e)}")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="算法保存算子 - 将训练后的模型保存回算法仓")

    # 必需参数
    parser.add_argument('--algo_id', type=str, required=True, help="原始算法ID")
    parser.add_argument('--algo_version', type=str, required=True, help="原始算法版本")
    parser.add_argument('--model_path', type=str, required=True, help="训练后的模型文件路径")

    # 可选参数
    parser.add_argument('--framework', type=str, default='pytorch', help="训练框架 (默认: pytorch)")
    parser.add_argument('--inference_framework', type=str, default='custom', help="推理框架 (默认: custom)")
    parser.add_argument('--description', type=str, default='', help="模型描述")
    parser.add_argument('--model_metrics', type=str, default='{}', help="模型评估指标 (JSON格式)")

    # ClearML相关参数
    parser.add_argument('--clearml_project_name', type=str, default='', help="ClearML项目名称")
    parser.add_argument('--clearml_task_id', type=str, default='', help="ClearML任务ID")
    parser.add_argument('--clearml_metric_name', type=str, default='accuracy', help="ClearML评估指标名称")
    parser.add_argument('--use_best_task', action='store_true', help="是否使用ClearML最佳任务")

    args = parser.parse_args()

    try:
        # 解析模型指标
        model_metrics = json.loads(args.model_metrics) if args.model_metrics else {}

        # 初始化算法保存器
        saver = AlgorithmSaver()

        # 处理ClearML相关逻辑
        clearml_task_id = args.clearml_task_id
        model_path = args.model_path

        if args.use_best_task and args.clearml_project_name:
            # 获取最佳任务
            best_task_id = saver.get_best_clearml_task(args.clearml_project_name, args.clearml_metric_name)
            if best_task_id:
                clearml_task_id = best_task_id
                logger.info(f"使用最佳任务: {best_task_id}")

        if clearml_task_id:
            # 从ClearML任务获取模型信息
            clearml_model_path, clearml_metrics = saver.get_clearml_task_info(clearml_task_id)

            # 优先使用ClearML中的模型路径和指标
            if clearml_model_path:
                model_path = clearml_model_path
                logger.info(f"使用ClearML模型路径: {model_path}")

            if clearml_metrics:
                model_metrics.update(clearml_metrics)
                logger.info(f"合并ClearML指标: {len(clearml_metrics)} 个指标")

        # 保存算法
        new_version = saver.save_algorithm(
            algo_id=args.algo_id,
            algo_version=args.algo_version,
            model_path=model_path,
            model_metrics=model_metrics,
            framework=args.framework,
            inference_framework=args.inference_framework,
            description=args.description,
            clearml_task_id=clearml_task_id
        )

        if new_version:
            logger.info(f"算法保存完成! 新版本: {new_version}")

            # 输出结果供后续任务使用
            result = {
                'success': True,
                'original_algo_id': args.algo_id,
                'original_algo_version': args.algo_version,
                'new_algo_version': new_version,
                'model_path': model_path,
                'metrics': model_metrics,
                'clearml_task_id': clearml_task_id
            }

            # 保存结果到文件
            output_file = '/tmp/algorithm_save_result.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            logger.info(f"保存结果已写入: {output_file}")
            print(f"SUCCESS: 算法已保存，新版本: {new_version}")

        else:
            logger.error("算法保存失败!")
            print("ERROR: 算法保存失败")
            sys.exit(1)

    except Exception as e:
        logger.error(f"执行失败: {str(e)}")
        print(f"ERROR: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
