apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: prometheus-chproxy   # 这里必须是k8s-app  不然prometheus采集不了该资源
  name: prometheus-chproxy
  namespace: monitoring
spec:
  endpoints:    # endpoints
  - interval: 30s
    # honorLabels: true
    port: rpc    # 端口号只能是字符串, 在pod中定义的端口name
  jobLabel: k8s-app
  namespaceSelector:
    matchNames:
    - clickhouse
  selector:
    matchLabels:
      app: clickhouse-proxy
