server {
    listen       80;
    server_name  _;
    access_log  /data/log/nginx/access.log  main;
    error_log   /data/log/nginx/error.log;

    location = / {
        rewrite ^(.*) $scheme://$http_host/frontend/ permanent;
    }

    # 静态资源
    location /frontend/ {
        root   /data/web;
        index  index.html index.htm;
        try_files $uri $uri/ /frontend/index.html;
        if ($request_filename ~* .*\.html$) {
          add_header Cache-Control "no-cache, no-store";
        }
    }

    # 静态资源
    location /static/appbuilder/ {
        root   /data/web;
        index  index.html index.htm;
        try_files $uri $uri/ /frontend/index.html;
        if ($request_filename ~* .*\.html$) {
          add_header Cache-Control "no-cache, no-store";
        }
    }

    location / {
        # 线上生产使用
        # proxy_pass http://kubeflow-dashboard.infra/;
        # 本地调试使用
        proxy_pass http://myapp/;

        proxy_http_version 1.1;
        # 连接延时
        proxy_connect_timeout 3600s;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        # IP 穿透
        # proxy_set_header Host   $proxy_host;
        proxy_set_header Host       $http_host;
        # proxy_set_header Host       $host;
        # proxy_set_header Host       $host:$proxy_port;
        proxy_set_header        X-Real-IP $remote_addr;
        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        # WebSocket 穿透
        proxy_set_header Origin "";
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

    }

}

