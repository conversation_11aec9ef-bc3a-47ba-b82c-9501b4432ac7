import requests
import os
import json


clearml_api_host = os.environ.get("CLEARML_API_HOST", "")

# need ： task id； algo id  ；algo version


# 整体流程：从上一个任务获取必要信息——algo_id  algo_version 更新的model path(在train中先将其写入某临时目录)  存入对象存储
# 以下问题主要在谁注册到了json文件，目的是获取算法数据 
# task存储在mongodb内，通过task.get_by_id获取到task对象 
def get_trained_algo(clearml_api_host: str, task_id: str):
    try:
        # 获取task对象 
        task_payload = {"task":task_id}
        url = f'{clearml_api_host}/tasks.get_by_id'
        resp = requests.post(url, json = task_payload)
        param = resp.get("task").get("hyperparams").json()
        print("[DEBUG_PARAM]: ",type(param))
        algo_id = resp.get("task").get("algorithm").get("id")
        print("[DEBUG_ID]: ",algo_id)
        algo_version = resp.get("task").get("algorithm").get("version")
        print("[DEBUG_VERSION]: ",algo_version)
    except Exception as ex:
        print(ex)
    return algo_id, algo_version,param

# 调用clearml apiserver algo.storage_training_result方法，创建新的数据并将其存储
def storage_training_result(clearml_api_host: str, algo_id: str, version: str, release: dict):
    try:
        url = f'{clearml_api_host}/algo.storage_training_result'
        print("[DEBUG]: ",algo_id, version, release)
        storage_payload = {"algo_id":algo_id, "version": version, "release": release}
        requests.post(url, json = storage_payload)
    except Exception as ex:
        print(ex)

if __name__ == "__main__":
    # 获取task_id
    task_id = os.getenv.get("KFJ_TASK_ID", "")
    algo_id, algo_version, param = get_trained_algo(clearml_api_host, task_id)
    storage_training_result(clearml_api_host, algo_id, algo_version, param)