apiVersion: batch.volcano.sh/v1alpha1
kind: Job
metadata:
  name: task1
  namespace: pipeline
spec:
  minAvailable: 2
  schedulerName: volcano     #  default-scheduler
#   priorityClassName: high-priority
  policies:
    - event: PodEvicted
      action: RestartJob
  plugins:
    env: []
    svc: []
#   maxRetry: 5
  queue: default
#   volumes:
#     - mountPath: "/myinput"
#     - mountPath: "/myoutput"
#       volumeClaimName: "testvolumeclaimname"
#       volumeClaim:
#         accessModes: [ "ReadWriteOnce" ]
#         storageClassName: "my-storage-class"

  tasks:
    - replicas: 2
      name: "worker"     # 形成名称
      template:
        metadata:
          name: web
        spec:
          containers:
            - image: nginx
              imagePullPolicy: IfNotPresent
              name: nginx
              resources:
                requests:
                  cpu: "1"
          restartPolicy: Never