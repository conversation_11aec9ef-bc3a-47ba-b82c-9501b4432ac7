# 模型训练
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: kubeflow-kubeflow-user-workspace
  labels:
    kubeflow-pvname: kubeflow-kubeflow-user-workspace
spec:
#  storageClassName: pipeline
  capacity:
    storage: 500Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: ""
  csi:
    driver: csi.juicefs.com
    volumeHandle: kubeflow-kubeflow-user-workspace
    fsType: juicefs
    nodePublishSecretRef:
      name: juicefs-sc-secret
      namespace: kube-system
    volumeAttributes:
      juicefs/mount-cpu-limit: 5000m
      juicefs/mount-memory-limit: 5Gi
      juicefs/mount-cpu-request: 1m
      juicefs/mount-memory-request: 1Mi
  mountOptions:
    - subdir=kubeflow/pipeline/workspace
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kubeflow-user-workspace
  namespace: kubeflow
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
#  selector:
#    matchLabels:
#      kubeflow-pvname: kubeflow-kubeflow-user-workspace
  storageClassName: ""
  volumeName: kubeflow-kubeflow-user-workspace