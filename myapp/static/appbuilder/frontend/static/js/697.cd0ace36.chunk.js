"use strict";(self.webpackChunkkubeflow_frontend=self.webpackChunkkubeflow_frontend||[]).push([[697],{89839:function(e,t,a){a.d(t,{B6:function(){return d},dD:function(){return i},ny:function(){return r}});var n=a(68739),i=function(e){return n.Z.get(e)},r=function(e){return n.Z.get(e)},d=function(e){return n.Z.get(e)}},54189:function(e,t,a){a.d(t,{Z:function(){return _}});var n=a(1413),i=a(29439),r=a(4519),d=a(1631),c=a(93849),o=a(81748),s=a(2556),l={height:300};function _(e){var t=(0,r.useState)(),a=(0,i.Z)(t,2),_=a[0],h=a[1],u=Math.random().toString(36).substring(2),m=(0,o.$G)(),p=m.t,v=(m.i18n,{});return(0,r.useEffect)((function(){var t=document.getElementById(u);if(t){var a=d.S1(t);a.setOption((0,n.Z)((0,n.Z)({},v),e.option)),_||h(a)}}),[e.option,e.data]),(0,s.jsx)(c.Z,{spinning:e.loading,children:(0,s.jsxs)("div",{className:"chart-container",children:[(0,s.jsx)("div",{id:u,style:(0,n.Z)((0,n.Z)({},l),e.style)}),e.isNoData?(0,s.jsx)("div",{className:"chart-nodata",children:(0,s.jsx)("div",{children:p("\u6682\u65e0\u6570\u636e")})}):null]})})}},76877:function(e,t,a){a.d(t,{Z:function(){return b}});var n=a(93433),i=a(29439),r=a(1413),d=a(45987),c=a(4519),o=a(35492),s=a(20011),l=a(79551),_=a(28532),h=a(12513),u=a(80211),m=a(10089),p=a(2704),v=a(25738),f=a(1126),x=a.p+"static/media/emptyBg.15fdf5f39309784ac66e.png",g=a(74308),j=a(81748),E=a(2556),N=["onResize","width"],y=a(17972),M=function(e){var t=e.onResize,a=e.width,n=(0,d.Z)(e,N);return a?(0,E.jsx)(g.Resizable,{width:a,height:0,handle:(0,E.jsx)("span",{className:"react-resizable-handle",onClick:function(e){e.stopPropagation()}}),onResize:t,draggableOpts:{enableUserSelectHack:!1},children:(0,E.jsx)("th",(0,r.Z)((0,r.Z)({},n),{},{style:(0,r.Z)((0,r.Z)({},null===n||void 0===n?void 0:n.style),{},{userSelect:"none"})}))}):(0,E.jsx)("th",(0,r.Z)({},n))},b=function(e){var t=(0,c.useState)(!1),a=(0,i.Z)(t,2),d=a[0],g=a[1],N=(0,c.useState)({header:[],data:[]}),b=(0,i.Z)(N,2),k=b[0],C=b[1],D=(0,c.useState)([]),O=(0,i.Z)(D,2),P=O[0],w=O[1],I=(0,c.useState)(e.columns),Z=(0,i.Z)(I,2),T=Z[0],R=Z[1],B=function(t){return function(a,i){var d=i.size;if(!(d.width<100)){var c=(0,n.Z)(T);c[t]=(0,r.Z)((0,r.Z)({},c[t]),{},{width:d.width});var o=c.reduce((function(e,t){return e+t.width||100}),0)+200;localStorage.setItem(e.tableKey||"",JSON.stringify(c)),K((0,r.Z)((0,r.Z)({},S),{},{x:o})),R(c)}}},L=T.map((function(e,t){return(0,r.Z)((0,r.Z)({},e),{},{width:e.width||200,onHeaderCell:function(e){return{width:e.width,onResize:B(t)}}})})),A=(0,c.useState)(e.scroll),W=(0,i.Z)(A,2),S=W[0],K=W[1],U=(0,j.$G)(),z=U.t;U.i18n;(0,c.useEffect)((function(){R(e.columns)}),[e.columns]),(0,c.useEffect)((function(){K(e.scroll)}),[e.scroll]),(0,c.useEffect)((function(){if(e.dataSource){var t=e.columns.filter((function(e){return~P.indexOf(e.dataIndex)}));V(t,e.dataSource)}}),[e.dataSource,e.columns]);var V=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,a=e.map((function(e){return e.dataIndex})).filter((function(e){return"handle"!==e})),n=e.map((function(e){return e.title})).filter((function(e){return e!==z("\u64cd\u4f5c")})),i=[];t.forEach((function(e){var t={};a.map((function(a){var n=e[a];t[a]=n||""})),i.push(t)})),C({header:n,data:i})},H=function(){var e=k.header,t=k.data,a="";return e.length&&t.length?(a="|"+e.join("|")+"|\n",t.forEach((function(e){var t=Object.values(e).map((function(e){return""===e?" ":e}));a=a+"|"+t.join("|")+"|\n"}))):a="",a},Y=function(){var e=k.header,t=k.data,a="";return e.length&&t.length?(a=e.join("\t")+"\n",t.forEach((function(e){var t=Object.values(e).map((function(e){return""===e?" ":e}));a=a+t.join("\t")+"\n"}))):a="",a};return(0,E.jsxs)(s.Z,{className:"tablebox",direction:"vertical",size:"middle",children:[(0,E.jsxs)(l.Z,{width:1e3,maskClosable:!1,centered:!0,bodyStyle:{maxHeight:500,overflow:"auto"},visible:d,title:z("\u5bfc\u51fa\u6570\u636e"),onCancel:function(){g(!1)},footer:null,children:[(0,E.jsxs)("div",{style:{position:"relative"},children:[(0,E.jsxs)("div",{className:"mb16",children:[(0,E.jsxs)("span",{className:"pr8",children:[z("\u9009\u62e9\u9700\u8981\u5bfc\u51fa\u7684\u5217"),"\uff1a"]}),(0,E.jsx)(_.Z.Group,{options:e.columns.map((function(e){return{label:e.title,value:e.dataIndex}})).filter((function(e){return"handle"!==e.value})),defaultValue:[],value:P,onChange:function(t){w(t);var a=e.columns.filter((function(e){return~t.indexOf(e.dataIndex)}));V(a,e.dataSource)}})]}),(0,E.jsxs)("div",{style:{position:"absolute",right:0,bottom:0},children:[(0,E.jsx)(h.Z,{size:"small",type:"link",onClick:function(){w(e.columns.map((function(e){return e.dataIndex})).filter((function(e){return"handle"!==e}))),V(e.columns,e.dataSource)},children:z("\u5168\u9009")}),(0,E.jsx)(h.Z,{size:"small",type:"link",onClick:function(){w([]),V([],e.dataSource)},children:z("\u53cd\u9009")})]})]}),(0,E.jsxs)(u.Z,{children:[(0,E.jsx)(u.Z.TabPane,{tab:"Wiki\u683c\u5f0f",children:(0,E.jsx)(y,{text:H(),onCopy:function(){return m.ZP.success(z("\u5df2\u590d\u5236\u5230\u7c98\u8d34\u677f"))},children:(0,E.jsx)("pre",{style:{cursor:"pointer",minHeight:100},children:(0,E.jsx)("code",{children:H()})})})},"jira"),(0,E.jsx)(u.Z.TabPane,{tab:"Text\u683c\u5f0f",children:(0,E.jsx)(y,{text:Y(),onCopy:function(){return m.ZP.success(z("\u5df2\u590d\u5236\u5230\u7c98\u8d34\u677f"))},children:(0,E.jsx)("pre",{style:{cursor:"pointer",minHeight:100},children:(0,E.jsx)("code",{children:Y()})})})},"test")]})]}),e.titleNode||e.buttonNode||!e.cancelExportData?(0,E.jsxs)(o.Z,{justify:"space-between",align:"middle",children:[(0,E.jsx)(p.Z,{children:(0,E.jsx)(s.Z,{align:"center",children:e.titleNode})}),(0,E.jsx)(p.Z,{children:(0,E.jsxs)(s.Z,{align:"center",children:[e.buttonNode,e.cancelExportData?null:(0,E.jsx)(h.Z,{style:{marginLeft:6},onClick:function(){return g(!0)},children:z("\u5bfc\u51fa\u6570\u636e")})]})})]}):null,(0,E.jsx)(v.ZP,{renderEmpty:function(){return(0,E.jsxs)(o.Z,{justify:"center",align:"middle",style:{height:360,flexDirection:"column"},children:[(0,E.jsx)("img",{src:x,style:{width:266},alt:""}),(0,E.jsx)("div",{children:z("\u6682\u65e0\u6570\u636e")})]})},children:(0,E.jsx)(f.Z,{size:e.size||"middle",rowKey:e.rowKey?e.rowKey:"id",dataSource:e.dataSource,components:{header:{cell:M}},columns:L,pagination:!1!==e.pagination&&(0,r.Z)({},e.pagination),scroll:S,loading:e.loading,onChange:e.onChange,rowSelection:e.rowSelection})})]})}},75743:function(e,t,a){a.r(t),a.d(t,{default:function(){return D}});var n=a(4519),i=a(29439),r=a(93849),d=a(47239),c=a(89839),o=a(2556);function s(){return(0,o.jsxs)("div",{className:"dna p-a",style:{left:-180},children:[(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})}),(0,o.jsx)("div",{className:"ele",children:(0,o.jsx)("div",{className:"dot"})})]})}var l=a(16548),_=a(94119),h=a(93433),u=a(15671),m=a(43144),p=a(60136),v=a(29388),f=a(4942),x=a(1413),g=a(38299),j=a(50884).Z,E=function(){function e(t){var a=this,n=t.containerId,r=t.mainViewId,d=t.margin,c=void 0===d?0:d;(0,u.Z)(this,e),this.container=void 0,this.scaleView=void 0,this.mainView=void 0,this.gNode=void 0,this.gLink=void 0,this.gText=void 0,this.gBorder=void 0,this.gTextCache=void 0,this.innerWidth=void 0,this.innerHeight=void 0,this.tip=void 0,this.fontSize=14,this.zoom=void 0,this.currentNode=void 0,this.margin=void 0,this.list2Tree=function(e){for(var t,n=[],i=e.map((function(e){return e.children&&delete e.children,e})),r=a.list2Map(i,"ci_id"),d=0;d<i.length;d++){var c=i[d];if(void 0!==c.parentId){var o=r.get(c.parentId);o.children?o.children.push(c):o.children=[c]}else t=r.get(c.ci_id)}return n.push(t),n},this.tree2List=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children",a=(0,h.Z)(e),n=[];a.length;){var i=a.shift();n.push(i),i&&i[t]&&a.push.apply(a,(0,h.Z)(i[t]))}return n},this.list2Map=function(e,t){for(var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=new Map,i=0;i<e.length;i++){var r=e[i];r&&r[t]&&(a||!n.get(r[t]))&&n.set(r[t],r)}return n},this.treeCutNode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"id",n=arguments.length>3?arguments[3]:void 0;if(void 0===n)return e;var i=function e(i){for(var r=[],d=0;d<i.length;d++){var c=i[d],o=(0,x.Z)({},c);c[t]&&c[t].length?c[a]===n?(o[t]=[],r.push(o)):(o[t]=e(c[t]),r.push(o)):r.push(o)}return r}(e);return i},this.parse2Rectangle=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"0,0 2,0 2,2 0,2").split(" ").map((function(e){var t=e.split(","),a=(0,i.Z)(t,2);return{x:+a[0],y:+a[1]}})),t=e.reduce((function(e,t,a){var n=(0,x.Z)({},e);return n["x".concat(a)]=t.x,n["y".concat(a)]=t.y,n}),{}),a=t.x1-t.x0,n=t.y3-t.y0;return(0,x.Z)((0,x.Z)({coordList:e},t),{},{width:Math.abs(a),height:Math.abs(n),center:[a/2,n/2]})};var o=this;this.margin=c,this.container=document.getElementById(n),this.mainView=g.Ys("#".concat(r)),this.scaleView=this.mainView.append("g").attr("id","scaleView"),this.scaleView.attr("style","transition:all .3s");var s={top:c,right:c,bottom:c,left:c},l=this.container.scrollWidth-s.top-s.bottom,_=this.container.scrollHeight-s.left-s.right;this.mainView.attr("width",l).attr("height",_).attr("viewBox","0 0 ".concat(l," ").concat(_)).attr("transform","translate(".concat(s.left,", ").concat(s.top,")")),this.gTextCache=this.mainView.append("g").attr("id","gTextCache"),this.gBorder=this.scaleView.append("g").attr("id","gBorder"),this.gText=this.scaleView.append("g").attr("id","gText"),this.gLink=this.scaleView.append("g").attr("id","gLink"),this.gNode=this.scaleView.append("g").attr("id","gNode").attr("cursor","pointer").attr("pointer-events","all").attr("transform","translate(0 ".concat(.2*_,") scale(.5,.5)")),this.scaleView.append("defs").attr("id","gDefs").append("marker").attr("id","arrowRight").attr("viewBox","-10 -10 20 20").attr("refX",10).attr("refY",0).attr("markerWidth",6).attr("markerHeight",6).attr("orient","auto").append("path").attr("d","M -10,-10 L 10,0 L -10,10").attr("fill","#cdcdcd").attr("stroke-width",2).attr("stroke","#cdcdcd");var m=j();m.attr("class","d3-tip").html((function(e){return e})),this.tip=m,this.mainView.call(m);var p=g.sPX().scaleExtent([.1,2]).on("zoom",(function(e){var t=e.transform;o.scaleView.attr("style","transition:all 0s").attr("transform",t),o.tip.hide()})).on("end",(function(){o.scaleView.attr("style","transition:all 0.3s")}));this.zoom=p,this.mainView.call(p).call(p.transform,g.CRH.translate(0,0).scale(1)).on("dblclick.zoom",null),this.innerHeight=_,this.innerWidth=l}return(0,m.Z)(e,[{key:"reSize",value:function(){this.mainView.attr("width",0).attr("height",0).attr("viewBox","0 0 ".concat(0," ",0));var e=this.margin,t=this.margin,a=this.margin,n=this.margin,i=this.container.scrollWidth-e-a,r=this.container.scrollHeight-n-t;console.log(this.container.scrollWidth,this.container.scrollHeight),this.mainView.attr("width",i).attr("height",r).attr("viewBox","0 0 ".concat(i," ").concat(r)),this.innerHeight=r,this.innerWidth=i}},{key:"getTextRect",value:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:14,n=this.gTextCache.append("text").text(e).attr("font-size","".concat(a,"px")),i=null===(t=n.node())||void 0===t?void 0:t.getBBox(),r={width:(null===i||void 0===i?void 0:i.width)||0,height:(null===i||void 0===i?void 0:i.height)||0};return n.remove(),r}}]),e}(),N=(a(91157).f.name,[{background:"#00d4001a",color:"#00d400",border:"#00d400",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#00d4c81a",color:"#00d4c8",border:"#00d4c8",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#0000d41a",color:"#0000d4",border:"#0000d4",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#c800d41a",color:"#c800d4",border:"#c800d4",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#d464001a",color:"#d46400",border:"#d46400",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#96d4001a",color:"#96d400",border:"#96d400",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#00d4961a",color:"#00d496",border:"#00d496",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#0096d41a",color:"#0096d4",border:"#0096d4",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#6400d41a",color:"#6400d4",border:"#6400d4",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#d400001a",color:"#d40000",border:"#d40000",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"},{background:"#d4c8001a",color:"#d4c800",border:"#d4c800",activeColor:"#0078d4",disabled:"#cdcdcd1a",disabledColor:"#cdcdcd"}]),y=["ROOT","BUSINESS","THEME","COLLECT"].reduce((function(e,t,a){return(0,x.Z)((0,x.Z)({},e),{},(0,f.Z)({},t,N[a%N.length]))}),{}),M=function(e){(0,p.Z)(a,e);var t=(0,v.Z)(a);function a(e){var n,i=e.containerId,r=e.mainViewId,d=e.margin,c=void 0===d?0:d,o=e.isCollection;return(0,u.Z)(this,a),(n=t.call(this,{containerId:i,mainViewId:r,margin:c})).dataMap=new Map,n.dataMapByName=new Map,n.renderNodesMap=new Map,n.nodesInGraphMap=new Map,n.nodesInCollectionMap=new Map,n.dataNodes=[],n.activeNodeId=void 0,n.rootNode=void 0,n.handleNodeClick=void 0,n.loadingStart=void 0,n.loadingEnd=void 0,n.isCollection=void 0,n.htmlStrEnCode=function(e){return"".concat(e).replace(/[\u00A0-\u9999<>\-\&\:]/g,(function(e){return"&#"+e.charCodeAt(0)+";"}))},n.isCollection=!!o,n}return(0,m.Z)(a,[{key:"enCodeNodeId",value:function(e){return"node_".concat(e.split("").map((function(e){return e.charCodeAt(0)})).join(""))}},{key:"deCodeNodeId",value:function(e){var t=e.replace(/^node_/,"").replaceAll("_rep_",":").replaceAll("_mid_","-");return this.htmlStrEnCode(t)}},{key:"preHandleNodes",value:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children",n=new Map,i=new Map,r=e.map((function(e,t){return"node_".concat(a,"_").concat(t)})),d=a,c="children"===a?"parent":"children";!function e(r){for(var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2?arguments[2]:void 0,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],_=[],u=0;u<r.length;u++){var m,p,v=r[u];l.push("".concat(u));t.htmlStrEnCode(v.nid);var g="node_".concat(a,"_").concat(l.join("_")),j=i.get(v.nid);j&&(g=j.key);var E=n.get(g),N=(0,x.Z)((0,x.Z)({},v),{},(m={key:g,level:o,relativeKey:a},(0,f.Z)(m,c,s?[s]:[]),(0,f.Z)(m,d,[]),m));if(E)N=E[c].map((function(e){return e.key})).includes((null===s||void 0===s?void 0:s.key)||"")?E:(0,x.Z)((0,x.Z)({},E),{},(0,f.Z)({},c,[s].concat((0,h.Z)(E[c]))));v[d]&&null!==(p=v[d])&&void 0!==p&&p.length&&e(v[d]||[],o+1,N,l),n.set(N.key,N),i.set(N.nid,N),_.push(N),l.pop()}return _}(e),n.forEach((function(e){for(var t=e[c],a=0;a<t.length;a++){var i=t[a].key,r=n.get(i);r&&(r[d].push(e),n.set(i,r))}e[c]=e[c].map((function(e){return n.get(e.key)})),n.set(e.key,e)}));var o=r.map((function(e){return n.get(e)}));return o?(0,h.Z)(o):[]}},{key:"initData",value:function(e){var t=this;this.nodesInCollectionMap=new Map,this.dataNodes=[],console.log("data",e);var a=this.preHandleNodes(e,"parent"),n=this.preHandleNodes(e,"children");console.log("preHandleNextData",n);for(var i=0;i<n.length;i++)n[i].key="node_".concat(i),n[i].parent=a[i].parent;var r=n[0],d=n;this.rootNode=r;var c=this.preRenderDataReady(d);console.log("preRenderData",c),this.renderNode(c).then((function(){t.centerApp()}))}},{key:"preRenderDataReady",value:function(e){if(null!==e&&void 0!==e&&e.length){var t=this.tree2List(e,"parent"),a=this.tree2List(e,"children"),n=[].concat((0,h.Z)(t),(0,h.Z)(a)),i=this.list2Map(n,"key"),r=this.list2Map(n,"node_name"),d=[];i.forEach((function(e){d.push(e)})),this.dataMap=i,this.dataMapByName=r,this.dataNodes=d}else this.dataNodes=[];return this.dataNodes}},{key:"createRenderNodes",value:function(e,t){var a=this;return e.map((function(e){return a.nodesInCollectionMap.get(e.key)?"":"COLLECT"===e.data_fields?"".concat(e.key,'\n\t\t\t\t\t[label="\u805a\u5408\u8282\u70b9\uff0c\u5269\u4f59').concat(e.collectNum,'\u4e2a\u8282\u70b9(\u53cc\u51fb\u5c55\u5f00) + ",\n\t\t\t\t\t\tshape=box,\n\t\t\t\t\t\tstyle=dashed,\n\t\t\t\t\t\tmargin=0,\n\t\t\t\t\t\tid=').concat(e.key,"\n\t\t\t\t\t];"):"".concat(e.key,'\n\t\t\t\t\t[label="\u5360\u4f4d\u7b26\u5360\u4f4d\u7b26\u5360\u4f4d').concat(e.key,'",\n\t\t\t\t\t\tshape=box,\n\t\t\t\t\t\twidth=8,\n\t\t\t\t\t\theight=0.8,\n\t\t\t\t\t\tmargin=0,\n\t\t\t\t\t\tid=').concat(e.key,"\n\t\t\t\t\t];")})).filter((function(e){return!!e}))}},{key:"cerateRenderNodesRelation",value:function(e){var t=this,a=[];return e.forEach((function(e){var n=e.parent,i=e.children;(n||[]).forEach((function(n){if(!(t.nodesInCollectionMap.get(e.key)||t.nodesInCollectionMap.get(n.key))){var i="\n\t\t\t\t\t".concat(n.key,"->").concat(e.key,' [id="edgePre_').concat(n.key,"_edge_").concat(e.key,'"];');-1===a.indexOf(i)&&a.push(i)}})),(i||[]).forEach((function(n){if(!(t.nodesInCollectionMap.get(e.key)||t.nodesInCollectionMap.get(n.key))){var i="\n\t\t\t\t\t".concat(e.key,"->").concat(n.key,' [id="edgePre_').concat(e.key,"_edge_").concat(n.key,'"];');-1===a.indexOf(i)&&a.push(i)}}))})),a}},{key:"backRenderHandle",value:function(){var e=this,t=this,a=new Map;g.td_("title").remove(),g.td_(".node").each((function(t){try{var n,i=t.key,r=null===(n=e.dataMap)||void 0===n?void 0:n.get(i),d=y[(null===r||void 0===r?void 0:r.data_fields)||""]||N[0],c=g.td_("#".concat(i," polygon")).datum(),o={renderInfo:t,renderId:i,data:r,theme:d,id:i,x:c.center.x,y:c.center.y};a.set(i,o),g.td_("#".concat(i,' text[fill="#000000"]')).attr("type","mainText")}catch(s){console.log(s)}})),this.renderNodesMap=a,this.beautifulNode();var n=null;g.td_('.node[type="collect"]').on("click",(function(e,a){clearTimeout(n),n=setTimeout((function(){t.handleNodeClick&&t.handleNodeClick(e)}),200)}));var i=document.getElementById("d3MainView");i&&(i.onclick=function(e){for(var a=!1,n=0;n<e.path.length;n++){var i=e.path[n];if(i.id&&~i.id.indexOf("node_")){a=!0;break}}a||t.refresh()})}},{key:"beautifulNode",value:function(){var e=this,t=this,a=g.td_(".node polygon").data();console.log("boxs",a),a.forEach((function(a){var n,i=a.bbox,r=a.parent.key,d=null===(n=e.dataMap)||void 0===n?void 0:n.get(r);d&&(g.Ys("#".concat(r)).remove(),g.Ys("#mainGroup").append("g").attr("id",r).attr("class","node").attr("type","normal").on("click",(function(e,a){var n,i=null===(n=t.renderNodesMap)||void 0===n?void 0:n.get(this.id);i&&t.highlightRelation(i),t.handleNodeClick&&t.handleNodeClick(null===i||void 0===i?void 0:i.data)})).append("rect").attr("id","rect_".concat(r)).attr("x",i.x).attr("y",i.y).attr("rx",i.height/2).attr("ry",i.height/2).attr("width",i.width).attr("height",i.height).attr("fill","#fff").attr("stroke","#cdcdcd").attr("stroke-width",1).attr("style","transition:all 0.3s;"),g.Ys("#".concat(r)).append("path").attr("d","M".concat(i.x," ").concat(i.cy," L").concat(i.x+2*i.width/3," ").concat(i.cy)).attr("stroke","#cdcdcd").attr("stroke-dasharray","5,5"),g.Ys("#".concat(r)).append("rect").attr("class","rectBg").attr("x",i.x).attr("y",i.y).attr("rx",i.height/2).attr("ry",i.height/2).attr("width",i.height).attr("height",i.height).attr("fill",d.color),g.Ys("#".concat(r)).append("rect").attr("class","rectBg").attr("id","iconRect_".concat(r)).attr("x",i.x+i.height/4).attr("y",i.y).attr("rx",i.height/2).attr("ry",i.height/2).attr("width",i.height/2).attr("height",i.height).attr("fill",d.color).attr("style","transition:all 0.3s;"),g.Ys("#".concat(r)).append("g").attr("id","icon_".concat(r)).html(d.icon),g.Ys("#icon_".concat(r," svg")).attr("fill","#fff").attr("width",i.height/2).attr("height",i.height/2).attr("x",i.x+i.height/4).attr("y",i.y+.23*i.height),g.Ys("#".concat(r)).append("text").text(d.title).attr("class","nodeType").attr("x",i.x+1.2*i.height).attr("y",i.y+.75*i.height/2).attr("width",i.height).attr("height",i.height).attr("font-weight","bold").attr("fill",d.color),g.Ys("#".concat(r)).on("mouseover",(function(e){g.Ys("#rect_".concat(r)).attr("stroke",d.color),g.Ys("#iconRect_".concat(r)).attr("rx",0).attr("ry",0).attr("x",i.x+i.height/2)})).on("mouseout",(function(e){g.Ys("#rect_".concat(r)).attr("stroke","#cdcdcd"),g.Ys("#iconRect_".concat(r)).attr("rx",i.height/2).attr("ry",i.height/2).attr("x",i.x+i.height/4)})),g.Ys("#".concat(r)).append("text").text(d.name).attr("class","nodeContent").attr("x",i.x+1.2*i.height).attr("y",i.y+.8*i.height).attr("width",i.height).attr("height",i.height),g.Ys("#".concat(r)).append("g").attr("id","icon_status_text_".concat(r)).append("text").text(d.status.label).attr("x",i.x+i.width-2.8*i.height/3).attr("y",i.y+.6*i.height/2))}))}},{key:"handleCollectExpand",value:function(e){var t,a,n=this.dataMap.get(e),r=null===n||void 0===n?void 0:n.relativeKey,d=(null===n||void 0===n?void 0:n.parent)||[],c=(0,i.Z)(d,1)[0],o=(null===n||void 0===n?void 0:n.children)||[],s=(0,i.Z)(o,1)[0],l=c&&(null===(t=this.dataMap)||void 0===t?void 0:t.get(c.key)),_=s&&(null===(a=this.dataMap)||void 0===a?void 0:a.get(s.key));if(l&&n&&"children"===r){var u=l.collectionChildren||[],m=l.children||[],p=l.children.pop(),v=u.shift();if(v){this.nodesInCollectionMap.delete(v.key);for(var f=(0,h.Z)(v.children);f.length;){var x=f.shift();x&&(this.nodesInCollectionMap.delete(x.key),f.push.apply(f,(0,h.Z)(x.children||[])))}n.collectNum=(n.collectNum||0)-1,n.collectionNodes=u,l.collectionChildren=u,u.length?l.children=[].concat((0,h.Z)(m),[v,p]):l.children=[].concat((0,h.Z)(m),[v])}}if(_&&n&&"parent"===r){var g=_.collectionParent||[],j=_.parent||[],E=_.parent.pop(),N=g.shift();if(N){this.nodesInCollectionMap.delete(N.key);for(var y=(0,h.Z)(N.parent);y.length;){var M=y.shift();M&&(this.nodesInCollectionMap.delete(M.key),y.push.apply(y,(0,h.Z)(M.parent||[])))}n.collectNum=(n.collectNum||0)-1,n.collectionNodes=g,_.collectionParent=g,g.length?_.parent=[].concat((0,h.Z)(j),[N,E]):_.parent=[].concat((0,h.Z)(j),[N])}}}},{key:"renderNode",value:function(e,t){var a=this;this.loadingStart&&this.loadingStart();var n=this.createRenderNodes(e,t),i=this.cerateRenderNodesRelation(e),r='digraph  {\n\t\t\tid=mainGroup;\n\t\t\trankdir = LR;\n\t\t\tranksep = 1;\n\t\t\tnodesep = 1;\n\t\t\tedge [color="#cdcdcd"];\n\t\t\t'.concat(n.join(" ")," ").concat(i.join(" "),"\n        }");g.Ys("#gNode").graphviz({zoom:!1,zoomTranslateExtent:[0,0]}).dot(r);return new Promise((function(e,t){setTimeout((function(){try{g.Ys("#gNode").graphviz({zoom:!1,zoomTranslateExtent:[0,0]}).renderDot(r)}catch(t){console.log(t)}a.backRenderHandle(),a.loadingEnd&&a.loadingEnd(),e("")}),100)}))}},{key:"highlightRelation",value:function(e){var t,a;g.td_(".node polygon").attr("stroke","#cdcdcd").attr("fill","#ffffff"),g.td_(".node text").attr("fill","#cdcdcd"),g.td_(".node .rectBg").attr("fill","#cdcdcd"),g.td_(".edge path").attr("stroke","#cdcdcd"),g.td_(".edge polygon").attr("stroke","#cdcdcd").attr("fill","#cdcdcd");var n=this.treeCutNode([e.data],"parent","key",null===(t=this.rootNode)||void 0===t?void 0:t.key),i=this.treeCutNode([e.data],"children","key",null===(a=this.rootNode)||void 0===a?void 0:a.key),r=this.tree2List(n,"parent"),d=this.tree2List(i,"children");console.log("nodeParentList",r);for(var c=0;c<r.length;c++){var o=r[c];if(o){var s=o.color;g.td_("#".concat(o.key," .rectBg")).attr("fill",s),g.td_("#".concat(o.key," .nodeType")).attr("fill",s),g.td_("#".concat(o.key," .nodeContent")).attr("fill","#000")}if(o&&o.parent&&o.parent.length)for(var l=0;l<o.parent.length;l++){var _=o.parent[l],h="edgePre_".concat(_.key,"_edge_").concat(o.key);g.td_("#".concat(h," path")).attr("stroke","#1e1653"),g.td_("#".concat(h," polygon")).attr("stroke","#1e1653").attr("fill","#1e1653")}}for(var u=0;u<d.length;u++){var m=d[u];if(m){var p=m.color;g.td_("#".concat(m.key," .rectBg")).attr("fill",p),g.td_("#".concat(m.key," .nodeType")).attr("fill",p),g.td_("#".concat(m.key," .nodeContent")).attr("fill","#000")}if(m&&m.children&&m.children.length)for(var v=0;v<m.children.length;v++){var f=m.children[v],x="edgePre_".concat(m.key,"_edge_").concat(f.key);g.td_("#".concat(x," path")).attr("stroke","#1e1653"),g.td_("#".concat(x," polygon")).attr("stroke","#1e1653").attr("fill","#1e1653")}}}},{key:"refresh",value:function(){console.log("refresh");for(var e=this.dataNodes,t=0;t<e.length;t++){var a=e[t];if("COLLECT"!==(null===a||void 0===a?void 0:a.data_fields)){if(a){var n=a.color;g.td_("#".concat(a.key," .rectBg")).attr("fill",n),g.td_("#".concat(a.key," .nodeType")).attr("fill",n),g.td_("#".concat(a.key," .nodeContent")).attr("fill","#000")}if(a&&a.parent&&a.parent.length)for(var i=0;i<a.parent.length;i++){var r=a.parent[i],d="edgePre_".concat(r.key,"_edge_").concat(a.key);g.td_("#".concat(d," path")).attr("stroke","#cdcdcd"),g.td_("#".concat(d," polygon")).attr("stroke","#cdcdcd").attr("fill","#cdcdcd")}}else g.td_("#".concat(a.key," polygon")).attr("stroke","#000000"),g.Ys("#".concat(a.key," text")).attr("fill","#000000")}for(var c=0;c<e.length;c++){var o=e[c];if("COLLECT"!==(null===o||void 0===o?void 0:o.data_fields)){if(o){var s=o.color;g.td_("#".concat(o.key," .rectBg")).attr("fill",s),g.td_("#".concat(o.key," .nodeType")).attr("fill",s),g.td_("#".concat(o.key," .nodeContent")).attr("fill","#000")}if(o&&o.children&&o.children.length)for(var l=0;l<o.children.length;l++){var _=o.children[l],h="edgePre_".concat(o.key,"_edge_").concat(_.key);g.td_("#".concat(h," path")).attr("stroke","#cdcdcd"),g.td_("#".concat(h," polygon")).attr("stroke","#cdcdcd").attr("fill","#cdcdcd")}}else g.td_("#".concat(o.key," polygon")).attr("stroke","#000000"),g.Ys("#".concat(o.key," text")).attr("fill","#000000")}}},{key:"anchorNode",value:function(e){var t;this.resetNode(this.activeNodeId||"");var a=g.Ys("#mainGroup").datum().translation.y,n=null===(t=this.renderNodesMap)||void 0===t?void 0:t.get(e);if(n){var i=-n.x*(96/72)+this.innerWidth/2+100,r=-(a- -n.y)*(96/72)+this.innerHeight/2;this.mainView.call(this.zoom.transform,g.CRH.translate(i,r).scale(1))}this.activeNode(e)}},{key:"centerApp",value:function(){this.resetNode(this.activeNodeId||"");var e=g.Ys("#scaleView").node().getBBox(),t=e.width/2,a=this.innerWidth/2-t,n=e.height/2,i=this.innerHeight/2-n-250;this.mainView.call(this.zoom.transform,g.CRH.translate(a,i).scale(1))}},{key:"activeNode",value:function(e){var t,a,n=null===(t=this.renderNodesMap)||void 0===t?void 0:t.get(e),i=null===(a=this.dataMap)||void 0===a?void 0:a.get(e);this.activeNodeId&&g.td_("#".concat(this.activeNodeId," .nodeContent")).attr("fill","#000"),n&&i&&(this.activeNodeId=e)}},{key:"resetNode",value:function(e){var t,a,n=null===(t=this.renderNodesMap)||void 0===t?void 0:t.get(e),i=null===(a=this.dataMap)||void 0===a?void 0:a.get(e);n&&i&&g.td_("[node_id=value".concat(n.renderId,"]")).attr("fill","#000000")}}]),a}(E),b=a(81748);function k(e){var t=(0,n.useState)(!1),h=(0,i.Z)(t,2),u=h[0],m=h[1],p=(0,n.useState)(!1),v=(0,i.Z)(p,2),f=v[0],x=v[1],g=(0,n.useState)(!1),j=(0,i.Z)(g,2),E=j[0],N=j[1],y=(0,n.useState)([]),k=(0,i.Z)(y,2),C=k[0],D=k[1],O=(0,n.useState)(!1),P=(0,i.Z)(O,2),w=P[0],I=P[1],Z=(0,n.useState)(),T=(0,i.Z)(Z,2),R=T[0],B=T[1],L=(0,b.$G)(),A=L.t,W=(L.i18n,(0,n.useRef)()),S=(0,n.useState)(),K=(0,i.Z)(S,2),U=K[0],z=K[1],V=(0,n.useRef)(U);(0,n.useEffect)((function(){var e,t=new M({containerId:"d3Container",mainViewId:"d3MainView",margin:16});e=t,V.current=e,z(e);var a=document.getElementById("resizeIframe");a.contentWindow&&(a.contentWindow.onresize=function(){setTimeout((function(){var e;console.log(V.current),null===(e=V.current)||void 0===e||e.reSize()}),1e3)})}),[]),(0,n.useEffect)((function(){if(U){var e=(0,l.jS)("backurl")||"";Y(e)}}),[U]);var H=function(e){console.log(e);var t=U&&U.dataMap&&U.dataMap.get(e.key);console.log("currentNode",t),D([]),I(!0),x(!0),(0,c.ny)((null===t||void 0===t?void 0:t.detail_url)||"").then((function(e){console.log(e.data.result.detail);var t=e.data.result.detail;D(t),I(!1)})).catch((function(){I(!1)}))},Y=function(e){U&&(e?(m(!0),(0,c.dD)(e).then((function(e){var t=e.data.result.dag||[],a=e.data.result.layout||{};W.current=t,t.length?N(!1):N(!0),U.initData(t),U.handleNodeClick=H,U.loadingStart=function(){m(!0)},U.loadingEnd=function(){m(!1)},B(a)})).catch((function(e){console.log(e),N(!0)})).finally((function(){m(!1)}))):(U.initData([]),N(!0)))};return(0,o.jsxs)("div",{className:"p-r h100",id:"fullContainer",children:[(0,o.jsx)("iframe",{id:"resizeIframe",src:"",frameBorder:"0",className:"p-a z-1",style:{width:"100%",height:"100%"}}),u?(0,o.jsx)("div",{className:"p-a w100 h100 d-f ac jc mark z999 fadein",children:(0,o.jsx)(r.Z,{spinning:u,indicator:(0,o.jsx)(s,{}),children:(0,o.jsx)("div",{})})}):null,(0,o.jsx)(d.Z,{title:"\u8282\u70b9\u8be6\u60c5",width:800,closable:!1,onClose:function(){x(!1)},visible:f,className:"nodedetail-wapper",children:(0,o.jsx)(r.Z,{spinning:w,children:(0,o.jsx)(_.Z,{data:C})})}),E?(0,o.jsx)("div",{className:"p-a w100 h100 d-f ac jc ta-c z1",children:(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{children:(0,o.jsx)("img",{className:"w320",src:a(96726),alt:""})}),(0,o.jsx)("div",{className:"fs22",children:A("\u6682\u65e0\u6570\u636e")})]})}):null,(0,o.jsxs)("div",{className:"d-f fd-c h100 ov-h",children:[(0,o.jsxs)("div",{className:"tree-header",children:[(0,o.jsxs)("div",{className:"p16 d-f jc-b ac",children:[(0,o.jsxs)("div",{className:"d-f ac",children:[(0,o.jsx)("span",{className:"icon-custom",dangerouslySetInnerHTML:{__html:(null===R||void 0===R?void 0:R.icon)||""}}),(0,o.jsx)("span",{className:"ml8 fs18",children:null===R||void 0===R?void 0:R.title})]}),(0,o.jsx)("div",{children:null===R||void 0===R?void 0:R.right_button.map((function(e){return(0,o.jsx)("div",{onClick:function(){window.open(e.url,"blank")},className:"c-text-w ml8 btn-ghost d-il",children:e.label})}))})]}),(0,o.jsx)("div",{className:"header-detail p16 d-f",children:((null===R||void 0===R?void 0:R.detail)||[]).map((function(e){return(0,o.jsx)("div",{className:"flex1 mr48 header-detail-item",children:e.map((function(e){return(0,o.jsxs)("div",{className:"pb2",children:[e.label,"\uff1a",e.value]})}))})}))})]}),(0,o.jsx)("div",{id:"d3Container",className:"flex1",children:(0,o.jsx)("svg",{id:"d3MainView"})})]})]})}function C(){return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(k,{})})}function D(){return(0,o.jsx)("div",{className:"fade-in h100 d-f fd-c",children:(0,o.jsx)(C,{})})}},94119:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){__webpack_require__.d(__webpack_exports__,{Z:function(){return NodeDetail}});var _home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__(93433),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__(4942),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(1413),_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(29439),antd__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__(2704),antd__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__(35492),antd__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__(80211),antd__WEBPACK_IMPORTED_MODULE_13__=__webpack_require__(12513),react__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(4519),_components_TableBox_TableBox__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(76877),_components_EchartCore_EchartCore__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(54189),_api_commonPipeline__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(89839),_NodeDetail_less__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(38188),react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(2556);function NodeDetail(props){var _useState=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1),_useState2=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__.Z)(_useState,2),isSqlVisable=_useState2[0],setIsSqlVisable=_useState2[1],_useState3=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(),_useState4=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__.Z)(_useState3,2),currentDataItem=_useState4[0],setCurrentDataItem=_useState4[1],_useState5=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}),_useState6=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__.Z)(_useState5,2),nodeInfoApiMap=_useState6[0],_setNodeInfoApiMap=_useState6[1],nodeInfoApiMapRef=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(nodeInfoApiMap),setNodeInfoApiMap=function(e){nodeInfoApiMapRef.current=e,_setNodeInfoApiMap(e)};(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((function(){var e=[],t=[];props.data.forEach((function(a){a.content.forEach((function(n){if("api"===n.groupContent.type){var i=(0,_api_commonPipeline__WEBPACK_IMPORTED_MODULE_3__.B6)(n.groupContent.value);e.push(i),t.push("".concat(a.tabName,"_").concat(n.groupName))}}))})),console.log("apiList",e),Promise.all(e).then((function(e){var a=e.map((function(e){return e.data.result})).reduce((function(e,a,n){return(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__.Z)((0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__.Z)({},e),{},(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_8__.Z)({},t[n],(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_7__.Z)({},a)))}),{});setNodeInfoApiMap(a)}))}),[props.data]);var handelNodeDetailTable=function(e){try{JSON.parse("".concat(e.value||[]))}catch(n){console.log(n)}var t=JSON.parse("".concat(e.value||"[]")),a=Object.entries(t[0]||{}).reduce((function(e,t){var a=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__.Z)(t,2),n=a[0];a[1];return[].concat((0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_9__.Z)(e),[{title:n,dataIndex:n,key:n}])}),[]);return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__.Z,{span:16,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_components_TableBox_TableBox__WEBPACK_IMPORTED_MODULE_1__.Z,{rowKey:function(e){return JSON.stringify(e)},size:"small",cancelExportData:!0,columns:a,pagination:!1,dataSource:t})})},handleGroupContent=function(e,t,a,n){switch(e){case"map":return renderMapComponent(t);case"echart":return renderEchart(t);case"text":return renderMapText(t);case"iframe":return renderMapIframe(t);case"html":return renderHtml(t);case"api":return renderApi(t,a,n);default:return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("span",{style:{wordBreak:"break-word",whiteSpace:"pre-wrap"},children:t})}},renderApi=function(e,t,a){var n="".concat(t,"_").concat(a);return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{children:nodeInfoApiMapRef.current[n]?nodeInfoApiMapRef.current[n].value:""})},renderHtml=function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{dangerouslySetInnerHTML:{__html:e}})},renderMapComponent=function(e){var t=Object.entries(e).reduce((function(e,t){var a=(0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__.Z)(t,2),n=a[0],i=a[1];return[].concat((0,_home_myapp_myapp_frontend_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_9__.Z)(e),[{label:n,value:i}])}),[]);return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{className:"bg-title p16",children:t.map((function(e,t){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__.Z,{className:"mb8 w100",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__.Z,{span:8,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{className:"ta-l",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)("strong",{children:[e.label,"\uff1a"]})})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__.Z,{span:16,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("span",{style:{wordBreak:"break-word",whiteSpace:"pre-wrap"},children:e.value})})]},"nodeDetailItem_".concat(t))}))})},renderEchart=function renderEchart(data){var currentOps={};return eval("currentOps=".concat(data)),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{className:"bg-title p16",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_components_EchartCore_EchartCore__WEBPACK_IMPORTED_MODULE_2__.Z,{option:currentOps,loading:!1})})},renderMapText=function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{className:"p16 bg-title",style:{wordBreak:"break-word",whiteSpace:"pre-wrap"},children:e})},renderMapIframe=function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("iframe",{src:e.url,allowFullScreen:!0,allow:"microphone;camera;midi;encrypted-media;",className:"w100 fade-in",style:{border:0,height:500}})};return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment,{children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__.Z,{className:"nodedetail-tab",children:props.data.map((function(e,t){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__.Z.TabPane,{tab:e.tabName,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)("div",{className:"d-f fd-c jc-b h100",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{className:"flex1",children:e.content.map((function(t,a){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)("div",{className:"mb32",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{className:"fs16 mb16 bor-l b-theme pl4",style:{borderLeftWidth:2},dangerouslySetInnerHTML:{__html:t.groupName}}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{children:handleGroupContent(t.groupContent.type,t.groupContent.value,e.tabName,t.groupName)})]},"nodeGroup".concat(a))}))}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("div",{className:"nodedetail-tool",children:e.bottomButton.map((function(e){return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_13__.Z,{className:"mr12 icon-tool-wrapper",onClick:function(){window.open(e.url,"blank")},children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("span",{className:"icon-tool",dangerouslySetInnerHTML:{__html:e.icon}}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)("span",{className:"ml6",children:e.text})]})}))})]})},"nodeDetailTab".concat(t))}))})})}},38188:function(){}}]);
//# sourceMappingURL=697.cd0ace36.chunk.js.map