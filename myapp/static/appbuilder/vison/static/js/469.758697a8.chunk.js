"use strict";(self.webpackChunkvite_ml_platform=self.webpackChunkvite_ml_platform||[]).push([[469],{45117:function(e,n,t){t.d(n,{C:function(){return a},T:function(){return o}});var i=t(16030),o=function(){return(0,i.I0)()},a=i.v9},34469:function(e,n,t){t.r(n),t.d(n,{default:function(){return Z}});var i=t(29439),o=t(72791),a=t(59332),s=t(82728),l=t(46933),d=t(23629),r=t(93657),c=t(29093),u=t(47535),m=t(61460),p=t(29618),h=t(70927),f=t(56756),g=t(4026),x=t(53997),y=t(18511),v=t(61020),w=t(64011),b=t(45117),j=t(84806),S=t.n(j),k=t(18323),C={sectionStyles:(0,k.y)({marginBottom:16,padding:"0 10px 24px 10px",borderBottom:"1px solid #dadada",selectors:{".subtitle":{marginBottom:20,height:24,lineHeight:"1.1",fontSize:20,fontWeight:"bold",fontFamily:'"Raleway","Helvetica Neue",Helvetica,Arial,sans-serif;'},".expand-button":{color:"#005cd2",marginRight:24,fontSize:14,marginBottom:12}}}),sampleStyles:(0,k.y)({minHeight:186,position:"relative",display:"flex",flexWrap:"wrap"}),sampleHide:(0,k.y)({height:186,overflow:"hidden"}),sampleCardStyle:(0,k.y)({width:200,marginRight:24,marginBottom:24}),cardContainer:(0,k.y)({width:200,height:124,borderRadius:2,display:"flex",flexDirection:"column",cursor:"pointer",backgroundColor:"#fff",border:"1px solid #dadada",selectors:{":hover":{boxShadow:"rgb(0 0 0 / 18%) 0px 6.4px 14.4px 0px, rgb(0 0 0 / 22%) 0px 1.2px 3.6px 0px"}}}),addIconStyles:(0,k.y)({fontSize:38,padding:"42px 0 29px",cursor:"pointer",textAlign:"center",color:"#005cd2"}),sampleImgStyles:(0,k.y)({height:"100%",backgroundRepeat:"no-repeat",backgroundSize:"cover",selectors:{img:{width:"100%",height:"100%"}}}),cardTitleStyles:(0,k.y)({marginTop:16,textAlign:"center",cursor:"pointer",whiteSpace:"nowrap",display:"flex",flexFlow:"column nowrap",width:"auto",height:"auto",textOverflow:"ellipsis"}),videoPlayerStyles:(0,k.y)({backgroundColor:"#000",width:800,height:500})},_=t(39230),N=t(80184),W=function(e){var n=(0,b.T)(),t=(0,o.useState)(!1),s=(0,i.Z)(t,2),l=s[0],d=s[1],r=(0,o.useState)(!1),c=(0,i.Z)(r,2),u=c[0],m=c[1],p=(0,o.useState)(""),h=(0,i.Z)(p,2),f=h[0],g=h[1],j=(0,o.useState)(void 0),k=(0,i.Z)(j,2),W=k[0],R=k[1],T=(0,b.C)(w.zr),z=(0,_.$G)(),K=z.t,Z=z.i18n;console.log("i18n.language",Z.language);return(0,o.useEffect)((function(){u&&f&&setTimeout((function(){var e=new(S())({id:"video-player",url:f,width:800,height:500,videoInit:!0,playbackRate:[.5,.75,1,1.5,2],defaultPlaybackRate:1,download:!0,pip:!0,disableProgress:!1,allowPlayAfterEnded:!0,allowSeekAfterEnded:!0});R(e)}),0),u&&W&&(W.destroy(),R(void 0))}),[u,f]),(0,o.useEffect)((function(){}),[W]),(0,N.jsxs)(a.K,{className:C.sectionStyles,children:[(0,N.jsxs)(a.K,{className:"flex-section",horizontal:!0,horizontalAlign:"space-between",children:[(0,N.jsx)("div",{className:"subtitle",children:e.name}),(0,N.jsx)("div",{className:"expand-button",onClick:function(){d(!l)},children:K(l?"\u6298\u53e0":"\u66f4\u591a")})]}),(0,N.jsxs)("div",{className:"".concat(C.sampleStyles," ").concat(l?"":C.sampleHide),children:[e.first?(0,N.jsxs)("div",{className:C.sampleCardStyle,onClick:function(){x.Z.pipeline_modelview_add({describe:"new-pipeline-".concat(Date.now()),name:"".concat(T,"-pipeline-").concat(Date.now()),node_selector:"cpu=true,train=true",schedule_type:"once",image_pull_policy:"Always",parallelism:1,project:7}).then((function(e){if(0===(null===e||void 0===e?void 0:e.status)&&"success"===(null===e||void 0===e?void 0:e.message)){var n,t="".concat(window.location.origin).concat(location.pathname,"?pipeline_id=").concat(null===e||void 0===e||null===(n=e.result)||void 0===n?void 0:n.id);window.open("".concat(window.location.origin,"/frontend/showOutLink?url=").concat(encodeURIComponent(t)),"bank")}})).catch((function(e){e.response&&n((0,w.L$)({msg:e.response.data.message}))}))},children:[(0,N.jsx)("div",{className:C.cardContainer,children:(0,N.jsx)(y.J,{iconName:"Add",className:C.addIconStyles})}),(0,N.jsx)("div",{className:C.cardTitleStyles,children:(0,N.jsx)("span",{children:K("\u65b0\u5efa\u6d41\u6c34\u7ebf")})})]}):null,e.data.map((function(e,n){return e.img?(0,N.jsxs)("div",{className:C.sampleCardStyle,onClick:function(){!function(e){var n="".concat(window.location.origin).concat(location.pathname,"?pipeline_id=").concat(null===e||void 0===e?void 0:e.id);switch(e.type){case"link":window.open("".concat(window.location.origin,"/frontend/showOutLink?url=").concat(encodeURIComponent(n)),"bank");break;case"outside":window.open(e.url,"_blank");break;case"video":m(!0),g(e.url)}}(e)},children:[(0,N.jsx)("div",{className:C.cardContainer,children:(0,N.jsx)("div",{className:C.sampleImgStyles,children:(0,N.jsx)("img",{src:e.img,alt:e.name})})}),(0,N.jsx)("div",{className:C.cardTitleStyles,children:(0,N.jsx)("span",{children:e.name})})]},n):null}))]}),(0,N.jsx)(v.u,{isOpen:u,onDismiss:function(){return m(!1)},children:(0,N.jsx)("div",{id:"video-player",className:C.videoPlayerStyles})})]})},R=t(48636),T={root:{width:"100%",height:"100%",overflow:"hidden"}},z={dropdown:{width:100,marginLeft:10,marginRight:20}},K=[{key:"10",text:"10"},{key:"25",text:"25"},{key:"50",text:"50"},{key:"100",text:"100"}],Z=function(){var e=(0,b.T)(),n=(0,b.C)(R.cJ),t=(0,b.C)(R.td),y=(0,o.useState)([]),v=(0,i.Z)(y,2),w=v[0],j=v[1],S=(0,o.useState)([]),k=(0,i.Z)(S,2),C=k[0],Z=k[1],H=(0,o.useState)([]),A=(0,i.Z)(H,2),I=A[0],O=A[1],B=(0,o.useState)(0),D=(0,i.Z)(B,2),E=D[0],P=D[1],L=(0,o.useState)(10),M=(0,i.Z)(L,2),J=M[0],F=M[1],G=(0,o.useState)(!1),U=(0,i.Z)(G,2),$=U[0],V=U[1],q=(0,o.useState)(!1),Q=(0,i.Z)(q,2),X=Q[0],Y=Q[1],ee=(0,_.$G)(),ne=ee.t,te=(ee.i18n,[{key:"id",name:"ID",fieldName:"id",minWidth:50,maxWidth:100,data:"number"},{key:"name",name:ne("\u4efb\u52a1\u6d41"),fieldName:"name",minWidth:200,maxWidth:350,data:"string",onRender:function(e){return(0,N.jsx)("span",{style:{textDecoration:"underline",color:"#005ccb",cursor:"pointer"},onClick:function(){ie(e)},dangerouslySetInnerHTML:{__html:e.name}})}},{key:"describe",name:ne("\u63cf\u8ff0"),fieldName:"describe",minWidth:200,maxWidth:300,data:"string"},{key:"changed_on",name:ne("\u4fee\u6539\u65f6\u95f4"),fieldName:"changed_on",minWidth:200,maxWidth:300,data:"string"},{key:"project_id",name:ne("\u9879\u76ee\u7ec4"),minWidth:150,maxWidth:200,onRender:function(e){return(0,N.jsx)("div",{children:I[e.project_id].name})}}]),ie=function(e){var n="".concat(window.location.origin).concat(location.pathname,"?pipeline_id=").concat(null===e||void 0===e?void 0:e.id);window.open("".concat(window.location.origin,"/frontend/showOutLink?url=").concat(encodeURIComponent(n)),"bank")};return(0,o.useEffect)((function(){j([{name:ne("\u65b0\u4eba\u5236\u4f5c\u4e00\u4e2apipeline"),img:"/static/assets/images/ad/video-cover1-thumb.png",url:"https://cube-studio.oss-cn-hangzhou.aliyuncs.com/cube-studio.mp4",type:"video"},{name:ne("\u81ea\u5b9a\u4e49\u4efb\u52a1\u6a21\u677f"),img:"/static/assets/images/ad/video-cover2-thumb.png",url:"https://cube-studio.oss-cn-hangzhou.aliyuncs.com/job-template.mp4",type:"video"}]),x.Z.pipeline_modelview_demo().then((function(e){if(0===e.status){var n=e.result.map((function(e){return{id:e.id,name:e.describe,img:JSON.parse(e.parameter).img||"",type:"link"}}));Z(n)}})),x.Z.project_all().then((function(n){if(0===n.status){V(!0);var t=[];n.result.data.forEach((function(e){e.id&&(t[e.id]=e)})),console.log("list",t),O(t),e((0,R.JO)())}}))}),[]),(0,o.useEffect)((function(){$&&e((0,R.Jd)({page:E,page_size:J}))}),[J,$,E]),(0,o.useEffect)((function(){t&&Y(t.length<J)}),[t]),(0,N.jsx)(a.K,{className:"home-container",styles:T,children:(0,N.jsxs)(a.K,{as:"main",grow:!0,verticalFill:!0,styles:{root:{padding:"8px 14px",overflow:"scroll"}},children:[(0,N.jsx)(W,{name:ne("\u5e73\u53f0\u4e3b\u8981\u529f\u80fd"),data:C,first:!0}),(0,N.jsx)(W,{name:ne("\u65b0\u624b\u89c6\u9891"),data:w}),(0,N.jsxs)(a.K,{styles:{root:{marginTop:"16px !important",padding:"0 10px 24px"}},children:[(0,N.jsx)(a.K,{className:"flex-section",horizontal:!0,horizontalAlign:"space-between",children:(0,N.jsx)("div",{className:"subtitle",style:{marginBottom:8,height:24,lineHeight:"1.1",fontSize:20,fontWeight:"bold"},children:ne("\u6d41\u6c34\u7ebf")})}),(0,N.jsxs)(s.o,{"aria-label":"Basic Pivot Example",defaultSelectedKey:"1",children:[(0,N.jsx)(l.M,{headerText:ne("\u6211\u7684"),headerButtonProps:{"data-order":1,"data-title":"My Files Title"},itemKey:"1",children:(0,N.jsx)("div",{children:(0,N.jsx)(d.W,{items:n,columns:te.concat({key:"action",name:ne("\u64cd\u4f5c"),minWidth:200,maxWidth:300,onRender:function(n){return(0,N.jsx)("div",{children:(0,N.jsx)(r.G,{content:ne("\u5220\u9664"),children:(0,N.jsx)(c.h,{onClick:function(){!function(n){null!==n&&void 0!==n&&n.id&&x.Z.pipeline_modelview_delete(n.id).then((function(n){0===n.status&&e((0,R.JO)())}))}(n)},iconProps:{iconName:"Delete",styles:{root:{color:"red"}}}})})})}}),selectionMode:u.oW.none,setKey:"none",layoutMode:m.Oh.fixedColumns,isHeaderVisible:!0,compact:!0,styles:{headerWrapper:{".ms-DetailsHeader":{paddingTop:0}},contentWrapper:{lineHeight:"32px"}}})})}),(0,N.jsx)(l.M,{headerText:ne("\u534f\u4f5c"),itemKey:"2",children:(0,N.jsxs)("div",{children:[(0,N.jsx)(p.J,{setKey:"none",isHeaderVisible:!0,items:t||[],columns:te,compact:!!t,selectionMode:u.oW.none,layoutMode:m.Oh.fixedColumns,enableShimmer:!t,detailsListStyles:{headerWrapper:{".ms-DetailsHeader":{paddingTop:0}},contentWrapper:{lineHeight:"32px"}},listProps:{renderedWindowsAhead:0,renderedWindowsBehind:0}}),(0,N.jsxs)(a.K,{horizontal:!0,reversed:!0,verticalAlign:"center",styles:{root:{marginTop:20}},children:[(0,N.jsx)(h.K,{text:ne("\u4e0b\u4e00\u9875"),styles:{root:{marginRight:10}},disabled:X,onClick:function(){!X&&t&&P(E+1)}}),(0,N.jsx)(h.K,{text:ne("\u4e0a\u4e00\u9875"),styles:{root:{marginRight:10}},disabled:0===E,onClick:function(){0!==E&&t&&P(E-1)}}),(0,N.jsx)(f.L,{defaultSelectedKey:"10",placeholder:ne("\u9009\u62e9\u9875\u6570"),options:K,styles:z,onChange:function(e,n){P(0),(null===n||void 0===n?void 0:n.key)&&F(+n.key)}}),(0,N.jsx)(g.x,{styles:{root:{fontSize:14,fontWeight:600}},children:ne("\u9009\u62e9\u9875\u6570")})]})]})})]})]})]})})}}}]);