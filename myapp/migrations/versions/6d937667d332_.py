"""empty message

Revision ID: 6d937667d332
Revises: aa2d53864721
Create Date: 2023-04-06 13:35:35.294410

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6d937667d332'
down_revision = 'aa2d53864721'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sqlab_query',
    sa.Column('created_on', sa.DateTime(), nullable=True),
    sa.Column('changed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('submit_time', sa.String(length=40), nullable=False),
    sa.Column('start_time', sa.String(length=40), nullable=False),
    sa.Column('end_time', sa.String(length=40), nullable=False),
    sa.Column('engine_arg1', sa.String(length=200), nullable=False),
    sa.Column('engine_arg2', sa.String(length=200), nullable=False),
    sa.Column('qsql', sa.String(length=5000), nullable=False),
    sa.Column('engine', sa.String(length=100), nullable=False),
    sa.Column('deli', sa.String(length=11), nullable=False),
    sa.Column('stage', sa.String(length=11), nullable=False),
    sa.Column('status', sa.String(length=11), nullable=False),
    sa.Column('task_id', sa.String(length=100), nullable=False),
    sa.Column('log_url', sa.String(length=400), nullable=False),
    sa.Column('ui_url', sa.String(length=400), nullable=False),
    sa.Column('result_url', sa.String(length=400), nullable=False),
    sa.Column('result_line_num', sa.String(length=400), nullable=False),
    sa.Column('err_msg', sa.String(length=5000), nullable=False),
    sa.Column('username', sa.String(length=400), nullable=False),
    sa.Column('created_by_fk', sa.Integer(), nullable=True),
    sa.Column('changed_by_fk', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], ),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('aihub', sa.Column('expand', sa.Text(), nullable=True))
    op.add_column('aihub', sa.Column('images', sa.String(length=200), nullable=True))
    op.add_column('dataset', sa.Column('doc', sa.String(length=200), nullable=True))
    op.add_column('dataset', sa.Column('features', sa.Text(), nullable=True))
    op.add_column('dataset', sa.Column('info', sa.Text(), nullable=True))
    op.add_column('dataset', sa.Column('metric_info', sa.Text(), nullable=True))
    op.add_column('dataset', sa.Column('secret', sa.String(length=200), nullable=True))
    op.add_column('dataset', sa.Column('segment', sa.Text(), nullable=True))
    op.add_column('dataset', sa.Column('split', sa.String(length=200), nullable=True))
    op.add_column('dataset', sa.Column('subdataset', sa.String(length=200), nullable=True))
    op.add_column('dataset', sa.Column('version', sa.String(length=200), nullable=True))
    op.drop_constraint('metadata_metric_ibfk_1', 'metadata_metric', type_='foreignkey')
    op.drop_constraint('metadata_metric_ibfk_2', 'metadata_metric', type_='foreignkey')
    op.drop_column('metadata_metric', 'created_on')
    op.drop_column('metadata_metric', 'changed_by_fk')
    op.drop_column('metadata_metric', 'created_by_fk')
    op.drop_column('metadata_metric', 'changed_on')
    op.add_column('notebook', sa.Column('env', sa.String(length=400), nullable=True))
    op.add_column('pytorchjob', sa.Column('cluster', sa.String(length=100), nullable=True))
    op.add_column('tfjob', sa.Column('cluster', sa.String(length=100), nullable=True))
    op.add_column('workflow', sa.Column('cluster', sa.String(length=100), nullable=True))
    op.add_column('xgbjob', sa.Column('cluster', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('xgbjob', 'cluster')
    op.drop_column('workflow', 'cluster')
    op.drop_column('tfjob', 'cluster')
    op.drop_column('pytorchjob', 'cluster')
    op.drop_column('notebook', 'env')
    op.add_column('metadata_metric', sa.Column('changed_on', mysql.DATETIME(), nullable=True))
    op.add_column('metadata_metric', sa.Column('created_by_fk', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('metadata_metric', sa.Column('changed_by_fk', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('metadata_metric', sa.Column('created_on', mysql.DATETIME(), nullable=True))
    op.create_foreign_key('metadata_metric_ibfk_2', 'metadata_metric', 'ab_user', ['created_by_fk'], ['id'])
    op.create_foreign_key('metadata_metric_ibfk_1', 'metadata_metric', 'ab_user', ['changed_by_fk'], ['id'])
    op.drop_column('dataset', 'version')
    op.drop_column('dataset', 'subdataset')
    op.drop_column('dataset', 'split')
    op.drop_column('dataset', 'segment')
    op.drop_column('dataset', 'secret')
    op.drop_column('dataset', 'metric_info')
    op.drop_column('dataset', 'info')
    op.drop_column('dataset', 'features')
    op.drop_column('dataset', 'doc')
    op.drop_column('aihub', 'images')
    op.drop_column('aihub', 'expand')
    op.drop_table('sqlab_query')
    # ### end Alembic commands ###
