
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-configmap
  namespace: infra
  labels:
    app: nginx
data:
  default.conf: |-
    server {
        listen       80;
        server_name  localhost;
        access_log  /data/log/nginx/access.log  main;
        error_log   /data/log/nginx/error.log;

        location = / {
            rewrite ^(.*) $scheme://$http_host/frontend/ permanent;
        }

        # 静态资源
        location /frontend/ {
            autoindex off; # 禁止目录遍历
            root   /data/web;
            index  index.html index.htm;
            try_files $uri $uri/ /frontend/index.html;
            if ($request_filename ~* .*\.html$) {
              add_header Cache-Control "no-cache, no-store";
            }
        }

        # 静态资源
        location /static/appbuilder/ {
            autoindex off; # 禁止目录遍历
            root   /data/web;
            index  index.html index.htm;
            try_files $uri $uri/ /frontend/index.html;
            if ($request_filename ~* .*\.html$) {
              add_header Cache-Control "no-cache, no-store";
            }
        }

        location / {
            autoindex off; # 禁止目录遍历
            # 线上生产使用
            proxy_pass http://kubeflow-dashboard.infra/;

            proxy_http_version 1.1;
            # 连接延时
            proxy_connect_timeout 3600s;
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            # IP 穿透
            # proxy_set_header        Host $proxy_host;
            # proxy_set_header Host       $host;
            proxy_set_header Host       $http_host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            # WebSocket 穿透
            proxy_set_header Origin "";
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

        }

    }



---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubeflow-dashboard-frontend
  namespace: infra
  labels:
    app: kubeflow-dashboard-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kubeflow-dashboard-frontend
  template:
    metadata:
      name: kubeflow-dashboard-frontend
      labels:
        app: kubeflow-dashboard-frontend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubeflow-dashboard
                operator: In
                values:
                - "true"
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai
        - name: nginx-configmap
          configMap:
            name: nginx-configmap
            items:
              - key: default.conf
                path: default.conf
      serviceAccountName: kubeflow-dashboard
      imagePullSecrets:
        - name: hubsecret
      containers:
        - name: kubeflow-dashboard-frontend
          image: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard-frontend
          imagePullPolicy: Always  # IfNotPresent  Always
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          resources:
            requests:
              cpu: 10m
              memory: 100Mi

          volumeMounts:
            - name: nginx-configmap
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: default.conf

---

apiVersion: v1
kind: Service
metadata:
  name: kubeflow-dashboard-frontend
  namespace: infra
  labels:
    app: kubeflow-dashboard-frontend
spec:
  ports:
    - name: http
      port: 80
      targetPort: 80
      protocol: TCP
  selector:
    app: kubeflow-dashboard-frontend

