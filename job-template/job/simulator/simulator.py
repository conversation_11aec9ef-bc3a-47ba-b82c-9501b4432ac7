import json
import argparse
import os
import sys

def generate_simulation_envs(controller_ip: str, num_envs: int):
    """
    生成仿真环境列表并输出为 JSON 文件。
    :param controller_ip: 仿真控制器 IP 地址
    :param num_envs: 并行环境数量
    """
    env_list = []

    for i in range(num_envs):

        # TODO: 连接客户仿真环境，调用仿真环境接口，并启动仿真环境。
        env = {
            "id": i,
            "sim_addr": controller_ip,
            "env_name": f"env_{i}"
        }
        env_list.append(env)

    # 通过环境变量获取中间文件放置路径
    run_id = os.getenv("KFJ_RUN_ID", "")
    runner = os.getenv("KFJ_RUNNER", "")
    if not run_id or not runner:
        print("Error KFJ_RUN_ID not found")
        sys.exit(1)
    
    cahce_path = f"/cache/{runner}/{run_id}"
    if not os.path.exists(cahce_path):
        os.mkdir(cahce_path)

    output_file = f"{cahce_path}/env_info.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({"simu_env_list": env_list, "run_id":run_id, "runner":runner}, f, ensure_ascii=False, indent=4)

    print(f"✅ 成功生成 {num_envs} 个仿真环境，并写入文件 {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="生成仿真环境配置文件")
    parser.add_argument('--ip', type=str, required=True, help="仿真控制器 IP 地址")
    parser.add_argument('--num', type=int, required=True, help="并行环境数量")
    # parser.add_argument('--output', type=str, default="simulation_envs.json", help="输出 JSON 文件名（默认 simulation_envs.json）")

    args = parser.parse_args()

    generate_simulation_envs(args.ip, args.num)