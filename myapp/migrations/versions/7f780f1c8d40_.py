"""empty message

Revision ID: 7f780f1c8d40
Revises: 30f714129cdf
Create Date: 2022-09-09 12:12:02.841200

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7f780f1c8d40'
down_revision = '30f714129cdf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('kfservice')
    op.add_column('pipeline', sa.Column('cronjob_start_time', sa.String(length=300), nullable=True))
    op.add_column('task', sa.Column('skip', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('task', 'skip')
    op.drop_column('pipeline', 'cronjob_start_time')
    op.create_table('kfservice',
    sa.Column('created_on', mysql.DATETIME(), nullable=True),
    sa.Column('changed_on', mysql.DATETIME(), nullable=True),
    sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
    sa.Column('project_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('name', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('label', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('service_type', mysql.ENUM('predictor', 'transformer', 'explainer'), nullable=False),
    sa.Column('default_service_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('canary_service_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('canary_traffic_percent', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('created_by_fk', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('changed_by_fk', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['canary_service_id'], ['service.id'], name='kfservice_ibfk_1'),
    sa.ForeignKeyConstraint(['changed_by_fk'], ['ab_user.id'], name='kfservice_ibfk_2'),
    sa.ForeignKeyConstraint(['created_by_fk'], ['ab_user.id'], name='kfservice_ibfk_3'),
    sa.ForeignKeyConstraint(['default_service_id'], ['service.id'], name='kfservice_ibfk_4'),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], name='kfservice_ibfk_5'),
    sa.PrimaryKeyConstraint('id'),
    mysql_default_charset='utf8',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
