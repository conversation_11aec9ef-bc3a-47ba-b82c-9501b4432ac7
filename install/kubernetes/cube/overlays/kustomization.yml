apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: infra

configMapGenerator:
- name: kubeflow-dashboard-config
  files:
  - config/config.py
  - config/entrypoint.sh
  - config/project.py
# kubectl delete configmap kubeflow-dashboard-config -n infra
# kubectl create configmap kubeflow-dashboard-config --from-file=config -n infra

- name: deploy-config
  literals:
  - STAGE=prod
  - REDIS_HOST=redis-master.infra
  - REDIS_PORT=6379
  - REDIS_PASSWORD=admin
  - MYSQL_SERVICE=mysql+pymysql://root:<EMAIL>:3306/kubeflow?charset=utf8
  - ENVIRONMENT=DEV


vars:
- name: STAGE
  objref:
    kind: ConfigMap
    name: deploy-config
    apiVersion: v1
  fieldref:
    fieldpath: data.STAGE

resources:
  - ../base

images:
- name: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard
  newName: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard
  newTag: 2025.03.01
- name: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard-frontend
  newName: ccr.ccs.tencentyun.com/cube-studio/kubeflow-dashboard-frontend
  newTag: 2025.03.01


# kustomize build . | kubectl apply -f -




