#
# The Alluxio Open Foundation licenses this work under the Apache License, version 2.0
# (the "License"). You may not use this work except in compliance with the License, which is
# available at www.apache.org/licenses/LICENSE-2.0
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
# either express or implied, as more fully set forth in the License.
#
# See the NOTICE file distributed with this work for information regarding copyright ownership.
#

{{ if not (eq .Values.worker.enabled false) -}}

{{ $shortCircuitEnabled := .Values.shortCircuit.enabled -}}
{{ $needDomainSocketVolume := and (and $shortCircuitEnabled (eq .Values.shortCircuit.policy "uuid")) (eq .Values.shortCircuit.volumeType "persistentVolumeClaim") -}}
{{ if $needDomainSocketVolume -}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Values.shortCircuit.pvcName }}
  labels:
    app: {{ template "alluxio.name" . }}
    chart: {{ template "alluxio.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    role: alluxio-worker
spec:
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{ .Values.shortCircuit.size }}
  storageClassName: {{ .Values.shortCircuit.storageClass }}
  accessModes:
{{ toYaml .Values.shortCircuit.accessModes | trim | indent 4 }}
  selector:
    matchLabels:
      app: {{ template "alluxio.name" . }}
      release: {{ .Release.Name }}
      heritage: {{ .Release.Service }}
      role: alluxio-worker
{{- end -}}
{{- end }}
