"""empty message

Revision ID: 444d4749dfa1
Revises: 16f0ba32110a
Create Date: 2021-10-14 06:23:17.102819

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '444d4749dfa1'
down_revision = '16f0ba32110a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('docker', sa.Column('expand', sa.Text(length=65536), nullable=True))
    op.add_column('docker', sa.Column('last_image', sa.String(length=200), nullable=True))
    op.add_column('docker', sa.Column('need_gpu', sa.<PERSON>(), nullable=True))
    op.add_column('pipeline', sa.Column('expired_limit', sa.Integer(), nullable=False))
    op.add_column('service', sa.Column('expand', sa.Text(length=65536), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'expand')
    op.drop_column('pipeline', 'expired_limit')
    op.drop_column('docker', 'need_gpu')
    op.drop_column('docker', 'last_image')
    op.drop_column('docker', 'expand')
    # ### end Alembic commands ###
