@property --angle {
    syntax: '<angle>';
    inherits: false;
    initial-value: 0deg;
  }
  
//   html, body {
//       width: 100%;
//       height: 100%;
//       display: flex;
//       background: #000;
//   }
  
  .g-container {
      position: relative;
      margin: auto;
      width: 640px;
      height: 56px;
    //   filter: drop-shadow(0 0 5px hsl(162, 100%, 58%)) drop-shadow(0 0 10px hsl(270, 73%, 53%));
  }
  
  .g-triangle {
      width: 640px;
      height: 56px;
      clip-path: 
          polygon(
            10px 0, 
            0 10px, 
            0px 100%,
            1px 100%,
            
            1px 11px, 
            11px 1px,                 
            calc(100% - 1px) 1px, 
            calc(100% - 1px) calc(100% - 11px),  
            calc(100% - 11px) calc(100% - 1px), 
            1px calc(100% - 1px),
            
            1px 100%, 
            calc(100% - 10px)  100%,  
            100%  calc(100% - 10px),  
            100% 0%
      );
      background: conic-gradient(from var(--angle), hsl(162, 100%, 58%), hsl(270, 73%, 53%), hsl(162, 100%, 58%));
      animation: rotate 3s infinite linear;
  }
  
  @keyframes rotate {
      to {
          --angle: 360deg;
      }
  }



@property --xPoint {
    syntax: '<length>';
    inherits: false;
    initial-value: 400px;
  }
  @property --yPoint {
    syntax: '<length>';
    inherits: false;
    initial-value: 300px;
  }
  @property --x2Point {
    syntax: '<length>';
    inherits: false;
    initial-value: 500px;
  }
  @property --y2Point {
    syntax: '<length>';
    inherits: false;
    initial-value: 300px;
  }
  
//   body,
//   html {
//       width: 100%;
//       height: 100%;
//       display: flex;
//       // background:
//       //     conic-gradient(
//       //         from -45deg at 400px 300px,
//       //         hsla(170deg, 100%, 55%, .8),
//       //         // hsla(170deg, 100%, 58%, .5),
//       //         transparent 50%,
//       //         hsla(219deg, 95%, 70%, .7) 100%),
//       //     linear-gradient(-45deg, #061248, #142268);
//       background: #000;
//   }
  
  .wrap {
      position: relative;
      margin: auto;
      width: 100%;
      height: 100%;
    //   width: 1000px;
    //   height: 600px;
      background:
          conic-gradient(
              from -45deg at var(--xPoint) var(--yPoint),
              hsla(170deg, 100%, 70%, .7),
              transparent 50%,
              hsla(219deg, 90%, 80%, .5) 100%),
          conic-gradient(
              from 180deg at var(--x2Point) var(--y2Point),
              hsla(170deg, 100%, 70%, .7),
              transparent 50%,
              hsla(219deg, 90%, 80%, .5) 100%),
              linear-gradient(-45deg, #060d5e, #002268);
      animation: pointMove 2.5s infinite alternate linear;
      overflow: hidden;
  }
  
  .shadowTop {
      position: absolute;
      top: -300px;
      left: -330px;
      width: 430px;
      height: 300px;
      background: #fff;
      transform-origin: 100% 100%;
      transform: rotate(225deg);
      clip-path: polygon(-100% 100%, 200% 100%, 200% 500%, -100% 500%);
      box-shadow: 
          0px 0 .5px hsla(170deg, 95%, 80%, 1),
          0px 0 1px hsla(170deg, 91%, 80%, .95),
          0px 0 2px hsla(171deg, 91%, 80%, .95),
          0px 0 3px hsla(171deg, 91%, 80%, .95),
          0px 0 4px hsla(171deg, 91%, 82%, .9),
          0px 0 5px hsla(172deg, 91%, 82%, .9),
          0px 0 10px hsla(173deg, 91%, 84%, .9),
          0px 0 20px hsla(174deg, 91%, 86%, .85),
          0px 0 40px hsla(175deg, 91%, 86%, .85),
          0px 0 60px hsla(175deg, 91%, 86%, .85);
      animation: scale 2.5s infinite alternate linear;
      mix-blend-mode: hard-light;
  }
  
  .shadowBottom {
      position: absolute;
      top: 300px;
      left: 500px;
      width: 400px;
      height: 300px;
      background: #000;
      transform-origin: 0 100%;
      clip-path: polygon(0 -100%, -200% -100%, -200% 200%, 0 200%);
      box-shadow: 
          0px 0 .5px hsla(170deg, 95%, 80%, 1),
          0px 0 1px hsla(170deg, 91%, 80%, .95),
          0px 0 2px hsla(171deg, 91%, 80%, .95),
          0px 0 3px hsla(171deg, 91%, 80%, .95),
          0px 0 4px hsla(171deg, 91%, 82%, .9),
          0px 0 5px hsla(172deg, 91%, 82%, .9),
          0px 0 10px hsla(173deg, 91%, 84%, .9),
          0px 0 20px hsla(174deg, 91%, 86%, .85),
          0px 0 40px hsla(175deg, 91%, 86%, .85),
          0px 0 60px hsla(175deg, 91%, 86%, .85);
      animation: scaleBottom 2.5s infinite -2.5s alternate linear;
      mix-blend-mode: hard-light;
  }
   
  @keyframes scale {
      50%,
      100% {
          transform: rotate(225deg) scale(0);
      }
  }
  
  @keyframes scaleBottom {
      50%,
      100% {
          transform: scale(0);
      }
  }
  
  @keyframes pointMove {
      0% {
          --x2Point: 500px;
          --y2Point: 600px;
      }
      50% {
          --xPoint: 100px;
          --yPoint: 0;
          --x2Point: 500px;
          --y2Point: 600px;
      }
      100% {
          --xPoint: 100px;
          --yPoint: 0;
          --x2Point: 500px;
          --y2Point: 300px;
      }
  }