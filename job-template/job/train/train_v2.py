# 直接获取到参数组创建训练

import argparse
import sys
import os
import json
from common import info
from common import algo
from common import task


def create_tasks(hyperParams, project_id, image,  algo_id, algo_version, dataset_id, dataset_version):

    index = 0
    for comb in hyperParams.get("comboData", []):
        index += 1
        params_dict = {}
        for k,v in comb.items():
            if k  == "train_cmd":
                cmd = v
            else:
                params_dict.update({k:{"section":"Params","name":k, "value":str(v)}})
        print(f"dataset_id {dataset_id}, dataset_version {dataset_version}")
        task_id = task.create_task(project_id=project_id, 
                            task_name=f'{project_id}-{index}', 
                            cmd=cmd, 
                            params=params_dict,
                            algo_id=algo_id, 
                            algo_version=algo_version, 
                            dataset_id=dataset_id,
                            dataset_version=dataset_version, 
                            image=image)
        task.start_task(task_id)
    return


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="算法训练配置")
    parser.add_argument('--algo_id', type=str, required=True, help="算法ID")
    parser.add_argument('--algo_version', type=str, required=True, help="算法版本")
    parser.add_argument('--hyperParams', type=str, required=True, help="超参")
    args = parser.parse_args()


    # 1、拿到算法参数、运行命令
    algo_version = algo.get_algo_version_detail(algo_id=args.algo_id, algo_version=args.algo_version)
    if not algo_version:
        print(f'Error: algo {args.algo_id} version{args.algo_version} not found...')

        
    image = "hub.tiduyun.com:5000/clearml/executor:1.4.1-keras"
    if  algo_version.get("exec_config",{}).get("image") and algo_version.get("exec_config",{}).get("tag"):
        image = algo_version.get("exec_config",{}).get("image") + ":" +algo_version.get("exec_config",{}).get("tag")

    cmd = algo_version.get("args",{}).get("input_train",{}).get("train_cmd")
    original_params = algo_version.get("args",{}).get("input_train",{})
    hyperParams = json.loads(args.hyperParams)
    if not hyperParams.get("comboData",{}):
        print(f'Error: comboData not found')

    for param in original_params:
        print("param")
    
    # 2、批量创建训练任务
    project_name = f'project-{os.environ.get("KFJ_RUN_ID")}'
    project_id = task.create_project(project_name)

    # info.set_json_value("dataset.id", "01010101010")
    # info.set_json_value("dataset.version", "1.00")

    dataset_id = info.get_from_json("dataset.id")
    dataset_version = info.get_from_json("dataset.version")

    create_tasks(hyperParams, project_id,  image, args.algo_id, args.algo_version, dataset_id, dataset_version)